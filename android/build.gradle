buildscript {
    ext.kotlin_version = '1.7.20'
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.2.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.google.gms:google-services:4.3.13'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.3.0'

    }
}

allprojects {
    repositories {
        google()
        jcenter()
        mavenCentral()
    }
}

rootProject.buildDir = '../build'
subprojects {
    afterEvaluate {
        if (project.plugins.hasPlugin("com.android.application")
                || project.plugins.hasPlugin("com.android.library")) {

            if (project.name == "flutter_contacts") {
                project.android.compileOptions {
                    sourceCompatibility = JavaVersion.VERSION_1_8
                    targetCompatibility = JavaVersion.VERSION_1_8
                }
                project.android.kotlinOptions {
                    jvmTarget = '1.8'
                }
            }
        }
    }
    project.buildDir = "${rootProject.buildDir}/${project.name}"
    project.evaluationDependsOn(':app')
}
tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
