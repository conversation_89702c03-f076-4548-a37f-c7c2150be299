{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "cloud_firestore", "path": "/Users/<USER>/Documents/flutterVersion/flutter3.3.9/.pub-cache/hosted/pub.dartlang.org/cloud_firestore-0.13.5/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_auth", "path": "/Users/<USER>/Documents/flutterVersion/flutter3.3.9/.pub-cache/hosted/pub.dartlang.org/firebase_auth-0.16.0/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_core", "path": "/Users/<USER>/Documents/flutterVersion/flutter3.3.9/.pub-cache/hosted/pub.dartlang.org/firebase_core-0.4.4+3/", "native_build": true, "dependencies": []}, {"name": "firebase_storage", "path": "/Users/<USER>/Documents/flutterVersion/flutter3.3.9/.pub-cache/hosted/pub.dartlang.org/firebase_storage-3.1.5/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "image_picker", "path": "/Users/<USER>/Documents/flutterVersion/flutter3.3.9/.pub-cache/hosted/pub.dartlang.org/image_picker-0.6.5+1/", "native_build": true, "dependencies": []}], "android": [{"name": "cloud_firestore", "path": "/Users/<USER>/Documents/flutterVersion/flutter3.3.9/.pub-cache/hosted/pub.dartlang.org/cloud_firestore-0.13.5/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_auth", "path": "/Users/<USER>/Documents/flutterVersion/flutter3.3.9/.pub-cache/hosted/pub.dartlang.org/firebase_auth-0.16.0/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_core", "path": "/Users/<USER>/Documents/flutterVersion/flutter3.3.9/.pub-cache/hosted/pub.dartlang.org/firebase_core-0.4.4+3/", "native_build": true, "dependencies": []}, {"name": "firebase_storage", "path": "/Users/<USER>/Documents/flutterVersion/flutter3.3.9/.pub-cache/hosted/pub.dartlang.org/firebase_storage-3.1.5/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "flutter_plugin_android_lifecycle", "path": "/Users/<USER>/Documents/flutterVersion/flutter3.3.9/.pub-cache/hosted/pub.dartlang.org/flutter_plugin_android_lifecycle-1.0.7/", "native_build": true, "dependencies": []}, {"name": "image_picker", "path": "/Users/<USER>/Documents/flutterVersion/flutter3.3.9/.pub-cache/hosted/pub.dartlang.org/image_picker-0.6.5+1/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"]}], "macos": [{"name": "cloud_firestore", "path": "/Users/<USER>/Documents/flutterVersion/flutter3.3.9/.pub-cache/hosted/pub.dartlang.org/cloud_firestore-0.13.5/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_auth", "path": "/Users/<USER>/Documents/flutterVersion/flutter3.3.9/.pub-cache/hosted/pub.dartlang.org/firebase_auth-0.16.0/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_core", "path": "/Users/<USER>/Documents/flutterVersion/flutter3.3.9/.pub-cache/hosted/pub.dartlang.org/firebase_core-0.4.4+3/", "native_build": true, "dependencies": []}, {"name": "firebase_storage", "path": "/Users/<USER>/Documents/flutterVersion/flutter3.3.9/.pub-cache/hosted/pub.dartlang.org/firebase_storage-3.1.5/", "native_build": true, "dependencies": ["firebase_core"]}], "linux": [], "windows": [], "web": [{"name": "cloud_firestore_web", "path": "/Users/<USER>/Documents/flutterVersion/flutter3.3.9/.pub-cache/hosted/pub.dartlang.org/cloud_firestore_web-0.1.1+2/", "dependencies": []}, {"name": "firebase_auth_web", "path": "/Users/<USER>/Documents/flutterVersion/flutter3.3.9/.pub-cache/hosted/pub.dartlang.org/firebase_auth_web-0.1.2/", "dependencies": []}, {"name": "firebase_core_web", "path": "/Users/<USER>/Documents/flutterVersion/flutter3.3.9/.pub-cache/hosted/pub.dartlang.org/firebase_core_web-0.1.1+2/", "dependencies": []}]}, "dependencyGraph": [{"name": "cloud_firestore", "dependencies": ["firebase_core", "cloud_firestore_web"]}, {"name": "cloud_firestore_web", "dependencies": ["firebase_core"]}, {"name": "firebase_auth", "dependencies": ["firebase_core", "firebase_auth_web"]}, {"name": "firebase_auth_web", "dependencies": []}, {"name": "firebase_core", "dependencies": ["firebase_core_web"]}, {"name": "firebase_core_web", "dependencies": []}, {"name": "firebase_storage", "dependencies": ["firebase_core"]}, {"name": "flutter_plugin_android_lifecycle", "dependencies": []}, {"name": "image_picker", "dependencies": ["flutter_plugin_android_lifecycle"]}], "date_created": "2024-03-11 11:21:16.525111", "version": "3.3.9"}