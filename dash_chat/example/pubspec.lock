# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  async:
    dependency: transitive
    description:
      name: async
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.9.0"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  characters:
    dependency: transitive
    description:
      name: characters
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.1"
  charcode:
    dependency: transitive
    description:
      name: charcode
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0"
  clock:
    dependency: transitive
    description:
      name: clock
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.1"
  cloud_firestore:
    dependency: "direct main"
    description:
      name: cloud_firestore
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.13.5"
  cloud_firestore_platform_interface:
    dependency: transitive
    description:
      name: cloud_firestore_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.0"
  cloud_firestore_web:
    dependency: transitive
    description:
      name: cloud_firestore_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.1.1+2"
  collection:
    dependency: transitive
    description:
      name: collection
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.16.0"
  crypto:
    dependency: transitive
    description:
      name: crypto
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.1"
  cupertino_icons:
    dependency: "direct main"
    description:
      name: cupertino_icons
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.1.2"
  dash_chat:
    dependency: "direct dev"
    description:
      path: ".."
      relative: true
    source: path
    version: "1.1.5"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.1"
  firebase:
    dependency: transitive
    description:
      name: firebase
      url: "https://pub.dartlang.org"
    source: hosted
    version: "7.3.0"
  firebase_auth:
    dependency: "direct main"
    description:
      name: firebase_auth
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.16.0"
  firebase_auth_platform_interface:
    dependency: transitive
    description:
      name: firebase_auth_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.7"
  firebase_auth_web:
    dependency: transitive
    description:
      name: firebase_auth_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.1.2"
  firebase_core:
    dependency: transitive
    description:
      name: firebase_core
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.4.4+3"
  firebase_core_platform_interface:
    dependency: transitive
    description:
      name: firebase_core_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.4"
  firebase_core_web:
    dependency: transitive
    description:
      name: firebase_core_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.1.1+2"
  firebase_storage:
    dependency: "direct main"
    description:
      name: firebase_storage
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.5"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_parsed_text:
    dependency: transitive
    description:
      name: flutter_parsed_text
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.0"
  flutter_plugin_android_lifecycle:
    dependency: transitive
    description:
      name: flutter_plugin_android_lifecycle
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.7"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  http:
    dependency: transitive
    description:
      name: http
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.12.0+4"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.4"
  image_picker:
    dependency: "direct main"
    description:
      name: image_picker
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.6.5+1"
  intl:
    dependency: transitive
    description:
      name: intl
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.17.0"
  js:
    dependency: transitive
    description:
      name: js
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.6.4"
  matcher:
    dependency: transitive
    description:
      name: matcher
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.12.12"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.1.5"
  meta:
    dependency: transitive
    description:
      name: meta
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.8.0"
  path:
    dependency: transitive
    description:
      name: path
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.8.2"
  pedantic:
    dependency: transitive
    description:
      name: pedantic
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.8.0+1"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.2"
  quiver:
    dependency: transitive
    description:
      name: quiver
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.5"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.99"
  source_span:
    dependency: transitive
    description:
      name: source_span
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.9.0"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.10.0"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.1"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.1"
  test_api:
    dependency: transitive
    description:
      name: test_api
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.4.12"
  transparent_image:
    dependency: transitive
    description:
      name: transparent_image
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.0"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.0"
  uuid:
    dependency: transitive
    description:
      name: uuid
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.4"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.2"
sdks:
  dart: ">=2.17.0-0 <3.0.0"
  flutter: ">=1.12.13+hotfix.4"
