part of dash_chat;

/// Avatar container for the the chat view uses a [CircleAvatar]
/// widget as default which can be overriden by providing
/// [avatarBuilder] property
class AvatarContainer extends StatelessWidget {
  /// A [ChatUser] object use to get the url of the user
  /// avatar
  final ChatUser user;

  /// [onPress] function takea a function with this structure
  /// [Function(ChatUser)] will trigger when the avatar
  /// is tapped on
  final Function(ChatUser) onPress;

  /// [onLongPress] function takea a function with this structure
  /// [Function(ChatUser)] will trigger when the avatar
  /// is long pressed
  final Function(ChatUser) onLongPress;

  /// [avatarBuilder] function takea a function with this structure
  /// [Widget Function(ChatUser)] to build the avatar
  final Widget Function(ChatUser) avatarBuilder;

  /// [constraints] to apply to build the layout
  /// by default used MediaQuery and take screen size as constaints
  final BoxConstraints constraints;

  const AvatarContainer({
    @required this.user,
    this.onPress,
    this.onLongPress,
    this.avatarBuilder,
    this.constraints,
  });

  @override
  Widget build(BuildContext context) {
    final constraints = this.constraints ??
        BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height,
            maxWidth: MediaQuery.of(context).size.width);
    return GestureDetector(
      onTap: () => onPress != null ? onPress(user) : null,
      onLongPress: () => onLongPress != null ? onLongPress(user) : null,
      child: avatarBuilder != null
          ? avatarBuilder(user)
          : Stack(
              alignment: Alignment.center,
              children: <Widget>[
                ClipOval(
                  child: Container(
                    height: constraints.maxWidth * 0.15,
                    width: constraints.maxWidth * 0.15,
                    child: Center(
                        child: Text(user.name == null || user.name.isEmpty
                            ? ''
                            : user.name[0])),
                  ),
                ),
                user.avatar != null && user.avatar.length != 0
                    ? Container(
                        decoration:
                            BoxDecoration(shape: BoxShape.circle, boxShadow: [
                          BoxShadow(
                            color: LikeWalletAppTheme.black.withOpacity(0.5),
                            spreadRadius: 3,
                            blurRadius: 6,
                            offset: Offset(2, 1), // changes position of shadow
                          ),
                        ]),
                        child: ClipOval(
                          child: FadeInImage.memoryNetwork(
                            image: user.avatar,
                            placeholder: kTransparentImage,
                            fit: BoxFit.cover,
                            height: constraints.maxWidth * 0.15,
                            width: constraints.maxWidth * 0.15,
                          ),
                        ),
                      )
                    : Container()
              ],
            ),
    );
  }
}
