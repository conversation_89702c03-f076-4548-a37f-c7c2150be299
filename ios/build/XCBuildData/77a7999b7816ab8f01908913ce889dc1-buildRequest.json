{"_buildCommand2": {"command": "prepareForIndexing", "enableIndexBuildArena": false, "targets": null}, "buildCommand": "build", "configuredTargets": [{"guid": "18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49"}], "containerPath": "/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner.xcodeproj", "continueBuildingAfterErrors": true, "enableIndexBuildArena": false, "hideShellScriptEnvironment": false, "parameters": {"action": "build", "activeArchitecture": "x86_64", "activeRunDestination": {"disableOnlyActiveArch": false, "platform": "iphonesimulator", "sdk": "iphonesimulator15.0", "sdkVariant": "iphonesimulator", "supportedArchitectures": ["x86_64"], "targetArchitecture": "x86_64"}, "arenaInfo": {"buildIntermediatesPath": "", "buildProductsPath": "", "derivedDataPath": "/Users/<USER>/Library/Developer/Xcode/DerivedData", "indexDataStoreFolderPath": "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-hbmtdpxidruszpaaeuveldpjuspm/Index/DataStore", "indexEnableDataStore": true, "indexPCHPath": "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-hbmtdpxidruszpaaeuveldpjuspm/Index/PrecompiledHeaders", "pchPath": ""}, "configurationName": "Debug", "overrides": {"synthesized": {"table": {"ASSETCATALOG_FILTER_FOR_DEVICE_MODEL": "iPhone10,4", "ASSETCATALOG_FILTER_FOR_DEVICE_OS_VERSION": "15.0", "BUILD_ACTIVE_RESOURCES_ONLY": "YES", "ENABLE_PREVIEWS": "NO", "TARGET_DEVICE_IDENTIFIER": "7E256F87-A048-497E-B1B0-F06B3A14E031", "TARGET_DEVICE_MODEL": "iPhone10,4", "TARGET_DEVICE_OS_VERSION": "15.0", "TARGET_DEVICE_PLATFORM_NAME": "iphonesimulator"}}}}, "schemeCommand": "launch", "schemeCommand2": "launch", "shouldCollectMetrics": false, "showNonLoggedProgress": true, "useDryRun": false, "useImplicitDependencies": true, "useLegacyBuildLocations": false, "useParallelTargets": true}