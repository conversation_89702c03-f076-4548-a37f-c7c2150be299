client:
  name: basic
  version: 0
  file-system: default

targets:
  "": ["<all>"]

nodes:
  "/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios": {"is-mutated":true}
  "/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app": {"is-mutated":true}
  "/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Runner": {"is-mutated":true}
  "<TRIGGER: Ld /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Runner normal>": {"is-command-timestamp":true}
  "<TRIGGER: MkDir /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app>": {"is-command-timestamp":true}

commands:
  "<all>": {"tool":"phony","inputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/_CodeSignature","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--end>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--modules-ready>"],"outputs":["<all>"]}
  "<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-Debug-iphonesimulator-x86_64-stale-file-removal>": {"tool":"stale-file-removal","expectedOutputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Runner","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/_CodeSignature","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/assetcatalog_generated_info.plist","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Assets.car","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen-SBPartialInfo.plist","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main.storyboardc","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main-SBPartialInfo.plist","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/AppDelegate.o","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/AppFrameworkInfo.plist","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/GoogleService-Info.plist","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64.swiftsourceinfo","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftdoc","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftmodule","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftdoc","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftmodule","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner-Swift.h","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Runner","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Base.lproj/Main.storyboardc","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Pods-Runner-checkManifestLockResult.txt","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/openssl_grpc.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FMDB.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Flutter.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GTMSessionFetcher.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleAPIClientForREST.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleToolboxForMac.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleUtilities.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/IdensicMobileSDK.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/IdensicMobileSDK_Liveness3D.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBLPromises.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Protobuf.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Reachability.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/ZoomAuthentication.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/absl.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/app_settings.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/camera.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/connectivity.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/contacts_service.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/esys_flutter_share.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_appavailability.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_idensic_mobile_sdk_plugin.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_local_notifications.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_secure_storage.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_share_me.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/fluttertoast.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/grpcpp.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/grpc.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_crop.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_gallery_saver.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_picker.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/keyboard_visibility.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/launch_review.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/leveldb.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/local_auth.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/nanopb.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/open_appstore.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/package_info.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/path_provider.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/permission.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/qrcode_flutter.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/share.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/shared_preferences.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/simple_image_crop.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sms_autofill.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sms_receiver.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sqflite.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/store_redirect.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/url_launcher.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/video_player.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/webview_flutter.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Info.plist","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app.xcent","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Entitlements-Simulated.plist","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Entitlements.plist","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-non-framework-target-headers.hmap","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-target-headers.hmap","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-generated-files.hmap","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-own-target-headers.hmap","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-project-headers.hmap","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.hmap","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-328736DAE7F587435A6E60E5.sh","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3759590B8F78A0B90ADCF815.sh","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/all-product-headers.yaml"],"roots":["/tmp/Runner.dst","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios"],"outputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-Debug-iphonesimulator-x86_64-stale-file-removal>"]}
  "Gate target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangeAlternatePermissions": {"tool":"phony","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangePermissions>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangeAlternatePermissions>"]}
  "Gate target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangePermissions": {"tool":"phony","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--Barrier-StripSymbols>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangePermissions>"]}
  "Gate target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--Barrier-CodeSign": {"tool":"phony","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangeAlternatePermissions>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","<CodeSign /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app>"],"outputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--Barrier-CodeSign>"]}
  "Gate target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--Barrier-CopyAside": {"tool":"phony","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--ProductPostprocessingTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--Barrier-CopyAside>"]}
  "Gate target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--Barrier-RegisterExecutionPolicyException": {"tool":"phony","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--Barrier-CodeSign>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","<RegisterExecutionPolicyException /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app>"],"outputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--Barrier-RegisterExecutionPolicyException>"]}
  "Gate target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--Barrier-RegisterProduct": {"tool":"phony","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--Barrier-Validate>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","<Touch /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app>"],"outputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--Barrier-RegisterProduct>"]}
  "Gate target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--Barrier-StripSymbols": {"tool":"phony","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--Barrier-CopyAside>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--Barrier-StripSymbols>"]}
  "Gate target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--Barrier-Validate": {"tool":"phony","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--Barrier-RegisterExecutionPolicyException>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--Barrier-Validate>"]}
  "Gate target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--CopySwiftPackageResourcesTaskProducer": {"tool":"phony","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--CopySwiftPackageResourcesTaskProducer>"]}
  "Gate target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer": {"tool":"phony","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--XCFrameworkTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app.xcent","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Entitlements-Simulated.plist","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Entitlements.plist"],"outputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>"]}
  "Gate target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer": {"tool":"phony","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-non-framework-target-headers.hmap","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-target-headers.hmap","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-generated-files.hmap","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-own-target-headers.hmap","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-project-headers.hmap","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.hmap","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/all-product-headers.yaml"],"outputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>"]}
  "Gate target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--InfoPlistTaskProducer": {"tool":"phony","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Info.plist"],"outputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--InfoPlistTaskProducer>"]}
  "Gate target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer": {"tool":"phony","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>"]}
  "Gate target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--ProductPostprocessingTaskProducer": {"tool":"phony","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase7--cp--embed-pods-frameworks>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--InfoPlistTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--VersionPlistTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--SanitizerTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--SwiftStandardLibrariesTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--SwiftFrameworkABICheckerTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--StubBinaryTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--TestTargetTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--CopySwiftPackageResourcesTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--ProductPostprocessingTaskProducer>"]}
  "Gate target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--ProductStructureTaskProducer": {"tool":"phony","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--start>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","<MkDir /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app>","<MkDir /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks>"],"outputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--ProductStructureTaskProducer>"]}
  "Gate target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--SanitizerTaskProducer": {"tool":"phony","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--SanitizerTaskProducer>"]}
  "Gate target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--StubBinaryTaskProducer": {"tool":"phony","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--StubBinaryTaskProducer>"]}
  "Gate target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--SwiftFrameworkABICheckerTaskProducer": {"tool":"phony","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase7--cp--embed-pods-frameworks>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--SwiftFrameworkABICheckerTaskProducer>"]}
  "Gate target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--SwiftStandardLibrariesTaskProducer": {"tool":"phony","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase7--cp--embed-pods-frameworks>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","<CopySwiftStdlib /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app>"],"outputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--SwiftStandardLibrariesTaskProducer>"]}
  "Gate target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--TestTargetPostprocessingTaskProducer": {"tool":"phony","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--ProductPostprocessingTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--TestTargetPostprocessingTaskProducer>"]}
  "Gate target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--TestTargetTaskProducer": {"tool":"phony","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--TestTargetTaskProducer>"]}
  "Gate target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--VersionPlistTaskProducer": {"tool":"phony","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--VersionPlistTaskProducer>"]}
  "Gate target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--XCFrameworkTaskProducer": {"tool":"phony","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--ProductStructureTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--XCFrameworkTaskProducer>"]}
  "Gate target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling": {"tool":"phony","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-Debug-iphonesimulator-x86_64-stale-file-removal>","<CreateBuildDirectory-/tmp/Runner.dst>","<CreateBuildDirectory-/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios>","<CreateBuildDirectory-/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios>"],"outputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"]}
  "Gate target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion": {"tool":"phony","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>"]}
  "Gate target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--end": {"tool":"phony","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--entry>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangeAlternatePermissions>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangePermissions>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--Barrier-CodeSign>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--Barrier-CopyAside>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--Barrier-RegisterExecutionPolicyException>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--Barrier-RegisterProduct>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--Barrier-StripSymbols>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--Barrier-Validate>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--CopySwiftPackageResourcesTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--InfoPlistTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--ProductPostprocessingTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--ProductStructureTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--SanitizerTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--StubBinaryTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--SwiftFrameworkABICheckerTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--SwiftStandardLibrariesTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--TestTargetPostprocessingTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--TestTargetTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--VersionPlistTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--XCFrameworkTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--generated-headers>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase0--cp--check-pods-manifest-lock>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase4-copy-bundle-resources>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase5-copy-files>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase6-thin-binary>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase7--cp--embed-pods-frameworks>","<CodeSign /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app>","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/assetcatalog_generated_info.plist","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Assets.car","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen-SBPartialInfo.plist","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main.storyboardc","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main-SBPartialInfo.plist","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/AppDelegate.o","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/AppFrameworkInfo.plist","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/GoogleService-Info.plist","<CopySwiftStdlib /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app>","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64.swiftsourceinfo","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftdoc","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftmodule","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftdoc","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftmodule","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner-Swift.h","<Linked Binary /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Runner>","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Base.lproj/Main.storyboardc","<MkDir /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app>","<MkDir /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks>","<execute-shell-script-b97f721a235acf137390128f1b58ef8c9eb60ff613d36b3b9942d1a6e1e7c6aa>","<execute-shell-script-b97f721a235acf137390128f1b58ef8cf1eee2015e8ff5ebcd27678f788c2826>","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Pods-Runner-checkManifestLockResult.txt","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/openssl_grpc.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FMDB.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Flutter.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GTMSessionFetcher.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleAPIClientForREST.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleToolboxForMac.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleUtilities.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/IdensicMobileSDK.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/IdensicMobileSDK_Liveness3D.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBLPromises.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Protobuf.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Reachability.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/ZoomAuthentication.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/absl.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/app_settings.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/camera.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/connectivity.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/contacts_service.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/esys_flutter_share.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_appavailability.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_idensic_mobile_sdk_plugin.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_local_notifications.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_secure_storage.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_share_me.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/fluttertoast.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/grpcpp.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/grpc.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_crop.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_gallery_saver.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_picker.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/keyboard_visibility.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/launch_review.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/leveldb.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/local_auth.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/nanopb.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/open_appstore.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/package_info.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/path_provider.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/permission.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/qrcode_flutter.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/share.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/shared_preferences.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/simple_image_crop.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sms_autofill.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sms_receiver.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sqflite.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/store_redirect.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/url_launcher.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/video_player.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/webview_flutter.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Info.plist","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app.xcent","<RegisterExecutionPolicyException /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app>","<Touch /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app>","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Entitlements-Simulated.plist","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Entitlements.plist","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-non-framework-target-headers.hmap","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-target-headers.hmap","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-generated-files.hmap","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-own-target-headers.hmap","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-project-headers.hmap","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.hmap","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-328736DAE7F587435A6E60E5.sh","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3759590B8F78A0B90ADCF815.sh","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/all-product-headers.yaml"],"outputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--end>"]}
  "Gate target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--entry": {"tool":"phony","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-Debug-iphonesimulator-x86_64-stale-file-removal>","<CreateBuildDirectory-/tmp/Runner.dst>","<CreateBuildDirectory-/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios>","<CreateBuildDirectory-/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--entry>"]}
  "Gate target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--generated-headers": {"tool":"phony","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/AppDelegate.o","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner-Swift.h"],"outputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--generated-headers>"]}
  "Gate target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--immediate": {"tool":"phony","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-Debug-iphonesimulator-x86_64-stale-file-removal>","<CreateBuildDirectory-/tmp/Runner.dst>","<CreateBuildDirectory-/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios>","<CreateBuildDirectory-/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios>"],"outputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--immediate>"]}
  "Gate target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--modules-ready": {"tool":"phony","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/AppDelegate.o","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64.swiftsourceinfo","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftdoc","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftmodule","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftdoc","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftmodule","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner-Swift.h","<execute-shell-script-b97f721a235acf137390128f1b58ef8c9eb60ff613d36b3b9942d1a6e1e7c6aa>","<execute-shell-script-b97f721a235acf137390128f1b58ef8cf1eee2015e8ff5ebcd27678f788c2826>","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Pods-Runner-checkManifestLockResult.txt","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/openssl_grpc.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FMDB.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Flutter.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GTMSessionFetcher.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleAPIClientForREST.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleToolboxForMac.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleUtilities.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/IdensicMobileSDK.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/IdensicMobileSDK_Liveness3D.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBLPromises.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Protobuf.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Reachability.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/ZoomAuthentication.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/absl.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/app_settings.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/camera.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/connectivity.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/contacts_service.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/esys_flutter_share.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_appavailability.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_idensic_mobile_sdk_plugin.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_local_notifications.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_secure_storage.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_share_me.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/fluttertoast.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/grpcpp.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/grpc.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_crop.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_gallery_saver.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_picker.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/keyboard_visibility.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/launch_review.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/leveldb.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/local_auth.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/nanopb.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/open_appstore.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/package_info.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/path_provider.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/permission.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/qrcode_flutter.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/share.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/shared_preferences.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/simple_image_crop.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sms_autofill.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sms_receiver.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sqflite.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/store_redirect.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/url_launcher.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/video_player.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/webview_flutter.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-328736DAE7F587435A6E60E5.sh","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3759590B8F78A0B90ADCF815.sh","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh"],"outputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--modules-ready>"]}
  "Gate target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase0--cp--check-pods-manifest-lock": {"tool":"phony","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Pods-Runner-checkManifestLockResult.txt","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-328736DAE7F587435A6E60E5.sh"],"outputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase0--cp--check-pods-manifest-lock>"]}
  "Gate target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script": {"tool":"phony","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase0--cp--check-pods-manifest-lock>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","<execute-shell-script-b97f721a235acf137390128f1b58ef8c9eb60ff613d36b3b9942d1a6e1e7c6aa>","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh"],"outputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>"]}
  "Gate target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources": {"tool":"phony","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/AppDelegate.o","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64.swiftsourceinfo","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftdoc","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftmodule","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftdoc","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftmodule","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner-Swift.h","<Linked Binary /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Runner>","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList"],"outputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>"]}
  "Gate target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase4-copy-bundle-resources": {"tool":"phony","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/assetcatalog_generated_info.plist","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Assets.car","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen-SBPartialInfo.plist","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main.storyboardc","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main-SBPartialInfo.plist","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/AppFrameworkInfo.plist","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/GoogleService-Info.plist","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Base.lproj/Main.storyboardc"],"outputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase4-copy-bundle-resources>"]}
  "Gate target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase5-copy-files": {"tool":"phony","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase4-copy-bundle-resources>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase5-copy-files>"]}
  "Gate target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase6-thin-binary": {"tool":"phony","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase5-copy-files>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","<execute-shell-script-b97f721a235acf137390128f1b58ef8cf1eee2015e8ff5ebcd27678f788c2826>","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh"],"outputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase6-thin-binary>"]}
  "Gate target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase7--cp--embed-pods-frameworks": {"tool":"phony","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase6-thin-binary>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/openssl_grpc.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FMDB.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Flutter.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GTMSessionFetcher.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleAPIClientForREST.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleToolboxForMac.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleUtilities.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/IdensicMobileSDK.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/IdensicMobileSDK_Liveness3D.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBLPromises.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Protobuf.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Reachability.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/ZoomAuthentication.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/absl.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/app_settings.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/camera.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/connectivity.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/contacts_service.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/esys_flutter_share.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_appavailability.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_idensic_mobile_sdk_plugin.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_local_notifications.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_secure_storage.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_share_me.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/fluttertoast.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/grpcpp.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/grpc.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_crop.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_gallery_saver.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_picker.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/keyboard_visibility.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/launch_review.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/leveldb.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/local_auth.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/nanopb.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/open_appstore.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/package_info.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/path_provider.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/permission.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/qrcode_flutter.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/share.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/shared_preferences.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/simple_image_crop.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sms_autofill.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sms_receiver.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sqflite.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/store_redirect.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/url_launcher.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/video_player.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/webview_flutter.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3759590B8F78A0B90ADCF815.sh"],"outputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase7--cp--embed-pods-frameworks>"]}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:CodeSign /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app": {"tool":"shell","description":"CodeSign /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app","inputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Info.plist/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app.xcent/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBLPromises.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FMDB.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Flutter.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GTMSessionFetcher.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleAPIClientForREST.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleToolboxForMac.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleUtilities.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/IdensicMobileSDK.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/IdensicMobileSDK_Liveness3D.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Protobuf.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Reachability.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/ZoomAuthentication.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/absl.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/app_settings.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/camera.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/connectivity.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/contacts_service.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/esys_flutter_share.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_appavailability.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_idensic_mobile_sdk_plugin.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_local_notifications.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_secure_storage.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_share_me.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/fluttertoast.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/grpc.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/grpcpp.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_crop.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_gallery_saver.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_picker.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/keyboard_visibility.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/launch_review.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/leveldb.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/local_auth.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/nanopb.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/open_appstore.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/openssl_grpc.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/package_info.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/path_provider.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/permission.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/qrcode_flutter.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/share.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/shared_preferences.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/simple_image_crop.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sms_autofill.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sms_receiver.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sqflite.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/store_redirect.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/url_launcher.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/video_player.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/webview_flutter.framework/","/Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Flutter/AppFrameworkInfo.plist/","/Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Runner/Assets.xcassets/","/Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Runner/Base.lproj/LaunchScreen.storyboard/","/Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Runner/Base.lproj/Main.storyboard/","/Users/<USER>/Downloads/GoogleService-Info.plist/","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangeAlternatePermissions>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--entry>","<TRIGGER: Ld /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Runner normal>","<TRIGGER: MkDir /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/_CodeSignature","<CodeSign /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app>"],"args":["/usr/bin/codesign","--force","--sign","-","--entitlements","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app.xcent","--timestamp=none","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app"],"env":{"CODESIGN_ALLOCATE":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/codesign_allocate"},"can-safely-interrupt":false,"working-directory":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","signature":"d99abdad0eb2eb0870e727cad4afb93f"}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:CompileAssetCatalog /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app /Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Runner/Assets.xcassets": {"tool":"shell","description":"CompileAssetCatalog /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app /Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Runner/Assets.xcassets","inputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Runner/Assets.xcassets/","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/assetcatalog_generated_info.plist","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Assets.car"],"args":["/Applications/Xcode.app/Contents/Developer/usr/bin/actool","--output-format","human-readable-text","--notices","--warnings","--export-dependency-info","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/assetcatalog_dependencies","--output-partial-info-plist","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/assetcatalog_generated_info.plist","--app-icon","AppIcon","--compress-pngs","--enable-on-demand-resources","YES","--filter-for-device-model","iPod9,1","--filter-for-device-os-version","14.1","--development-region","en","--target-device","iphone","--target-device","ipad","--minimum-deployment-target","12.0","--platform","iphonesimulator","--compile","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app","/Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Runner/Assets.xcassets"],"env":{},"working-directory":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","control-enabled":false,"deps":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/assetcatalog_dependencies"],"deps-style":"dependency-info","signature":"d9631fd01169fe451253845c847d92bd"}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:CompileC /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o /Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Runner/GeneratedPluginRegistrant.m normal x86_64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool":"shell","description":"CompileC /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o /Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Runner/GeneratedPluginRegistrant.m normal x86_64 objective-c com.apple.compilers.llvm.clang.1_0.compiler","inputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Runner/GeneratedPluginRegistrant.m","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--generated-headers>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o"],"args":["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang","-x","objective-c","-target","x86_64-apple-ios12.0-simulator","-fmessage-length=0","-fdiagnostics-show-note-include-stack","-fmacro-backtrace-limit=0","-std=gnu99","-fobjc-arc","-fmodules","-gmodules","-fmodules-cache-path=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex","-fmodules-prune-interval=86400","-fmodules-prune-after=345600","-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation","-fmodules-validate-once-per-build-session","-Wnon-modular-include-in-framework-module","-Werror=non-modular-include-in-framework-module","-Wno-trigraphs","-fpascal-strings","-O0","-fno-common","-Wno-missing-field-initializers","-Wno-missing-prototypes","-Werror=return-type","-Wunreachable-code","-Wquoted-include-in-framework-header","-Wno-implicit-atomic-properties","-Werror=deprecated-objc-isa-usage","-Wno-objc-interface-ivars","-Werror=objc-root-class","-Wno-arc-repeated-use-of-weak","-Wimplicit-retain-self","-Wduplicate-method-match","-Wno-missing-braces","-Wparentheses","-Wswitch","-Wunused-function","-Wno-unused-label","-Wno-unused-parameter","-Wunused-variable","-Wunused-value","-Wempty-body","-Wuninitialized","-Wconditional-uninitialized","-Wno-unknown-pragmas","-Wno-shadow","-Wno-four-char-constants","-Wno-conversion","-Wconstant-conversion","-Wint-conversion","-Wbool-conversion","-Wenum-conversion","-Wno-float-conversion","-Wnon-literal-null-conversion","-Wobjc-literal-conversion","-Wshorten-64-to-32","-Wpointer-sign","-Wno-newline-eof","-Wno-selector","-Wno-strict-selector-match","-Wundeclared-selector","-Wdeprecated-implementations","-DDEBUG=1","-DOBJC_OLD_DISPATCH_PROTOTYPES=0","-isysroot","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.1.sdk","-fasm-blocks","-fstrict-aliasing","-Wprotocol","-Wdeprecated-declarations","-g","-Wno-sign-conversion","-Winfinite-recursion","-Wcomma","-Wblock-capture-autoreleasing","-Wstrict-prototypes","-Wno-semicolon-before-method-body","-fobjc-abi-version=2","-fobjc-legacy-dispatch","-index-store-path","/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-fkbtwimyrzsitfdjgucczdgaurfg/Index/DataStore","-iquote","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-generated-files.hmap","-I/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-own-target-headers.hmap","-I/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-target-headers.hmap","-iquote","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-project-headers.hmap","-I/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/include","-I/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources-normal/x86_64","-I/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/x86_64","-I/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","-F/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator","-F/Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Flutter","-MMD","-MT","dependencies","-MF","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.d","--serialize-diagnostics","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.dia","-c","/Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Runner/GeneratedPluginRegistrant.m","-o","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o"],"env":{"LANG":"en_US.US-ASCII"},"working-directory":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","deps":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.d"],"deps-style":"makefile","signature":"32d1e8ddc2dfc1aaacd3e09ec1e0377a"}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:CompileC /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c normal x86_64 c com.apple.compilers.llvm.clang.1_0.compiler": {"tool":"shell","description":"CompileC /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c normal x86_64 c com.apple.compilers.llvm.clang.1_0.compiler","inputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--generated-headers>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o"],"args":["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang","-x","c","-target","x86_64-apple-ios12.0-simulator","-fmessage-length=0","-fdiagnostics-show-note-include-stack","-fmacro-backtrace-limit=0","-std=gnu99","-fmodules","-gmodules","-fmodules-cache-path=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex","-fmodules-prune-interval=86400","-fmodules-prune-after=345600","-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation","-fmodules-validate-once-per-build-session","-Wnon-modular-include-in-framework-module","-Werror=non-modular-include-in-framework-module","-Wno-trigraphs","-fpascal-strings","-O0","-fno-common","-Wno-missing-field-initializers","-Wno-missing-prototypes","-Werror=return-type","-Wunreachable-code","-Wquoted-include-in-framework-header","-Werror=deprecated-objc-isa-usage","-Werror=objc-root-class","-Wno-missing-braces","-Wparentheses","-Wswitch","-Wunused-function","-Wno-unused-label","-Wno-unused-parameter","-Wunused-variable","-Wunused-value","-Wempty-body","-Wuninitialized","-Wconditional-uninitialized","-Wno-unknown-pragmas","-Wno-shadow","-Wno-four-char-constants","-Wno-conversion","-Wconstant-conversion","-Wint-conversion","-Wbool-conversion","-Wenum-conversion","-Wno-float-conversion","-Wnon-literal-null-conversion","-Wobjc-literal-conversion","-Wshorten-64-to-32","-Wpointer-sign","-Wno-newline-eof","-DDEBUG=1","-isysroot","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.1.sdk","-fasm-blocks","-fstrict-aliasing","-Wdeprecated-declarations","-g","-Wno-sign-conversion","-Winfinite-recursion","-Wcomma","-Wblock-capture-autoreleasing","-Wstrict-prototypes","-Wno-semicolon-before-method-body","-index-store-path","/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-fkbtwimyrzsitfdjgucczdgaurfg/Index/DataStore","-iquote","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-generated-files.hmap","-I/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-own-target-headers.hmap","-I/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-target-headers.hmap","-iquote","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-project-headers.hmap","-I/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/include","-I/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources-normal/x86_64","-I/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/x86_64","-I/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","-F/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator","-F/Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Flutter","-MMD","-MT","dependencies","-MF","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.d","--serialize-diagnostics","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.dia","-c","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c","-o","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o"],"env":{"LANG":"en_US.US-ASCII"},"working-directory":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","deps":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.d"],"deps-style":"makefile","signature":"ad5fd34d4efc4ea0efaa388c7f5db9f2"}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:CompileStoryboard /Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Runner/Base.lproj/LaunchScreen.storyboard": {"tool":"shell","description":"CompileStoryboard /Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Runner/Base.lproj/LaunchScreen.storyboard","inputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Runner/Base.lproj/LaunchScreen.storyboard","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen-SBPartialInfo.plist"],"args":["/Applications/Xcode.app/Contents/Developer/usr/bin/ibtool","--errors","--warnings","--notices","--module","Runner","--output-partial-info-plist","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen-SBPartialInfo.plist","--auto-activate-custom-fonts","--target-device","iphone","--target-device","ipad","--minimum-deployment-target","12.0","--output-format","human-readable-text","--compilation-directory","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj","/Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Runner/Base.lproj/LaunchScreen.storyboard"],"env":{"XCODE_DEVELOPER_USR_PATH":"/Applications/Xcode.app/Contents/Developer/usr/bin/.."},"working-directory":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","control-enabled":false,"signature":"d2c09f82fe475bfd45da02df700de2c3"}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:CompileStoryboard /Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Runner/Base.lproj/Main.storyboard": {"tool":"shell","description":"CompileStoryboard /Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Runner/Base.lproj/Main.storyboard","inputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Runner/Base.lproj/Main.storyboard","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main.storyboardc","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main-SBPartialInfo.plist"],"args":["/Applications/Xcode.app/Contents/Developer/usr/bin/ibtool","--errors","--warnings","--notices","--module","Runner","--output-partial-info-plist","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main-SBPartialInfo.plist","--auto-activate-custom-fonts","--target-device","iphone","--target-device","ipad","--minimum-deployment-target","12.0","--output-format","human-readable-text","--compilation-directory","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj","/Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Runner/Base.lproj/Main.storyboard"],"env":{"XCODE_DEVELOPER_USR_PATH":"/Applications/Xcode.app/Contents/Developer/usr/bin/.."},"working-directory":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","control-enabled":false,"signature":"be5bb59bbc57f2ca7c747c3c66557084"}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:CompileSwiftSources normal x86_64 com.apple.xcode.tools.swift.compiler": {"tool":"shell","description":"CompileSwiftSources normal x86_64 com.apple.xcode.tools.swift.compiler","inputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Runner/AppDelegate.swift","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-generated-files.hmap","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-own-target-headers.hmap","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-target-headers.hmap","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-project-headers.hmap","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/AppDelegate.o","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc"],"args":["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-incremental","-module-name","Runner","-Onone","-enable-batch-mode","-enforce-exclusivity=checked","@/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.1.sdk","-target","x86_64-apple-ios12.0-simulator","-g","-module-cache-path","/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex","-Xfrontend","-serialize-debugging-options","-enable-testing","-index-store-path","/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-fkbtwimyrzsitfdjgucczdgaurfg/Index/DataStore","-swift-version","5","-I","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator","-F","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator","-F","/Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Flutter","-parse-as-library","-c","-j12","-output-file-map","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json","-parseable-output","-serialize-diagnostics","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule","-Xcc","-I/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/swift-overrides.hmap","-Xcc","-iquote","-Xcc","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-generated-files.hmap","-Xcc","-I/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-own-target-headers.hmap","-Xcc","-I/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-target-headers.hmap","-Xcc","-iquote","-Xcc","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-project-headers.hmap","-Xcc","-I/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/include","-Xcc","-I/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources-normal/x86_64","-Xcc","-I/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/x86_64","-Xcc","-I/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","-Xcc","-DDEBUG=1","-emit-objc-header","-emit-objc-header-path","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h","-import-objc-header","/Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Runner/Runner-Bridging-Header.h","-pch-output-dir","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/SharedPrecompiledHeaders","-working-directory","/Users/<USER>/Dabzshot/LikeWalletPharse3/ios"],"env":{"DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","SDKROOT":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.1.sdk"},"working-directory":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","deps":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/AppDelegate.d"],"deps-style":"makefile","signature":"31132aa11be66993c20c29de4d9b2858"}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:CopyPlistFile /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/AppFrameworkInfo.plist /Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Flutter/AppFrameworkInfo.plist": {"tool":"copy-plist","description":"CopyPlistFile /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/AppFrameworkInfo.plist /Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Flutter/AppFrameworkInfo.plist","inputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Flutter/AppFrameworkInfo.plist","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/AppFrameworkInfo.plist"]}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:CopyPlistFile /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/GoogleService-Info.plist /Users/<USER>/Downloads/GoogleService-Info.plist": {"tool":"copy-plist","description":"CopyPlistFile /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/GoogleService-Info.plist /Users/<USER>/Downloads/GoogleService-Info.plist","inputs":["/Users/<USER>/Downloads/GoogleService-Info.plist","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/GoogleService-Info.plist"]}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:CopySwiftLibs /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app": {"tool":"embed-swift-stdlib","description":"CopySwiftLibs /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app","inputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Runner","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase7--cp--embed-pods-frameworks>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["<CopySwiftStdlib /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app>"],"deps":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/SwiftStdLibToolInputDependencies.dep"}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:CreateBuildDirectory /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios": {"tool":"create-build-directory","description":"CreateBuildDirectory /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios","inputs":[],"outputs":["<CreateBuildDirectory-/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios>","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios"]}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:Ditto /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo": {"tool":"shell","description":"Ditto /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo","inputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo"],"args":["/usr/bin/ditto","-rsrc","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo"],"env":{},"working-directory":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","signature":"2a6cd41135a5374a71de02ef5cc9c805"}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:Ditto /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64.swiftsourceinfo /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo": {"tool":"shell","description":"Ditto /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64.swiftsourceinfo /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo","inputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64.swiftsourceinfo"],"args":["/usr/bin/ditto","-rsrc","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64.swiftsourceinfo"],"env":{},"working-directory":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","signature":"ad7ad301c61951cb785c2b8e454ba2c9"}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:Ditto /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftdoc /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc": {"tool":"shell","description":"Ditto /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftdoc /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc","inputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftdoc"],"args":["/usr/bin/ditto","-rsrc","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftdoc"],"env":{},"working-directory":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","signature":"6743a7618a7ab0aec1327083404b52d2"}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:Ditto /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftmodule /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule": {"tool":"shell","description":"Ditto /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftmodule /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule","inputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftmodule"],"args":["/usr/bin/ditto","-rsrc","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftmodule"],"env":{},"working-directory":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","signature":"2d712bae15de53e292b55aea71fbc71f"}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:Ditto /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftdoc /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc": {"tool":"shell","description":"Ditto /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftdoc /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc","inputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftdoc"],"args":["/usr/bin/ditto","-rsrc","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftdoc"],"env":{},"working-directory":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","signature":"f482504514e21d769874104eb68c79ce"}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:Ditto /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftmodule /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule": {"tool":"shell","description":"Ditto /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftmodule /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule","inputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftmodule"],"args":["/usr/bin/ditto","-rsrc","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftmodule"],"env":{},"working-directory":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","signature":"92aa856fd367f954a9afe5b0a1c6dd8d"}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:Ditto /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner-Swift.h /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h": {"tool":"shell","description":"Ditto /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner-Swift.h /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h","inputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner-Swift.h"],"args":["/usr/bin/ditto","-rsrc","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner-Swift.h"],"env":{},"working-directory":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","signature":"fc087438b20fb53f13e7d1104b36a9ea"}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:Ld /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Runner normal": {"tool":"shell","description":"Ld /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Runner normal","inputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/AppDelegate.o","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Runner","<Linked Binary /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Runner>","<TRIGGER: Ld /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Runner normal>"],"args":["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang","-target","x86_64-apple-ios12.0-simulator","-isysroot","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.1.sdk","-L/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator","-L/Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Flutter","-F/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator","-F/Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Flutter","-filelist","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList","-Xlinker","-rpath","-Xlinker","/usr/lib/swift","-Xlinker","-rpath","-Xlinker","@executable_path/Frameworks","-dead_strip","-Xlinker","-object_path_lto","-Xlinker","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_lto.o","-Xlinker","-export_dynamic","-Xlinker","-no_deduplicate","-Xlinker","-objc_abi_version","-Xlinker","2","-fobjc-arc","-fobjc-link-runtime","-L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator","-L/usr/lib/swift","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule","-framework","Flutter","-Xlinker","-sectcreate","-Xlinker","__TEXT","-Xlinker","__entitlements","-Xlinker","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent","-framework","Pods_Runner","-Xlinker","-dependency_info","-Xlinker","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_dependency_info.dat","-o","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Runner"],"env":{},"working-directory":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","deps":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_dependency_info.dat"],"deps-style":"dependency-info","signature":"7a596f55317f0cd46317874eac4d9c5f"}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:LinkStoryboards": {"tool":"shell","description":"LinkStoryboards","inputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main.storyboardc","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Base.lproj/Main.storyboardc"],"args":["/Applications/Xcode.app/Contents/Developer/usr/bin/ibtool","--errors","--warnings","--notices","--module","Runner","--target-device","iphone","--target-device","ipad","--minimum-deployment-target","12.0","--output-format","human-readable-text","--link","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main.storyboardc"],"env":{"XCODE_DEVELOPER_USR_PATH":"/Applications/Xcode.app/Contents/Developer/usr/bin/.."},"working-directory":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","control-enabled":false,"signature":"ec655acadd1a7ce99effbbd594a052aa"}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:MkDir /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app": {"tool":"mkdir","description":"MkDir /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--start>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app","<MkDir /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app>","<TRIGGER: MkDir /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app>"]}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:MkDir /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks": {"tool":"mkdir","description":"MkDir /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--start>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks","<MkDir /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks>"]}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:PhaseScriptExecution Run Script /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh": {"tool":"shell","description":"PhaseScriptExecution Run Script /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh","inputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase0--cp--check-pods-manifest-lock>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["<execute-shell-script-b97f721a235acf137390128f1b58ef8c9eb60ff613d36b3b9942d1a6e1e7c6aa>"],"args":["/bin/sh","-c","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh"],"env":{"ACTION":"build","AD_HOC_CODE_SIGNING_ALLOWED":"YES","ALTERNATE_GROUP":"staff","ALTERNATE_MODE":"u+w,go-w,a+rX","ALTERNATE_OWNER":"oil","ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES":"NO","ALWAYS_SEARCH_USER_PATHS":"NO","ALWAYS_USE_SEPARATE_HEADERMAPS":"NO","APPLE_INTERNAL_DEVELOPER_DIR":"/AppleInternal/Developer","APPLE_INTERNAL_DIR":"/AppleInternal","APPLE_INTERNAL_DOCUMENTATION_DIR":"/AppleInternal/Documentation","APPLE_INTERNAL_LIBRARY_DIR":"/AppleInternal/Library","APPLE_INTERNAL_TOOLS":"/AppleInternal/Developer/Tools","APPLICATION_EXTENSION_API_ONLY":"NO","APPLY_RULES_IN_COPY_FILES":"NO","APPLY_RULES_IN_COPY_HEADERS":"NO","ARCHS":"x86_64","ARCHS_STANDARD":"arm64 x86_64","ARCHS_STANDARD_32_64_BIT":"arm64 i386 x86_64","ARCHS_STANDARD_32_BIT":"i386","ARCHS_STANDARD_64_BIT":"arm64 x86_64","ARCHS_STANDARD_INCLUDING_64_BIT":"arm64 x86_64","ARCHS_UNIVERSAL_IPHONE_OS":"arm64 i386 x86_64","ASSETCATALOG_COMPILER_APPICON_NAME":"AppIcon","ASSETCATALOG_FILTER_FOR_DEVICE_MODEL":"iPod9,1","ASSETCATALOG_FILTER_FOR_DEVICE_OS_VERSION":"14.1","AVAILABLE_PLATFORMS":"appletvos appletvsimulator iphoneos iphonesimulator macosx watchos watchsimulator","AppIdentifierPrefix":"HJAN5D475T.","BITCODE_GENERATION_MODE":"marker","BUILD_ACTIVE_RESOURCES_ONLY":"YES","BUILD_COMPONENTS":"headers build","BUILD_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios","BUILD_LIBRARY_FOR_DISTRIBUTION":"NO","BUILD_ROOT":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios","BUILD_STYLE":"","BUILD_VARIANTS":"normal","BUILT_PRODUCTS_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator","BUNDLE_CONTENTS_FOLDER_PATH_deep":"Contents/","BUNDLE_EXECUTABLE_FOLDER_NAME_deep":"MacOS","BUNDLE_FORMAT":"shallow","BUNDLE_FRAMEWORKS_FOLDER_PATH":"Frameworks","BUNDLE_PLUGINS_FOLDER_PATH":"PlugIns","BUNDLE_PRIVATE_HEADERS_FOLDER_PATH":"PrivateHeaders","BUNDLE_PUBLIC_HEADERS_FOLDER_PATH":"Headers","CACHE_ROOT":"/var/folders/bk/sl_nb2512xq326xbgz2djqx80000gr/C/com.apple.DeveloperTools/12.1-12A7403/Xcode","CCHROOT":"/var/folders/bk/sl_nb2512xq326xbgz2djqx80000gr/C/com.apple.DeveloperTools/12.1-12A7403/Xcode","CHMOD":"/bin/chmod","CHOWN":"/usr/sbin/chown","CLANG_ANALYZER_NONNULL":"YES","CLANG_CXX_LANGUAGE_STANDARD":"gnu++0x","CLANG_CXX_LIBRARY":"libc++","CLANG_ENABLE_MODULES":"YES","CLANG_ENABLE_OBJC_ARC":"YES","CLANG_MODULES_BUILD_SESSION_FILE":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation","CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING":"YES","CLANG_WARN_BOOL_CONVERSION":"YES","CLANG_WARN_COMMA":"YES","CLANG_WARN_CONSTANT_CONVERSION":"YES","CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS":"YES","CLANG_WARN_DIRECT_OBJC_ISA_USAGE":"YES_ERROR","CLANG_WARN_EMPTY_BODY":"YES","CLANG_WARN_ENUM_CONVERSION":"YES","CLANG_WARN_INFINITE_RECURSION":"YES","CLANG_WARN_INT_CONVERSION":"YES","CLANG_WARN_NON_LITERAL_NULL_CONVERSION":"YES","CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF":"YES","CLANG_WARN_OBJC_LITERAL_CONVERSION":"YES","CLANG_WARN_OBJC_ROOT_CLASS":"YES_ERROR","CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER":"YES","CLANG_WARN_RANGE_LOOP_ANALYSIS":"YES","CLANG_WARN_STRICT_PROTOTYPES":"YES","CLANG_WARN_SUSPICIOUS_MOVE":"YES","CLANG_WARN_UNREACHABLE_CODE":"YES","CLANG_WARN__DUPLICATE_METHOD_MATCH":"YES","CLASS_FILE_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/JavaClasses","CLEAN_PRECOMPS":"YES","CLONE_HEADERS":"NO","CODESIGNING_FOLDER_PATH":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app","CODE_SIGNING_ALLOWED":"YES","CODE_SIGNING_REQUIRED":"YES","CODE_SIGN_CONTEXT_CLASS":"XCiPhoneSimulatorCodeSignContext","CODE_SIGN_IDENTITY":"Apple Development","CODE_SIGN_INJECT_BASE_ENTITLEMENTS":"YES","CODE_SIGN_STYLE":"Automatic","COLOR_DIAGNOSTICS":"NO","COMBINE_HIDPI_IMAGES":"NO","COMPILER_INDEX_STORE_ENABLE":"Default","COMPOSITE_SDK_DIRS":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/CompositeSDKs","COMPRESS_PNG_FILES":"YES","CONFIGURATION":"Debug","CONFIGURATION_BUILD_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator","CONFIGURATION_TEMP_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator","CONTENTS_FOLDER_PATH":"Runner.app","COPYING_PRESERVES_HFS_DATA":"NO","COPY_HEADERS_RUN_UNIFDEF":"NO","COPY_PHASE_STRIP":"NO","COPY_RESOURCES_FROM_STATIC_FRAMEWORKS":"YES","CORRESPONDING_DEVICE_PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform","CORRESPONDING_DEVICE_PLATFORM_NAME":"iphoneos","CORRESPONDING_DEVICE_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.1.sdk","CORRESPONDING_DEVICE_SDK_NAME":"iphoneos14.1","CP":"/bin/cp","CREATE_INFOPLIST_SECTION_IN_BINARY":"NO","CURRENT_ARCH":"undefined_arch","CURRENT_PROJECT_VERSION":"164","CURRENT_VARIANT":"normal","DART_DEFINES":"flutter.inspector.structuredErrors%3Dtrue","DART_OBFUSCATION":"false","DEAD_CODE_STRIPPING":"YES","DEBUGGING_SYMBOLS":"YES","DEBUG_INFORMATION_FORMAT":"dwarf","DEFAULT_COMPILER":"com.apple.compilers.llvm.clang.1_0","DEFAULT_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","DEFAULT_KEXT_INSTALL_PATH":"/System/Library/Extensions","DEFINES_MODULE":"NO","DEPLOYMENT_LOCATION":"NO","DEPLOYMENT_POSTPROCESSING":"NO","DEPLOYMENT_TARGET_CLANG_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_CLANG_FLAG_NAME":"mios-simulator-version-min","DEPLOYMENT_TARGET_CLANG_FLAG_PREFIX":"-mios-simulator-version-min=","DEPLOYMENT_TARGET_LD_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_LD_FLAG_NAME":"ios_simulator_version_min","DEPLOYMENT_TARGET_SETTING_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_SUGGESTED_VALUES":"9.0 9.1 9.2 9.3 10.0 10.1 10.2 10.3 11.0 11.1 11.2 11.3 11.4 12.0 12.1 12.2 12.3 12.4 13.0 13.1 13.2 13.3 13.4 13.5 13.6 14.0 14.1","DERIVED_FILES_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","DERIVED_FILE_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","DERIVED_SOURCES_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","DEVELOPER_FRAMEWORKS_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_FRAMEWORKS_DIR_QUOTED":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Library","DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs","DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Tools","DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","DEVELOPMENT_LANGUAGE":"en","DEVELOPMENT_TEAM":"HJAN5D475T","DOCUMENTATION_FOLDER_PATH":"Runner.app/en.lproj/Documentation","DONT_GENERATE_INFOPLIST_FILE":"NO","DO_HEADER_SCANNING_IN_JAM":"NO","DSTROOT":"/tmp/Runner.dst","DT_TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","DWARF_DSYM_FILE_NAME":"Runner.app.dSYM","DWARF_DSYM_FILE_SHOULD_ACCOMPANY_PRODUCT":"NO","DWARF_DSYM_FOLDER_PATH":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator","EFFECTIVE_PLATFORM_NAME":"-iphonesimulator","EMBEDDED_CONTENT_CONTAINS_SWIFT":"NO","EMBED_ASSET_PACKS_IN_PRODUCT_BUNDLE":"NO","ENABLE_BITCODE":"NO","ENABLE_DEFAULT_HEADER_SEARCH_PATHS":"YES","ENABLE_HARDENED_RUNTIME":"NO","ENABLE_HEADER_DEPENDENCIES":"YES","ENABLE_ON_DEMAND_RESOURCES":"YES","ENABLE_PREVIEWS":"NO","ENABLE_STRICT_OBJC_MSGSEND":"YES","ENABLE_TESTABILITY":"YES","ENABLE_TESTING_SEARCH_PATHS":"NO","ENTITLEMENTS_DESTINATION":"__entitlements","ENTITLEMENTS_REQUIRED":"YES","EXCLUDED_INSTALLSRC_SUBDIRECTORY_PATTERNS":".DS_Store .svn .git .hg CVS","EXCLUDED_RECURSIVE_SEARCH_PATH_SUBDIRECTORIES":"*.nib *.lproj *.framework *.gch *.xcode* *.xcassets (*) .DS_Store CVS .svn .git .hg *.pbproj *.pbxproj","EXECUTABLES_FOLDER_PATH":"Runner.app/Executables","EXECUTABLE_FOLDER_PATH":"Runner.app","EXECUTABLE_NAME":"Runner","EXECUTABLE_PATH":"Runner.app/Runner","EXPANDED_CODE_SIGN_IDENTITY":"-","EXPANDED_CODE_SIGN_IDENTITY_NAME":"-","FILE_LIST":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects/LinkFileList","FIXED_FILES_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/FixedFiles","FLUTTER_APPLICATION_PATH":"/Users/<USER>/Dabzshot/LikeWalletPharse3","FLUTTER_BUILD_DIR":"build","FLUTTER_BUILD_NAME":"2.0.14","FLUTTER_BUILD_NUMBER":"160","FLUTTER_FRAMEWORK_DIR":"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios","FLUTTER_ROOT":"/Users/<USER>/flutter","FLUTTER_TARGET":"/Users/<USER>/Dabzshot/LikeWalletPharse3/lib/main.dart","FRAMEWORKS_FOLDER_PATH":"Runner.app/Frameworks","FRAMEWORK_FLAG_PREFIX":"-framework","FRAMEWORK_SEARCH_PATHS":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator  /Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Flutter","FRAMEWORK_VERSION":"A","FULL_PRODUCT_NAME":"Runner.app","GCC3_VERSION":"3.3","GCC_C_LANGUAGE_STANDARD":"gnu99","GCC_DYNAMIC_NO_PIC":"NO","GCC_INLINES_ARE_PRIVATE_EXTERN":"YES","GCC_NO_COMMON_BLOCKS":"YES","GCC_OBJC_LEGACY_DISPATCH":"YES","GCC_OPTIMIZATION_LEVEL":"0","GCC_PFE_FILE_C_DIALECTS":"c objective-c c++ objective-c++","GCC_PREPROCESSOR_DEFINITIONS":"DEBUG=1 ","GCC_SYMBOLS_PRIVATE_EXTERN":"NO","GCC_TREAT_WARNINGS_AS_ERRORS":"NO","GCC_VERSION":"com.apple.compilers.llvm.clang.1_0","GCC_VERSION_IDENTIFIER":"com_apple_compilers_llvm_clang_1_0","GCC_WARN_64_TO_32_BIT_CONVERSION":"YES","GCC_WARN_ABOUT_RETURN_TYPE":"YES_ERROR","GCC_WARN_UNDECLARED_SELECTOR":"YES","GCC_WARN_UNINITIALIZED_AUTOS":"YES_AGGRESSIVE","GCC_WARN_UNUSED_FUNCTION":"YES","GCC_WARN_UNUSED_VARIABLE":"YES","GENERATED_MODULEMAP_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/GeneratedModuleMaps-iphonesimulator","GENERATE_MASTER_OBJECT_FILE":"NO","GENERATE_PKGINFO_FILE":"YES","GENERATE_PROFILING_CODE":"NO","GENERATE_TEXT_BASED_STUBS":"NO","GID":"20","GROUP":"staff","HEADERMAP_INCLUDES_FLAT_ENTRIES_FOR_TARGET_BEING_BUILT":"YES","HEADERMAP_INCLUDES_FRAMEWORK_ENTRIES_FOR_ALL_PRODUCT_TYPES":"YES","HEADERMAP_INCLUDES_NONPUBLIC_NONPRIVATE_HEADERS":"YES","HEADERMAP_INCLUDES_PROJECT_HEADERS":"YES","HEADERMAP_USES_FRAMEWORK_PREFIX_ENTRIES":"YES","HEADERMAP_USES_VFS":"NO","HEADER_SEARCH_PATHS":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/include ","HIDE_BITCODE_SYMBOLS":"YES","HOME":"/Users/<USER>","ICONV":"/usr/bin/iconv","INFOPLIST_EXPAND_BUILD_SETTINGS":"YES","INFOPLIST_FILE":"Runner/Info.plist","INFOPLIST_OUTPUT_FORMAT":"binary","INFOPLIST_PATH":"Runner.app/Info.plist","INFOPLIST_PREPROCESS":"NO","INFOSTRINGS_PATH":"Runner.app/en.lproj/InfoPlist.strings","INLINE_PRIVATE_FRAMEWORKS":"NO","INSTALLHDRS_COPY_PHASE":"NO","INSTALLHDRS_SCRIPT_PHASE":"NO","INSTALL_DIR":"/tmp/Runner.dst/Applications","INSTALL_GROUP":"staff","INSTALL_MODE_FLAG":"u+w,go-w,a+rX","INSTALL_OWNER":"oil","INSTALL_PATH":"/Applications","INSTALL_ROOT":"/tmp/Runner.dst","IPHONEOS_DEPLOYMENT_TARGET":"12.0","JAVAC_DEFAULT_FLAGS":"-J-Xms64m -J-XX:NewSize=4M -J-Dfile.encoding=UTF8","JAVA_APP_STUB":"/System/Library/Frameworks/JavaVM.framework/Resources/MacOS/JavaApplicationStub","JAVA_ARCHIVE_CLASSES":"YES","JAVA_ARCHIVE_TYPE":"JAR","JAVA_COMPILER":"/usr/bin/javac","JAVA_FOLDER_PATH":"Runner.app/Java","JAVA_FRAMEWORK_RESOURCES_DIRS":"Resources","JAVA_JAR_FLAGS":"cv","JAVA_SOURCE_SUBDIR":".","JAVA_USE_DEPENDENCIES":"YES","JAVA_ZIP_FLAGS":"-urg","JIKES_DEFAULT_FLAGS":"+E +OLDCSO","KEEP_PRIVATE_EXTERNS":"NO","LD_DEPENDENCY_INFO_FILE":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/undefined_arch/Runner_dependency_info.dat","LD_ENTITLEMENTS_SECTION":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent","LD_GENERATE_MAP_FILE":"NO","LD_MAP_FILE_PATH":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-LinkMap-normal-undefined_arch.txt","LD_NO_PIE":"NO","LD_QUOTE_LINKER_ARGUMENTS_FOR_COMPILER_DRIVER":"YES","LD_RUNPATH_SEARCH_PATHS":" @executable_path/Frameworks","LEGACY_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/PlugIns/Xcode3Core.ideplugin/Contents/SharedSupport/Developer","LEX":"lex","LIBRARY_DEXT_INSTALL_PATH":"/Library/DriverExtensions","LIBRARY_FLAG_NOSPACE":"YES","LIBRARY_FLAG_PREFIX":"-l","LIBRARY_KEXT_INSTALL_PATH":"/Library/Extensions","LIBRARY_SEARCH_PATHS":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator  /Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Flutter","LINKER_DISPLAYS_MANGLED_NAMES":"NO","LINK_FILE_LIST_normal_x86_64":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList","LINK_WITH_STANDARD_LIBRARIES":"YES","LLVM_TARGET_TRIPLE_OS_VERSION":"ios12.0","LLVM_TARGET_TRIPLE_SUFFIX":"-simulator","LLVM_TARGET_TRIPLE_VENDOR":"apple","LOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app/en.lproj","LOCALIZED_STRING_MACRO_NAMES":"NSLocalizedString CFCopyLocalizedString","LOCALIZED_STRING_SWIFTUI_SUPPORT":"YES","LOCAL_ADMIN_APPS_DIR":"/Applications/Utilities","LOCAL_APPS_DIR":"/Applications","LOCAL_DEVELOPER_DIR":"/Library/Developer","LOCAL_LIBRARY_DIR":"/Library","LOCROOT":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","LOCSYMROOT":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","MACH_O_TYPE":"mh_execute","MAC_OS_X_PRODUCT_BUILD_VERSION":"20B29","MAC_OS_X_VERSION_ACTUAL":"110001","MAC_OS_X_VERSION_MAJOR":"110000","MAC_OS_X_VERSION_MINOR":"0001","MARKETING_VERSION":"2.0.25","METAL_LIBRARY_FILE_BASE":"default","METAL_LIBRARY_OUTPUT_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app","MODULES_FOLDER_PATH":"Runner.app/Modules","MODULE_CACHE_DIR":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex","MTL_ENABLE_DEBUG_INFO":"YES","NATIVE_ARCH":"x86_64","NATIVE_ARCH_32_BIT":"i386","NATIVE_ARCH_64_BIT":"x86_64","NATIVE_ARCH_ACTUAL":"x86_64","NO_COMMON":"YES","OBJC_ABI_VERSION":"2","OBJECT_FILE_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects","OBJECT_FILE_DIR_normal":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal","OBJROOT":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios","ONLY_ACTIVE_ARCH":"YES","OS":"MACOS","OSAC":"/usr/bin/osacompile","OTHER_LDFLAGS":" -framework Flutter","PACKAGE_CONFIG":".packages","PACKAGE_TYPE":"com.apple.package-type.wrapper.application","PASCAL_STRINGS":"YES","PATH":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/libexec:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/local/bin:/Applications/Xcode.app/Contents/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/usr/local/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin","PATH_PREFIXES_EXCLUDED_FROM_HEADER_DEPENDENCIES":"/usr/include /usr/local/include /System/Library/Frameworks /System/Library/PrivateFrameworks /Applications/Xcode.app/Contents/Developer/Headers /Applications/Xcode.app/Contents/Developer/SDKs /Applications/Xcode.app/Contents/Developer/Platforms","PBDEVELOPMENTPLIST_PATH":"Runner.app/pbdevelopment.plist","PER_ARCH_OBJECT_FILE_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/undefined_arch","PER_VARIANT_OBJECT_FILE_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal","PKGINFO_FILE_PATH":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/PkgInfo","PKGINFO_PATH":"Runner.app/PkgInfo","PLATFORM_DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Applications","PLATFORM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/bin","PLATFORM_DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Library","PLATFORM_DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs","PLATFORM_DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Tools","PLATFORM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr","PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform","PLATFORM_DISPLAY_NAME":"iOS Simulator","PLATFORM_FAMILY_NAME":"iOS","PLATFORM_NAME":"iphonesimulator","PLATFORM_PREFERRED_ARCH":"x86_64","PLATFORM_PRODUCT_BUILD_VERSION":"18A8394","PLIST_FILE_OUTPUT_FORMAT":"binary","PLUGINS_FOLDER_PATH":"Runner.app/PlugIns","PRECOMPS_INCLUDE_HEADERS_FROM_BUILT_PRODUCTS_DIR":"YES","PRECOMP_DESTINATION_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/PrefixHeaders","PRESERVE_DEAD_CODE_INITS_AND_TERMS":"NO","PRIVATE_HEADERS_FOLDER_PATH":"Runner.app/PrivateHeaders","PRODUCT_BUNDLE_IDENTIFIER":"likewallet.likewallet","PRODUCT_BUNDLE_PACKAGE_TYPE":"APPL","PRODUCT_MODULE_NAME":"Runner","PRODUCT_NAME":"Runner","PRODUCT_SETTINGS_PATH":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Runner/Info.plist","PRODUCT_TYPE":"com.apple.product-type.application","PROFILING_CODE":"NO","PROJECT":"Runner","PROJECT_DERIVED_FILE_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/DerivedSources","PROJECT_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","PROJECT_FILE_PATH":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Runner.xcodeproj","PROJECT_NAME":"Runner","PROJECT_TEMP_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build","PROJECT_TEMP_ROOT":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios","PUBLIC_HEADERS_FOLDER_PATH":"Runner.app/Headers","RECURSIVE_SEARCH_PATHS_FOLLOW_SYMLINKS":"YES","REMOVE_CVS_FROM_RESOURCES":"YES","REMOVE_GIT_FROM_RESOURCES":"YES","REMOVE_HEADERS_FROM_EMBEDDED_BUNDLES":"YES","REMOVE_HG_FROM_RESOURCES":"YES","REMOVE_SVN_FROM_RESOURCES":"YES","REZ_COLLECTOR_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/ResourceManagerResources","REZ_OBJECTS_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/ResourceManagerResources/Objects","REZ_SEARCH_PATHS":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator ","SCAN_ALL_SOURCE_FILES_FOR_INCLUDES":"NO","SCRIPTS_FOLDER_PATH":"Runner.app/Scripts","SCRIPT_INPUT_FILE_COUNT":"0","SCRIPT_INPUT_FILE_LIST_COUNT":"0","SCRIPT_OUTPUT_FILE_COUNT":"0","SCRIPT_OUTPUT_FILE_LIST_COUNT":"0","SDKROOT":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.1.sdk","SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.1.sdk","SDK_DIR_iphonesimulator14_1":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.1.sdk","SDK_NAME":"iphonesimulator14.1","SDK_NAMES":"iphonesimulator14.1","SDK_PRODUCT_BUILD_VERSION":"18A8394","SDK_VERSION":"14.1","SDK_VERSION_ACTUAL":"140100","SDK_VERSION_MAJOR":"140000","SDK_VERSION_MINOR":"100","SED":"/usr/bin/sed","SEPARATE_STRIP":"NO","SEPARATE_SYMBOL_EDIT":"NO","SET_DIR_MODE_OWNER_GROUP":"YES","SET_FILE_MODE_OWNER_GROUP":"NO","SHALLOW_BUNDLE":"YES","SHARED_DERIVED_FILE_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/DerivedSources","SHARED_FRAMEWORKS_FOLDER_PATH":"Runner.app/SharedFrameworks","SHARED_PRECOMPS_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/SharedPrecompiledHeaders","SHARED_SUPPORT_FOLDER_PATH":"Runner.app/SharedSupport","SKIP_INSTALL":"NO","SOURCE_ROOT":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","SRCROOT":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","STRINGS_FILE_OUTPUT_ENCODING":"binary","STRIP_BITCODE_FROM_COPIED_FILES":"NO","STRIP_INSTALLED_PRODUCT":"YES","STRIP_STYLE":"all","STRIP_SWIFT_SYMBOLS":"YES","SUPPORTED_DEVICE_FAMILIES":"1,2","SUPPORTED_PLATFORMS":"iphoneos iphonesimulator","SUPPORTS_TEXT_BASED_API":"NO","SWIFT_OBJC_BRIDGING_HEADER":"Runner/Runner-Bridging-Header.h","SWIFT_OPTIMIZATION_LEVEL":"-Onone","SWIFT_PLATFORM_TARGET_PREFIX":"ios","SWIFT_RESPONSE_FILE_PATH_normal_x86_64":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList","SWIFT_VERSION":"5.0","SYMROOT":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios","SYSTEM_ADMIN_APPS_DIR":"/Applications/Utilities","SYSTEM_APPS_DIR":"/Applications","SYSTEM_CORE_SERVICES_DIR":"/System/Library/CoreServices","SYSTEM_DEMOS_DIR":"/Applications/Extras","SYSTEM_DEVELOPER_APPS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","SYSTEM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","SYSTEM_DEVELOPER_DEMOS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities/Built Examples","SYSTEM_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","SYSTEM_DEVELOPER_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library","SYSTEM_DEVELOPER_GRAPHICS_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Graphics Tools","SYSTEM_DEVELOPER_JAVA_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Java Tools","SYSTEM_DEVELOPER_PERFORMANCE_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Performance Tools","SYSTEM_DEVELOPER_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes","SYSTEM_DEVELOPER_TOOLS":"/Applications/Xcode.app/Contents/Developer/Tools","SYSTEM_DEVELOPER_TOOLS_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/documentation/DeveloperTools","SYSTEM_DEVELOPER_TOOLS_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes/DeveloperTools","SYSTEM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","SYSTEM_DEVELOPER_UTILITIES_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities","SYSTEM_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","SYSTEM_DOCUMENTATION_DIR":"/Library/Documentation","SYSTEM_KEXT_INSTALL_PATH":"/System/Library/Extensions","SYSTEM_LIBRARY_DIR":"/System/Library","TAPI_VERIFY_MODE":"ErrorsOnly","TARGETED_DEVICE_FAMILY":"1,2","TARGETNAME":"Runner","TARGET_BUILD_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator","TARGET_DEVICE_IDENTIFIER":"41B25245-6594-41DE-9511-845A598DA691","TARGET_DEVICE_MODEL":"iPod9,1","TARGET_DEVICE_OS_VERSION":"14.1","TARGET_NAME":"Runner","TARGET_TEMP_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_FILES_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_FILE_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_ROOT":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios","TEST_FRAMEWORK_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Library/Frameworks /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.1.sdk/Developer/Library/Frameworks","TEST_LIBRARY_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/lib","TOOLCHAINS":"com.apple.dt.toolchain.XcodeDefault","TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","TRACK_WIDGET_CREATION":"true","TREAT_MISSING_BASELINES_AS_TEST_FAILURES":"NO","TREE_SHAKE_ICONS":"false","TeamIdentifierPrefix":"HJAN5D475T.","UID":"504","UNLOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app","UNSTRIPPED_PRODUCT":"NO","USER":"oil","USER_APPS_DIR":"/Users/<USER>/Applications","USER_LIBRARY_DIR":"/Users/<USER>/Library","USE_DYNAMIC_NO_PIC":"YES","USE_HEADERMAP":"YES","USE_HEADER_SYMLINKS":"NO","USE_LLVM_TARGET_TRIPLES":"YES","USE_LLVM_TARGET_TRIPLES_FOR_CLANG":"YES","USE_LLVM_TARGET_TRIPLES_FOR_LD":"YES","USE_LLVM_TARGET_TRIPLES_FOR_TAPI":"YES","VALIDATE_DEVELOPMENT_ASSET_PATHS":"YES_ERROR","VALIDATE_PRODUCT":"NO","VALIDATE_WORKSPACE":"YES_ERROR","VALID_ARCHS":"arm64 arm64e i386 x86_64","VERBOSE_PBXCP":"NO","VERSIONING_SYSTEM":"apple-generic","VERSIONPLIST_PATH":"Runner.app/version.plist","VERSION_INFO_BUILDER":"oil","VERSION_INFO_FILE":"Runner_vers.c","VERSION_INFO_STRING":"\"@(#)PROGRAM:Runner  PROJECT:Runner-164\"","WRAPPER_EXTENSION":"app","WRAPPER_NAME":"Runner.app","WRAPPER_SUFFIX":".app","WRAP_ASSET_PACKS_IN_SEPARATE_DIRECTORIES":"NO","XCODE_APP_SUPPORT_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Xcode","XCODE_PRODUCT_BUILD_VERSION":"12A7403","XCODE_VERSION_ACTUAL":"1210","XCODE_VERSION_MAJOR":"1200","XCODE_VERSION_MINOR":"1210","XPCSERVICES_FOLDER_PATH":"Runner.app/XPCServices","YACC":"yacc","arch":"undefined_arch","variant":"normal"},"allow-missing-inputs":true,"always-out-of-date":true,"working-directory":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","control-enabled":false,"signature":"0150e7a8eb3e312199b6c025f7acbffa"}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:PhaseScriptExecution Thin Binary /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh": {"tool":"shell","description":"PhaseScriptExecution Thin Binary /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh","inputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase5-copy-files>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["<execute-shell-script-b97f721a235acf137390128f1b58ef8cf1eee2015e8ff5ebcd27678f788c2826>"],"args":["/bin/sh","-c","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh"],"env":{"ACTION":"build","AD_HOC_CODE_SIGNING_ALLOWED":"YES","ALTERNATE_GROUP":"staff","ALTERNATE_MODE":"u+w,go-w,a+rX","ALTERNATE_OWNER":"oil","ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES":"NO","ALWAYS_SEARCH_USER_PATHS":"NO","ALWAYS_USE_SEPARATE_HEADERMAPS":"NO","APPLE_INTERNAL_DEVELOPER_DIR":"/AppleInternal/Developer","APPLE_INTERNAL_DIR":"/AppleInternal","APPLE_INTERNAL_DOCUMENTATION_DIR":"/AppleInternal/Documentation","APPLE_INTERNAL_LIBRARY_DIR":"/AppleInternal/Library","APPLE_INTERNAL_TOOLS":"/AppleInternal/Developer/Tools","APPLICATION_EXTENSION_API_ONLY":"NO","APPLY_RULES_IN_COPY_FILES":"NO","APPLY_RULES_IN_COPY_HEADERS":"NO","ARCHS":"x86_64","ARCHS_STANDARD":"arm64 x86_64","ARCHS_STANDARD_32_64_BIT":"arm64 i386 x86_64","ARCHS_STANDARD_32_BIT":"i386","ARCHS_STANDARD_64_BIT":"arm64 x86_64","ARCHS_STANDARD_INCLUDING_64_BIT":"arm64 x86_64","ARCHS_UNIVERSAL_IPHONE_OS":"arm64 i386 x86_64","ASSETCATALOG_COMPILER_APPICON_NAME":"AppIcon","ASSETCATALOG_FILTER_FOR_DEVICE_MODEL":"iPod9,1","ASSETCATALOG_FILTER_FOR_DEVICE_OS_VERSION":"14.1","AVAILABLE_PLATFORMS":"appletvos appletvsimulator iphoneos iphonesimulator macosx watchos watchsimulator","AppIdentifierPrefix":"HJAN5D475T.","BITCODE_GENERATION_MODE":"marker","BUILD_ACTIVE_RESOURCES_ONLY":"YES","BUILD_COMPONENTS":"headers build","BUILD_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios","BUILD_LIBRARY_FOR_DISTRIBUTION":"NO","BUILD_ROOT":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios","BUILD_STYLE":"","BUILD_VARIANTS":"normal","BUILT_PRODUCTS_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator","BUNDLE_CONTENTS_FOLDER_PATH_deep":"Contents/","BUNDLE_EXECUTABLE_FOLDER_NAME_deep":"MacOS","BUNDLE_FORMAT":"shallow","BUNDLE_FRAMEWORKS_FOLDER_PATH":"Frameworks","BUNDLE_PLUGINS_FOLDER_PATH":"PlugIns","BUNDLE_PRIVATE_HEADERS_FOLDER_PATH":"PrivateHeaders","BUNDLE_PUBLIC_HEADERS_FOLDER_PATH":"Headers","CACHE_ROOT":"/var/folders/bk/sl_nb2512xq326xbgz2djqx80000gr/C/com.apple.DeveloperTools/12.1-12A7403/Xcode","CCHROOT":"/var/folders/bk/sl_nb2512xq326xbgz2djqx80000gr/C/com.apple.DeveloperTools/12.1-12A7403/Xcode","CHMOD":"/bin/chmod","CHOWN":"/usr/sbin/chown","CLANG_ANALYZER_NONNULL":"YES","CLANG_CXX_LANGUAGE_STANDARD":"gnu++0x","CLANG_CXX_LIBRARY":"libc++","CLANG_ENABLE_MODULES":"YES","CLANG_ENABLE_OBJC_ARC":"YES","CLANG_MODULES_BUILD_SESSION_FILE":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation","CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING":"YES","CLANG_WARN_BOOL_CONVERSION":"YES","CLANG_WARN_COMMA":"YES","CLANG_WARN_CONSTANT_CONVERSION":"YES","CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS":"YES","CLANG_WARN_DIRECT_OBJC_ISA_USAGE":"YES_ERROR","CLANG_WARN_EMPTY_BODY":"YES","CLANG_WARN_ENUM_CONVERSION":"YES","CLANG_WARN_INFINITE_RECURSION":"YES","CLANG_WARN_INT_CONVERSION":"YES","CLANG_WARN_NON_LITERAL_NULL_CONVERSION":"YES","CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF":"YES","CLANG_WARN_OBJC_LITERAL_CONVERSION":"YES","CLANG_WARN_OBJC_ROOT_CLASS":"YES_ERROR","CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER":"YES","CLANG_WARN_RANGE_LOOP_ANALYSIS":"YES","CLANG_WARN_STRICT_PROTOTYPES":"YES","CLANG_WARN_SUSPICIOUS_MOVE":"YES","CLANG_WARN_UNREACHABLE_CODE":"YES","CLANG_WARN__DUPLICATE_METHOD_MATCH":"YES","CLASS_FILE_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/JavaClasses","CLEAN_PRECOMPS":"YES","CLONE_HEADERS":"NO","CODESIGNING_FOLDER_PATH":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app","CODE_SIGNING_ALLOWED":"YES","CODE_SIGNING_REQUIRED":"YES","CODE_SIGN_CONTEXT_CLASS":"XCiPhoneSimulatorCodeSignContext","CODE_SIGN_IDENTITY":"Apple Development","CODE_SIGN_INJECT_BASE_ENTITLEMENTS":"YES","CODE_SIGN_STYLE":"Automatic","COLOR_DIAGNOSTICS":"NO","COMBINE_HIDPI_IMAGES":"NO","COMPILER_INDEX_STORE_ENABLE":"Default","COMPOSITE_SDK_DIRS":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/CompositeSDKs","COMPRESS_PNG_FILES":"YES","CONFIGURATION":"Debug","CONFIGURATION_BUILD_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator","CONFIGURATION_TEMP_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator","CONTENTS_FOLDER_PATH":"Runner.app","COPYING_PRESERVES_HFS_DATA":"NO","COPY_HEADERS_RUN_UNIFDEF":"NO","COPY_PHASE_STRIP":"NO","COPY_RESOURCES_FROM_STATIC_FRAMEWORKS":"YES","CORRESPONDING_DEVICE_PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform","CORRESPONDING_DEVICE_PLATFORM_NAME":"iphoneos","CORRESPONDING_DEVICE_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.1.sdk","CORRESPONDING_DEVICE_SDK_NAME":"iphoneos14.1","CP":"/bin/cp","CREATE_INFOPLIST_SECTION_IN_BINARY":"NO","CURRENT_ARCH":"undefined_arch","CURRENT_PROJECT_VERSION":"164","CURRENT_VARIANT":"normal","DART_DEFINES":"flutter.inspector.structuredErrors%3Dtrue","DART_OBFUSCATION":"false","DEAD_CODE_STRIPPING":"YES","DEBUGGING_SYMBOLS":"YES","DEBUG_INFORMATION_FORMAT":"dwarf","DEFAULT_COMPILER":"com.apple.compilers.llvm.clang.1_0","DEFAULT_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","DEFAULT_KEXT_INSTALL_PATH":"/System/Library/Extensions","DEFINES_MODULE":"NO","DEPLOYMENT_LOCATION":"NO","DEPLOYMENT_POSTPROCESSING":"NO","DEPLOYMENT_TARGET_CLANG_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_CLANG_FLAG_NAME":"mios-simulator-version-min","DEPLOYMENT_TARGET_CLANG_FLAG_PREFIX":"-mios-simulator-version-min=","DEPLOYMENT_TARGET_LD_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_LD_FLAG_NAME":"ios_simulator_version_min","DEPLOYMENT_TARGET_SETTING_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_SUGGESTED_VALUES":"9.0 9.1 9.2 9.3 10.0 10.1 10.2 10.3 11.0 11.1 11.2 11.3 11.4 12.0 12.1 12.2 12.3 12.4 13.0 13.1 13.2 13.3 13.4 13.5 13.6 14.0 14.1","DERIVED_FILES_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","DERIVED_FILE_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","DERIVED_SOURCES_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","DEVELOPER_FRAMEWORKS_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_FRAMEWORKS_DIR_QUOTED":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Library","DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs","DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Tools","DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","DEVELOPMENT_LANGUAGE":"en","DEVELOPMENT_TEAM":"HJAN5D475T","DOCUMENTATION_FOLDER_PATH":"Runner.app/en.lproj/Documentation","DONT_GENERATE_INFOPLIST_FILE":"NO","DO_HEADER_SCANNING_IN_JAM":"NO","DSTROOT":"/tmp/Runner.dst","DT_TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","DWARF_DSYM_FILE_NAME":"Runner.app.dSYM","DWARF_DSYM_FILE_SHOULD_ACCOMPANY_PRODUCT":"NO","DWARF_DSYM_FOLDER_PATH":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator","EFFECTIVE_PLATFORM_NAME":"-iphonesimulator","EMBEDDED_CONTENT_CONTAINS_SWIFT":"NO","EMBED_ASSET_PACKS_IN_PRODUCT_BUNDLE":"NO","ENABLE_BITCODE":"NO","ENABLE_DEFAULT_HEADER_SEARCH_PATHS":"YES","ENABLE_HARDENED_RUNTIME":"NO","ENABLE_HEADER_DEPENDENCIES":"YES","ENABLE_ON_DEMAND_RESOURCES":"YES","ENABLE_PREVIEWS":"NO","ENABLE_STRICT_OBJC_MSGSEND":"YES","ENABLE_TESTABILITY":"YES","ENABLE_TESTING_SEARCH_PATHS":"NO","ENTITLEMENTS_DESTINATION":"__entitlements","ENTITLEMENTS_REQUIRED":"YES","EXCLUDED_INSTALLSRC_SUBDIRECTORY_PATTERNS":".DS_Store .svn .git .hg CVS","EXCLUDED_RECURSIVE_SEARCH_PATH_SUBDIRECTORIES":"*.nib *.lproj *.framework *.gch *.xcode* *.xcassets (*) .DS_Store CVS .svn .git .hg *.pbproj *.pbxproj","EXECUTABLES_FOLDER_PATH":"Runner.app/Executables","EXECUTABLE_FOLDER_PATH":"Runner.app","EXECUTABLE_NAME":"Runner","EXECUTABLE_PATH":"Runner.app/Runner","EXPANDED_CODE_SIGN_IDENTITY":"-","EXPANDED_CODE_SIGN_IDENTITY_NAME":"-","FILE_LIST":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects/LinkFileList","FIXED_FILES_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/FixedFiles","FLUTTER_APPLICATION_PATH":"/Users/<USER>/Dabzshot/LikeWalletPharse3","FLUTTER_BUILD_DIR":"build","FLUTTER_BUILD_NAME":"2.0.14","FLUTTER_BUILD_NUMBER":"160","FLUTTER_FRAMEWORK_DIR":"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios","FLUTTER_ROOT":"/Users/<USER>/flutter","FLUTTER_TARGET":"/Users/<USER>/Dabzshot/LikeWalletPharse3/lib/main.dart","FRAMEWORKS_FOLDER_PATH":"Runner.app/Frameworks","FRAMEWORK_FLAG_PREFIX":"-framework","FRAMEWORK_SEARCH_PATHS":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator  /Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Flutter","FRAMEWORK_VERSION":"A","FULL_PRODUCT_NAME":"Runner.app","GCC3_VERSION":"3.3","GCC_C_LANGUAGE_STANDARD":"gnu99","GCC_DYNAMIC_NO_PIC":"NO","GCC_INLINES_ARE_PRIVATE_EXTERN":"YES","GCC_NO_COMMON_BLOCKS":"YES","GCC_OBJC_LEGACY_DISPATCH":"YES","GCC_OPTIMIZATION_LEVEL":"0","GCC_PFE_FILE_C_DIALECTS":"c objective-c c++ objective-c++","GCC_PREPROCESSOR_DEFINITIONS":"DEBUG=1 ","GCC_SYMBOLS_PRIVATE_EXTERN":"NO","GCC_TREAT_WARNINGS_AS_ERRORS":"NO","GCC_VERSION":"com.apple.compilers.llvm.clang.1_0","GCC_VERSION_IDENTIFIER":"com_apple_compilers_llvm_clang_1_0","GCC_WARN_64_TO_32_BIT_CONVERSION":"YES","GCC_WARN_ABOUT_RETURN_TYPE":"YES_ERROR","GCC_WARN_UNDECLARED_SELECTOR":"YES","GCC_WARN_UNINITIALIZED_AUTOS":"YES_AGGRESSIVE","GCC_WARN_UNUSED_FUNCTION":"YES","GCC_WARN_UNUSED_VARIABLE":"YES","GENERATED_MODULEMAP_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/GeneratedModuleMaps-iphonesimulator","GENERATE_MASTER_OBJECT_FILE":"NO","GENERATE_PKGINFO_FILE":"YES","GENERATE_PROFILING_CODE":"NO","GENERATE_TEXT_BASED_STUBS":"NO","GID":"20","GROUP":"staff","HEADERMAP_INCLUDES_FLAT_ENTRIES_FOR_TARGET_BEING_BUILT":"YES","HEADERMAP_INCLUDES_FRAMEWORK_ENTRIES_FOR_ALL_PRODUCT_TYPES":"YES","HEADERMAP_INCLUDES_NONPUBLIC_NONPRIVATE_HEADERS":"YES","HEADERMAP_INCLUDES_PROJECT_HEADERS":"YES","HEADERMAP_USES_FRAMEWORK_PREFIX_ENTRIES":"YES","HEADERMAP_USES_VFS":"NO","HEADER_SEARCH_PATHS":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/include ","HIDE_BITCODE_SYMBOLS":"YES","HOME":"/Users/<USER>","ICONV":"/usr/bin/iconv","INFOPLIST_EXPAND_BUILD_SETTINGS":"YES","INFOPLIST_FILE":"Runner/Info.plist","INFOPLIST_OUTPUT_FORMAT":"binary","INFOPLIST_PATH":"Runner.app/Info.plist","INFOPLIST_PREPROCESS":"NO","INFOSTRINGS_PATH":"Runner.app/en.lproj/InfoPlist.strings","INLINE_PRIVATE_FRAMEWORKS":"NO","INSTALLHDRS_COPY_PHASE":"NO","INSTALLHDRS_SCRIPT_PHASE":"NO","INSTALL_DIR":"/tmp/Runner.dst/Applications","INSTALL_GROUP":"staff","INSTALL_MODE_FLAG":"u+w,go-w,a+rX","INSTALL_OWNER":"oil","INSTALL_PATH":"/Applications","INSTALL_ROOT":"/tmp/Runner.dst","IPHONEOS_DEPLOYMENT_TARGET":"12.0","JAVAC_DEFAULT_FLAGS":"-J-Xms64m -J-XX:NewSize=4M -J-Dfile.encoding=UTF8","JAVA_APP_STUB":"/System/Library/Frameworks/JavaVM.framework/Resources/MacOS/JavaApplicationStub","JAVA_ARCHIVE_CLASSES":"YES","JAVA_ARCHIVE_TYPE":"JAR","JAVA_COMPILER":"/usr/bin/javac","JAVA_FOLDER_PATH":"Runner.app/Java","JAVA_FRAMEWORK_RESOURCES_DIRS":"Resources","JAVA_JAR_FLAGS":"cv","JAVA_SOURCE_SUBDIR":".","JAVA_USE_DEPENDENCIES":"YES","JAVA_ZIP_FLAGS":"-urg","JIKES_DEFAULT_FLAGS":"+E +OLDCSO","KEEP_PRIVATE_EXTERNS":"NO","LD_DEPENDENCY_INFO_FILE":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/undefined_arch/Runner_dependency_info.dat","LD_ENTITLEMENTS_SECTION":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent","LD_GENERATE_MAP_FILE":"NO","LD_MAP_FILE_PATH":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-LinkMap-normal-undefined_arch.txt","LD_NO_PIE":"NO","LD_QUOTE_LINKER_ARGUMENTS_FOR_COMPILER_DRIVER":"YES","LD_RUNPATH_SEARCH_PATHS":" @executable_path/Frameworks","LEGACY_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/PlugIns/Xcode3Core.ideplugin/Contents/SharedSupport/Developer","LEX":"lex","LIBRARY_DEXT_INSTALL_PATH":"/Library/DriverExtensions","LIBRARY_FLAG_NOSPACE":"YES","LIBRARY_FLAG_PREFIX":"-l","LIBRARY_KEXT_INSTALL_PATH":"/Library/Extensions","LIBRARY_SEARCH_PATHS":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator  /Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Flutter","LINKER_DISPLAYS_MANGLED_NAMES":"NO","LINK_FILE_LIST_normal_x86_64":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList","LINK_WITH_STANDARD_LIBRARIES":"YES","LLVM_TARGET_TRIPLE_OS_VERSION":"ios12.0","LLVM_TARGET_TRIPLE_SUFFIX":"-simulator","LLVM_TARGET_TRIPLE_VENDOR":"apple","LOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app/en.lproj","LOCALIZED_STRING_MACRO_NAMES":"NSLocalizedString CFCopyLocalizedString","LOCALIZED_STRING_SWIFTUI_SUPPORT":"YES","LOCAL_ADMIN_APPS_DIR":"/Applications/Utilities","LOCAL_APPS_DIR":"/Applications","LOCAL_DEVELOPER_DIR":"/Library/Developer","LOCAL_LIBRARY_DIR":"/Library","LOCROOT":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","LOCSYMROOT":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","MACH_O_TYPE":"mh_execute","MAC_OS_X_PRODUCT_BUILD_VERSION":"20B29","MAC_OS_X_VERSION_ACTUAL":"110001","MAC_OS_X_VERSION_MAJOR":"110000","MAC_OS_X_VERSION_MINOR":"0001","MARKETING_VERSION":"2.0.25","METAL_LIBRARY_FILE_BASE":"default","METAL_LIBRARY_OUTPUT_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app","MODULES_FOLDER_PATH":"Runner.app/Modules","MODULE_CACHE_DIR":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex","MTL_ENABLE_DEBUG_INFO":"YES","NATIVE_ARCH":"x86_64","NATIVE_ARCH_32_BIT":"i386","NATIVE_ARCH_64_BIT":"x86_64","NATIVE_ARCH_ACTUAL":"x86_64","NO_COMMON":"YES","OBJC_ABI_VERSION":"2","OBJECT_FILE_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects","OBJECT_FILE_DIR_normal":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal","OBJROOT":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios","ONLY_ACTIVE_ARCH":"YES","OS":"MACOS","OSAC":"/usr/bin/osacompile","OTHER_LDFLAGS":" -framework Flutter","PACKAGE_CONFIG":".packages","PACKAGE_TYPE":"com.apple.package-type.wrapper.application","PASCAL_STRINGS":"YES","PATH":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/libexec:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/local/bin:/Applications/Xcode.app/Contents/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/usr/local/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin","PATH_PREFIXES_EXCLUDED_FROM_HEADER_DEPENDENCIES":"/usr/include /usr/local/include /System/Library/Frameworks /System/Library/PrivateFrameworks /Applications/Xcode.app/Contents/Developer/Headers /Applications/Xcode.app/Contents/Developer/SDKs /Applications/Xcode.app/Contents/Developer/Platforms","PBDEVELOPMENTPLIST_PATH":"Runner.app/pbdevelopment.plist","PER_ARCH_OBJECT_FILE_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/undefined_arch","PER_VARIANT_OBJECT_FILE_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal","PKGINFO_FILE_PATH":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/PkgInfo","PKGINFO_PATH":"Runner.app/PkgInfo","PLATFORM_DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Applications","PLATFORM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/bin","PLATFORM_DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Library","PLATFORM_DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs","PLATFORM_DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Tools","PLATFORM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr","PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform","PLATFORM_DISPLAY_NAME":"iOS Simulator","PLATFORM_FAMILY_NAME":"iOS","PLATFORM_NAME":"iphonesimulator","PLATFORM_PREFERRED_ARCH":"x86_64","PLATFORM_PRODUCT_BUILD_VERSION":"18A8394","PLIST_FILE_OUTPUT_FORMAT":"binary","PLUGINS_FOLDER_PATH":"Runner.app/PlugIns","PRECOMPS_INCLUDE_HEADERS_FROM_BUILT_PRODUCTS_DIR":"YES","PRECOMP_DESTINATION_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/PrefixHeaders","PRESERVE_DEAD_CODE_INITS_AND_TERMS":"NO","PRIVATE_HEADERS_FOLDER_PATH":"Runner.app/PrivateHeaders","PRODUCT_BUNDLE_IDENTIFIER":"likewallet.likewallet","PRODUCT_BUNDLE_PACKAGE_TYPE":"APPL","PRODUCT_MODULE_NAME":"Runner","PRODUCT_NAME":"Runner","PRODUCT_SETTINGS_PATH":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Runner/Info.plist","PRODUCT_TYPE":"com.apple.product-type.application","PROFILING_CODE":"NO","PROJECT":"Runner","PROJECT_DERIVED_FILE_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/DerivedSources","PROJECT_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","PROJECT_FILE_PATH":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Runner.xcodeproj","PROJECT_NAME":"Runner","PROJECT_TEMP_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build","PROJECT_TEMP_ROOT":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios","PUBLIC_HEADERS_FOLDER_PATH":"Runner.app/Headers","RECURSIVE_SEARCH_PATHS_FOLLOW_SYMLINKS":"YES","REMOVE_CVS_FROM_RESOURCES":"YES","REMOVE_GIT_FROM_RESOURCES":"YES","REMOVE_HEADERS_FROM_EMBEDDED_BUNDLES":"YES","REMOVE_HG_FROM_RESOURCES":"YES","REMOVE_SVN_FROM_RESOURCES":"YES","REZ_COLLECTOR_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/ResourceManagerResources","REZ_OBJECTS_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/ResourceManagerResources/Objects","REZ_SEARCH_PATHS":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator ","SCAN_ALL_SOURCE_FILES_FOR_INCLUDES":"NO","SCRIPTS_FOLDER_PATH":"Runner.app/Scripts","SCRIPT_INPUT_FILE_COUNT":"0","SCRIPT_INPUT_FILE_LIST_COUNT":"0","SCRIPT_OUTPUT_FILE_COUNT":"0","SCRIPT_OUTPUT_FILE_LIST_COUNT":"0","SDKROOT":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.1.sdk","SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.1.sdk","SDK_DIR_iphonesimulator14_1":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.1.sdk","SDK_NAME":"iphonesimulator14.1","SDK_NAMES":"iphonesimulator14.1","SDK_PRODUCT_BUILD_VERSION":"18A8394","SDK_VERSION":"14.1","SDK_VERSION_ACTUAL":"140100","SDK_VERSION_MAJOR":"140000","SDK_VERSION_MINOR":"100","SED":"/usr/bin/sed","SEPARATE_STRIP":"NO","SEPARATE_SYMBOL_EDIT":"NO","SET_DIR_MODE_OWNER_GROUP":"YES","SET_FILE_MODE_OWNER_GROUP":"NO","SHALLOW_BUNDLE":"YES","SHARED_DERIVED_FILE_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/DerivedSources","SHARED_FRAMEWORKS_FOLDER_PATH":"Runner.app/SharedFrameworks","SHARED_PRECOMPS_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/SharedPrecompiledHeaders","SHARED_SUPPORT_FOLDER_PATH":"Runner.app/SharedSupport","SKIP_INSTALL":"NO","SOURCE_ROOT":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","SRCROOT":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","STRINGS_FILE_OUTPUT_ENCODING":"binary","STRIP_BITCODE_FROM_COPIED_FILES":"NO","STRIP_INSTALLED_PRODUCT":"YES","STRIP_STYLE":"all","STRIP_SWIFT_SYMBOLS":"YES","SUPPORTED_DEVICE_FAMILIES":"1,2","SUPPORTED_PLATFORMS":"iphoneos iphonesimulator","SUPPORTS_TEXT_BASED_API":"NO","SWIFT_OBJC_BRIDGING_HEADER":"Runner/Runner-Bridging-Header.h","SWIFT_OPTIMIZATION_LEVEL":"-Onone","SWIFT_PLATFORM_TARGET_PREFIX":"ios","SWIFT_RESPONSE_FILE_PATH_normal_x86_64":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList","SWIFT_VERSION":"5.0","SYMROOT":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios","SYSTEM_ADMIN_APPS_DIR":"/Applications/Utilities","SYSTEM_APPS_DIR":"/Applications","SYSTEM_CORE_SERVICES_DIR":"/System/Library/CoreServices","SYSTEM_DEMOS_DIR":"/Applications/Extras","SYSTEM_DEVELOPER_APPS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","SYSTEM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","SYSTEM_DEVELOPER_DEMOS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities/Built Examples","SYSTEM_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","SYSTEM_DEVELOPER_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library","SYSTEM_DEVELOPER_GRAPHICS_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Graphics Tools","SYSTEM_DEVELOPER_JAVA_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Java Tools","SYSTEM_DEVELOPER_PERFORMANCE_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Performance Tools","SYSTEM_DEVELOPER_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes","SYSTEM_DEVELOPER_TOOLS":"/Applications/Xcode.app/Contents/Developer/Tools","SYSTEM_DEVELOPER_TOOLS_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/documentation/DeveloperTools","SYSTEM_DEVELOPER_TOOLS_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes/DeveloperTools","SYSTEM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","SYSTEM_DEVELOPER_UTILITIES_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities","SYSTEM_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","SYSTEM_DOCUMENTATION_DIR":"/Library/Documentation","SYSTEM_KEXT_INSTALL_PATH":"/System/Library/Extensions","SYSTEM_LIBRARY_DIR":"/System/Library","TAPI_VERIFY_MODE":"ErrorsOnly","TARGETED_DEVICE_FAMILY":"1,2","TARGETNAME":"Runner","TARGET_BUILD_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator","TARGET_DEVICE_IDENTIFIER":"41B25245-6594-41DE-9511-845A598DA691","TARGET_DEVICE_MODEL":"iPod9,1","TARGET_DEVICE_OS_VERSION":"14.1","TARGET_NAME":"Runner","TARGET_TEMP_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_FILES_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_FILE_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_ROOT":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios","TEST_FRAMEWORK_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Library/Frameworks /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.1.sdk/Developer/Library/Frameworks","TEST_LIBRARY_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/lib","TOOLCHAINS":"com.apple.dt.toolchain.XcodeDefault","TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","TRACK_WIDGET_CREATION":"true","TREAT_MISSING_BASELINES_AS_TEST_FAILURES":"NO","TREE_SHAKE_ICONS":"false","TeamIdentifierPrefix":"HJAN5D475T.","UID":"504","UNLOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app","UNSTRIPPED_PRODUCT":"NO","USER":"oil","USER_APPS_DIR":"/Users/<USER>/Applications","USER_LIBRARY_DIR":"/Users/<USER>/Library","USE_DYNAMIC_NO_PIC":"YES","USE_HEADERMAP":"YES","USE_HEADER_SYMLINKS":"NO","USE_LLVM_TARGET_TRIPLES":"YES","USE_LLVM_TARGET_TRIPLES_FOR_CLANG":"YES","USE_LLVM_TARGET_TRIPLES_FOR_LD":"YES","USE_LLVM_TARGET_TRIPLES_FOR_TAPI":"YES","VALIDATE_DEVELOPMENT_ASSET_PATHS":"YES_ERROR","VALIDATE_PRODUCT":"NO","VALIDATE_WORKSPACE":"YES_ERROR","VALID_ARCHS":"arm64 arm64e i386 x86_64","VERBOSE_PBXCP":"NO","VERSIONING_SYSTEM":"apple-generic","VERSIONPLIST_PATH":"Runner.app/version.plist","VERSION_INFO_BUILDER":"oil","VERSION_INFO_FILE":"Runner_vers.c","VERSION_INFO_STRING":"\"@(#)PROGRAM:Runner  PROJECT:Runner-164\"","WRAPPER_EXTENSION":"app","WRAPPER_NAME":"Runner.app","WRAPPER_SUFFIX":".app","WRAP_ASSET_PACKS_IN_SEPARATE_DIRECTORIES":"NO","XCODE_APP_SUPPORT_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Xcode","XCODE_PRODUCT_BUILD_VERSION":"12A7403","XCODE_VERSION_ACTUAL":"1210","XCODE_VERSION_MAJOR":"1200","XCODE_VERSION_MINOR":"1210","XPCSERVICES_FOLDER_PATH":"Runner.app/XPCServices","YACC":"yacc","arch":"undefined_arch","variant":"normal"},"allow-missing-inputs":true,"always-out-of-date":true,"working-directory":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","control-enabled":false,"signature":"14730abfc586298414ac6663e6395240"}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:PhaseScriptExecution [CP] Check Pods Manifest.lock /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-328736DAE7F587435A6E60E5.sh": {"tool":"shell","description":"PhaseScriptExecution [CP] Check Pods Manifest.lock /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-328736DAE7F587435A6E60E5.sh","inputs":["/Podfile.lock","/Manifest.lock","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-328736DAE7F587435A6E60E5.sh","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Pods-Runner-checkManifestLockResult.txt"],"args":["/bin/sh","-c","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-328736DAE7F587435A6E60E5.sh"],"env":{"ACTION":"build","AD_HOC_CODE_SIGNING_ALLOWED":"YES","ALTERNATE_GROUP":"staff","ALTERNATE_MODE":"u+w,go-w,a+rX","ALTERNATE_OWNER":"oil","ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES":"NO","ALWAYS_SEARCH_USER_PATHS":"NO","ALWAYS_USE_SEPARATE_HEADERMAPS":"NO","APPLE_INTERNAL_DEVELOPER_DIR":"/AppleInternal/Developer","APPLE_INTERNAL_DIR":"/AppleInternal","APPLE_INTERNAL_DOCUMENTATION_DIR":"/AppleInternal/Documentation","APPLE_INTERNAL_LIBRARY_DIR":"/AppleInternal/Library","APPLE_INTERNAL_TOOLS":"/AppleInternal/Developer/Tools","APPLICATION_EXTENSION_API_ONLY":"NO","APPLY_RULES_IN_COPY_FILES":"NO","APPLY_RULES_IN_COPY_HEADERS":"NO","ARCHS":"x86_64","ARCHS_STANDARD":"arm64 x86_64","ARCHS_STANDARD_32_64_BIT":"arm64 i386 x86_64","ARCHS_STANDARD_32_BIT":"i386","ARCHS_STANDARD_64_BIT":"arm64 x86_64","ARCHS_STANDARD_INCLUDING_64_BIT":"arm64 x86_64","ARCHS_UNIVERSAL_IPHONE_OS":"arm64 i386 x86_64","ASSETCATALOG_COMPILER_APPICON_NAME":"AppIcon","ASSETCATALOG_FILTER_FOR_DEVICE_MODEL":"iPod9,1","ASSETCATALOG_FILTER_FOR_DEVICE_OS_VERSION":"14.1","AVAILABLE_PLATFORMS":"appletvos appletvsimulator iphoneos iphonesimulator macosx watchos watchsimulator","AppIdentifierPrefix":"HJAN5D475T.","BITCODE_GENERATION_MODE":"marker","BUILD_ACTIVE_RESOURCES_ONLY":"YES","BUILD_COMPONENTS":"headers build","BUILD_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios","BUILD_LIBRARY_FOR_DISTRIBUTION":"NO","BUILD_ROOT":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios","BUILD_STYLE":"","BUILD_VARIANTS":"normal","BUILT_PRODUCTS_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator","BUNDLE_CONTENTS_FOLDER_PATH_deep":"Contents/","BUNDLE_EXECUTABLE_FOLDER_NAME_deep":"MacOS","BUNDLE_FORMAT":"shallow","BUNDLE_FRAMEWORKS_FOLDER_PATH":"Frameworks","BUNDLE_PLUGINS_FOLDER_PATH":"PlugIns","BUNDLE_PRIVATE_HEADERS_FOLDER_PATH":"PrivateHeaders","BUNDLE_PUBLIC_HEADERS_FOLDER_PATH":"Headers","CACHE_ROOT":"/var/folders/bk/sl_nb2512xq326xbgz2djqx80000gr/C/com.apple.DeveloperTools/12.1-12A7403/Xcode","CCHROOT":"/var/folders/bk/sl_nb2512xq326xbgz2djqx80000gr/C/com.apple.DeveloperTools/12.1-12A7403/Xcode","CHMOD":"/bin/chmod","CHOWN":"/usr/sbin/chown","CLANG_ANALYZER_NONNULL":"YES","CLANG_CXX_LANGUAGE_STANDARD":"gnu++0x","CLANG_CXX_LIBRARY":"libc++","CLANG_ENABLE_MODULES":"YES","CLANG_ENABLE_OBJC_ARC":"YES","CLANG_MODULES_BUILD_SESSION_FILE":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation","CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING":"YES","CLANG_WARN_BOOL_CONVERSION":"YES","CLANG_WARN_COMMA":"YES","CLANG_WARN_CONSTANT_CONVERSION":"YES","CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS":"YES","CLANG_WARN_DIRECT_OBJC_ISA_USAGE":"YES_ERROR","CLANG_WARN_EMPTY_BODY":"YES","CLANG_WARN_ENUM_CONVERSION":"YES","CLANG_WARN_INFINITE_RECURSION":"YES","CLANG_WARN_INT_CONVERSION":"YES","CLANG_WARN_NON_LITERAL_NULL_CONVERSION":"YES","CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF":"YES","CLANG_WARN_OBJC_LITERAL_CONVERSION":"YES","CLANG_WARN_OBJC_ROOT_CLASS":"YES_ERROR","CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER":"YES","CLANG_WARN_RANGE_LOOP_ANALYSIS":"YES","CLANG_WARN_STRICT_PROTOTYPES":"YES","CLANG_WARN_SUSPICIOUS_MOVE":"YES","CLANG_WARN_UNREACHABLE_CODE":"YES","CLANG_WARN__DUPLICATE_METHOD_MATCH":"YES","CLASS_FILE_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/JavaClasses","CLEAN_PRECOMPS":"YES","CLONE_HEADERS":"NO","CODESIGNING_FOLDER_PATH":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app","CODE_SIGNING_ALLOWED":"YES","CODE_SIGNING_REQUIRED":"YES","CODE_SIGN_CONTEXT_CLASS":"XCiPhoneSimulatorCodeSignContext","CODE_SIGN_IDENTITY":"Apple Development","CODE_SIGN_INJECT_BASE_ENTITLEMENTS":"YES","CODE_SIGN_STYLE":"Automatic","COLOR_DIAGNOSTICS":"NO","COMBINE_HIDPI_IMAGES":"NO","COMPILER_INDEX_STORE_ENABLE":"Default","COMPOSITE_SDK_DIRS":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/CompositeSDKs","COMPRESS_PNG_FILES":"YES","CONFIGURATION":"Debug","CONFIGURATION_BUILD_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator","CONFIGURATION_TEMP_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator","CONTENTS_FOLDER_PATH":"Runner.app","COPYING_PRESERVES_HFS_DATA":"NO","COPY_HEADERS_RUN_UNIFDEF":"NO","COPY_PHASE_STRIP":"NO","COPY_RESOURCES_FROM_STATIC_FRAMEWORKS":"YES","CORRESPONDING_DEVICE_PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform","CORRESPONDING_DEVICE_PLATFORM_NAME":"iphoneos","CORRESPONDING_DEVICE_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.1.sdk","CORRESPONDING_DEVICE_SDK_NAME":"iphoneos14.1","CP":"/bin/cp","CREATE_INFOPLIST_SECTION_IN_BINARY":"NO","CURRENT_ARCH":"undefined_arch","CURRENT_PROJECT_VERSION":"164","CURRENT_VARIANT":"normal","DART_DEFINES":"flutter.inspector.structuredErrors%3Dtrue","DART_OBFUSCATION":"false","DEAD_CODE_STRIPPING":"YES","DEBUGGING_SYMBOLS":"YES","DEBUG_INFORMATION_FORMAT":"dwarf","DEFAULT_COMPILER":"com.apple.compilers.llvm.clang.1_0","DEFAULT_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","DEFAULT_KEXT_INSTALL_PATH":"/System/Library/Extensions","DEFINES_MODULE":"NO","DEPLOYMENT_LOCATION":"NO","DEPLOYMENT_POSTPROCESSING":"NO","DEPLOYMENT_TARGET_CLANG_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_CLANG_FLAG_NAME":"mios-simulator-version-min","DEPLOYMENT_TARGET_CLANG_FLAG_PREFIX":"-mios-simulator-version-min=","DEPLOYMENT_TARGET_LD_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_LD_FLAG_NAME":"ios_simulator_version_min","DEPLOYMENT_TARGET_SETTING_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_SUGGESTED_VALUES":"9.0 9.1 9.2 9.3 10.0 10.1 10.2 10.3 11.0 11.1 11.2 11.3 11.4 12.0 12.1 12.2 12.3 12.4 13.0 13.1 13.2 13.3 13.4 13.5 13.6 14.0 14.1","DERIVED_FILES_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","DERIVED_FILE_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","DERIVED_SOURCES_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","DEVELOPER_FRAMEWORKS_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_FRAMEWORKS_DIR_QUOTED":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Library","DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs","DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Tools","DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","DEVELOPMENT_LANGUAGE":"en","DEVELOPMENT_TEAM":"HJAN5D475T","DOCUMENTATION_FOLDER_PATH":"Runner.app/en.lproj/Documentation","DONT_GENERATE_INFOPLIST_FILE":"NO","DO_HEADER_SCANNING_IN_JAM":"NO","DSTROOT":"/tmp/Runner.dst","DT_TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","DWARF_DSYM_FILE_NAME":"Runner.app.dSYM","DWARF_DSYM_FILE_SHOULD_ACCOMPANY_PRODUCT":"NO","DWARF_DSYM_FOLDER_PATH":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator","EFFECTIVE_PLATFORM_NAME":"-iphonesimulator","EMBEDDED_CONTENT_CONTAINS_SWIFT":"NO","EMBED_ASSET_PACKS_IN_PRODUCT_BUNDLE":"NO","ENABLE_BITCODE":"NO","ENABLE_DEFAULT_HEADER_SEARCH_PATHS":"YES","ENABLE_HARDENED_RUNTIME":"NO","ENABLE_HEADER_DEPENDENCIES":"YES","ENABLE_ON_DEMAND_RESOURCES":"YES","ENABLE_PREVIEWS":"NO","ENABLE_STRICT_OBJC_MSGSEND":"YES","ENABLE_TESTABILITY":"YES","ENABLE_TESTING_SEARCH_PATHS":"NO","ENTITLEMENTS_DESTINATION":"__entitlements","ENTITLEMENTS_REQUIRED":"YES","EXCLUDED_INSTALLSRC_SUBDIRECTORY_PATTERNS":".DS_Store .svn .git .hg CVS","EXCLUDED_RECURSIVE_SEARCH_PATH_SUBDIRECTORIES":"*.nib *.lproj *.framework *.gch *.xcode* *.xcassets (*) .DS_Store CVS .svn .git .hg *.pbproj *.pbxproj","EXECUTABLES_FOLDER_PATH":"Runner.app/Executables","EXECUTABLE_FOLDER_PATH":"Runner.app","EXECUTABLE_NAME":"Runner","EXECUTABLE_PATH":"Runner.app/Runner","EXPANDED_CODE_SIGN_IDENTITY":"-","EXPANDED_CODE_SIGN_IDENTITY_NAME":"-","FILE_LIST":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects/LinkFileList","FIXED_FILES_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/FixedFiles","FLUTTER_APPLICATION_PATH":"/Users/<USER>/Dabzshot/LikeWalletPharse3","FLUTTER_BUILD_DIR":"build","FLUTTER_BUILD_NAME":"2.0.14","FLUTTER_BUILD_NUMBER":"160","FLUTTER_FRAMEWORK_DIR":"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios","FLUTTER_ROOT":"/Users/<USER>/flutter","FLUTTER_TARGET":"/Users/<USER>/Dabzshot/LikeWalletPharse3/lib/main.dart","FRAMEWORKS_FOLDER_PATH":"Runner.app/Frameworks","FRAMEWORK_FLAG_PREFIX":"-framework","FRAMEWORK_SEARCH_PATHS":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator  /Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Flutter","FRAMEWORK_VERSION":"A","FULL_PRODUCT_NAME":"Runner.app","GCC3_VERSION":"3.3","GCC_C_LANGUAGE_STANDARD":"gnu99","GCC_DYNAMIC_NO_PIC":"NO","GCC_INLINES_ARE_PRIVATE_EXTERN":"YES","GCC_NO_COMMON_BLOCKS":"YES","GCC_OBJC_LEGACY_DISPATCH":"YES","GCC_OPTIMIZATION_LEVEL":"0","GCC_PFE_FILE_C_DIALECTS":"c objective-c c++ objective-c++","GCC_PREPROCESSOR_DEFINITIONS":"DEBUG=1 ","GCC_SYMBOLS_PRIVATE_EXTERN":"NO","GCC_TREAT_WARNINGS_AS_ERRORS":"NO","GCC_VERSION":"com.apple.compilers.llvm.clang.1_0","GCC_VERSION_IDENTIFIER":"com_apple_compilers_llvm_clang_1_0","GCC_WARN_64_TO_32_BIT_CONVERSION":"YES","GCC_WARN_ABOUT_RETURN_TYPE":"YES_ERROR","GCC_WARN_UNDECLARED_SELECTOR":"YES","GCC_WARN_UNINITIALIZED_AUTOS":"YES_AGGRESSIVE","GCC_WARN_UNUSED_FUNCTION":"YES","GCC_WARN_UNUSED_VARIABLE":"YES","GENERATED_MODULEMAP_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/GeneratedModuleMaps-iphonesimulator","GENERATE_MASTER_OBJECT_FILE":"NO","GENERATE_PKGINFO_FILE":"YES","GENERATE_PROFILING_CODE":"NO","GENERATE_TEXT_BASED_STUBS":"NO","GID":"20","GROUP":"staff","HEADERMAP_INCLUDES_FLAT_ENTRIES_FOR_TARGET_BEING_BUILT":"YES","HEADERMAP_INCLUDES_FRAMEWORK_ENTRIES_FOR_ALL_PRODUCT_TYPES":"YES","HEADERMAP_INCLUDES_NONPUBLIC_NONPRIVATE_HEADERS":"YES","HEADERMAP_INCLUDES_PROJECT_HEADERS":"YES","HEADERMAP_USES_FRAMEWORK_PREFIX_ENTRIES":"YES","HEADERMAP_USES_VFS":"NO","HEADER_SEARCH_PATHS":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/include ","HIDE_BITCODE_SYMBOLS":"YES","HOME":"/Users/<USER>","ICONV":"/usr/bin/iconv","INFOPLIST_EXPAND_BUILD_SETTINGS":"YES","INFOPLIST_FILE":"Runner/Info.plist","INFOPLIST_OUTPUT_FORMAT":"binary","INFOPLIST_PATH":"Runner.app/Info.plist","INFOPLIST_PREPROCESS":"NO","INFOSTRINGS_PATH":"Runner.app/en.lproj/InfoPlist.strings","INLINE_PRIVATE_FRAMEWORKS":"NO","INSTALLHDRS_COPY_PHASE":"NO","INSTALLHDRS_SCRIPT_PHASE":"NO","INSTALL_DIR":"/tmp/Runner.dst/Applications","INSTALL_GROUP":"staff","INSTALL_MODE_FLAG":"u+w,go-w,a+rX","INSTALL_OWNER":"oil","INSTALL_PATH":"/Applications","INSTALL_ROOT":"/tmp/Runner.dst","IPHONEOS_DEPLOYMENT_TARGET":"12.0","JAVAC_DEFAULT_FLAGS":"-J-Xms64m -J-XX:NewSize=4M -J-Dfile.encoding=UTF8","JAVA_APP_STUB":"/System/Library/Frameworks/JavaVM.framework/Resources/MacOS/JavaApplicationStub","JAVA_ARCHIVE_CLASSES":"YES","JAVA_ARCHIVE_TYPE":"JAR","JAVA_COMPILER":"/usr/bin/javac","JAVA_FOLDER_PATH":"Runner.app/Java","JAVA_FRAMEWORK_RESOURCES_DIRS":"Resources","JAVA_JAR_FLAGS":"cv","JAVA_SOURCE_SUBDIR":".","JAVA_USE_DEPENDENCIES":"YES","JAVA_ZIP_FLAGS":"-urg","JIKES_DEFAULT_FLAGS":"+E +OLDCSO","KEEP_PRIVATE_EXTERNS":"NO","LD_DEPENDENCY_INFO_FILE":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/undefined_arch/Runner_dependency_info.dat","LD_ENTITLEMENTS_SECTION":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent","LD_GENERATE_MAP_FILE":"NO","LD_MAP_FILE_PATH":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-LinkMap-normal-undefined_arch.txt","LD_NO_PIE":"NO","LD_QUOTE_LINKER_ARGUMENTS_FOR_COMPILER_DRIVER":"YES","LD_RUNPATH_SEARCH_PATHS":" @executable_path/Frameworks","LEGACY_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/PlugIns/Xcode3Core.ideplugin/Contents/SharedSupport/Developer","LEX":"lex","LIBRARY_DEXT_INSTALL_PATH":"/Library/DriverExtensions","LIBRARY_FLAG_NOSPACE":"YES","LIBRARY_FLAG_PREFIX":"-l","LIBRARY_KEXT_INSTALL_PATH":"/Library/Extensions","LIBRARY_SEARCH_PATHS":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator  /Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Flutter","LINKER_DISPLAYS_MANGLED_NAMES":"NO","LINK_FILE_LIST_normal_x86_64":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList","LINK_WITH_STANDARD_LIBRARIES":"YES","LLVM_TARGET_TRIPLE_OS_VERSION":"ios12.0","LLVM_TARGET_TRIPLE_SUFFIX":"-simulator","LLVM_TARGET_TRIPLE_VENDOR":"apple","LOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app/en.lproj","LOCALIZED_STRING_MACRO_NAMES":"NSLocalizedString CFCopyLocalizedString","LOCALIZED_STRING_SWIFTUI_SUPPORT":"YES","LOCAL_ADMIN_APPS_DIR":"/Applications/Utilities","LOCAL_APPS_DIR":"/Applications","LOCAL_DEVELOPER_DIR":"/Library/Developer","LOCAL_LIBRARY_DIR":"/Library","LOCROOT":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","LOCSYMROOT":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","MACH_O_TYPE":"mh_execute","MAC_OS_X_PRODUCT_BUILD_VERSION":"20B29","MAC_OS_X_VERSION_ACTUAL":"110001","MAC_OS_X_VERSION_MAJOR":"110000","MAC_OS_X_VERSION_MINOR":"0001","MARKETING_VERSION":"2.0.25","METAL_LIBRARY_FILE_BASE":"default","METAL_LIBRARY_OUTPUT_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app","MODULES_FOLDER_PATH":"Runner.app/Modules","MODULE_CACHE_DIR":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex","MTL_ENABLE_DEBUG_INFO":"YES","NATIVE_ARCH":"x86_64","NATIVE_ARCH_32_BIT":"i386","NATIVE_ARCH_64_BIT":"x86_64","NATIVE_ARCH_ACTUAL":"x86_64","NO_COMMON":"YES","OBJC_ABI_VERSION":"2","OBJECT_FILE_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects","OBJECT_FILE_DIR_normal":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal","OBJROOT":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios","ONLY_ACTIVE_ARCH":"YES","OS":"MACOS","OSAC":"/usr/bin/osacompile","OTHER_LDFLAGS":" -framework Flutter","PACKAGE_CONFIG":".packages","PACKAGE_TYPE":"com.apple.package-type.wrapper.application","PASCAL_STRINGS":"YES","PATH":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/libexec:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/local/bin:/Applications/Xcode.app/Contents/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/usr/local/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin","PATH_PREFIXES_EXCLUDED_FROM_HEADER_DEPENDENCIES":"/usr/include /usr/local/include /System/Library/Frameworks /System/Library/PrivateFrameworks /Applications/Xcode.app/Contents/Developer/Headers /Applications/Xcode.app/Contents/Developer/SDKs /Applications/Xcode.app/Contents/Developer/Platforms","PBDEVELOPMENTPLIST_PATH":"Runner.app/pbdevelopment.plist","PER_ARCH_OBJECT_FILE_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/undefined_arch","PER_VARIANT_OBJECT_FILE_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal","PKGINFO_FILE_PATH":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/PkgInfo","PKGINFO_PATH":"Runner.app/PkgInfo","PLATFORM_DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Applications","PLATFORM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/bin","PLATFORM_DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Library","PLATFORM_DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs","PLATFORM_DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Tools","PLATFORM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr","PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform","PLATFORM_DISPLAY_NAME":"iOS Simulator","PLATFORM_FAMILY_NAME":"iOS","PLATFORM_NAME":"iphonesimulator","PLATFORM_PREFERRED_ARCH":"x86_64","PLATFORM_PRODUCT_BUILD_VERSION":"18A8394","PLIST_FILE_OUTPUT_FORMAT":"binary","PLUGINS_FOLDER_PATH":"Runner.app/PlugIns","PRECOMPS_INCLUDE_HEADERS_FROM_BUILT_PRODUCTS_DIR":"YES","PRECOMP_DESTINATION_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/PrefixHeaders","PRESERVE_DEAD_CODE_INITS_AND_TERMS":"NO","PRIVATE_HEADERS_FOLDER_PATH":"Runner.app/PrivateHeaders","PRODUCT_BUNDLE_IDENTIFIER":"likewallet.likewallet","PRODUCT_BUNDLE_PACKAGE_TYPE":"APPL","PRODUCT_MODULE_NAME":"Runner","PRODUCT_NAME":"Runner","PRODUCT_SETTINGS_PATH":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Runner/Info.plist","PRODUCT_TYPE":"com.apple.product-type.application","PROFILING_CODE":"NO","PROJECT":"Runner","PROJECT_DERIVED_FILE_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/DerivedSources","PROJECT_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","PROJECT_FILE_PATH":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Runner.xcodeproj","PROJECT_NAME":"Runner","PROJECT_TEMP_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build","PROJECT_TEMP_ROOT":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios","PUBLIC_HEADERS_FOLDER_PATH":"Runner.app/Headers","RECURSIVE_SEARCH_PATHS_FOLLOW_SYMLINKS":"YES","REMOVE_CVS_FROM_RESOURCES":"YES","REMOVE_GIT_FROM_RESOURCES":"YES","REMOVE_HEADERS_FROM_EMBEDDED_BUNDLES":"YES","REMOVE_HG_FROM_RESOURCES":"YES","REMOVE_SVN_FROM_RESOURCES":"YES","REZ_COLLECTOR_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/ResourceManagerResources","REZ_OBJECTS_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/ResourceManagerResources/Objects","REZ_SEARCH_PATHS":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator ","SCAN_ALL_SOURCE_FILES_FOR_INCLUDES":"NO","SCRIPTS_FOLDER_PATH":"Runner.app/Scripts","SCRIPT_INPUT_FILE_0":"/Podfile.lock","SCRIPT_INPUT_FILE_1":"/Manifest.lock","SCRIPT_INPUT_FILE_COUNT":"2","SCRIPT_INPUT_FILE_LIST_COUNT":"0","SCRIPT_OUTPUT_FILE_0":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Pods-Runner-checkManifestLockResult.txt","SCRIPT_OUTPUT_FILE_COUNT":"1","SCRIPT_OUTPUT_FILE_LIST_COUNT":"0","SDKROOT":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.1.sdk","SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.1.sdk","SDK_DIR_iphonesimulator14_1":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.1.sdk","SDK_NAME":"iphonesimulator14.1","SDK_NAMES":"iphonesimulator14.1","SDK_PRODUCT_BUILD_VERSION":"18A8394","SDK_VERSION":"14.1","SDK_VERSION_ACTUAL":"140100","SDK_VERSION_MAJOR":"140000","SDK_VERSION_MINOR":"100","SED":"/usr/bin/sed","SEPARATE_STRIP":"NO","SEPARATE_SYMBOL_EDIT":"NO","SET_DIR_MODE_OWNER_GROUP":"YES","SET_FILE_MODE_OWNER_GROUP":"NO","SHALLOW_BUNDLE":"YES","SHARED_DERIVED_FILE_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/DerivedSources","SHARED_FRAMEWORKS_FOLDER_PATH":"Runner.app/SharedFrameworks","SHARED_PRECOMPS_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/SharedPrecompiledHeaders","SHARED_SUPPORT_FOLDER_PATH":"Runner.app/SharedSupport","SKIP_INSTALL":"NO","SOURCE_ROOT":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","SRCROOT":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","STRINGS_FILE_OUTPUT_ENCODING":"binary","STRIP_BITCODE_FROM_COPIED_FILES":"NO","STRIP_INSTALLED_PRODUCT":"YES","STRIP_STYLE":"all","STRIP_SWIFT_SYMBOLS":"YES","SUPPORTED_DEVICE_FAMILIES":"1,2","SUPPORTED_PLATFORMS":"iphoneos iphonesimulator","SUPPORTS_TEXT_BASED_API":"NO","SWIFT_OBJC_BRIDGING_HEADER":"Runner/Runner-Bridging-Header.h","SWIFT_OPTIMIZATION_LEVEL":"-Onone","SWIFT_PLATFORM_TARGET_PREFIX":"ios","SWIFT_RESPONSE_FILE_PATH_normal_x86_64":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList","SWIFT_VERSION":"5.0","SYMROOT":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios","SYSTEM_ADMIN_APPS_DIR":"/Applications/Utilities","SYSTEM_APPS_DIR":"/Applications","SYSTEM_CORE_SERVICES_DIR":"/System/Library/CoreServices","SYSTEM_DEMOS_DIR":"/Applications/Extras","SYSTEM_DEVELOPER_APPS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","SYSTEM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","SYSTEM_DEVELOPER_DEMOS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities/Built Examples","SYSTEM_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","SYSTEM_DEVELOPER_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library","SYSTEM_DEVELOPER_GRAPHICS_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Graphics Tools","SYSTEM_DEVELOPER_JAVA_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Java Tools","SYSTEM_DEVELOPER_PERFORMANCE_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Performance Tools","SYSTEM_DEVELOPER_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes","SYSTEM_DEVELOPER_TOOLS":"/Applications/Xcode.app/Contents/Developer/Tools","SYSTEM_DEVELOPER_TOOLS_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/documentation/DeveloperTools","SYSTEM_DEVELOPER_TOOLS_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes/DeveloperTools","SYSTEM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","SYSTEM_DEVELOPER_UTILITIES_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities","SYSTEM_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","SYSTEM_DOCUMENTATION_DIR":"/Library/Documentation","SYSTEM_KEXT_INSTALL_PATH":"/System/Library/Extensions","SYSTEM_LIBRARY_DIR":"/System/Library","TAPI_VERIFY_MODE":"ErrorsOnly","TARGETED_DEVICE_FAMILY":"1,2","TARGETNAME":"Runner","TARGET_BUILD_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator","TARGET_DEVICE_IDENTIFIER":"41B25245-6594-41DE-9511-845A598DA691","TARGET_DEVICE_MODEL":"iPod9,1","TARGET_DEVICE_OS_VERSION":"14.1","TARGET_NAME":"Runner","TARGET_TEMP_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_FILES_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_FILE_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_ROOT":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios","TEST_FRAMEWORK_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Library/Frameworks /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.1.sdk/Developer/Library/Frameworks","TEST_LIBRARY_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/lib","TOOLCHAINS":"com.apple.dt.toolchain.XcodeDefault","TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","TRACK_WIDGET_CREATION":"true","TREAT_MISSING_BASELINES_AS_TEST_FAILURES":"NO","TREE_SHAKE_ICONS":"false","TeamIdentifierPrefix":"HJAN5D475T.","UID":"504","UNLOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app","UNSTRIPPED_PRODUCT":"NO","USER":"oil","USER_APPS_DIR":"/Users/<USER>/Applications","USER_LIBRARY_DIR":"/Users/<USER>/Library","USE_DYNAMIC_NO_PIC":"YES","USE_HEADERMAP":"YES","USE_HEADER_SYMLINKS":"NO","USE_LLVM_TARGET_TRIPLES":"YES","USE_LLVM_TARGET_TRIPLES_FOR_CLANG":"YES","USE_LLVM_TARGET_TRIPLES_FOR_LD":"YES","USE_LLVM_TARGET_TRIPLES_FOR_TAPI":"YES","VALIDATE_DEVELOPMENT_ASSET_PATHS":"YES_ERROR","VALIDATE_PRODUCT":"NO","VALIDATE_WORKSPACE":"YES_ERROR","VALID_ARCHS":"arm64 arm64e i386 x86_64","VERBOSE_PBXCP":"NO","VERSIONING_SYSTEM":"apple-generic","VERSIONPLIST_PATH":"Runner.app/version.plist","VERSION_INFO_BUILDER":"oil","VERSION_INFO_FILE":"Runner_vers.c","VERSION_INFO_STRING":"\"@(#)PROGRAM:Runner  PROJECT:Runner-164\"","WRAPPER_EXTENSION":"app","WRAPPER_NAME":"Runner.app","WRAPPER_SUFFIX":".app","WRAP_ASSET_PACKS_IN_SEPARATE_DIRECTORIES":"NO","XCODE_APP_SUPPORT_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Xcode","XCODE_PRODUCT_BUILD_VERSION":"12A7403","XCODE_VERSION_ACTUAL":"1210","XCODE_VERSION_MAJOR":"1200","XCODE_VERSION_MINOR":"1210","XPCSERVICES_FOLDER_PATH":"Runner.app/XPCServices","YACC":"yacc","arch":"undefined_arch","variant":"normal"},"allow-missing-inputs":true,"working-directory":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","control-enabled":false,"signature":"d1166a0142997df07318a91e6060fe76"}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:PhaseScriptExecution [CP] Embed Pods Frameworks /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3759590B8F78A0B90ADCF815.sh": {"tool":"shell","description":"PhaseScriptExecution [CP] Embed Pods Frameworks /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3759590B8F78A0B90ADCF815.sh","inputs":["/Target Support Files/Pods-Runner/Pods-Runner-frameworks.sh","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/BoringSSL-GRPC/openssl_grpc.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/FMDB/FMDB.framework","/Flutter/Flutter.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/GTMSessionFetcher/GTMSessionFetcher.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/GoogleAPIClientForREST/GoogleAPIClientForREST.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/GoogleToolboxForMac/GoogleToolboxForMac.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework","/IdensicMobileSDK/IdensicMobileSDK.framework","/IdensicMobileSDK/IdensicMobileSDK_Liveness3D.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Protobuf/Protobuf.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Reachability/Reachability.framework","/ZoomAuthSDK/ZoomAuthentication.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/abseil/absl.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/app_settings/app_settings.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/camera/camera.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/connectivity/connectivity.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/contacts_service/contacts_service.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/esys_flutter_share/esys_flutter_share.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/flutter_appavailability/flutter_appavailability.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/flutter_idensic_mobile_sdk_plugin/flutter_idensic_mobile_sdk_plugin.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/flutter_local_notifications/flutter_local_notifications.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/flutter_secure_storage/flutter_secure_storage.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/flutter_share_me/flutter_share_me.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/fluttertoast/fluttertoast.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/gRPC-C++/grpcpp.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/gRPC-Core/grpc.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/image_crop/image_crop.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/image_gallery_saver/image_gallery_saver.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/image_picker/image_picker.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/keyboard_visibility/keyboard_visibility.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/launch_review/launch_review.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/leveldb-library/leveldb.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/local_auth/local_auth.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/nanopb/nanopb.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/open_appstore/open_appstore.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/package_info/package_info.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/path_provider/path_provider.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/permission/permission.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/qrcode_flutter/qrcode_flutter.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/share/share.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/shared_preferences/shared_preferences.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/simple_image_crop/simple_image_crop.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/sms_autofill/sms_autofill.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/sms_receiver/sms_receiver.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/sqflite/sqflite.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/store_redirect/store_redirect.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/url_launcher/url_launcher.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/video_player/video_player.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/webview_flutter/webview_flutter.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3759590B8F78A0B90ADCF815.sh","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase6-thin-binary>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/openssl_grpc.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FMDB.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Flutter.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GTMSessionFetcher.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleAPIClientForREST.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleToolboxForMac.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleUtilities.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/IdensicMobileSDK.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/IdensicMobileSDK_Liveness3D.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBLPromises.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Protobuf.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Reachability.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/ZoomAuthentication.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/absl.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/app_settings.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/camera.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/connectivity.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/contacts_service.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/esys_flutter_share.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_appavailability.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_idensic_mobile_sdk_plugin.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_local_notifications.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_secure_storage.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_share_me.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/fluttertoast.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/grpcpp.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/grpc.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_crop.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_gallery_saver.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_picker.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/keyboard_visibility.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/launch_review.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/leveldb.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/local_auth.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/nanopb.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/open_appstore.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/package_info.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/path_provider.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/permission.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/qrcode_flutter.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/share.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/shared_preferences.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/simple_image_crop.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sms_autofill.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sms_receiver.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sqflite.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/store_redirect.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/url_launcher.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/video_player.framework","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/webview_flutter.framework"],"args":["/bin/sh","-c","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3759590B8F78A0B90ADCF815.sh"],"env":{"ACTION":"build","AD_HOC_CODE_SIGNING_ALLOWED":"YES","ALTERNATE_GROUP":"staff","ALTERNATE_MODE":"u+w,go-w,a+rX","ALTERNATE_OWNER":"oil","ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES":"NO","ALWAYS_SEARCH_USER_PATHS":"NO","ALWAYS_USE_SEPARATE_HEADERMAPS":"NO","APPLE_INTERNAL_DEVELOPER_DIR":"/AppleInternal/Developer","APPLE_INTERNAL_DIR":"/AppleInternal","APPLE_INTERNAL_DOCUMENTATION_DIR":"/AppleInternal/Documentation","APPLE_INTERNAL_LIBRARY_DIR":"/AppleInternal/Library","APPLE_INTERNAL_TOOLS":"/AppleInternal/Developer/Tools","APPLICATION_EXTENSION_API_ONLY":"NO","APPLY_RULES_IN_COPY_FILES":"NO","APPLY_RULES_IN_COPY_HEADERS":"NO","ARCHS":"x86_64","ARCHS_STANDARD":"arm64 x86_64","ARCHS_STANDARD_32_64_BIT":"arm64 i386 x86_64","ARCHS_STANDARD_32_BIT":"i386","ARCHS_STANDARD_64_BIT":"arm64 x86_64","ARCHS_STANDARD_INCLUDING_64_BIT":"arm64 x86_64","ARCHS_UNIVERSAL_IPHONE_OS":"arm64 i386 x86_64","ASSETCATALOG_COMPILER_APPICON_NAME":"AppIcon","ASSETCATALOG_FILTER_FOR_DEVICE_MODEL":"iPod9,1","ASSETCATALOG_FILTER_FOR_DEVICE_OS_VERSION":"14.1","AVAILABLE_PLATFORMS":"appletvos appletvsimulator iphoneos iphonesimulator macosx watchos watchsimulator","AppIdentifierPrefix":"HJAN5D475T.","BITCODE_GENERATION_MODE":"marker","BUILD_ACTIVE_RESOURCES_ONLY":"YES","BUILD_COMPONENTS":"headers build","BUILD_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios","BUILD_LIBRARY_FOR_DISTRIBUTION":"NO","BUILD_ROOT":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios","BUILD_STYLE":"","BUILD_VARIANTS":"normal","BUILT_PRODUCTS_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator","BUNDLE_CONTENTS_FOLDER_PATH_deep":"Contents/","BUNDLE_EXECUTABLE_FOLDER_NAME_deep":"MacOS","BUNDLE_FORMAT":"shallow","BUNDLE_FRAMEWORKS_FOLDER_PATH":"Frameworks","BUNDLE_PLUGINS_FOLDER_PATH":"PlugIns","BUNDLE_PRIVATE_HEADERS_FOLDER_PATH":"PrivateHeaders","BUNDLE_PUBLIC_HEADERS_FOLDER_PATH":"Headers","CACHE_ROOT":"/var/folders/bk/sl_nb2512xq326xbgz2djqx80000gr/C/com.apple.DeveloperTools/12.1-12A7403/Xcode","CCHROOT":"/var/folders/bk/sl_nb2512xq326xbgz2djqx80000gr/C/com.apple.DeveloperTools/12.1-12A7403/Xcode","CHMOD":"/bin/chmod","CHOWN":"/usr/sbin/chown","CLANG_ANALYZER_NONNULL":"YES","CLANG_CXX_LANGUAGE_STANDARD":"gnu++0x","CLANG_CXX_LIBRARY":"libc++","CLANG_ENABLE_MODULES":"YES","CLANG_ENABLE_OBJC_ARC":"YES","CLANG_MODULES_BUILD_SESSION_FILE":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation","CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING":"YES","CLANG_WARN_BOOL_CONVERSION":"YES","CLANG_WARN_COMMA":"YES","CLANG_WARN_CONSTANT_CONVERSION":"YES","CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS":"YES","CLANG_WARN_DIRECT_OBJC_ISA_USAGE":"YES_ERROR","CLANG_WARN_EMPTY_BODY":"YES","CLANG_WARN_ENUM_CONVERSION":"YES","CLANG_WARN_INFINITE_RECURSION":"YES","CLANG_WARN_INT_CONVERSION":"YES","CLANG_WARN_NON_LITERAL_NULL_CONVERSION":"YES","CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF":"YES","CLANG_WARN_OBJC_LITERAL_CONVERSION":"YES","CLANG_WARN_OBJC_ROOT_CLASS":"YES_ERROR","CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER":"YES","CLANG_WARN_RANGE_LOOP_ANALYSIS":"YES","CLANG_WARN_STRICT_PROTOTYPES":"YES","CLANG_WARN_SUSPICIOUS_MOVE":"YES","CLANG_WARN_UNREACHABLE_CODE":"YES","CLANG_WARN__DUPLICATE_METHOD_MATCH":"YES","CLASS_FILE_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/JavaClasses","CLEAN_PRECOMPS":"YES","CLONE_HEADERS":"NO","CODESIGNING_FOLDER_PATH":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app","CODE_SIGNING_ALLOWED":"YES","CODE_SIGNING_REQUIRED":"YES","CODE_SIGN_CONTEXT_CLASS":"XCiPhoneSimulatorCodeSignContext","CODE_SIGN_IDENTITY":"Apple Development","CODE_SIGN_INJECT_BASE_ENTITLEMENTS":"YES","CODE_SIGN_STYLE":"Automatic","COLOR_DIAGNOSTICS":"NO","COMBINE_HIDPI_IMAGES":"NO","COMPILER_INDEX_STORE_ENABLE":"Default","COMPOSITE_SDK_DIRS":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/CompositeSDKs","COMPRESS_PNG_FILES":"YES","CONFIGURATION":"Debug","CONFIGURATION_BUILD_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator","CONFIGURATION_TEMP_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator","CONTENTS_FOLDER_PATH":"Runner.app","COPYING_PRESERVES_HFS_DATA":"NO","COPY_HEADERS_RUN_UNIFDEF":"NO","COPY_PHASE_STRIP":"NO","COPY_RESOURCES_FROM_STATIC_FRAMEWORKS":"YES","CORRESPONDING_DEVICE_PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform","CORRESPONDING_DEVICE_PLATFORM_NAME":"iphoneos","CORRESPONDING_DEVICE_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.1.sdk","CORRESPONDING_DEVICE_SDK_NAME":"iphoneos14.1","CP":"/bin/cp","CREATE_INFOPLIST_SECTION_IN_BINARY":"NO","CURRENT_ARCH":"undefined_arch","CURRENT_PROJECT_VERSION":"164","CURRENT_VARIANT":"normal","DART_DEFINES":"flutter.inspector.structuredErrors%3Dtrue","DART_OBFUSCATION":"false","DEAD_CODE_STRIPPING":"YES","DEBUGGING_SYMBOLS":"YES","DEBUG_INFORMATION_FORMAT":"dwarf","DEFAULT_COMPILER":"com.apple.compilers.llvm.clang.1_0","DEFAULT_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","DEFAULT_KEXT_INSTALL_PATH":"/System/Library/Extensions","DEFINES_MODULE":"NO","DEPLOYMENT_LOCATION":"NO","DEPLOYMENT_POSTPROCESSING":"NO","DEPLOYMENT_TARGET_CLANG_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_CLANG_FLAG_NAME":"mios-simulator-version-min","DEPLOYMENT_TARGET_CLANG_FLAG_PREFIX":"-mios-simulator-version-min=","DEPLOYMENT_TARGET_LD_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_LD_FLAG_NAME":"ios_simulator_version_min","DEPLOYMENT_TARGET_SETTING_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_SUGGESTED_VALUES":"9.0 9.1 9.2 9.3 10.0 10.1 10.2 10.3 11.0 11.1 11.2 11.3 11.4 12.0 12.1 12.2 12.3 12.4 13.0 13.1 13.2 13.3 13.4 13.5 13.6 14.0 14.1","DERIVED_FILES_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","DERIVED_FILE_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","DERIVED_SOURCES_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","DEVELOPER_FRAMEWORKS_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_FRAMEWORKS_DIR_QUOTED":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Library","DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs","DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Tools","DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","DEVELOPMENT_LANGUAGE":"en","DEVELOPMENT_TEAM":"HJAN5D475T","DOCUMENTATION_FOLDER_PATH":"Runner.app/en.lproj/Documentation","DONT_GENERATE_INFOPLIST_FILE":"NO","DO_HEADER_SCANNING_IN_JAM":"NO","DSTROOT":"/tmp/Runner.dst","DT_TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","DWARF_DSYM_FILE_NAME":"Runner.app.dSYM","DWARF_DSYM_FILE_SHOULD_ACCOMPANY_PRODUCT":"NO","DWARF_DSYM_FOLDER_PATH":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator","EFFECTIVE_PLATFORM_NAME":"-iphonesimulator","EMBEDDED_CONTENT_CONTAINS_SWIFT":"NO","EMBED_ASSET_PACKS_IN_PRODUCT_BUNDLE":"NO","ENABLE_BITCODE":"NO","ENABLE_DEFAULT_HEADER_SEARCH_PATHS":"YES","ENABLE_HARDENED_RUNTIME":"NO","ENABLE_HEADER_DEPENDENCIES":"YES","ENABLE_ON_DEMAND_RESOURCES":"YES","ENABLE_PREVIEWS":"NO","ENABLE_STRICT_OBJC_MSGSEND":"YES","ENABLE_TESTABILITY":"YES","ENABLE_TESTING_SEARCH_PATHS":"NO","ENTITLEMENTS_DESTINATION":"__entitlements","ENTITLEMENTS_REQUIRED":"YES","EXCLUDED_INSTALLSRC_SUBDIRECTORY_PATTERNS":".DS_Store .svn .git .hg CVS","EXCLUDED_RECURSIVE_SEARCH_PATH_SUBDIRECTORIES":"*.nib *.lproj *.framework *.gch *.xcode* *.xcassets (*) .DS_Store CVS .svn .git .hg *.pbproj *.pbxproj","EXECUTABLES_FOLDER_PATH":"Runner.app/Executables","EXECUTABLE_FOLDER_PATH":"Runner.app","EXECUTABLE_NAME":"Runner","EXECUTABLE_PATH":"Runner.app/Runner","EXPANDED_CODE_SIGN_IDENTITY":"-","EXPANDED_CODE_SIGN_IDENTITY_NAME":"-","FILE_LIST":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects/LinkFileList","FIXED_FILES_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/FixedFiles","FLUTTER_APPLICATION_PATH":"/Users/<USER>/Dabzshot/LikeWalletPharse3","FLUTTER_BUILD_DIR":"build","FLUTTER_BUILD_NAME":"2.0.14","FLUTTER_BUILD_NUMBER":"160","FLUTTER_FRAMEWORK_DIR":"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios","FLUTTER_ROOT":"/Users/<USER>/flutter","FLUTTER_TARGET":"/Users/<USER>/Dabzshot/LikeWalletPharse3/lib/main.dart","FRAMEWORKS_FOLDER_PATH":"Runner.app/Frameworks","FRAMEWORK_FLAG_PREFIX":"-framework","FRAMEWORK_SEARCH_PATHS":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator  /Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Flutter","FRAMEWORK_VERSION":"A","FULL_PRODUCT_NAME":"Runner.app","GCC3_VERSION":"3.3","GCC_C_LANGUAGE_STANDARD":"gnu99","GCC_DYNAMIC_NO_PIC":"NO","GCC_INLINES_ARE_PRIVATE_EXTERN":"YES","GCC_NO_COMMON_BLOCKS":"YES","GCC_OBJC_LEGACY_DISPATCH":"YES","GCC_OPTIMIZATION_LEVEL":"0","GCC_PFE_FILE_C_DIALECTS":"c objective-c c++ objective-c++","GCC_PREPROCESSOR_DEFINITIONS":"DEBUG=1 ","GCC_SYMBOLS_PRIVATE_EXTERN":"NO","GCC_TREAT_WARNINGS_AS_ERRORS":"NO","GCC_VERSION":"com.apple.compilers.llvm.clang.1_0","GCC_VERSION_IDENTIFIER":"com_apple_compilers_llvm_clang_1_0","GCC_WARN_64_TO_32_BIT_CONVERSION":"YES","GCC_WARN_ABOUT_RETURN_TYPE":"YES_ERROR","GCC_WARN_UNDECLARED_SELECTOR":"YES","GCC_WARN_UNINITIALIZED_AUTOS":"YES_AGGRESSIVE","GCC_WARN_UNUSED_FUNCTION":"YES","GCC_WARN_UNUSED_VARIABLE":"YES","GENERATED_MODULEMAP_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/GeneratedModuleMaps-iphonesimulator","GENERATE_MASTER_OBJECT_FILE":"NO","GENERATE_PKGINFO_FILE":"YES","GENERATE_PROFILING_CODE":"NO","GENERATE_TEXT_BASED_STUBS":"NO","GID":"20","GROUP":"staff","HEADERMAP_INCLUDES_FLAT_ENTRIES_FOR_TARGET_BEING_BUILT":"YES","HEADERMAP_INCLUDES_FRAMEWORK_ENTRIES_FOR_ALL_PRODUCT_TYPES":"YES","HEADERMAP_INCLUDES_NONPUBLIC_NONPRIVATE_HEADERS":"YES","HEADERMAP_INCLUDES_PROJECT_HEADERS":"YES","HEADERMAP_USES_FRAMEWORK_PREFIX_ENTRIES":"YES","HEADERMAP_USES_VFS":"NO","HEADER_SEARCH_PATHS":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/include ","HIDE_BITCODE_SYMBOLS":"YES","HOME":"/Users/<USER>","ICONV":"/usr/bin/iconv","INFOPLIST_EXPAND_BUILD_SETTINGS":"YES","INFOPLIST_FILE":"Runner/Info.plist","INFOPLIST_OUTPUT_FORMAT":"binary","INFOPLIST_PATH":"Runner.app/Info.plist","INFOPLIST_PREPROCESS":"NO","INFOSTRINGS_PATH":"Runner.app/en.lproj/InfoPlist.strings","INLINE_PRIVATE_FRAMEWORKS":"NO","INSTALLHDRS_COPY_PHASE":"NO","INSTALLHDRS_SCRIPT_PHASE":"NO","INSTALL_DIR":"/tmp/Runner.dst/Applications","INSTALL_GROUP":"staff","INSTALL_MODE_FLAG":"u+w,go-w,a+rX","INSTALL_OWNER":"oil","INSTALL_PATH":"/Applications","INSTALL_ROOT":"/tmp/Runner.dst","IPHONEOS_DEPLOYMENT_TARGET":"12.0","JAVAC_DEFAULT_FLAGS":"-J-Xms64m -J-XX:NewSize=4M -J-Dfile.encoding=UTF8","JAVA_APP_STUB":"/System/Library/Frameworks/JavaVM.framework/Resources/MacOS/JavaApplicationStub","JAVA_ARCHIVE_CLASSES":"YES","JAVA_ARCHIVE_TYPE":"JAR","JAVA_COMPILER":"/usr/bin/javac","JAVA_FOLDER_PATH":"Runner.app/Java","JAVA_FRAMEWORK_RESOURCES_DIRS":"Resources","JAVA_JAR_FLAGS":"cv","JAVA_SOURCE_SUBDIR":".","JAVA_USE_DEPENDENCIES":"YES","JAVA_ZIP_FLAGS":"-urg","JIKES_DEFAULT_FLAGS":"+E +OLDCSO","KEEP_PRIVATE_EXTERNS":"NO","LD_DEPENDENCY_INFO_FILE":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/undefined_arch/Runner_dependency_info.dat","LD_ENTITLEMENTS_SECTION":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent","LD_GENERATE_MAP_FILE":"NO","LD_MAP_FILE_PATH":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-LinkMap-normal-undefined_arch.txt","LD_NO_PIE":"NO","LD_QUOTE_LINKER_ARGUMENTS_FOR_COMPILER_DRIVER":"YES","LD_RUNPATH_SEARCH_PATHS":" @executable_path/Frameworks","LEGACY_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/PlugIns/Xcode3Core.ideplugin/Contents/SharedSupport/Developer","LEX":"lex","LIBRARY_DEXT_INSTALL_PATH":"/Library/DriverExtensions","LIBRARY_FLAG_NOSPACE":"YES","LIBRARY_FLAG_PREFIX":"-l","LIBRARY_KEXT_INSTALL_PATH":"/Library/Extensions","LIBRARY_SEARCH_PATHS":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator  /Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Flutter","LINKER_DISPLAYS_MANGLED_NAMES":"NO","LINK_FILE_LIST_normal_x86_64":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList","LINK_WITH_STANDARD_LIBRARIES":"YES","LLVM_TARGET_TRIPLE_OS_VERSION":"ios12.0","LLVM_TARGET_TRIPLE_SUFFIX":"-simulator","LLVM_TARGET_TRIPLE_VENDOR":"apple","LOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app/en.lproj","LOCALIZED_STRING_MACRO_NAMES":"NSLocalizedString CFCopyLocalizedString","LOCALIZED_STRING_SWIFTUI_SUPPORT":"YES","LOCAL_ADMIN_APPS_DIR":"/Applications/Utilities","LOCAL_APPS_DIR":"/Applications","LOCAL_DEVELOPER_DIR":"/Library/Developer","LOCAL_LIBRARY_DIR":"/Library","LOCROOT":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","LOCSYMROOT":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","MACH_O_TYPE":"mh_execute","MAC_OS_X_PRODUCT_BUILD_VERSION":"20B29","MAC_OS_X_VERSION_ACTUAL":"110001","MAC_OS_X_VERSION_MAJOR":"110000","MAC_OS_X_VERSION_MINOR":"0001","MARKETING_VERSION":"2.0.25","METAL_LIBRARY_FILE_BASE":"default","METAL_LIBRARY_OUTPUT_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app","MODULES_FOLDER_PATH":"Runner.app/Modules","MODULE_CACHE_DIR":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex","MTL_ENABLE_DEBUG_INFO":"YES","NATIVE_ARCH":"x86_64","NATIVE_ARCH_32_BIT":"i386","NATIVE_ARCH_64_BIT":"x86_64","NATIVE_ARCH_ACTUAL":"x86_64","NO_COMMON":"YES","OBJC_ABI_VERSION":"2","OBJECT_FILE_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects","OBJECT_FILE_DIR_normal":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal","OBJROOT":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios","ONLY_ACTIVE_ARCH":"YES","OS":"MACOS","OSAC":"/usr/bin/osacompile","OTHER_LDFLAGS":" -framework Flutter","PACKAGE_CONFIG":".packages","PACKAGE_TYPE":"com.apple.package-type.wrapper.application","PASCAL_STRINGS":"YES","PATH":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/libexec:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/local/bin:/Applications/Xcode.app/Contents/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/usr/local/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin","PATH_PREFIXES_EXCLUDED_FROM_HEADER_DEPENDENCIES":"/usr/include /usr/local/include /System/Library/Frameworks /System/Library/PrivateFrameworks /Applications/Xcode.app/Contents/Developer/Headers /Applications/Xcode.app/Contents/Developer/SDKs /Applications/Xcode.app/Contents/Developer/Platforms","PBDEVELOPMENTPLIST_PATH":"Runner.app/pbdevelopment.plist","PER_ARCH_OBJECT_FILE_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/undefined_arch","PER_VARIANT_OBJECT_FILE_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal","PKGINFO_FILE_PATH":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/PkgInfo","PKGINFO_PATH":"Runner.app/PkgInfo","PLATFORM_DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Applications","PLATFORM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/bin","PLATFORM_DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Library","PLATFORM_DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs","PLATFORM_DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Tools","PLATFORM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr","PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform","PLATFORM_DISPLAY_NAME":"iOS Simulator","PLATFORM_FAMILY_NAME":"iOS","PLATFORM_NAME":"iphonesimulator","PLATFORM_PREFERRED_ARCH":"x86_64","PLATFORM_PRODUCT_BUILD_VERSION":"18A8394","PLIST_FILE_OUTPUT_FORMAT":"binary","PLUGINS_FOLDER_PATH":"Runner.app/PlugIns","PRECOMPS_INCLUDE_HEADERS_FROM_BUILT_PRODUCTS_DIR":"YES","PRECOMP_DESTINATION_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/PrefixHeaders","PRESERVE_DEAD_CODE_INITS_AND_TERMS":"NO","PRIVATE_HEADERS_FOLDER_PATH":"Runner.app/PrivateHeaders","PRODUCT_BUNDLE_IDENTIFIER":"likewallet.likewallet","PRODUCT_BUNDLE_PACKAGE_TYPE":"APPL","PRODUCT_MODULE_NAME":"Runner","PRODUCT_NAME":"Runner","PRODUCT_SETTINGS_PATH":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Runner/Info.plist","PRODUCT_TYPE":"com.apple.product-type.application","PROFILING_CODE":"NO","PROJECT":"Runner","PROJECT_DERIVED_FILE_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/DerivedSources","PROJECT_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","PROJECT_FILE_PATH":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Runner.xcodeproj","PROJECT_NAME":"Runner","PROJECT_TEMP_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build","PROJECT_TEMP_ROOT":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios","PUBLIC_HEADERS_FOLDER_PATH":"Runner.app/Headers","RECURSIVE_SEARCH_PATHS_FOLLOW_SYMLINKS":"YES","REMOVE_CVS_FROM_RESOURCES":"YES","REMOVE_GIT_FROM_RESOURCES":"YES","REMOVE_HEADERS_FROM_EMBEDDED_BUNDLES":"YES","REMOVE_HG_FROM_RESOURCES":"YES","REMOVE_SVN_FROM_RESOURCES":"YES","REZ_COLLECTOR_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/ResourceManagerResources","REZ_OBJECTS_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/ResourceManagerResources/Objects","REZ_SEARCH_PATHS":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator ","SCAN_ALL_SOURCE_FILES_FOR_INCLUDES":"NO","SCRIPTS_FOLDER_PATH":"Runner.app/Scripts","SCRIPT_INPUT_FILE_0":"/Target Support Files/Pods-Runner/Pods-Runner-frameworks.sh","SCRIPT_INPUT_FILE_1":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/BoringSSL-GRPC/openssl_grpc.framework","SCRIPT_INPUT_FILE_10":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises.framework","SCRIPT_INPUT_FILE_11":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Protobuf/Protobuf.framework","SCRIPT_INPUT_FILE_12":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Reachability/Reachability.framework","SCRIPT_INPUT_FILE_13":"/ZoomAuthSDK/ZoomAuthentication.framework","SCRIPT_INPUT_FILE_14":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/abseil/absl.framework","SCRIPT_INPUT_FILE_15":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/app_settings/app_settings.framework","SCRIPT_INPUT_FILE_16":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/camera/camera.framework","SCRIPT_INPUT_FILE_17":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/connectivity/connectivity.framework","SCRIPT_INPUT_FILE_18":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/contacts_service/contacts_service.framework","SCRIPT_INPUT_FILE_19":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/esys_flutter_share/esys_flutter_share.framework","SCRIPT_INPUT_FILE_2":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/FMDB/FMDB.framework","SCRIPT_INPUT_FILE_20":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/flutter_appavailability/flutter_appavailability.framework","SCRIPT_INPUT_FILE_21":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/flutter_idensic_mobile_sdk_plugin/flutter_idensic_mobile_sdk_plugin.framework","SCRIPT_INPUT_FILE_22":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/flutter_local_notifications/flutter_local_notifications.framework","SCRIPT_INPUT_FILE_23":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/flutter_secure_storage/flutter_secure_storage.framework","SCRIPT_INPUT_FILE_24":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/flutter_share_me/flutter_share_me.framework","SCRIPT_INPUT_FILE_25":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/fluttertoast/fluttertoast.framework","SCRIPT_INPUT_FILE_26":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/gRPC-C++/grpcpp.framework","SCRIPT_INPUT_FILE_27":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/gRPC-Core/grpc.framework","SCRIPT_INPUT_FILE_28":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/image_crop/image_crop.framework","SCRIPT_INPUT_FILE_29":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/image_gallery_saver/image_gallery_saver.framework","SCRIPT_INPUT_FILE_3":"/Flutter/Flutter.framework","SCRIPT_INPUT_FILE_30":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/image_picker/image_picker.framework","SCRIPT_INPUT_FILE_31":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/keyboard_visibility/keyboard_visibility.framework","SCRIPT_INPUT_FILE_32":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/launch_review/launch_review.framework","SCRIPT_INPUT_FILE_33":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/leveldb-library/leveldb.framework","SCRIPT_INPUT_FILE_34":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/local_auth/local_auth.framework","SCRIPT_INPUT_FILE_35":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/nanopb/nanopb.framework","SCRIPT_INPUT_FILE_36":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/open_appstore/open_appstore.framework","SCRIPT_INPUT_FILE_37":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/package_info/package_info.framework","SCRIPT_INPUT_FILE_38":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/path_provider/path_provider.framework","SCRIPT_INPUT_FILE_39":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/permission/permission.framework","SCRIPT_INPUT_FILE_4":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/GTMSessionFetcher/GTMSessionFetcher.framework","SCRIPT_INPUT_FILE_40":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/qrcode_flutter/qrcode_flutter.framework","SCRIPT_INPUT_FILE_41":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/share/share.framework","SCRIPT_INPUT_FILE_42":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/shared_preferences/shared_preferences.framework","SCRIPT_INPUT_FILE_43":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/simple_image_crop/simple_image_crop.framework","SCRIPT_INPUT_FILE_44":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/sms_autofill/sms_autofill.framework","SCRIPT_INPUT_FILE_45":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/sms_receiver/sms_receiver.framework","SCRIPT_INPUT_FILE_46":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/sqflite/sqflite.framework","SCRIPT_INPUT_FILE_47":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/store_redirect/store_redirect.framework","SCRIPT_INPUT_FILE_48":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/url_launcher/url_launcher.framework","SCRIPT_INPUT_FILE_49":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/video_player/video_player.framework","SCRIPT_INPUT_FILE_5":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/GoogleAPIClientForREST/GoogleAPIClientForREST.framework","SCRIPT_INPUT_FILE_50":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/webview_flutter/webview_flutter.framework","SCRIPT_INPUT_FILE_6":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/GoogleToolboxForMac/GoogleToolboxForMac.framework","SCRIPT_INPUT_FILE_7":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework","SCRIPT_INPUT_FILE_8":"/IdensicMobileSDK/IdensicMobileSDK.framework","SCRIPT_INPUT_FILE_9":"/IdensicMobileSDK/IdensicMobileSDK_Liveness3D.framework","SCRIPT_INPUT_FILE_COUNT":"51","SCRIPT_INPUT_FILE_LIST_COUNT":"0","SCRIPT_OUTPUT_FILE_0":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/openssl_grpc.framework","SCRIPT_OUTPUT_FILE_1":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FMDB.framework","SCRIPT_OUTPUT_FILE_10":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Protobuf.framework","SCRIPT_OUTPUT_FILE_11":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Reachability.framework","SCRIPT_OUTPUT_FILE_12":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/ZoomAuthentication.framework","SCRIPT_OUTPUT_FILE_13":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/absl.framework","SCRIPT_OUTPUT_FILE_14":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/app_settings.framework","SCRIPT_OUTPUT_FILE_15":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/camera.framework","SCRIPT_OUTPUT_FILE_16":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/connectivity.framework","SCRIPT_OUTPUT_FILE_17":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/contacts_service.framework","SCRIPT_OUTPUT_FILE_18":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/esys_flutter_share.framework","SCRIPT_OUTPUT_FILE_19":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_appavailability.framework","SCRIPT_OUTPUT_FILE_2":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Flutter.framework","SCRIPT_OUTPUT_FILE_20":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_idensic_mobile_sdk_plugin.framework","SCRIPT_OUTPUT_FILE_21":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_local_notifications.framework","SCRIPT_OUTPUT_FILE_22":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_secure_storage.framework","SCRIPT_OUTPUT_FILE_23":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_share_me.framework","SCRIPT_OUTPUT_FILE_24":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/fluttertoast.framework","SCRIPT_OUTPUT_FILE_25":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/grpcpp.framework","SCRIPT_OUTPUT_FILE_26":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/grpc.framework","SCRIPT_OUTPUT_FILE_27":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_crop.framework","SCRIPT_OUTPUT_FILE_28":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_gallery_saver.framework","SCRIPT_OUTPUT_FILE_29":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_picker.framework","SCRIPT_OUTPUT_FILE_3":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GTMSessionFetcher.framework","SCRIPT_OUTPUT_FILE_30":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/keyboard_visibility.framework","SCRIPT_OUTPUT_FILE_31":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/launch_review.framework","SCRIPT_OUTPUT_FILE_32":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/leveldb.framework","SCRIPT_OUTPUT_FILE_33":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/local_auth.framework","SCRIPT_OUTPUT_FILE_34":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/nanopb.framework","SCRIPT_OUTPUT_FILE_35":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/open_appstore.framework","SCRIPT_OUTPUT_FILE_36":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/package_info.framework","SCRIPT_OUTPUT_FILE_37":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/path_provider.framework","SCRIPT_OUTPUT_FILE_38":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/permission.framework","SCRIPT_OUTPUT_FILE_39":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/qrcode_flutter.framework","SCRIPT_OUTPUT_FILE_4":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleAPIClientForREST.framework","SCRIPT_OUTPUT_FILE_40":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/share.framework","SCRIPT_OUTPUT_FILE_41":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/shared_preferences.framework","SCRIPT_OUTPUT_FILE_42":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/simple_image_crop.framework","SCRIPT_OUTPUT_FILE_43":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sms_autofill.framework","SCRIPT_OUTPUT_FILE_44":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sms_receiver.framework","SCRIPT_OUTPUT_FILE_45":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sqflite.framework","SCRIPT_OUTPUT_FILE_46":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/store_redirect.framework","SCRIPT_OUTPUT_FILE_47":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/url_launcher.framework","SCRIPT_OUTPUT_FILE_48":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/video_player.framework","SCRIPT_OUTPUT_FILE_49":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/webview_flutter.framework","SCRIPT_OUTPUT_FILE_5":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleToolboxForMac.framework","SCRIPT_OUTPUT_FILE_6":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleUtilities.framework","SCRIPT_OUTPUT_FILE_7":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/IdensicMobileSDK.framework","SCRIPT_OUTPUT_FILE_8":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/IdensicMobileSDK_Liveness3D.framework","SCRIPT_OUTPUT_FILE_9":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBLPromises.framework","SCRIPT_OUTPUT_FILE_COUNT":"50","SCRIPT_OUTPUT_FILE_LIST_COUNT":"0","SDKROOT":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.1.sdk","SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.1.sdk","SDK_DIR_iphonesimulator14_1":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.1.sdk","SDK_NAME":"iphonesimulator14.1","SDK_NAMES":"iphonesimulator14.1","SDK_PRODUCT_BUILD_VERSION":"18A8394","SDK_VERSION":"14.1","SDK_VERSION_ACTUAL":"140100","SDK_VERSION_MAJOR":"140000","SDK_VERSION_MINOR":"100","SED":"/usr/bin/sed","SEPARATE_STRIP":"NO","SEPARATE_SYMBOL_EDIT":"NO","SET_DIR_MODE_OWNER_GROUP":"YES","SET_FILE_MODE_OWNER_GROUP":"NO","SHALLOW_BUNDLE":"YES","SHARED_DERIVED_FILE_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/DerivedSources","SHARED_FRAMEWORKS_FOLDER_PATH":"Runner.app/SharedFrameworks","SHARED_PRECOMPS_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/SharedPrecompiledHeaders","SHARED_SUPPORT_FOLDER_PATH":"Runner.app/SharedSupport","SKIP_INSTALL":"NO","SOURCE_ROOT":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","SRCROOT":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","STRINGS_FILE_OUTPUT_ENCODING":"binary","STRIP_BITCODE_FROM_COPIED_FILES":"NO","STRIP_INSTALLED_PRODUCT":"YES","STRIP_STYLE":"all","STRIP_SWIFT_SYMBOLS":"YES","SUPPORTED_DEVICE_FAMILIES":"1,2","SUPPORTED_PLATFORMS":"iphoneos iphonesimulator","SUPPORTS_TEXT_BASED_API":"NO","SWIFT_OBJC_BRIDGING_HEADER":"Runner/Runner-Bridging-Header.h","SWIFT_OPTIMIZATION_LEVEL":"-Onone","SWIFT_PLATFORM_TARGET_PREFIX":"ios","SWIFT_RESPONSE_FILE_PATH_normal_x86_64":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList","SWIFT_VERSION":"5.0","SYMROOT":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios","SYSTEM_ADMIN_APPS_DIR":"/Applications/Utilities","SYSTEM_APPS_DIR":"/Applications","SYSTEM_CORE_SERVICES_DIR":"/System/Library/CoreServices","SYSTEM_DEMOS_DIR":"/Applications/Extras","SYSTEM_DEVELOPER_APPS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","SYSTEM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","SYSTEM_DEVELOPER_DEMOS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities/Built Examples","SYSTEM_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","SYSTEM_DEVELOPER_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library","SYSTEM_DEVELOPER_GRAPHICS_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Graphics Tools","SYSTEM_DEVELOPER_JAVA_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Java Tools","SYSTEM_DEVELOPER_PERFORMANCE_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Performance Tools","SYSTEM_DEVELOPER_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes","SYSTEM_DEVELOPER_TOOLS":"/Applications/Xcode.app/Contents/Developer/Tools","SYSTEM_DEVELOPER_TOOLS_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/documentation/DeveloperTools","SYSTEM_DEVELOPER_TOOLS_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes/DeveloperTools","SYSTEM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","SYSTEM_DEVELOPER_UTILITIES_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities","SYSTEM_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","SYSTEM_DOCUMENTATION_DIR":"/Library/Documentation","SYSTEM_KEXT_INSTALL_PATH":"/System/Library/Extensions","SYSTEM_LIBRARY_DIR":"/System/Library","TAPI_VERIFY_MODE":"ErrorsOnly","TARGETED_DEVICE_FAMILY":"1,2","TARGETNAME":"Runner","TARGET_BUILD_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator","TARGET_DEVICE_IDENTIFIER":"41B25245-6594-41DE-9511-845A598DA691","TARGET_DEVICE_MODEL":"iPod9,1","TARGET_DEVICE_OS_VERSION":"14.1","TARGET_NAME":"Runner","TARGET_TEMP_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_FILES_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_FILE_DIR":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_ROOT":"/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios","TEST_FRAMEWORK_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Library/Frameworks /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.1.sdk/Developer/Library/Frameworks","TEST_LIBRARY_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/lib","TOOLCHAINS":"com.apple.dt.toolchain.XcodeDefault","TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","TRACK_WIDGET_CREATION":"true","TREAT_MISSING_BASELINES_AS_TEST_FAILURES":"NO","TREE_SHAKE_ICONS":"false","TeamIdentifierPrefix":"HJAN5D475T.","UID":"504","UNLOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app","UNSTRIPPED_PRODUCT":"NO","USER":"oil","USER_APPS_DIR":"/Users/<USER>/Applications","USER_LIBRARY_DIR":"/Users/<USER>/Library","USE_DYNAMIC_NO_PIC":"YES","USE_HEADERMAP":"YES","USE_HEADER_SYMLINKS":"NO","USE_LLVM_TARGET_TRIPLES":"YES","USE_LLVM_TARGET_TRIPLES_FOR_CLANG":"YES","USE_LLVM_TARGET_TRIPLES_FOR_LD":"YES","USE_LLVM_TARGET_TRIPLES_FOR_TAPI":"YES","VALIDATE_DEVELOPMENT_ASSET_PATHS":"YES_ERROR","VALIDATE_PRODUCT":"NO","VALIDATE_WORKSPACE":"YES_ERROR","VALID_ARCHS":"arm64 arm64e i386 x86_64","VERBOSE_PBXCP":"NO","VERSIONING_SYSTEM":"apple-generic","VERSIONPLIST_PATH":"Runner.app/version.plist","VERSION_INFO_BUILDER":"oil","VERSION_INFO_FILE":"Runner_vers.c","VERSION_INFO_STRING":"\"@(#)PROGRAM:Runner  PROJECT:Runner-164\"","WRAPPER_EXTENSION":"app","WRAPPER_NAME":"Runner.app","WRAPPER_SUFFIX":".app","WRAP_ASSET_PACKS_IN_SEPARATE_DIRECTORIES":"NO","XCODE_APP_SUPPORT_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Xcode","XCODE_PRODUCT_BUILD_VERSION":"12A7403","XCODE_VERSION_ACTUAL":"1210","XCODE_VERSION_MAJOR":"1200","XCODE_VERSION_MINOR":"1210","XPCSERVICES_FOLDER_PATH":"Runner.app/XPCServices","YACC":"yacc","arch":"undefined_arch","variant":"normal"},"allow-missing-inputs":true,"working-directory":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","control-enabled":false,"signature":"2af51e9425b851554ede5655145ecd3e"}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:ProcessInfoPlistFile /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Info.plist /Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Runner/Info.plist": {"tool":"info-plist-processor","description":"ProcessInfoPlistFile /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Info.plist /Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Runner/Info.plist","inputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/ios/Runner/Info.plist","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen-SBPartialInfo.plist","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main-SBPartialInfo.plist","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/assetcatalog_generated_info.plist","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Info.plist"]}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:ProcessProductPackaging  /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent": {"tool":"process-product-entitlements","description":"ProcessProductPackaging  /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent","inputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Entitlements-Simulated.plist","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--XCFrameworkTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent"]}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:ProcessProductPackaging  /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app.xcent": {"tool":"process-product-entitlements","description":"ProcessProductPackaging  /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app.xcent","inputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Entitlements.plist","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--XCFrameworkTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app.xcent"]}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:RegisterExecutionPolicyException /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app": {"tool":"register-execution-policy-exception","description":"RegisterExecutionPolicyException /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app","inputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--Barrier-CodeSign>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["<RegisterExecutionPolicyException /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app>"]}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:Touch /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app": {"tool":"shell","description":"Touch /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app","inputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--Barrier-Validate>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["<Touch /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app>"],"args":["/usr/bin/touch","-c","/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app"],"env":{},"working-directory":"/Users/<USER>/Dabzshot/LikeWalletPharse3/ios","signature":"b887f259fe74be7e99142b4cf5e3e8f0"}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Entitlements-Simulated.plist": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Entitlements-Simulated.plist","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--XCFrameworkTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Entitlements-Simulated.plist"]}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Entitlements.plist": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Entitlements.plist","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--XCFrameworkTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Entitlements.plist"]}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c"]}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json"]}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList"]}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList"]}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-non-framework-target-headers.hmap": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-non-framework-target-headers.hmap","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-non-framework-target-headers.hmap"]}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-target-headers.hmap": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-target-headers.hmap","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-target-headers.hmap"]}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-generated-files.hmap": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-generated-files.hmap","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-generated-files.hmap"]}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-own-target-headers.hmap": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-own-target-headers.hmap","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-own-target-headers.hmap"]}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-project-headers.hmap": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-project-headers.hmap","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-project-headers.hmap"]}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.hmap": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.hmap","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.hmap"]}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-328736DAE7F587435A6E60E5.sh": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-328736DAE7F587435A6E60E5.sh","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-328736DAE7F587435A6E60E5.sh"]}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3759590B8F78A0B90ADCF815.sh": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3759590B8F78A0B90ADCF815.sh","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase6-thin-binary>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3759590B8F78A0B90ADCF815.sh"]}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase5-copy-files>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh"]}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--phase0--cp--check-pods-manifest-lock>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh"]}
  "target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/all-product-headers.yaml": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/all-product-headers.yaml","inputs":["<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-b97f721a235acf137390128f1b58ef8c88a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/Dabzshot/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/all-product-headers.yaml"]}

