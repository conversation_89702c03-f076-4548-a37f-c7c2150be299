client:
  name: basic
  version: 0
  file-system: default

targets:
  "": ["<all>"]

nodes:
  "/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios": {"is-mutated":true}
  "/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos": {"is-mutated":true}
  "/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app": {"is-mutated":true}
  "<TRIGGER: MkDir /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app>": {"is-command-timestamp":true}

commands:
  "::CreateBuildDirectory /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios": {"tool":"create-build-directory","description":"CreateBuildDirectory /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","inputs":[],"outputs":["<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios>","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios"]}
  "::CreateBuildDirectory /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos": {"tool":"create-build-directory","description":"CreateBuildDirectory /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios"],"outputs":["<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos>","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos"]}
  "<all>": {"tool":"phony","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--end>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--modules-ready>"],"outputs":["<all>"]}
  "<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-Debug-iphoneos-arm64-build-headers-stale-file-removal>": {"tool":"stale-file-removal","expectedOutputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/assetcatalog_generated_info.plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Assets.car","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/GeneratedPluginRegistrant.o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner_vers.o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/LaunchScreen-SBPartialInfo.plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/Main.storyboardc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/Main-SBPartialInfo.plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/AppDelegate.o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftmodule","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftsourceinfo","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner-Swift.h","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftdoc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/AppFrameworkInfo.plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/GoogleService-Info.plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Runner","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Base.lproj/Main.storyboardc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner-Swift.h","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64-apple-ios.swiftdoc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64.swiftdoc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64-apple-ios.swiftmodule","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64.swiftmodule","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/arm64.swiftsourceinfo","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Pods-Runner-checkManifestLockResult.txt","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/openssl_grpc.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/DTTJailbreakDetection.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FMDB.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseAuth.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseCore.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseCoreDiagnostics.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseCrashlytics.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseFirestore.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseInstallations.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseMessaging.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseStorage.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/GTMSessionFetcher.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/GoogleDataTransport.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/GoogleToolboxForMac.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/GoogleUtilities.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/MTBBarcodeScanner.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FBLPromises.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/Protobuf.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/Reachability.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/Toast.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/absl.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/app_settings.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/camera.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/connectivity.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/contacts_service.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_appavailability.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_idensic_mobile_sdk_plugin.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_jailbreak_detection.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_local_notifications.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_native_timezone.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_secure_storage.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/fluttertoast.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/grpcpp.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/grpc.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/image_crop.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/image_gallery_saver.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/image_picker.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/keyboard_visibility.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/launch_review.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/leveldb.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/local_auth.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/nanopb.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/native_device_orientation.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/open_appstore.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/package_info.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/path_provider.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/permission.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/qrcode_flutter.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/share.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/shared_preferences.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/simple_image_crop.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/sms_autofill.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/sms_receiver.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/social_share_plugin.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/sqflite.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/store_redirect.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/uni_links.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/url_launcher.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/video_player.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/webview_flutter.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FBAEMKit.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FBSDKCoreKit.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FBSDKCoreKit_Basics.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FBSDKShareKit.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/IdensicMobileSDK.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Info.plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner_vers.c","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner-OutputFileMap.json","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.LinkFileList","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.SwiftFileList","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-all-non-framework-target-headers.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-all-target-headers.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-generated-files.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-own-target-headers.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-project-headers.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-328736DAE7F587435A6E60E5.sh","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-3759590B8F78A0B90ADCF815.sh","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-9740EEB61CF901F6004384FC.sh","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/all-product-headers.yaml"],"roots":["/tmp/Runner.dst","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-Debug-iphoneos-arm64-build-headers-stale-file-removal>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangeAlternatePermissions": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangePermissions>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--will-sign>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangeAlternatePermissions>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangePermissions": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-StripSymbols>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--will-sign>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangePermissions>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-CodeSign": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangeAlternatePermissions>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--will-sign>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-CodeSign>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-CopyAside": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ProductPostprocessingTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--will-sign>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-CopyAside>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-RegisterExecutionPolicyException": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-CodeSign>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--will-sign>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","<RegisterExecutionPolicyException /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-RegisterExecutionPolicyException>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-RegisterProduct": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-Validate>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--will-sign>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","<Touch /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-RegisterProduct>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-StripSymbols": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-CopyAside>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--will-sign>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-StripSymbols>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-Validate": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-RegisterExecutionPolicyException>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--will-sign>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","<Validate /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-Validate>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--CopySwiftPackageResourcesTaskProducer": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--CopySwiftPackageResourcesTaskProducer>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--DocumentationTaskProducer": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--DocumentationTaskProducer>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ProductStructureTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-all-non-framework-target-headers.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-all-target-headers.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-generated-files.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-own-target-headers.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-project-headers.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/all-product-headers.yaml"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--InfoPlistTaskProducer": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Info.plist"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--InfoPlistTaskProducer>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ProductPostprocessingTaskProducer": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--package-copy-files-phase>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--InfoPlistTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--VersionPlistTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--SanitizerTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--SwiftStandardLibrariesTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--SwiftFrameworkABICheckerTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--SwiftABIBaselineGenerationTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--StubBinaryTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--TestTargetTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--TestHostTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--CopySwiftPackageResourcesTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--DocumentationTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ProductPostprocessingTaskProducer>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ProductStructureTaskProducer": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--start>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","<MkDir /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app>","<MkDir /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ProductStructureTaskProducer>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--SanitizerTaskProducer": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--SanitizerTaskProducer>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--StubBinaryTaskProducer": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--StubBinaryTaskProducer>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--SwiftABIBaselineGenerationTaskProducer": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--package-copy-files-phase>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--SwiftABIBaselineGenerationTaskProducer>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--SwiftFrameworkABICheckerTaskProducer": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--package-copy-files-phase>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--SwiftFrameworkABICheckerTaskProducer>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--SwiftStandardLibrariesTaskProducer": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--package-copy-files-phase>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","<CopySwiftStdlib /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--SwiftStandardLibrariesTaskProducer>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--TestHostTaskProducer": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--TestHostTaskProducer>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--TestTargetPostprocessingTaskProducer": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ProductPostprocessingTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--TestTargetPostprocessingTaskProducer>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--TestTargetTaskProducer": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--TestTargetTaskProducer>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--VersionPlistTaskProducer": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--VersionPlistTaskProducer>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-Debug-iphoneos-arm64-build-headers-stale-file-removal>","<CreateBuildDirectory-/tmp/Runner.dst>","<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios>","<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios>","<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--end": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangeAlternatePermissions>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangePermissions>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-CodeSign>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-CopyAside>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-RegisterExecutionPolicyException>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-RegisterProduct>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-StripSymbols>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-Validate>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--CopySwiftPackageResourcesTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--DocumentationTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--InfoPlistTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ProductPostprocessingTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ProductStructureTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--SanitizerTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--StubBinaryTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--SwiftABIBaselineGenerationTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--SwiftFrameworkABICheckerTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--SwiftStandardLibrariesTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--TestHostTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--TestTargetPostprocessingTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--TestTargetTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--VersionPlistTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--generated-headers>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--package-copy-files-phase>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase0--cp--check-pods-manifest-lock>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase4-copy-bundle-resources>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase5-copy-files>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase6-thin-binary>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase7--cp--embed-pods-frameworks>","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/assetcatalog_generated_info.plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Assets.car","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/GeneratedPluginRegistrant.o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner_vers.o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/LaunchScreen-SBPartialInfo.plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/Main.storyboardc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/Main-SBPartialInfo.plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/AppDelegate.o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftmodule","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftsourceinfo","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner-Swift.h","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftdoc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/AppFrameworkInfo.plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/GoogleService-Info.plist","<CopySwiftStdlib /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app>","<Linked Binary /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Runner>","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Base.lproj/Main.storyboardc","<MkDir /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app>","<MkDir /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks>","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner-Swift.h","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64-apple-ios.swiftdoc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64.swiftdoc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64-apple-ios.swiftmodule","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64.swiftmodule","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/arm64.swiftsourceinfo","<execute-shell-script-18c1723432283e0cc55f10a6dcfd9e029eb60ff613d36b3b9942d1a6e1e7c6aa-target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49->","<execute-shell-script-18c1723432283e0cc55f10a6dcfd9e02f1eee2015e8ff5ebcd27678f788c2826-target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49->","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Pods-Runner-checkManifestLockResult.txt","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/openssl_grpc.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/DTTJailbreakDetection.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FMDB.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseAuth.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseCore.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseCoreDiagnostics.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseCrashlytics.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseFirestore.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseInstallations.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseMessaging.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseStorage.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/GTMSessionFetcher.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/GoogleDataTransport.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/GoogleToolboxForMac.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/GoogleUtilities.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/MTBBarcodeScanner.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FBLPromises.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/Protobuf.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/Reachability.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/Toast.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/absl.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/app_settings.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/camera.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/connectivity.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/contacts_service.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_appavailability.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_idensic_mobile_sdk_plugin.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_jailbreak_detection.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_local_notifications.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_native_timezone.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_secure_storage.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/fluttertoast.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/grpcpp.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/grpc.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/image_crop.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/image_gallery_saver.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/image_picker.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/keyboard_visibility.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/launch_review.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/leveldb.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/local_auth.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/nanopb.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/native_device_orientation.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/open_appstore.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/package_info.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/path_provider.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/permission.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/qrcode_flutter.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/share.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/shared_preferences.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/simple_image_crop.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/sms_autofill.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/sms_receiver.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/social_share_plugin.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/sqflite.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/store_redirect.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/uni_links.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/url_launcher.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/video_player.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/webview_flutter.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FBAEMKit.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FBSDKCoreKit.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FBSDKCoreKit_Basics.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FBSDKShareKit.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/IdensicMobileSDK.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Info.plist","<RegisterExecutionPolicyException /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app>","<Touch /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app>","<Validate /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app>","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner_vers.c","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner-OutputFileMap.json","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.LinkFileList","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.SwiftFileList","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-all-non-framework-target-headers.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-all-target-headers.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-generated-files.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-own-target-headers.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-project-headers.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-328736DAE7F587435A6E60E5.sh","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-3759590B8F78A0B90ADCF815.sh","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-9740EEB61CF901F6004384FC.sh","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/all-product-headers.yaml"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--end>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-Debug-iphoneos-arm64-build-headers-stale-file-removal>","<CreateBuildDirectory-/tmp/Runner.dst>","<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios>","<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios>","<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--generated-headers": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/AppDelegate.o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftmodule","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftsourceinfo","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner-Swift.h","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftdoc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner-Swift.h"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--generated-headers>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--immediate": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-Debug-iphoneos-arm64-build-headers-stale-file-removal>","<CreateBuildDirectory-/tmp/Runner.dst>","<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios>","<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios>","<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--immediate>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--modules-ready": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/AppDelegate.o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftmodule","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftsourceinfo","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner-Swift.h","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftdoc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner-Swift.h","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64-apple-ios.swiftdoc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64.swiftdoc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64-apple-ios.swiftmodule","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64.swiftmodule","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/arm64.swiftsourceinfo","<execute-shell-script-18c1723432283e0cc55f10a6dcfd9e029eb60ff613d36b3b9942d1a6e1e7c6aa-target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49->","<execute-shell-script-18c1723432283e0cc55f10a6dcfd9e02f1eee2015e8ff5ebcd27678f788c2826-target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49->","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Pods-Runner-checkManifestLockResult.txt","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/openssl_grpc.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/DTTJailbreakDetection.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FMDB.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseAuth.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseCore.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseCoreDiagnostics.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseCrashlytics.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseFirestore.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseInstallations.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseMessaging.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseStorage.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/GTMSessionFetcher.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/GoogleDataTransport.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/GoogleToolboxForMac.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/GoogleUtilities.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/MTBBarcodeScanner.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FBLPromises.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/Protobuf.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/Reachability.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/Toast.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/absl.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/app_settings.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/camera.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/connectivity.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/contacts_service.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_appavailability.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_idensic_mobile_sdk_plugin.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_jailbreak_detection.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_local_notifications.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_native_timezone.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_secure_storage.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/fluttertoast.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/grpcpp.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/grpc.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/image_crop.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/image_gallery_saver.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/image_picker.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/keyboard_visibility.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/launch_review.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/leveldb.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/local_auth.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/nanopb.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/native_device_orientation.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/open_appstore.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/package_info.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/path_provider.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/permission.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/qrcode_flutter.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/share.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/shared_preferences.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/simple_image_crop.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/sms_autofill.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/sms_receiver.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/social_share_plugin.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/sqflite.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/store_redirect.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/uni_links.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/url_launcher.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/video_player.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/webview_flutter.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FBAEMKit.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FBSDKCoreKit.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FBSDKCoreKit_Basics.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FBSDKShareKit.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/IdensicMobileSDK.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-328736DAE7F587435A6E60E5.sh","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-3759590B8F78A0B90ADCF815.sh","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-9740EEB61CF901F6004384FC.sh"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--modules-ready>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--package-copy-files-phase": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase7--cp--embed-pods-frameworks>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--package-copy-files-phase>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase0--cp--check-pods-manifest-lock": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Pods-Runner-checkManifestLockResult.txt","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-328736DAE7F587435A6E60E5.sh"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase0--cp--check-pods-manifest-lock>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase0--cp--check-pods-manifest-lock>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","<execute-shell-script-18c1723432283e0cc55f10a6dcfd9e029eb60ff613d36b3b9942d1a6e1e7c6aa-target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49->","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-9740EEB61CF901F6004384FC.sh"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/GeneratedPluginRegistrant.o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner_vers.o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/AppDelegate.o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftmodule","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftsourceinfo","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner-Swift.h","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftdoc","<Linked Binary /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Runner>","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner-Swift.h","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64-apple-ios.swiftdoc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64.swiftdoc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64-apple-ios.swiftmodule","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64.swiftmodule","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/arm64.swiftsourceinfo","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner_vers.c","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner-OutputFileMap.json","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.LinkFileList","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.SwiftFileList"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase4-copy-bundle-resources": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/assetcatalog_generated_info.plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Assets.car","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/LaunchScreen-SBPartialInfo.plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/Main.storyboardc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/Main-SBPartialInfo.plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/AppFrameworkInfo.plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/GoogleService-Info.plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Base.lproj/Main.storyboardc"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase4-copy-bundle-resources>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase5-copy-files": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase4-copy-bundle-resources>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase5-copy-files>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase6-thin-binary": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase5-copy-files>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","<execute-shell-script-18c1723432283e0cc55f10a6dcfd9e02f1eee2015e8ff5ebcd27678f788c2826-target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49->","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase6-thin-binary>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase7--cp--embed-pods-frameworks": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase6-thin-binary>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/openssl_grpc.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/DTTJailbreakDetection.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FMDB.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseAuth.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseCore.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseCoreDiagnostics.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseCrashlytics.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseFirestore.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseInstallations.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseMessaging.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseStorage.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/GTMSessionFetcher.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/GoogleDataTransport.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/GoogleToolboxForMac.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/GoogleUtilities.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/MTBBarcodeScanner.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FBLPromises.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/Protobuf.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/Reachability.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/Toast.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/absl.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/app_settings.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/camera.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/connectivity.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/contacts_service.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_appavailability.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_idensic_mobile_sdk_plugin.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_jailbreak_detection.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_local_notifications.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_native_timezone.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_secure_storage.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/fluttertoast.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/grpcpp.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/grpc.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/image_crop.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/image_gallery_saver.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/image_picker.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/keyboard_visibility.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/launch_review.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/leveldb.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/local_auth.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/nanopb.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/native_device_orientation.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/open_appstore.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/package_info.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/path_provider.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/permission.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/qrcode_flutter.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/share.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/shared_preferences.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/simple_image_crop.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/sms_autofill.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/sms_receiver.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/social_share_plugin.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/sqflite.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/store_redirect.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/uni_links.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/url_launcher.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/video_player.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/webview_flutter.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FBAEMKit.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FBSDKCoreKit.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FBSDKCoreKit_Basics.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FBSDKShareKit.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/IdensicMobileSDK.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-3759590B8F78A0B90ADCF815.sh"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase7--cp--embed-pods-frameworks>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--unsigned-product-ready": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/GeneratedPluginRegistrant.o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner_vers.o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/AppDelegate.o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftmodule","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftsourceinfo","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner-Swift.h","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftdoc","<CopySwiftStdlib /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app>","<Linked Binary /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Runner>","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner-Swift.h","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64-apple-ios.swiftdoc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64.swiftdoc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64-apple-ios.swiftmodule","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64.swiftmodule","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/arm64.swiftsourceinfo","<execute-shell-script-18c1723432283e0cc55f10a6dcfd9e029eb60ff613d36b3b9942d1a6e1e7c6aa-target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49->","<execute-shell-script-18c1723432283e0cc55f10a6dcfd9e02f1eee2015e8ff5ebcd27678f788c2826-target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49->","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Pods-Runner-checkManifestLockResult.txt","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/openssl_grpc.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/DTTJailbreakDetection.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FMDB.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseAuth.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseCore.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseCoreDiagnostics.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseCrashlytics.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseFirestore.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseInstallations.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseMessaging.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseStorage.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/GTMSessionFetcher.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/GoogleDataTransport.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/GoogleToolboxForMac.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/GoogleUtilities.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/MTBBarcodeScanner.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FBLPromises.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/Protobuf.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/Reachability.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/Toast.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/absl.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/app_settings.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/camera.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/connectivity.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/contacts_service.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_appavailability.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_idensic_mobile_sdk_plugin.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_jailbreak_detection.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_local_notifications.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_native_timezone.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_secure_storage.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/fluttertoast.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/grpcpp.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/grpc.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/image_crop.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/image_gallery_saver.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/image_picker.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/keyboard_visibility.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/launch_review.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/leveldb.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/local_auth.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/nanopb.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/native_device_orientation.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/open_appstore.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/package_info.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/path_provider.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/permission.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/qrcode_flutter.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/share.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/shared_preferences.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/simple_image_crop.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/sms_autofill.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/sms_receiver.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/social_share_plugin.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/sqflite.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/store_redirect.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/uni_links.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/url_launcher.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/video_player.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/webview_flutter.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FBAEMKit.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FBSDKCoreKit.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FBSDKCoreKit_Basics.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FBSDKShareKit.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/IdensicMobileSDK.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner_vers.c","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner-OutputFileMap.json","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.LinkFileList","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.SwiftFileList","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-328736DAE7F587435A6E60E5.sh","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-3759590B8F78A0B90ADCF815.sh","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-9740EEB61CF901F6004384FC.sh"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--unsigned-product-ready>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--will-sign": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--unsigned-product-ready>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--will-sign>"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:CompileAssetCatalog /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/Assets.xcassets": {"tool":"shell","description":"CompileAssetCatalog /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/Assets.xcassets","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/Assets.xcassets/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/assetcatalog_generated_info.plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Assets.car"],"args":["/Applications/Xcode.app/Contents/Developer/usr/bin/actool","--output-format","human-readable-text","--notices","--warnings","--export-dependency-info","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/assetcatalog_dependencies","--output-partial-info-plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/assetcatalog_generated_info.plist","--app-icon","AppIcon","--compress-pngs","--enable-on-demand-resources","YES","--development-region","en","--target-device","iphone","--target-device","ipad","--minimum-deployment-target","12.0","--platform","iphoneos","--compile","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/Assets.xcassets"],"env":{},"working-directory":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","control-enabled":false,"deps":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/assetcatalog_dependencies"],"deps-style":"dependency-info","signature":"f15ccc9288f27bf04907203be7f6e885"}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:CompileC /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/GeneratedPluginRegistrant.o /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/GeneratedPluginRegistrant.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool":"shell","description":"CompileC /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/GeneratedPluginRegistrant.o /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/GeneratedPluginRegistrant.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/GeneratedPluginRegistrant.m","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--generated-headers>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/GeneratedPluginRegistrant.o"],"args":["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang","-x","objective-c","-target","arm64-apple-ios12.0","-fmessage-length=0","-fdiagnostics-show-note-include-stack","-fmacro-backtrace-limit=0","-std=gnu99","-fobjc-arc","-fmodules","-gmodules","-fmodules-cache-path=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex","-fmodules-prune-interval=86400","-fmodules-prune-after=345600","-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation","-fmodules-validate-once-per-build-session","-Wnon-modular-include-in-framework-module","-Werror=non-modular-include-in-framework-module","-Wno-trigraphs","-fpascal-strings","-O0","-fno-common","-Wno-missing-field-initializers","-Wno-missing-prototypes","-Werror=return-type","-Wunreachable-code","-Wno-implicit-atomic-properties","-Werror=deprecated-objc-isa-usage","-Wno-objc-interface-ivars","-Werror=objc-root-class","-Wno-arc-repeated-use-of-weak","-Wimplicit-retain-self","-Wduplicate-method-match","-Wno-missing-braces","-Wparentheses","-Wswitch","-Wunused-function","-Wno-unused-label","-Wno-unused-parameter","-Wunused-variable","-Wunused-value","-Wempty-body","-Wuninitialized","-Wconditional-uninitialized","-Wno-unknown-pragmas","-Wno-shadow","-Wno-four-char-constants","-Wno-conversion","-Wconstant-conversion","-Wint-conversion","-Wbool-conversion","-Wenum-conversion","-Wno-float-conversion","-Wnon-literal-null-conversion","-Wobjc-literal-conversion","-Wshorten-64-to-32","-Wpointer-sign","-Wno-newline-eof","-Wno-selector","-Wno-strict-selector-match","-Wundeclared-selector","-Wdeprecated-implementations","-DDEBUG=1","-DCOCOAPODS=1","-DDEBUG=1","-DGPB_USE_PROTOBUF_FRAMEWORK_IMPORTS=1","-DDEBUG=1","-DPB_FIELD_32BIT=1","-DPB_NO_PACKED_STRUCTS=1","-DPB_ENABLE_MALLOC=1","-DOBJC_OLD_DISPATCH_PROTOTYPES=0","-isysroot","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS15.0.sdk","-fstrict-aliasing","-Wprotocol","-Wdeprecated-declarations","-g","-Wno-sign-conversion","-Winfinite-recursion","-Wcomma","-Wblock-capture-autoreleasing","-Wstrict-prototypes","-Wno-semicolon-before-method-body","-index-store-path","/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-hbmtdpxidruszpaaeuveldpjuspm/Index/DataStore","-iquote","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-generated-files.hmap","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-own-target-headers.hmap","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-all-target-headers.hmap","-iquote","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-project-headers.hmap","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/include","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/BoringSSL-GRPC/openssl_grpc.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/DTTJailbreakDetection/DTTJailbreakDetection.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FMDB/FMDB.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseAuth/FirebaseAuth.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCore/FirebaseCore.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCoreDiagnostics/FirebaseCoreDiagnostics.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCrashlytics/FirebaseCrashlytics.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseFirestore/FirebaseFirestore.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseInstallations/FirebaseInstallations.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseMessaging/FirebaseMessaging.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseStorage/FirebaseStorage.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GTMSessionFetcher/GTMSessionFetcher.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleDataTransport/GoogleDataTransport.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleToolboxForMac/GoogleToolboxForMac.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleUtilities/GoogleUtilities.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleUtilitiesComponents/GoogleUtilitiesComponents.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/MTBBarcodeScanner/MTBBarcodeScanner.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/PromisesObjC/FBLPromises.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Protobuf/Protobuf.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Reachability/Reachability.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Toast/Toast.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/abseil/absl.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/app_settings/app_settings.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/camera/camera.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/cloud_firestore/cloud_firestore.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/connectivity/connectivity.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/contacts_service/contacts_service.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_analytics/firebase_analytics.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_auth/firebase_auth.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_core/firebase_core.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_crashlytics/firebase_crashlytics.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_messaging/firebase_messaging.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_storage/firebase_storage.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_appavailability/flutter_appavailability.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_idensic_mobile_sdk_plugin/flutter_idensic_mobile_sdk_plugin.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_jailbreak_detection/flutter_jailbreak_detection.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_local_notifications/flutter_local_notifications.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_native_timezone/flutter_native_timezone.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_secure_storage/flutter_secure_storage.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/fluttertoast/fluttertoast.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/gRPC-C++/grpcpp.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/gRPC-Core/grpc.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_crop/image_crop.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_gallery_saver/image_gallery_saver.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_picker/image_picker.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/keyboard_visibility/keyboard_visibility.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/launch_review/launch_review.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/leveldb-library/leveldb.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/local_auth/local_auth.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/nanopb/nanopb.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/native_device_orientation/native_device_orientation.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/open_appstore/open_appstore.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/package_info/package_info.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/path_provider/path_provider.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/permission/permission.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/permission_handler/permission_handler.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/qr_code_scanner/qr_code_scanner.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/qrcode_flutter/qrcode_flutter.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/share/share.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/shared_preferences/shared_preferences.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/simple_image_crop/simple_image_crop.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sms_autofill/sms_autofill.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sms_receiver/sms_receiver.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/social_share_plugin/social_share_plugin.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sqflite/sqflite.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/store_redirect/store_redirect.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/uni_links/uni_links.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/url_launcher/url_launcher.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/video_player/video_player.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/webview_flutter/webview_flutter.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/Headers/Public","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/Headers/Public/Firebase","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/Headers/Public/GoogleMLKit","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/Firebase/CoreOnly/Sources","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/GoogleMLKit/MLKitCore/Sources","-I/Sources/FBLPromises/include","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources-normal/arm64","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/arm64","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/BoringSSL-GRPC","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/DTTJailbreakDetection","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FMDB","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseAuth","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCore","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCoreDiagnostics","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCrashlytics","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseFirestore","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseInstallations","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseMessaging","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseStorage","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GTMSessionFetcher","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleDataTransport","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleToolboxForMac","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleUtilities","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleUtilitiesComponents","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/MTBBarcodeScanner","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/PromisesObjC","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Protobuf","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Reachability","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Toast","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/abseil","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/app_settings","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/camera","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/cloud_firestore","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/connectivity","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/contacts_service","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_analytics","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_auth","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_core","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_crashlytics","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_messaging","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_storage","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_appavailability","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_idensic_mobile_sdk_plugin","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_jailbreak_detection","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_local_notifications","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_native_timezone","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_secure_storage","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/fluttertoast","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/gRPC-C++","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/gRPC-Core","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_crop","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_gallery_saver","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_picker","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/keyboard_visibility","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/launch_review","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/leveldb-library","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/local_auth","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/nanopb","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/native_device_orientation","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/open_appstore","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/package_info","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/path_provider","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/permission","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/permission_handler","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/qr_code_scanner","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/qrcode_flutter","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/share","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/shared_preferences","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/simple_image_crop","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sms_autofill","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sms_receiver","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/social_share_plugin","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sqflite","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/store_redirect","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/uni_links","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/url_launcher","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/video_player","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/webview_flutter","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/FBAEMKit/XCFrameworks","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/FBSDKCoreKit/XCFrameworks","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/FBSDKCoreKit_Basics/XCFrameworks","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/FBSDKShareKit/XCFrameworks","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/FirebaseAnalytics/Frameworks","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/GoogleAppMeasurement/Frameworks","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/IdensicMobileSDK","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/MLImage/Frameworks","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/MLKitBarcodeScanning/Frameworks","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/MLKitCommon/Frameworks","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/MLKitVision/Frameworks","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FBAEMKit","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FBSDKCoreKit","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FBSDKCoreKit_Basics","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FBSDKShareKit","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FirebaseAnalytics/AdIdSupport","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/GoogleAppMeasurement/AdIdSupport","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/IdensicMobileSDK/Core","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Flutter","-MMD","-MT","dependencies","-MF","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/GeneratedPluginRegistrant.d","--serialize-diagnostics","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/GeneratedPluginRegistrant.dia","-c","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/GeneratedPluginRegistrant.m","-o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/GeneratedPluginRegistrant.o"],"env":{"LANG":"en_US.US-ASCII"},"working-directory":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","deps":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/GeneratedPluginRegistrant.d"],"deps-style":"makefile","signature":"060f7964e6ba19bd03aa364db57be622"}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:CompileC /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner_vers.o /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner_vers.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler": {"tool":"shell","description":"CompileC /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner_vers.o /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner_vers.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner_vers.c","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--generated-headers>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner_vers.o"],"args":["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang","-x","c","-target","arm64-apple-ios12.0","-fmessage-length=0","-fdiagnostics-show-note-include-stack","-fmacro-backtrace-limit=0","-std=gnu99","-fmodules","-gmodules","-fmodules-cache-path=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex","-fmodules-prune-interval=86400","-fmodules-prune-after=345600","-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation","-fmodules-validate-once-per-build-session","-Wnon-modular-include-in-framework-module","-Werror=non-modular-include-in-framework-module","-Wno-trigraphs","-fpascal-strings","-O0","-fno-common","-Wno-missing-field-initializers","-Wno-missing-prototypes","-Werror=return-type","-Wunreachable-code","-Werror=deprecated-objc-isa-usage","-Werror=objc-root-class","-Wno-missing-braces","-Wparentheses","-Wswitch","-Wunused-function","-Wno-unused-label","-Wno-unused-parameter","-Wunused-variable","-Wunused-value","-Wempty-body","-Wuninitialized","-Wconditional-uninitialized","-Wno-unknown-pragmas","-Wno-shadow","-Wno-four-char-constants","-Wno-conversion","-Wconstant-conversion","-Wint-conversion","-Wbool-conversion","-Wenum-conversion","-Wno-float-conversion","-Wnon-literal-null-conversion","-Wobjc-literal-conversion","-Wshorten-64-to-32","-Wpointer-sign","-Wno-newline-eof","-DDEBUG=1","-DCOCOAPODS=1","-DDEBUG=1","-DGPB_USE_PROTOBUF_FRAMEWORK_IMPORTS=1","-DDEBUG=1","-DPB_FIELD_32BIT=1","-DPB_NO_PACKED_STRUCTS=1","-DPB_ENABLE_MALLOC=1","-isysroot","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS15.0.sdk","-fstrict-aliasing","-Wdeprecated-declarations","-g","-Wno-sign-conversion","-Winfinite-recursion","-Wcomma","-Wblock-capture-autoreleasing","-Wstrict-prototypes","-Wno-semicolon-before-method-body","-index-store-path","/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-hbmtdpxidruszpaaeuveldpjuspm/Index/DataStore","-iquote","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-generated-files.hmap","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-own-target-headers.hmap","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-all-target-headers.hmap","-iquote","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-project-headers.hmap","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/include","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/BoringSSL-GRPC/openssl_grpc.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/DTTJailbreakDetection/DTTJailbreakDetection.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FMDB/FMDB.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseAuth/FirebaseAuth.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCore/FirebaseCore.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCoreDiagnostics/FirebaseCoreDiagnostics.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCrashlytics/FirebaseCrashlytics.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseFirestore/FirebaseFirestore.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseInstallations/FirebaseInstallations.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseMessaging/FirebaseMessaging.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseStorage/FirebaseStorage.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GTMSessionFetcher/GTMSessionFetcher.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleDataTransport/GoogleDataTransport.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleToolboxForMac/GoogleToolboxForMac.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleUtilities/GoogleUtilities.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleUtilitiesComponents/GoogleUtilitiesComponents.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/MTBBarcodeScanner/MTBBarcodeScanner.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/PromisesObjC/FBLPromises.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Protobuf/Protobuf.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Reachability/Reachability.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Toast/Toast.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/abseil/absl.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/app_settings/app_settings.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/camera/camera.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/cloud_firestore/cloud_firestore.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/connectivity/connectivity.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/contacts_service/contacts_service.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_analytics/firebase_analytics.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_auth/firebase_auth.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_core/firebase_core.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_crashlytics/firebase_crashlytics.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_messaging/firebase_messaging.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_storage/firebase_storage.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_appavailability/flutter_appavailability.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_idensic_mobile_sdk_plugin/flutter_idensic_mobile_sdk_plugin.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_jailbreak_detection/flutter_jailbreak_detection.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_local_notifications/flutter_local_notifications.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_native_timezone/flutter_native_timezone.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_secure_storage/flutter_secure_storage.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/fluttertoast/fluttertoast.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/gRPC-C++/grpcpp.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/gRPC-Core/grpc.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_crop/image_crop.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_gallery_saver/image_gallery_saver.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_picker/image_picker.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/keyboard_visibility/keyboard_visibility.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/launch_review/launch_review.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/leveldb-library/leveldb.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/local_auth/local_auth.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/nanopb/nanopb.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/native_device_orientation/native_device_orientation.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/open_appstore/open_appstore.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/package_info/package_info.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/path_provider/path_provider.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/permission/permission.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/permission_handler/permission_handler.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/qr_code_scanner/qr_code_scanner.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/qrcode_flutter/qrcode_flutter.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/share/share.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/shared_preferences/shared_preferences.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/simple_image_crop/simple_image_crop.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sms_autofill/sms_autofill.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sms_receiver/sms_receiver.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/social_share_plugin/social_share_plugin.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sqflite/sqflite.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/store_redirect/store_redirect.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/uni_links/uni_links.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/url_launcher/url_launcher.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/video_player/video_player.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/webview_flutter/webview_flutter.framework/Headers","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/Headers/Public","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/Headers/Public/Firebase","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/Headers/Public/GoogleMLKit","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/Firebase/CoreOnly/Sources","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/GoogleMLKit/MLKitCore/Sources","-I/Sources/FBLPromises/include","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources-normal/arm64","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/arm64","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/BoringSSL-GRPC","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/DTTJailbreakDetection","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FMDB","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseAuth","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCore","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCoreDiagnostics","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCrashlytics","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseFirestore","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseInstallations","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseMessaging","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseStorage","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GTMSessionFetcher","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleDataTransport","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleToolboxForMac","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleUtilities","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleUtilitiesComponents","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/MTBBarcodeScanner","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/PromisesObjC","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Protobuf","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Reachability","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Toast","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/abseil","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/app_settings","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/camera","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/cloud_firestore","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/connectivity","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/contacts_service","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_analytics","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_auth","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_core","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_crashlytics","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_messaging","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_storage","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_appavailability","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_idensic_mobile_sdk_plugin","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_jailbreak_detection","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_local_notifications","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_native_timezone","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_secure_storage","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/fluttertoast","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/gRPC-C++","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/gRPC-Core","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_crop","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_gallery_saver","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_picker","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/keyboard_visibility","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/launch_review","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/leveldb-library","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/local_auth","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/nanopb","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/native_device_orientation","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/open_appstore","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/package_info","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/path_provider","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/permission","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/permission_handler","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/qr_code_scanner","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/qrcode_flutter","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/share","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/shared_preferences","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/simple_image_crop","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sms_autofill","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sms_receiver","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/social_share_plugin","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sqflite","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/store_redirect","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/uni_links","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/url_launcher","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/video_player","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/webview_flutter","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/FBAEMKit/XCFrameworks","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/FBSDKCoreKit/XCFrameworks","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/FBSDKCoreKit_Basics/XCFrameworks","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/FBSDKShareKit/XCFrameworks","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/FirebaseAnalytics/Frameworks","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/GoogleAppMeasurement/Frameworks","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/IdensicMobileSDK","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/MLImage/Frameworks","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/MLKitBarcodeScanning/Frameworks","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/MLKitCommon/Frameworks","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/MLKitVision/Frameworks","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FBAEMKit","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FBSDKCoreKit","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FBSDKCoreKit_Basics","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FBSDKShareKit","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FirebaseAnalytics/AdIdSupport","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/GoogleAppMeasurement/AdIdSupport","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/IdensicMobileSDK/Core","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Flutter","-MMD","-MT","dependencies","-MF","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner_vers.d","--serialize-diagnostics","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner_vers.dia","-c","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner_vers.c","-o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner_vers.o"],"env":{"LANG":"en_US.US-ASCII"},"working-directory":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","deps":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner_vers.d"],"deps-style":"makefile","signature":"cec451f99fe761ae96a841b04242b2b0"}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:CompileStoryboard /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/Base.lproj/LaunchScreen.storyboard": {"tool":"shell","description":"CompileStoryboard /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/Base.lproj/LaunchScreen.storyboard","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/Base.lproj/LaunchScreen.storyboard","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/LaunchScreen-SBPartialInfo.plist"],"args":["/Applications/Xcode.app/Contents/Developer/usr/bin/ibtool","--errors","--warnings","--notices","--module","Runner","--output-partial-info-plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/LaunchScreen-SBPartialInfo.plist","--auto-activate-custom-fonts","--target-device","iphone","--target-device","ipad","--minimum-deployment-target","12.0","--output-format","human-readable-text","--compilation-directory","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/Base.lproj/LaunchScreen.storyboard"],"env":{"XCODE_DEVELOPER_USR_PATH":"/Applications/Xcode.app/Contents/Developer/usr/bin/.."},"working-directory":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","control-enabled":false,"signature":"317039979d6e9a78269b4be3224af1a5"}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:CompileStoryboard /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/Base.lproj/Main.storyboard": {"tool":"shell","description":"CompileStoryboard /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/Base.lproj/Main.storyboard","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/Base.lproj/Main.storyboard","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/Main.storyboardc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/Main-SBPartialInfo.plist"],"args":["/Applications/Xcode.app/Contents/Developer/usr/bin/ibtool","--errors","--warnings","--notices","--module","Runner","--output-partial-info-plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/Main-SBPartialInfo.plist","--auto-activate-custom-fonts","--target-device","iphone","--target-device","ipad","--minimum-deployment-target","12.0","--output-format","human-readable-text","--compilation-directory","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/Base.lproj/Main.storyboard"],"env":{"XCODE_DEVELOPER_USR_PATH":"/Applications/Xcode.app/Contents/Developer/usr/bin/.."},"working-directory":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","control-enabled":false,"signature":"d52db21f13201f8dde38b8f4b4baaa2a"}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:CompileSwiftSources normal arm64 com.apple.xcode.tools.swift.compiler": {"tool":"shell","description":"CompileSwiftSources normal arm64 com.apple.xcode.tools.swift.compiler","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/AppDelegate.swift","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.SwiftFileList","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner-OutputFileMap.json","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-generated-files.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-own-target-headers.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-all-target-headers.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-project-headers.hmap","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/AppDelegate.o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftmodule","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftsourceinfo","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner-Swift.h","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftdoc"],"args":["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-incremental","-module-name","Runner","-Onone","-enable-batch-mode","-enforce-exclusivity=checked","@/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.SwiftFileList","-D","COCOAPODS","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS15.0.sdk","-target","arm64-apple-ios12.0","-g","-module-cache-path","/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex","-Xfrontend","-serialize-debugging-options","-enable-testing","-index-store-path","/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-hbmtdpxidruszpaaeuveldpjuspm/Index/DataStore","-swift-version","5","-I","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/BoringSSL-GRPC","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/DTTJailbreakDetection","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FMDB","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseAuth","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCore","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCoreDiagnostics","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCrashlytics","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseFirestore","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseInstallations","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseMessaging","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseStorage","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GTMSessionFetcher","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleDataTransport","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleToolboxForMac","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleUtilities","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleUtilitiesComponents","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/MTBBarcodeScanner","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/PromisesObjC","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Protobuf","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Reachability","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Toast","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/abseil","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/app_settings","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/camera","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/cloud_firestore","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/connectivity","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/contacts_service","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_analytics","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_auth","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_core","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_crashlytics","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_messaging","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_storage","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_appavailability","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_idensic_mobile_sdk_plugin","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_jailbreak_detection","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_local_notifications","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_native_timezone","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_secure_storage","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/fluttertoast","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/gRPC-C++","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/gRPC-Core","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_crop","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_gallery_saver","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_picker","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/keyboard_visibility","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/launch_review","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/leveldb-library","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/local_auth","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/nanopb","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/native_device_orientation","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/open_appstore","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/package_info","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/path_provider","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/permission","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/permission_handler","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/qr_code_scanner","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/qrcode_flutter","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/share","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/shared_preferences","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/simple_image_crop","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sms_autofill","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sms_receiver","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/social_share_plugin","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sqflite","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/store_redirect","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/uni_links","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/url_launcher","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/video_player","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/webview_flutter","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/FBAEMKit/XCFrameworks","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/FBSDKCoreKit/XCFrameworks","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/FBSDKCoreKit_Basics/XCFrameworks","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/FBSDKShareKit/XCFrameworks","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/FirebaseAnalytics/Frameworks","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/GoogleAppMeasurement/Frameworks","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/IdensicMobileSDK","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/MLImage/Frameworks","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/MLKitBarcodeScanning/Frameworks","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/MLKitCommon/Frameworks","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/MLKitVision/Frameworks","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FBAEMKit","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FBSDKCoreKit","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FBSDKCoreKit_Basics","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FBSDKShareKit","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FirebaseAnalytics/AdIdSupport","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/GoogleAppMeasurement/AdIdSupport","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/IdensicMobileSDK/Core","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Flutter","-parse-as-library","-c","-j8","-output-file-map","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner-OutputFileMap.json","-parseable-output","-serialize-diagnostics","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftmodule","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/swift-overrides.hmap","-Xcc","-iquote","-Xcc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-generated-files.hmap","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-own-target-headers.hmap","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-all-target-headers.hmap","-Xcc","-iquote","-Xcc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-project-headers.hmap","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/include","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/BoringSSL-GRPC/openssl_grpc.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/DTTJailbreakDetection/DTTJailbreakDetection.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FMDB/FMDB.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseAuth/FirebaseAuth.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCore/FirebaseCore.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCoreDiagnostics/FirebaseCoreDiagnostics.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCrashlytics/FirebaseCrashlytics.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseFirestore/FirebaseFirestore.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseInstallations/FirebaseInstallations.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseMessaging/FirebaseMessaging.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseStorage/FirebaseStorage.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GTMSessionFetcher/GTMSessionFetcher.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleDataTransport/GoogleDataTransport.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleToolboxForMac/GoogleToolboxForMac.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleUtilities/GoogleUtilities.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleUtilitiesComponents/GoogleUtilitiesComponents.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/MTBBarcodeScanner/MTBBarcodeScanner.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/PromisesObjC/FBLPromises.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Protobuf/Protobuf.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Reachability/Reachability.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Toast/Toast.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/abseil/absl.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/app_settings/app_settings.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/camera/camera.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/cloud_firestore/cloud_firestore.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/connectivity/connectivity.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/contacts_service/contacts_service.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_analytics/firebase_analytics.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_auth/firebase_auth.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_core/firebase_core.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_crashlytics/firebase_crashlytics.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_messaging/firebase_messaging.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_storage/firebase_storage.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_appavailability/flutter_appavailability.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_idensic_mobile_sdk_plugin/flutter_idensic_mobile_sdk_plugin.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_jailbreak_detection/flutter_jailbreak_detection.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_local_notifications/flutter_local_notifications.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_native_timezone/flutter_native_timezone.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_secure_storage/flutter_secure_storage.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/fluttertoast/fluttertoast.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/gRPC-C++/grpcpp.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/gRPC-Core/grpc.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_crop/image_crop.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_gallery_saver/image_gallery_saver.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_picker/image_picker.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/keyboard_visibility/keyboard_visibility.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/launch_review/launch_review.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/leveldb-library/leveldb.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/local_auth/local_auth.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/nanopb/nanopb.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/native_device_orientation/native_device_orientation.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/open_appstore/open_appstore.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/package_info/package_info.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/path_provider/path_provider.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/permission/permission.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/permission_handler/permission_handler.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/qr_code_scanner/qr_code_scanner.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/qrcode_flutter/qrcode_flutter.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/share/share.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/shared_preferences/shared_preferences.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/simple_image_crop/simple_image_crop.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sms_autofill/sms_autofill.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sms_receiver/sms_receiver.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/social_share_plugin/social_share_plugin.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sqflite/sqflite.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/store_redirect/store_redirect.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/uni_links/uni_links.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/url_launcher/url_launcher.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/video_player/video_player.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/webview_flutter/webview_flutter.framework/Headers","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/Headers/Public","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/Headers/Public/Firebase","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/Headers/Public/GoogleMLKit","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/Firebase/CoreOnly/Sources","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/GoogleMLKit/MLKitCore/Sources","-Xcc","-I/Sources/FBLPromises/include","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources-normal/arm64","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/arm64","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources","-Xcc","-DDEBUG=1","-Xcc","-DCOCOAPODS=1","-Xcc","-DDEBUG=1","-Xcc","-DGPB_USE_PROTOBUF_FRAMEWORK_IMPORTS=1","-Xcc","-DDEBUG=1","-Xcc","-DPB_FIELD_32BIT=1","-Xcc","-DPB_NO_PACKED_STRUCTS=1","-Xcc","-DPB_ENABLE_MALLOC=1","-emit-objc-header","-emit-objc-header-path","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner-Swift.h","-import-objc-header","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/Runner-Bridging-Header.h","-pch-output-dir","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/SharedPrecompiledHeaders","-working-directory","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios"],"env":{"DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","SDKROOT":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS15.0.sdk"},"working-directory":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","deps":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/AppDelegate.d"],"deps-style":"makefile","signature":"121a940e98797fbbf4451620ddda92f7"}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:CopyPlistFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/AppFrameworkInfo.plist /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Flutter/AppFrameworkInfo.plist": {"tool":"copy-plist","description":"CopyPlistFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/AppFrameworkInfo.plist /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Flutter/AppFrameworkInfo.plist","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Flutter/AppFrameworkInfo.plist","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/AppFrameworkInfo.plist"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:CopyPlistFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/GoogleService-Info.plist /Users/<USER>/Downloads/GoogleService-Info.plist": {"tool":"copy-plist","description":"CopyPlistFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/GoogleService-Info.plist /Users/<USER>/Downloads/GoogleService-Info.plist","inputs":["/Users/<USER>/Downloads/GoogleService-Info.plist","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/GoogleService-Info.plist"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:CopySwiftLibs /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app": {"tool":"embed-swift-stdlib","description":"CopySwiftLibs /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Runner","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--package-copy-files-phase>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["<CopySwiftStdlib /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app>"],"deps":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/SwiftStdLibToolInputDependencies.dep"}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:Ld /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Runner normal": {"tool":"shell","description":"Ld /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Runner normal","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner_vers.o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/GeneratedPluginRegistrant.o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/AppDelegate.o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.LinkFileList","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Runner","<Linked Binary /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Runner>"],"args":["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang","-target","arm64-apple-ios12.0","-isysroot","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS15.0.sdk","-L/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos","-L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos","-L/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS15.0.sdk/usr/lib/swift","-L/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Flutter","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/BoringSSL-GRPC","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/DTTJailbreakDetection","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FMDB","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseAuth","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCore","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCoreDiagnostics","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCrashlytics","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseFirestore","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseInstallations","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseMessaging","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseStorage","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GTMSessionFetcher","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleDataTransport","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleToolboxForMac","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleUtilities","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleUtilitiesComponents","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/MTBBarcodeScanner","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/PromisesObjC","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Protobuf","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Reachability","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Toast","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/abseil","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/app_settings","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/camera","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/cloud_firestore","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/connectivity","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/contacts_service","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_analytics","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_auth","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_core","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_crashlytics","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_messaging","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_storage","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_appavailability","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_idensic_mobile_sdk_plugin","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_jailbreak_detection","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_local_notifications","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_native_timezone","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_secure_storage","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/fluttertoast","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/gRPC-C++","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/gRPC-Core","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_crop","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_gallery_saver","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_picker","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/keyboard_visibility","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/launch_review","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/leveldb-library","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/local_auth","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/nanopb","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/native_device_orientation","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/open_appstore","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/package_info","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/path_provider","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/permission","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/permission_handler","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/qr_code_scanner","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/qrcode_flutter","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/share","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/shared_preferences","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/simple_image_crop","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sms_autofill","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sms_receiver","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/social_share_plugin","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sqflite","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/store_redirect","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/uni_links","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/url_launcher","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/video_player","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/webview_flutter","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/FBAEMKit/XCFrameworks","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/FBSDKCoreKit/XCFrameworks","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/FBSDKCoreKit_Basics/XCFrameworks","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/FBSDKShareKit/XCFrameworks","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/FirebaseAnalytics/Frameworks","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/GoogleAppMeasurement/Frameworks","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/IdensicMobileSDK","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/MLImage/Frameworks","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/MLKitBarcodeScanning/Frameworks","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/MLKitCommon/Frameworks","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/MLKitVision/Frameworks","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FBAEMKit","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FBSDKCoreKit","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FBSDKCoreKit_Basics","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FBSDKShareKit","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FirebaseAnalytics/AdIdSupport","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/GoogleAppMeasurement/AdIdSupport","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/IdensicMobileSDK/Core","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Flutter","-filelist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.LinkFileList","-Xlinker","-rpath","-Xlinker","/usr/lib/swift","-Xlinker","-rpath","-Xlinker","/usr/lib/swift","-Xlinker","-rpath","-Xlinker","@executable_path/Frameworks","-Xlinker","-rpath","-Xlinker","@loader_path/Frameworks","-Xlinker","-rpath","-Xlinker","@executable_path/Frameworks","-dead_strip","-Xlinker","-object_path_lto","-Xlinker","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner_lto.o","-Xlinker","-export_dynamic","-Xlinker","-no_deduplicate","-fobjc-arc","-fobjc-link-runtime","-L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos","-L/usr/lib/swift","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftmodule","-ObjC","-lc++","-lsqlite3","-lz","-framework","AVFoundation","-framework","Accelerate","-framework","CoreGraphics","-framework","CoreImage","-framework","CoreMedia","-framework","CoreTelephony","-framework","CoreVideo","-framework","DTTJailbreakDetection","-framework","FBAEMKit","-framework","FBLPromises","-framework","FBSDKCoreKit","-framework","FBSDKCoreKit_Basics","-framework","FBSDKShareKit","-framework","FMDB","-framework","FirebaseAnalytics","-framework","FirebaseAuth","-framework","FirebaseCore","-framework","FirebaseCoreDiagnostics","-framework","FirebaseCrashlytics","-framework","FirebaseFirestore","-framework","FirebaseInstallations","-framework","FirebaseMessaging","-framework","FirebaseStorage","-framework","Foundation","-framework","GTMSessionFetcher","-framework","GoogleAppMeasurement","-framework","GoogleDataTransport","-framework","GoogleToolboxForMac","-framework","GoogleUtilities","-framework","GoogleUtilitiesComponents","-framework","IdensicMobileSDK","-framework","LocalAuthentication","-framework","MLImage","-framework","MLKitBarcodeScanning","-framework","MLKitCommon","-framework","MLKitVision","-framework","MTBBarcodeScanner","-framework","Protobuf","-framework","QuartzCore","-framework","Reachability","-framework","SafariServices","-framework","Security","-framework","StoreKit","-framework","SystemConfiguration","-framework","Toast","-framework","UIKit","-framework","absl","-framework","app_settings","-framework","camera","-framework","cloud_firestore","-framework","connectivity","-framework","contacts_service","-framework","firebase_analytics","-framework","firebase_auth","-framework","firebase_core","-framework","firebase_crashlytics","-framework","firebase_messaging","-framework","firebase_storage","-framework","flutter_appavailability","-framework","flutter_idensic_mobile_sdk_plugin","-framework","flutter_jailbreak_detection","-framework","flutter_local_notifications","-framework","flutter_native_timezone","-framework","flutter_secure_storage","-framework","fluttertoast","-framework","grpc","-framework","grpcpp","-framework","image_crop","-framework","image_gallery_saver","-framework","image_picker","-framework","keyboard_visibility","-framework","launch_review","-framework","leveldb","-framework","local_auth","-framework","nanopb","-framework","native_device_orientation","-framework","open_appstore","-framework","openssl_grpc","-framework","package_info","-framework","path_provider","-framework","permission","-framework","permission_handler","-framework","qr_code_scanner","-framework","qrcode_flutter","-framework","share","-framework","shared_preferences","-framework","simple_image_crop","-framework","sms_autofill","-framework","sms_receiver","-framework","social_share_plugin","-framework","sqflite","-framework","store_redirect","-framework","uni_links","-framework","url_launcher","-framework","video_player","-framework","webview_flutter","-weak_framework","UserNotifications","-framework","Pods_Runner","-Xlinker","-dependency_info","-Xlinker","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner_dependency_info.dat","-o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Runner"],"env":{},"working-directory":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","deps":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner_dependency_info.dat"],"deps-style":"dependency-info","signature":"9e20be780990e35b257b5cd3ed544788"}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:LinkStoryboards": {"tool":"shell","description":"LinkStoryboards","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/Main.storyboardc","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Base.lproj/Main.storyboardc"],"args":["/Applications/Xcode.app/Contents/Developer/usr/bin/ibtool","--errors","--warnings","--notices","--module","Runner","--target-device","iphone","--target-device","ipad","--minimum-deployment-target","12.0","--output-format","human-readable-text","--link","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/Main.storyboardc"],"env":{"XCODE_DEVELOPER_USR_PATH":"/Applications/Xcode.app/Contents/Developer/usr/bin/.."},"working-directory":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","control-enabled":false,"signature":"6df707180fac534165e913ec6298e7b6"}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:MkDir /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app": {"tool":"mkdir","description":"MkDir /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--start>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app","<MkDir /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app>","<TRIGGER: MkDir /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app>"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:MkDir /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks": {"tool":"mkdir","description":"MkDir /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--start>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks","<MkDir /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks>"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:PBXCp /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner-Swift.h /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner-Swift.h": {"tool":"file-copy","description":"PBXCp /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner-Swift.h /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner-Swift.h","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner-Swift.h/","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner-Swift.h"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:PBXCp /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftdoc /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64-apple-ios.swiftdoc": {"tool":"file-copy","description":"PBXCp /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftdoc /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64-apple-ios.swiftdoc","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftdoc/","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64-apple-ios.swiftdoc"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:PBXCp /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftdoc /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64.swiftdoc": {"tool":"file-copy","description":"PBXCp /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftdoc /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64.swiftdoc","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftdoc/","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64.swiftdoc"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:PBXCp /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftmodule /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64-apple-ios.swiftmodule": {"tool":"file-copy","description":"PBXCp /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftmodule /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64-apple-ios.swiftmodule","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftmodule/","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64-apple-ios.swiftmodule"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:PBXCp /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftmodule /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64.swiftmodule": {"tool":"file-copy","description":"PBXCp /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftmodule /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64.swiftmodule","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftmodule/","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64.swiftmodule"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:PBXCp /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftsourceinfo /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo": {"tool":"file-copy","description":"PBXCp /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftsourceinfo /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftsourceinfo/","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:PBXCp /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftsourceinfo /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/arm64.swiftsourceinfo": {"tool":"file-copy","description":"PBXCp /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftsourceinfo /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/arm64.swiftsourceinfo","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftsourceinfo/","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/arm64.swiftsourceinfo"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:PhaseScriptExecution Run Script /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-9740EEB61CF901F6004384FC.sh": {"tool":"shell","description":"PhaseScriptExecution Run Script /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-9740EEB61CF901F6004384FC.sh","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-9740EEB61CF901F6004384FC.sh","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase0--cp--check-pods-manifest-lock>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["<execute-shell-script-18c1723432283e0cc55f10a6dcfd9e029eb60ff613d36b3b9942d1a6e1e7c6aa-target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49->"],"args":["/bin/sh","-c","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-9740EEB61CF901F6004384FC.sh"],"env":{"ACTION":"build","AD_HOC_CODE_SIGNING_ALLOWED":"NO","ALLOW_TARGET_PLATFORM_SPECIALIZATION":"NO","ALTERNATE_GROUP":"staff","ALTERNATE_MODE":"u+w,go-w,a+rX","ALTERNATE_OWNER":"dabzshot","ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES":"YES","ALWAYS_SEARCH_USER_PATHS":"NO","ALWAYS_USE_SEPARATE_HEADERMAPS":"NO","APPLE_INTERNAL_DEVELOPER_DIR":"/AppleInternal/Developer","APPLE_INTERNAL_DIR":"/AppleInternal","APPLE_INTERNAL_DOCUMENTATION_DIR":"/AppleInternal/Documentation","APPLE_INTERNAL_LIBRARY_DIR":"/AppleInternal/Library","APPLE_INTERNAL_TOOLS":"/AppleInternal/Developer/Tools","APPLICATION_EXTENSION_API_ONLY":"NO","APPLY_RULES_IN_COPY_FILES":"NO","APPLY_RULES_IN_COPY_HEADERS":"NO","ARCHS":"arm64","ARCHS_STANDARD":"arm64","ARCHS_STANDARD_32_64_BIT":"armv7 arm64","ARCHS_STANDARD_32_BIT":"armv7","ARCHS_STANDARD_64_BIT":"arm64","ARCHS_STANDARD_INCLUDING_64_BIT":"arm64","ARCHS_UNIVERSAL_IPHONE_OS":"armv7 arm64","ASSETCATALOG_COMPILER_APPICON_NAME":"AppIcon","AVAILABLE_PLATFORMS":"appletvos appletvsimulator driverkit iphoneos iphonesimulator macosx watchos watchsimulator","BITCODE_GENERATION_MODE":"marker","BUILD_ACTIVE_RESOURCES_ONLY":"NO","BUILD_COMPONENTS":"headers build","BUILD_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","BUILD_LIBRARY_FOR_DISTRIBUTION":"NO","BUILD_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","BUILD_STYLE":"","BUILD_VARIANTS":"normal","BUILT_PRODUCTS_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos","BUNDLE_CONTENTS_FOLDER_PATH_deep":"Contents/","BUNDLE_EXECUTABLE_FOLDER_NAME_deep":"MacOS","BUNDLE_FORMAT":"shallow","BUNDLE_FRAMEWORKS_FOLDER_PATH":"Frameworks","BUNDLE_PLUGINS_FOLDER_PATH":"PlugIns","BUNDLE_PRIVATE_HEADERS_FOLDER_PATH":"PrivateHeaders","BUNDLE_PUBLIC_HEADERS_FOLDER_PATH":"Headers","CACHE_ROOT":"/var/folders/81/hsp6b80d6fn5sq0f3xxdpfd00000gn/C/com.apple.DeveloperTools/13.1-13A1030d/Xcode","CCHROOT":"/var/folders/81/hsp6b80d6fn5sq0f3xxdpfd00000gn/C/com.apple.DeveloperTools/13.1-13A1030d/Xcode","CHMOD":"/bin/chmod","CHOWN":"/usr/sbin/chown","CLANG_ANALYZER_NONNULL":"YES","CLANG_CXX_LANGUAGE_STANDARD":"gnu++0x","CLANG_CXX_LIBRARY":"libc++","CLANG_ENABLE_MODULES":"YES","CLANG_ENABLE_OBJC_ARC":"YES","CLANG_MODULES_BUILD_SESSION_FILE":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation","CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING":"YES","CLANG_WARN_BOOL_CONVERSION":"YES","CLANG_WARN_COMMA":"YES","CLANG_WARN_CONSTANT_CONVERSION":"YES","CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS":"YES","CLANG_WARN_DIRECT_OBJC_ISA_USAGE":"YES_ERROR","CLANG_WARN_EMPTY_BODY":"YES","CLANG_WARN_ENUM_CONVERSION":"YES","CLANG_WARN_INFINITE_RECURSION":"YES","CLANG_WARN_INT_CONVERSION":"YES","CLANG_WARN_NON_LITERAL_NULL_CONVERSION":"YES","CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF":"YES","CLANG_WARN_OBJC_LITERAL_CONVERSION":"YES","CLANG_WARN_OBJC_ROOT_CLASS":"YES_ERROR","CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER":"NO","CLANG_WARN_RANGE_LOOP_ANALYSIS":"YES","CLANG_WARN_STRICT_PROTOTYPES":"YES","CLANG_WARN_SUSPICIOUS_MOVE":"YES","CLANG_WARN_UNREACHABLE_CODE":"YES","CLANG_WARN__DUPLICATE_METHOD_MATCH":"YES","CLASS_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/JavaClasses","CLEAN_PRECOMPS":"YES","CLONE_HEADERS":"NO","COCOAPODS_PARALLEL_CODE_SIGN":"true","CODESIGNING_FOLDER_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app","CODE_SIGNING_ALLOWED":"YES","CODE_SIGNING_REQUIRED":"YES","CODE_SIGN_CONTEXT_CLASS":"XCiPhoneOSCodeSignContext","CODE_SIGN_ENTITLEMENTS":"Runner/Runner.entitlements","CODE_SIGN_IDENTITY":"Apple Development","CODE_SIGN_INJECT_BASE_ENTITLEMENTS":"YES","CODE_SIGN_STYLE":"Automatic","COLOR_DIAGNOSTICS":"NO","COMBINE_HIDPI_IMAGES":"NO","COMPILER_INDEX_STORE_ENABLE":"Default","COMPOSITE_SDK_DIRS":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/CompositeSDKs","COMPRESS_PNG_FILES":"YES","CONFIGURATION":"Debug","CONFIGURATION_BUILD_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos","CONFIGURATION_TEMP_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos","CONTENTS_FOLDER_PATH":"Runner.app","COPYING_PRESERVES_HFS_DATA":"NO","COPY_HEADERS_RUN_UNIFDEF":"NO","COPY_PHASE_STRIP":"NO","COPY_RESOURCES_FROM_STATIC_FRAMEWORKS":"YES","CORRESPONDING_SIMULATOR_PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform","CORRESPONDING_SIMULATOR_PLATFORM_NAME":"iphonesimulator","CORRESPONDING_SIMULATOR_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator15.0.sdk","CORRESPONDING_SIMULATOR_SDK_NAME":"iphonesimulator15.0","CP":"/bin/cp","CREATE_INFOPLIST_SECTION_IN_BINARY":"NO","CURRENT_ARCH":"undefined_arch","CURRENT_PROJECT_VERSION":"207","CURRENT_VARIANT":"normal","DART_DEFINES":"Zmx1dHRlci5pbnNwZWN0b3Iuc3RydWN0dXJlZEVycm9ycz10cnVl,RkxVVFRFUl9XRUJfQVVUT19ERVRFQ1Q9dHJ1ZQ==","DART_OBFUSCATION":"false","DEAD_CODE_STRIPPING":"YES","DEBUGGING_SYMBOLS":"YES","DEBUG_INFORMATION_FORMAT":"dwarf","DEFAULT_COMPILER":"com.apple.compilers.llvm.clang.1_0","DEFAULT_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","DEFAULT_KEXT_INSTALL_PATH":"/System/Library/Extensions","DEFINES_MODULE":"NO","DEPLOYMENT_LOCATION":"NO","DEPLOYMENT_POSTPROCESSING":"NO","DEPLOYMENT_TARGET_CLANG_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_CLANG_FLAG_NAME":"miphoneos-version-min","DEPLOYMENT_TARGET_CLANG_FLAG_PREFIX":"-miphoneos-version-min=","DEPLOYMENT_TARGET_LD_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_LD_FLAG_NAME":"ios_version_min","DEPLOYMENT_TARGET_SETTING_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_SUGGESTED_VALUES":"9.0 9.1 9.2 9.3 10.0 10.1 10.2 10.3 11.0 11.1 11.2 11.3 11.4 12.0 12.1 12.2 12.3 12.4 13.0 13.1 13.2 13.3 13.4 13.5 13.6 14.0 14.1 14.2 14.3 14.4 14.5 14.6 14.7 15.0","DERIVED_FILES_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources","DERIVED_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources","DERIVED_SOURCES_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources","DERIVE_MACCATALYST_PRODUCT_BUNDLE_IDENTIFIER":"NO","DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","DEVELOPER_FRAMEWORKS_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_FRAMEWORKS_DIR_QUOTED":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Library","DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs","DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Tools","DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","DEVELOPMENT_LANGUAGE":"en","DEVELOPMENT_TEAM":"HJAN5D475T","DOCUMENTATION_FOLDER_PATH":"Runner.app/en.lproj/Documentation","DONT_GENERATE_INFOPLIST_FILE":"NO","DO_HEADER_SCANNING_IN_JAM":"NO","DSTROOT":"/tmp/Runner.dst","DT_TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","DWARF_DSYM_FILE_NAME":"Runner.app.dSYM","DWARF_DSYM_FILE_SHOULD_ACCOMPANY_PRODUCT":"NO","DWARF_DSYM_FOLDER_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos","EFFECTIVE_PLATFORM_NAME":"-iphoneos","EMBEDDED_CONTENT_CONTAINS_SWIFT":"NO","EMBEDDED_PROFILE_NAME":"embedded.mobileprovision","EMBED_ASSET_PACKS_IN_PRODUCT_BUNDLE":"NO","ENABLE_APP_SANDBOX":"NO","ENABLE_BITCODE":"NO","ENABLE_DEFAULT_HEADER_SEARCH_PATHS":"YES","ENABLE_HARDENED_RUNTIME":"NO","ENABLE_HEADER_DEPENDENCIES":"YES","ENABLE_ON_DEMAND_RESOURCES":"YES","ENABLE_PREVIEWS":"NO","ENABLE_STRICT_OBJC_MSGSEND":"YES","ENABLE_TESTABILITY":"YES","ENABLE_TESTING_SEARCH_PATHS":"NO","ENTITLEMENTS_ALLOWED":"YES","ENTITLEMENTS_DESTINATION":"Signature","ENTITLEMENTS_REQUIRED":"YES","EXCLUDED_INSTALLSRC_SUBDIRECTORY_PATTERNS":".DS_Store .svn .git .hg CVS","EXCLUDED_RECURSIVE_SEARCH_PATH_SUBDIRECTORIES":"*.nib *.lproj *.framework *.gch *.xcode* *.xcassets (*) .DS_Store CVS .svn .git .hg *.pbproj *.pbxproj","EXECUTABLES_FOLDER_PATH":"Runner.app/Executables","EXECUTABLE_FOLDER_PATH":"Runner.app","EXECUTABLE_NAME":"Runner","EXECUTABLE_PATH":"Runner.app/Runner","EXTRA_FRONT_END_OPTIONS":"--no-sound-null-safety","FILE_LIST":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects/LinkFileList","FIXED_FILES_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/FixedFiles","FLUTTER_APPLICATION_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3","FLUTTER_BUILD_DIR":"build","FLUTTER_BUILD_NAME":"2.0.36","FLUTTER_BUILD_NUMBER":"191","FLUTTER_ROOT":"/Users/<USER>/Desktop/flutter","FLUTTER_TARGET":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/lib/main.dart","FRAMEWORKS_FOLDER_PATH":"Runner.app/Frameworks","FRAMEWORK_FLAG_PREFIX":"-framework","FRAMEWORK_SEARCH_PATHS":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos  \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/BoringSSL-GRPC\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/DTTJailbreakDetection\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FMDB\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseAuth\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCore\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCoreDiagnostics\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCrashlytics\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseFirestore\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseInstallations\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseMessaging\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseStorage\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GTMSessionFetcher\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleDataTransport\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleToolboxForMac\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleUtilities\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleUtilitiesComponents\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/MTBBarcodeScanner\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/PromisesObjC\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Protobuf\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Reachability\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Toast\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/abseil\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/app_settings\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/camera\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/cloud_firestore\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/connectivity\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/contacts_service\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_analytics\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_auth\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_core\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_crashlytics\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_messaging\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_storage\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_appavailability\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_idensic_mobile_sdk_plugin\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_jailbreak_detection\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_local_notifications\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_native_timezone\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_secure_storage\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/fluttertoast\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/gRPC-C++\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/gRPC-Core\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_crop\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_gallery_saver\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_picker\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/keyboard_visibility\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/launch_review\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/leveldb-library\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/local_auth\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/nanopb\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/native_device_orientation\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/open_appstore\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/package_info\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/path_provider\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/permission\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/permission_handler\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/qr_code_scanner\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/qrcode_flutter\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/share\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/shared_preferences\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/simple_image_crop\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sms_autofill\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sms_receiver\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/social_share_plugin\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sqflite\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/store_redirect\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/uni_links\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/url_launcher\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/video_player\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/webview_flutter\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/FBAEMKit/XCFrameworks\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/FBSDKCoreKit/XCFrameworks\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/FBSDKCoreKit_Basics/XCFrameworks\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/FBSDKShareKit/XCFrameworks\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/FirebaseAnalytics/Frameworks\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/GoogleAppMeasurement/Frameworks\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/IdensicMobileSDK\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/MLImage/Frameworks\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/MLKitBarcodeScanning/Frameworks\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/MLKitCommon/Frameworks\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/MLKitVision/Frameworks\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FBAEMKit\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FBSDKCoreKit\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FBSDKCoreKit_Basics\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FBSDKShareKit\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FirebaseAnalytics/AdIdSupport\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/GoogleAppMeasurement/AdIdSupport\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/IdensicMobileSDK/Core\" /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Flutter","FRAMEWORK_VERSION":"A","FULL_PRODUCT_NAME":"Runner.app","GCC3_VERSION":"3.3","GCC_C_LANGUAGE_STANDARD":"gnu99","GCC_DYNAMIC_NO_PIC":"NO","GCC_INLINES_ARE_PRIVATE_EXTERN":"YES","GCC_NO_COMMON_BLOCKS":"YES","GCC_OPTIMIZATION_LEVEL":"0","GCC_PFE_FILE_C_DIALECTS":"c objective-c c++ objective-c++","GCC_PREPROCESSOR_DEFINITIONS":"DEBUG=1  COCOAPODS=1 DEBUG=1  GPB_USE_PROTOBUF_FRAMEWORK_IMPORTS=1 DEBUG=1  PB_FIELD_32BIT=1 PB_NO_PACKED_STRUCTS=1 PB_ENABLE_MALLOC=1","GCC_SYMBOLS_PRIVATE_EXTERN":"NO","GCC_THUMB_SUPPORT":"YES","GCC_TREAT_WARNINGS_AS_ERRORS":"NO","GCC_VERSION":"com.apple.compilers.llvm.clang.1_0","GCC_VERSION_IDENTIFIER":"com_apple_compilers_llvm_clang_1_0","GCC_WARN_64_TO_32_BIT_CONVERSION":"YES","GCC_WARN_ABOUT_RETURN_TYPE":"YES_ERROR","GCC_WARN_UNDECLARED_SELECTOR":"YES","GCC_WARN_UNINITIALIZED_AUTOS":"YES_AGGRESSIVE","GCC_WARN_UNUSED_FUNCTION":"YES","GCC_WARN_UNUSED_VARIABLE":"YES","GENERATED_MODULEMAP_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/GeneratedModuleMaps-iphoneos","GENERATE_INFOPLIST_FILE":"NO","GENERATE_MASTER_OBJECT_FILE":"NO","GENERATE_PKGINFO_FILE":"YES","GENERATE_PROFILING_CODE":"NO","GENERATE_TEXT_BASED_STUBS":"NO","GID":"20","GROUP":"staff","HEADERMAP_INCLUDES_FLAT_ENTRIES_FOR_TARGET_BEING_BUILT":"YES","HEADERMAP_INCLUDES_FRAMEWORK_ENTRIES_FOR_ALL_PRODUCT_TYPES":"YES","HEADERMAP_INCLUDES_NONPUBLIC_NONPRIVATE_HEADERS":"YES","HEADERMAP_INCLUDES_PROJECT_HEADERS":"YES","HEADERMAP_USES_FRAMEWORK_PREFIX_ENTRIES":"YES","HEADERMAP_USES_VFS":"NO","HEADER_SEARCH_PATHS":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/include  \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/BoringSSL-GRPC/openssl_grpc.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/DTTJailbreakDetection/DTTJailbreakDetection.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FMDB/FMDB.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseAuth/FirebaseAuth.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCore/FirebaseCore.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCoreDiagnostics/FirebaseCoreDiagnostics.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCrashlytics/FirebaseCrashlytics.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseFirestore/FirebaseFirestore.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseInstallations/FirebaseInstallations.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseMessaging/FirebaseMessaging.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseStorage/FirebaseStorage.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GTMSessionFetcher/GTMSessionFetcher.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleDataTransport/GoogleDataTransport.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleToolboxForMac/GoogleToolboxForMac.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleUtilities/GoogleUtilities.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleUtilitiesComponents/GoogleUtilitiesComponents.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/MTBBarcodeScanner/MTBBarcodeScanner.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/PromisesObjC/FBLPromises.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Protobuf/Protobuf.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Reachability/Reachability.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Toast/Toast.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/abseil/absl.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/app_settings/app_settings.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/camera/camera.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/cloud_firestore/cloud_firestore.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/connectivity/connectivity.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/contacts_service/contacts_service.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_analytics/firebase_analytics.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_auth/firebase_auth.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_core/firebase_core.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_crashlytics/firebase_crashlytics.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_messaging/firebase_messaging.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_storage/firebase_storage.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_appavailability/flutter_appavailability.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_idensic_mobile_sdk_plugin/flutter_idensic_mobile_sdk_plugin.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_jailbreak_detection/flutter_jailbreak_detection.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_local_notifications/flutter_local_notifications.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_native_timezone/flutter_native_timezone.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_secure_storage/flutter_secure_storage.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/fluttertoast/fluttertoast.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/gRPC-C++/grpcpp.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/gRPC-Core/grpc.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_crop/image_crop.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_gallery_saver/image_gallery_saver.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_picker/image_picker.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/keyboard_visibility/keyboard_visibility.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/launch_review/launch_review.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/leveldb-library/leveldb.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/local_auth/local_auth.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/nanopb/nanopb.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/native_device_orientation/native_device_orientation.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/open_appstore/open_appstore.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/package_info/package_info.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/path_provider/path_provider.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/permission/permission.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/permission_handler/permission_handler.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/qr_code_scanner/qr_code_scanner.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/qrcode_flutter/qrcode_flutter.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/share/share.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/shared_preferences/shared_preferences.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/simple_image_crop/simple_image_crop.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sms_autofill/sms_autofill.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sms_receiver/sms_receiver.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/social_share_plugin/social_share_plugin.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sqflite/sqflite.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/store_redirect/store_redirect.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/uni_links/uni_links.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/url_launcher/url_launcher.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/video_player/video_player.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/webview_flutter/webview_flutter.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/Headers/Public\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/Headers/Public/Firebase\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/Headers/Public/GoogleMLKit\"  /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/Firebase/CoreOnly/Sources  /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/GoogleMLKit/MLKitCore/Sources \"/Sources/FBLPromises/include\"","HIDE_BITCODE_SYMBOLS":"YES","HOME":"/Users/<USER>","ICONV":"/usr/bin/iconv","INFOPLIST_EXPAND_BUILD_SETTINGS":"YES","INFOPLIST_FILE":"Runner/Info.plist","INFOPLIST_OUTPUT_FORMAT":"binary","INFOPLIST_PATH":"Runner.app/Info.plist","INFOPLIST_PREPROCESS":"NO","INFOSTRINGS_PATH":"Runner.app/en.lproj/InfoPlist.strings","INLINE_PRIVATE_FRAMEWORKS":"NO","INSTALLHDRS_COPY_PHASE":"NO","INSTALLHDRS_SCRIPT_PHASE":"NO","INSTALL_DIR":"/tmp/Runner.dst/Applications","INSTALL_GROUP":"staff","INSTALL_MODE_FLAG":"u+w,go-w,a+rX","INSTALL_OWNER":"dabzshot","INSTALL_PATH":"/Applications","INSTALL_ROOT":"/tmp/Runner.dst","IPHONEOS_DEPLOYMENT_TARGET":"12.0","JAVAC_DEFAULT_FLAGS":"-J-Xms64m -J-XX:NewSize=4M -J-Dfile.encoding=UTF8","JAVA_APP_STUB":"/System/Library/Frameworks/JavaVM.framework/Resources/MacOS/JavaApplicationStub","JAVA_ARCHIVE_CLASSES":"YES","JAVA_ARCHIVE_TYPE":"JAR","JAVA_COMPILER":"/usr/bin/javac","JAVA_FOLDER_PATH":"Runner.app/Java","JAVA_FRAMEWORK_RESOURCES_DIRS":"Resources","JAVA_JAR_FLAGS":"cv","JAVA_SOURCE_SUBDIR":".","JAVA_USE_DEPENDENCIES":"YES","JAVA_ZIP_FLAGS":"-urg","JIKES_DEFAULT_FLAGS":"+E +OLDCSO","KASAN_DEFAULT_CFLAGS":"-DKASAN=1 -fsanitize=address -mllvm -asan-globals-live-support -mllvm -asan-force-dynamic-shadow","KEEP_PRIVATE_EXTERNS":"NO","LD_DEPENDENCY_INFO_FILE":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/undefined_arch/Runner_dependency_info.dat","LD_GENERATE_MAP_FILE":"NO","LD_MAP_FILE_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-LinkMap-normal-undefined_arch.txt","LD_NO_PIE":"NO","LD_QUOTE_LINKER_ARGUMENTS_FOR_COMPILER_DRIVER":"YES","LD_RUNPATH_SEARCH_PATHS":" /usr/lib/swift '@executable_path/Frameworks' '@loader_path/Frameworks' @executable_path/Frameworks","LEGACY_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/PlugIns/Xcode3Core.ideplugin/Contents/SharedSupport/Developer","LEX":"lex","LIBRARY_DEXT_INSTALL_PATH":"/Library/DriverExtensions","LIBRARY_FLAG_NOSPACE":"YES","LIBRARY_FLAG_PREFIX":"-l","LIBRARY_KEXT_INSTALL_PATH":"/Library/Extensions","LIBRARY_SEARCH_PATHS":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS15.0.sdk/usr/lib/swift /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Flutter","LINKER_DISPLAYS_MANGLED_NAMES":"NO","LINK_FILE_LIST_normal_arm64":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.LinkFileList","LINK_WITH_STANDARD_LIBRARIES":"YES","LLVM_TARGET_TRIPLE_OS_VERSION":"ios12.0","LLVM_TARGET_TRIPLE_VENDOR":"apple","LOCALIZATION_EXPORT_SUPPORTED":"YES","LOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app/en.lproj","LOCALIZED_STRING_MACRO_NAMES":"NSLocalizedString CFCopyLocalizedString","LOCALIZED_STRING_SWIFTUI_SUPPORT":"YES","LOCAL_ADMIN_APPS_DIR":"/Applications/Utilities","LOCAL_APPS_DIR":"/Applications","LOCAL_DEVELOPER_DIR":"/Library/Developer","LOCAL_LIBRARY_DIR":"/Library","LOCROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","LOCSYMROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","MACH_O_TYPE":"mh_execute","MAC_OS_X_PRODUCT_BUILD_VERSION":"20G95","MAC_OS_X_VERSION_ACTUAL":"110502","MAC_OS_X_VERSION_MAJOR":"110000","MAC_OS_X_VERSION_MINOR":"110500","MARKETING_VERSION":"2.0.86","METAL_LIBRARY_FILE_BASE":"default","METAL_LIBRARY_OUTPUT_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app","MODULES_FOLDER_PATH":"Runner.app/Modules","MODULE_CACHE_DIR":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex","MTL_ENABLE_DEBUG_INFO":"YES","NATIVE_ARCH":"x86_64","NATIVE_ARCH_32_BIT":"i386","NATIVE_ARCH_64_BIT":"x86_64","NATIVE_ARCH_ACTUAL":"x86_64","NO_COMMON":"YES","OBJECT_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects","OBJECT_FILE_DIR_normal":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal","OBJROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","ONLY_ACTIVE_ARCH":"NO","OS":"MACOS","OSAC":"/usr/bin/osacompile","OTHER_LDFLAGS":" -ObjC -l\"c++\" -l\"sqlite3\" -l\"z\" -framework \"AVFoundation\" -framework \"Accelerate\" -framework \"CoreGraphics\" -framework \"CoreImage\" -framework \"CoreMedia\" -framework \"CoreTelephony\" -framework \"CoreVideo\" -framework \"DTTJailbreakDetection\" -framework \"FBAEMKit\" -framework \"FBLPromises\" -framework \"FBSDKCoreKit\" -framework \"FBSDKCoreKit_Basics\" -framework \"FBSDKShareKit\" -framework \"FMDB\" -framework \"FirebaseAnalytics\" -framework \"FirebaseAuth\" -framework \"FirebaseCore\" -framework \"FirebaseCoreDiagnostics\" -framework \"FirebaseCrashlytics\" -framework \"FirebaseFirestore\" -framework \"FirebaseInstallations\" -framework \"FirebaseMessaging\" -framework \"FirebaseStorage\" -framework \"Foundation\" -framework \"GTMSessionFetcher\" -framework \"GoogleAppMeasurement\" -framework \"GoogleDataTransport\" -framework \"GoogleToolboxForMac\" -framework \"GoogleUtilities\" -framework \"GoogleUtilitiesComponents\" -framework \"IdensicMobileSDK\" -framework \"LocalAuthentication\" -framework \"MLImage\" -framework \"MLKitBarcodeScanning\" -framework \"MLKitCommon\" -framework \"MLKitVision\" -framework \"MTBBarcodeScanner\" -framework \"Protobuf\" -framework \"QuartzCore\" -framework \"Reachability\" -framework \"SafariServices\" -framework \"Security\" -framework \"StoreKit\" -framework \"SystemConfiguration\" -framework \"Toast\" -framework \"UIKit\" -framework \"absl\" -framework \"app_settings\" -framework \"camera\" -framework \"cloud_firestore\" -framework \"connectivity\" -framework \"contacts_service\" -framework \"firebase_analytics\" -framework \"firebase_auth\" -framework \"firebase_core\" -framework \"firebase_crashlytics\" -framework \"firebase_messaging\" -framework \"firebase_storage\" -framework \"flutter_appavailability\" -framework \"flutter_idensic_mobile_sdk_plugin\" -framework \"flutter_jailbreak_detection\" -framework \"flutter_local_notifications\" -framework \"flutter_native_timezone\" -framework \"flutter_secure_storage\" -framework \"fluttertoast\" -framework \"grpc\" -framework \"grpcpp\" -framework \"image_crop\" -framework \"image_gallery_saver\" -framework \"image_picker\" -framework \"keyboard_visibility\" -framework \"launch_review\" -framework \"leveldb\" -framework \"local_auth\" -framework \"nanopb\" -framework \"native_device_orientation\" -framework \"open_appstore\" -framework \"openssl_grpc\" -framework \"package_info\" -framework \"path_provider\" -framework \"permission\" -framework \"permission_handler\" -framework \"qr_code_scanner\" -framework \"qrcode_flutter\" -framework \"share\" -framework \"shared_preferences\" -framework \"simple_image_crop\" -framework \"sms_autofill\" -framework \"sms_receiver\" -framework \"social_share_plugin\" -framework \"sqflite\" -framework \"store_redirect\" -framework \"uni_links\" -framework \"url_launcher\" -framework \"video_player\" -framework \"webview_flutter\" -weak_framework \"UserNotifications\"","OTHER_SWIFT_FLAGS":" -D COCOAPODS","PACKAGE_CONFIG":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/.dart_tool/package_config.json","PACKAGE_TYPE":"com.apple.package-type.wrapper.application","PASCAL_STRINGS":"YES","PATH":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/libexec:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/local/bin:/Applications/Xcode.app/Contents/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/usr/local/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin","PATH_PREFIXES_EXCLUDED_FROM_HEADER_DEPENDENCIES":"/usr/include /usr/local/include /System/Library/Frameworks /System/Library/PrivateFrameworks /Applications/Xcode.app/Contents/Developer/Headers /Applications/Xcode.app/Contents/Developer/SDKs /Applications/Xcode.app/Contents/Developer/Platforms","PBDEVELOPMENTPLIST_PATH":"Runner.app/pbdevelopment.plist","PER_ARCH_OBJECT_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/undefined_arch","PER_VARIANT_OBJECT_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal","PKGINFO_FILE_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/PkgInfo","PKGINFO_PATH":"Runner.app/PkgInfo","PLATFORM_DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Applications","PLATFORM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin","PLATFORM_DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library","PLATFORM_DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs","PLATFORM_DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Tools","PLATFORM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr","PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform","PLATFORM_DISPLAY_NAME":"iOS","PLATFORM_FAMILY_NAME":"iOS","PLATFORM_NAME":"iphoneos","PLATFORM_PREFERRED_ARCH":"arm64","PLATFORM_PRODUCT_BUILD_VERSION":"19A339","PLIST_FILE_OUTPUT_FORMAT":"binary","PLUGINS_FOLDER_PATH":"Runner.app/PlugIns","PODS_BUILD_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","PODS_CONFIGURATION_BUILD_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos","PODS_PODFILE_DIR_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/.","PODS_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods","PODS_XCFRAMEWORKS_BUILD_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates","PRECOMPS_INCLUDE_HEADERS_FROM_BUILT_PRODUCTS_DIR":"YES","PRECOMP_DESTINATION_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/PrefixHeaders","PRESERVE_DEAD_CODE_INITS_AND_TERMS":"NO","PRIVATE_HEADERS_FOLDER_PATH":"Runner.app/PrivateHeaders","PRODUCT_BUNDLE_IDENTIFIER":"likewallet.likewallet","PRODUCT_BUNDLE_PACKAGE_TYPE":"APPL","PRODUCT_MODULE_NAME":"Runner","PRODUCT_NAME":"Runner","PRODUCT_SETTINGS_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/Info.plist","PRODUCT_TYPE":"com.apple.product-type.application","PROFILING_CODE":"NO","PROJECT":"Runner","PROJECT_DERIVED_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/DerivedSources","PROJECT_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","PROJECT_FILE_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner.xcodeproj","PROJECT_NAME":"Runner","PROJECT_TEMP_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build","PROJECT_TEMP_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","PROVISIONING_PROFILE_REQUIRED":"YES","PUBLIC_HEADERS_FOLDER_PATH":"Runner.app/Headers","RECURSIVE_SEARCH_PATHS_FOLLOW_SYMLINKS":"YES","REMOVE_CVS_FROM_RESOURCES":"YES","REMOVE_GIT_FROM_RESOURCES":"YES","REMOVE_HEADERS_FROM_EMBEDDED_BUNDLES":"YES","REMOVE_HG_FROM_RESOURCES":"YES","REMOVE_SVN_FROM_RESOURCES":"YES","RESOURCE_RULES_REQUIRED":"YES","REZ_COLLECTOR_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/ResourceManagerResources","REZ_OBJECTS_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/ResourceManagerResources/Objects","REZ_SEARCH_PATHS":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos ","SCAN_ALL_SOURCE_FILES_FOR_INCLUDES":"NO","SCRIPTS_FOLDER_PATH":"Runner.app/Scripts","SCRIPT_INPUT_FILE_COUNT":"0","SCRIPT_INPUT_FILE_LIST_COUNT":"0","SCRIPT_OUTPUT_FILE_COUNT":"0","SCRIPT_OUTPUT_FILE_LIST_COUNT":"0","SDKROOT":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS15.0.sdk","SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS15.0.sdk","SDK_DIR_iphoneos":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS15.0.sdk","SDK_DIR_iphoneos15_0":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS15.0.sdk","SDK_NAME":"iphoneos15.0","SDK_NAMES":"iphoneos15.0","SDK_PRODUCT_BUILD_VERSION":"19A339","SDK_VERSION":"15.0","SDK_VERSION_ACTUAL":"150000","SDK_VERSION_MAJOR":"150000","SDK_VERSION_MINOR":"150000","SED":"/usr/bin/sed","SEPARATE_STRIP":"NO","SEPARATE_SYMBOL_EDIT":"NO","SET_DIR_MODE_OWNER_GROUP":"YES","SET_FILE_MODE_OWNER_GROUP":"NO","SHALLOW_BUNDLE":"YES","SHARED_DERIVED_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/DerivedSources","SHARED_FRAMEWORKS_FOLDER_PATH":"Runner.app/SharedFrameworks","SHARED_PRECOMPS_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/SharedPrecompiledHeaders","SHARED_SUPPORT_FOLDER_PATH":"Runner.app/SharedSupport","SKIP_INSTALL":"NO","SOURCE_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","SRCROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","STRINGSDATA_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/undefined_arch","STRINGSDATA_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build","STRINGS_FILE_INFOPLIST_RENAME":"YES","STRINGS_FILE_OUTPUT_ENCODING":"binary","STRIP_BITCODE_FROM_COPIED_FILES":"YES","STRIP_INSTALLED_PRODUCT":"NO","STRIP_STYLE":"all","STRIP_SWIFT_SYMBOLS":"YES","SUPPORTED_DEVICE_FAMILIES":"1,2","SUPPORTED_PLATFORMS":"iphoneos iphonesimulator","SUPPORTS_MACCATALYST":"NO","SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD":"YES","SUPPORTS_TEXT_BASED_API":"NO","SWIFT_EMIT_LOC_STRINGS":"NO","SWIFT_OBJC_BRIDGING_HEADER":"Runner/Runner-Bridging-Header.h","SWIFT_OPTIMIZATION_LEVEL":"-Onone","SWIFT_PLATFORM_TARGET_PREFIX":"ios","SWIFT_RESPONSE_FILE_PATH_normal_arm64":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.SwiftFileList","SWIFT_VERSION":"5.0","SYMROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","SYSTEM_ADMIN_APPS_DIR":"/Applications/Utilities","SYSTEM_APPS_DIR":"/Applications","SYSTEM_CORE_SERVICES_DIR":"/System/Library/CoreServices","SYSTEM_DEMOS_DIR":"/Applications/Extras","SYSTEM_DEVELOPER_APPS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","SYSTEM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","SYSTEM_DEVELOPER_DEMOS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities/Built Examples","SYSTEM_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","SYSTEM_DEVELOPER_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library","SYSTEM_DEVELOPER_GRAPHICS_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Graphics Tools","SYSTEM_DEVELOPER_JAVA_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Java Tools","SYSTEM_DEVELOPER_PERFORMANCE_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Performance Tools","SYSTEM_DEVELOPER_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes","SYSTEM_DEVELOPER_TOOLS":"/Applications/Xcode.app/Contents/Developer/Tools","SYSTEM_DEVELOPER_TOOLS_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/documentation/DeveloperTools","SYSTEM_DEVELOPER_TOOLS_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes/DeveloperTools","SYSTEM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","SYSTEM_DEVELOPER_UTILITIES_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities","SYSTEM_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","SYSTEM_DOCUMENTATION_DIR":"/Library/Documentation","SYSTEM_KEXT_INSTALL_PATH":"/System/Library/Extensions","SYSTEM_LIBRARY_DIR":"/System/Library","TAPI_VERIFY_MODE":"ErrorsOnly","TARGETED_DEVICE_FAMILY":"1,2","TARGETNAME":"Runner","TARGET_BUILD_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos","TARGET_NAME":"Runner","TARGET_TEMP_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build","TEMP_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build","TEMP_FILES_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build","TEMP_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build","TEMP_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","TEST_FRAMEWORK_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/Frameworks /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS15.0.sdk/Developer/Library/Frameworks","TEST_LIBRARY_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib","TOOLCHAINS":"com.apple.dt.toolchain.XcodeDefault","TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","TRACK_WIDGET_CREATION":"true","TREAT_MISSING_BASELINES_AS_TEST_FAILURES":"NO","TREE_SHAKE_ICONS":"false","UID":"501","UNLOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app","UNSTRIPPED_PRODUCT":"NO","USER":"dabzshot","USER_APPS_DIR":"/Users/<USER>/Applications","USER_LIBRARY_DIR":"/Users/<USER>/Library","USE_DYNAMIC_NO_PIC":"YES","USE_HEADERMAP":"YES","USE_HEADER_SYMLINKS":"NO","USE_LLVM_TARGET_TRIPLES":"YES","USE_LLVM_TARGET_TRIPLES_FOR_CLANG":"YES","USE_LLVM_TARGET_TRIPLES_FOR_LD":"YES","USE_LLVM_TARGET_TRIPLES_FOR_TAPI":"YES","USE_RECURSIVE_SCRIPT_INPUTS_IN_SCRIPT_PHASES":"YES","VALIDATE_DEVELOPMENT_ASSET_PATHS":"YES_ERROR","VALIDATE_PRODUCT":"NO","VALIDATE_WORKSPACE":"YES_ERROR","VALID_ARCHS":"arm64 arm64e armv7 armv7s","VERBOSE_PBXCP":"NO","VERSIONING_SYSTEM":"apple-generic","VERSIONPLIST_PATH":"Runner.app/version.plist","VERSION_INFO_BUILDER":"dabzshot","VERSION_INFO_FILE":"Runner_vers.c","VERSION_INFO_STRING":"\"@(#)PROGRAM:Runner  PROJECT:Runner-207\"","WRAPPER_EXTENSION":"app","WRAPPER_NAME":"Runner.app","WRAPPER_SUFFIX":".app","WRAP_ASSET_PACKS_IN_SEPARATE_DIRECTORIES":"NO","XCODE_APP_SUPPORT_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Xcode","XCODE_PRODUCT_BUILD_VERSION":"13A1030d","XCODE_VERSION_ACTUAL":"1310","XCODE_VERSION_MAJOR":"1300","XCODE_VERSION_MINOR":"1310","XPCSERVICES_FOLDER_PATH":"Runner.app/XPCServices","YACC":"yacc","arch":"undefined_arch","variant":"normal"},"allow-missing-inputs":true,"always-out-of-date":true,"working-directory":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","control-enabled":false,"signature":"0b7078178552d298025dfed16460e887"}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:PhaseScriptExecution Thin Binary /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh": {"tool":"shell","description":"PhaseScriptExecution Thin Binary /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase5-copy-files>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["<execute-shell-script-18c1723432283e0cc55f10a6dcfd9e02f1eee2015e8ff5ebcd27678f788c2826-target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49->"],"args":["/bin/sh","-c","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh"],"env":{"ACTION":"build","AD_HOC_CODE_SIGNING_ALLOWED":"NO","ALLOW_TARGET_PLATFORM_SPECIALIZATION":"NO","ALTERNATE_GROUP":"staff","ALTERNATE_MODE":"u+w,go-w,a+rX","ALTERNATE_OWNER":"dabzshot","ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES":"YES","ALWAYS_SEARCH_USER_PATHS":"NO","ALWAYS_USE_SEPARATE_HEADERMAPS":"NO","APPLE_INTERNAL_DEVELOPER_DIR":"/AppleInternal/Developer","APPLE_INTERNAL_DIR":"/AppleInternal","APPLE_INTERNAL_DOCUMENTATION_DIR":"/AppleInternal/Documentation","APPLE_INTERNAL_LIBRARY_DIR":"/AppleInternal/Library","APPLE_INTERNAL_TOOLS":"/AppleInternal/Developer/Tools","APPLICATION_EXTENSION_API_ONLY":"NO","APPLY_RULES_IN_COPY_FILES":"NO","APPLY_RULES_IN_COPY_HEADERS":"NO","ARCHS":"arm64","ARCHS_STANDARD":"arm64","ARCHS_STANDARD_32_64_BIT":"armv7 arm64","ARCHS_STANDARD_32_BIT":"armv7","ARCHS_STANDARD_64_BIT":"arm64","ARCHS_STANDARD_INCLUDING_64_BIT":"arm64","ARCHS_UNIVERSAL_IPHONE_OS":"armv7 arm64","ASSETCATALOG_COMPILER_APPICON_NAME":"AppIcon","AVAILABLE_PLATFORMS":"appletvos appletvsimulator driverkit iphoneos iphonesimulator macosx watchos watchsimulator","BITCODE_GENERATION_MODE":"marker","BUILD_ACTIVE_RESOURCES_ONLY":"NO","BUILD_COMPONENTS":"headers build","BUILD_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","BUILD_LIBRARY_FOR_DISTRIBUTION":"NO","BUILD_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","BUILD_STYLE":"","BUILD_VARIANTS":"normal","BUILT_PRODUCTS_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos","BUNDLE_CONTENTS_FOLDER_PATH_deep":"Contents/","BUNDLE_EXECUTABLE_FOLDER_NAME_deep":"MacOS","BUNDLE_FORMAT":"shallow","BUNDLE_FRAMEWORKS_FOLDER_PATH":"Frameworks","BUNDLE_PLUGINS_FOLDER_PATH":"PlugIns","BUNDLE_PRIVATE_HEADERS_FOLDER_PATH":"PrivateHeaders","BUNDLE_PUBLIC_HEADERS_FOLDER_PATH":"Headers","CACHE_ROOT":"/var/folders/81/hsp6b80d6fn5sq0f3xxdpfd00000gn/C/com.apple.DeveloperTools/13.1-13A1030d/Xcode","CCHROOT":"/var/folders/81/hsp6b80d6fn5sq0f3xxdpfd00000gn/C/com.apple.DeveloperTools/13.1-13A1030d/Xcode","CHMOD":"/bin/chmod","CHOWN":"/usr/sbin/chown","CLANG_ANALYZER_NONNULL":"YES","CLANG_CXX_LANGUAGE_STANDARD":"gnu++0x","CLANG_CXX_LIBRARY":"libc++","CLANG_ENABLE_MODULES":"YES","CLANG_ENABLE_OBJC_ARC":"YES","CLANG_MODULES_BUILD_SESSION_FILE":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation","CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING":"YES","CLANG_WARN_BOOL_CONVERSION":"YES","CLANG_WARN_COMMA":"YES","CLANG_WARN_CONSTANT_CONVERSION":"YES","CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS":"YES","CLANG_WARN_DIRECT_OBJC_ISA_USAGE":"YES_ERROR","CLANG_WARN_EMPTY_BODY":"YES","CLANG_WARN_ENUM_CONVERSION":"YES","CLANG_WARN_INFINITE_RECURSION":"YES","CLANG_WARN_INT_CONVERSION":"YES","CLANG_WARN_NON_LITERAL_NULL_CONVERSION":"YES","CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF":"YES","CLANG_WARN_OBJC_LITERAL_CONVERSION":"YES","CLANG_WARN_OBJC_ROOT_CLASS":"YES_ERROR","CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER":"NO","CLANG_WARN_RANGE_LOOP_ANALYSIS":"YES","CLANG_WARN_STRICT_PROTOTYPES":"YES","CLANG_WARN_SUSPICIOUS_MOVE":"YES","CLANG_WARN_UNREACHABLE_CODE":"YES","CLANG_WARN__DUPLICATE_METHOD_MATCH":"YES","CLASS_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/JavaClasses","CLEAN_PRECOMPS":"YES","CLONE_HEADERS":"NO","COCOAPODS_PARALLEL_CODE_SIGN":"true","CODESIGNING_FOLDER_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app","CODE_SIGNING_ALLOWED":"YES","CODE_SIGNING_REQUIRED":"YES","CODE_SIGN_CONTEXT_CLASS":"XCiPhoneOSCodeSignContext","CODE_SIGN_ENTITLEMENTS":"Runner/Runner.entitlements","CODE_SIGN_IDENTITY":"Apple Development","CODE_SIGN_INJECT_BASE_ENTITLEMENTS":"YES","CODE_SIGN_STYLE":"Automatic","COLOR_DIAGNOSTICS":"NO","COMBINE_HIDPI_IMAGES":"NO","COMPILER_INDEX_STORE_ENABLE":"Default","COMPOSITE_SDK_DIRS":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/CompositeSDKs","COMPRESS_PNG_FILES":"YES","CONFIGURATION":"Debug","CONFIGURATION_BUILD_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos","CONFIGURATION_TEMP_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos","CONTENTS_FOLDER_PATH":"Runner.app","COPYING_PRESERVES_HFS_DATA":"NO","COPY_HEADERS_RUN_UNIFDEF":"NO","COPY_PHASE_STRIP":"NO","COPY_RESOURCES_FROM_STATIC_FRAMEWORKS":"YES","CORRESPONDING_SIMULATOR_PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform","CORRESPONDING_SIMULATOR_PLATFORM_NAME":"iphonesimulator","CORRESPONDING_SIMULATOR_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator15.0.sdk","CORRESPONDING_SIMULATOR_SDK_NAME":"iphonesimulator15.0","CP":"/bin/cp","CREATE_INFOPLIST_SECTION_IN_BINARY":"NO","CURRENT_ARCH":"undefined_arch","CURRENT_PROJECT_VERSION":"207","CURRENT_VARIANT":"normal","DART_DEFINES":"Zmx1dHRlci5pbnNwZWN0b3Iuc3RydWN0dXJlZEVycm9ycz10cnVl,RkxVVFRFUl9XRUJfQVVUT19ERVRFQ1Q9dHJ1ZQ==","DART_OBFUSCATION":"false","DEAD_CODE_STRIPPING":"YES","DEBUGGING_SYMBOLS":"YES","DEBUG_INFORMATION_FORMAT":"dwarf","DEFAULT_COMPILER":"com.apple.compilers.llvm.clang.1_0","DEFAULT_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","DEFAULT_KEXT_INSTALL_PATH":"/System/Library/Extensions","DEFINES_MODULE":"NO","DEPLOYMENT_LOCATION":"NO","DEPLOYMENT_POSTPROCESSING":"NO","DEPLOYMENT_TARGET_CLANG_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_CLANG_FLAG_NAME":"miphoneos-version-min","DEPLOYMENT_TARGET_CLANG_FLAG_PREFIX":"-miphoneos-version-min=","DEPLOYMENT_TARGET_LD_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_LD_FLAG_NAME":"ios_version_min","DEPLOYMENT_TARGET_SETTING_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_SUGGESTED_VALUES":"9.0 9.1 9.2 9.3 10.0 10.1 10.2 10.3 11.0 11.1 11.2 11.3 11.4 12.0 12.1 12.2 12.3 12.4 13.0 13.1 13.2 13.3 13.4 13.5 13.6 14.0 14.1 14.2 14.3 14.4 14.5 14.6 14.7 15.0","DERIVED_FILES_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources","DERIVED_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources","DERIVED_SOURCES_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources","DERIVE_MACCATALYST_PRODUCT_BUNDLE_IDENTIFIER":"NO","DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","DEVELOPER_FRAMEWORKS_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_FRAMEWORKS_DIR_QUOTED":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Library","DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs","DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Tools","DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","DEVELOPMENT_LANGUAGE":"en","DEVELOPMENT_TEAM":"HJAN5D475T","DOCUMENTATION_FOLDER_PATH":"Runner.app/en.lproj/Documentation","DONT_GENERATE_INFOPLIST_FILE":"NO","DO_HEADER_SCANNING_IN_JAM":"NO","DSTROOT":"/tmp/Runner.dst","DT_TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","DWARF_DSYM_FILE_NAME":"Runner.app.dSYM","DWARF_DSYM_FILE_SHOULD_ACCOMPANY_PRODUCT":"NO","DWARF_DSYM_FOLDER_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos","EFFECTIVE_PLATFORM_NAME":"-iphoneos","EMBEDDED_CONTENT_CONTAINS_SWIFT":"NO","EMBEDDED_PROFILE_NAME":"embedded.mobileprovision","EMBED_ASSET_PACKS_IN_PRODUCT_BUNDLE":"NO","ENABLE_APP_SANDBOX":"NO","ENABLE_BITCODE":"NO","ENABLE_DEFAULT_HEADER_SEARCH_PATHS":"YES","ENABLE_HARDENED_RUNTIME":"NO","ENABLE_HEADER_DEPENDENCIES":"YES","ENABLE_ON_DEMAND_RESOURCES":"YES","ENABLE_PREVIEWS":"NO","ENABLE_STRICT_OBJC_MSGSEND":"YES","ENABLE_TESTABILITY":"YES","ENABLE_TESTING_SEARCH_PATHS":"NO","ENTITLEMENTS_ALLOWED":"YES","ENTITLEMENTS_DESTINATION":"Signature","ENTITLEMENTS_REQUIRED":"YES","EXCLUDED_INSTALLSRC_SUBDIRECTORY_PATTERNS":".DS_Store .svn .git .hg CVS","EXCLUDED_RECURSIVE_SEARCH_PATH_SUBDIRECTORIES":"*.nib *.lproj *.framework *.gch *.xcode* *.xcassets (*) .DS_Store CVS .svn .git .hg *.pbproj *.pbxproj","EXECUTABLES_FOLDER_PATH":"Runner.app/Executables","EXECUTABLE_FOLDER_PATH":"Runner.app","EXECUTABLE_NAME":"Runner","EXECUTABLE_PATH":"Runner.app/Runner","EXTRA_FRONT_END_OPTIONS":"--no-sound-null-safety","FILE_LIST":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects/LinkFileList","FIXED_FILES_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/FixedFiles","FLUTTER_APPLICATION_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3","FLUTTER_BUILD_DIR":"build","FLUTTER_BUILD_NAME":"2.0.36","FLUTTER_BUILD_NUMBER":"191","FLUTTER_ROOT":"/Users/<USER>/Desktop/flutter","FLUTTER_TARGET":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/lib/main.dart","FRAMEWORKS_FOLDER_PATH":"Runner.app/Frameworks","FRAMEWORK_FLAG_PREFIX":"-framework","FRAMEWORK_SEARCH_PATHS":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos  \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/BoringSSL-GRPC\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/DTTJailbreakDetection\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FMDB\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseAuth\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCore\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCoreDiagnostics\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCrashlytics\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseFirestore\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseInstallations\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseMessaging\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseStorage\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GTMSessionFetcher\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleDataTransport\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleToolboxForMac\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleUtilities\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleUtilitiesComponents\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/MTBBarcodeScanner\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/PromisesObjC\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Protobuf\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Reachability\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Toast\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/abseil\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/app_settings\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/camera\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/cloud_firestore\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/connectivity\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/contacts_service\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_analytics\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_auth\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_core\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_crashlytics\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_messaging\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_storage\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_appavailability\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_idensic_mobile_sdk_plugin\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_jailbreak_detection\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_local_notifications\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_native_timezone\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_secure_storage\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/fluttertoast\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/gRPC-C++\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/gRPC-Core\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_crop\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_gallery_saver\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_picker\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/keyboard_visibility\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/launch_review\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/leveldb-library\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/local_auth\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/nanopb\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/native_device_orientation\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/open_appstore\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/package_info\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/path_provider\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/permission\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/permission_handler\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/qr_code_scanner\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/qrcode_flutter\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/share\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/shared_preferences\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/simple_image_crop\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sms_autofill\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sms_receiver\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/social_share_plugin\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sqflite\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/store_redirect\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/uni_links\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/url_launcher\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/video_player\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/webview_flutter\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/FBAEMKit/XCFrameworks\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/FBSDKCoreKit/XCFrameworks\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/FBSDKCoreKit_Basics/XCFrameworks\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/FBSDKShareKit/XCFrameworks\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/FirebaseAnalytics/Frameworks\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/GoogleAppMeasurement/Frameworks\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/IdensicMobileSDK\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/MLImage/Frameworks\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/MLKitBarcodeScanning/Frameworks\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/MLKitCommon/Frameworks\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/MLKitVision/Frameworks\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FBAEMKit\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FBSDKCoreKit\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FBSDKCoreKit_Basics\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FBSDKShareKit\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FirebaseAnalytics/AdIdSupport\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/GoogleAppMeasurement/AdIdSupport\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/IdensicMobileSDK/Core\" /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Flutter","FRAMEWORK_VERSION":"A","FULL_PRODUCT_NAME":"Runner.app","GCC3_VERSION":"3.3","GCC_C_LANGUAGE_STANDARD":"gnu99","GCC_DYNAMIC_NO_PIC":"NO","GCC_INLINES_ARE_PRIVATE_EXTERN":"YES","GCC_NO_COMMON_BLOCKS":"YES","GCC_OPTIMIZATION_LEVEL":"0","GCC_PFE_FILE_C_DIALECTS":"c objective-c c++ objective-c++","GCC_PREPROCESSOR_DEFINITIONS":"DEBUG=1  COCOAPODS=1 DEBUG=1  GPB_USE_PROTOBUF_FRAMEWORK_IMPORTS=1 DEBUG=1  PB_FIELD_32BIT=1 PB_NO_PACKED_STRUCTS=1 PB_ENABLE_MALLOC=1","GCC_SYMBOLS_PRIVATE_EXTERN":"NO","GCC_THUMB_SUPPORT":"YES","GCC_TREAT_WARNINGS_AS_ERRORS":"NO","GCC_VERSION":"com.apple.compilers.llvm.clang.1_0","GCC_VERSION_IDENTIFIER":"com_apple_compilers_llvm_clang_1_0","GCC_WARN_64_TO_32_BIT_CONVERSION":"YES","GCC_WARN_ABOUT_RETURN_TYPE":"YES_ERROR","GCC_WARN_UNDECLARED_SELECTOR":"YES","GCC_WARN_UNINITIALIZED_AUTOS":"YES_AGGRESSIVE","GCC_WARN_UNUSED_FUNCTION":"YES","GCC_WARN_UNUSED_VARIABLE":"YES","GENERATED_MODULEMAP_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/GeneratedModuleMaps-iphoneos","GENERATE_INFOPLIST_FILE":"NO","GENERATE_MASTER_OBJECT_FILE":"NO","GENERATE_PKGINFO_FILE":"YES","GENERATE_PROFILING_CODE":"NO","GENERATE_TEXT_BASED_STUBS":"NO","GID":"20","GROUP":"staff","HEADERMAP_INCLUDES_FLAT_ENTRIES_FOR_TARGET_BEING_BUILT":"YES","HEADERMAP_INCLUDES_FRAMEWORK_ENTRIES_FOR_ALL_PRODUCT_TYPES":"YES","HEADERMAP_INCLUDES_NONPUBLIC_NONPRIVATE_HEADERS":"YES","HEADERMAP_INCLUDES_PROJECT_HEADERS":"YES","HEADERMAP_USES_FRAMEWORK_PREFIX_ENTRIES":"YES","HEADERMAP_USES_VFS":"NO","HEADER_SEARCH_PATHS":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/include  \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/BoringSSL-GRPC/openssl_grpc.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/DTTJailbreakDetection/DTTJailbreakDetection.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FMDB/FMDB.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseAuth/FirebaseAuth.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCore/FirebaseCore.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCoreDiagnostics/FirebaseCoreDiagnostics.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCrashlytics/FirebaseCrashlytics.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseFirestore/FirebaseFirestore.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseInstallations/FirebaseInstallations.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseMessaging/FirebaseMessaging.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseStorage/FirebaseStorage.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GTMSessionFetcher/GTMSessionFetcher.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleDataTransport/GoogleDataTransport.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleToolboxForMac/GoogleToolboxForMac.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleUtilities/GoogleUtilities.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleUtilitiesComponents/GoogleUtilitiesComponents.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/MTBBarcodeScanner/MTBBarcodeScanner.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/PromisesObjC/FBLPromises.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Protobuf/Protobuf.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Reachability/Reachability.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Toast/Toast.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/abseil/absl.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/app_settings/app_settings.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/camera/camera.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/cloud_firestore/cloud_firestore.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/connectivity/connectivity.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/contacts_service/contacts_service.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_analytics/firebase_analytics.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_auth/firebase_auth.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_core/firebase_core.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_crashlytics/firebase_crashlytics.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_messaging/firebase_messaging.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_storage/firebase_storage.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_appavailability/flutter_appavailability.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_idensic_mobile_sdk_plugin/flutter_idensic_mobile_sdk_plugin.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_jailbreak_detection/flutter_jailbreak_detection.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_local_notifications/flutter_local_notifications.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_native_timezone/flutter_native_timezone.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_secure_storage/flutter_secure_storage.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/fluttertoast/fluttertoast.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/gRPC-C++/grpcpp.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/gRPC-Core/grpc.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_crop/image_crop.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_gallery_saver/image_gallery_saver.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_picker/image_picker.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/keyboard_visibility/keyboard_visibility.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/launch_review/launch_review.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/leveldb-library/leveldb.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/local_auth/local_auth.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/nanopb/nanopb.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/native_device_orientation/native_device_orientation.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/open_appstore/open_appstore.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/package_info/package_info.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/path_provider/path_provider.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/permission/permission.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/permission_handler/permission_handler.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/qr_code_scanner/qr_code_scanner.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/qrcode_flutter/qrcode_flutter.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/share/share.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/shared_preferences/shared_preferences.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/simple_image_crop/simple_image_crop.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sms_autofill/sms_autofill.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sms_receiver/sms_receiver.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/social_share_plugin/social_share_plugin.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sqflite/sqflite.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/store_redirect/store_redirect.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/uni_links/uni_links.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/url_launcher/url_launcher.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/video_player/video_player.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/webview_flutter/webview_flutter.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/Headers/Public\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/Headers/Public/Firebase\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/Headers/Public/GoogleMLKit\"  /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/Firebase/CoreOnly/Sources  /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/GoogleMLKit/MLKitCore/Sources \"/Sources/FBLPromises/include\"","HIDE_BITCODE_SYMBOLS":"YES","HOME":"/Users/<USER>","ICONV":"/usr/bin/iconv","INFOPLIST_EXPAND_BUILD_SETTINGS":"YES","INFOPLIST_FILE":"Runner/Info.plist","INFOPLIST_OUTPUT_FORMAT":"binary","INFOPLIST_PATH":"Runner.app/Info.plist","INFOPLIST_PREPROCESS":"NO","INFOSTRINGS_PATH":"Runner.app/en.lproj/InfoPlist.strings","INLINE_PRIVATE_FRAMEWORKS":"NO","INSTALLHDRS_COPY_PHASE":"NO","INSTALLHDRS_SCRIPT_PHASE":"NO","INSTALL_DIR":"/tmp/Runner.dst/Applications","INSTALL_GROUP":"staff","INSTALL_MODE_FLAG":"u+w,go-w,a+rX","INSTALL_OWNER":"dabzshot","INSTALL_PATH":"/Applications","INSTALL_ROOT":"/tmp/Runner.dst","IPHONEOS_DEPLOYMENT_TARGET":"12.0","JAVAC_DEFAULT_FLAGS":"-J-Xms64m -J-XX:NewSize=4M -J-Dfile.encoding=UTF8","JAVA_APP_STUB":"/System/Library/Frameworks/JavaVM.framework/Resources/MacOS/JavaApplicationStub","JAVA_ARCHIVE_CLASSES":"YES","JAVA_ARCHIVE_TYPE":"JAR","JAVA_COMPILER":"/usr/bin/javac","JAVA_FOLDER_PATH":"Runner.app/Java","JAVA_FRAMEWORK_RESOURCES_DIRS":"Resources","JAVA_JAR_FLAGS":"cv","JAVA_SOURCE_SUBDIR":".","JAVA_USE_DEPENDENCIES":"YES","JAVA_ZIP_FLAGS":"-urg","JIKES_DEFAULT_FLAGS":"+E +OLDCSO","KASAN_DEFAULT_CFLAGS":"-DKASAN=1 -fsanitize=address -mllvm -asan-globals-live-support -mllvm -asan-force-dynamic-shadow","KEEP_PRIVATE_EXTERNS":"NO","LD_DEPENDENCY_INFO_FILE":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/undefined_arch/Runner_dependency_info.dat","LD_GENERATE_MAP_FILE":"NO","LD_MAP_FILE_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-LinkMap-normal-undefined_arch.txt","LD_NO_PIE":"NO","LD_QUOTE_LINKER_ARGUMENTS_FOR_COMPILER_DRIVER":"YES","LD_RUNPATH_SEARCH_PATHS":" /usr/lib/swift '@executable_path/Frameworks' '@loader_path/Frameworks' @executable_path/Frameworks","LEGACY_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/PlugIns/Xcode3Core.ideplugin/Contents/SharedSupport/Developer","LEX":"lex","LIBRARY_DEXT_INSTALL_PATH":"/Library/DriverExtensions","LIBRARY_FLAG_NOSPACE":"YES","LIBRARY_FLAG_PREFIX":"-l","LIBRARY_KEXT_INSTALL_PATH":"/Library/Extensions","LIBRARY_SEARCH_PATHS":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS15.0.sdk/usr/lib/swift /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Flutter","LINKER_DISPLAYS_MANGLED_NAMES":"NO","LINK_FILE_LIST_normal_arm64":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.LinkFileList","LINK_WITH_STANDARD_LIBRARIES":"YES","LLVM_TARGET_TRIPLE_OS_VERSION":"ios12.0","LLVM_TARGET_TRIPLE_VENDOR":"apple","LOCALIZATION_EXPORT_SUPPORTED":"YES","LOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app/en.lproj","LOCALIZED_STRING_MACRO_NAMES":"NSLocalizedString CFCopyLocalizedString","LOCALIZED_STRING_SWIFTUI_SUPPORT":"YES","LOCAL_ADMIN_APPS_DIR":"/Applications/Utilities","LOCAL_APPS_DIR":"/Applications","LOCAL_DEVELOPER_DIR":"/Library/Developer","LOCAL_LIBRARY_DIR":"/Library","LOCROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","LOCSYMROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","MACH_O_TYPE":"mh_execute","MAC_OS_X_PRODUCT_BUILD_VERSION":"20G95","MAC_OS_X_VERSION_ACTUAL":"110502","MAC_OS_X_VERSION_MAJOR":"110000","MAC_OS_X_VERSION_MINOR":"110500","MARKETING_VERSION":"2.0.86","METAL_LIBRARY_FILE_BASE":"default","METAL_LIBRARY_OUTPUT_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app","MODULES_FOLDER_PATH":"Runner.app/Modules","MODULE_CACHE_DIR":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex","MTL_ENABLE_DEBUG_INFO":"YES","NATIVE_ARCH":"x86_64","NATIVE_ARCH_32_BIT":"i386","NATIVE_ARCH_64_BIT":"x86_64","NATIVE_ARCH_ACTUAL":"x86_64","NO_COMMON":"YES","OBJECT_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects","OBJECT_FILE_DIR_normal":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal","OBJROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","ONLY_ACTIVE_ARCH":"NO","OS":"MACOS","OSAC":"/usr/bin/osacompile","OTHER_LDFLAGS":" -ObjC -l\"c++\" -l\"sqlite3\" -l\"z\" -framework \"AVFoundation\" -framework \"Accelerate\" -framework \"CoreGraphics\" -framework \"CoreImage\" -framework \"CoreMedia\" -framework \"CoreTelephony\" -framework \"CoreVideo\" -framework \"DTTJailbreakDetection\" -framework \"FBAEMKit\" -framework \"FBLPromises\" -framework \"FBSDKCoreKit\" -framework \"FBSDKCoreKit_Basics\" -framework \"FBSDKShareKit\" -framework \"FMDB\" -framework \"FirebaseAnalytics\" -framework \"FirebaseAuth\" -framework \"FirebaseCore\" -framework \"FirebaseCoreDiagnostics\" -framework \"FirebaseCrashlytics\" -framework \"FirebaseFirestore\" -framework \"FirebaseInstallations\" -framework \"FirebaseMessaging\" -framework \"FirebaseStorage\" -framework \"Foundation\" -framework \"GTMSessionFetcher\" -framework \"GoogleAppMeasurement\" -framework \"GoogleDataTransport\" -framework \"GoogleToolboxForMac\" -framework \"GoogleUtilities\" -framework \"GoogleUtilitiesComponents\" -framework \"IdensicMobileSDK\" -framework \"LocalAuthentication\" -framework \"MLImage\" -framework \"MLKitBarcodeScanning\" -framework \"MLKitCommon\" -framework \"MLKitVision\" -framework \"MTBBarcodeScanner\" -framework \"Protobuf\" -framework \"QuartzCore\" -framework \"Reachability\" -framework \"SafariServices\" -framework \"Security\" -framework \"StoreKit\" -framework \"SystemConfiguration\" -framework \"Toast\" -framework \"UIKit\" -framework \"absl\" -framework \"app_settings\" -framework \"camera\" -framework \"cloud_firestore\" -framework \"connectivity\" -framework \"contacts_service\" -framework \"firebase_analytics\" -framework \"firebase_auth\" -framework \"firebase_core\" -framework \"firebase_crashlytics\" -framework \"firebase_messaging\" -framework \"firebase_storage\" -framework \"flutter_appavailability\" -framework \"flutter_idensic_mobile_sdk_plugin\" -framework \"flutter_jailbreak_detection\" -framework \"flutter_local_notifications\" -framework \"flutter_native_timezone\" -framework \"flutter_secure_storage\" -framework \"fluttertoast\" -framework \"grpc\" -framework \"grpcpp\" -framework \"image_crop\" -framework \"image_gallery_saver\" -framework \"image_picker\" -framework \"keyboard_visibility\" -framework \"launch_review\" -framework \"leveldb\" -framework \"local_auth\" -framework \"nanopb\" -framework \"native_device_orientation\" -framework \"open_appstore\" -framework \"openssl_grpc\" -framework \"package_info\" -framework \"path_provider\" -framework \"permission\" -framework \"permission_handler\" -framework \"qr_code_scanner\" -framework \"qrcode_flutter\" -framework \"share\" -framework \"shared_preferences\" -framework \"simple_image_crop\" -framework \"sms_autofill\" -framework \"sms_receiver\" -framework \"social_share_plugin\" -framework \"sqflite\" -framework \"store_redirect\" -framework \"uni_links\" -framework \"url_launcher\" -framework \"video_player\" -framework \"webview_flutter\" -weak_framework \"UserNotifications\"","OTHER_SWIFT_FLAGS":" -D COCOAPODS","PACKAGE_CONFIG":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/.dart_tool/package_config.json","PACKAGE_TYPE":"com.apple.package-type.wrapper.application","PASCAL_STRINGS":"YES","PATH":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/libexec:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/local/bin:/Applications/Xcode.app/Contents/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/usr/local/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin","PATH_PREFIXES_EXCLUDED_FROM_HEADER_DEPENDENCIES":"/usr/include /usr/local/include /System/Library/Frameworks /System/Library/PrivateFrameworks /Applications/Xcode.app/Contents/Developer/Headers /Applications/Xcode.app/Contents/Developer/SDKs /Applications/Xcode.app/Contents/Developer/Platforms","PBDEVELOPMENTPLIST_PATH":"Runner.app/pbdevelopment.plist","PER_ARCH_OBJECT_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/undefined_arch","PER_VARIANT_OBJECT_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal","PKGINFO_FILE_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/PkgInfo","PKGINFO_PATH":"Runner.app/PkgInfo","PLATFORM_DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Applications","PLATFORM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin","PLATFORM_DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library","PLATFORM_DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs","PLATFORM_DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Tools","PLATFORM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr","PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform","PLATFORM_DISPLAY_NAME":"iOS","PLATFORM_FAMILY_NAME":"iOS","PLATFORM_NAME":"iphoneos","PLATFORM_PREFERRED_ARCH":"arm64","PLATFORM_PRODUCT_BUILD_VERSION":"19A339","PLIST_FILE_OUTPUT_FORMAT":"binary","PLUGINS_FOLDER_PATH":"Runner.app/PlugIns","PODS_BUILD_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","PODS_CONFIGURATION_BUILD_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos","PODS_PODFILE_DIR_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/.","PODS_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods","PODS_XCFRAMEWORKS_BUILD_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates","PRECOMPS_INCLUDE_HEADERS_FROM_BUILT_PRODUCTS_DIR":"YES","PRECOMP_DESTINATION_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/PrefixHeaders","PRESERVE_DEAD_CODE_INITS_AND_TERMS":"NO","PRIVATE_HEADERS_FOLDER_PATH":"Runner.app/PrivateHeaders","PRODUCT_BUNDLE_IDENTIFIER":"likewallet.likewallet","PRODUCT_BUNDLE_PACKAGE_TYPE":"APPL","PRODUCT_MODULE_NAME":"Runner","PRODUCT_NAME":"Runner","PRODUCT_SETTINGS_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/Info.plist","PRODUCT_TYPE":"com.apple.product-type.application","PROFILING_CODE":"NO","PROJECT":"Runner","PROJECT_DERIVED_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/DerivedSources","PROJECT_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","PROJECT_FILE_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner.xcodeproj","PROJECT_NAME":"Runner","PROJECT_TEMP_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build","PROJECT_TEMP_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","PROVISIONING_PROFILE_REQUIRED":"YES","PUBLIC_HEADERS_FOLDER_PATH":"Runner.app/Headers","RECURSIVE_SEARCH_PATHS_FOLLOW_SYMLINKS":"YES","REMOVE_CVS_FROM_RESOURCES":"YES","REMOVE_GIT_FROM_RESOURCES":"YES","REMOVE_HEADERS_FROM_EMBEDDED_BUNDLES":"YES","REMOVE_HG_FROM_RESOURCES":"YES","REMOVE_SVN_FROM_RESOURCES":"YES","RESOURCE_RULES_REQUIRED":"YES","REZ_COLLECTOR_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/ResourceManagerResources","REZ_OBJECTS_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/ResourceManagerResources/Objects","REZ_SEARCH_PATHS":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos ","SCAN_ALL_SOURCE_FILES_FOR_INCLUDES":"NO","SCRIPTS_FOLDER_PATH":"Runner.app/Scripts","SCRIPT_INPUT_FILE_COUNT":"0","SCRIPT_INPUT_FILE_LIST_COUNT":"0","SCRIPT_OUTPUT_FILE_COUNT":"0","SCRIPT_OUTPUT_FILE_LIST_COUNT":"0","SDKROOT":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS15.0.sdk","SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS15.0.sdk","SDK_DIR_iphoneos":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS15.0.sdk","SDK_DIR_iphoneos15_0":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS15.0.sdk","SDK_NAME":"iphoneos15.0","SDK_NAMES":"iphoneos15.0","SDK_PRODUCT_BUILD_VERSION":"19A339","SDK_VERSION":"15.0","SDK_VERSION_ACTUAL":"150000","SDK_VERSION_MAJOR":"150000","SDK_VERSION_MINOR":"150000","SED":"/usr/bin/sed","SEPARATE_STRIP":"NO","SEPARATE_SYMBOL_EDIT":"NO","SET_DIR_MODE_OWNER_GROUP":"YES","SET_FILE_MODE_OWNER_GROUP":"NO","SHALLOW_BUNDLE":"YES","SHARED_DERIVED_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/DerivedSources","SHARED_FRAMEWORKS_FOLDER_PATH":"Runner.app/SharedFrameworks","SHARED_PRECOMPS_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/SharedPrecompiledHeaders","SHARED_SUPPORT_FOLDER_PATH":"Runner.app/SharedSupport","SKIP_INSTALL":"NO","SOURCE_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","SRCROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","STRINGSDATA_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/undefined_arch","STRINGSDATA_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build","STRINGS_FILE_INFOPLIST_RENAME":"YES","STRINGS_FILE_OUTPUT_ENCODING":"binary","STRIP_BITCODE_FROM_COPIED_FILES":"YES","STRIP_INSTALLED_PRODUCT":"NO","STRIP_STYLE":"all","STRIP_SWIFT_SYMBOLS":"YES","SUPPORTED_DEVICE_FAMILIES":"1,2","SUPPORTED_PLATFORMS":"iphoneos iphonesimulator","SUPPORTS_MACCATALYST":"NO","SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD":"YES","SUPPORTS_TEXT_BASED_API":"NO","SWIFT_EMIT_LOC_STRINGS":"NO","SWIFT_OBJC_BRIDGING_HEADER":"Runner/Runner-Bridging-Header.h","SWIFT_OPTIMIZATION_LEVEL":"-Onone","SWIFT_PLATFORM_TARGET_PREFIX":"ios","SWIFT_RESPONSE_FILE_PATH_normal_arm64":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.SwiftFileList","SWIFT_VERSION":"5.0","SYMROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","SYSTEM_ADMIN_APPS_DIR":"/Applications/Utilities","SYSTEM_APPS_DIR":"/Applications","SYSTEM_CORE_SERVICES_DIR":"/System/Library/CoreServices","SYSTEM_DEMOS_DIR":"/Applications/Extras","SYSTEM_DEVELOPER_APPS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","SYSTEM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","SYSTEM_DEVELOPER_DEMOS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities/Built Examples","SYSTEM_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","SYSTEM_DEVELOPER_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library","SYSTEM_DEVELOPER_GRAPHICS_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Graphics Tools","SYSTEM_DEVELOPER_JAVA_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Java Tools","SYSTEM_DEVELOPER_PERFORMANCE_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Performance Tools","SYSTEM_DEVELOPER_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes","SYSTEM_DEVELOPER_TOOLS":"/Applications/Xcode.app/Contents/Developer/Tools","SYSTEM_DEVELOPER_TOOLS_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/documentation/DeveloperTools","SYSTEM_DEVELOPER_TOOLS_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes/DeveloperTools","SYSTEM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","SYSTEM_DEVELOPER_UTILITIES_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities","SYSTEM_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","SYSTEM_DOCUMENTATION_DIR":"/Library/Documentation","SYSTEM_KEXT_INSTALL_PATH":"/System/Library/Extensions","SYSTEM_LIBRARY_DIR":"/System/Library","TAPI_VERIFY_MODE":"ErrorsOnly","TARGETED_DEVICE_FAMILY":"1,2","TARGETNAME":"Runner","TARGET_BUILD_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos","TARGET_NAME":"Runner","TARGET_TEMP_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build","TEMP_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build","TEMP_FILES_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build","TEMP_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build","TEMP_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","TEST_FRAMEWORK_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/Frameworks /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS15.0.sdk/Developer/Library/Frameworks","TEST_LIBRARY_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib","TOOLCHAINS":"com.apple.dt.toolchain.XcodeDefault","TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","TRACK_WIDGET_CREATION":"true","TREAT_MISSING_BASELINES_AS_TEST_FAILURES":"NO","TREE_SHAKE_ICONS":"false","UID":"501","UNLOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app","UNSTRIPPED_PRODUCT":"NO","USER":"dabzshot","USER_APPS_DIR":"/Users/<USER>/Applications","USER_LIBRARY_DIR":"/Users/<USER>/Library","USE_DYNAMIC_NO_PIC":"YES","USE_HEADERMAP":"YES","USE_HEADER_SYMLINKS":"NO","USE_LLVM_TARGET_TRIPLES":"YES","USE_LLVM_TARGET_TRIPLES_FOR_CLANG":"YES","USE_LLVM_TARGET_TRIPLES_FOR_LD":"YES","USE_LLVM_TARGET_TRIPLES_FOR_TAPI":"YES","USE_RECURSIVE_SCRIPT_INPUTS_IN_SCRIPT_PHASES":"YES","VALIDATE_DEVELOPMENT_ASSET_PATHS":"YES_ERROR","VALIDATE_PRODUCT":"NO","VALIDATE_WORKSPACE":"YES_ERROR","VALID_ARCHS":"arm64 arm64e armv7 armv7s","VERBOSE_PBXCP":"NO","VERSIONING_SYSTEM":"apple-generic","VERSIONPLIST_PATH":"Runner.app/version.plist","VERSION_INFO_BUILDER":"dabzshot","VERSION_INFO_FILE":"Runner_vers.c","VERSION_INFO_STRING":"\"@(#)PROGRAM:Runner  PROJECT:Runner-207\"","WRAPPER_EXTENSION":"app","WRAPPER_NAME":"Runner.app","WRAPPER_SUFFIX":".app","WRAP_ASSET_PACKS_IN_SEPARATE_DIRECTORIES":"NO","XCODE_APP_SUPPORT_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Xcode","XCODE_PRODUCT_BUILD_VERSION":"13A1030d","XCODE_VERSION_ACTUAL":"1310","XCODE_VERSION_MAJOR":"1300","XCODE_VERSION_MINOR":"1310","XPCSERVICES_FOLDER_PATH":"Runner.app/XPCServices","YACC":"yacc","arch":"undefined_arch","variant":"normal"},"allow-missing-inputs":true,"always-out-of-date":true,"working-directory":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","control-enabled":false,"signature":"d8fcfe573c36f55d9d2a9a4cdc927483"}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:PhaseScriptExecution [CP] Check Pods Manifest.lock /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-328736DAE7F587435A6E60E5.sh": {"tool":"shell","description":"PhaseScriptExecution [CP] Check Pods Manifest.lock /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-328736DAE7F587435A6E60E5.sh","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Podfile.lock/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/Manifest.lock/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-328736DAE7F587435A6E60E5.sh","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Pods-Runner-checkManifestLockResult.txt"],"args":["/bin/sh","-c","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-328736DAE7F587435A6E60E5.sh"],"env":{"ACTION":"build","AD_HOC_CODE_SIGNING_ALLOWED":"NO","ALLOW_TARGET_PLATFORM_SPECIALIZATION":"NO","ALTERNATE_GROUP":"staff","ALTERNATE_MODE":"u+w,go-w,a+rX","ALTERNATE_OWNER":"dabzshot","ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES":"YES","ALWAYS_SEARCH_USER_PATHS":"NO","ALWAYS_USE_SEPARATE_HEADERMAPS":"NO","APPLE_INTERNAL_DEVELOPER_DIR":"/AppleInternal/Developer","APPLE_INTERNAL_DIR":"/AppleInternal","APPLE_INTERNAL_DOCUMENTATION_DIR":"/AppleInternal/Documentation","APPLE_INTERNAL_LIBRARY_DIR":"/AppleInternal/Library","APPLE_INTERNAL_TOOLS":"/AppleInternal/Developer/Tools","APPLICATION_EXTENSION_API_ONLY":"NO","APPLY_RULES_IN_COPY_FILES":"NO","APPLY_RULES_IN_COPY_HEADERS":"NO","ARCHS":"arm64","ARCHS_STANDARD":"arm64","ARCHS_STANDARD_32_64_BIT":"armv7 arm64","ARCHS_STANDARD_32_BIT":"armv7","ARCHS_STANDARD_64_BIT":"arm64","ARCHS_STANDARD_INCLUDING_64_BIT":"arm64","ARCHS_UNIVERSAL_IPHONE_OS":"armv7 arm64","ASSETCATALOG_COMPILER_APPICON_NAME":"AppIcon","AVAILABLE_PLATFORMS":"appletvos appletvsimulator driverkit iphoneos iphonesimulator macosx watchos watchsimulator","BITCODE_GENERATION_MODE":"marker","BUILD_ACTIVE_RESOURCES_ONLY":"NO","BUILD_COMPONENTS":"headers build","BUILD_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","BUILD_LIBRARY_FOR_DISTRIBUTION":"NO","BUILD_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","BUILD_STYLE":"","BUILD_VARIANTS":"normal","BUILT_PRODUCTS_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos","BUNDLE_CONTENTS_FOLDER_PATH_deep":"Contents/","BUNDLE_EXECUTABLE_FOLDER_NAME_deep":"MacOS","BUNDLE_FORMAT":"shallow","BUNDLE_FRAMEWORKS_FOLDER_PATH":"Frameworks","BUNDLE_PLUGINS_FOLDER_PATH":"PlugIns","BUNDLE_PRIVATE_HEADERS_FOLDER_PATH":"PrivateHeaders","BUNDLE_PUBLIC_HEADERS_FOLDER_PATH":"Headers","CACHE_ROOT":"/var/folders/81/hsp6b80d6fn5sq0f3xxdpfd00000gn/C/com.apple.DeveloperTools/13.1-13A1030d/Xcode","CCHROOT":"/var/folders/81/hsp6b80d6fn5sq0f3xxdpfd00000gn/C/com.apple.DeveloperTools/13.1-13A1030d/Xcode","CHMOD":"/bin/chmod","CHOWN":"/usr/sbin/chown","CLANG_ANALYZER_NONNULL":"YES","CLANG_CXX_LANGUAGE_STANDARD":"gnu++0x","CLANG_CXX_LIBRARY":"libc++","CLANG_ENABLE_MODULES":"YES","CLANG_ENABLE_OBJC_ARC":"YES","CLANG_MODULES_BUILD_SESSION_FILE":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation","CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING":"YES","CLANG_WARN_BOOL_CONVERSION":"YES","CLANG_WARN_COMMA":"YES","CLANG_WARN_CONSTANT_CONVERSION":"YES","CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS":"YES","CLANG_WARN_DIRECT_OBJC_ISA_USAGE":"YES_ERROR","CLANG_WARN_EMPTY_BODY":"YES","CLANG_WARN_ENUM_CONVERSION":"YES","CLANG_WARN_INFINITE_RECURSION":"YES","CLANG_WARN_INT_CONVERSION":"YES","CLANG_WARN_NON_LITERAL_NULL_CONVERSION":"YES","CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF":"YES","CLANG_WARN_OBJC_LITERAL_CONVERSION":"YES","CLANG_WARN_OBJC_ROOT_CLASS":"YES_ERROR","CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER":"NO","CLANG_WARN_RANGE_LOOP_ANALYSIS":"YES","CLANG_WARN_STRICT_PROTOTYPES":"YES","CLANG_WARN_SUSPICIOUS_MOVE":"YES","CLANG_WARN_UNREACHABLE_CODE":"YES","CLANG_WARN__DUPLICATE_METHOD_MATCH":"YES","CLASS_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/JavaClasses","CLEAN_PRECOMPS":"YES","CLONE_HEADERS":"NO","COCOAPODS_PARALLEL_CODE_SIGN":"true","CODESIGNING_FOLDER_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app","CODE_SIGNING_ALLOWED":"YES","CODE_SIGNING_REQUIRED":"YES","CODE_SIGN_CONTEXT_CLASS":"XCiPhoneOSCodeSignContext","CODE_SIGN_ENTITLEMENTS":"Runner/Runner.entitlements","CODE_SIGN_IDENTITY":"Apple Development","CODE_SIGN_INJECT_BASE_ENTITLEMENTS":"YES","CODE_SIGN_STYLE":"Automatic","COLOR_DIAGNOSTICS":"NO","COMBINE_HIDPI_IMAGES":"NO","COMPILER_INDEX_STORE_ENABLE":"Default","COMPOSITE_SDK_DIRS":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/CompositeSDKs","COMPRESS_PNG_FILES":"YES","CONFIGURATION":"Debug","CONFIGURATION_BUILD_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos","CONFIGURATION_TEMP_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos","CONTENTS_FOLDER_PATH":"Runner.app","COPYING_PRESERVES_HFS_DATA":"NO","COPY_HEADERS_RUN_UNIFDEF":"NO","COPY_PHASE_STRIP":"NO","COPY_RESOURCES_FROM_STATIC_FRAMEWORKS":"YES","CORRESPONDING_SIMULATOR_PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform","CORRESPONDING_SIMULATOR_PLATFORM_NAME":"iphonesimulator","CORRESPONDING_SIMULATOR_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator15.0.sdk","CORRESPONDING_SIMULATOR_SDK_NAME":"iphonesimulator15.0","CP":"/bin/cp","CREATE_INFOPLIST_SECTION_IN_BINARY":"NO","CURRENT_ARCH":"undefined_arch","CURRENT_PROJECT_VERSION":"207","CURRENT_VARIANT":"normal","DART_DEFINES":"Zmx1dHRlci5pbnNwZWN0b3Iuc3RydWN0dXJlZEVycm9ycz10cnVl,RkxVVFRFUl9XRUJfQVVUT19ERVRFQ1Q9dHJ1ZQ==","DART_OBFUSCATION":"false","DEAD_CODE_STRIPPING":"YES","DEBUGGING_SYMBOLS":"YES","DEBUG_INFORMATION_FORMAT":"dwarf","DEFAULT_COMPILER":"com.apple.compilers.llvm.clang.1_0","DEFAULT_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","DEFAULT_KEXT_INSTALL_PATH":"/System/Library/Extensions","DEFINES_MODULE":"NO","DEPLOYMENT_LOCATION":"NO","DEPLOYMENT_POSTPROCESSING":"NO","DEPLOYMENT_TARGET_CLANG_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_CLANG_FLAG_NAME":"miphoneos-version-min","DEPLOYMENT_TARGET_CLANG_FLAG_PREFIX":"-miphoneos-version-min=","DEPLOYMENT_TARGET_LD_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_LD_FLAG_NAME":"ios_version_min","DEPLOYMENT_TARGET_SETTING_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_SUGGESTED_VALUES":"9.0 9.1 9.2 9.3 10.0 10.1 10.2 10.3 11.0 11.1 11.2 11.3 11.4 12.0 12.1 12.2 12.3 12.4 13.0 13.1 13.2 13.3 13.4 13.5 13.6 14.0 14.1 14.2 14.3 14.4 14.5 14.6 14.7 15.0","DERIVED_FILES_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources","DERIVED_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources","DERIVED_SOURCES_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources","DERIVE_MACCATALYST_PRODUCT_BUNDLE_IDENTIFIER":"NO","DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","DEVELOPER_FRAMEWORKS_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_FRAMEWORKS_DIR_QUOTED":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Library","DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs","DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Tools","DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","DEVELOPMENT_LANGUAGE":"en","DEVELOPMENT_TEAM":"HJAN5D475T","DOCUMENTATION_FOLDER_PATH":"Runner.app/en.lproj/Documentation","DONT_GENERATE_INFOPLIST_FILE":"NO","DO_HEADER_SCANNING_IN_JAM":"NO","DSTROOT":"/tmp/Runner.dst","DT_TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","DWARF_DSYM_FILE_NAME":"Runner.app.dSYM","DWARF_DSYM_FILE_SHOULD_ACCOMPANY_PRODUCT":"NO","DWARF_DSYM_FOLDER_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos","EFFECTIVE_PLATFORM_NAME":"-iphoneos","EMBEDDED_CONTENT_CONTAINS_SWIFT":"NO","EMBEDDED_PROFILE_NAME":"embedded.mobileprovision","EMBED_ASSET_PACKS_IN_PRODUCT_BUNDLE":"NO","ENABLE_APP_SANDBOX":"NO","ENABLE_BITCODE":"NO","ENABLE_DEFAULT_HEADER_SEARCH_PATHS":"YES","ENABLE_HARDENED_RUNTIME":"NO","ENABLE_HEADER_DEPENDENCIES":"YES","ENABLE_ON_DEMAND_RESOURCES":"YES","ENABLE_PREVIEWS":"NO","ENABLE_STRICT_OBJC_MSGSEND":"YES","ENABLE_TESTABILITY":"YES","ENABLE_TESTING_SEARCH_PATHS":"NO","ENTITLEMENTS_ALLOWED":"YES","ENTITLEMENTS_DESTINATION":"Signature","ENTITLEMENTS_REQUIRED":"YES","EXCLUDED_INSTALLSRC_SUBDIRECTORY_PATTERNS":".DS_Store .svn .git .hg CVS","EXCLUDED_RECURSIVE_SEARCH_PATH_SUBDIRECTORIES":"*.nib *.lproj *.framework *.gch *.xcode* *.xcassets (*) .DS_Store CVS .svn .git .hg *.pbproj *.pbxproj","EXECUTABLES_FOLDER_PATH":"Runner.app/Executables","EXECUTABLE_FOLDER_PATH":"Runner.app","EXECUTABLE_NAME":"Runner","EXECUTABLE_PATH":"Runner.app/Runner","EXTRA_FRONT_END_OPTIONS":"--no-sound-null-safety","FILE_LIST":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects/LinkFileList","FIXED_FILES_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/FixedFiles","FLUTTER_APPLICATION_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3","FLUTTER_BUILD_DIR":"build","FLUTTER_BUILD_NAME":"2.0.36","FLUTTER_BUILD_NUMBER":"191","FLUTTER_ROOT":"/Users/<USER>/Desktop/flutter","FLUTTER_TARGET":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/lib/main.dart","FRAMEWORKS_FOLDER_PATH":"Runner.app/Frameworks","FRAMEWORK_FLAG_PREFIX":"-framework","FRAMEWORK_SEARCH_PATHS":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos  \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/BoringSSL-GRPC\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/DTTJailbreakDetection\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FMDB\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseAuth\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCore\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCoreDiagnostics\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCrashlytics\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseFirestore\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseInstallations\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseMessaging\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseStorage\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GTMSessionFetcher\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleDataTransport\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleToolboxForMac\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleUtilities\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleUtilitiesComponents\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/MTBBarcodeScanner\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/PromisesObjC\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Protobuf\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Reachability\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Toast\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/abseil\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/app_settings\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/camera\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/cloud_firestore\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/connectivity\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/contacts_service\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_analytics\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_auth\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_core\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_crashlytics\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_messaging\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_storage\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_appavailability\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_idensic_mobile_sdk_plugin\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_jailbreak_detection\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_local_notifications\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_native_timezone\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_secure_storage\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/fluttertoast\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/gRPC-C++\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/gRPC-Core\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_crop\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_gallery_saver\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_picker\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/keyboard_visibility\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/launch_review\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/leveldb-library\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/local_auth\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/nanopb\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/native_device_orientation\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/open_appstore\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/package_info\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/path_provider\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/permission\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/permission_handler\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/qr_code_scanner\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/qrcode_flutter\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/share\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/shared_preferences\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/simple_image_crop\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sms_autofill\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sms_receiver\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/social_share_plugin\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sqflite\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/store_redirect\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/uni_links\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/url_launcher\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/video_player\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/webview_flutter\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/FBAEMKit/XCFrameworks\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/FBSDKCoreKit/XCFrameworks\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/FBSDKCoreKit_Basics/XCFrameworks\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/FBSDKShareKit/XCFrameworks\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/FirebaseAnalytics/Frameworks\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/GoogleAppMeasurement/Frameworks\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/IdensicMobileSDK\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/MLImage/Frameworks\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/MLKitBarcodeScanning/Frameworks\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/MLKitCommon/Frameworks\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/MLKitVision/Frameworks\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FBAEMKit\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FBSDKCoreKit\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FBSDKCoreKit_Basics\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FBSDKShareKit\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FirebaseAnalytics/AdIdSupport\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/GoogleAppMeasurement/AdIdSupport\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/IdensicMobileSDK/Core\" /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Flutter","FRAMEWORK_VERSION":"A","FULL_PRODUCT_NAME":"Runner.app","GCC3_VERSION":"3.3","GCC_C_LANGUAGE_STANDARD":"gnu99","GCC_DYNAMIC_NO_PIC":"NO","GCC_INLINES_ARE_PRIVATE_EXTERN":"YES","GCC_NO_COMMON_BLOCKS":"YES","GCC_OPTIMIZATION_LEVEL":"0","GCC_PFE_FILE_C_DIALECTS":"c objective-c c++ objective-c++","GCC_PREPROCESSOR_DEFINITIONS":"DEBUG=1  COCOAPODS=1 DEBUG=1  GPB_USE_PROTOBUF_FRAMEWORK_IMPORTS=1 DEBUG=1  PB_FIELD_32BIT=1 PB_NO_PACKED_STRUCTS=1 PB_ENABLE_MALLOC=1","GCC_SYMBOLS_PRIVATE_EXTERN":"NO","GCC_THUMB_SUPPORT":"YES","GCC_TREAT_WARNINGS_AS_ERRORS":"NO","GCC_VERSION":"com.apple.compilers.llvm.clang.1_0","GCC_VERSION_IDENTIFIER":"com_apple_compilers_llvm_clang_1_0","GCC_WARN_64_TO_32_BIT_CONVERSION":"YES","GCC_WARN_ABOUT_RETURN_TYPE":"YES_ERROR","GCC_WARN_UNDECLARED_SELECTOR":"YES","GCC_WARN_UNINITIALIZED_AUTOS":"YES_AGGRESSIVE","GCC_WARN_UNUSED_FUNCTION":"YES","GCC_WARN_UNUSED_VARIABLE":"YES","GENERATED_MODULEMAP_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/GeneratedModuleMaps-iphoneos","GENERATE_INFOPLIST_FILE":"NO","GENERATE_MASTER_OBJECT_FILE":"NO","GENERATE_PKGINFO_FILE":"YES","GENERATE_PROFILING_CODE":"NO","GENERATE_TEXT_BASED_STUBS":"NO","GID":"20","GROUP":"staff","HEADERMAP_INCLUDES_FLAT_ENTRIES_FOR_TARGET_BEING_BUILT":"YES","HEADERMAP_INCLUDES_FRAMEWORK_ENTRIES_FOR_ALL_PRODUCT_TYPES":"YES","HEADERMAP_INCLUDES_NONPUBLIC_NONPRIVATE_HEADERS":"YES","HEADERMAP_INCLUDES_PROJECT_HEADERS":"YES","HEADERMAP_USES_FRAMEWORK_PREFIX_ENTRIES":"YES","HEADERMAP_USES_VFS":"NO","HEADER_SEARCH_PATHS":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/include  \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/BoringSSL-GRPC/openssl_grpc.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/DTTJailbreakDetection/DTTJailbreakDetection.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FMDB/FMDB.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseAuth/FirebaseAuth.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCore/FirebaseCore.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCoreDiagnostics/FirebaseCoreDiagnostics.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCrashlytics/FirebaseCrashlytics.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseFirestore/FirebaseFirestore.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseInstallations/FirebaseInstallations.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseMessaging/FirebaseMessaging.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseStorage/FirebaseStorage.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GTMSessionFetcher/GTMSessionFetcher.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleDataTransport/GoogleDataTransport.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleToolboxForMac/GoogleToolboxForMac.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleUtilities/GoogleUtilities.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleUtilitiesComponents/GoogleUtilitiesComponents.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/MTBBarcodeScanner/MTBBarcodeScanner.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/PromisesObjC/FBLPromises.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Protobuf/Protobuf.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Reachability/Reachability.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Toast/Toast.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/abseil/absl.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/app_settings/app_settings.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/camera/camera.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/cloud_firestore/cloud_firestore.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/connectivity/connectivity.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/contacts_service/contacts_service.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_analytics/firebase_analytics.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_auth/firebase_auth.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_core/firebase_core.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_crashlytics/firebase_crashlytics.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_messaging/firebase_messaging.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_storage/firebase_storage.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_appavailability/flutter_appavailability.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_idensic_mobile_sdk_plugin/flutter_idensic_mobile_sdk_plugin.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_jailbreak_detection/flutter_jailbreak_detection.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_local_notifications/flutter_local_notifications.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_native_timezone/flutter_native_timezone.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_secure_storage/flutter_secure_storage.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/fluttertoast/fluttertoast.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/gRPC-C++/grpcpp.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/gRPC-Core/grpc.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_crop/image_crop.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_gallery_saver/image_gallery_saver.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_picker/image_picker.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/keyboard_visibility/keyboard_visibility.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/launch_review/launch_review.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/leveldb-library/leveldb.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/local_auth/local_auth.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/nanopb/nanopb.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/native_device_orientation/native_device_orientation.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/open_appstore/open_appstore.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/package_info/package_info.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/path_provider/path_provider.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/permission/permission.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/permission_handler/permission_handler.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/qr_code_scanner/qr_code_scanner.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/qrcode_flutter/qrcode_flutter.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/share/share.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/shared_preferences/shared_preferences.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/simple_image_crop/simple_image_crop.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sms_autofill/sms_autofill.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sms_receiver/sms_receiver.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/social_share_plugin/social_share_plugin.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sqflite/sqflite.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/store_redirect/store_redirect.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/uni_links/uni_links.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/url_launcher/url_launcher.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/video_player/video_player.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/webview_flutter/webview_flutter.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/Headers/Public\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/Headers/Public/Firebase\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/Headers/Public/GoogleMLKit\"  /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/Firebase/CoreOnly/Sources  /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/GoogleMLKit/MLKitCore/Sources \"/Sources/FBLPromises/include\"","HIDE_BITCODE_SYMBOLS":"YES","HOME":"/Users/<USER>","ICONV":"/usr/bin/iconv","INFOPLIST_EXPAND_BUILD_SETTINGS":"YES","INFOPLIST_FILE":"Runner/Info.plist","INFOPLIST_OUTPUT_FORMAT":"binary","INFOPLIST_PATH":"Runner.app/Info.plist","INFOPLIST_PREPROCESS":"NO","INFOSTRINGS_PATH":"Runner.app/en.lproj/InfoPlist.strings","INLINE_PRIVATE_FRAMEWORKS":"NO","INSTALLHDRS_COPY_PHASE":"NO","INSTALLHDRS_SCRIPT_PHASE":"NO","INSTALL_DIR":"/tmp/Runner.dst/Applications","INSTALL_GROUP":"staff","INSTALL_MODE_FLAG":"u+w,go-w,a+rX","INSTALL_OWNER":"dabzshot","INSTALL_PATH":"/Applications","INSTALL_ROOT":"/tmp/Runner.dst","IPHONEOS_DEPLOYMENT_TARGET":"12.0","JAVAC_DEFAULT_FLAGS":"-J-Xms64m -J-XX:NewSize=4M -J-Dfile.encoding=UTF8","JAVA_APP_STUB":"/System/Library/Frameworks/JavaVM.framework/Resources/MacOS/JavaApplicationStub","JAVA_ARCHIVE_CLASSES":"YES","JAVA_ARCHIVE_TYPE":"JAR","JAVA_COMPILER":"/usr/bin/javac","JAVA_FOLDER_PATH":"Runner.app/Java","JAVA_FRAMEWORK_RESOURCES_DIRS":"Resources","JAVA_JAR_FLAGS":"cv","JAVA_SOURCE_SUBDIR":".","JAVA_USE_DEPENDENCIES":"YES","JAVA_ZIP_FLAGS":"-urg","JIKES_DEFAULT_FLAGS":"+E +OLDCSO","KASAN_DEFAULT_CFLAGS":"-DKASAN=1 -fsanitize=address -mllvm -asan-globals-live-support -mllvm -asan-force-dynamic-shadow","KEEP_PRIVATE_EXTERNS":"NO","LD_DEPENDENCY_INFO_FILE":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/undefined_arch/Runner_dependency_info.dat","LD_GENERATE_MAP_FILE":"NO","LD_MAP_FILE_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-LinkMap-normal-undefined_arch.txt","LD_NO_PIE":"NO","LD_QUOTE_LINKER_ARGUMENTS_FOR_COMPILER_DRIVER":"YES","LD_RUNPATH_SEARCH_PATHS":" /usr/lib/swift '@executable_path/Frameworks' '@loader_path/Frameworks' @executable_path/Frameworks","LEGACY_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/PlugIns/Xcode3Core.ideplugin/Contents/SharedSupport/Developer","LEX":"lex","LIBRARY_DEXT_INSTALL_PATH":"/Library/DriverExtensions","LIBRARY_FLAG_NOSPACE":"YES","LIBRARY_FLAG_PREFIX":"-l","LIBRARY_KEXT_INSTALL_PATH":"/Library/Extensions","LIBRARY_SEARCH_PATHS":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS15.0.sdk/usr/lib/swift /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Flutter","LINKER_DISPLAYS_MANGLED_NAMES":"NO","LINK_FILE_LIST_normal_arm64":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.LinkFileList","LINK_WITH_STANDARD_LIBRARIES":"YES","LLVM_TARGET_TRIPLE_OS_VERSION":"ios12.0","LLVM_TARGET_TRIPLE_VENDOR":"apple","LOCALIZATION_EXPORT_SUPPORTED":"YES","LOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app/en.lproj","LOCALIZED_STRING_MACRO_NAMES":"NSLocalizedString CFCopyLocalizedString","LOCALIZED_STRING_SWIFTUI_SUPPORT":"YES","LOCAL_ADMIN_APPS_DIR":"/Applications/Utilities","LOCAL_APPS_DIR":"/Applications","LOCAL_DEVELOPER_DIR":"/Library/Developer","LOCAL_LIBRARY_DIR":"/Library","LOCROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","LOCSYMROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","MACH_O_TYPE":"mh_execute","MAC_OS_X_PRODUCT_BUILD_VERSION":"20G95","MAC_OS_X_VERSION_ACTUAL":"110502","MAC_OS_X_VERSION_MAJOR":"110000","MAC_OS_X_VERSION_MINOR":"110500","MARKETING_VERSION":"2.0.86","METAL_LIBRARY_FILE_BASE":"default","METAL_LIBRARY_OUTPUT_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app","MODULES_FOLDER_PATH":"Runner.app/Modules","MODULE_CACHE_DIR":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex","MTL_ENABLE_DEBUG_INFO":"YES","NATIVE_ARCH":"x86_64","NATIVE_ARCH_32_BIT":"i386","NATIVE_ARCH_64_BIT":"x86_64","NATIVE_ARCH_ACTUAL":"x86_64","NO_COMMON":"YES","OBJECT_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects","OBJECT_FILE_DIR_normal":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal","OBJROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","ONLY_ACTIVE_ARCH":"NO","OS":"MACOS","OSAC":"/usr/bin/osacompile","OTHER_LDFLAGS":" -ObjC -l\"c++\" -l\"sqlite3\" -l\"z\" -framework \"AVFoundation\" -framework \"Accelerate\" -framework \"CoreGraphics\" -framework \"CoreImage\" -framework \"CoreMedia\" -framework \"CoreTelephony\" -framework \"CoreVideo\" -framework \"DTTJailbreakDetection\" -framework \"FBAEMKit\" -framework \"FBLPromises\" -framework \"FBSDKCoreKit\" -framework \"FBSDKCoreKit_Basics\" -framework \"FBSDKShareKit\" -framework \"FMDB\" -framework \"FirebaseAnalytics\" -framework \"FirebaseAuth\" -framework \"FirebaseCore\" -framework \"FirebaseCoreDiagnostics\" -framework \"FirebaseCrashlytics\" -framework \"FirebaseFirestore\" -framework \"FirebaseInstallations\" -framework \"FirebaseMessaging\" -framework \"FirebaseStorage\" -framework \"Foundation\" -framework \"GTMSessionFetcher\" -framework \"GoogleAppMeasurement\" -framework \"GoogleDataTransport\" -framework \"GoogleToolboxForMac\" -framework \"GoogleUtilities\" -framework \"GoogleUtilitiesComponents\" -framework \"IdensicMobileSDK\" -framework \"LocalAuthentication\" -framework \"MLImage\" -framework \"MLKitBarcodeScanning\" -framework \"MLKitCommon\" -framework \"MLKitVision\" -framework \"MTBBarcodeScanner\" -framework \"Protobuf\" -framework \"QuartzCore\" -framework \"Reachability\" -framework \"SafariServices\" -framework \"Security\" -framework \"StoreKit\" -framework \"SystemConfiguration\" -framework \"Toast\" -framework \"UIKit\" -framework \"absl\" -framework \"app_settings\" -framework \"camera\" -framework \"cloud_firestore\" -framework \"connectivity\" -framework \"contacts_service\" -framework \"firebase_analytics\" -framework \"firebase_auth\" -framework \"firebase_core\" -framework \"firebase_crashlytics\" -framework \"firebase_messaging\" -framework \"firebase_storage\" -framework \"flutter_appavailability\" -framework \"flutter_idensic_mobile_sdk_plugin\" -framework \"flutter_jailbreak_detection\" -framework \"flutter_local_notifications\" -framework \"flutter_native_timezone\" -framework \"flutter_secure_storage\" -framework \"fluttertoast\" -framework \"grpc\" -framework \"grpcpp\" -framework \"image_crop\" -framework \"image_gallery_saver\" -framework \"image_picker\" -framework \"keyboard_visibility\" -framework \"launch_review\" -framework \"leveldb\" -framework \"local_auth\" -framework \"nanopb\" -framework \"native_device_orientation\" -framework \"open_appstore\" -framework \"openssl_grpc\" -framework \"package_info\" -framework \"path_provider\" -framework \"permission\" -framework \"permission_handler\" -framework \"qr_code_scanner\" -framework \"qrcode_flutter\" -framework \"share\" -framework \"shared_preferences\" -framework \"simple_image_crop\" -framework \"sms_autofill\" -framework \"sms_receiver\" -framework \"social_share_plugin\" -framework \"sqflite\" -framework \"store_redirect\" -framework \"uni_links\" -framework \"url_launcher\" -framework \"video_player\" -framework \"webview_flutter\" -weak_framework \"UserNotifications\"","OTHER_SWIFT_FLAGS":" -D COCOAPODS","PACKAGE_CONFIG":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/.dart_tool/package_config.json","PACKAGE_TYPE":"com.apple.package-type.wrapper.application","PASCAL_STRINGS":"YES","PATH":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/libexec:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/local/bin:/Applications/Xcode.app/Contents/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/usr/local/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin","PATH_PREFIXES_EXCLUDED_FROM_HEADER_DEPENDENCIES":"/usr/include /usr/local/include /System/Library/Frameworks /System/Library/PrivateFrameworks /Applications/Xcode.app/Contents/Developer/Headers /Applications/Xcode.app/Contents/Developer/SDKs /Applications/Xcode.app/Contents/Developer/Platforms","PBDEVELOPMENTPLIST_PATH":"Runner.app/pbdevelopment.plist","PER_ARCH_OBJECT_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/undefined_arch","PER_VARIANT_OBJECT_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal","PKGINFO_FILE_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/PkgInfo","PKGINFO_PATH":"Runner.app/PkgInfo","PLATFORM_DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Applications","PLATFORM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin","PLATFORM_DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library","PLATFORM_DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs","PLATFORM_DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Tools","PLATFORM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr","PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform","PLATFORM_DISPLAY_NAME":"iOS","PLATFORM_FAMILY_NAME":"iOS","PLATFORM_NAME":"iphoneos","PLATFORM_PREFERRED_ARCH":"arm64","PLATFORM_PRODUCT_BUILD_VERSION":"19A339","PLIST_FILE_OUTPUT_FORMAT":"binary","PLUGINS_FOLDER_PATH":"Runner.app/PlugIns","PODS_BUILD_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","PODS_CONFIGURATION_BUILD_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos","PODS_PODFILE_DIR_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/.","PODS_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods","PODS_XCFRAMEWORKS_BUILD_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates","PRECOMPS_INCLUDE_HEADERS_FROM_BUILT_PRODUCTS_DIR":"YES","PRECOMP_DESTINATION_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/PrefixHeaders","PRESERVE_DEAD_CODE_INITS_AND_TERMS":"NO","PRIVATE_HEADERS_FOLDER_PATH":"Runner.app/PrivateHeaders","PRODUCT_BUNDLE_IDENTIFIER":"likewallet.likewallet","PRODUCT_BUNDLE_PACKAGE_TYPE":"APPL","PRODUCT_MODULE_NAME":"Runner","PRODUCT_NAME":"Runner","PRODUCT_SETTINGS_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/Info.plist","PRODUCT_TYPE":"com.apple.product-type.application","PROFILING_CODE":"NO","PROJECT":"Runner","PROJECT_DERIVED_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/DerivedSources","PROJECT_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","PROJECT_FILE_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner.xcodeproj","PROJECT_NAME":"Runner","PROJECT_TEMP_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build","PROJECT_TEMP_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","PROVISIONING_PROFILE_REQUIRED":"YES","PUBLIC_HEADERS_FOLDER_PATH":"Runner.app/Headers","RECURSIVE_SEARCH_PATHS_FOLLOW_SYMLINKS":"YES","REMOVE_CVS_FROM_RESOURCES":"YES","REMOVE_GIT_FROM_RESOURCES":"YES","REMOVE_HEADERS_FROM_EMBEDDED_BUNDLES":"YES","REMOVE_HG_FROM_RESOURCES":"YES","REMOVE_SVN_FROM_RESOURCES":"YES","RESOURCE_RULES_REQUIRED":"YES","REZ_COLLECTOR_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/ResourceManagerResources","REZ_OBJECTS_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/ResourceManagerResources/Objects","REZ_SEARCH_PATHS":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos ","SCAN_ALL_SOURCE_FILES_FOR_INCLUDES":"NO","SCRIPTS_FOLDER_PATH":"Runner.app/Scripts","SCRIPT_INPUT_FILE_0":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Podfile.lock","SCRIPT_INPUT_FILE_1":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/Manifest.lock","SCRIPT_INPUT_FILE_COUNT":"2","SCRIPT_INPUT_FILE_LIST_COUNT":"0","SCRIPT_OUTPUT_FILE_0":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Pods-Runner-checkManifestLockResult.txt","SCRIPT_OUTPUT_FILE_COUNT":"1","SCRIPT_OUTPUT_FILE_LIST_COUNT":"0","SDKROOT":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS15.0.sdk","SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS15.0.sdk","SDK_DIR_iphoneos":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS15.0.sdk","SDK_DIR_iphoneos15_0":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS15.0.sdk","SDK_NAME":"iphoneos15.0","SDK_NAMES":"iphoneos15.0","SDK_PRODUCT_BUILD_VERSION":"19A339","SDK_VERSION":"15.0","SDK_VERSION_ACTUAL":"150000","SDK_VERSION_MAJOR":"150000","SDK_VERSION_MINOR":"150000","SED":"/usr/bin/sed","SEPARATE_STRIP":"NO","SEPARATE_SYMBOL_EDIT":"NO","SET_DIR_MODE_OWNER_GROUP":"YES","SET_FILE_MODE_OWNER_GROUP":"NO","SHALLOW_BUNDLE":"YES","SHARED_DERIVED_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/DerivedSources","SHARED_FRAMEWORKS_FOLDER_PATH":"Runner.app/SharedFrameworks","SHARED_PRECOMPS_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/SharedPrecompiledHeaders","SHARED_SUPPORT_FOLDER_PATH":"Runner.app/SharedSupport","SKIP_INSTALL":"NO","SOURCE_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","SRCROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","STRINGSDATA_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/undefined_arch","STRINGSDATA_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build","STRINGS_FILE_INFOPLIST_RENAME":"YES","STRINGS_FILE_OUTPUT_ENCODING":"binary","STRIP_BITCODE_FROM_COPIED_FILES":"YES","STRIP_INSTALLED_PRODUCT":"NO","STRIP_STYLE":"all","STRIP_SWIFT_SYMBOLS":"YES","SUPPORTED_DEVICE_FAMILIES":"1,2","SUPPORTED_PLATFORMS":"iphoneos iphonesimulator","SUPPORTS_MACCATALYST":"NO","SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD":"YES","SUPPORTS_TEXT_BASED_API":"NO","SWIFT_EMIT_LOC_STRINGS":"NO","SWIFT_OBJC_BRIDGING_HEADER":"Runner/Runner-Bridging-Header.h","SWIFT_OPTIMIZATION_LEVEL":"-Onone","SWIFT_PLATFORM_TARGET_PREFIX":"ios","SWIFT_RESPONSE_FILE_PATH_normal_arm64":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.SwiftFileList","SWIFT_VERSION":"5.0","SYMROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","SYSTEM_ADMIN_APPS_DIR":"/Applications/Utilities","SYSTEM_APPS_DIR":"/Applications","SYSTEM_CORE_SERVICES_DIR":"/System/Library/CoreServices","SYSTEM_DEMOS_DIR":"/Applications/Extras","SYSTEM_DEVELOPER_APPS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","SYSTEM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","SYSTEM_DEVELOPER_DEMOS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities/Built Examples","SYSTEM_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","SYSTEM_DEVELOPER_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library","SYSTEM_DEVELOPER_GRAPHICS_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Graphics Tools","SYSTEM_DEVELOPER_JAVA_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Java Tools","SYSTEM_DEVELOPER_PERFORMANCE_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Performance Tools","SYSTEM_DEVELOPER_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes","SYSTEM_DEVELOPER_TOOLS":"/Applications/Xcode.app/Contents/Developer/Tools","SYSTEM_DEVELOPER_TOOLS_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/documentation/DeveloperTools","SYSTEM_DEVELOPER_TOOLS_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes/DeveloperTools","SYSTEM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","SYSTEM_DEVELOPER_UTILITIES_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities","SYSTEM_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","SYSTEM_DOCUMENTATION_DIR":"/Library/Documentation","SYSTEM_KEXT_INSTALL_PATH":"/System/Library/Extensions","SYSTEM_LIBRARY_DIR":"/System/Library","TAPI_VERIFY_MODE":"ErrorsOnly","TARGETED_DEVICE_FAMILY":"1,2","TARGETNAME":"Runner","TARGET_BUILD_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos","TARGET_NAME":"Runner","TARGET_TEMP_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build","TEMP_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build","TEMP_FILES_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build","TEMP_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build","TEMP_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","TEST_FRAMEWORK_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/Frameworks /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS15.0.sdk/Developer/Library/Frameworks","TEST_LIBRARY_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib","TOOLCHAINS":"com.apple.dt.toolchain.XcodeDefault","TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","TRACK_WIDGET_CREATION":"true","TREAT_MISSING_BASELINES_AS_TEST_FAILURES":"NO","TREE_SHAKE_ICONS":"false","UID":"501","UNLOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app","UNSTRIPPED_PRODUCT":"NO","USER":"dabzshot","USER_APPS_DIR":"/Users/<USER>/Applications","USER_LIBRARY_DIR":"/Users/<USER>/Library","USE_DYNAMIC_NO_PIC":"YES","USE_HEADERMAP":"YES","USE_HEADER_SYMLINKS":"NO","USE_LLVM_TARGET_TRIPLES":"YES","USE_LLVM_TARGET_TRIPLES_FOR_CLANG":"YES","USE_LLVM_TARGET_TRIPLES_FOR_LD":"YES","USE_LLVM_TARGET_TRIPLES_FOR_TAPI":"YES","USE_RECURSIVE_SCRIPT_INPUTS_IN_SCRIPT_PHASES":"YES","VALIDATE_DEVELOPMENT_ASSET_PATHS":"YES_ERROR","VALIDATE_PRODUCT":"NO","VALIDATE_WORKSPACE":"YES_ERROR","VALID_ARCHS":"arm64 arm64e armv7 armv7s","VERBOSE_PBXCP":"NO","VERSIONING_SYSTEM":"apple-generic","VERSIONPLIST_PATH":"Runner.app/version.plist","VERSION_INFO_BUILDER":"dabzshot","VERSION_INFO_FILE":"Runner_vers.c","VERSION_INFO_STRING":"\"@(#)PROGRAM:Runner  PROJECT:Runner-207\"","WRAPPER_EXTENSION":"app","WRAPPER_NAME":"Runner.app","WRAPPER_SUFFIX":".app","WRAP_ASSET_PACKS_IN_SEPARATE_DIRECTORIES":"NO","XCODE_APP_SUPPORT_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Xcode","XCODE_PRODUCT_BUILD_VERSION":"13A1030d","XCODE_VERSION_ACTUAL":"1310","XCODE_VERSION_MAJOR":"1300","XCODE_VERSION_MINOR":"1310","XPCSERVICES_FOLDER_PATH":"Runner.app/XPCServices","YACC":"yacc","arch":"undefined_arch","variant":"normal"},"allow-missing-inputs":true,"working-directory":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","control-enabled":false,"signature":"ca54a997215d1a1bf22e0af5f30feba7"}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:PhaseScriptExecution [CP] Embed Pods Frameworks /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-3759590B8F78A0B90ADCF815.sh": {"tool":"shell","description":"PhaseScriptExecution [CP] Embed Pods Frameworks /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-3759590B8F78A0B90ADCF815.sh","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/Target Support Files/Pods-Runner/Pods-Runner-frameworks.sh/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/BoringSSL-GRPC/openssl_grpc.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/DTTJailbreakDetection/DTTJailbreakDetection.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FMDB/FMDB.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseAuth/FirebaseAuth.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCore/FirebaseCore.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCoreDiagnostics/FirebaseCoreDiagnostics.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCrashlytics/FirebaseCrashlytics.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseFirestore/FirebaseFirestore.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseInstallations/FirebaseInstallations.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseMessaging/FirebaseMessaging.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseStorage/FirebaseStorage.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GTMSessionFetcher/GTMSessionFetcher.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleDataTransport/GoogleDataTransport.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleToolboxForMac/GoogleToolboxForMac.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleUtilities/GoogleUtilities.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/MTBBarcodeScanner/MTBBarcodeScanner.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/PromisesObjC/FBLPromises.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Protobuf/Protobuf.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Reachability/Reachability.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Toast/Toast.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/abseil/absl.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/app_settings/app_settings.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/camera/camera.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/connectivity/connectivity.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/contacts_service/contacts_service.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_appavailability/flutter_appavailability.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_idensic_mobile_sdk_plugin/flutter_idensic_mobile_sdk_plugin.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_jailbreak_detection/flutter_jailbreak_detection.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_local_notifications/flutter_local_notifications.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_native_timezone/flutter_native_timezone.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_secure_storage/flutter_secure_storage.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/fluttertoast/fluttertoast.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/gRPC-C++/grpcpp.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/gRPC-Core/grpc.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_crop/image_crop.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_gallery_saver/image_gallery_saver.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_picker/image_picker.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/keyboard_visibility/keyboard_visibility.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/launch_review/launch_review.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/leveldb-library/leveldb.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/local_auth/local_auth.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/nanopb/nanopb.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/native_device_orientation/native_device_orientation.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/open_appstore/open_appstore.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/package_info/package_info.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/path_provider/path_provider.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/permission/permission.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/qrcode_flutter/qrcode_flutter.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/share/share.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/shared_preferences/shared_preferences.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/simple_image_crop/simple_image_crop.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sms_autofill/sms_autofill.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sms_receiver/sms_receiver.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/social_share_plugin/social_share_plugin.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sqflite/sqflite.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/store_redirect/store_redirect.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/uni_links/uni_links.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/url_launcher/url_launcher.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/video_player/video_player.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/webview_flutter/webview_flutter.framework/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FBAEMKit/FBAEMKit.framework/FBAEMKit/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FBSDKCoreKit/FBSDKCoreKit.framework/FBSDKCoreKit/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FBSDKCoreKit_Basics/FBSDKCoreKit_Basics.framework/FBSDKCoreKit_Basics/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FBSDKShareKit/FBSDKShareKit.framework/FBSDKShareKit/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/IdensicMobileSDK/Core/IdensicMobileSDK.framework/IdensicMobileSDK/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-3759590B8F78A0B90ADCF815.sh","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase6-thin-binary>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/openssl_grpc.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/DTTJailbreakDetection.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FMDB.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseAuth.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseCore.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseCoreDiagnostics.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseCrashlytics.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseFirestore.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseInstallations.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseMessaging.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseStorage.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/GTMSessionFetcher.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/GoogleDataTransport.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/GoogleToolboxForMac.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/GoogleUtilities.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/MTBBarcodeScanner.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FBLPromises.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/Protobuf.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/Reachability.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/Toast.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/absl.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/app_settings.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/camera.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/connectivity.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/contacts_service.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_appavailability.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_idensic_mobile_sdk_plugin.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_jailbreak_detection.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_local_notifications.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_native_timezone.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_secure_storage.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/fluttertoast.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/grpcpp.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/grpc.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/image_crop.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/image_gallery_saver.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/image_picker.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/keyboard_visibility.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/launch_review.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/leveldb.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/local_auth.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/nanopb.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/native_device_orientation.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/open_appstore.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/package_info.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/path_provider.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/permission.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/qrcode_flutter.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/share.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/shared_preferences.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/simple_image_crop.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/sms_autofill.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/sms_receiver.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/social_share_plugin.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/sqflite.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/store_redirect.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/uni_links.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/url_launcher.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/video_player.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/webview_flutter.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FBAEMKit.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FBSDKCoreKit.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FBSDKCoreKit_Basics.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FBSDKShareKit.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/IdensicMobileSDK.framework"],"args":["/bin/sh","-c","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-3759590B8F78A0B90ADCF815.sh"],"env":{"ACTION":"build","AD_HOC_CODE_SIGNING_ALLOWED":"NO","ALLOW_TARGET_PLATFORM_SPECIALIZATION":"NO","ALTERNATE_GROUP":"staff","ALTERNATE_MODE":"u+w,go-w,a+rX","ALTERNATE_OWNER":"dabzshot","ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES":"YES","ALWAYS_SEARCH_USER_PATHS":"NO","ALWAYS_USE_SEPARATE_HEADERMAPS":"NO","APPLE_INTERNAL_DEVELOPER_DIR":"/AppleInternal/Developer","APPLE_INTERNAL_DIR":"/AppleInternal","APPLE_INTERNAL_DOCUMENTATION_DIR":"/AppleInternal/Documentation","APPLE_INTERNAL_LIBRARY_DIR":"/AppleInternal/Library","APPLE_INTERNAL_TOOLS":"/AppleInternal/Developer/Tools","APPLICATION_EXTENSION_API_ONLY":"NO","APPLY_RULES_IN_COPY_FILES":"NO","APPLY_RULES_IN_COPY_HEADERS":"NO","ARCHS":"arm64","ARCHS_STANDARD":"arm64","ARCHS_STANDARD_32_64_BIT":"armv7 arm64","ARCHS_STANDARD_32_BIT":"armv7","ARCHS_STANDARD_64_BIT":"arm64","ARCHS_STANDARD_INCLUDING_64_BIT":"arm64","ARCHS_UNIVERSAL_IPHONE_OS":"armv7 arm64","ASSETCATALOG_COMPILER_APPICON_NAME":"AppIcon","AVAILABLE_PLATFORMS":"appletvos appletvsimulator driverkit iphoneos iphonesimulator macosx watchos watchsimulator","BITCODE_GENERATION_MODE":"marker","BUILD_ACTIVE_RESOURCES_ONLY":"NO","BUILD_COMPONENTS":"headers build","BUILD_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","BUILD_LIBRARY_FOR_DISTRIBUTION":"NO","BUILD_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","BUILD_STYLE":"","BUILD_VARIANTS":"normal","BUILT_PRODUCTS_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos","BUNDLE_CONTENTS_FOLDER_PATH_deep":"Contents/","BUNDLE_EXECUTABLE_FOLDER_NAME_deep":"MacOS","BUNDLE_FORMAT":"shallow","BUNDLE_FRAMEWORKS_FOLDER_PATH":"Frameworks","BUNDLE_PLUGINS_FOLDER_PATH":"PlugIns","BUNDLE_PRIVATE_HEADERS_FOLDER_PATH":"PrivateHeaders","BUNDLE_PUBLIC_HEADERS_FOLDER_PATH":"Headers","CACHE_ROOT":"/var/folders/81/hsp6b80d6fn5sq0f3xxdpfd00000gn/C/com.apple.DeveloperTools/13.1-13A1030d/Xcode","CCHROOT":"/var/folders/81/hsp6b80d6fn5sq0f3xxdpfd00000gn/C/com.apple.DeveloperTools/13.1-13A1030d/Xcode","CHMOD":"/bin/chmod","CHOWN":"/usr/sbin/chown","CLANG_ANALYZER_NONNULL":"YES","CLANG_CXX_LANGUAGE_STANDARD":"gnu++0x","CLANG_CXX_LIBRARY":"libc++","CLANG_ENABLE_MODULES":"YES","CLANG_ENABLE_OBJC_ARC":"YES","CLANG_MODULES_BUILD_SESSION_FILE":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation","CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING":"YES","CLANG_WARN_BOOL_CONVERSION":"YES","CLANG_WARN_COMMA":"YES","CLANG_WARN_CONSTANT_CONVERSION":"YES","CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS":"YES","CLANG_WARN_DIRECT_OBJC_ISA_USAGE":"YES_ERROR","CLANG_WARN_EMPTY_BODY":"YES","CLANG_WARN_ENUM_CONVERSION":"YES","CLANG_WARN_INFINITE_RECURSION":"YES","CLANG_WARN_INT_CONVERSION":"YES","CLANG_WARN_NON_LITERAL_NULL_CONVERSION":"YES","CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF":"YES","CLANG_WARN_OBJC_LITERAL_CONVERSION":"YES","CLANG_WARN_OBJC_ROOT_CLASS":"YES_ERROR","CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER":"NO","CLANG_WARN_RANGE_LOOP_ANALYSIS":"YES","CLANG_WARN_STRICT_PROTOTYPES":"YES","CLANG_WARN_SUSPICIOUS_MOVE":"YES","CLANG_WARN_UNREACHABLE_CODE":"YES","CLANG_WARN__DUPLICATE_METHOD_MATCH":"YES","CLASS_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/JavaClasses","CLEAN_PRECOMPS":"YES","CLONE_HEADERS":"NO","COCOAPODS_PARALLEL_CODE_SIGN":"true","CODESIGNING_FOLDER_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app","CODE_SIGNING_ALLOWED":"YES","CODE_SIGNING_REQUIRED":"YES","CODE_SIGN_CONTEXT_CLASS":"XCiPhoneOSCodeSignContext","CODE_SIGN_ENTITLEMENTS":"Runner/Runner.entitlements","CODE_SIGN_IDENTITY":"Apple Development","CODE_SIGN_INJECT_BASE_ENTITLEMENTS":"YES","CODE_SIGN_STYLE":"Automatic","COLOR_DIAGNOSTICS":"NO","COMBINE_HIDPI_IMAGES":"NO","COMPILER_INDEX_STORE_ENABLE":"Default","COMPOSITE_SDK_DIRS":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/CompositeSDKs","COMPRESS_PNG_FILES":"YES","CONFIGURATION":"Debug","CONFIGURATION_BUILD_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos","CONFIGURATION_TEMP_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos","CONTENTS_FOLDER_PATH":"Runner.app","COPYING_PRESERVES_HFS_DATA":"NO","COPY_HEADERS_RUN_UNIFDEF":"NO","COPY_PHASE_STRIP":"NO","COPY_RESOURCES_FROM_STATIC_FRAMEWORKS":"YES","CORRESPONDING_SIMULATOR_PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform","CORRESPONDING_SIMULATOR_PLATFORM_NAME":"iphonesimulator","CORRESPONDING_SIMULATOR_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator15.0.sdk","CORRESPONDING_SIMULATOR_SDK_NAME":"iphonesimulator15.0","CP":"/bin/cp","CREATE_INFOPLIST_SECTION_IN_BINARY":"NO","CURRENT_ARCH":"undefined_arch","CURRENT_PROJECT_VERSION":"207","CURRENT_VARIANT":"normal","DART_DEFINES":"Zmx1dHRlci5pbnNwZWN0b3Iuc3RydWN0dXJlZEVycm9ycz10cnVl,RkxVVFRFUl9XRUJfQVVUT19ERVRFQ1Q9dHJ1ZQ==","DART_OBFUSCATION":"false","DEAD_CODE_STRIPPING":"YES","DEBUGGING_SYMBOLS":"YES","DEBUG_INFORMATION_FORMAT":"dwarf","DEFAULT_COMPILER":"com.apple.compilers.llvm.clang.1_0","DEFAULT_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","DEFAULT_KEXT_INSTALL_PATH":"/System/Library/Extensions","DEFINES_MODULE":"NO","DEPLOYMENT_LOCATION":"NO","DEPLOYMENT_POSTPROCESSING":"NO","DEPLOYMENT_TARGET_CLANG_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_CLANG_FLAG_NAME":"miphoneos-version-min","DEPLOYMENT_TARGET_CLANG_FLAG_PREFIX":"-miphoneos-version-min=","DEPLOYMENT_TARGET_LD_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_LD_FLAG_NAME":"ios_version_min","DEPLOYMENT_TARGET_SETTING_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_SUGGESTED_VALUES":"9.0 9.1 9.2 9.3 10.0 10.1 10.2 10.3 11.0 11.1 11.2 11.3 11.4 12.0 12.1 12.2 12.3 12.4 13.0 13.1 13.2 13.3 13.4 13.5 13.6 14.0 14.1 14.2 14.3 14.4 14.5 14.6 14.7 15.0","DERIVED_FILES_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources","DERIVED_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources","DERIVED_SOURCES_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources","DERIVE_MACCATALYST_PRODUCT_BUNDLE_IDENTIFIER":"NO","DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","DEVELOPER_FRAMEWORKS_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_FRAMEWORKS_DIR_QUOTED":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Library","DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs","DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Tools","DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","DEVELOPMENT_LANGUAGE":"en","DEVELOPMENT_TEAM":"HJAN5D475T","DOCUMENTATION_FOLDER_PATH":"Runner.app/en.lproj/Documentation","DONT_GENERATE_INFOPLIST_FILE":"NO","DO_HEADER_SCANNING_IN_JAM":"NO","DSTROOT":"/tmp/Runner.dst","DT_TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","DWARF_DSYM_FILE_NAME":"Runner.app.dSYM","DWARF_DSYM_FILE_SHOULD_ACCOMPANY_PRODUCT":"NO","DWARF_DSYM_FOLDER_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos","EFFECTIVE_PLATFORM_NAME":"-iphoneos","EMBEDDED_CONTENT_CONTAINS_SWIFT":"NO","EMBEDDED_PROFILE_NAME":"embedded.mobileprovision","EMBED_ASSET_PACKS_IN_PRODUCT_BUNDLE":"NO","ENABLE_APP_SANDBOX":"NO","ENABLE_BITCODE":"NO","ENABLE_DEFAULT_HEADER_SEARCH_PATHS":"YES","ENABLE_HARDENED_RUNTIME":"NO","ENABLE_HEADER_DEPENDENCIES":"YES","ENABLE_ON_DEMAND_RESOURCES":"YES","ENABLE_PREVIEWS":"NO","ENABLE_STRICT_OBJC_MSGSEND":"YES","ENABLE_TESTABILITY":"YES","ENABLE_TESTING_SEARCH_PATHS":"NO","ENTITLEMENTS_ALLOWED":"YES","ENTITLEMENTS_DESTINATION":"Signature","ENTITLEMENTS_REQUIRED":"YES","EXCLUDED_INSTALLSRC_SUBDIRECTORY_PATTERNS":".DS_Store .svn .git .hg CVS","EXCLUDED_RECURSIVE_SEARCH_PATH_SUBDIRECTORIES":"*.nib *.lproj *.framework *.gch *.xcode* *.xcassets (*) .DS_Store CVS .svn .git .hg *.pbproj *.pbxproj","EXECUTABLES_FOLDER_PATH":"Runner.app/Executables","EXECUTABLE_FOLDER_PATH":"Runner.app","EXECUTABLE_NAME":"Runner","EXECUTABLE_PATH":"Runner.app/Runner","EXTRA_FRONT_END_OPTIONS":"--no-sound-null-safety","FILE_LIST":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects/LinkFileList","FIXED_FILES_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/FixedFiles","FLUTTER_APPLICATION_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3","FLUTTER_BUILD_DIR":"build","FLUTTER_BUILD_NAME":"2.0.36","FLUTTER_BUILD_NUMBER":"191","FLUTTER_ROOT":"/Users/<USER>/Desktop/flutter","FLUTTER_TARGET":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/lib/main.dart","FRAMEWORKS_FOLDER_PATH":"Runner.app/Frameworks","FRAMEWORK_FLAG_PREFIX":"-framework","FRAMEWORK_SEARCH_PATHS":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos  \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/BoringSSL-GRPC\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/DTTJailbreakDetection\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FMDB\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseAuth\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCore\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCoreDiagnostics\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCrashlytics\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseFirestore\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseInstallations\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseMessaging\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseStorage\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GTMSessionFetcher\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleDataTransport\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleToolboxForMac\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleUtilities\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleUtilitiesComponents\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/MTBBarcodeScanner\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/PromisesObjC\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Protobuf\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Reachability\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Toast\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/abseil\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/app_settings\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/camera\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/cloud_firestore\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/connectivity\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/contacts_service\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_analytics\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_auth\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_core\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_crashlytics\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_messaging\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_storage\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_appavailability\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_idensic_mobile_sdk_plugin\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_jailbreak_detection\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_local_notifications\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_native_timezone\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_secure_storage\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/fluttertoast\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/gRPC-C++\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/gRPC-Core\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_crop\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_gallery_saver\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_picker\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/keyboard_visibility\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/launch_review\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/leveldb-library\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/local_auth\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/nanopb\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/native_device_orientation\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/open_appstore\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/package_info\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/path_provider\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/permission\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/permission_handler\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/qr_code_scanner\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/qrcode_flutter\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/share\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/shared_preferences\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/simple_image_crop\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sms_autofill\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sms_receiver\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/social_share_plugin\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sqflite\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/store_redirect\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/uni_links\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/url_launcher\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/video_player\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/webview_flutter\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/FBAEMKit/XCFrameworks\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/FBSDKCoreKit/XCFrameworks\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/FBSDKCoreKit_Basics/XCFrameworks\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/FBSDKShareKit/XCFrameworks\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/FirebaseAnalytics/Frameworks\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/GoogleAppMeasurement/Frameworks\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/IdensicMobileSDK\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/MLImage/Frameworks\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/MLKitBarcodeScanning/Frameworks\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/MLKitCommon/Frameworks\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/MLKitVision/Frameworks\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FBAEMKit\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FBSDKCoreKit\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FBSDKCoreKit_Basics\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FBSDKShareKit\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FirebaseAnalytics/AdIdSupport\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/GoogleAppMeasurement/AdIdSupport\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/IdensicMobileSDK/Core\" /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Flutter","FRAMEWORK_VERSION":"A","FULL_PRODUCT_NAME":"Runner.app","GCC3_VERSION":"3.3","GCC_C_LANGUAGE_STANDARD":"gnu99","GCC_DYNAMIC_NO_PIC":"NO","GCC_INLINES_ARE_PRIVATE_EXTERN":"YES","GCC_NO_COMMON_BLOCKS":"YES","GCC_OPTIMIZATION_LEVEL":"0","GCC_PFE_FILE_C_DIALECTS":"c objective-c c++ objective-c++","GCC_PREPROCESSOR_DEFINITIONS":"DEBUG=1  COCOAPODS=1 DEBUG=1  GPB_USE_PROTOBUF_FRAMEWORK_IMPORTS=1 DEBUG=1  PB_FIELD_32BIT=1 PB_NO_PACKED_STRUCTS=1 PB_ENABLE_MALLOC=1","GCC_SYMBOLS_PRIVATE_EXTERN":"NO","GCC_THUMB_SUPPORT":"YES","GCC_TREAT_WARNINGS_AS_ERRORS":"NO","GCC_VERSION":"com.apple.compilers.llvm.clang.1_0","GCC_VERSION_IDENTIFIER":"com_apple_compilers_llvm_clang_1_0","GCC_WARN_64_TO_32_BIT_CONVERSION":"YES","GCC_WARN_ABOUT_RETURN_TYPE":"YES_ERROR","GCC_WARN_UNDECLARED_SELECTOR":"YES","GCC_WARN_UNINITIALIZED_AUTOS":"YES_AGGRESSIVE","GCC_WARN_UNUSED_FUNCTION":"YES","GCC_WARN_UNUSED_VARIABLE":"YES","GENERATED_MODULEMAP_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/GeneratedModuleMaps-iphoneos","GENERATE_INFOPLIST_FILE":"NO","GENERATE_MASTER_OBJECT_FILE":"NO","GENERATE_PKGINFO_FILE":"YES","GENERATE_PROFILING_CODE":"NO","GENERATE_TEXT_BASED_STUBS":"NO","GID":"20","GROUP":"staff","HEADERMAP_INCLUDES_FLAT_ENTRIES_FOR_TARGET_BEING_BUILT":"YES","HEADERMAP_INCLUDES_FRAMEWORK_ENTRIES_FOR_ALL_PRODUCT_TYPES":"YES","HEADERMAP_INCLUDES_NONPUBLIC_NONPRIVATE_HEADERS":"YES","HEADERMAP_INCLUDES_PROJECT_HEADERS":"YES","HEADERMAP_USES_FRAMEWORK_PREFIX_ENTRIES":"YES","HEADERMAP_USES_VFS":"NO","HEADER_SEARCH_PATHS":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/include  \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/BoringSSL-GRPC/openssl_grpc.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/DTTJailbreakDetection/DTTJailbreakDetection.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FMDB/FMDB.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseAuth/FirebaseAuth.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCore/FirebaseCore.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCoreDiagnostics/FirebaseCoreDiagnostics.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCrashlytics/FirebaseCrashlytics.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseFirestore/FirebaseFirestore.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseInstallations/FirebaseInstallations.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseMessaging/FirebaseMessaging.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseStorage/FirebaseStorage.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GTMSessionFetcher/GTMSessionFetcher.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleDataTransport/GoogleDataTransport.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleToolboxForMac/GoogleToolboxForMac.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleUtilities/GoogleUtilities.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleUtilitiesComponents/GoogleUtilitiesComponents.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/MTBBarcodeScanner/MTBBarcodeScanner.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/PromisesObjC/FBLPromises.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Protobuf/Protobuf.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Reachability/Reachability.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Toast/Toast.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/abseil/absl.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/app_settings/app_settings.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/camera/camera.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/cloud_firestore/cloud_firestore.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/connectivity/connectivity.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/contacts_service/contacts_service.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_analytics/firebase_analytics.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_auth/firebase_auth.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_core/firebase_core.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_crashlytics/firebase_crashlytics.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_messaging/firebase_messaging.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/firebase_storage/firebase_storage.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_appavailability/flutter_appavailability.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_idensic_mobile_sdk_plugin/flutter_idensic_mobile_sdk_plugin.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_jailbreak_detection/flutter_jailbreak_detection.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_local_notifications/flutter_local_notifications.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_native_timezone/flutter_native_timezone.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_secure_storage/flutter_secure_storage.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/fluttertoast/fluttertoast.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/gRPC-C++/grpcpp.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/gRPC-Core/grpc.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_crop/image_crop.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_gallery_saver/image_gallery_saver.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_picker/image_picker.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/keyboard_visibility/keyboard_visibility.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/launch_review/launch_review.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/leveldb-library/leveldb.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/local_auth/local_auth.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/nanopb/nanopb.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/native_device_orientation/native_device_orientation.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/open_appstore/open_appstore.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/package_info/package_info.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/path_provider/path_provider.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/permission/permission.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/permission_handler/permission_handler.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/qr_code_scanner/qr_code_scanner.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/qrcode_flutter/qrcode_flutter.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/share/share.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/shared_preferences/shared_preferences.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/simple_image_crop/simple_image_crop.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sms_autofill/sms_autofill.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sms_receiver/sms_receiver.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/social_share_plugin/social_share_plugin.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sqflite/sqflite.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/store_redirect/store_redirect.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/uni_links/uni_links.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/url_launcher/url_launcher.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/video_player/video_player.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/webview_flutter/webview_flutter.framework/Headers\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/Headers/Public\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/Headers/Public/Firebase\" \"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/Headers/Public/GoogleMLKit\"  /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/Firebase/CoreOnly/Sources  /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/GoogleMLKit/MLKitCore/Sources \"/Sources/FBLPromises/include\"","HIDE_BITCODE_SYMBOLS":"YES","HOME":"/Users/<USER>","ICONV":"/usr/bin/iconv","INFOPLIST_EXPAND_BUILD_SETTINGS":"YES","INFOPLIST_FILE":"Runner/Info.plist","INFOPLIST_OUTPUT_FORMAT":"binary","INFOPLIST_PATH":"Runner.app/Info.plist","INFOPLIST_PREPROCESS":"NO","INFOSTRINGS_PATH":"Runner.app/en.lproj/InfoPlist.strings","INLINE_PRIVATE_FRAMEWORKS":"NO","INSTALLHDRS_COPY_PHASE":"NO","INSTALLHDRS_SCRIPT_PHASE":"NO","INSTALL_DIR":"/tmp/Runner.dst/Applications","INSTALL_GROUP":"staff","INSTALL_MODE_FLAG":"u+w,go-w,a+rX","INSTALL_OWNER":"dabzshot","INSTALL_PATH":"/Applications","INSTALL_ROOT":"/tmp/Runner.dst","IPHONEOS_DEPLOYMENT_TARGET":"12.0","JAVAC_DEFAULT_FLAGS":"-J-Xms64m -J-XX:NewSize=4M -J-Dfile.encoding=UTF8","JAVA_APP_STUB":"/System/Library/Frameworks/JavaVM.framework/Resources/MacOS/JavaApplicationStub","JAVA_ARCHIVE_CLASSES":"YES","JAVA_ARCHIVE_TYPE":"JAR","JAVA_COMPILER":"/usr/bin/javac","JAVA_FOLDER_PATH":"Runner.app/Java","JAVA_FRAMEWORK_RESOURCES_DIRS":"Resources","JAVA_JAR_FLAGS":"cv","JAVA_SOURCE_SUBDIR":".","JAVA_USE_DEPENDENCIES":"YES","JAVA_ZIP_FLAGS":"-urg","JIKES_DEFAULT_FLAGS":"+E +OLDCSO","KASAN_DEFAULT_CFLAGS":"-DKASAN=1 -fsanitize=address -mllvm -asan-globals-live-support -mllvm -asan-force-dynamic-shadow","KEEP_PRIVATE_EXTERNS":"NO","LD_DEPENDENCY_INFO_FILE":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/undefined_arch/Runner_dependency_info.dat","LD_GENERATE_MAP_FILE":"NO","LD_MAP_FILE_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-LinkMap-normal-undefined_arch.txt","LD_NO_PIE":"NO","LD_QUOTE_LINKER_ARGUMENTS_FOR_COMPILER_DRIVER":"YES","LD_RUNPATH_SEARCH_PATHS":" /usr/lib/swift '@executable_path/Frameworks' '@loader_path/Frameworks' @executable_path/Frameworks","LEGACY_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/PlugIns/Xcode3Core.ideplugin/Contents/SharedSupport/Developer","LEX":"lex","LIBRARY_DEXT_INSTALL_PATH":"/Library/DriverExtensions","LIBRARY_FLAG_NOSPACE":"YES","LIBRARY_FLAG_PREFIX":"-l","LIBRARY_KEXT_INSTALL_PATH":"/Library/Extensions","LIBRARY_SEARCH_PATHS":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS15.0.sdk/usr/lib/swift /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Flutter","LINKER_DISPLAYS_MANGLED_NAMES":"NO","LINK_FILE_LIST_normal_arm64":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.LinkFileList","LINK_WITH_STANDARD_LIBRARIES":"YES","LLVM_TARGET_TRIPLE_OS_VERSION":"ios12.0","LLVM_TARGET_TRIPLE_VENDOR":"apple","LOCALIZATION_EXPORT_SUPPORTED":"YES","LOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app/en.lproj","LOCALIZED_STRING_MACRO_NAMES":"NSLocalizedString CFCopyLocalizedString","LOCALIZED_STRING_SWIFTUI_SUPPORT":"YES","LOCAL_ADMIN_APPS_DIR":"/Applications/Utilities","LOCAL_APPS_DIR":"/Applications","LOCAL_DEVELOPER_DIR":"/Library/Developer","LOCAL_LIBRARY_DIR":"/Library","LOCROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","LOCSYMROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","MACH_O_TYPE":"mh_execute","MAC_OS_X_PRODUCT_BUILD_VERSION":"20G95","MAC_OS_X_VERSION_ACTUAL":"110502","MAC_OS_X_VERSION_MAJOR":"110000","MAC_OS_X_VERSION_MINOR":"110500","MARKETING_VERSION":"2.0.86","METAL_LIBRARY_FILE_BASE":"default","METAL_LIBRARY_OUTPUT_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app","MODULES_FOLDER_PATH":"Runner.app/Modules","MODULE_CACHE_DIR":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex","MTL_ENABLE_DEBUG_INFO":"YES","NATIVE_ARCH":"x86_64","NATIVE_ARCH_32_BIT":"i386","NATIVE_ARCH_64_BIT":"x86_64","NATIVE_ARCH_ACTUAL":"x86_64","NO_COMMON":"YES","OBJECT_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects","OBJECT_FILE_DIR_normal":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal","OBJROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","ONLY_ACTIVE_ARCH":"NO","OS":"MACOS","OSAC":"/usr/bin/osacompile","OTHER_LDFLAGS":" -ObjC -l\"c++\" -l\"sqlite3\" -l\"z\" -framework \"AVFoundation\" -framework \"Accelerate\" -framework \"CoreGraphics\" -framework \"CoreImage\" -framework \"CoreMedia\" -framework \"CoreTelephony\" -framework \"CoreVideo\" -framework \"DTTJailbreakDetection\" -framework \"FBAEMKit\" -framework \"FBLPromises\" -framework \"FBSDKCoreKit\" -framework \"FBSDKCoreKit_Basics\" -framework \"FBSDKShareKit\" -framework \"FMDB\" -framework \"FirebaseAnalytics\" -framework \"FirebaseAuth\" -framework \"FirebaseCore\" -framework \"FirebaseCoreDiagnostics\" -framework \"FirebaseCrashlytics\" -framework \"FirebaseFirestore\" -framework \"FirebaseInstallations\" -framework \"FirebaseMessaging\" -framework \"FirebaseStorage\" -framework \"Foundation\" -framework \"GTMSessionFetcher\" -framework \"GoogleAppMeasurement\" -framework \"GoogleDataTransport\" -framework \"GoogleToolboxForMac\" -framework \"GoogleUtilities\" -framework \"GoogleUtilitiesComponents\" -framework \"IdensicMobileSDK\" -framework \"LocalAuthentication\" -framework \"MLImage\" -framework \"MLKitBarcodeScanning\" -framework \"MLKitCommon\" -framework \"MLKitVision\" -framework \"MTBBarcodeScanner\" -framework \"Protobuf\" -framework \"QuartzCore\" -framework \"Reachability\" -framework \"SafariServices\" -framework \"Security\" -framework \"StoreKit\" -framework \"SystemConfiguration\" -framework \"Toast\" -framework \"UIKit\" -framework \"absl\" -framework \"app_settings\" -framework \"camera\" -framework \"cloud_firestore\" -framework \"connectivity\" -framework \"contacts_service\" -framework \"firebase_analytics\" -framework \"firebase_auth\" -framework \"firebase_core\" -framework \"firebase_crashlytics\" -framework \"firebase_messaging\" -framework \"firebase_storage\" -framework \"flutter_appavailability\" -framework \"flutter_idensic_mobile_sdk_plugin\" -framework \"flutter_jailbreak_detection\" -framework \"flutter_local_notifications\" -framework \"flutter_native_timezone\" -framework \"flutter_secure_storage\" -framework \"fluttertoast\" -framework \"grpc\" -framework \"grpcpp\" -framework \"image_crop\" -framework \"image_gallery_saver\" -framework \"image_picker\" -framework \"keyboard_visibility\" -framework \"launch_review\" -framework \"leveldb\" -framework \"local_auth\" -framework \"nanopb\" -framework \"native_device_orientation\" -framework \"open_appstore\" -framework \"openssl_grpc\" -framework \"package_info\" -framework \"path_provider\" -framework \"permission\" -framework \"permission_handler\" -framework \"qr_code_scanner\" -framework \"qrcode_flutter\" -framework \"share\" -framework \"shared_preferences\" -framework \"simple_image_crop\" -framework \"sms_autofill\" -framework \"sms_receiver\" -framework \"social_share_plugin\" -framework \"sqflite\" -framework \"store_redirect\" -framework \"uni_links\" -framework \"url_launcher\" -framework \"video_player\" -framework \"webview_flutter\" -weak_framework \"UserNotifications\"","OTHER_SWIFT_FLAGS":" -D COCOAPODS","PACKAGE_CONFIG":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/.dart_tool/package_config.json","PACKAGE_TYPE":"com.apple.package-type.wrapper.application","PASCAL_STRINGS":"YES","PATH":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/libexec:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/local/bin:/Applications/Xcode.app/Contents/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/usr/local/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin","PATH_PREFIXES_EXCLUDED_FROM_HEADER_DEPENDENCIES":"/usr/include /usr/local/include /System/Library/Frameworks /System/Library/PrivateFrameworks /Applications/Xcode.app/Contents/Developer/Headers /Applications/Xcode.app/Contents/Developer/SDKs /Applications/Xcode.app/Contents/Developer/Platforms","PBDEVELOPMENTPLIST_PATH":"Runner.app/pbdevelopment.plist","PER_ARCH_OBJECT_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/undefined_arch","PER_VARIANT_OBJECT_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal","PKGINFO_FILE_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/PkgInfo","PKGINFO_PATH":"Runner.app/PkgInfo","PLATFORM_DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Applications","PLATFORM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin","PLATFORM_DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library","PLATFORM_DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs","PLATFORM_DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Tools","PLATFORM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr","PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform","PLATFORM_DISPLAY_NAME":"iOS","PLATFORM_FAMILY_NAME":"iOS","PLATFORM_NAME":"iphoneos","PLATFORM_PREFERRED_ARCH":"arm64","PLATFORM_PRODUCT_BUILD_VERSION":"19A339","PLIST_FILE_OUTPUT_FORMAT":"binary","PLUGINS_FOLDER_PATH":"Runner.app/PlugIns","PODS_BUILD_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","PODS_CONFIGURATION_BUILD_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos","PODS_PODFILE_DIR_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/.","PODS_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods","PODS_XCFRAMEWORKS_BUILD_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates","PRECOMPS_INCLUDE_HEADERS_FROM_BUILT_PRODUCTS_DIR":"YES","PRECOMP_DESTINATION_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/PrefixHeaders","PRESERVE_DEAD_CODE_INITS_AND_TERMS":"NO","PRIVATE_HEADERS_FOLDER_PATH":"Runner.app/PrivateHeaders","PRODUCT_BUNDLE_IDENTIFIER":"likewallet.likewallet","PRODUCT_BUNDLE_PACKAGE_TYPE":"APPL","PRODUCT_MODULE_NAME":"Runner","PRODUCT_NAME":"Runner","PRODUCT_SETTINGS_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/Info.plist","PRODUCT_TYPE":"com.apple.product-type.application","PROFILING_CODE":"NO","PROJECT":"Runner","PROJECT_DERIVED_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/DerivedSources","PROJECT_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","PROJECT_FILE_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner.xcodeproj","PROJECT_NAME":"Runner","PROJECT_TEMP_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build","PROJECT_TEMP_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","PROVISIONING_PROFILE_REQUIRED":"YES","PUBLIC_HEADERS_FOLDER_PATH":"Runner.app/Headers","RECURSIVE_SEARCH_PATHS_FOLLOW_SYMLINKS":"YES","REMOVE_CVS_FROM_RESOURCES":"YES","REMOVE_GIT_FROM_RESOURCES":"YES","REMOVE_HEADERS_FROM_EMBEDDED_BUNDLES":"YES","REMOVE_HG_FROM_RESOURCES":"YES","REMOVE_SVN_FROM_RESOURCES":"YES","RESOURCE_RULES_REQUIRED":"YES","REZ_COLLECTOR_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/ResourceManagerResources","REZ_OBJECTS_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/ResourceManagerResources/Objects","REZ_SEARCH_PATHS":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos ","SCAN_ALL_SOURCE_FILES_FOR_INCLUDES":"NO","SCRIPTS_FOLDER_PATH":"Runner.app/Scripts","SCRIPT_INPUT_FILE_0":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Pods/Target Support Files/Pods-Runner/Pods-Runner-frameworks.sh","SCRIPT_INPUT_FILE_1":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/BoringSSL-GRPC/openssl_grpc.framework","SCRIPT_INPUT_FILE_10":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseMessaging/FirebaseMessaging.framework","SCRIPT_INPUT_FILE_11":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseStorage/FirebaseStorage.framework","SCRIPT_INPUT_FILE_12":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GTMSessionFetcher/GTMSessionFetcher.framework","SCRIPT_INPUT_FILE_13":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleDataTransport/GoogleDataTransport.framework","SCRIPT_INPUT_FILE_14":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleToolboxForMac/GoogleToolboxForMac.framework","SCRIPT_INPUT_FILE_15":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/GoogleUtilities/GoogleUtilities.framework","SCRIPT_INPUT_FILE_16":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/MTBBarcodeScanner/MTBBarcodeScanner.framework","SCRIPT_INPUT_FILE_17":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/PromisesObjC/FBLPromises.framework","SCRIPT_INPUT_FILE_18":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Protobuf/Protobuf.framework","SCRIPT_INPUT_FILE_19":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Reachability/Reachability.framework","SCRIPT_INPUT_FILE_2":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/DTTJailbreakDetection/DTTJailbreakDetection.framework","SCRIPT_INPUT_FILE_20":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Toast/Toast.framework","SCRIPT_INPUT_FILE_21":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/abseil/absl.framework","SCRIPT_INPUT_FILE_22":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/app_settings/app_settings.framework","SCRIPT_INPUT_FILE_23":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/camera/camera.framework","SCRIPT_INPUT_FILE_24":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/connectivity/connectivity.framework","SCRIPT_INPUT_FILE_25":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/contacts_service/contacts_service.framework","SCRIPT_INPUT_FILE_26":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_appavailability/flutter_appavailability.framework","SCRIPT_INPUT_FILE_27":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_idensic_mobile_sdk_plugin/flutter_idensic_mobile_sdk_plugin.framework","SCRIPT_INPUT_FILE_28":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_jailbreak_detection/flutter_jailbreak_detection.framework","SCRIPT_INPUT_FILE_29":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_local_notifications/flutter_local_notifications.framework","SCRIPT_INPUT_FILE_3":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FMDB/FMDB.framework","SCRIPT_INPUT_FILE_30":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_native_timezone/flutter_native_timezone.framework","SCRIPT_INPUT_FILE_31":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/flutter_secure_storage/flutter_secure_storage.framework","SCRIPT_INPUT_FILE_32":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/fluttertoast/fluttertoast.framework","SCRIPT_INPUT_FILE_33":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/gRPC-C++/grpcpp.framework","SCRIPT_INPUT_FILE_34":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/gRPC-Core/grpc.framework","SCRIPT_INPUT_FILE_35":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_crop/image_crop.framework","SCRIPT_INPUT_FILE_36":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_gallery_saver/image_gallery_saver.framework","SCRIPT_INPUT_FILE_37":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/image_picker/image_picker.framework","SCRIPT_INPUT_FILE_38":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/keyboard_visibility/keyboard_visibility.framework","SCRIPT_INPUT_FILE_39":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/launch_review/launch_review.framework","SCRIPT_INPUT_FILE_4":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseAuth/FirebaseAuth.framework","SCRIPT_INPUT_FILE_40":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/leveldb-library/leveldb.framework","SCRIPT_INPUT_FILE_41":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/local_auth/local_auth.framework","SCRIPT_INPUT_FILE_42":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/nanopb/nanopb.framework","SCRIPT_INPUT_FILE_43":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/native_device_orientation/native_device_orientation.framework","SCRIPT_INPUT_FILE_44":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/open_appstore/open_appstore.framework","SCRIPT_INPUT_FILE_45":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/package_info/package_info.framework","SCRIPT_INPUT_FILE_46":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/path_provider/path_provider.framework","SCRIPT_INPUT_FILE_47":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/permission/permission.framework","SCRIPT_INPUT_FILE_48":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/qrcode_flutter/qrcode_flutter.framework","SCRIPT_INPUT_FILE_49":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/share/share.framework","SCRIPT_INPUT_FILE_5":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCore/FirebaseCore.framework","SCRIPT_INPUT_FILE_50":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/shared_preferences/shared_preferences.framework","SCRIPT_INPUT_FILE_51":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/simple_image_crop/simple_image_crop.framework","SCRIPT_INPUT_FILE_52":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sms_autofill/sms_autofill.framework","SCRIPT_INPUT_FILE_53":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sms_receiver/sms_receiver.framework","SCRIPT_INPUT_FILE_54":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/social_share_plugin/social_share_plugin.framework","SCRIPT_INPUT_FILE_55":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/sqflite/sqflite.framework","SCRIPT_INPUT_FILE_56":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/store_redirect/store_redirect.framework","SCRIPT_INPUT_FILE_57":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/uni_links/uni_links.framework","SCRIPT_INPUT_FILE_58":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/url_launcher/url_launcher.framework","SCRIPT_INPUT_FILE_59":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/video_player/video_player.framework","SCRIPT_INPUT_FILE_6":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCoreDiagnostics/FirebaseCoreDiagnostics.framework","SCRIPT_INPUT_FILE_60":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/webview_flutter/webview_flutter.framework","SCRIPT_INPUT_FILE_61":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FBAEMKit/FBAEMKit.framework/FBAEMKit","SCRIPT_INPUT_FILE_62":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FBSDKCoreKit/FBSDKCoreKit.framework/FBSDKCoreKit","SCRIPT_INPUT_FILE_63":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FBSDKCoreKit_Basics/FBSDKCoreKit_Basics.framework/FBSDKCoreKit_Basics","SCRIPT_INPUT_FILE_64":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/FBSDKShareKit/FBSDKShareKit.framework/FBSDKShareKit","SCRIPT_INPUT_FILE_65":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/XCFrameworkIntermediates/IdensicMobileSDK/Core/IdensicMobileSDK.framework/IdensicMobileSDK","SCRIPT_INPUT_FILE_7":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseCrashlytics/FirebaseCrashlytics.framework","SCRIPT_INPUT_FILE_8":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseFirestore/FirebaseFirestore.framework","SCRIPT_INPUT_FILE_9":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/FirebaseInstallations/FirebaseInstallations.framework","SCRIPT_INPUT_FILE_COUNT":"66","SCRIPT_INPUT_FILE_LIST_COUNT":"0","SCRIPT_OUTPUT_FILE_0":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/openssl_grpc.framework","SCRIPT_OUTPUT_FILE_1":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/DTTJailbreakDetection.framework","SCRIPT_OUTPUT_FILE_10":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseStorage.framework","SCRIPT_OUTPUT_FILE_11":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/GTMSessionFetcher.framework","SCRIPT_OUTPUT_FILE_12":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/GoogleDataTransport.framework","SCRIPT_OUTPUT_FILE_13":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/GoogleToolboxForMac.framework","SCRIPT_OUTPUT_FILE_14":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/GoogleUtilities.framework","SCRIPT_OUTPUT_FILE_15":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/MTBBarcodeScanner.framework","SCRIPT_OUTPUT_FILE_16":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FBLPromises.framework","SCRIPT_OUTPUT_FILE_17":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/Protobuf.framework","SCRIPT_OUTPUT_FILE_18":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/Reachability.framework","SCRIPT_OUTPUT_FILE_19":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/Toast.framework","SCRIPT_OUTPUT_FILE_2":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FMDB.framework","SCRIPT_OUTPUT_FILE_20":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/absl.framework","SCRIPT_OUTPUT_FILE_21":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/app_settings.framework","SCRIPT_OUTPUT_FILE_22":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/camera.framework","SCRIPT_OUTPUT_FILE_23":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/connectivity.framework","SCRIPT_OUTPUT_FILE_24":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/contacts_service.framework","SCRIPT_OUTPUT_FILE_25":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_appavailability.framework","SCRIPT_OUTPUT_FILE_26":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_idensic_mobile_sdk_plugin.framework","SCRIPT_OUTPUT_FILE_27":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_jailbreak_detection.framework","SCRIPT_OUTPUT_FILE_28":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_local_notifications.framework","SCRIPT_OUTPUT_FILE_29":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_native_timezone.framework","SCRIPT_OUTPUT_FILE_3":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseAuth.framework","SCRIPT_OUTPUT_FILE_30":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_secure_storage.framework","SCRIPT_OUTPUT_FILE_31":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/fluttertoast.framework","SCRIPT_OUTPUT_FILE_32":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/grpcpp.framework","SCRIPT_OUTPUT_FILE_33":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/grpc.framework","SCRIPT_OUTPUT_FILE_34":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/image_crop.framework","SCRIPT_OUTPUT_FILE_35":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/image_gallery_saver.framework","SCRIPT_OUTPUT_FILE_36":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/image_picker.framework","SCRIPT_OUTPUT_FILE_37":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/keyboard_visibility.framework","SCRIPT_OUTPUT_FILE_38":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/launch_review.framework","SCRIPT_OUTPUT_FILE_39":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/leveldb.framework","SCRIPT_OUTPUT_FILE_4":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseCore.framework","SCRIPT_OUTPUT_FILE_40":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/local_auth.framework","SCRIPT_OUTPUT_FILE_41":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/nanopb.framework","SCRIPT_OUTPUT_FILE_42":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/native_device_orientation.framework","SCRIPT_OUTPUT_FILE_43":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/open_appstore.framework","SCRIPT_OUTPUT_FILE_44":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/package_info.framework","SCRIPT_OUTPUT_FILE_45":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/path_provider.framework","SCRIPT_OUTPUT_FILE_46":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/permission.framework","SCRIPT_OUTPUT_FILE_47":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/qrcode_flutter.framework","SCRIPT_OUTPUT_FILE_48":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/share.framework","SCRIPT_OUTPUT_FILE_49":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/shared_preferences.framework","SCRIPT_OUTPUT_FILE_5":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseCoreDiagnostics.framework","SCRIPT_OUTPUT_FILE_50":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/simple_image_crop.framework","SCRIPT_OUTPUT_FILE_51":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/sms_autofill.framework","SCRIPT_OUTPUT_FILE_52":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/sms_receiver.framework","SCRIPT_OUTPUT_FILE_53":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/social_share_plugin.framework","SCRIPT_OUTPUT_FILE_54":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/sqflite.framework","SCRIPT_OUTPUT_FILE_55":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/store_redirect.framework","SCRIPT_OUTPUT_FILE_56":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/uni_links.framework","SCRIPT_OUTPUT_FILE_57":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/url_launcher.framework","SCRIPT_OUTPUT_FILE_58":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/video_player.framework","SCRIPT_OUTPUT_FILE_59":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/webview_flutter.framework","SCRIPT_OUTPUT_FILE_6":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseCrashlytics.framework","SCRIPT_OUTPUT_FILE_60":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FBAEMKit.framework","SCRIPT_OUTPUT_FILE_61":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FBSDKCoreKit.framework","SCRIPT_OUTPUT_FILE_62":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FBSDKCoreKit_Basics.framework","SCRIPT_OUTPUT_FILE_63":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FBSDKShareKit.framework","SCRIPT_OUTPUT_FILE_64":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/IdensicMobileSDK.framework","SCRIPT_OUTPUT_FILE_7":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseFirestore.framework","SCRIPT_OUTPUT_FILE_8":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseInstallations.framework","SCRIPT_OUTPUT_FILE_9":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Frameworks/FirebaseMessaging.framework","SCRIPT_OUTPUT_FILE_COUNT":"65","SCRIPT_OUTPUT_FILE_LIST_COUNT":"0","SDKROOT":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS15.0.sdk","SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS15.0.sdk","SDK_DIR_iphoneos":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS15.0.sdk","SDK_DIR_iphoneos15_0":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS15.0.sdk","SDK_NAME":"iphoneos15.0","SDK_NAMES":"iphoneos15.0","SDK_PRODUCT_BUILD_VERSION":"19A339","SDK_VERSION":"15.0","SDK_VERSION_ACTUAL":"150000","SDK_VERSION_MAJOR":"150000","SDK_VERSION_MINOR":"150000","SED":"/usr/bin/sed","SEPARATE_STRIP":"NO","SEPARATE_SYMBOL_EDIT":"NO","SET_DIR_MODE_OWNER_GROUP":"YES","SET_FILE_MODE_OWNER_GROUP":"NO","SHALLOW_BUNDLE":"YES","SHARED_DERIVED_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/DerivedSources","SHARED_FRAMEWORKS_FOLDER_PATH":"Runner.app/SharedFrameworks","SHARED_PRECOMPS_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/SharedPrecompiledHeaders","SHARED_SUPPORT_FOLDER_PATH":"Runner.app/SharedSupport","SKIP_INSTALL":"NO","SOURCE_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","SRCROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","STRINGSDATA_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/undefined_arch","STRINGSDATA_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build","STRINGS_FILE_INFOPLIST_RENAME":"YES","STRINGS_FILE_OUTPUT_ENCODING":"binary","STRIP_BITCODE_FROM_COPIED_FILES":"YES","STRIP_INSTALLED_PRODUCT":"NO","STRIP_STYLE":"all","STRIP_SWIFT_SYMBOLS":"YES","SUPPORTED_DEVICE_FAMILIES":"1,2","SUPPORTED_PLATFORMS":"iphoneos iphonesimulator","SUPPORTS_MACCATALYST":"NO","SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD":"YES","SUPPORTS_TEXT_BASED_API":"NO","SWIFT_EMIT_LOC_STRINGS":"NO","SWIFT_OBJC_BRIDGING_HEADER":"Runner/Runner-Bridging-Header.h","SWIFT_OPTIMIZATION_LEVEL":"-Onone","SWIFT_PLATFORM_TARGET_PREFIX":"ios","SWIFT_RESPONSE_FILE_PATH_normal_arm64":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.SwiftFileList","SWIFT_VERSION":"5.0","SYMROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","SYSTEM_ADMIN_APPS_DIR":"/Applications/Utilities","SYSTEM_APPS_DIR":"/Applications","SYSTEM_CORE_SERVICES_DIR":"/System/Library/CoreServices","SYSTEM_DEMOS_DIR":"/Applications/Extras","SYSTEM_DEVELOPER_APPS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","SYSTEM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","SYSTEM_DEVELOPER_DEMOS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities/Built Examples","SYSTEM_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","SYSTEM_DEVELOPER_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library","SYSTEM_DEVELOPER_GRAPHICS_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Graphics Tools","SYSTEM_DEVELOPER_JAVA_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Java Tools","SYSTEM_DEVELOPER_PERFORMANCE_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Performance Tools","SYSTEM_DEVELOPER_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes","SYSTEM_DEVELOPER_TOOLS":"/Applications/Xcode.app/Contents/Developer/Tools","SYSTEM_DEVELOPER_TOOLS_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/documentation/DeveloperTools","SYSTEM_DEVELOPER_TOOLS_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes/DeveloperTools","SYSTEM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","SYSTEM_DEVELOPER_UTILITIES_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities","SYSTEM_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","SYSTEM_DOCUMENTATION_DIR":"/Library/Documentation","SYSTEM_KEXT_INSTALL_PATH":"/System/Library/Extensions","SYSTEM_LIBRARY_DIR":"/System/Library","TAPI_VERIFY_MODE":"ErrorsOnly","TARGETED_DEVICE_FAMILY":"1,2","TARGETNAME":"Runner","TARGET_BUILD_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos","TARGET_NAME":"Runner","TARGET_TEMP_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build","TEMP_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build","TEMP_FILES_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build","TEMP_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build","TEMP_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","TEST_FRAMEWORK_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/Frameworks /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS15.0.sdk/Developer/Library/Frameworks","TEST_LIBRARY_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib","TOOLCHAINS":"com.apple.dt.toolchain.XcodeDefault","TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","TRACK_WIDGET_CREATION":"true","TREAT_MISSING_BASELINES_AS_TEST_FAILURES":"NO","TREE_SHAKE_ICONS":"false","UID":"501","UNLOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app","UNSTRIPPED_PRODUCT":"NO","USER":"dabzshot","USER_APPS_DIR":"/Users/<USER>/Applications","USER_LIBRARY_DIR":"/Users/<USER>/Library","USE_DYNAMIC_NO_PIC":"YES","USE_HEADERMAP":"YES","USE_HEADER_SYMLINKS":"NO","USE_LLVM_TARGET_TRIPLES":"YES","USE_LLVM_TARGET_TRIPLES_FOR_CLANG":"YES","USE_LLVM_TARGET_TRIPLES_FOR_LD":"YES","USE_LLVM_TARGET_TRIPLES_FOR_TAPI":"YES","USE_RECURSIVE_SCRIPT_INPUTS_IN_SCRIPT_PHASES":"YES","VALIDATE_DEVELOPMENT_ASSET_PATHS":"YES_ERROR","VALIDATE_PRODUCT":"NO","VALIDATE_WORKSPACE":"YES_ERROR","VALID_ARCHS":"arm64 arm64e armv7 armv7s","VERBOSE_PBXCP":"NO","VERSIONING_SYSTEM":"apple-generic","VERSIONPLIST_PATH":"Runner.app/version.plist","VERSION_INFO_BUILDER":"dabzshot","VERSION_INFO_FILE":"Runner_vers.c","VERSION_INFO_STRING":"\"@(#)PROGRAM:Runner  PROJECT:Runner-207\"","WRAPPER_EXTENSION":"app","WRAPPER_NAME":"Runner.app","WRAPPER_SUFFIX":".app","WRAP_ASSET_PACKS_IN_SEPARATE_DIRECTORIES":"NO","XCODE_APP_SUPPORT_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Xcode","XCODE_PRODUCT_BUILD_VERSION":"13A1030d","XCODE_VERSION_ACTUAL":"1310","XCODE_VERSION_MAJOR":"1300","XCODE_VERSION_MINOR":"1310","XPCSERVICES_FOLDER_PATH":"Runner.app/XPCServices","YACC":"yacc","arch":"undefined_arch","variant":"normal"},"allow-missing-inputs":true,"working-directory":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","control-enabled":false,"signature":"67e6d5c0fcf37925ca212fd628525cc8"}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:ProcessInfoPlistFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Info.plist /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/Info.plist": {"tool":"info-plist-processor","description":"ProcessInfoPlistFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Info.plist /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/Info.plist","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/Info.plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/LaunchScreen-SBPartialInfo.plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/Main-SBPartialInfo.plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/assetcatalog_generated_info.plist","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app/Info.plist"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:RegisterExecutionPolicyException /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app": {"tool":"register-execution-policy-exception","description":"RegisterExecutionPolicyException /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-CodeSign>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--will-sign>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["<RegisterExecutionPolicyException /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app>"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:Touch /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app": {"tool":"shell","description":"Touch /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-Validate>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--will-sign>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["<Touch /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app>"],"args":["/usr/bin/touch","-c","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app"],"env":{},"working-directory":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","signature":"8b30504629f6884ef09065abe5d729b1"}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:Validate /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app": {"tool":"validate-product","description":"Validate /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-RegisterExecutionPolicyException>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--will-sign>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>","<TRIGGER: MkDir /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app>"],"outputs":["<Validate /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos/Runner.app>"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner_vers.c": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner_vers.c","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner_vers.c"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner-OutputFileMap.json": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner-OutputFileMap.json","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner-OutputFileMap.json"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.LinkFileList": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.LinkFileList","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.LinkFileList"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.SwiftFileList": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.SwiftFileList","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.SwiftFileList"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-all-non-framework-target-headers.hmap": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-all-non-framework-target-headers.hmap","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-all-non-framework-target-headers.hmap"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-all-target-headers.hmap": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-all-target-headers.hmap","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-all-target-headers.hmap"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-generated-files.hmap": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-generated-files.hmap","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-generated-files.hmap"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-own-target-headers.hmap": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-own-target-headers.hmap","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-own-target-headers.hmap"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-project-headers.hmap": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-project-headers.hmap","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-project-headers.hmap"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner.hmap": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner.hmap","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner.hmap"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-328736DAE7F587435A6E60E5.sh": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-328736DAE7F587435A6E60E5.sh","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-328736DAE7F587435A6E60E5.sh"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-3759590B8F78A0B90ADCF815.sh": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-3759590B8F78A0B90ADCF815.sh","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase6-thin-binary>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-3759590B8F78A0B90ADCF815.sh"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase5-copy-files>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-9740EEB61CF901F6004384FC.sh": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-9740EEB61CF901F6004384FC.sh","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase0--cp--check-pods-manifest-lock>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-9740EEB61CF901F6004384FC.sh"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/all-product-headers.yaml": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/all-product-headers.yaml","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphoneos/Runner.build/all-product-headers.yaml"]}

