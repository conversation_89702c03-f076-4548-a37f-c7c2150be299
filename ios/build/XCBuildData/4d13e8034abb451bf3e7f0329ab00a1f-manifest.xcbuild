client:
  name: basic
  version: 0
  file-system: default

targets:
  "": ["<all>"]

nodes:
  "/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios": {"is-mutated":true}
  "/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos": {"is-mutated":true}
  "/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator": {"is-mutated":true}

commands:
  "::CreateBuildDirectory /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios": {"tool":"create-build-directory","description":"CreateBuildDirectory /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","inputs":[],"outputs":["<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios>","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios"]}
  "::CreateBuildDirectory /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos": {"tool":"create-build-directory","description":"CreateBuildDirectory /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios"],"outputs":["<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos>","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos"]}
  "::CreateBuildDirectory /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator": {"tool":"create-build-directory","description":"CreateBuildDirectory /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios"],"outputs":["<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator>","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator"]}
  "<all>": {"tool":"phony","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks","<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphoneos>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--end>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--modules-ready>"],"outputs":["<all>"]}
  "<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-Debug-iphonesimulator-x86_64-build-headers-stale-file-removal>": {"tool":"stale-file-removal","expectedOutputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/assetcatalog_generated_info.plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Assets.car","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen-SBPartialInfo.plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main.storyboardc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main-SBPartialInfo.plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/AppDelegate.o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/AppFrameworkInfo.plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/GoogleService-Info.plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Runner","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Base.lproj/Main.storyboardc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner-Swift.h","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftdoc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftdoc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftmodule","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftmodule","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64.swiftsourceinfo","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Pods-Runner-checkManifestLockResult.txt","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/openssl_grpc.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/DTTJailbreakDetection.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FMDB.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseAuth.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCore.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCoreDiagnostics.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCrashlytics.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseFirestore.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseInstallations.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseMessaging.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseStorage.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GTMSessionFetcher.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleDataTransport.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleToolboxForMac.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleUtilities.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/MTBBarcodeScanner.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBLPromises.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Protobuf.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Reachability.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Toast.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/absl.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/app_settings.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/camera.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/connectivity.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/contacts_service.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_appavailability.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_idensic_mobile_sdk_plugin.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_jailbreak_detection.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_local_notifications.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_native_timezone.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_secure_storage.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/fluttertoast.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/grpcpp.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/grpc.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_crop.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_gallery_saver.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_picker.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/keyboard_visibility.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/launch_review.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/leveldb.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/local_auth.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/nanopb.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/native_device_orientation.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/open_appstore.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/package_info.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/path_provider.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/permission.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/qrcode_flutter.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/share.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/shared_preferences.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/simple_image_crop.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sms_autofill.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sms_receiver.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/social_share_plugin.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sqflite.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/store_redirect.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/uni_links.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/url_launcher.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/video_player.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/webview_flutter.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBAEMKit.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBSDKCoreKit.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBSDKCoreKit_Basics.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBSDKShareKit.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/IdensicMobileSDK.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Info.plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-non-framework-target-headers.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-target-headers.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-generated-files.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-own-target-headers.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-project-headers.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-328736DAE7F587435A6E60E5.sh","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3759590B8F78A0B90ADCF815.sh","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/all-product-headers.yaml"],"roots":["/tmp/Runner.dst","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-Debug-iphonesimulator-x86_64-build-headers-stale-file-removal>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangeAlternatePermissions": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangePermissions>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--will-sign>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangeAlternatePermissions>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangePermissions": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-StripSymbols>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--will-sign>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangePermissions>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-CodeSign": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangeAlternatePermissions>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--will-sign>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-CodeSign>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-CopyAside": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ProductPostprocessingTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--will-sign>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-CopyAside>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-RegisterExecutionPolicyException": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-CodeSign>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--will-sign>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","<RegisterExecutionPolicyException /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-RegisterExecutionPolicyException>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-RegisterProduct": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-Validate>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--will-sign>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","<Touch /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-RegisterProduct>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-StripSymbols": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-CopyAside>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--will-sign>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-StripSymbols>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-Validate": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-RegisterExecutionPolicyException>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--will-sign>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-Validate>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--CopySwiftPackageResourcesTaskProducer": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--CopySwiftPackageResourcesTaskProducer>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--DocumentationTaskProducer": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--DocumentationTaskProducer>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ProductStructureTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-non-framework-target-headers.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-target-headers.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-generated-files.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-own-target-headers.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-project-headers.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/all-product-headers.yaml"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--InfoPlistTaskProducer": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Info.plist"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--InfoPlistTaskProducer>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ProductPostprocessingTaskProducer": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--package-copy-files-phase>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--InfoPlistTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--VersionPlistTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--SanitizerTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--SwiftStandardLibrariesTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--SwiftFrameworkABICheckerTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--SwiftABIBaselineGenerationTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--StubBinaryTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--TestTargetTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--TestHostTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--CopySwiftPackageResourcesTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--DocumentationTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ProductPostprocessingTaskProducer>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ProductStructureTaskProducer": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--start>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","<MkDir /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app>","<MkDir /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ProductStructureTaskProducer>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--SanitizerTaskProducer": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--SanitizerTaskProducer>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--StubBinaryTaskProducer": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--StubBinaryTaskProducer>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--SwiftABIBaselineGenerationTaskProducer": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--package-copy-files-phase>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--SwiftABIBaselineGenerationTaskProducer>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--SwiftFrameworkABICheckerTaskProducer": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--package-copy-files-phase>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--SwiftFrameworkABICheckerTaskProducer>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--SwiftStandardLibrariesTaskProducer": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--package-copy-files-phase>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","<CopySwiftStdlib /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--SwiftStandardLibrariesTaskProducer>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--TestHostTaskProducer": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--TestHostTaskProducer>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--TestTargetPostprocessingTaskProducer": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ProductPostprocessingTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--TestTargetPostprocessingTaskProducer>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--TestTargetTaskProducer": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--TestTargetTaskProducer>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--VersionPlistTaskProducer": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--VersionPlistTaskProducer>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-Debug-iphonesimulator-x86_64-build-headers-stale-file-removal>","<CreateBuildDirectory-/tmp/Runner.dst>","<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios>","<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios>","<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--end": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangeAlternatePermissions>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangePermissions>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-CodeSign>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-CopyAside>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-RegisterExecutionPolicyException>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-RegisterProduct>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-StripSymbols>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-Validate>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--CopySwiftPackageResourcesTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--DocumentationTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--InfoPlistTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ProductPostprocessingTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ProductStructureTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--SanitizerTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--StubBinaryTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--SwiftABIBaselineGenerationTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--SwiftFrameworkABICheckerTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--SwiftStandardLibrariesTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--TestHostTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--TestTargetPostprocessingTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--TestTargetTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--VersionPlistTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--generated-headers>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--package-copy-files-phase>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase0--cp--check-pods-manifest-lock>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase4-copy-bundle-resources>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase5-copy-files>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase6-thin-binary>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase7--cp--embed-pods-frameworks>","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/assetcatalog_generated_info.plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Assets.car","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen-SBPartialInfo.plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main.storyboardc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main-SBPartialInfo.plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/AppDelegate.o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/AppFrameworkInfo.plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/GoogleService-Info.plist","<CopySwiftStdlib /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app>","<Linked Binary /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Runner>","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Base.lproj/Main.storyboardc","<MkDir /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app>","<MkDir /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks>","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner-Swift.h","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftdoc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftdoc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftmodule","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftmodule","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64.swiftsourceinfo","<execute-shell-script-18c1723432283e0cc55f10a6dcfd9e029eb60ff613d36b3b9942d1a6e1e7c6aa-target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49->","<execute-shell-script-18c1723432283e0cc55f10a6dcfd9e02f1eee2015e8ff5ebcd27678f788c2826-target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49->","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Pods-Runner-checkManifestLockResult.txt","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/openssl_grpc.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/DTTJailbreakDetection.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FMDB.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseAuth.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCore.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCoreDiagnostics.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCrashlytics.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseFirestore.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseInstallations.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseMessaging.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseStorage.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GTMSessionFetcher.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleDataTransport.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleToolboxForMac.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleUtilities.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/MTBBarcodeScanner.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBLPromises.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Protobuf.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Reachability.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Toast.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/absl.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/app_settings.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/camera.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/connectivity.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/contacts_service.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_appavailability.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_idensic_mobile_sdk_plugin.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_jailbreak_detection.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_local_notifications.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_native_timezone.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_secure_storage.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/fluttertoast.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/grpcpp.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/grpc.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_crop.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_gallery_saver.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_picker.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/keyboard_visibility.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/launch_review.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/leveldb.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/local_auth.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/nanopb.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/native_device_orientation.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/open_appstore.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/package_info.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/path_provider.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/permission.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/qrcode_flutter.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/share.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/shared_preferences.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/simple_image_crop.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sms_autofill.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sms_receiver.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/social_share_plugin.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sqflite.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/store_redirect.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/uni_links.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/url_launcher.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/video_player.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/webview_flutter.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBAEMKit.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBSDKCoreKit.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBSDKCoreKit_Basics.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBSDKShareKit.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/IdensicMobileSDK.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Info.plist","<RegisterExecutionPolicyException /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app>","<Touch /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app>","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-non-framework-target-headers.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-target-headers.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-generated-files.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-own-target-headers.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-project-headers.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-328736DAE7F587435A6E60E5.sh","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3759590B8F78A0B90ADCF815.sh","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/all-product-headers.yaml"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--end>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-Debug-iphonesimulator-x86_64-build-headers-stale-file-removal>","<CreateBuildDirectory-/tmp/Runner.dst>","<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios>","<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios>","<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--generated-headers": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/AppDelegate.o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner-Swift.h"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--generated-headers>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--immediate": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-Debug-iphonesimulator-x86_64-build-headers-stale-file-removal>","<CreateBuildDirectory-/tmp/Runner.dst>","<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios>","<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios>","<CreateBuildDirectory-/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--immediate>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--modules-ready": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/AppDelegate.o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner-Swift.h","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftdoc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftdoc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftmodule","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftmodule","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64.swiftsourceinfo","<execute-shell-script-18c1723432283e0cc55f10a6dcfd9e029eb60ff613d36b3b9942d1a6e1e7c6aa-target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49->","<execute-shell-script-18c1723432283e0cc55f10a6dcfd9e02f1eee2015e8ff5ebcd27678f788c2826-target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49->","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Pods-Runner-checkManifestLockResult.txt","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/openssl_grpc.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/DTTJailbreakDetection.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FMDB.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseAuth.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCore.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCoreDiagnostics.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCrashlytics.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseFirestore.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseInstallations.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseMessaging.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseStorage.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GTMSessionFetcher.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleDataTransport.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleToolboxForMac.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleUtilities.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/MTBBarcodeScanner.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBLPromises.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Protobuf.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Reachability.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Toast.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/absl.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/app_settings.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/camera.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/connectivity.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/contacts_service.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_appavailability.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_idensic_mobile_sdk_plugin.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_jailbreak_detection.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_local_notifications.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_native_timezone.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_secure_storage.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/fluttertoast.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/grpcpp.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/grpc.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_crop.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_gallery_saver.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_picker.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/keyboard_visibility.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/launch_review.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/leveldb.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/local_auth.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/nanopb.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/native_device_orientation.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/open_appstore.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/package_info.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/path_provider.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/permission.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/qrcode_flutter.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/share.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/shared_preferences.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/simple_image_crop.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sms_autofill.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sms_receiver.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/social_share_plugin.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sqflite.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/store_redirect.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/uni_links.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/url_launcher.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/video_player.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/webview_flutter.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBAEMKit.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBSDKCoreKit.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBSDKCoreKit_Basics.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBSDKShareKit.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/IdensicMobileSDK.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-328736DAE7F587435A6E60E5.sh","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3759590B8F78A0B90ADCF815.sh","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--modules-ready>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--package-copy-files-phase": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase7--cp--embed-pods-frameworks>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--package-copy-files-phase>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase0--cp--check-pods-manifest-lock": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Pods-Runner-checkManifestLockResult.txt","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-328736DAE7F587435A6E60E5.sh"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase0--cp--check-pods-manifest-lock>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase0--cp--check-pods-manifest-lock>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","<execute-shell-script-18c1723432283e0cc55f10a6dcfd9e029eb60ff613d36b3b9942d1a6e1e7c6aa-target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49->","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/AppDelegate.o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc","<Linked Binary /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Runner>","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner-Swift.h","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftdoc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftdoc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftmodule","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftmodule","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64.swiftsourceinfo","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase4-copy-bundle-resources": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/assetcatalog_generated_info.plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Assets.car","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen-SBPartialInfo.plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main.storyboardc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main-SBPartialInfo.plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/AppFrameworkInfo.plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/GoogleService-Info.plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Base.lproj/Main.storyboardc"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase4-copy-bundle-resources>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase5-copy-files": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase4-copy-bundle-resources>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase5-copy-files>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase6-thin-binary": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase5-copy-files>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","<execute-shell-script-18c1723432283e0cc55f10a6dcfd9e02f1eee2015e8ff5ebcd27678f788c2826-target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49->","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase6-thin-binary>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase7--cp--embed-pods-frameworks": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase6-thin-binary>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/openssl_grpc.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/DTTJailbreakDetection.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FMDB.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseAuth.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCore.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCoreDiagnostics.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCrashlytics.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseFirestore.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseInstallations.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseMessaging.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseStorage.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GTMSessionFetcher.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleDataTransport.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleToolboxForMac.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleUtilities.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/MTBBarcodeScanner.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBLPromises.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Protobuf.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Reachability.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Toast.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/absl.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/app_settings.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/camera.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/connectivity.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/contacts_service.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_appavailability.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_idensic_mobile_sdk_plugin.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_jailbreak_detection.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_local_notifications.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_native_timezone.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_secure_storage.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/fluttertoast.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/grpcpp.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/grpc.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_crop.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_gallery_saver.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_picker.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/keyboard_visibility.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/launch_review.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/leveldb.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/local_auth.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/nanopb.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/native_device_orientation.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/open_appstore.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/package_info.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/path_provider.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/permission.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/qrcode_flutter.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/share.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/shared_preferences.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/simple_image_crop.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sms_autofill.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sms_receiver.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/social_share_plugin.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sqflite.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/store_redirect.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/uni_links.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/url_launcher.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/video_player.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/webview_flutter.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBAEMKit.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBSDKCoreKit.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBSDKCoreKit_Basics.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBSDKShareKit.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/IdensicMobileSDK.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3759590B8F78A0B90ADCF815.sh"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase7--cp--embed-pods-frameworks>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--unsigned-product-ready": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/AppDelegate.o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc","<CopySwiftStdlib /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app>","<Linked Binary /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Runner>","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner-Swift.h","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftdoc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftdoc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftmodule","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftmodule","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64.swiftsourceinfo","<execute-shell-script-18c1723432283e0cc55f10a6dcfd9e029eb60ff613d36b3b9942d1a6e1e7c6aa-target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49->","<execute-shell-script-18c1723432283e0cc55f10a6dcfd9e02f1eee2015e8ff5ebcd27678f788c2826-target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49->","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Pods-Runner-checkManifestLockResult.txt","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/openssl_grpc.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/DTTJailbreakDetection.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FMDB.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseAuth.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCore.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCoreDiagnostics.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCrashlytics.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseFirestore.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseInstallations.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseMessaging.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseStorage.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GTMSessionFetcher.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleDataTransport.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleToolboxForMac.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleUtilities.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/MTBBarcodeScanner.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBLPromises.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Protobuf.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Reachability.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Toast.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/absl.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/app_settings.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/camera.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/connectivity.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/contacts_service.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_appavailability.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_idensic_mobile_sdk_plugin.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_jailbreak_detection.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_local_notifications.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_native_timezone.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_secure_storage.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/fluttertoast.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/grpcpp.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/grpc.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_crop.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_gallery_saver.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_picker.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/keyboard_visibility.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/launch_review.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/leveldb.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/local_auth.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/nanopb.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/native_device_orientation.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/open_appstore.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/package_info.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/path_provider.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/permission.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/qrcode_flutter.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/share.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/shared_preferences.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/simple_image_crop.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sms_autofill.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sms_receiver.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/social_share_plugin.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sqflite.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/store_redirect.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/uni_links.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/url_launcher.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/video_player.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/webview_flutter.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBAEMKit.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBSDKCoreKit.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBSDKCoreKit_Basics.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBSDKShareKit.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/IdensicMobileSDK.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-328736DAE7F587435A6E60E5.sh","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3759590B8F78A0B90ADCF815.sh","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--unsigned-product-ready>"]}
  "Gate target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--will-sign": {"tool":"phony","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--unsigned-product-ready>"],"outputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--will-sign>"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:CompileAssetCatalog /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/Assets.xcassets": {"tool":"shell","description":"CompileAssetCatalog /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/Assets.xcassets","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/Assets.xcassets/","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/assetcatalog_generated_info.plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Assets.car"],"args":["/Applications/Xcode.app/Contents/Developer/usr/bin/actool","--output-format","human-readable-text","--notices","--warnings","--export-dependency-info","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/assetcatalog_dependencies","--output-partial-info-plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/assetcatalog_generated_info.plist","--app-icon","AppIcon","--compress-pngs","--enable-on-demand-resources","YES","--filter-for-device-model","iPhone10,4","--filter-for-device-os-version","15.0","--development-region","en","--target-device","iphone","--target-device","ipad","--minimum-deployment-target","12.0","--platform","iphonesimulator","--compile","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/Assets.xcassets"],"env":{},"working-directory":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","control-enabled":false,"deps":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/assetcatalog_dependencies"],"deps-style":"dependency-info","signature":"cbd1022f5a9d7730eb1e7e80c6f8d793"}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:CompileC /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/GeneratedPluginRegistrant.m normal x86_64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool":"shell","description":"CompileC /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/GeneratedPluginRegistrant.m normal x86_64 objective-c com.apple.compilers.llvm.clang.1_0.compiler","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/GeneratedPluginRegistrant.m","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--generated-headers>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o"],"args":["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang","-x","objective-c","-target","x86_64-apple-ios12.0-simulator","-fmessage-length=0","-fdiagnostics-show-note-include-stack","-fmacro-backtrace-limit=0","-std=gnu99","-fobjc-arc","-fmodules","-gmodules","-fmodules-cache-path=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex","-fmodules-prune-interval=86400","-fmodules-prune-after=345600","-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation","-fmodules-validate-once-per-build-session","-Wnon-modular-include-in-framework-module","-Werror=non-modular-include-in-framework-module","-Wno-trigraphs","-fpascal-strings","-O0","-fno-common","-Wno-missing-field-initializers","-Wno-missing-prototypes","-Werror=return-type","-Wunreachable-code","-Wquoted-include-in-framework-header","-Wno-implicit-atomic-properties","-Werror=deprecated-objc-isa-usage","-Wno-objc-interface-ivars","-Werror=objc-root-class","-Wno-arc-repeated-use-of-weak","-Wimplicit-retain-self","-Wduplicate-method-match","-Wno-missing-braces","-Wparentheses","-Wswitch","-Wunused-function","-Wno-unused-label","-Wno-unused-parameter","-Wunused-variable","-Wunused-value","-Wempty-body","-Wuninitialized","-Wconditional-uninitialized","-Wno-unknown-pragmas","-Wno-shadow","-Wno-four-char-constants","-Wno-conversion","-Wconstant-conversion","-Wint-conversion","-Wbool-conversion","-Wenum-conversion","-Wno-float-conversion","-Wnon-literal-null-conversion","-Wobjc-literal-conversion","-Wshorten-64-to-32","-Wpointer-sign","-Wno-newline-eof","-Wno-selector","-Wno-strict-selector-match","-Wundeclared-selector","-Wdeprecated-implementations","-DDEBUG=1","-DOBJC_OLD_DISPATCH_PROTOTYPES=0","-isysroot","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator15.0.sdk","-fasm-blocks","-fstrict-aliasing","-Wprotocol","-Wdeprecated-declarations","-g","-Wno-sign-conversion","-Winfinite-recursion","-Wcomma","-Wblock-capture-autoreleasing","-Wstrict-prototypes","-Wno-semicolon-before-method-body","-fobjc-abi-version=2","-fobjc-legacy-dispatch","-index-store-path","/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-hbmtdpxidruszpaaeuveldpjuspm/Index/DataStore","-iquote","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-generated-files.hmap","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-own-target-headers.hmap","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-target-headers.hmap","-iquote","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-project-headers.hmap","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/include","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources-normal/x86_64","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/x86_64","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Flutter","-MMD","-MT","dependencies","-MF","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.d","--serialize-diagnostics","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.dia","-c","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/GeneratedPluginRegistrant.m","-o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o"],"env":{"LANG":"en_US.US-ASCII"},"working-directory":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","deps":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.d"],"deps-style":"makefile","signature":"bfb47d5f2fc234ff3c6a10ab50c43804"}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:CompileC /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c normal x86_64 c com.apple.compilers.llvm.clang.1_0.compiler": {"tool":"shell","description":"CompileC /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c normal x86_64 c com.apple.compilers.llvm.clang.1_0.compiler","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--generated-headers>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o"],"args":["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang","-x","c","-target","x86_64-apple-ios12.0-simulator","-fmessage-length=0","-fdiagnostics-show-note-include-stack","-fmacro-backtrace-limit=0","-std=gnu99","-fmodules","-gmodules","-fmodules-cache-path=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex","-fmodules-prune-interval=86400","-fmodules-prune-after=345600","-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation","-fmodules-validate-once-per-build-session","-Wnon-modular-include-in-framework-module","-Werror=non-modular-include-in-framework-module","-Wno-trigraphs","-fpascal-strings","-O0","-fno-common","-Wno-missing-field-initializers","-Wno-missing-prototypes","-Werror=return-type","-Wunreachable-code","-Wquoted-include-in-framework-header","-Werror=deprecated-objc-isa-usage","-Werror=objc-root-class","-Wno-missing-braces","-Wparentheses","-Wswitch","-Wunused-function","-Wno-unused-label","-Wno-unused-parameter","-Wunused-variable","-Wunused-value","-Wempty-body","-Wuninitialized","-Wconditional-uninitialized","-Wno-unknown-pragmas","-Wno-shadow","-Wno-four-char-constants","-Wno-conversion","-Wconstant-conversion","-Wint-conversion","-Wbool-conversion","-Wenum-conversion","-Wno-float-conversion","-Wnon-literal-null-conversion","-Wobjc-literal-conversion","-Wshorten-64-to-32","-Wpointer-sign","-Wno-newline-eof","-DDEBUG=1","-isysroot","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator15.0.sdk","-fasm-blocks","-fstrict-aliasing","-Wdeprecated-declarations","-g","-Wno-sign-conversion","-Winfinite-recursion","-Wcomma","-Wblock-capture-autoreleasing","-Wstrict-prototypes","-Wno-semicolon-before-method-body","-index-store-path","/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-hbmtdpxidruszpaaeuveldpjuspm/Index/DataStore","-iquote","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-generated-files.hmap","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-own-target-headers.hmap","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-target-headers.hmap","-iquote","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-project-headers.hmap","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/include","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources-normal/x86_64","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/x86_64","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Flutter","-MMD","-MT","dependencies","-MF","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.d","--serialize-diagnostics","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.dia","-c","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c","-o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o"],"env":{"LANG":"en_US.US-ASCII"},"working-directory":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","deps":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.d"],"deps-style":"makefile","signature":"52bb8a9c2eacaa026053aabb3d9819e9"}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:CompileStoryboard /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/Base.lproj/LaunchScreen.storyboard": {"tool":"shell","description":"CompileStoryboard /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/Base.lproj/LaunchScreen.storyboard","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/Base.lproj/LaunchScreen.storyboard","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen-SBPartialInfo.plist"],"args":["/Applications/Xcode.app/Contents/Developer/usr/bin/ibtool","--errors","--warnings","--notices","--module","Runner","--output-partial-info-plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen-SBPartialInfo.plist","--auto-activate-custom-fonts","--target-device","iphone","--target-device","ipad","--minimum-deployment-target","12.0","--output-format","human-readable-text","--compilation-directory","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/Base.lproj/LaunchScreen.storyboard"],"env":{"XCODE_DEVELOPER_USR_PATH":"/Applications/Xcode.app/Contents/Developer/usr/bin/.."},"working-directory":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","control-enabled":false,"signature":"8da61659dcd3e780c0276a12f67723d4"}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:CompileStoryboard /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/Base.lproj/Main.storyboard": {"tool":"shell","description":"CompileStoryboard /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/Base.lproj/Main.storyboard","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/Base.lproj/Main.storyboard","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main.storyboardc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main-SBPartialInfo.plist"],"args":["/Applications/Xcode.app/Contents/Developer/usr/bin/ibtool","--errors","--warnings","--notices","--module","Runner","--output-partial-info-plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main-SBPartialInfo.plist","--auto-activate-custom-fonts","--target-device","iphone","--target-device","ipad","--minimum-deployment-target","12.0","--output-format","human-readable-text","--compilation-directory","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/Base.lproj/Main.storyboard"],"env":{"XCODE_DEVELOPER_USR_PATH":"/Applications/Xcode.app/Contents/Developer/usr/bin/.."},"working-directory":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","control-enabled":false,"signature":"4897f31aec0834bfc6c70971c0d48585"}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:CompileSwiftSources normal x86_64 com.apple.xcode.tools.swift.compiler": {"tool":"shell","description":"CompileSwiftSources normal x86_64 com.apple.xcode.tools.swift.compiler","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/AppDelegate.swift","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-generated-files.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-own-target-headers.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-target-headers.hmap","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-project-headers.hmap","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/AppDelegate.o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc"],"args":["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-incremental","-module-name","Runner","-Onone","-enable-batch-mode","-enforce-exclusivity=checked","@/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator15.0.sdk","-target","x86_64-apple-ios12.0-simulator","-g","-module-cache-path","/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex","-Xfrontend","-serialize-debugging-options","-enable-testing","-index-store-path","/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-hbmtdpxidruszpaaeuveldpjuspm/Index/DataStore","-swift-version","5","-I","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator","-F","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Flutter","-parse-as-library","-c","-j8","-output-file-map","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json","-parseable-output","-serialize-diagnostics","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/swift-overrides.hmap","-Xcc","-iquote","-Xcc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-generated-files.hmap","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-own-target-headers.hmap","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-target-headers.hmap","-Xcc","-iquote","-Xcc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-project-headers.hmap","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/include","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources-normal/x86_64","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/x86_64","-Xcc","-I/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","-Xcc","-DDEBUG=1","-emit-objc-header","-emit-objc-header-path","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h","-import-objc-header","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/Runner-Bridging-Header.h","-pch-output-dir","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/SharedPrecompiledHeaders","-working-directory","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios"],"env":{"DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","SDKROOT":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator15.0.sdk"},"working-directory":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","deps":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/AppDelegate.d"],"deps-style":"makefile","signature":"b05184085950b1f4e4f5ef7023d333b0"}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:CopyPlistFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/AppFrameworkInfo.plist /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Flutter/AppFrameworkInfo.plist": {"tool":"copy-plist","description":"CopyPlistFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/AppFrameworkInfo.plist /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Flutter/AppFrameworkInfo.plist","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Flutter/AppFrameworkInfo.plist","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/AppFrameworkInfo.plist"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:CopyPlistFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/GoogleService-Info.plist /Users/<USER>/Downloads/GoogleService-Info.plist": {"tool":"copy-plist","description":"CopyPlistFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/GoogleService-Info.plist /Users/<USER>/Downloads/GoogleService-Info.plist","inputs":["/Users/<USER>/Downloads/GoogleService-Info.plist","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/GoogleService-Info.plist"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:CopySwiftLibs /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app": {"tool":"embed-swift-stdlib","description":"CopySwiftLibs /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Runner","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--package-copy-files-phase>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["<CopySwiftStdlib /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app>"],"deps":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/SwiftStdLibToolInputDependencies.dep"}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:Ld /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Runner normal": {"tool":"shell","description":"Ld /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Runner normal","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/AppDelegate.o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Runner","<Linked Binary /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Runner>"],"args":["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang","-target","x86_64-apple-ios12.0-simulator","-isysroot","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator15.0.sdk","-L/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator","-L/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Flutter","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator","-F/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Flutter","-filelist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList","-Xlinker","-rpath","-Xlinker","/usr/lib/swift","-Xlinker","-rpath","-Xlinker","@executable_path/Frameworks","-dead_strip","-Xlinker","-object_path_lto","-Xlinker","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_lto.o","-Xlinker","-export_dynamic","-Xlinker","-no_deduplicate","-Xlinker","-objc_abi_version","-Xlinker","2","-fobjc-arc","-fobjc-link-runtime","-L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator","-L/usr/lib/swift","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule","-framework","Pods_Runner","-Xlinker","-dependency_info","-Xlinker","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_dependency_info.dat","-o","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Runner"],"env":{},"working-directory":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","deps":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_dependency_info.dat"],"deps-style":"dependency-info","signature":"5aef4fb7b808b9601f02d6c1630477fa"}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:LinkStoryboards": {"tool":"shell","description":"LinkStoryboards","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main.storyboardc","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Base.lproj/Main.storyboardc"],"args":["/Applications/Xcode.app/Contents/Developer/usr/bin/ibtool","--errors","--warnings","--notices","--module","Runner","--target-device","iphone","--target-device","ipad","--minimum-deployment-target","12.0","--output-format","human-readable-text","--link","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main.storyboardc"],"env":{"XCODE_DEVELOPER_USR_PATH":"/Applications/Xcode.app/Contents/Developer/usr/bin/.."},"working-directory":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","control-enabled":false,"signature":"dcba09667b5934a8f353b78a58352cb0"}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:MkDir /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app": {"tool":"mkdir","description":"MkDir /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--start>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app","<MkDir /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app>"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:MkDir /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks": {"tool":"mkdir","description":"MkDir /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--start>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks","<MkDir /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks>"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:PBXCp /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner-Swift.h": {"tool":"file-copy","description":"PBXCp /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner-Swift.h","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h/","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner-Swift.h"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:PBXCp /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftdoc": {"tool":"file-copy","description":"PBXCp /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftdoc","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc/","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftdoc"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:PBXCp /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftdoc": {"tool":"file-copy","description":"PBXCp /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftdoc","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc/","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftdoc"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:PBXCp /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftmodule": {"tool":"file-copy","description":"PBXCp /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftmodule","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule/","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftmodule"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:PBXCp /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftmodule": {"tool":"file-copy","description":"PBXCp /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftmodule","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule/","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftmodule"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:PBXCp /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo": {"tool":"file-copy","description":"PBXCp /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo/","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:PBXCp /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64.swiftsourceinfo": {"tool":"file-copy","description":"PBXCp /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64.swiftsourceinfo","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo/","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64.swiftsourceinfo"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:PhaseScriptExecution Run Script /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh": {"tool":"shell","description":"PhaseScriptExecution Run Script /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase0--cp--check-pods-manifest-lock>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["<execute-shell-script-18c1723432283e0cc55f10a6dcfd9e029eb60ff613d36b3b9942d1a6e1e7c6aa-target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49->"],"args":["/bin/sh","-c","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh"],"env":{"ACTION":"build","AD_HOC_CODE_SIGNING_ALLOWED":"YES","ALLOW_TARGET_PLATFORM_SPECIALIZATION":"NO","ALTERNATE_GROUP":"staff","ALTERNATE_MODE":"u+w,go-w,a+rX","ALTERNATE_OWNER":"dabzshot","ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES":"NO","ALWAYS_SEARCH_USER_PATHS":"NO","ALWAYS_USE_SEPARATE_HEADERMAPS":"NO","APPLE_INTERNAL_DEVELOPER_DIR":"/AppleInternal/Developer","APPLE_INTERNAL_DIR":"/AppleInternal","APPLE_INTERNAL_DOCUMENTATION_DIR":"/AppleInternal/Documentation","APPLE_INTERNAL_LIBRARY_DIR":"/AppleInternal/Library","APPLE_INTERNAL_TOOLS":"/AppleInternal/Developer/Tools","APPLICATION_EXTENSION_API_ONLY":"NO","APPLY_RULES_IN_COPY_FILES":"NO","APPLY_RULES_IN_COPY_HEADERS":"NO","ARCHS":"x86_64","ARCHS_STANDARD":"arm64 x86_64","ARCHS_STANDARD_32_64_BIT":"arm64 i386 x86_64","ARCHS_STANDARD_32_BIT":"i386","ARCHS_STANDARD_64_BIT":"arm64 x86_64","ARCHS_STANDARD_INCLUDING_64_BIT":"arm64 x86_64","ARCHS_UNIVERSAL_IPHONE_OS":"arm64 i386 x86_64","ASSETCATALOG_COMPILER_APPICON_NAME":"AppIcon","ASSETCATALOG_FILTER_FOR_DEVICE_MODEL":"iPhone10,4","ASSETCATALOG_FILTER_FOR_DEVICE_OS_VERSION":"15.0","AVAILABLE_PLATFORMS":"appletvos appletvsimulator driverkit iphoneos iphonesimulator macosx watchos watchsimulator","BITCODE_GENERATION_MODE":"marker","BUILD_ACTIVE_RESOURCES_ONLY":"YES","BUILD_COMPONENTS":"headers build","BUILD_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","BUILD_LIBRARY_FOR_DISTRIBUTION":"NO","BUILD_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","BUILD_STYLE":"","BUILD_VARIANTS":"normal","BUILT_PRODUCTS_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator","BUNDLE_CONTENTS_FOLDER_PATH_deep":"Contents/","BUNDLE_EXECUTABLE_FOLDER_NAME_deep":"MacOS","BUNDLE_FORMAT":"shallow","BUNDLE_FRAMEWORKS_FOLDER_PATH":"Frameworks","BUNDLE_PLUGINS_FOLDER_PATH":"PlugIns","BUNDLE_PRIVATE_HEADERS_FOLDER_PATH":"PrivateHeaders","BUNDLE_PUBLIC_HEADERS_FOLDER_PATH":"Headers","CACHE_ROOT":"/var/folders/81/hsp6b80d6fn5sq0f3xxdpfd00000gn/C/com.apple.DeveloperTools/13.1-13A1030d/Xcode","CCHROOT":"/var/folders/81/hsp6b80d6fn5sq0f3xxdpfd00000gn/C/com.apple.DeveloperTools/13.1-13A1030d/Xcode","CHMOD":"/bin/chmod","CHOWN":"/usr/sbin/chown","CLANG_ANALYZER_NONNULL":"YES","CLANG_CXX_LANGUAGE_STANDARD":"gnu++0x","CLANG_CXX_LIBRARY":"libc++","CLANG_ENABLE_MODULES":"YES","CLANG_ENABLE_OBJC_ARC":"YES","CLANG_MODULES_BUILD_SESSION_FILE":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation","CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING":"YES","CLANG_WARN_BOOL_CONVERSION":"YES","CLANG_WARN_COMMA":"YES","CLANG_WARN_CONSTANT_CONVERSION":"YES","CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS":"YES","CLANG_WARN_DIRECT_OBJC_ISA_USAGE":"YES_ERROR","CLANG_WARN_EMPTY_BODY":"YES","CLANG_WARN_ENUM_CONVERSION":"YES","CLANG_WARN_INFINITE_RECURSION":"YES","CLANG_WARN_INT_CONVERSION":"YES","CLANG_WARN_NON_LITERAL_NULL_CONVERSION":"YES","CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF":"YES","CLANG_WARN_OBJC_LITERAL_CONVERSION":"YES","CLANG_WARN_OBJC_ROOT_CLASS":"YES_ERROR","CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER":"YES","CLANG_WARN_RANGE_LOOP_ANALYSIS":"YES","CLANG_WARN_STRICT_PROTOTYPES":"YES","CLANG_WARN_SUSPICIOUS_MOVE":"YES","CLANG_WARN_UNREACHABLE_CODE":"YES","CLANG_WARN__DUPLICATE_METHOD_MATCH":"YES","CLASS_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/JavaClasses","CLEAN_PRECOMPS":"YES","CLONE_HEADERS":"NO","COCOAPODS_PARALLEL_CODE_SIGN":"true","CODESIGNING_FOLDER_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app","CODE_SIGNING_ALLOWED":"YES","CODE_SIGNING_REQUIRED":"YES","CODE_SIGN_CONTEXT_CLASS":"XCiPhoneSimulatorCodeSignContext","CODE_SIGN_ENTITLEMENTS":"Runner/Runner.entitlements","CODE_SIGN_IDENTITY":"Apple Development","CODE_SIGN_INJECT_BASE_ENTITLEMENTS":"YES","CODE_SIGN_STYLE":"Automatic","COLOR_DIAGNOSTICS":"NO","COMBINE_HIDPI_IMAGES":"NO","COMPILER_INDEX_STORE_ENABLE":"Default","COMPOSITE_SDK_DIRS":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/CompositeSDKs","COMPRESS_PNG_FILES":"YES","CONFIGURATION":"Debug","CONFIGURATION_BUILD_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator","CONFIGURATION_TEMP_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator","CONTENTS_FOLDER_PATH":"Runner.app","COPYING_PRESERVES_HFS_DATA":"NO","COPY_HEADERS_RUN_UNIFDEF":"NO","COPY_PHASE_STRIP":"NO","COPY_RESOURCES_FROM_STATIC_FRAMEWORKS":"YES","CORRESPONDING_DEVICE_PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform","CORRESPONDING_DEVICE_PLATFORM_NAME":"iphoneos","CORRESPONDING_DEVICE_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS15.0.sdk","CORRESPONDING_DEVICE_SDK_NAME":"iphoneos15.0","CP":"/bin/cp","CREATE_INFOPLIST_SECTION_IN_BINARY":"NO","CURRENT_ARCH":"undefined_arch","CURRENT_PROJECT_VERSION":"207","CURRENT_VARIANT":"normal","DART_DEFINES":"Zmx1dHRlci5pbnNwZWN0b3Iuc3RydWN0dXJlZEVycm9ycz10cnVl,RkxVVFRFUl9XRUJfQVVUT19ERVRFQ1Q9dHJ1ZQ==","DART_OBFUSCATION":"false","DEAD_CODE_STRIPPING":"YES","DEBUGGING_SYMBOLS":"YES","DEBUG_INFORMATION_FORMAT":"dwarf","DEFAULT_COMPILER":"com.apple.compilers.llvm.clang.1_0","DEFAULT_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","DEFAULT_KEXT_INSTALL_PATH":"/System/Library/Extensions","DEFINES_MODULE":"NO","DEPLOYMENT_LOCATION":"NO","DEPLOYMENT_POSTPROCESSING":"NO","DEPLOYMENT_TARGET_CLANG_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_CLANG_FLAG_NAME":"mios-simulator-version-min","DEPLOYMENT_TARGET_CLANG_FLAG_PREFIX":"-mios-simulator-version-min=","DEPLOYMENT_TARGET_LD_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_LD_FLAG_NAME":"ios_simulator_version_min","DEPLOYMENT_TARGET_SETTING_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_SUGGESTED_VALUES":"9.0 9.1 9.2 9.3 10.0 10.1 10.2 10.3 11.0 11.1 11.2 11.3 11.4 12.0 12.1 12.2 12.3 12.4 13.0 13.1 13.2 13.3 13.4 13.5 13.6 14.0 14.1 14.2 14.3 14.4 14.5 14.6 14.7 15.0","DERIVED_FILES_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","DERIVED_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","DERIVED_SOURCES_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","DEVELOPER_FRAMEWORKS_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_FRAMEWORKS_DIR_QUOTED":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Library","DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs","DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Tools","DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","DEVELOPMENT_LANGUAGE":"en","DEVELOPMENT_TEAM":"HJAN5D475T","DOCUMENTATION_FOLDER_PATH":"Runner.app/en.lproj/Documentation","DONT_GENERATE_INFOPLIST_FILE":"NO","DO_HEADER_SCANNING_IN_JAM":"NO","DSTROOT":"/tmp/Runner.dst","DT_TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","DWARF_DSYM_FILE_NAME":"Runner.app.dSYM","DWARF_DSYM_FILE_SHOULD_ACCOMPANY_PRODUCT":"NO","DWARF_DSYM_FOLDER_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator","EFFECTIVE_PLATFORM_NAME":"-iphonesimulator","EMBEDDED_CONTENT_CONTAINS_SWIFT":"NO","EMBED_ASSET_PACKS_IN_PRODUCT_BUNDLE":"NO","ENABLE_APP_SANDBOX":"NO","ENABLE_BITCODE":"NO","ENABLE_DEFAULT_HEADER_SEARCH_PATHS":"YES","ENABLE_HARDENED_RUNTIME":"NO","ENABLE_HEADER_DEPENDENCIES":"YES","ENABLE_ON_DEMAND_RESOURCES":"YES","ENABLE_PREVIEWS":"NO","ENABLE_STRICT_OBJC_MSGSEND":"YES","ENABLE_TESTABILITY":"YES","ENABLE_TESTING_SEARCH_PATHS":"NO","ENTITLEMENTS_DESTINATION":"__entitlements","ENTITLEMENTS_REQUIRED":"YES","EXCLUDED_ARCHS":"arm64 i386","EXCLUDED_INSTALLSRC_SUBDIRECTORY_PATTERNS":".DS_Store .svn .git .hg CVS","EXCLUDED_RECURSIVE_SEARCH_PATH_SUBDIRECTORIES":"*.nib *.lproj *.framework *.gch *.xcode* *.xcassets (*) .DS_Store CVS .svn .git .hg *.pbproj *.pbxproj","EXECUTABLES_FOLDER_PATH":"Runner.app/Executables","EXECUTABLE_FOLDER_PATH":"Runner.app","EXECUTABLE_NAME":"Runner","EXECUTABLE_PATH":"Runner.app/Runner","EXTRA_FRONT_END_OPTIONS":"--no-sound-null-safety","FILE_LIST":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects/LinkFileList","FIXED_FILES_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/FixedFiles","FLUTTER_APPLICATION_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3","FLUTTER_BUILD_DIR":"build","FLUTTER_BUILD_NAME":"2.0.36","FLUTTER_BUILD_NUMBER":"191","FLUTTER_ROOT":"/Users/<USER>/Desktop/flutter","FLUTTER_TARGET":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/lib/main.dart","FRAMEWORKS_FOLDER_PATH":"Runner.app/Frameworks","FRAMEWORK_FLAG_PREFIX":"-framework","FRAMEWORK_SEARCH_PATHS":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator  /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Flutter","FRAMEWORK_VERSION":"A","FULL_PRODUCT_NAME":"Runner.app","GCC3_VERSION":"3.3","GCC_C_LANGUAGE_STANDARD":"gnu99","GCC_DYNAMIC_NO_PIC":"NO","GCC_INLINES_ARE_PRIVATE_EXTERN":"YES","GCC_NO_COMMON_BLOCKS":"YES","GCC_OBJC_LEGACY_DISPATCH":"YES","GCC_OPTIMIZATION_LEVEL":"0","GCC_PFE_FILE_C_DIALECTS":"c objective-c c++ objective-c++","GCC_PREPROCESSOR_DEFINITIONS":"DEBUG=1 ","GCC_SYMBOLS_PRIVATE_EXTERN":"NO","GCC_TREAT_WARNINGS_AS_ERRORS":"NO","GCC_VERSION":"com.apple.compilers.llvm.clang.1_0","GCC_VERSION_IDENTIFIER":"com_apple_compilers_llvm_clang_1_0","GCC_WARN_64_TO_32_BIT_CONVERSION":"YES","GCC_WARN_ABOUT_RETURN_TYPE":"YES_ERROR","GCC_WARN_UNDECLARED_SELECTOR":"YES","GCC_WARN_UNINITIALIZED_AUTOS":"YES_AGGRESSIVE","GCC_WARN_UNUSED_FUNCTION":"YES","GCC_WARN_UNUSED_VARIABLE":"YES","GENERATED_MODULEMAP_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/GeneratedModuleMaps-iphonesimulator","GENERATE_INFOPLIST_FILE":"NO","GENERATE_MASTER_OBJECT_FILE":"NO","GENERATE_PKGINFO_FILE":"YES","GENERATE_PROFILING_CODE":"NO","GENERATE_TEXT_BASED_STUBS":"NO","GID":"20","GROUP":"staff","HEADERMAP_INCLUDES_FLAT_ENTRIES_FOR_TARGET_BEING_BUILT":"YES","HEADERMAP_INCLUDES_FRAMEWORK_ENTRIES_FOR_ALL_PRODUCT_TYPES":"YES","HEADERMAP_INCLUDES_NONPUBLIC_NONPRIVATE_HEADERS":"YES","HEADERMAP_INCLUDES_PROJECT_HEADERS":"YES","HEADERMAP_USES_FRAMEWORK_PREFIX_ENTRIES":"YES","HEADERMAP_USES_VFS":"NO","HEADER_SEARCH_PATHS":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/include ","HIDE_BITCODE_SYMBOLS":"YES","HOME":"/Users/<USER>","ICONV":"/usr/bin/iconv","INFOPLIST_EXPAND_BUILD_SETTINGS":"YES","INFOPLIST_FILE":"Runner/Info.plist","INFOPLIST_OUTPUT_FORMAT":"binary","INFOPLIST_PATH":"Runner.app/Info.plist","INFOPLIST_PREPROCESS":"NO","INFOSTRINGS_PATH":"Runner.app/en.lproj/InfoPlist.strings","INLINE_PRIVATE_FRAMEWORKS":"NO","INSTALLHDRS_COPY_PHASE":"NO","INSTALLHDRS_SCRIPT_PHASE":"NO","INSTALL_DIR":"/tmp/Runner.dst/Applications","INSTALL_GROUP":"staff","INSTALL_MODE_FLAG":"u+w,go-w,a+rX","INSTALL_OWNER":"dabzshot","INSTALL_PATH":"/Applications","INSTALL_ROOT":"/tmp/Runner.dst","IPHONEOS_DEPLOYMENT_TARGET":"12.0","JAVAC_DEFAULT_FLAGS":"-J-Xms64m -J-XX:NewSize=4M -J-Dfile.encoding=UTF8","JAVA_APP_STUB":"/System/Library/Frameworks/JavaVM.framework/Resources/MacOS/JavaApplicationStub","JAVA_ARCHIVE_CLASSES":"YES","JAVA_ARCHIVE_TYPE":"JAR","JAVA_COMPILER":"/usr/bin/javac","JAVA_FOLDER_PATH":"Runner.app/Java","JAVA_FRAMEWORK_RESOURCES_DIRS":"Resources","JAVA_JAR_FLAGS":"cv","JAVA_SOURCE_SUBDIR":".","JAVA_USE_DEPENDENCIES":"YES","JAVA_ZIP_FLAGS":"-urg","JIKES_DEFAULT_FLAGS":"+E +OLDCSO","KEEP_PRIVATE_EXTERNS":"NO","LD_DEPENDENCY_INFO_FILE":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/undefined_arch/Runner_dependency_info.dat","LD_GENERATE_MAP_FILE":"NO","LD_MAP_FILE_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-LinkMap-normal-undefined_arch.txt","LD_NO_PIE":"NO","LD_QUOTE_LINKER_ARGUMENTS_FOR_COMPILER_DRIVER":"YES","LD_RUNPATH_SEARCH_PATHS":" @executable_path/Frameworks","LEGACY_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/PlugIns/Xcode3Core.ideplugin/Contents/SharedSupport/Developer","LEX":"lex","LIBRARY_DEXT_INSTALL_PATH":"/Library/DriverExtensions","LIBRARY_FLAG_NOSPACE":"YES","LIBRARY_FLAG_PREFIX":"-l","LIBRARY_KEXT_INSTALL_PATH":"/Library/Extensions","LIBRARY_SEARCH_PATHS":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator  /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Flutter","LINKER_DISPLAYS_MANGLED_NAMES":"NO","LINK_FILE_LIST_normal_x86_64":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList","LINK_WITH_STANDARD_LIBRARIES":"YES","LLVM_TARGET_TRIPLE_OS_VERSION":"ios12.0","LLVM_TARGET_TRIPLE_SUFFIX":"-simulator","LLVM_TARGET_TRIPLE_VENDOR":"apple","LOCALIZATION_EXPORT_SUPPORTED":"YES","LOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app/en.lproj","LOCALIZED_STRING_MACRO_NAMES":"NSLocalizedString CFCopyLocalizedString","LOCALIZED_STRING_SWIFTUI_SUPPORT":"YES","LOCAL_ADMIN_APPS_DIR":"/Applications/Utilities","LOCAL_APPS_DIR":"/Applications","LOCAL_DEVELOPER_DIR":"/Library/Developer","LOCAL_LIBRARY_DIR":"/Library","LOCROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","LOCSYMROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","MACH_O_TYPE":"mh_execute","MAC_OS_X_PRODUCT_BUILD_VERSION":"20G95","MAC_OS_X_VERSION_ACTUAL":"110502","MAC_OS_X_VERSION_MAJOR":"110000","MAC_OS_X_VERSION_MINOR":"110500","MARKETING_VERSION":"2.0.86","METAL_LIBRARY_FILE_BASE":"default","METAL_LIBRARY_OUTPUT_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app","MODULES_FOLDER_PATH":"Runner.app/Modules","MODULE_CACHE_DIR":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex","MTL_ENABLE_DEBUG_INFO":"YES","NATIVE_ARCH":"x86_64","NATIVE_ARCH_32_BIT":"i386","NATIVE_ARCH_64_BIT":"x86_64","NATIVE_ARCH_ACTUAL":"x86_64","NO_COMMON":"YES","OBJC_ABI_VERSION":"2","OBJECT_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects","OBJECT_FILE_DIR_normal":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal","OBJROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","ONLY_ACTIVE_ARCH":"YES","OS":"MACOS","OSAC":"/usr/bin/osacompile","PACKAGE_CONFIG":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/.dart_tool/package_config.json","PACKAGE_TYPE":"com.apple.package-type.wrapper.application","PASCAL_STRINGS":"YES","PATH":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/libexec:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/local/bin:/Applications/Xcode.app/Contents/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/usr/local/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin","PATH_PREFIXES_EXCLUDED_FROM_HEADER_DEPENDENCIES":"/usr/include /usr/local/include /System/Library/Frameworks /System/Library/PrivateFrameworks /Applications/Xcode.app/Contents/Developer/Headers /Applications/Xcode.app/Contents/Developer/SDKs /Applications/Xcode.app/Contents/Developer/Platforms","PBDEVELOPMENTPLIST_PATH":"Runner.app/pbdevelopment.plist","PER_ARCH_OBJECT_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/undefined_arch","PER_VARIANT_OBJECT_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal","PKGINFO_FILE_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/PkgInfo","PKGINFO_PATH":"Runner.app/PkgInfo","PLATFORM_DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Applications","PLATFORM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/bin","PLATFORM_DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Library","PLATFORM_DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs","PLATFORM_DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Tools","PLATFORM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr","PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform","PLATFORM_DISPLAY_NAME":"iOS Simulator","PLATFORM_FAMILY_NAME":"iOS","PLATFORM_NAME":"iphonesimulator","PLATFORM_PREFERRED_ARCH":"x86_64","PLATFORM_PRODUCT_BUILD_VERSION":"19A339","PLIST_FILE_OUTPUT_FORMAT":"binary","PLUGINS_FOLDER_PATH":"Runner.app/PlugIns","PRECOMPS_INCLUDE_HEADERS_FROM_BUILT_PRODUCTS_DIR":"YES","PRECOMP_DESTINATION_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/PrefixHeaders","PRESERVE_DEAD_CODE_INITS_AND_TERMS":"NO","PRIVATE_HEADERS_FOLDER_PATH":"Runner.app/PrivateHeaders","PRODUCT_BUNDLE_IDENTIFIER":"likewallet.likewallet","PRODUCT_BUNDLE_PACKAGE_TYPE":"APPL","PRODUCT_MODULE_NAME":"Runner","PRODUCT_NAME":"Runner","PRODUCT_SETTINGS_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/Info.plist","PRODUCT_TYPE":"com.apple.product-type.application","PROFILING_CODE":"NO","PROJECT":"Runner","PROJECT_DERIVED_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/DerivedSources","PROJECT_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","PROJECT_FILE_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner.xcodeproj","PROJECT_NAME":"Runner","PROJECT_TEMP_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build","PROJECT_TEMP_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","PUBLIC_HEADERS_FOLDER_PATH":"Runner.app/Headers","RECURSIVE_SEARCH_PATHS_FOLLOW_SYMLINKS":"YES","REMOVE_CVS_FROM_RESOURCES":"YES","REMOVE_GIT_FROM_RESOURCES":"YES","REMOVE_HEADERS_FROM_EMBEDDED_BUNDLES":"YES","REMOVE_HG_FROM_RESOURCES":"YES","REMOVE_SVN_FROM_RESOURCES":"YES","REZ_COLLECTOR_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/ResourceManagerResources","REZ_OBJECTS_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/ResourceManagerResources/Objects","REZ_SEARCH_PATHS":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator ","SCAN_ALL_SOURCE_FILES_FOR_INCLUDES":"NO","SCRIPTS_FOLDER_PATH":"Runner.app/Scripts","SCRIPT_INPUT_FILE_COUNT":"0","SCRIPT_INPUT_FILE_LIST_COUNT":"0","SCRIPT_OUTPUT_FILE_COUNT":"0","SCRIPT_OUTPUT_FILE_LIST_COUNT":"0","SDKROOT":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator15.0.sdk","SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator15.0.sdk","SDK_DIR_iphonesimulator":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator15.0.sdk","SDK_DIR_iphonesimulator15_0":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator15.0.sdk","SDK_NAME":"iphonesimulator15.0","SDK_NAMES":"iphonesimulator15.0","SDK_PRODUCT_BUILD_VERSION":"19A339","SDK_VERSION":"15.0","SDK_VERSION_ACTUAL":"150000","SDK_VERSION_MAJOR":"150000","SDK_VERSION_MINOR":"150000","SED":"/usr/bin/sed","SEPARATE_STRIP":"NO","SEPARATE_SYMBOL_EDIT":"NO","SET_DIR_MODE_OWNER_GROUP":"YES","SET_FILE_MODE_OWNER_GROUP":"NO","SHALLOW_BUNDLE":"YES","SHARED_DERIVED_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/DerivedSources","SHARED_FRAMEWORKS_FOLDER_PATH":"Runner.app/SharedFrameworks","SHARED_PRECOMPS_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/SharedPrecompiledHeaders","SHARED_SUPPORT_FOLDER_PATH":"Runner.app/SharedSupport","SKIP_INSTALL":"NO","SOURCE_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","SRCROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","STRINGSDATA_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/undefined_arch","STRINGSDATA_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","STRINGS_FILE_INFOPLIST_RENAME":"YES","STRINGS_FILE_OUTPUT_ENCODING":"binary","STRIP_BITCODE_FROM_COPIED_FILES":"NO","STRIP_INSTALLED_PRODUCT":"NO","STRIP_STYLE":"all","STRIP_SWIFT_SYMBOLS":"YES","SUPPORTED_DEVICE_FAMILIES":"1,2","SUPPORTED_PLATFORMS":"iphoneos iphonesimulator","SUPPORTS_TEXT_BASED_API":"NO","SWIFT_EMIT_LOC_STRINGS":"NO","SWIFT_OBJC_BRIDGING_HEADER":"Runner/Runner-Bridging-Header.h","SWIFT_OPTIMIZATION_LEVEL":"-Onone","SWIFT_PLATFORM_TARGET_PREFIX":"ios","SWIFT_RESPONSE_FILE_PATH_normal_x86_64":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList","SWIFT_VERSION":"5.0","SYMROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","SYSTEM_ADMIN_APPS_DIR":"/Applications/Utilities","SYSTEM_APPS_DIR":"/Applications","SYSTEM_CORE_SERVICES_DIR":"/System/Library/CoreServices","SYSTEM_DEMOS_DIR":"/Applications/Extras","SYSTEM_DEVELOPER_APPS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","SYSTEM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","SYSTEM_DEVELOPER_DEMOS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities/Built Examples","SYSTEM_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","SYSTEM_DEVELOPER_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library","SYSTEM_DEVELOPER_GRAPHICS_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Graphics Tools","SYSTEM_DEVELOPER_JAVA_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Java Tools","SYSTEM_DEVELOPER_PERFORMANCE_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Performance Tools","SYSTEM_DEVELOPER_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes","SYSTEM_DEVELOPER_TOOLS":"/Applications/Xcode.app/Contents/Developer/Tools","SYSTEM_DEVELOPER_TOOLS_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/documentation/DeveloperTools","SYSTEM_DEVELOPER_TOOLS_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes/DeveloperTools","SYSTEM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","SYSTEM_DEVELOPER_UTILITIES_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities","SYSTEM_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","SYSTEM_DOCUMENTATION_DIR":"/Library/Documentation","SYSTEM_KEXT_INSTALL_PATH":"/System/Library/Extensions","SYSTEM_LIBRARY_DIR":"/System/Library","TAPI_VERIFY_MODE":"ErrorsOnly","TARGETED_DEVICE_FAMILY":"1,2","TARGETNAME":"Runner","TARGET_BUILD_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator","TARGET_DEVICE_IDENTIFIER":"7E256F87-A048-497E-B1B0-F06B3A14E031","TARGET_DEVICE_MODEL":"iPhone10,4","TARGET_DEVICE_OS_VERSION":"15.0","TARGET_DEVICE_PLATFORM_NAME":"iphonesimulator","TARGET_NAME":"Runner","TARGET_TEMP_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_FILES_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","TEST_FRAMEWORK_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Library/Frameworks /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator15.0.sdk/Developer/Library/Frameworks","TEST_LIBRARY_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/lib","TOOLCHAINS":"com.apple.dt.toolchain.XcodeDefault","TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","TRACK_WIDGET_CREATION":"true","TREAT_MISSING_BASELINES_AS_TEST_FAILURES":"NO","TREE_SHAKE_ICONS":"false","UID":"501","UNLOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app","UNSTRIPPED_PRODUCT":"NO","USER":"dabzshot","USER_APPS_DIR":"/Users/<USER>/Applications","USER_LIBRARY_DIR":"/Users/<USER>/Library","USE_DYNAMIC_NO_PIC":"YES","USE_HEADERMAP":"YES","USE_HEADER_SYMLINKS":"NO","USE_LLVM_TARGET_TRIPLES":"YES","USE_LLVM_TARGET_TRIPLES_FOR_CLANG":"YES","USE_LLVM_TARGET_TRIPLES_FOR_LD":"YES","USE_LLVM_TARGET_TRIPLES_FOR_TAPI":"YES","VALIDATE_DEVELOPMENT_ASSET_PATHS":"YES_ERROR","VALIDATE_PRODUCT":"NO","VALIDATE_WORKSPACE":"YES_ERROR","VALID_ARCHS":"arm64 arm64e i386 x86_64","VERBOSE_PBXCP":"NO","VERSIONING_SYSTEM":"apple-generic","VERSIONPLIST_PATH":"Runner.app/version.plist","VERSION_INFO_BUILDER":"dabzshot","VERSION_INFO_FILE":"Runner_vers.c","VERSION_INFO_STRING":"\"@(#)PROGRAM:Runner  PROJECT:Runner-207\"","WRAPPER_EXTENSION":"app","WRAPPER_NAME":"Runner.app","WRAPPER_SUFFIX":".app","WRAP_ASSET_PACKS_IN_SEPARATE_DIRECTORIES":"NO","XCODE_APP_SUPPORT_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Xcode","XCODE_PRODUCT_BUILD_VERSION":"13A1030d","XCODE_VERSION_ACTUAL":"1310","XCODE_VERSION_MAJOR":"1300","XCODE_VERSION_MINOR":"1310","XPCSERVICES_FOLDER_PATH":"Runner.app/XPCServices","YACC":"yacc","arch":"undefined_arch","variant":"normal"},"allow-missing-inputs":true,"always-out-of-date":true,"working-directory":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","control-enabled":false,"signature":"257880a1f443d0b84b17bee0a7739942"}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:PhaseScriptExecution Thin Binary /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh": {"tool":"shell","description":"PhaseScriptExecution Thin Binary /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase5-copy-files>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["<execute-shell-script-18c1723432283e0cc55f10a6dcfd9e02f1eee2015e8ff5ebcd27678f788c2826-target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49->"],"args":["/bin/sh","-c","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh"],"env":{"ACTION":"build","AD_HOC_CODE_SIGNING_ALLOWED":"YES","ALLOW_TARGET_PLATFORM_SPECIALIZATION":"NO","ALTERNATE_GROUP":"staff","ALTERNATE_MODE":"u+w,go-w,a+rX","ALTERNATE_OWNER":"dabzshot","ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES":"NO","ALWAYS_SEARCH_USER_PATHS":"NO","ALWAYS_USE_SEPARATE_HEADERMAPS":"NO","APPLE_INTERNAL_DEVELOPER_DIR":"/AppleInternal/Developer","APPLE_INTERNAL_DIR":"/AppleInternal","APPLE_INTERNAL_DOCUMENTATION_DIR":"/AppleInternal/Documentation","APPLE_INTERNAL_LIBRARY_DIR":"/AppleInternal/Library","APPLE_INTERNAL_TOOLS":"/AppleInternal/Developer/Tools","APPLICATION_EXTENSION_API_ONLY":"NO","APPLY_RULES_IN_COPY_FILES":"NO","APPLY_RULES_IN_COPY_HEADERS":"NO","ARCHS":"x86_64","ARCHS_STANDARD":"arm64 x86_64","ARCHS_STANDARD_32_64_BIT":"arm64 i386 x86_64","ARCHS_STANDARD_32_BIT":"i386","ARCHS_STANDARD_64_BIT":"arm64 x86_64","ARCHS_STANDARD_INCLUDING_64_BIT":"arm64 x86_64","ARCHS_UNIVERSAL_IPHONE_OS":"arm64 i386 x86_64","ASSETCATALOG_COMPILER_APPICON_NAME":"AppIcon","ASSETCATALOG_FILTER_FOR_DEVICE_MODEL":"iPhone10,4","ASSETCATALOG_FILTER_FOR_DEVICE_OS_VERSION":"15.0","AVAILABLE_PLATFORMS":"appletvos appletvsimulator driverkit iphoneos iphonesimulator macosx watchos watchsimulator","BITCODE_GENERATION_MODE":"marker","BUILD_ACTIVE_RESOURCES_ONLY":"YES","BUILD_COMPONENTS":"headers build","BUILD_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","BUILD_LIBRARY_FOR_DISTRIBUTION":"NO","BUILD_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","BUILD_STYLE":"","BUILD_VARIANTS":"normal","BUILT_PRODUCTS_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator","BUNDLE_CONTENTS_FOLDER_PATH_deep":"Contents/","BUNDLE_EXECUTABLE_FOLDER_NAME_deep":"MacOS","BUNDLE_FORMAT":"shallow","BUNDLE_FRAMEWORKS_FOLDER_PATH":"Frameworks","BUNDLE_PLUGINS_FOLDER_PATH":"PlugIns","BUNDLE_PRIVATE_HEADERS_FOLDER_PATH":"PrivateHeaders","BUNDLE_PUBLIC_HEADERS_FOLDER_PATH":"Headers","CACHE_ROOT":"/var/folders/81/hsp6b80d6fn5sq0f3xxdpfd00000gn/C/com.apple.DeveloperTools/13.1-13A1030d/Xcode","CCHROOT":"/var/folders/81/hsp6b80d6fn5sq0f3xxdpfd00000gn/C/com.apple.DeveloperTools/13.1-13A1030d/Xcode","CHMOD":"/bin/chmod","CHOWN":"/usr/sbin/chown","CLANG_ANALYZER_NONNULL":"YES","CLANG_CXX_LANGUAGE_STANDARD":"gnu++0x","CLANG_CXX_LIBRARY":"libc++","CLANG_ENABLE_MODULES":"YES","CLANG_ENABLE_OBJC_ARC":"YES","CLANG_MODULES_BUILD_SESSION_FILE":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation","CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING":"YES","CLANG_WARN_BOOL_CONVERSION":"YES","CLANG_WARN_COMMA":"YES","CLANG_WARN_CONSTANT_CONVERSION":"YES","CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS":"YES","CLANG_WARN_DIRECT_OBJC_ISA_USAGE":"YES_ERROR","CLANG_WARN_EMPTY_BODY":"YES","CLANG_WARN_ENUM_CONVERSION":"YES","CLANG_WARN_INFINITE_RECURSION":"YES","CLANG_WARN_INT_CONVERSION":"YES","CLANG_WARN_NON_LITERAL_NULL_CONVERSION":"YES","CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF":"YES","CLANG_WARN_OBJC_LITERAL_CONVERSION":"YES","CLANG_WARN_OBJC_ROOT_CLASS":"YES_ERROR","CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER":"YES","CLANG_WARN_RANGE_LOOP_ANALYSIS":"YES","CLANG_WARN_STRICT_PROTOTYPES":"YES","CLANG_WARN_SUSPICIOUS_MOVE":"YES","CLANG_WARN_UNREACHABLE_CODE":"YES","CLANG_WARN__DUPLICATE_METHOD_MATCH":"YES","CLASS_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/JavaClasses","CLEAN_PRECOMPS":"YES","CLONE_HEADERS":"NO","COCOAPODS_PARALLEL_CODE_SIGN":"true","CODESIGNING_FOLDER_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app","CODE_SIGNING_ALLOWED":"YES","CODE_SIGNING_REQUIRED":"YES","CODE_SIGN_CONTEXT_CLASS":"XCiPhoneSimulatorCodeSignContext","CODE_SIGN_ENTITLEMENTS":"Runner/Runner.entitlements","CODE_SIGN_IDENTITY":"Apple Development","CODE_SIGN_INJECT_BASE_ENTITLEMENTS":"YES","CODE_SIGN_STYLE":"Automatic","COLOR_DIAGNOSTICS":"NO","COMBINE_HIDPI_IMAGES":"NO","COMPILER_INDEX_STORE_ENABLE":"Default","COMPOSITE_SDK_DIRS":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/CompositeSDKs","COMPRESS_PNG_FILES":"YES","CONFIGURATION":"Debug","CONFIGURATION_BUILD_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator","CONFIGURATION_TEMP_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator","CONTENTS_FOLDER_PATH":"Runner.app","COPYING_PRESERVES_HFS_DATA":"NO","COPY_HEADERS_RUN_UNIFDEF":"NO","COPY_PHASE_STRIP":"NO","COPY_RESOURCES_FROM_STATIC_FRAMEWORKS":"YES","CORRESPONDING_DEVICE_PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform","CORRESPONDING_DEVICE_PLATFORM_NAME":"iphoneos","CORRESPONDING_DEVICE_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS15.0.sdk","CORRESPONDING_DEVICE_SDK_NAME":"iphoneos15.0","CP":"/bin/cp","CREATE_INFOPLIST_SECTION_IN_BINARY":"NO","CURRENT_ARCH":"undefined_arch","CURRENT_PROJECT_VERSION":"207","CURRENT_VARIANT":"normal","DART_DEFINES":"Zmx1dHRlci5pbnNwZWN0b3Iuc3RydWN0dXJlZEVycm9ycz10cnVl,RkxVVFRFUl9XRUJfQVVUT19ERVRFQ1Q9dHJ1ZQ==","DART_OBFUSCATION":"false","DEAD_CODE_STRIPPING":"YES","DEBUGGING_SYMBOLS":"YES","DEBUG_INFORMATION_FORMAT":"dwarf","DEFAULT_COMPILER":"com.apple.compilers.llvm.clang.1_0","DEFAULT_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","DEFAULT_KEXT_INSTALL_PATH":"/System/Library/Extensions","DEFINES_MODULE":"NO","DEPLOYMENT_LOCATION":"NO","DEPLOYMENT_POSTPROCESSING":"NO","DEPLOYMENT_TARGET_CLANG_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_CLANG_FLAG_NAME":"mios-simulator-version-min","DEPLOYMENT_TARGET_CLANG_FLAG_PREFIX":"-mios-simulator-version-min=","DEPLOYMENT_TARGET_LD_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_LD_FLAG_NAME":"ios_simulator_version_min","DEPLOYMENT_TARGET_SETTING_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_SUGGESTED_VALUES":"9.0 9.1 9.2 9.3 10.0 10.1 10.2 10.3 11.0 11.1 11.2 11.3 11.4 12.0 12.1 12.2 12.3 12.4 13.0 13.1 13.2 13.3 13.4 13.5 13.6 14.0 14.1 14.2 14.3 14.4 14.5 14.6 14.7 15.0","DERIVED_FILES_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","DERIVED_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","DERIVED_SOURCES_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","DEVELOPER_FRAMEWORKS_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_FRAMEWORKS_DIR_QUOTED":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Library","DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs","DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Tools","DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","DEVELOPMENT_LANGUAGE":"en","DEVELOPMENT_TEAM":"HJAN5D475T","DOCUMENTATION_FOLDER_PATH":"Runner.app/en.lproj/Documentation","DONT_GENERATE_INFOPLIST_FILE":"NO","DO_HEADER_SCANNING_IN_JAM":"NO","DSTROOT":"/tmp/Runner.dst","DT_TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","DWARF_DSYM_FILE_NAME":"Runner.app.dSYM","DWARF_DSYM_FILE_SHOULD_ACCOMPANY_PRODUCT":"NO","DWARF_DSYM_FOLDER_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator","EFFECTIVE_PLATFORM_NAME":"-iphonesimulator","EMBEDDED_CONTENT_CONTAINS_SWIFT":"NO","EMBED_ASSET_PACKS_IN_PRODUCT_BUNDLE":"NO","ENABLE_APP_SANDBOX":"NO","ENABLE_BITCODE":"NO","ENABLE_DEFAULT_HEADER_SEARCH_PATHS":"YES","ENABLE_HARDENED_RUNTIME":"NO","ENABLE_HEADER_DEPENDENCIES":"YES","ENABLE_ON_DEMAND_RESOURCES":"YES","ENABLE_PREVIEWS":"NO","ENABLE_STRICT_OBJC_MSGSEND":"YES","ENABLE_TESTABILITY":"YES","ENABLE_TESTING_SEARCH_PATHS":"NO","ENTITLEMENTS_DESTINATION":"__entitlements","ENTITLEMENTS_REQUIRED":"YES","EXCLUDED_ARCHS":"arm64 i386","EXCLUDED_INSTALLSRC_SUBDIRECTORY_PATTERNS":".DS_Store .svn .git .hg CVS","EXCLUDED_RECURSIVE_SEARCH_PATH_SUBDIRECTORIES":"*.nib *.lproj *.framework *.gch *.xcode* *.xcassets (*) .DS_Store CVS .svn .git .hg *.pbproj *.pbxproj","EXECUTABLES_FOLDER_PATH":"Runner.app/Executables","EXECUTABLE_FOLDER_PATH":"Runner.app","EXECUTABLE_NAME":"Runner","EXECUTABLE_PATH":"Runner.app/Runner","EXTRA_FRONT_END_OPTIONS":"--no-sound-null-safety","FILE_LIST":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects/LinkFileList","FIXED_FILES_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/FixedFiles","FLUTTER_APPLICATION_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3","FLUTTER_BUILD_DIR":"build","FLUTTER_BUILD_NAME":"2.0.36","FLUTTER_BUILD_NUMBER":"191","FLUTTER_ROOT":"/Users/<USER>/Desktop/flutter","FLUTTER_TARGET":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/lib/main.dart","FRAMEWORKS_FOLDER_PATH":"Runner.app/Frameworks","FRAMEWORK_FLAG_PREFIX":"-framework","FRAMEWORK_SEARCH_PATHS":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator  /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Flutter","FRAMEWORK_VERSION":"A","FULL_PRODUCT_NAME":"Runner.app","GCC3_VERSION":"3.3","GCC_C_LANGUAGE_STANDARD":"gnu99","GCC_DYNAMIC_NO_PIC":"NO","GCC_INLINES_ARE_PRIVATE_EXTERN":"YES","GCC_NO_COMMON_BLOCKS":"YES","GCC_OBJC_LEGACY_DISPATCH":"YES","GCC_OPTIMIZATION_LEVEL":"0","GCC_PFE_FILE_C_DIALECTS":"c objective-c c++ objective-c++","GCC_PREPROCESSOR_DEFINITIONS":"DEBUG=1 ","GCC_SYMBOLS_PRIVATE_EXTERN":"NO","GCC_TREAT_WARNINGS_AS_ERRORS":"NO","GCC_VERSION":"com.apple.compilers.llvm.clang.1_0","GCC_VERSION_IDENTIFIER":"com_apple_compilers_llvm_clang_1_0","GCC_WARN_64_TO_32_BIT_CONVERSION":"YES","GCC_WARN_ABOUT_RETURN_TYPE":"YES_ERROR","GCC_WARN_UNDECLARED_SELECTOR":"YES","GCC_WARN_UNINITIALIZED_AUTOS":"YES_AGGRESSIVE","GCC_WARN_UNUSED_FUNCTION":"YES","GCC_WARN_UNUSED_VARIABLE":"YES","GENERATED_MODULEMAP_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/GeneratedModuleMaps-iphonesimulator","GENERATE_INFOPLIST_FILE":"NO","GENERATE_MASTER_OBJECT_FILE":"NO","GENERATE_PKGINFO_FILE":"YES","GENERATE_PROFILING_CODE":"NO","GENERATE_TEXT_BASED_STUBS":"NO","GID":"20","GROUP":"staff","HEADERMAP_INCLUDES_FLAT_ENTRIES_FOR_TARGET_BEING_BUILT":"YES","HEADERMAP_INCLUDES_FRAMEWORK_ENTRIES_FOR_ALL_PRODUCT_TYPES":"YES","HEADERMAP_INCLUDES_NONPUBLIC_NONPRIVATE_HEADERS":"YES","HEADERMAP_INCLUDES_PROJECT_HEADERS":"YES","HEADERMAP_USES_FRAMEWORK_PREFIX_ENTRIES":"YES","HEADERMAP_USES_VFS":"NO","HEADER_SEARCH_PATHS":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/include ","HIDE_BITCODE_SYMBOLS":"YES","HOME":"/Users/<USER>","ICONV":"/usr/bin/iconv","INFOPLIST_EXPAND_BUILD_SETTINGS":"YES","INFOPLIST_FILE":"Runner/Info.plist","INFOPLIST_OUTPUT_FORMAT":"binary","INFOPLIST_PATH":"Runner.app/Info.plist","INFOPLIST_PREPROCESS":"NO","INFOSTRINGS_PATH":"Runner.app/en.lproj/InfoPlist.strings","INLINE_PRIVATE_FRAMEWORKS":"NO","INSTALLHDRS_COPY_PHASE":"NO","INSTALLHDRS_SCRIPT_PHASE":"NO","INSTALL_DIR":"/tmp/Runner.dst/Applications","INSTALL_GROUP":"staff","INSTALL_MODE_FLAG":"u+w,go-w,a+rX","INSTALL_OWNER":"dabzshot","INSTALL_PATH":"/Applications","INSTALL_ROOT":"/tmp/Runner.dst","IPHONEOS_DEPLOYMENT_TARGET":"12.0","JAVAC_DEFAULT_FLAGS":"-J-Xms64m -J-XX:NewSize=4M -J-Dfile.encoding=UTF8","JAVA_APP_STUB":"/System/Library/Frameworks/JavaVM.framework/Resources/MacOS/JavaApplicationStub","JAVA_ARCHIVE_CLASSES":"YES","JAVA_ARCHIVE_TYPE":"JAR","JAVA_COMPILER":"/usr/bin/javac","JAVA_FOLDER_PATH":"Runner.app/Java","JAVA_FRAMEWORK_RESOURCES_DIRS":"Resources","JAVA_JAR_FLAGS":"cv","JAVA_SOURCE_SUBDIR":".","JAVA_USE_DEPENDENCIES":"YES","JAVA_ZIP_FLAGS":"-urg","JIKES_DEFAULT_FLAGS":"+E +OLDCSO","KEEP_PRIVATE_EXTERNS":"NO","LD_DEPENDENCY_INFO_FILE":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/undefined_arch/Runner_dependency_info.dat","LD_GENERATE_MAP_FILE":"NO","LD_MAP_FILE_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-LinkMap-normal-undefined_arch.txt","LD_NO_PIE":"NO","LD_QUOTE_LINKER_ARGUMENTS_FOR_COMPILER_DRIVER":"YES","LD_RUNPATH_SEARCH_PATHS":" @executable_path/Frameworks","LEGACY_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/PlugIns/Xcode3Core.ideplugin/Contents/SharedSupport/Developer","LEX":"lex","LIBRARY_DEXT_INSTALL_PATH":"/Library/DriverExtensions","LIBRARY_FLAG_NOSPACE":"YES","LIBRARY_FLAG_PREFIX":"-l","LIBRARY_KEXT_INSTALL_PATH":"/Library/Extensions","LIBRARY_SEARCH_PATHS":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator  /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Flutter","LINKER_DISPLAYS_MANGLED_NAMES":"NO","LINK_FILE_LIST_normal_x86_64":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList","LINK_WITH_STANDARD_LIBRARIES":"YES","LLVM_TARGET_TRIPLE_OS_VERSION":"ios12.0","LLVM_TARGET_TRIPLE_SUFFIX":"-simulator","LLVM_TARGET_TRIPLE_VENDOR":"apple","LOCALIZATION_EXPORT_SUPPORTED":"YES","LOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app/en.lproj","LOCALIZED_STRING_MACRO_NAMES":"NSLocalizedString CFCopyLocalizedString","LOCALIZED_STRING_SWIFTUI_SUPPORT":"YES","LOCAL_ADMIN_APPS_DIR":"/Applications/Utilities","LOCAL_APPS_DIR":"/Applications","LOCAL_DEVELOPER_DIR":"/Library/Developer","LOCAL_LIBRARY_DIR":"/Library","LOCROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","LOCSYMROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","MACH_O_TYPE":"mh_execute","MAC_OS_X_PRODUCT_BUILD_VERSION":"20G95","MAC_OS_X_VERSION_ACTUAL":"110502","MAC_OS_X_VERSION_MAJOR":"110000","MAC_OS_X_VERSION_MINOR":"110500","MARKETING_VERSION":"2.0.86","METAL_LIBRARY_FILE_BASE":"default","METAL_LIBRARY_OUTPUT_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app","MODULES_FOLDER_PATH":"Runner.app/Modules","MODULE_CACHE_DIR":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex","MTL_ENABLE_DEBUG_INFO":"YES","NATIVE_ARCH":"x86_64","NATIVE_ARCH_32_BIT":"i386","NATIVE_ARCH_64_BIT":"x86_64","NATIVE_ARCH_ACTUAL":"x86_64","NO_COMMON":"YES","OBJC_ABI_VERSION":"2","OBJECT_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects","OBJECT_FILE_DIR_normal":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal","OBJROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","ONLY_ACTIVE_ARCH":"YES","OS":"MACOS","OSAC":"/usr/bin/osacompile","PACKAGE_CONFIG":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/.dart_tool/package_config.json","PACKAGE_TYPE":"com.apple.package-type.wrapper.application","PASCAL_STRINGS":"YES","PATH":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/libexec:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/local/bin:/Applications/Xcode.app/Contents/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/usr/local/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin","PATH_PREFIXES_EXCLUDED_FROM_HEADER_DEPENDENCIES":"/usr/include /usr/local/include /System/Library/Frameworks /System/Library/PrivateFrameworks /Applications/Xcode.app/Contents/Developer/Headers /Applications/Xcode.app/Contents/Developer/SDKs /Applications/Xcode.app/Contents/Developer/Platforms","PBDEVELOPMENTPLIST_PATH":"Runner.app/pbdevelopment.plist","PER_ARCH_OBJECT_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/undefined_arch","PER_VARIANT_OBJECT_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal","PKGINFO_FILE_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/PkgInfo","PKGINFO_PATH":"Runner.app/PkgInfo","PLATFORM_DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Applications","PLATFORM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/bin","PLATFORM_DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Library","PLATFORM_DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs","PLATFORM_DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Tools","PLATFORM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr","PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform","PLATFORM_DISPLAY_NAME":"iOS Simulator","PLATFORM_FAMILY_NAME":"iOS","PLATFORM_NAME":"iphonesimulator","PLATFORM_PREFERRED_ARCH":"x86_64","PLATFORM_PRODUCT_BUILD_VERSION":"19A339","PLIST_FILE_OUTPUT_FORMAT":"binary","PLUGINS_FOLDER_PATH":"Runner.app/PlugIns","PRECOMPS_INCLUDE_HEADERS_FROM_BUILT_PRODUCTS_DIR":"YES","PRECOMP_DESTINATION_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/PrefixHeaders","PRESERVE_DEAD_CODE_INITS_AND_TERMS":"NO","PRIVATE_HEADERS_FOLDER_PATH":"Runner.app/PrivateHeaders","PRODUCT_BUNDLE_IDENTIFIER":"likewallet.likewallet","PRODUCT_BUNDLE_PACKAGE_TYPE":"APPL","PRODUCT_MODULE_NAME":"Runner","PRODUCT_NAME":"Runner","PRODUCT_SETTINGS_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/Info.plist","PRODUCT_TYPE":"com.apple.product-type.application","PROFILING_CODE":"NO","PROJECT":"Runner","PROJECT_DERIVED_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/DerivedSources","PROJECT_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","PROJECT_FILE_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner.xcodeproj","PROJECT_NAME":"Runner","PROJECT_TEMP_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build","PROJECT_TEMP_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","PUBLIC_HEADERS_FOLDER_PATH":"Runner.app/Headers","RECURSIVE_SEARCH_PATHS_FOLLOW_SYMLINKS":"YES","REMOVE_CVS_FROM_RESOURCES":"YES","REMOVE_GIT_FROM_RESOURCES":"YES","REMOVE_HEADERS_FROM_EMBEDDED_BUNDLES":"YES","REMOVE_HG_FROM_RESOURCES":"YES","REMOVE_SVN_FROM_RESOURCES":"YES","REZ_COLLECTOR_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/ResourceManagerResources","REZ_OBJECTS_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/ResourceManagerResources/Objects","REZ_SEARCH_PATHS":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator ","SCAN_ALL_SOURCE_FILES_FOR_INCLUDES":"NO","SCRIPTS_FOLDER_PATH":"Runner.app/Scripts","SCRIPT_INPUT_FILE_COUNT":"0","SCRIPT_INPUT_FILE_LIST_COUNT":"0","SCRIPT_OUTPUT_FILE_COUNT":"0","SCRIPT_OUTPUT_FILE_LIST_COUNT":"0","SDKROOT":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator15.0.sdk","SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator15.0.sdk","SDK_DIR_iphonesimulator":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator15.0.sdk","SDK_DIR_iphonesimulator15_0":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator15.0.sdk","SDK_NAME":"iphonesimulator15.0","SDK_NAMES":"iphonesimulator15.0","SDK_PRODUCT_BUILD_VERSION":"19A339","SDK_VERSION":"15.0","SDK_VERSION_ACTUAL":"150000","SDK_VERSION_MAJOR":"150000","SDK_VERSION_MINOR":"150000","SED":"/usr/bin/sed","SEPARATE_STRIP":"NO","SEPARATE_SYMBOL_EDIT":"NO","SET_DIR_MODE_OWNER_GROUP":"YES","SET_FILE_MODE_OWNER_GROUP":"NO","SHALLOW_BUNDLE":"YES","SHARED_DERIVED_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/DerivedSources","SHARED_FRAMEWORKS_FOLDER_PATH":"Runner.app/SharedFrameworks","SHARED_PRECOMPS_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/SharedPrecompiledHeaders","SHARED_SUPPORT_FOLDER_PATH":"Runner.app/SharedSupport","SKIP_INSTALL":"NO","SOURCE_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","SRCROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","STRINGSDATA_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/undefined_arch","STRINGSDATA_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","STRINGS_FILE_INFOPLIST_RENAME":"YES","STRINGS_FILE_OUTPUT_ENCODING":"binary","STRIP_BITCODE_FROM_COPIED_FILES":"NO","STRIP_INSTALLED_PRODUCT":"NO","STRIP_STYLE":"all","STRIP_SWIFT_SYMBOLS":"YES","SUPPORTED_DEVICE_FAMILIES":"1,2","SUPPORTED_PLATFORMS":"iphoneos iphonesimulator","SUPPORTS_TEXT_BASED_API":"NO","SWIFT_EMIT_LOC_STRINGS":"NO","SWIFT_OBJC_BRIDGING_HEADER":"Runner/Runner-Bridging-Header.h","SWIFT_OPTIMIZATION_LEVEL":"-Onone","SWIFT_PLATFORM_TARGET_PREFIX":"ios","SWIFT_RESPONSE_FILE_PATH_normal_x86_64":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList","SWIFT_VERSION":"5.0","SYMROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","SYSTEM_ADMIN_APPS_DIR":"/Applications/Utilities","SYSTEM_APPS_DIR":"/Applications","SYSTEM_CORE_SERVICES_DIR":"/System/Library/CoreServices","SYSTEM_DEMOS_DIR":"/Applications/Extras","SYSTEM_DEVELOPER_APPS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","SYSTEM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","SYSTEM_DEVELOPER_DEMOS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities/Built Examples","SYSTEM_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","SYSTEM_DEVELOPER_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library","SYSTEM_DEVELOPER_GRAPHICS_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Graphics Tools","SYSTEM_DEVELOPER_JAVA_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Java Tools","SYSTEM_DEVELOPER_PERFORMANCE_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Performance Tools","SYSTEM_DEVELOPER_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes","SYSTEM_DEVELOPER_TOOLS":"/Applications/Xcode.app/Contents/Developer/Tools","SYSTEM_DEVELOPER_TOOLS_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/documentation/DeveloperTools","SYSTEM_DEVELOPER_TOOLS_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes/DeveloperTools","SYSTEM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","SYSTEM_DEVELOPER_UTILITIES_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities","SYSTEM_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","SYSTEM_DOCUMENTATION_DIR":"/Library/Documentation","SYSTEM_KEXT_INSTALL_PATH":"/System/Library/Extensions","SYSTEM_LIBRARY_DIR":"/System/Library","TAPI_VERIFY_MODE":"ErrorsOnly","TARGETED_DEVICE_FAMILY":"1,2","TARGETNAME":"Runner","TARGET_BUILD_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator","TARGET_DEVICE_IDENTIFIER":"7E256F87-A048-497E-B1B0-F06B3A14E031","TARGET_DEVICE_MODEL":"iPhone10,4","TARGET_DEVICE_OS_VERSION":"15.0","TARGET_DEVICE_PLATFORM_NAME":"iphonesimulator","TARGET_NAME":"Runner","TARGET_TEMP_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_FILES_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","TEST_FRAMEWORK_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Library/Frameworks /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator15.0.sdk/Developer/Library/Frameworks","TEST_LIBRARY_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/lib","TOOLCHAINS":"com.apple.dt.toolchain.XcodeDefault","TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","TRACK_WIDGET_CREATION":"true","TREAT_MISSING_BASELINES_AS_TEST_FAILURES":"NO","TREE_SHAKE_ICONS":"false","UID":"501","UNLOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app","UNSTRIPPED_PRODUCT":"NO","USER":"dabzshot","USER_APPS_DIR":"/Users/<USER>/Applications","USER_LIBRARY_DIR":"/Users/<USER>/Library","USE_DYNAMIC_NO_PIC":"YES","USE_HEADERMAP":"YES","USE_HEADER_SYMLINKS":"NO","USE_LLVM_TARGET_TRIPLES":"YES","USE_LLVM_TARGET_TRIPLES_FOR_CLANG":"YES","USE_LLVM_TARGET_TRIPLES_FOR_LD":"YES","USE_LLVM_TARGET_TRIPLES_FOR_TAPI":"YES","VALIDATE_DEVELOPMENT_ASSET_PATHS":"YES_ERROR","VALIDATE_PRODUCT":"NO","VALIDATE_WORKSPACE":"YES_ERROR","VALID_ARCHS":"arm64 arm64e i386 x86_64","VERBOSE_PBXCP":"NO","VERSIONING_SYSTEM":"apple-generic","VERSIONPLIST_PATH":"Runner.app/version.plist","VERSION_INFO_BUILDER":"dabzshot","VERSION_INFO_FILE":"Runner_vers.c","VERSION_INFO_STRING":"\"@(#)PROGRAM:Runner  PROJECT:Runner-207\"","WRAPPER_EXTENSION":"app","WRAPPER_NAME":"Runner.app","WRAPPER_SUFFIX":".app","WRAP_ASSET_PACKS_IN_SEPARATE_DIRECTORIES":"NO","XCODE_APP_SUPPORT_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Xcode","XCODE_PRODUCT_BUILD_VERSION":"13A1030d","XCODE_VERSION_ACTUAL":"1310","XCODE_VERSION_MAJOR":"1300","XCODE_VERSION_MINOR":"1310","XPCSERVICES_FOLDER_PATH":"Runner.app/XPCServices","YACC":"yacc","arch":"undefined_arch","variant":"normal"},"allow-missing-inputs":true,"always-out-of-date":true,"working-directory":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","control-enabled":false,"signature":"8d5c57a360bf9a70de0321dba8d5d7c0"}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:PhaseScriptExecution [CP] Check Pods Manifest.lock /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-328736DAE7F587435A6E60E5.sh": {"tool":"shell","description":"PhaseScriptExecution [CP] Check Pods Manifest.lock /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-328736DAE7F587435A6E60E5.sh","inputs":["/Podfile.lock","/Manifest.lock","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-328736DAE7F587435A6E60E5.sh","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Pods-Runner-checkManifestLockResult.txt"],"args":["/bin/sh","-c","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-328736DAE7F587435A6E60E5.sh"],"env":{"ACTION":"build","AD_HOC_CODE_SIGNING_ALLOWED":"YES","ALLOW_TARGET_PLATFORM_SPECIALIZATION":"NO","ALTERNATE_GROUP":"staff","ALTERNATE_MODE":"u+w,go-w,a+rX","ALTERNATE_OWNER":"dabzshot","ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES":"NO","ALWAYS_SEARCH_USER_PATHS":"NO","ALWAYS_USE_SEPARATE_HEADERMAPS":"NO","APPLE_INTERNAL_DEVELOPER_DIR":"/AppleInternal/Developer","APPLE_INTERNAL_DIR":"/AppleInternal","APPLE_INTERNAL_DOCUMENTATION_DIR":"/AppleInternal/Documentation","APPLE_INTERNAL_LIBRARY_DIR":"/AppleInternal/Library","APPLE_INTERNAL_TOOLS":"/AppleInternal/Developer/Tools","APPLICATION_EXTENSION_API_ONLY":"NO","APPLY_RULES_IN_COPY_FILES":"NO","APPLY_RULES_IN_COPY_HEADERS":"NO","ARCHS":"x86_64","ARCHS_STANDARD":"arm64 x86_64","ARCHS_STANDARD_32_64_BIT":"arm64 i386 x86_64","ARCHS_STANDARD_32_BIT":"i386","ARCHS_STANDARD_64_BIT":"arm64 x86_64","ARCHS_STANDARD_INCLUDING_64_BIT":"arm64 x86_64","ARCHS_UNIVERSAL_IPHONE_OS":"arm64 i386 x86_64","ASSETCATALOG_COMPILER_APPICON_NAME":"AppIcon","ASSETCATALOG_FILTER_FOR_DEVICE_MODEL":"iPhone10,4","ASSETCATALOG_FILTER_FOR_DEVICE_OS_VERSION":"15.0","AVAILABLE_PLATFORMS":"appletvos appletvsimulator driverkit iphoneos iphonesimulator macosx watchos watchsimulator","BITCODE_GENERATION_MODE":"marker","BUILD_ACTIVE_RESOURCES_ONLY":"YES","BUILD_COMPONENTS":"headers build","BUILD_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","BUILD_LIBRARY_FOR_DISTRIBUTION":"NO","BUILD_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","BUILD_STYLE":"","BUILD_VARIANTS":"normal","BUILT_PRODUCTS_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator","BUNDLE_CONTENTS_FOLDER_PATH_deep":"Contents/","BUNDLE_EXECUTABLE_FOLDER_NAME_deep":"MacOS","BUNDLE_FORMAT":"shallow","BUNDLE_FRAMEWORKS_FOLDER_PATH":"Frameworks","BUNDLE_PLUGINS_FOLDER_PATH":"PlugIns","BUNDLE_PRIVATE_HEADERS_FOLDER_PATH":"PrivateHeaders","BUNDLE_PUBLIC_HEADERS_FOLDER_PATH":"Headers","CACHE_ROOT":"/var/folders/81/hsp6b80d6fn5sq0f3xxdpfd00000gn/C/com.apple.DeveloperTools/13.1-13A1030d/Xcode","CCHROOT":"/var/folders/81/hsp6b80d6fn5sq0f3xxdpfd00000gn/C/com.apple.DeveloperTools/13.1-13A1030d/Xcode","CHMOD":"/bin/chmod","CHOWN":"/usr/sbin/chown","CLANG_ANALYZER_NONNULL":"YES","CLANG_CXX_LANGUAGE_STANDARD":"gnu++0x","CLANG_CXX_LIBRARY":"libc++","CLANG_ENABLE_MODULES":"YES","CLANG_ENABLE_OBJC_ARC":"YES","CLANG_MODULES_BUILD_SESSION_FILE":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation","CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING":"YES","CLANG_WARN_BOOL_CONVERSION":"YES","CLANG_WARN_COMMA":"YES","CLANG_WARN_CONSTANT_CONVERSION":"YES","CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS":"YES","CLANG_WARN_DIRECT_OBJC_ISA_USAGE":"YES_ERROR","CLANG_WARN_EMPTY_BODY":"YES","CLANG_WARN_ENUM_CONVERSION":"YES","CLANG_WARN_INFINITE_RECURSION":"YES","CLANG_WARN_INT_CONVERSION":"YES","CLANG_WARN_NON_LITERAL_NULL_CONVERSION":"YES","CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF":"YES","CLANG_WARN_OBJC_LITERAL_CONVERSION":"YES","CLANG_WARN_OBJC_ROOT_CLASS":"YES_ERROR","CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER":"YES","CLANG_WARN_RANGE_LOOP_ANALYSIS":"YES","CLANG_WARN_STRICT_PROTOTYPES":"YES","CLANG_WARN_SUSPICIOUS_MOVE":"YES","CLANG_WARN_UNREACHABLE_CODE":"YES","CLANG_WARN__DUPLICATE_METHOD_MATCH":"YES","CLASS_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/JavaClasses","CLEAN_PRECOMPS":"YES","CLONE_HEADERS":"NO","COCOAPODS_PARALLEL_CODE_SIGN":"true","CODESIGNING_FOLDER_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app","CODE_SIGNING_ALLOWED":"YES","CODE_SIGNING_REQUIRED":"YES","CODE_SIGN_CONTEXT_CLASS":"XCiPhoneSimulatorCodeSignContext","CODE_SIGN_ENTITLEMENTS":"Runner/Runner.entitlements","CODE_SIGN_IDENTITY":"Apple Development","CODE_SIGN_INJECT_BASE_ENTITLEMENTS":"YES","CODE_SIGN_STYLE":"Automatic","COLOR_DIAGNOSTICS":"NO","COMBINE_HIDPI_IMAGES":"NO","COMPILER_INDEX_STORE_ENABLE":"Default","COMPOSITE_SDK_DIRS":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/CompositeSDKs","COMPRESS_PNG_FILES":"YES","CONFIGURATION":"Debug","CONFIGURATION_BUILD_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator","CONFIGURATION_TEMP_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator","CONTENTS_FOLDER_PATH":"Runner.app","COPYING_PRESERVES_HFS_DATA":"NO","COPY_HEADERS_RUN_UNIFDEF":"NO","COPY_PHASE_STRIP":"NO","COPY_RESOURCES_FROM_STATIC_FRAMEWORKS":"YES","CORRESPONDING_DEVICE_PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform","CORRESPONDING_DEVICE_PLATFORM_NAME":"iphoneos","CORRESPONDING_DEVICE_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS15.0.sdk","CORRESPONDING_DEVICE_SDK_NAME":"iphoneos15.0","CP":"/bin/cp","CREATE_INFOPLIST_SECTION_IN_BINARY":"NO","CURRENT_ARCH":"undefined_arch","CURRENT_PROJECT_VERSION":"207","CURRENT_VARIANT":"normal","DART_DEFINES":"Zmx1dHRlci5pbnNwZWN0b3Iuc3RydWN0dXJlZEVycm9ycz10cnVl,RkxVVFRFUl9XRUJfQVVUT19ERVRFQ1Q9dHJ1ZQ==","DART_OBFUSCATION":"false","DEAD_CODE_STRIPPING":"YES","DEBUGGING_SYMBOLS":"YES","DEBUG_INFORMATION_FORMAT":"dwarf","DEFAULT_COMPILER":"com.apple.compilers.llvm.clang.1_0","DEFAULT_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","DEFAULT_KEXT_INSTALL_PATH":"/System/Library/Extensions","DEFINES_MODULE":"NO","DEPLOYMENT_LOCATION":"NO","DEPLOYMENT_POSTPROCESSING":"NO","DEPLOYMENT_TARGET_CLANG_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_CLANG_FLAG_NAME":"mios-simulator-version-min","DEPLOYMENT_TARGET_CLANG_FLAG_PREFIX":"-mios-simulator-version-min=","DEPLOYMENT_TARGET_LD_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_LD_FLAG_NAME":"ios_simulator_version_min","DEPLOYMENT_TARGET_SETTING_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_SUGGESTED_VALUES":"9.0 9.1 9.2 9.3 10.0 10.1 10.2 10.3 11.0 11.1 11.2 11.3 11.4 12.0 12.1 12.2 12.3 12.4 13.0 13.1 13.2 13.3 13.4 13.5 13.6 14.0 14.1 14.2 14.3 14.4 14.5 14.6 14.7 15.0","DERIVED_FILES_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","DERIVED_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","DERIVED_SOURCES_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","DEVELOPER_FRAMEWORKS_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_FRAMEWORKS_DIR_QUOTED":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Library","DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs","DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Tools","DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","DEVELOPMENT_LANGUAGE":"en","DEVELOPMENT_TEAM":"HJAN5D475T","DOCUMENTATION_FOLDER_PATH":"Runner.app/en.lproj/Documentation","DONT_GENERATE_INFOPLIST_FILE":"NO","DO_HEADER_SCANNING_IN_JAM":"NO","DSTROOT":"/tmp/Runner.dst","DT_TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","DWARF_DSYM_FILE_NAME":"Runner.app.dSYM","DWARF_DSYM_FILE_SHOULD_ACCOMPANY_PRODUCT":"NO","DWARF_DSYM_FOLDER_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator","EFFECTIVE_PLATFORM_NAME":"-iphonesimulator","EMBEDDED_CONTENT_CONTAINS_SWIFT":"NO","EMBED_ASSET_PACKS_IN_PRODUCT_BUNDLE":"NO","ENABLE_APP_SANDBOX":"NO","ENABLE_BITCODE":"NO","ENABLE_DEFAULT_HEADER_SEARCH_PATHS":"YES","ENABLE_HARDENED_RUNTIME":"NO","ENABLE_HEADER_DEPENDENCIES":"YES","ENABLE_ON_DEMAND_RESOURCES":"YES","ENABLE_PREVIEWS":"NO","ENABLE_STRICT_OBJC_MSGSEND":"YES","ENABLE_TESTABILITY":"YES","ENABLE_TESTING_SEARCH_PATHS":"NO","ENTITLEMENTS_DESTINATION":"__entitlements","ENTITLEMENTS_REQUIRED":"YES","EXCLUDED_ARCHS":"arm64 i386","EXCLUDED_INSTALLSRC_SUBDIRECTORY_PATTERNS":".DS_Store .svn .git .hg CVS","EXCLUDED_RECURSIVE_SEARCH_PATH_SUBDIRECTORIES":"*.nib *.lproj *.framework *.gch *.xcode* *.xcassets (*) .DS_Store CVS .svn .git .hg *.pbproj *.pbxproj","EXECUTABLES_FOLDER_PATH":"Runner.app/Executables","EXECUTABLE_FOLDER_PATH":"Runner.app","EXECUTABLE_NAME":"Runner","EXECUTABLE_PATH":"Runner.app/Runner","EXTRA_FRONT_END_OPTIONS":"--no-sound-null-safety","FILE_LIST":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects/LinkFileList","FIXED_FILES_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/FixedFiles","FLUTTER_APPLICATION_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3","FLUTTER_BUILD_DIR":"build","FLUTTER_BUILD_NAME":"2.0.36","FLUTTER_BUILD_NUMBER":"191","FLUTTER_ROOT":"/Users/<USER>/Desktop/flutter","FLUTTER_TARGET":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/lib/main.dart","FRAMEWORKS_FOLDER_PATH":"Runner.app/Frameworks","FRAMEWORK_FLAG_PREFIX":"-framework","FRAMEWORK_SEARCH_PATHS":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator  /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Flutter","FRAMEWORK_VERSION":"A","FULL_PRODUCT_NAME":"Runner.app","GCC3_VERSION":"3.3","GCC_C_LANGUAGE_STANDARD":"gnu99","GCC_DYNAMIC_NO_PIC":"NO","GCC_INLINES_ARE_PRIVATE_EXTERN":"YES","GCC_NO_COMMON_BLOCKS":"YES","GCC_OBJC_LEGACY_DISPATCH":"YES","GCC_OPTIMIZATION_LEVEL":"0","GCC_PFE_FILE_C_DIALECTS":"c objective-c c++ objective-c++","GCC_PREPROCESSOR_DEFINITIONS":"DEBUG=1 ","GCC_SYMBOLS_PRIVATE_EXTERN":"NO","GCC_TREAT_WARNINGS_AS_ERRORS":"NO","GCC_VERSION":"com.apple.compilers.llvm.clang.1_0","GCC_VERSION_IDENTIFIER":"com_apple_compilers_llvm_clang_1_0","GCC_WARN_64_TO_32_BIT_CONVERSION":"YES","GCC_WARN_ABOUT_RETURN_TYPE":"YES_ERROR","GCC_WARN_UNDECLARED_SELECTOR":"YES","GCC_WARN_UNINITIALIZED_AUTOS":"YES_AGGRESSIVE","GCC_WARN_UNUSED_FUNCTION":"YES","GCC_WARN_UNUSED_VARIABLE":"YES","GENERATED_MODULEMAP_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/GeneratedModuleMaps-iphonesimulator","GENERATE_INFOPLIST_FILE":"NO","GENERATE_MASTER_OBJECT_FILE":"NO","GENERATE_PKGINFO_FILE":"YES","GENERATE_PROFILING_CODE":"NO","GENERATE_TEXT_BASED_STUBS":"NO","GID":"20","GROUP":"staff","HEADERMAP_INCLUDES_FLAT_ENTRIES_FOR_TARGET_BEING_BUILT":"YES","HEADERMAP_INCLUDES_FRAMEWORK_ENTRIES_FOR_ALL_PRODUCT_TYPES":"YES","HEADERMAP_INCLUDES_NONPUBLIC_NONPRIVATE_HEADERS":"YES","HEADERMAP_INCLUDES_PROJECT_HEADERS":"YES","HEADERMAP_USES_FRAMEWORK_PREFIX_ENTRIES":"YES","HEADERMAP_USES_VFS":"NO","HEADER_SEARCH_PATHS":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/include ","HIDE_BITCODE_SYMBOLS":"YES","HOME":"/Users/<USER>","ICONV":"/usr/bin/iconv","INFOPLIST_EXPAND_BUILD_SETTINGS":"YES","INFOPLIST_FILE":"Runner/Info.plist","INFOPLIST_OUTPUT_FORMAT":"binary","INFOPLIST_PATH":"Runner.app/Info.plist","INFOPLIST_PREPROCESS":"NO","INFOSTRINGS_PATH":"Runner.app/en.lproj/InfoPlist.strings","INLINE_PRIVATE_FRAMEWORKS":"NO","INSTALLHDRS_COPY_PHASE":"NO","INSTALLHDRS_SCRIPT_PHASE":"NO","INSTALL_DIR":"/tmp/Runner.dst/Applications","INSTALL_GROUP":"staff","INSTALL_MODE_FLAG":"u+w,go-w,a+rX","INSTALL_OWNER":"dabzshot","INSTALL_PATH":"/Applications","INSTALL_ROOT":"/tmp/Runner.dst","IPHONEOS_DEPLOYMENT_TARGET":"12.0","JAVAC_DEFAULT_FLAGS":"-J-Xms64m -J-XX:NewSize=4M -J-Dfile.encoding=UTF8","JAVA_APP_STUB":"/System/Library/Frameworks/JavaVM.framework/Resources/MacOS/JavaApplicationStub","JAVA_ARCHIVE_CLASSES":"YES","JAVA_ARCHIVE_TYPE":"JAR","JAVA_COMPILER":"/usr/bin/javac","JAVA_FOLDER_PATH":"Runner.app/Java","JAVA_FRAMEWORK_RESOURCES_DIRS":"Resources","JAVA_JAR_FLAGS":"cv","JAVA_SOURCE_SUBDIR":".","JAVA_USE_DEPENDENCIES":"YES","JAVA_ZIP_FLAGS":"-urg","JIKES_DEFAULT_FLAGS":"+E +OLDCSO","KEEP_PRIVATE_EXTERNS":"NO","LD_DEPENDENCY_INFO_FILE":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/undefined_arch/Runner_dependency_info.dat","LD_GENERATE_MAP_FILE":"NO","LD_MAP_FILE_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-LinkMap-normal-undefined_arch.txt","LD_NO_PIE":"NO","LD_QUOTE_LINKER_ARGUMENTS_FOR_COMPILER_DRIVER":"YES","LD_RUNPATH_SEARCH_PATHS":" @executable_path/Frameworks","LEGACY_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/PlugIns/Xcode3Core.ideplugin/Contents/SharedSupport/Developer","LEX":"lex","LIBRARY_DEXT_INSTALL_PATH":"/Library/DriverExtensions","LIBRARY_FLAG_NOSPACE":"YES","LIBRARY_FLAG_PREFIX":"-l","LIBRARY_KEXT_INSTALL_PATH":"/Library/Extensions","LIBRARY_SEARCH_PATHS":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator  /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Flutter","LINKER_DISPLAYS_MANGLED_NAMES":"NO","LINK_FILE_LIST_normal_x86_64":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList","LINK_WITH_STANDARD_LIBRARIES":"YES","LLVM_TARGET_TRIPLE_OS_VERSION":"ios12.0","LLVM_TARGET_TRIPLE_SUFFIX":"-simulator","LLVM_TARGET_TRIPLE_VENDOR":"apple","LOCALIZATION_EXPORT_SUPPORTED":"YES","LOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app/en.lproj","LOCALIZED_STRING_MACRO_NAMES":"NSLocalizedString CFCopyLocalizedString","LOCALIZED_STRING_SWIFTUI_SUPPORT":"YES","LOCAL_ADMIN_APPS_DIR":"/Applications/Utilities","LOCAL_APPS_DIR":"/Applications","LOCAL_DEVELOPER_DIR":"/Library/Developer","LOCAL_LIBRARY_DIR":"/Library","LOCROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","LOCSYMROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","MACH_O_TYPE":"mh_execute","MAC_OS_X_PRODUCT_BUILD_VERSION":"20G95","MAC_OS_X_VERSION_ACTUAL":"110502","MAC_OS_X_VERSION_MAJOR":"110000","MAC_OS_X_VERSION_MINOR":"110500","MARKETING_VERSION":"2.0.86","METAL_LIBRARY_FILE_BASE":"default","METAL_LIBRARY_OUTPUT_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app","MODULES_FOLDER_PATH":"Runner.app/Modules","MODULE_CACHE_DIR":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex","MTL_ENABLE_DEBUG_INFO":"YES","NATIVE_ARCH":"x86_64","NATIVE_ARCH_32_BIT":"i386","NATIVE_ARCH_64_BIT":"x86_64","NATIVE_ARCH_ACTUAL":"x86_64","NO_COMMON":"YES","OBJC_ABI_VERSION":"2","OBJECT_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects","OBJECT_FILE_DIR_normal":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal","OBJROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","ONLY_ACTIVE_ARCH":"YES","OS":"MACOS","OSAC":"/usr/bin/osacompile","PACKAGE_CONFIG":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/.dart_tool/package_config.json","PACKAGE_TYPE":"com.apple.package-type.wrapper.application","PASCAL_STRINGS":"YES","PATH":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/libexec:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/local/bin:/Applications/Xcode.app/Contents/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/usr/local/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin","PATH_PREFIXES_EXCLUDED_FROM_HEADER_DEPENDENCIES":"/usr/include /usr/local/include /System/Library/Frameworks /System/Library/PrivateFrameworks /Applications/Xcode.app/Contents/Developer/Headers /Applications/Xcode.app/Contents/Developer/SDKs /Applications/Xcode.app/Contents/Developer/Platforms","PBDEVELOPMENTPLIST_PATH":"Runner.app/pbdevelopment.plist","PER_ARCH_OBJECT_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/undefined_arch","PER_VARIANT_OBJECT_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal","PKGINFO_FILE_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/PkgInfo","PKGINFO_PATH":"Runner.app/PkgInfo","PLATFORM_DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Applications","PLATFORM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/bin","PLATFORM_DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Library","PLATFORM_DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs","PLATFORM_DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Tools","PLATFORM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr","PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform","PLATFORM_DISPLAY_NAME":"iOS Simulator","PLATFORM_FAMILY_NAME":"iOS","PLATFORM_NAME":"iphonesimulator","PLATFORM_PREFERRED_ARCH":"x86_64","PLATFORM_PRODUCT_BUILD_VERSION":"19A339","PLIST_FILE_OUTPUT_FORMAT":"binary","PLUGINS_FOLDER_PATH":"Runner.app/PlugIns","PRECOMPS_INCLUDE_HEADERS_FROM_BUILT_PRODUCTS_DIR":"YES","PRECOMP_DESTINATION_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/PrefixHeaders","PRESERVE_DEAD_CODE_INITS_AND_TERMS":"NO","PRIVATE_HEADERS_FOLDER_PATH":"Runner.app/PrivateHeaders","PRODUCT_BUNDLE_IDENTIFIER":"likewallet.likewallet","PRODUCT_BUNDLE_PACKAGE_TYPE":"APPL","PRODUCT_MODULE_NAME":"Runner","PRODUCT_NAME":"Runner","PRODUCT_SETTINGS_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/Info.plist","PRODUCT_TYPE":"com.apple.product-type.application","PROFILING_CODE":"NO","PROJECT":"Runner","PROJECT_DERIVED_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/DerivedSources","PROJECT_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","PROJECT_FILE_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner.xcodeproj","PROJECT_NAME":"Runner","PROJECT_TEMP_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build","PROJECT_TEMP_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","PUBLIC_HEADERS_FOLDER_PATH":"Runner.app/Headers","RECURSIVE_SEARCH_PATHS_FOLLOW_SYMLINKS":"YES","REMOVE_CVS_FROM_RESOURCES":"YES","REMOVE_GIT_FROM_RESOURCES":"YES","REMOVE_HEADERS_FROM_EMBEDDED_BUNDLES":"YES","REMOVE_HG_FROM_RESOURCES":"YES","REMOVE_SVN_FROM_RESOURCES":"YES","REZ_COLLECTOR_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/ResourceManagerResources","REZ_OBJECTS_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/ResourceManagerResources/Objects","REZ_SEARCH_PATHS":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator ","SCAN_ALL_SOURCE_FILES_FOR_INCLUDES":"NO","SCRIPTS_FOLDER_PATH":"Runner.app/Scripts","SCRIPT_INPUT_FILE_0":"/Podfile.lock","SCRIPT_INPUT_FILE_1":"/Manifest.lock","SCRIPT_INPUT_FILE_COUNT":"2","SCRIPT_INPUT_FILE_LIST_COUNT":"0","SCRIPT_OUTPUT_FILE_0":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Pods-Runner-checkManifestLockResult.txt","SCRIPT_OUTPUT_FILE_COUNT":"1","SCRIPT_OUTPUT_FILE_LIST_COUNT":"0","SDKROOT":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator15.0.sdk","SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator15.0.sdk","SDK_DIR_iphonesimulator":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator15.0.sdk","SDK_DIR_iphonesimulator15_0":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator15.0.sdk","SDK_NAME":"iphonesimulator15.0","SDK_NAMES":"iphonesimulator15.0","SDK_PRODUCT_BUILD_VERSION":"19A339","SDK_VERSION":"15.0","SDK_VERSION_ACTUAL":"150000","SDK_VERSION_MAJOR":"150000","SDK_VERSION_MINOR":"150000","SED":"/usr/bin/sed","SEPARATE_STRIP":"NO","SEPARATE_SYMBOL_EDIT":"NO","SET_DIR_MODE_OWNER_GROUP":"YES","SET_FILE_MODE_OWNER_GROUP":"NO","SHALLOW_BUNDLE":"YES","SHARED_DERIVED_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/DerivedSources","SHARED_FRAMEWORKS_FOLDER_PATH":"Runner.app/SharedFrameworks","SHARED_PRECOMPS_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/SharedPrecompiledHeaders","SHARED_SUPPORT_FOLDER_PATH":"Runner.app/SharedSupport","SKIP_INSTALL":"NO","SOURCE_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","SRCROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","STRINGSDATA_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/undefined_arch","STRINGSDATA_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","STRINGS_FILE_INFOPLIST_RENAME":"YES","STRINGS_FILE_OUTPUT_ENCODING":"binary","STRIP_BITCODE_FROM_COPIED_FILES":"NO","STRIP_INSTALLED_PRODUCT":"NO","STRIP_STYLE":"all","STRIP_SWIFT_SYMBOLS":"YES","SUPPORTED_DEVICE_FAMILIES":"1,2","SUPPORTED_PLATFORMS":"iphoneos iphonesimulator","SUPPORTS_TEXT_BASED_API":"NO","SWIFT_EMIT_LOC_STRINGS":"NO","SWIFT_OBJC_BRIDGING_HEADER":"Runner/Runner-Bridging-Header.h","SWIFT_OPTIMIZATION_LEVEL":"-Onone","SWIFT_PLATFORM_TARGET_PREFIX":"ios","SWIFT_RESPONSE_FILE_PATH_normal_x86_64":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList","SWIFT_VERSION":"5.0","SYMROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","SYSTEM_ADMIN_APPS_DIR":"/Applications/Utilities","SYSTEM_APPS_DIR":"/Applications","SYSTEM_CORE_SERVICES_DIR":"/System/Library/CoreServices","SYSTEM_DEMOS_DIR":"/Applications/Extras","SYSTEM_DEVELOPER_APPS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","SYSTEM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","SYSTEM_DEVELOPER_DEMOS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities/Built Examples","SYSTEM_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","SYSTEM_DEVELOPER_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library","SYSTEM_DEVELOPER_GRAPHICS_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Graphics Tools","SYSTEM_DEVELOPER_JAVA_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Java Tools","SYSTEM_DEVELOPER_PERFORMANCE_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Performance Tools","SYSTEM_DEVELOPER_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes","SYSTEM_DEVELOPER_TOOLS":"/Applications/Xcode.app/Contents/Developer/Tools","SYSTEM_DEVELOPER_TOOLS_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/documentation/DeveloperTools","SYSTEM_DEVELOPER_TOOLS_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes/DeveloperTools","SYSTEM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","SYSTEM_DEVELOPER_UTILITIES_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities","SYSTEM_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","SYSTEM_DOCUMENTATION_DIR":"/Library/Documentation","SYSTEM_KEXT_INSTALL_PATH":"/System/Library/Extensions","SYSTEM_LIBRARY_DIR":"/System/Library","TAPI_VERIFY_MODE":"ErrorsOnly","TARGETED_DEVICE_FAMILY":"1,2","TARGETNAME":"Runner","TARGET_BUILD_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator","TARGET_DEVICE_IDENTIFIER":"7E256F87-A048-497E-B1B0-F06B3A14E031","TARGET_DEVICE_MODEL":"iPhone10,4","TARGET_DEVICE_OS_VERSION":"15.0","TARGET_DEVICE_PLATFORM_NAME":"iphonesimulator","TARGET_NAME":"Runner","TARGET_TEMP_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_FILES_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","TEST_FRAMEWORK_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Library/Frameworks /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator15.0.sdk/Developer/Library/Frameworks","TEST_LIBRARY_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/lib","TOOLCHAINS":"com.apple.dt.toolchain.XcodeDefault","TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","TRACK_WIDGET_CREATION":"true","TREAT_MISSING_BASELINES_AS_TEST_FAILURES":"NO","TREE_SHAKE_ICONS":"false","UID":"501","UNLOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app","UNSTRIPPED_PRODUCT":"NO","USER":"dabzshot","USER_APPS_DIR":"/Users/<USER>/Applications","USER_LIBRARY_DIR":"/Users/<USER>/Library","USE_DYNAMIC_NO_PIC":"YES","USE_HEADERMAP":"YES","USE_HEADER_SYMLINKS":"NO","USE_LLVM_TARGET_TRIPLES":"YES","USE_LLVM_TARGET_TRIPLES_FOR_CLANG":"YES","USE_LLVM_TARGET_TRIPLES_FOR_LD":"YES","USE_LLVM_TARGET_TRIPLES_FOR_TAPI":"YES","VALIDATE_DEVELOPMENT_ASSET_PATHS":"YES_ERROR","VALIDATE_PRODUCT":"NO","VALIDATE_WORKSPACE":"YES_ERROR","VALID_ARCHS":"arm64 arm64e i386 x86_64","VERBOSE_PBXCP":"NO","VERSIONING_SYSTEM":"apple-generic","VERSIONPLIST_PATH":"Runner.app/version.plist","VERSION_INFO_BUILDER":"dabzshot","VERSION_INFO_FILE":"Runner_vers.c","VERSION_INFO_STRING":"\"@(#)PROGRAM:Runner  PROJECT:Runner-207\"","WRAPPER_EXTENSION":"app","WRAPPER_NAME":"Runner.app","WRAPPER_SUFFIX":".app","WRAP_ASSET_PACKS_IN_SEPARATE_DIRECTORIES":"NO","XCODE_APP_SUPPORT_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Xcode","XCODE_PRODUCT_BUILD_VERSION":"13A1030d","XCODE_VERSION_ACTUAL":"1310","XCODE_VERSION_MAJOR":"1300","XCODE_VERSION_MINOR":"1310","XPCSERVICES_FOLDER_PATH":"Runner.app/XPCServices","YACC":"yacc","arch":"undefined_arch","variant":"normal"},"allow-missing-inputs":true,"working-directory":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","control-enabled":false,"signature":"72de9ee6d0b2e78be1a44fbdeca2b3f2"}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:PhaseScriptExecution [CP] Embed Pods Frameworks /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3759590B8F78A0B90ADCF815.sh": {"tool":"shell","description":"PhaseScriptExecution [CP] Embed Pods Frameworks /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3759590B8F78A0B90ADCF815.sh","inputs":["/Target Support Files/Pods-Runner/Pods-Runner-frameworks.sh","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/BoringSSL-GRPC/openssl_grpc.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/DTTJailbreakDetection/DTTJailbreakDetection.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/FMDB/FMDB.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/FirebaseAuth/FirebaseAuth.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/FirebaseCore/FirebaseCore.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/FirebaseCoreDiagnostics/FirebaseCoreDiagnostics.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/FirebaseCrashlytics/FirebaseCrashlytics.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/FirebaseFirestore/FirebaseFirestore.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/FirebaseInstallations/FirebaseInstallations.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/FirebaseMessaging/FirebaseMessaging.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/FirebaseStorage/FirebaseStorage.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/GTMSessionFetcher/GTMSessionFetcher.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/GoogleDataTransport/GoogleDataTransport.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/GoogleToolboxForMac/GoogleToolboxForMac.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/MTBBarcodeScanner/MTBBarcodeScanner.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Protobuf/Protobuf.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Reachability/Reachability.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Toast/Toast.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/abseil/absl.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/app_settings/app_settings.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/camera/camera.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/connectivity/connectivity.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/contacts_service/contacts_service.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/flutter_appavailability/flutter_appavailability.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/flutter_idensic_mobile_sdk_plugin/flutter_idensic_mobile_sdk_plugin.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/flutter_jailbreak_detection/flutter_jailbreak_detection.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/flutter_local_notifications/flutter_local_notifications.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/flutter_native_timezone/flutter_native_timezone.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/flutter_secure_storage/flutter_secure_storage.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/fluttertoast/fluttertoast.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/gRPC-C++/grpcpp.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/gRPC-Core/grpc.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/image_crop/image_crop.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/image_gallery_saver/image_gallery_saver.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/image_picker/image_picker.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/keyboard_visibility/keyboard_visibility.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/launch_review/launch_review.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/leveldb-library/leveldb.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/local_auth/local_auth.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/nanopb/nanopb.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/native_device_orientation/native_device_orientation.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/open_appstore/open_appstore.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/package_info/package_info.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/path_provider/path_provider.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/permission/permission.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/qrcode_flutter/qrcode_flutter.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/share/share.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/shared_preferences/shared_preferences.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/simple_image_crop/simple_image_crop.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/sms_autofill/sms_autofill.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/sms_receiver/sms_receiver.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/social_share_plugin/social_share_plugin.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/sqflite/sqflite.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/store_redirect/store_redirect.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/uni_links/uni_links.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/url_launcher/url_launcher.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/video_player/video_player.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/webview_flutter/webview_flutter.framework","/FBAEMKit/FBAEMKit.framework/FBAEMKit","/FBSDKCoreKit/FBSDKCoreKit.framework/FBSDKCoreKit","/FBSDKCoreKit_Basics/FBSDKCoreKit_Basics.framework/FBSDKCoreKit_Basics","/FBSDKShareKit/FBSDKShareKit.framework/FBSDKShareKit","/IdensicMobileSDK/Core/IdensicMobileSDK.framework/IdensicMobileSDK","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3759590B8F78A0B90ADCF815.sh","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase6-thin-binary>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/openssl_grpc.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/DTTJailbreakDetection.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FMDB.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseAuth.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCore.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCoreDiagnostics.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCrashlytics.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseFirestore.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseInstallations.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseMessaging.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseStorage.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GTMSessionFetcher.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleDataTransport.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleToolboxForMac.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleUtilities.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/MTBBarcodeScanner.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBLPromises.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Protobuf.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Reachability.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Toast.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/absl.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/app_settings.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/camera.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/connectivity.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/contacts_service.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_appavailability.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_idensic_mobile_sdk_plugin.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_jailbreak_detection.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_local_notifications.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_native_timezone.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_secure_storage.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/fluttertoast.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/grpcpp.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/grpc.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_crop.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_gallery_saver.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_picker.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/keyboard_visibility.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/launch_review.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/leveldb.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/local_auth.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/nanopb.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/native_device_orientation.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/open_appstore.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/package_info.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/path_provider.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/permission.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/qrcode_flutter.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/share.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/shared_preferences.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/simple_image_crop.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sms_autofill.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sms_receiver.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/social_share_plugin.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sqflite.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/store_redirect.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/uni_links.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/url_launcher.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/video_player.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/webview_flutter.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBAEMKit.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBSDKCoreKit.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBSDKCoreKit_Basics.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBSDKShareKit.framework","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/IdensicMobileSDK.framework"],"args":["/bin/sh","-c","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3759590B8F78A0B90ADCF815.sh"],"env":{"ACTION":"build","AD_HOC_CODE_SIGNING_ALLOWED":"YES","ALLOW_TARGET_PLATFORM_SPECIALIZATION":"NO","ALTERNATE_GROUP":"staff","ALTERNATE_MODE":"u+w,go-w,a+rX","ALTERNATE_OWNER":"dabzshot","ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES":"NO","ALWAYS_SEARCH_USER_PATHS":"NO","ALWAYS_USE_SEPARATE_HEADERMAPS":"NO","APPLE_INTERNAL_DEVELOPER_DIR":"/AppleInternal/Developer","APPLE_INTERNAL_DIR":"/AppleInternal","APPLE_INTERNAL_DOCUMENTATION_DIR":"/AppleInternal/Documentation","APPLE_INTERNAL_LIBRARY_DIR":"/AppleInternal/Library","APPLE_INTERNAL_TOOLS":"/AppleInternal/Developer/Tools","APPLICATION_EXTENSION_API_ONLY":"NO","APPLY_RULES_IN_COPY_FILES":"NO","APPLY_RULES_IN_COPY_HEADERS":"NO","ARCHS":"x86_64","ARCHS_STANDARD":"arm64 x86_64","ARCHS_STANDARD_32_64_BIT":"arm64 i386 x86_64","ARCHS_STANDARD_32_BIT":"i386","ARCHS_STANDARD_64_BIT":"arm64 x86_64","ARCHS_STANDARD_INCLUDING_64_BIT":"arm64 x86_64","ARCHS_UNIVERSAL_IPHONE_OS":"arm64 i386 x86_64","ASSETCATALOG_COMPILER_APPICON_NAME":"AppIcon","ASSETCATALOG_FILTER_FOR_DEVICE_MODEL":"iPhone10,4","ASSETCATALOG_FILTER_FOR_DEVICE_OS_VERSION":"15.0","AVAILABLE_PLATFORMS":"appletvos appletvsimulator driverkit iphoneos iphonesimulator macosx watchos watchsimulator","BITCODE_GENERATION_MODE":"marker","BUILD_ACTIVE_RESOURCES_ONLY":"YES","BUILD_COMPONENTS":"headers build","BUILD_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","BUILD_LIBRARY_FOR_DISTRIBUTION":"NO","BUILD_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","BUILD_STYLE":"","BUILD_VARIANTS":"normal","BUILT_PRODUCTS_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator","BUNDLE_CONTENTS_FOLDER_PATH_deep":"Contents/","BUNDLE_EXECUTABLE_FOLDER_NAME_deep":"MacOS","BUNDLE_FORMAT":"shallow","BUNDLE_FRAMEWORKS_FOLDER_PATH":"Frameworks","BUNDLE_PLUGINS_FOLDER_PATH":"PlugIns","BUNDLE_PRIVATE_HEADERS_FOLDER_PATH":"PrivateHeaders","BUNDLE_PUBLIC_HEADERS_FOLDER_PATH":"Headers","CACHE_ROOT":"/var/folders/81/hsp6b80d6fn5sq0f3xxdpfd00000gn/C/com.apple.DeveloperTools/13.1-13A1030d/Xcode","CCHROOT":"/var/folders/81/hsp6b80d6fn5sq0f3xxdpfd00000gn/C/com.apple.DeveloperTools/13.1-13A1030d/Xcode","CHMOD":"/bin/chmod","CHOWN":"/usr/sbin/chown","CLANG_ANALYZER_NONNULL":"YES","CLANG_CXX_LANGUAGE_STANDARD":"gnu++0x","CLANG_CXX_LIBRARY":"libc++","CLANG_ENABLE_MODULES":"YES","CLANG_ENABLE_OBJC_ARC":"YES","CLANG_MODULES_BUILD_SESSION_FILE":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation","CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING":"YES","CLANG_WARN_BOOL_CONVERSION":"YES","CLANG_WARN_COMMA":"YES","CLANG_WARN_CONSTANT_CONVERSION":"YES","CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS":"YES","CLANG_WARN_DIRECT_OBJC_ISA_USAGE":"YES_ERROR","CLANG_WARN_EMPTY_BODY":"YES","CLANG_WARN_ENUM_CONVERSION":"YES","CLANG_WARN_INFINITE_RECURSION":"YES","CLANG_WARN_INT_CONVERSION":"YES","CLANG_WARN_NON_LITERAL_NULL_CONVERSION":"YES","CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF":"YES","CLANG_WARN_OBJC_LITERAL_CONVERSION":"YES","CLANG_WARN_OBJC_ROOT_CLASS":"YES_ERROR","CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER":"YES","CLANG_WARN_RANGE_LOOP_ANALYSIS":"YES","CLANG_WARN_STRICT_PROTOTYPES":"YES","CLANG_WARN_SUSPICIOUS_MOVE":"YES","CLANG_WARN_UNREACHABLE_CODE":"YES","CLANG_WARN__DUPLICATE_METHOD_MATCH":"YES","CLASS_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/JavaClasses","CLEAN_PRECOMPS":"YES","CLONE_HEADERS":"NO","COCOAPODS_PARALLEL_CODE_SIGN":"true","CODESIGNING_FOLDER_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app","CODE_SIGNING_ALLOWED":"YES","CODE_SIGNING_REQUIRED":"YES","CODE_SIGN_CONTEXT_CLASS":"XCiPhoneSimulatorCodeSignContext","CODE_SIGN_ENTITLEMENTS":"Runner/Runner.entitlements","CODE_SIGN_IDENTITY":"Apple Development","CODE_SIGN_INJECT_BASE_ENTITLEMENTS":"YES","CODE_SIGN_STYLE":"Automatic","COLOR_DIAGNOSTICS":"NO","COMBINE_HIDPI_IMAGES":"NO","COMPILER_INDEX_STORE_ENABLE":"Default","COMPOSITE_SDK_DIRS":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/CompositeSDKs","COMPRESS_PNG_FILES":"YES","CONFIGURATION":"Debug","CONFIGURATION_BUILD_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator","CONFIGURATION_TEMP_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator","CONTENTS_FOLDER_PATH":"Runner.app","COPYING_PRESERVES_HFS_DATA":"NO","COPY_HEADERS_RUN_UNIFDEF":"NO","COPY_PHASE_STRIP":"NO","COPY_RESOURCES_FROM_STATIC_FRAMEWORKS":"YES","CORRESPONDING_DEVICE_PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform","CORRESPONDING_DEVICE_PLATFORM_NAME":"iphoneos","CORRESPONDING_DEVICE_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS15.0.sdk","CORRESPONDING_DEVICE_SDK_NAME":"iphoneos15.0","CP":"/bin/cp","CREATE_INFOPLIST_SECTION_IN_BINARY":"NO","CURRENT_ARCH":"undefined_arch","CURRENT_PROJECT_VERSION":"207","CURRENT_VARIANT":"normal","DART_DEFINES":"Zmx1dHRlci5pbnNwZWN0b3Iuc3RydWN0dXJlZEVycm9ycz10cnVl,RkxVVFRFUl9XRUJfQVVUT19ERVRFQ1Q9dHJ1ZQ==","DART_OBFUSCATION":"false","DEAD_CODE_STRIPPING":"YES","DEBUGGING_SYMBOLS":"YES","DEBUG_INFORMATION_FORMAT":"dwarf","DEFAULT_COMPILER":"com.apple.compilers.llvm.clang.1_0","DEFAULT_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","DEFAULT_KEXT_INSTALL_PATH":"/System/Library/Extensions","DEFINES_MODULE":"NO","DEPLOYMENT_LOCATION":"NO","DEPLOYMENT_POSTPROCESSING":"NO","DEPLOYMENT_TARGET_CLANG_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_CLANG_FLAG_NAME":"mios-simulator-version-min","DEPLOYMENT_TARGET_CLANG_FLAG_PREFIX":"-mios-simulator-version-min=","DEPLOYMENT_TARGET_LD_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_LD_FLAG_NAME":"ios_simulator_version_min","DEPLOYMENT_TARGET_SETTING_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_SUGGESTED_VALUES":"9.0 9.1 9.2 9.3 10.0 10.1 10.2 10.3 11.0 11.1 11.2 11.3 11.4 12.0 12.1 12.2 12.3 12.4 13.0 13.1 13.2 13.3 13.4 13.5 13.6 14.0 14.1 14.2 14.3 14.4 14.5 14.6 14.7 15.0","DERIVED_FILES_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","DERIVED_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","DERIVED_SOURCES_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","DEVELOPER_FRAMEWORKS_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_FRAMEWORKS_DIR_QUOTED":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Library","DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs","DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Tools","DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","DEVELOPMENT_LANGUAGE":"en","DEVELOPMENT_TEAM":"HJAN5D475T","DOCUMENTATION_FOLDER_PATH":"Runner.app/en.lproj/Documentation","DONT_GENERATE_INFOPLIST_FILE":"NO","DO_HEADER_SCANNING_IN_JAM":"NO","DSTROOT":"/tmp/Runner.dst","DT_TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","DWARF_DSYM_FILE_NAME":"Runner.app.dSYM","DWARF_DSYM_FILE_SHOULD_ACCOMPANY_PRODUCT":"NO","DWARF_DSYM_FOLDER_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator","EFFECTIVE_PLATFORM_NAME":"-iphonesimulator","EMBEDDED_CONTENT_CONTAINS_SWIFT":"NO","EMBED_ASSET_PACKS_IN_PRODUCT_BUNDLE":"NO","ENABLE_APP_SANDBOX":"NO","ENABLE_BITCODE":"NO","ENABLE_DEFAULT_HEADER_SEARCH_PATHS":"YES","ENABLE_HARDENED_RUNTIME":"NO","ENABLE_HEADER_DEPENDENCIES":"YES","ENABLE_ON_DEMAND_RESOURCES":"YES","ENABLE_PREVIEWS":"NO","ENABLE_STRICT_OBJC_MSGSEND":"YES","ENABLE_TESTABILITY":"YES","ENABLE_TESTING_SEARCH_PATHS":"NO","ENTITLEMENTS_DESTINATION":"__entitlements","ENTITLEMENTS_REQUIRED":"YES","EXCLUDED_ARCHS":"arm64 i386","EXCLUDED_INSTALLSRC_SUBDIRECTORY_PATTERNS":".DS_Store .svn .git .hg CVS","EXCLUDED_RECURSIVE_SEARCH_PATH_SUBDIRECTORIES":"*.nib *.lproj *.framework *.gch *.xcode* *.xcassets (*) .DS_Store CVS .svn .git .hg *.pbproj *.pbxproj","EXECUTABLES_FOLDER_PATH":"Runner.app/Executables","EXECUTABLE_FOLDER_PATH":"Runner.app","EXECUTABLE_NAME":"Runner","EXECUTABLE_PATH":"Runner.app/Runner","EXTRA_FRONT_END_OPTIONS":"--no-sound-null-safety","FILE_LIST":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects/LinkFileList","FIXED_FILES_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/FixedFiles","FLUTTER_APPLICATION_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3","FLUTTER_BUILD_DIR":"build","FLUTTER_BUILD_NAME":"2.0.36","FLUTTER_BUILD_NUMBER":"191","FLUTTER_ROOT":"/Users/<USER>/Desktop/flutter","FLUTTER_TARGET":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/lib/main.dart","FRAMEWORKS_FOLDER_PATH":"Runner.app/Frameworks","FRAMEWORK_FLAG_PREFIX":"-framework","FRAMEWORK_SEARCH_PATHS":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator  /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Flutter","FRAMEWORK_VERSION":"A","FULL_PRODUCT_NAME":"Runner.app","GCC3_VERSION":"3.3","GCC_C_LANGUAGE_STANDARD":"gnu99","GCC_DYNAMIC_NO_PIC":"NO","GCC_INLINES_ARE_PRIVATE_EXTERN":"YES","GCC_NO_COMMON_BLOCKS":"YES","GCC_OBJC_LEGACY_DISPATCH":"YES","GCC_OPTIMIZATION_LEVEL":"0","GCC_PFE_FILE_C_DIALECTS":"c objective-c c++ objective-c++","GCC_PREPROCESSOR_DEFINITIONS":"DEBUG=1 ","GCC_SYMBOLS_PRIVATE_EXTERN":"NO","GCC_TREAT_WARNINGS_AS_ERRORS":"NO","GCC_VERSION":"com.apple.compilers.llvm.clang.1_0","GCC_VERSION_IDENTIFIER":"com_apple_compilers_llvm_clang_1_0","GCC_WARN_64_TO_32_BIT_CONVERSION":"YES","GCC_WARN_ABOUT_RETURN_TYPE":"YES_ERROR","GCC_WARN_UNDECLARED_SELECTOR":"YES","GCC_WARN_UNINITIALIZED_AUTOS":"YES_AGGRESSIVE","GCC_WARN_UNUSED_FUNCTION":"YES","GCC_WARN_UNUSED_VARIABLE":"YES","GENERATED_MODULEMAP_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/GeneratedModuleMaps-iphonesimulator","GENERATE_INFOPLIST_FILE":"NO","GENERATE_MASTER_OBJECT_FILE":"NO","GENERATE_PKGINFO_FILE":"YES","GENERATE_PROFILING_CODE":"NO","GENERATE_TEXT_BASED_STUBS":"NO","GID":"20","GROUP":"staff","HEADERMAP_INCLUDES_FLAT_ENTRIES_FOR_TARGET_BEING_BUILT":"YES","HEADERMAP_INCLUDES_FRAMEWORK_ENTRIES_FOR_ALL_PRODUCT_TYPES":"YES","HEADERMAP_INCLUDES_NONPUBLIC_NONPRIVATE_HEADERS":"YES","HEADERMAP_INCLUDES_PROJECT_HEADERS":"YES","HEADERMAP_USES_FRAMEWORK_PREFIX_ENTRIES":"YES","HEADERMAP_USES_VFS":"NO","HEADER_SEARCH_PATHS":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/include ","HIDE_BITCODE_SYMBOLS":"YES","HOME":"/Users/<USER>","ICONV":"/usr/bin/iconv","INFOPLIST_EXPAND_BUILD_SETTINGS":"YES","INFOPLIST_FILE":"Runner/Info.plist","INFOPLIST_OUTPUT_FORMAT":"binary","INFOPLIST_PATH":"Runner.app/Info.plist","INFOPLIST_PREPROCESS":"NO","INFOSTRINGS_PATH":"Runner.app/en.lproj/InfoPlist.strings","INLINE_PRIVATE_FRAMEWORKS":"NO","INSTALLHDRS_COPY_PHASE":"NO","INSTALLHDRS_SCRIPT_PHASE":"NO","INSTALL_DIR":"/tmp/Runner.dst/Applications","INSTALL_GROUP":"staff","INSTALL_MODE_FLAG":"u+w,go-w,a+rX","INSTALL_OWNER":"dabzshot","INSTALL_PATH":"/Applications","INSTALL_ROOT":"/tmp/Runner.dst","IPHONEOS_DEPLOYMENT_TARGET":"12.0","JAVAC_DEFAULT_FLAGS":"-J-Xms64m -J-XX:NewSize=4M -J-Dfile.encoding=UTF8","JAVA_APP_STUB":"/System/Library/Frameworks/JavaVM.framework/Resources/MacOS/JavaApplicationStub","JAVA_ARCHIVE_CLASSES":"YES","JAVA_ARCHIVE_TYPE":"JAR","JAVA_COMPILER":"/usr/bin/javac","JAVA_FOLDER_PATH":"Runner.app/Java","JAVA_FRAMEWORK_RESOURCES_DIRS":"Resources","JAVA_JAR_FLAGS":"cv","JAVA_SOURCE_SUBDIR":".","JAVA_USE_DEPENDENCIES":"YES","JAVA_ZIP_FLAGS":"-urg","JIKES_DEFAULT_FLAGS":"+E +OLDCSO","KEEP_PRIVATE_EXTERNS":"NO","LD_DEPENDENCY_INFO_FILE":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/undefined_arch/Runner_dependency_info.dat","LD_GENERATE_MAP_FILE":"NO","LD_MAP_FILE_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-LinkMap-normal-undefined_arch.txt","LD_NO_PIE":"NO","LD_QUOTE_LINKER_ARGUMENTS_FOR_COMPILER_DRIVER":"YES","LD_RUNPATH_SEARCH_PATHS":" @executable_path/Frameworks","LEGACY_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/PlugIns/Xcode3Core.ideplugin/Contents/SharedSupport/Developer","LEX":"lex","LIBRARY_DEXT_INSTALL_PATH":"/Library/DriverExtensions","LIBRARY_FLAG_NOSPACE":"YES","LIBRARY_FLAG_PREFIX":"-l","LIBRARY_KEXT_INSTALL_PATH":"/Library/Extensions","LIBRARY_SEARCH_PATHS":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator  /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Flutter","LINKER_DISPLAYS_MANGLED_NAMES":"NO","LINK_FILE_LIST_normal_x86_64":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList","LINK_WITH_STANDARD_LIBRARIES":"YES","LLVM_TARGET_TRIPLE_OS_VERSION":"ios12.0","LLVM_TARGET_TRIPLE_SUFFIX":"-simulator","LLVM_TARGET_TRIPLE_VENDOR":"apple","LOCALIZATION_EXPORT_SUPPORTED":"YES","LOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app/en.lproj","LOCALIZED_STRING_MACRO_NAMES":"NSLocalizedString CFCopyLocalizedString","LOCALIZED_STRING_SWIFTUI_SUPPORT":"YES","LOCAL_ADMIN_APPS_DIR":"/Applications/Utilities","LOCAL_APPS_DIR":"/Applications","LOCAL_DEVELOPER_DIR":"/Library/Developer","LOCAL_LIBRARY_DIR":"/Library","LOCROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","LOCSYMROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","MACH_O_TYPE":"mh_execute","MAC_OS_X_PRODUCT_BUILD_VERSION":"20G95","MAC_OS_X_VERSION_ACTUAL":"110502","MAC_OS_X_VERSION_MAJOR":"110000","MAC_OS_X_VERSION_MINOR":"110500","MARKETING_VERSION":"2.0.86","METAL_LIBRARY_FILE_BASE":"default","METAL_LIBRARY_OUTPUT_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app","MODULES_FOLDER_PATH":"Runner.app/Modules","MODULE_CACHE_DIR":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex","MTL_ENABLE_DEBUG_INFO":"YES","NATIVE_ARCH":"x86_64","NATIVE_ARCH_32_BIT":"i386","NATIVE_ARCH_64_BIT":"x86_64","NATIVE_ARCH_ACTUAL":"x86_64","NO_COMMON":"YES","OBJC_ABI_VERSION":"2","OBJECT_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects","OBJECT_FILE_DIR_normal":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal","OBJROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","ONLY_ACTIVE_ARCH":"YES","OS":"MACOS","OSAC":"/usr/bin/osacompile","PACKAGE_CONFIG":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/.dart_tool/package_config.json","PACKAGE_TYPE":"com.apple.package-type.wrapper.application","PASCAL_STRINGS":"YES","PATH":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/libexec:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/local/bin:/Applications/Xcode.app/Contents/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/usr/local/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin","PATH_PREFIXES_EXCLUDED_FROM_HEADER_DEPENDENCIES":"/usr/include /usr/local/include /System/Library/Frameworks /System/Library/PrivateFrameworks /Applications/Xcode.app/Contents/Developer/Headers /Applications/Xcode.app/Contents/Developer/SDKs /Applications/Xcode.app/Contents/Developer/Platforms","PBDEVELOPMENTPLIST_PATH":"Runner.app/pbdevelopment.plist","PER_ARCH_OBJECT_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/undefined_arch","PER_VARIANT_OBJECT_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal","PKGINFO_FILE_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/PkgInfo","PKGINFO_PATH":"Runner.app/PkgInfo","PLATFORM_DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Applications","PLATFORM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/bin","PLATFORM_DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Library","PLATFORM_DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs","PLATFORM_DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Tools","PLATFORM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr","PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform","PLATFORM_DISPLAY_NAME":"iOS Simulator","PLATFORM_FAMILY_NAME":"iOS","PLATFORM_NAME":"iphonesimulator","PLATFORM_PREFERRED_ARCH":"x86_64","PLATFORM_PRODUCT_BUILD_VERSION":"19A339","PLIST_FILE_OUTPUT_FORMAT":"binary","PLUGINS_FOLDER_PATH":"Runner.app/PlugIns","PRECOMPS_INCLUDE_HEADERS_FROM_BUILT_PRODUCTS_DIR":"YES","PRECOMP_DESTINATION_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/PrefixHeaders","PRESERVE_DEAD_CODE_INITS_AND_TERMS":"NO","PRIVATE_HEADERS_FOLDER_PATH":"Runner.app/PrivateHeaders","PRODUCT_BUNDLE_IDENTIFIER":"likewallet.likewallet","PRODUCT_BUNDLE_PACKAGE_TYPE":"APPL","PRODUCT_MODULE_NAME":"Runner","PRODUCT_NAME":"Runner","PRODUCT_SETTINGS_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/Info.plist","PRODUCT_TYPE":"com.apple.product-type.application","PROFILING_CODE":"NO","PROJECT":"Runner","PROJECT_DERIVED_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/DerivedSources","PROJECT_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","PROJECT_FILE_PATH":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner.xcodeproj","PROJECT_NAME":"Runner","PROJECT_TEMP_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build","PROJECT_TEMP_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","PUBLIC_HEADERS_FOLDER_PATH":"Runner.app/Headers","RECURSIVE_SEARCH_PATHS_FOLLOW_SYMLINKS":"YES","REMOVE_CVS_FROM_RESOURCES":"YES","REMOVE_GIT_FROM_RESOURCES":"YES","REMOVE_HEADERS_FROM_EMBEDDED_BUNDLES":"YES","REMOVE_HG_FROM_RESOURCES":"YES","REMOVE_SVN_FROM_RESOURCES":"YES","REZ_COLLECTOR_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/ResourceManagerResources","REZ_OBJECTS_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/ResourceManagerResources/Objects","REZ_SEARCH_PATHS":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator ","SCAN_ALL_SOURCE_FILES_FOR_INCLUDES":"NO","SCRIPTS_FOLDER_PATH":"Runner.app/Scripts","SCRIPT_INPUT_FILE_0":"/Target Support Files/Pods-Runner/Pods-Runner-frameworks.sh","SCRIPT_INPUT_FILE_1":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/BoringSSL-GRPC/openssl_grpc.framework","SCRIPT_INPUT_FILE_10":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/FirebaseMessaging/FirebaseMessaging.framework","SCRIPT_INPUT_FILE_11":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/FirebaseStorage/FirebaseStorage.framework","SCRIPT_INPUT_FILE_12":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/GTMSessionFetcher/GTMSessionFetcher.framework","SCRIPT_INPUT_FILE_13":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/GoogleDataTransport/GoogleDataTransport.framework","SCRIPT_INPUT_FILE_14":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/GoogleToolboxForMac/GoogleToolboxForMac.framework","SCRIPT_INPUT_FILE_15":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework","SCRIPT_INPUT_FILE_16":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/MTBBarcodeScanner/MTBBarcodeScanner.framework","SCRIPT_INPUT_FILE_17":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises.framework","SCRIPT_INPUT_FILE_18":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Protobuf/Protobuf.framework","SCRIPT_INPUT_FILE_19":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Reachability/Reachability.framework","SCRIPT_INPUT_FILE_2":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/DTTJailbreakDetection/DTTJailbreakDetection.framework","SCRIPT_INPUT_FILE_20":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Toast/Toast.framework","SCRIPT_INPUT_FILE_21":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/abseil/absl.framework","SCRIPT_INPUT_FILE_22":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/app_settings/app_settings.framework","SCRIPT_INPUT_FILE_23":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/camera/camera.framework","SCRIPT_INPUT_FILE_24":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/connectivity/connectivity.framework","SCRIPT_INPUT_FILE_25":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/contacts_service/contacts_service.framework","SCRIPT_INPUT_FILE_26":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/flutter_appavailability/flutter_appavailability.framework","SCRIPT_INPUT_FILE_27":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/flutter_idensic_mobile_sdk_plugin/flutter_idensic_mobile_sdk_plugin.framework","SCRIPT_INPUT_FILE_28":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/flutter_jailbreak_detection/flutter_jailbreak_detection.framework","SCRIPT_INPUT_FILE_29":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/flutter_local_notifications/flutter_local_notifications.framework","SCRIPT_INPUT_FILE_3":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/FMDB/FMDB.framework","SCRIPT_INPUT_FILE_30":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/flutter_native_timezone/flutter_native_timezone.framework","SCRIPT_INPUT_FILE_31":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/flutter_secure_storage/flutter_secure_storage.framework","SCRIPT_INPUT_FILE_32":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/fluttertoast/fluttertoast.framework","SCRIPT_INPUT_FILE_33":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/gRPC-C++/grpcpp.framework","SCRIPT_INPUT_FILE_34":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/gRPC-Core/grpc.framework","SCRIPT_INPUT_FILE_35":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/image_crop/image_crop.framework","SCRIPT_INPUT_FILE_36":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/image_gallery_saver/image_gallery_saver.framework","SCRIPT_INPUT_FILE_37":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/image_picker/image_picker.framework","SCRIPT_INPUT_FILE_38":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/keyboard_visibility/keyboard_visibility.framework","SCRIPT_INPUT_FILE_39":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/launch_review/launch_review.framework","SCRIPT_INPUT_FILE_4":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/FirebaseAuth/FirebaseAuth.framework","SCRIPT_INPUT_FILE_40":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/leveldb-library/leveldb.framework","SCRIPT_INPUT_FILE_41":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/local_auth/local_auth.framework","SCRIPT_INPUT_FILE_42":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/nanopb/nanopb.framework","SCRIPT_INPUT_FILE_43":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/native_device_orientation/native_device_orientation.framework","SCRIPT_INPUT_FILE_44":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/open_appstore/open_appstore.framework","SCRIPT_INPUT_FILE_45":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/package_info/package_info.framework","SCRIPT_INPUT_FILE_46":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/path_provider/path_provider.framework","SCRIPT_INPUT_FILE_47":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/permission/permission.framework","SCRIPT_INPUT_FILE_48":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/qrcode_flutter/qrcode_flutter.framework","SCRIPT_INPUT_FILE_49":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/share/share.framework","SCRIPT_INPUT_FILE_5":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/FirebaseCore/FirebaseCore.framework","SCRIPT_INPUT_FILE_50":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/shared_preferences/shared_preferences.framework","SCRIPT_INPUT_FILE_51":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/simple_image_crop/simple_image_crop.framework","SCRIPT_INPUT_FILE_52":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/sms_autofill/sms_autofill.framework","SCRIPT_INPUT_FILE_53":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/sms_receiver/sms_receiver.framework","SCRIPT_INPUT_FILE_54":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/social_share_plugin/social_share_plugin.framework","SCRIPT_INPUT_FILE_55":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/sqflite/sqflite.framework","SCRIPT_INPUT_FILE_56":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/store_redirect/store_redirect.framework","SCRIPT_INPUT_FILE_57":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/uni_links/uni_links.framework","SCRIPT_INPUT_FILE_58":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/url_launcher/url_launcher.framework","SCRIPT_INPUT_FILE_59":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/video_player/video_player.framework","SCRIPT_INPUT_FILE_6":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/FirebaseCoreDiagnostics/FirebaseCoreDiagnostics.framework","SCRIPT_INPUT_FILE_60":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/webview_flutter/webview_flutter.framework","SCRIPT_INPUT_FILE_61":"/FBAEMKit/FBAEMKit.framework/FBAEMKit","SCRIPT_INPUT_FILE_62":"/FBSDKCoreKit/FBSDKCoreKit.framework/FBSDKCoreKit","SCRIPT_INPUT_FILE_63":"/FBSDKCoreKit_Basics/FBSDKCoreKit_Basics.framework/FBSDKCoreKit_Basics","SCRIPT_INPUT_FILE_64":"/FBSDKShareKit/FBSDKShareKit.framework/FBSDKShareKit","SCRIPT_INPUT_FILE_65":"/IdensicMobileSDK/Core/IdensicMobileSDK.framework/IdensicMobileSDK","SCRIPT_INPUT_FILE_7":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/FirebaseCrashlytics/FirebaseCrashlytics.framework","SCRIPT_INPUT_FILE_8":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/FirebaseFirestore/FirebaseFirestore.framework","SCRIPT_INPUT_FILE_9":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/FirebaseInstallations/FirebaseInstallations.framework","SCRIPT_INPUT_FILE_COUNT":"66","SCRIPT_INPUT_FILE_LIST_COUNT":"0","SCRIPT_OUTPUT_FILE_0":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/openssl_grpc.framework","SCRIPT_OUTPUT_FILE_1":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/DTTJailbreakDetection.framework","SCRIPT_OUTPUT_FILE_10":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseStorage.framework","SCRIPT_OUTPUT_FILE_11":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GTMSessionFetcher.framework","SCRIPT_OUTPUT_FILE_12":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleDataTransport.framework","SCRIPT_OUTPUT_FILE_13":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleToolboxForMac.framework","SCRIPT_OUTPUT_FILE_14":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleUtilities.framework","SCRIPT_OUTPUT_FILE_15":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/MTBBarcodeScanner.framework","SCRIPT_OUTPUT_FILE_16":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBLPromises.framework","SCRIPT_OUTPUT_FILE_17":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Protobuf.framework","SCRIPT_OUTPUT_FILE_18":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Reachability.framework","SCRIPT_OUTPUT_FILE_19":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Toast.framework","SCRIPT_OUTPUT_FILE_2":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FMDB.framework","SCRIPT_OUTPUT_FILE_20":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/absl.framework","SCRIPT_OUTPUT_FILE_21":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/app_settings.framework","SCRIPT_OUTPUT_FILE_22":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/camera.framework","SCRIPT_OUTPUT_FILE_23":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/connectivity.framework","SCRIPT_OUTPUT_FILE_24":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/contacts_service.framework","SCRIPT_OUTPUT_FILE_25":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_appavailability.framework","SCRIPT_OUTPUT_FILE_26":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_idensic_mobile_sdk_plugin.framework","SCRIPT_OUTPUT_FILE_27":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_jailbreak_detection.framework","SCRIPT_OUTPUT_FILE_28":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_local_notifications.framework","SCRIPT_OUTPUT_FILE_29":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_native_timezone.framework","SCRIPT_OUTPUT_FILE_3":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseAuth.framework","SCRIPT_OUTPUT_FILE_30":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_secure_storage.framework","SCRIPT_OUTPUT_FILE_31":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/fluttertoast.framework","SCRIPT_OUTPUT_FILE_32":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/grpcpp.framework","SCRIPT_OUTPUT_FILE_33":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/grpc.framework","SCRIPT_OUTPUT_FILE_34":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_crop.framework","SCRIPT_OUTPUT_FILE_35":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_gallery_saver.framework","SCRIPT_OUTPUT_FILE_36":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_picker.framework","SCRIPT_OUTPUT_FILE_37":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/keyboard_visibility.framework","SCRIPT_OUTPUT_FILE_38":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/launch_review.framework","SCRIPT_OUTPUT_FILE_39":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/leveldb.framework","SCRIPT_OUTPUT_FILE_4":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCore.framework","SCRIPT_OUTPUT_FILE_40":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/local_auth.framework","SCRIPT_OUTPUT_FILE_41":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/nanopb.framework","SCRIPT_OUTPUT_FILE_42":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/native_device_orientation.framework","SCRIPT_OUTPUT_FILE_43":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/open_appstore.framework","SCRIPT_OUTPUT_FILE_44":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/package_info.framework","SCRIPT_OUTPUT_FILE_45":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/path_provider.framework","SCRIPT_OUTPUT_FILE_46":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/permission.framework","SCRIPT_OUTPUT_FILE_47":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/qrcode_flutter.framework","SCRIPT_OUTPUT_FILE_48":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/share.framework","SCRIPT_OUTPUT_FILE_49":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/shared_preferences.framework","SCRIPT_OUTPUT_FILE_5":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCoreDiagnostics.framework","SCRIPT_OUTPUT_FILE_50":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/simple_image_crop.framework","SCRIPT_OUTPUT_FILE_51":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sms_autofill.framework","SCRIPT_OUTPUT_FILE_52":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sms_receiver.framework","SCRIPT_OUTPUT_FILE_53":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/social_share_plugin.framework","SCRIPT_OUTPUT_FILE_54":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sqflite.framework","SCRIPT_OUTPUT_FILE_55":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/store_redirect.framework","SCRIPT_OUTPUT_FILE_56":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/uni_links.framework","SCRIPT_OUTPUT_FILE_57":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/url_launcher.framework","SCRIPT_OUTPUT_FILE_58":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/video_player.framework","SCRIPT_OUTPUT_FILE_59":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/webview_flutter.framework","SCRIPT_OUTPUT_FILE_6":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCrashlytics.framework","SCRIPT_OUTPUT_FILE_60":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBAEMKit.framework","SCRIPT_OUTPUT_FILE_61":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBSDKCoreKit.framework","SCRIPT_OUTPUT_FILE_62":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBSDKCoreKit_Basics.framework","SCRIPT_OUTPUT_FILE_63":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBSDKShareKit.framework","SCRIPT_OUTPUT_FILE_64":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/IdensicMobileSDK.framework","SCRIPT_OUTPUT_FILE_7":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseFirestore.framework","SCRIPT_OUTPUT_FILE_8":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseInstallations.framework","SCRIPT_OUTPUT_FILE_9":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseMessaging.framework","SCRIPT_OUTPUT_FILE_COUNT":"65","SCRIPT_OUTPUT_FILE_LIST_COUNT":"0","SDKROOT":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator15.0.sdk","SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator15.0.sdk","SDK_DIR_iphonesimulator":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator15.0.sdk","SDK_DIR_iphonesimulator15_0":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator15.0.sdk","SDK_NAME":"iphonesimulator15.0","SDK_NAMES":"iphonesimulator15.0","SDK_PRODUCT_BUILD_VERSION":"19A339","SDK_VERSION":"15.0","SDK_VERSION_ACTUAL":"150000","SDK_VERSION_MAJOR":"150000","SDK_VERSION_MINOR":"150000","SED":"/usr/bin/sed","SEPARATE_STRIP":"NO","SEPARATE_SYMBOL_EDIT":"NO","SET_DIR_MODE_OWNER_GROUP":"YES","SET_FILE_MODE_OWNER_GROUP":"NO","SHALLOW_BUNDLE":"YES","SHARED_DERIVED_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/DerivedSources","SHARED_FRAMEWORKS_FOLDER_PATH":"Runner.app/SharedFrameworks","SHARED_PRECOMPS_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/SharedPrecompiledHeaders","SHARED_SUPPORT_FOLDER_PATH":"Runner.app/SharedSupport","SKIP_INSTALL":"NO","SOURCE_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","SRCROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","STRINGSDATA_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/undefined_arch","STRINGSDATA_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","STRINGS_FILE_INFOPLIST_RENAME":"YES","STRINGS_FILE_OUTPUT_ENCODING":"binary","STRIP_BITCODE_FROM_COPIED_FILES":"NO","STRIP_INSTALLED_PRODUCT":"NO","STRIP_STYLE":"all","STRIP_SWIFT_SYMBOLS":"YES","SUPPORTED_DEVICE_FAMILIES":"1,2","SUPPORTED_PLATFORMS":"iphoneos iphonesimulator","SUPPORTS_TEXT_BASED_API":"NO","SWIFT_EMIT_LOC_STRINGS":"NO","SWIFT_OBJC_BRIDGING_HEADER":"Runner/Runner-Bridging-Header.h","SWIFT_OPTIMIZATION_LEVEL":"-Onone","SWIFT_PLATFORM_TARGET_PREFIX":"ios","SWIFT_RESPONSE_FILE_PATH_normal_x86_64":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList","SWIFT_VERSION":"5.0","SYMROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","SYSTEM_ADMIN_APPS_DIR":"/Applications/Utilities","SYSTEM_APPS_DIR":"/Applications","SYSTEM_CORE_SERVICES_DIR":"/System/Library/CoreServices","SYSTEM_DEMOS_DIR":"/Applications/Extras","SYSTEM_DEVELOPER_APPS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","SYSTEM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","SYSTEM_DEVELOPER_DEMOS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities/Built Examples","SYSTEM_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","SYSTEM_DEVELOPER_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library","SYSTEM_DEVELOPER_GRAPHICS_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Graphics Tools","SYSTEM_DEVELOPER_JAVA_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Java Tools","SYSTEM_DEVELOPER_PERFORMANCE_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Performance Tools","SYSTEM_DEVELOPER_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes","SYSTEM_DEVELOPER_TOOLS":"/Applications/Xcode.app/Contents/Developer/Tools","SYSTEM_DEVELOPER_TOOLS_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/documentation/DeveloperTools","SYSTEM_DEVELOPER_TOOLS_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes/DeveloperTools","SYSTEM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","SYSTEM_DEVELOPER_UTILITIES_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities","SYSTEM_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","SYSTEM_DOCUMENTATION_DIR":"/Library/Documentation","SYSTEM_KEXT_INSTALL_PATH":"/System/Library/Extensions","SYSTEM_LIBRARY_DIR":"/System/Library","TAPI_VERIFY_MODE":"ErrorsOnly","TARGETED_DEVICE_FAMILY":"1,2","TARGETNAME":"Runner","TARGET_BUILD_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator","TARGET_DEVICE_IDENTIFIER":"7E256F87-A048-497E-B1B0-F06B3A14E031","TARGET_DEVICE_MODEL":"iPhone10,4","TARGET_DEVICE_OS_VERSION":"15.0","TARGET_DEVICE_PLATFORM_NAME":"iphonesimulator","TARGET_NAME":"Runner","TARGET_TEMP_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_FILES_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_FILE_DIR":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_ROOT":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios","TEST_FRAMEWORK_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Library/Frameworks /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator15.0.sdk/Developer/Library/Frameworks","TEST_LIBRARY_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/lib","TOOLCHAINS":"com.apple.dt.toolchain.XcodeDefault","TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","TRACK_WIDGET_CREATION":"true","TREAT_MISSING_BASELINES_AS_TEST_FAILURES":"NO","TREE_SHAKE_ICONS":"false","UID":"501","UNLOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app","UNSTRIPPED_PRODUCT":"NO","USER":"dabzshot","USER_APPS_DIR":"/Users/<USER>/Applications","USER_LIBRARY_DIR":"/Users/<USER>/Library","USE_DYNAMIC_NO_PIC":"YES","USE_HEADERMAP":"YES","USE_HEADER_SYMLINKS":"NO","USE_LLVM_TARGET_TRIPLES":"YES","USE_LLVM_TARGET_TRIPLES_FOR_CLANG":"YES","USE_LLVM_TARGET_TRIPLES_FOR_LD":"YES","USE_LLVM_TARGET_TRIPLES_FOR_TAPI":"YES","VALIDATE_DEVELOPMENT_ASSET_PATHS":"YES_ERROR","VALIDATE_PRODUCT":"NO","VALIDATE_WORKSPACE":"YES_ERROR","VALID_ARCHS":"arm64 arm64e i386 x86_64","VERBOSE_PBXCP":"NO","VERSIONING_SYSTEM":"apple-generic","VERSIONPLIST_PATH":"Runner.app/version.plist","VERSION_INFO_BUILDER":"dabzshot","VERSION_INFO_FILE":"Runner_vers.c","VERSION_INFO_STRING":"\"@(#)PROGRAM:Runner  PROJECT:Runner-207\"","WRAPPER_EXTENSION":"app","WRAPPER_NAME":"Runner.app","WRAPPER_SUFFIX":".app","WRAP_ASSET_PACKS_IN_SEPARATE_DIRECTORIES":"NO","XCODE_APP_SUPPORT_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Xcode","XCODE_PRODUCT_BUILD_VERSION":"13A1030d","XCODE_VERSION_ACTUAL":"1310","XCODE_VERSION_MAJOR":"1300","XCODE_VERSION_MINOR":"1310","XPCSERVICES_FOLDER_PATH":"Runner.app/XPCServices","YACC":"yacc","arch":"undefined_arch","variant":"normal"},"allow-missing-inputs":true,"working-directory":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","control-enabled":false,"signature":"1557726ee46c0a4b0582663672ae9417"}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:ProcessInfoPlistFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Info.plist /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/Info.plist": {"tool":"info-plist-processor","description":"ProcessInfoPlistFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Info.plist /Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/Info.plist","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner/Info.plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen-SBPartialInfo.plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main-SBPartialInfo.plist","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/assetcatalog_generated_info.plist","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app/Info.plist"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:RegisterExecutionPolicyException /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app": {"tool":"register-execution-policy-exception","description":"RegisterExecutionPolicyException /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-CodeSign>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--will-sign>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["<RegisterExecutionPolicyException /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app>"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:Touch /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app": {"tool":"shell","description":"Touch /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app","inputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--Barrier-Validate>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--will-sign>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["<Touch /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app>"],"args":["/usr/bin/touch","-c","/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Debug-iphonesimulator/Runner.app"],"env":{},"working-directory":"/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios","signature":"98133f1e5ff5781b5931be5854cef447"}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-non-framework-target-headers.hmap": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-non-framework-target-headers.hmap","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-non-framework-target-headers.hmap"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-target-headers.hmap": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-target-headers.hmap","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-target-headers.hmap"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-generated-files.hmap": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-generated-files.hmap","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-generated-files.hmap"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-own-target-headers.hmap": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-own-target-headers.hmap","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-own-target-headers.hmap"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-project-headers.hmap": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-project-headers.hmap","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-project-headers.hmap"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.hmap": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.hmap","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.hmap"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-328736DAE7F587435A6E60E5.sh": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-328736DAE7F587435A6E60E5.sh","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-328736DAE7F587435A6E60E5.sh"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3759590B8F78A0B90ADCF815.sh": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3759590B8F78A0B90ADCF815.sh","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase6-thin-binary>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3759590B8F78A0B90ADCF815.sh"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase5-copy-files>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--phase0--cp--check-pods-manifest-lock>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh"]}
  "target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/all-product-headers.yaml": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/all-product-headers.yaml","inputs":["<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/Documents/GitHub/LikeWalletPharse3/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/all-product-headers.yaml"]}

