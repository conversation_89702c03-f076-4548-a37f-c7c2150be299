{"_buildCommand2": {"command": "prepareForIndexing", "enableIndexBuildArena": false, "targets": null}, "buildCommand": "build", "configuredTargets": [{"guid": "18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49"}], "containerPath": "/Users/<USER>/Documents/GitHub/LikeWalletPharse3/ios/Runner.xcodeproj", "continueBuildingAfterErrors": true, "enableIndexBuildArena": false, "hideShellScriptEnvironment": false, "parameters": {"action": "build", "activeArchitecture": "arm64e", "activeRunDestination": {"disableOnlyActiveArch": true, "platform": "iphoneos", "sdk": "iphoneos15.0", "sdkVariant": "iphoneos", "supportedArchitectures": ["armv4t", "armv5", "armv6", "armv7", "armv7f", "armv7s", "armv7k", "arm64", "arm64e"], "targetArchitecture": "arm64e"}, "arenaInfo": {"buildIntermediatesPath": "", "buildProductsPath": "", "derivedDataPath": "/Users/<USER>/Library/Developer/Xcode/DerivedData", "indexDataStoreFolderPath": "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-hbmtdpxidruszpaaeuveldpjuspm/Index/DataStore", "indexEnableDataStore": true, "indexPCHPath": "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-hbmtdpxidruszpaaeuveldpjuspm/Index/PrecompiledHeaders", "pchPath": ""}, "configurationName": "Debug", "overrides": {"synthesized": {"table": {"ENABLE_PREVIEWS": "NO"}}}}, "schemeCommand": "launch", "schemeCommand2": "launch", "shouldCollectMetrics": false, "showNonLoggedProgress": true, "useDryRun": false, "useImplicitDependencies": true, "useLegacyBuildLocations": false, "useParallelTargets": true}