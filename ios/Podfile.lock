PODS:
  - abseil/algorithm (1.20211102.0):
    - abseil/algorithm/algorithm (= 1.20211102.0)
    - abseil/algorithm/container (= 1.20211102.0)
  - abseil/algorithm/algorithm (1.20211102.0):
    - abseil/base/config
  - abseil/algorithm/container (1.20211102.0):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/meta/type_traits
  - abseil/base (1.20211102.0):
    - abseil/base/atomic_hook (= 1.20211102.0)
    - abseil/base/base (= 1.20211102.0)
    - abseil/base/base_internal (= 1.20211102.0)
    - abseil/base/config (= 1.20211102.0)
    - abseil/base/core_headers (= 1.20211102.0)
    - abseil/base/dynamic_annotations (= 1.20211102.0)
    - abseil/base/endian (= 1.20211102.0)
    - abseil/base/errno_saver (= 1.20211102.0)
    - abseil/base/fast_type_id (= 1.20211102.0)
    - abseil/base/log_severity (= 1.20211102.0)
    - abseil/base/malloc_internal (= 1.20211102.0)
    - abseil/base/pretty_function (= 1.20211102.0)
    - abseil/base/raw_logging_internal (= 1.20211102.0)
    - abseil/base/spinlock_wait (= 1.20211102.0)
    - abseil/base/strerror (= 1.20211102.0)
    - abseil/base/throw_delegate (= 1.20211102.0)
  - abseil/base/atomic_hook (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/base/base (1.20211102.0):
    - abseil/base/atomic_hook
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/log_severity
    - abseil/base/raw_logging_internal
    - abseil/base/spinlock_wait
    - abseil/meta/type_traits
  - abseil/base/base_internal (1.20211102.0):
    - abseil/base/config
    - abseil/meta/type_traits
  - abseil/base/config (1.20211102.0)
  - abseil/base/core_headers (1.20211102.0):
    - abseil/base/config
  - abseil/base/dynamic_annotations (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/base/endian (1.20211102.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/base/errno_saver (1.20211102.0):
    - abseil/base/config
  - abseil/base/fast_type_id (1.20211102.0):
    - abseil/base/config
  - abseil/base/log_severity (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/base/malloc_internal (1.20211102.0):
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
  - abseil/base/pretty_function (1.20211102.0)
  - abseil/base/raw_logging_internal (1.20211102.0):
    - abseil/base/atomic_hook
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
  - abseil/base/spinlock_wait (1.20211102.0):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/base/errno_saver
  - abseil/base/strerror (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/errno_saver
  - abseil/base/throw_delegate (1.20211102.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
  - abseil/container/common (1.20211102.0):
    - abseil/meta/type_traits
    - abseil/types/optional
  - abseil/container/compressed_tuple (1.20211102.0):
    - abseil/utility/utility
  - abseil/container/container_memory (1.20211102.0):
    - abseil/base/config
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/utility/utility
  - abseil/container/fixed_array (1.20211102.0):
    - abseil/algorithm/algorithm
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/throw_delegate
    - abseil/container/compressed_tuple
    - abseil/memory/memory
  - abseil/container/flat_hash_map (1.20211102.0):
    - abseil/algorithm/container
    - abseil/container/container_memory
    - abseil/container/hash_function_defaults
    - abseil/container/raw_hash_map
    - abseil/memory/memory
  - abseil/container/hash_function_defaults (1.20211102.0):
    - abseil/base/config
    - abseil/hash/hash
    - abseil/strings/cord
    - abseil/strings/strings
  - abseil/container/hash_policy_traits (1.20211102.0):
    - abseil/meta/type_traits
  - abseil/container/hashtable_debug_hooks (1.20211102.0):
    - abseil/base/config
  - abseil/container/hashtablez_sampler (1.20211102.0):
    - abseil/base/base
    - abseil/base/core_headers
    - abseil/container/have_sse
    - abseil/debugging/stacktrace
    - abseil/memory/memory
    - abseil/profiling/exponential_biased
    - abseil/profiling/sample_recorder
    - abseil/synchronization/synchronization
    - abseil/utility/utility
  - abseil/container/have_sse (1.20211102.0)
  - abseil/container/inlined_vector (1.20211102.0):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/throw_delegate
    - abseil/container/inlined_vector_internal
    - abseil/memory/memory
  - abseil/container/inlined_vector_internal (1.20211102.0):
    - abseil/base/core_headers
    - abseil/container/compressed_tuple
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/types/span
  - abseil/container/layout (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/types/span
    - abseil/utility/utility
  - abseil/container/raw_hash_map (1.20211102.0):
    - abseil/base/throw_delegate
    - abseil/container/container_memory
    - abseil/container/raw_hash_set
  - abseil/container/raw_hash_set (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/container/common
    - abseil/container/compressed_tuple
    - abseil/container/container_memory
    - abseil/container/hash_policy_traits
    - abseil/container/hashtable_debug_hooks
    - abseil/container/hashtablez_sampler
    - abseil/container/have_sse
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/utility/utility
  - abseil/debugging/debugging_internal (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/errno_saver
    - abseil/base/raw_logging_internal
  - abseil/debugging/demangle_internal (1.20211102.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/debugging/stacktrace (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/debugging/debugging_internal
  - abseil/debugging/symbolize (1.20211102.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/debugging/debugging_internal
    - abseil/debugging/demangle_internal
    - abseil/strings/strings
  - abseil/functional/bind_front (1.20211102.0):
    - abseil/base/base_internal
    - abseil/container/compressed_tuple
    - abseil/meta/type_traits
    - abseil/utility/utility
  - abseil/functional/function_ref (1.20211102.0):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/meta/type_traits
  - abseil/hash/city (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
  - abseil/hash/hash (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/container/fixed_array
    - abseil/hash/city
    - abseil/hash/low_level_hash
    - abseil/meta/type_traits
    - abseil/numeric/int128
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/variant
    - abseil/utility/utility
  - abseil/hash/low_level_hash (1.20211102.0):
    - abseil/base/config
    - abseil/base/endian
    - abseil/numeric/bits
    - abseil/numeric/int128
  - abseil/memory (1.20211102.0):
    - abseil/memory/memory (= 1.20211102.0)
  - abseil/memory/memory (1.20211102.0):
    - abseil/base/core_headers
    - abseil/meta/type_traits
  - abseil/meta (1.20211102.0):
    - abseil/meta/type_traits (= 1.20211102.0)
  - abseil/meta/type_traits (1.20211102.0):
    - abseil/base/config
  - abseil/numeric/bits (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/numeric/int128 (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/numeric/bits
  - abseil/numeric/representation (1.20211102.0):
    - abseil/base/config
  - abseil/profiling/exponential_biased (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/profiling/sample_recorder (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/synchronization/synchronization
    - abseil/time/time
  - abseil/random/distributions (1.20211102.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/random/internal/distribution_caller
    - abseil/random/internal/fast_uniform_bits
    - abseil/random/internal/fastmath
    - abseil/random/internal/generate_real
    - abseil/random/internal/iostream_state_saver
    - abseil/random/internal/traits
    - abseil/random/internal/uniform_helper
    - abseil/random/internal/wide_multiply
    - abseil/strings/strings
  - abseil/random/internal/distribution_caller (1.20211102.0):
    - abseil/base/config
    - abseil/base/fast_type_id
    - abseil/utility/utility
  - abseil/random/internal/fast_uniform_bits (1.20211102.0):
    - abseil/base/config
    - abseil/meta/type_traits
  - abseil/random/internal/fastmath (1.20211102.0):
    - abseil/numeric/bits
  - abseil/random/internal/generate_real (1.20211102.0):
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/random/internal/fastmath
    - abseil/random/internal/traits
  - abseil/random/internal/iostream_state_saver (1.20211102.0):
    - abseil/meta/type_traits
    - abseil/numeric/int128
  - abseil/random/internal/nonsecure_base (1.20211102.0):
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/salted_seed_seq
    - abseil/random/internal/seed_material
    - abseil/types/optional
    - abseil/types/span
  - abseil/random/internal/pcg_engine (1.20211102.0):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/random/internal/fastmath
    - abseil/random/internal/iostream_state_saver
  - abseil/random/internal/platform (1.20211102.0):
    - abseil/base/config
  - abseil/random/internal/pool_urbg (1.20211102.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/random/internal/randen
    - abseil/random/internal/seed_material
    - abseil/random/internal/traits
    - abseil/random/seed_gen_exception
    - abseil/types/span
  - abseil/random/internal/randen (1.20211102.0):
    - abseil/base/raw_logging_internal
    - abseil/random/internal/platform
    - abseil/random/internal/randen_hwaes
    - abseil/random/internal/randen_slow
  - abseil/random/internal/randen_engine (1.20211102.0):
    - abseil/base/endian
    - abseil/meta/type_traits
    - abseil/random/internal/iostream_state_saver
    - abseil/random/internal/randen
  - abseil/random/internal/randen_hwaes (1.20211102.0):
    - abseil/base/config
    - abseil/random/internal/platform
    - abseil/random/internal/randen_hwaes_impl
  - abseil/random/internal/randen_hwaes_impl (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/numeric/int128
    - abseil/random/internal/platform
  - abseil/random/internal/randen_slow (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/numeric/int128
    - abseil/random/internal/platform
  - abseil/random/internal/salted_seed_seq (1.20211102.0):
    - abseil/container/inlined_vector
    - abseil/meta/type_traits
    - abseil/random/internal/seed_material
    - abseil/types/optional
    - abseil/types/span
  - abseil/random/internal/seed_material (1.20211102.0):
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
    - abseil/random/internal/fast_uniform_bits
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
  - abseil/random/internal/traits (1.20211102.0):
    - abseil/base/config
  - abseil/random/internal/uniform_helper (1.20211102.0):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/random/internal/traits
  - abseil/random/internal/wide_multiply (1.20211102.0):
    - abseil/base/config
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/random/internal/traits
  - abseil/random/random (1.20211102.0):
    - abseil/random/distributions
    - abseil/random/internal/nonsecure_base
    - abseil/random/internal/pcg_engine
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/randen_engine
    - abseil/random/seed_sequences
  - abseil/random/seed_gen_exception (1.20211102.0):
    - abseil/base/config
  - abseil/random/seed_sequences (1.20211102.0):
    - abseil/container/inlined_vector
    - abseil/random/internal/nonsecure_base
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/salted_seed_seq
    - abseil/random/internal/seed_material
    - abseil/random/seed_gen_exception
    - abseil/types/span
  - abseil/status/status (1.20211102.0):
    - abseil/base/atomic_hook
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/container/inlined_vector
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/functional/function_ref
    - abseil/strings/cord
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/optional
  - abseil/status/statusor (1.20211102.0):
    - abseil/base/base
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/meta/type_traits
    - abseil/status/status
    - abseil/strings/strings
    - abseil/types/variant
    - abseil/utility/utility
  - abseil/strings/cord (1.20211102.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/container/fixed_array
    - abseil/container/inlined_vector
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/strings/cord_internal
    - abseil/strings/cordz_functions
    - abseil/strings/cordz_info
    - abseil/strings/cordz_statistics
    - abseil/strings/cordz_update_scope
    - abseil/strings/cordz_update_tracker
    - abseil/strings/internal
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/optional
  - abseil/strings/cord_internal (1.20211102.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/base/throw_delegate
    - abseil/container/compressed_tuple
    - abseil/container/inlined_vector
    - abseil/container/layout
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/types/span
  - abseil/strings/cordz_functions (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/profiling/exponential_biased
  - abseil/strings/cordz_handle (1.20211102.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/synchronization/synchronization
  - abseil/strings/cordz_info (1.20211102.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/container/inlined_vector
    - abseil/debugging/stacktrace
    - abseil/strings/cord_internal
    - abseil/strings/cordz_functions
    - abseil/strings/cordz_handle
    - abseil/strings/cordz_statistics
    - abseil/strings/cordz_update_tracker
    - abseil/synchronization/synchronization
    - abseil/types/span
  - abseil/strings/cordz_statistics (1.20211102.0):
    - abseil/base/config
    - abseil/strings/cordz_update_tracker
  - abseil/strings/cordz_update_scope (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/strings/cord_internal
    - abseil/strings/cordz_info
    - abseil/strings/cordz_update_tracker
  - abseil/strings/cordz_update_tracker (1.20211102.0):
    - abseil/base/config
  - abseil/strings/internal (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/meta/type_traits
  - abseil/strings/str_format (1.20211102.0):
    - abseil/strings/str_format_internal
  - abseil/strings/str_format_internal (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/numeric/representation
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
  - abseil/strings/strings (1.20211102.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/base/throw_delegate
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/strings/internal
  - abseil/synchronization/graphcycles_internal (1.20211102.0):
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
  - abseil/synchronization/kernel_timeout_internal (1.20211102.0):
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/time/time
  - abseil/synchronization/synchronization (1.20211102.0):
    - abseil/base/atomic_hook
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/synchronization/graphcycles_internal
    - abseil/synchronization/kernel_timeout_internal
    - abseil/time/time
  - abseil/time (1.20211102.0):
    - abseil/time/internal (= 1.20211102.0)
    - abseil/time/time (= 1.20211102.0)
  - abseil/time/internal (1.20211102.0):
    - abseil/time/internal/cctz (= 1.20211102.0)
  - abseil/time/internal/cctz (1.20211102.0):
    - abseil/time/internal/cctz/civil_time (= 1.20211102.0)
    - abseil/time/internal/cctz/time_zone (= 1.20211102.0)
  - abseil/time/internal/cctz/civil_time (1.20211102.0):
    - abseil/base/config
  - abseil/time/internal/cctz/time_zone (1.20211102.0):
    - abseil/base/config
    - abseil/time/internal/cctz/civil_time
  - abseil/time/time (1.20211102.0):
    - abseil/base/base
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/numeric/int128
    - abseil/strings/strings
    - abseil/time/internal/cctz/civil_time
    - abseil/time/internal/cctz/time_zone
  - abseil/types (1.20211102.0):
    - abseil/types/any (= 1.20211102.0)
    - abseil/types/bad_any_cast (= 1.20211102.0)
    - abseil/types/bad_any_cast_impl (= 1.20211102.0)
    - abseil/types/bad_optional_access (= 1.20211102.0)
    - abseil/types/bad_variant_access (= 1.20211102.0)
    - abseil/types/compare (= 1.20211102.0)
    - abseil/types/optional (= 1.20211102.0)
    - abseil/types/span (= 1.20211102.0)
    - abseil/types/variant (= 1.20211102.0)
  - abseil/types/any (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/fast_type_id
    - abseil/meta/type_traits
    - abseil/types/bad_any_cast
    - abseil/utility/utility
  - abseil/types/bad_any_cast (1.20211102.0):
    - abseil/base/config
    - abseil/types/bad_any_cast_impl
  - abseil/types/bad_any_cast_impl (1.20211102.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
  - abseil/types/bad_optional_access (1.20211102.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
  - abseil/types/bad_variant_access (1.20211102.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
  - abseil/types/compare (1.20211102.0):
    - abseil/base/core_headers
    - abseil/meta/type_traits
  - abseil/types/optional (1.20211102.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/types/bad_optional_access
    - abseil/utility/utility
  - abseil/types/span (1.20211102.0):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/throw_delegate
    - abseil/meta/type_traits
  - abseil/types/variant (1.20211102.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/types/bad_variant_access
    - abseil/utility/utility
  - abseil/utility/utility (1.20211102.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/meta/type_traits
  - BoringSSL-GRPC (0.0.24):
    - BoringSSL-GRPC/Implementation (= 0.0.24)
    - BoringSSL-GRPC/Interface (= 0.0.24)
  - BoringSSL-GRPC/Implementation (0.0.24):
    - BoringSSL-GRPC/Interface (= 0.0.24)
  - BoringSSL-GRPC/Interface (0.0.24)
  - camera_avfoundation (0.0.1):
    - Flutter
  - cloud_firestore (2.5.4):
    - Firebase/Firestore (= 9.6.0)
    - firebase_core
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
    - ReachabilitySwift
  - Firebase/Analytics (9.6.0):
    - Firebase/Core
  - Firebase/Auth (9.6.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 9.6.0)
  - Firebase/Core (9.6.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 9.6.0)
  - Firebase/CoreOnly (9.6.0):
    - FirebaseCore (= 9.6.0)
  - Firebase/Firestore (9.6.0):
    - Firebase/CoreOnly
    - FirebaseFirestore (~> 9.6.0)
  - Firebase/Messaging (9.6.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 9.6.0)
  - Firebase/Storage (9.6.0):
    - Firebase/CoreOnly
    - FirebaseStorage (~> 9.6.0)
  - firebase_analytics (9.3.8):
    - Firebase/Analytics (= 9.6.0)
    - firebase_core
    - Flutter
  - firebase_auth (2.0.0):
    - Firebase/Auth (= 9.6.0)
    - firebase_core
    - Flutter
  - firebase_core (1.24.0):
    - Firebase/CoreOnly (= 9.6.0)
    - Flutter
  - firebase_messaging (13.1.0):
    - Firebase/Messaging (= 9.6.0)
    - firebase_core
    - Flutter
  - firebase_storage (10.3.11):
    - Firebase/Storage (= 9.6.0)
    - firebase_core
    - Flutter
  - FirebaseAnalytics (9.6.0):
    - FirebaseAnalytics/AdIdSupport (= 9.6.0)
    - FirebaseCore (~> 9.0)
    - FirebaseInstallations (~> 9.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/MethodSwizzler (~> 7.7)
    - GoogleUtilities/Network (~> 7.7)
    - "GoogleUtilities/NSData+zlib (~> 7.7)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (9.6.0):
    - FirebaseCore (~> 9.0)
    - FirebaseInstallations (~> 9.0)
    - GoogleAppMeasurement (= 9.6.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/MethodSwizzler (~> 7.7)
    - GoogleUtilities/Network (~> 7.7)
    - "GoogleUtilities/NSData+zlib (~> 7.7)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseAppCheckInterop (9.6.0)
  - FirebaseAuth (9.6.0):
    - FirebaseCore (~> 9.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/Environment (~> 7.7)
    - GTMSessionFetcher/Core (< 3.0, >= 1.7)
  - FirebaseAuthInterop (9.6.0)
  - FirebaseCore (9.6.0):
    - FirebaseCoreDiagnostics (~> 9.0)
    - FirebaseCoreInternal (~> 9.0)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/Logger (~> 7.7)
  - FirebaseCoreDiagnostics (9.6.0):
    - GoogleDataTransport (< 10.0.0, >= 9.1.4)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/Logger (~> 7.7)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseCoreExtension (9.6.0):
    - FirebaseCore (~> 9.0)
  - FirebaseCoreInternal (9.6.0):
    - "GoogleUtilities/NSData+zlib (~> 7.7)"
  - FirebaseFirestore (9.6.0):
    - abseil/algorithm (~> 1.20211102.0)
    - abseil/base (~> 1.20211102.0)
    - abseil/container/flat_hash_map (~> 1.20211102.0)
    - abseil/memory (~> 1.20211102.0)
    - abseil/meta (~> 1.20211102.0)
    - abseil/strings/strings (~> 1.20211102.0)
    - abseil/time (~> 1.20211102.0)
    - abseil/types (~> 1.20211102.0)
    - FirebaseCore (~> 9.0)
    - "gRPC-C++ (~> 1.44.0)"
    - leveldb-library (~> 1.22)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseInstallations (9.6.0):
    - FirebaseCore (~> 9.0)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/UserDefaults (~> 7.7)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (9.6.0):
    - FirebaseCore (~> 9.0)
    - FirebaseInstallations (~> 9.0)
    - GoogleDataTransport (< 10.0.0, >= 9.1.4)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/Reachability (~> 7.7)
    - GoogleUtilities/UserDefaults (~> 7.7)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseStorage (9.6.0):
    - FirebaseAppCheckInterop (~> 9.0)
    - FirebaseAuthInterop (~> 9.0)
    - FirebaseCore (~> 9.0)
    - FirebaseCoreExtension (~> 9.0)
    - FirebaseStorageInternal (~> 9.0)
  - FirebaseStorageInternal (9.6.0):
    - FirebaseCore (~> 9.0)
    - GTMSessionFetcher/Core (< 3.0, >= 1.7)
  - Flutter (1.0.0)
  - flutter_contacts (0.0.1):
    - Flutter
  - flutter_idensic_mobile_sdk_plugin (1.32.1):
    - Flutter
    - IdensicMobileSDK (= 1.32.0)
  - flutter_jailbreak_detection (1.0.0):
    - Flutter
    - IOSSecuritySuite
  - flutter_keyboard_visibility (0.0.1):
    - Flutter
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_localization (0.0.1):
    - Flutter
  - flutter_native_timezone (0.0.1):
    - Flutter
  - flutter_secure_storage (3.3.1):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
    - Toast
  - FMDB (2.7.12):
    - FMDB/standard (= 2.7.12)
  - FMDB/Core (2.7.12)
  - FMDB/standard (2.7.12):
    - FMDB/Core
  - geolocator_apple (1.2.0):
    - Flutter
  - GoogleAppMeasurement (9.6.0):
    - GoogleAppMeasurement/AdIdSupport (= 9.6.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/MethodSwizzler (~> 7.7)
    - GoogleUtilities/Network (~> 7.7)
    - "GoogleUtilities/NSData+zlib (~> 7.7)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (9.6.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 9.6.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/MethodSwizzler (~> 7.7)
    - GoogleUtilities/Network (~> 7.7)
    - "GoogleUtilities/NSData+zlib (~> 7.7)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (9.6.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/MethodSwizzler (~> 7.7)
    - GoogleUtilities/Network (~> 7.7)
    - "GoogleUtilities/NSData+zlib (~> 7.7)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - "gRPC-C++ (1.44.0)":
    - "gRPC-C++/Implementation (= 1.44.0)"
    - "gRPC-C++/Interface (= 1.44.0)"
  - "gRPC-C++/Implementation (1.44.0)":
    - abseil/base/base (= 1.20211102.0)
    - abseil/base/core_headers (= 1.20211102.0)
    - abseil/container/flat_hash_map (= 1.20211102.0)
    - abseil/container/inlined_vector (= 1.20211102.0)
    - abseil/functional/bind_front (= 1.20211102.0)
    - abseil/hash/hash (= 1.20211102.0)
    - abseil/memory/memory (= 1.20211102.0)
    - abseil/random/random (= 1.20211102.0)
    - abseil/status/status (= 1.20211102.0)
    - abseil/status/statusor (= 1.20211102.0)
    - abseil/strings/cord (= 1.20211102.0)
    - abseil/strings/str_format (= 1.20211102.0)
    - abseil/strings/strings (= 1.20211102.0)
    - abseil/synchronization/synchronization (= 1.20211102.0)
    - abseil/time/time (= 1.20211102.0)
    - abseil/types/optional (= 1.20211102.0)
    - abseil/types/variant (= 1.20211102.0)
    - abseil/utility/utility (= 1.20211102.0)
    - "gRPC-C++/Interface (= 1.44.0)"
    - gRPC-Core (= 1.44.0)
  - "gRPC-C++/Interface (1.44.0)"
  - gRPC-Core (1.44.0):
    - gRPC-Core/Implementation (= 1.44.0)
    - gRPC-Core/Interface (= 1.44.0)
  - gRPC-Core/Implementation (1.44.0):
    - abseil/base/base (= 1.20211102.0)
    - abseil/base/core_headers (= 1.20211102.0)
    - abseil/container/flat_hash_map (= 1.20211102.0)
    - abseil/container/inlined_vector (= 1.20211102.0)
    - abseil/functional/bind_front (= 1.20211102.0)
    - abseil/hash/hash (= 1.20211102.0)
    - abseil/memory/memory (= 1.20211102.0)
    - abseil/random/random (= 1.20211102.0)
    - abseil/status/status (= 1.20211102.0)
    - abseil/status/statusor (= 1.20211102.0)
    - abseil/strings/cord (= 1.20211102.0)
    - abseil/strings/str_format (= 1.20211102.0)
    - abseil/strings/strings (= 1.20211102.0)
    - abseil/synchronization/synchronization (= 1.20211102.0)
    - abseil/time/time (= 1.20211102.0)
    - abseil/types/optional (= 1.20211102.0)
    - abseil/types/variant (= 1.20211102.0)
    - abseil/utility/utility (= 1.20211102.0)
    - BoringSSL-GRPC (= 0.0.24)
    - gRPC-Core/Interface (= 1.44.0)
    - Libuv-gRPC (= 0.0.10)
  - gRPC-Core/Interface (1.44.0)
  - GTMSessionFetcher/Core (2.3.0)
  - IdensicMobileSDK (1.32.0):
    - IdensicMobileSDK/Default (= 1.32.0)
  - IdensicMobileSDK/Core (1.32.0)
  - IdensicMobileSDK/Default (1.32.0):
    - IdensicMobileSDK/Core
  - image_crop (0.0.1):
    - Flutter
  - image_gallery_saver (1.5.0):
    - Flutter
  - image_picker_ios (0.0.1):
    - Flutter
  - IOSSecuritySuite (2.1.0)
  - launch_review (0.0.1):
    - Flutter
  - leveldb-library (1.22.5)
  - Libuv-gRPC (0.0.10):
    - Libuv-gRPC/Implementation (= 0.0.10)
    - Libuv-gRPC/Interface (= 0.0.10)
  - Libuv-gRPC/Implementation (0.0.10):
    - Libuv-gRPC/Interface (= 0.0.10)
  - Libuv-gRPC/Interface (0.0.10)
  - local_auth (0.0.1):
    - Flutter
  - lock_to_win (0.0.1):
    - Flutter
  - MTBBarcodeScanner (5.0.11)
  - nanopb (2.30909.1):
    - nanopb/decode (= 2.30909.1)
    - nanopb/encode (= 2.30909.1)
  - nanopb/decode (2.30909.1)
  - nanopb/encode (2.30909.1)
  - package_info (0.0.1):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.1.1):
    - Flutter
  - photo_manager (2.0.0):
    - Flutter
    - FlutterMacOS
  - PromisesObjC (2.4.0)
  - qr_code_scanner (0.2.0):
    - Flutter
    - MTBBarcodeScanner
  - qrcode_flutter (0.0.1):
    - Flutter
  - ReachabilitySwift (5.2.3)
  - scan (0.0.1):
    - Flutter
  - share (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sms_autofill (0.0.1):
    - Flutter
  - sms_receiver (0.0.1):
    - Flutter
  - sqflite (0.0.3):
    - Flutter
    - FMDB (>= 2.7.5)
  - Toast (4.1.1)
  - uni_links (0.0.1):
    - Flutter
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter

DEPENDENCIES:
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - cloud_firestore (from `.symlinks/plugins/cloud_firestore/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_auth (from `.symlinks/plugins/firebase_auth/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - firebase_storage (from `.symlinks/plugins/firebase_storage/ios`)
  - Flutter (from `Flutter`)
  - flutter_contacts (from `.symlinks/plugins/flutter_contacts/ios`)
  - flutter_idensic_mobile_sdk_plugin (from `.symlinks/plugins/flutter_idensic_mobile_sdk_plugin/ios`)
  - flutter_jailbreak_detection (from `.symlinks/plugins/flutter_jailbreak_detection/ios`)
  - flutter_keyboard_visibility (from `.symlinks/plugins/flutter_keyboard_visibility/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_localization (from `.symlinks/plugins/flutter_localization/ios`)
  - flutter_native_timezone (from `.symlinks/plugins/flutter_native_timezone/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/ios`)
  - image_crop (from `.symlinks/plugins/image_crop/ios`)
  - image_gallery_saver (from `.symlinks/plugins/image_gallery_saver/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - launch_review (from `.symlinks/plugins/launch_review/ios`)
  - local_auth (from `.symlinks/plugins/local_auth/ios`)
  - lock_to_win (from `.symlinks/plugins/lock_to_win/ios`)
  - package_info (from `.symlinks/plugins/package_info/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/ios`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - photo_manager (from `.symlinks/plugins/photo_manager/ios`)
  - qr_code_scanner (from `.symlinks/plugins/qr_code_scanner/ios`)
  - qrcode_flutter (from `.symlinks/plugins/qrcode_flutter/ios`)
  - scan (from `.symlinks/plugins/scan/ios`)
  - share (from `.symlinks/plugins/share/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/ios`)
  - sms_autofill (from `.symlinks/plugins/sms_autofill/ios`)
  - sms_receiver (from `.symlinks/plugins/sms_receiver/ios`)
  - sqflite (from `.symlinks/plugins/sqflite/ios`)
  - uni_links (from `.symlinks/plugins/uni_links/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/ios`)

SPEC REPOS:
  https://github.com/SumSubstance/Specs.git:
    - IdensicMobileSDK
  trunk:
    - abseil
    - BoringSSL-GRPC
    - Firebase
    - FirebaseAnalytics
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseAuthInterop
    - FirebaseCore
    - FirebaseCoreDiagnostics
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseFirestore
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseStorage
    - FirebaseStorageInternal
    - FMDB
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleUtilities
    - "gRPC-C++"
    - gRPC-Core
    - GTMSessionFetcher
    - IOSSecuritySuite
    - leveldb-library
    - Libuv-gRPC
    - MTBBarcodeScanner
    - nanopb
    - PromisesObjC
    - ReachabilitySwift
    - Toast

EXTERNAL SOURCES:
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  cloud_firestore:
    :path: ".symlinks/plugins/cloud_firestore/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_auth:
    :path: ".symlinks/plugins/firebase_auth/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  firebase_storage:
    :path: ".symlinks/plugins/firebase_storage/ios"
  Flutter:
    :path: Flutter
  flutter_contacts:
    :path: ".symlinks/plugins/flutter_contacts/ios"
  flutter_idensic_mobile_sdk_plugin:
    :path: ".symlinks/plugins/flutter_idensic_mobile_sdk_plugin/ios"
  flutter_jailbreak_detection:
    :path: ".symlinks/plugins/flutter_jailbreak_detection/ios"
  flutter_keyboard_visibility:
    :path: ".symlinks/plugins/flutter_keyboard_visibility/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_localization:
    :path: ".symlinks/plugins/flutter_localization/ios"
  flutter_native_timezone:
    :path: ".symlinks/plugins/flutter_native_timezone/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/ios"
  image_crop:
    :path: ".symlinks/plugins/image_crop/ios"
  image_gallery_saver:
    :path: ".symlinks/plugins/image_gallery_saver/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  launch_review:
    :path: ".symlinks/plugins/launch_review/ios"
  local_auth:
    :path: ".symlinks/plugins/local_auth/ios"
  lock_to_win:
    :path: ".symlinks/plugins/lock_to_win/ios"
  package_info:
    :path: ".symlinks/plugins/package_info/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/ios"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  photo_manager:
    :path: ".symlinks/plugins/photo_manager/ios"
  qr_code_scanner:
    :path: ".symlinks/plugins/qr_code_scanner/ios"
  qrcode_flutter:
    :path: ".symlinks/plugins/qrcode_flutter/ios"
  scan:
    :path: ".symlinks/plugins/scan/ios"
  share:
    :path: ".symlinks/plugins/share/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/ios"
  sms_autofill:
    :path: ".symlinks/plugins/sms_autofill/ios"
  sms_receiver:
    :path: ".symlinks/plugins/sms_receiver/ios"
  sqflite:
    :path: ".symlinks/plugins/sqflite/ios"
  uni_links:
    :path: ".symlinks/plugins/uni_links/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/ios"

SPEC CHECKSUMS:
  abseil: ebe5b5529fb05d93a8bdb7951607be08b7fa71bc
  BoringSSL-GRPC: 3175b25143e648463a56daeaaa499c6cb86dad33
  camera_avfoundation: 3125e8cd1a4387f6f31c6c63abb8a55892a9eeeb
  cloud_firestore: bc2bc8456db5f8067349de0cbd9fb36b0747c258
  connectivity_plus: 07c49e96d7fc92bc9920617b83238c4d178b446a
  Firebase: 5ae8b7cf8efce559a653aef0ad95bab3f427c351
  firebase_analytics: 9937a91df25cce5b0ee19eaf65277ff8bf0f88e5
  firebase_auth: e10fad9165a799bb78575e5f80e4ad6d182dd9ec
  firebase_core: 7c28ecc1e5dd74e03829ac3e9ff5ba3314e737a9
  firebase_messaging: a2a8c6db6d799c7f59b1c0d7c68a2f0027bfa2c4
  firebase_storage: d1d5bb4959e97b203e5076d4d0edbb7028b711fd
  FirebaseAnalytics: 89ad762c6c3852a685794174757e2c60a36b6a82
  FirebaseAppCheckInterop: d5ecda0c09f8069406643d6e0fa12c09d1b736e3
  FirebaseAuth: e4a5d3c36e778e41141b91cc861103a441d80bcc
  FirebaseAuthInterop: b6cf02117f13a8400c8c8b4421e12c6e850bcaf3
  FirebaseCore: 2082fffcd855f95f883c0a1641133eb9bbe76d40
  FirebaseCoreDiagnostics: 99a495094b10a57eeb3ae8efa1665700ad0bdaa6
  FirebaseCoreExtension: e83465d1236b166d1d445bbf0e82b65acb30b73b
  FirebaseCoreInternal: bca76517fe1ed381e989f5e7d8abb0da8d85bed3
  FirebaseFirestore: c09ce6d050745a45fb75ebd901005ed3279c3caf
  FirebaseInstallations: 0a115432c4e223c5ab20b0dbbe4cbefa793a0e8e
  FirebaseMessaging: a4d7910e4af663c9cbfc1071c5bef34651690949
  FirebaseStorage: 1fead543a1f441c3b434c1c9f12560dd82f8b568
  FirebaseStorageInternal: 81d8a597324ccd06c41a43c5700bc1185a2fc328
  Flutter: f04841e97a9d0b0a8025694d0796dd46242b2854
  flutter_contacts: edb1c5ce76aa433e20e6cb14c615f4c0b66e0983
  flutter_idensic_mobile_sdk_plugin: 5fc6dcd6bbcb79121e1cc18b60fa84c13b7fe3b7
  flutter_jailbreak_detection: c5bf66ff6c0c4230769b6ba0bd63eb6ac4148a76
  flutter_keyboard_visibility: 0339d06371254c3eb25eeb90ba8d17dca8f9c069
  flutter_local_notifications: 0c0b1ae97e741e1521e4c1629a459d04b9aec743
  flutter_localization: f43b18844a2b3d2c71fd64f04ffd6b1e64dd54d4
  flutter_native_timezone: 5f05b2de06c9776b4cc70e1839f03de178394d22
  flutter_secure_storage: 7953c38a04c3fdbb00571bcd87d8e3b5ceb9daec
  fluttertoast: 48c57db1b71b0ce9e6bba9f31c940ff4b001293c
  FMDB: 728731dd336af3936ce00f91d9d8495f5718a0e6
  geolocator_apple: 6cbaf322953988e009e5ecb481f07efece75c450
  GoogleAppMeasurement: 6de2b1a69e4326eb82ee05d138f6a5cb7311bcb1
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  "gRPC-C++": 9675f953ace2b3de7c506039d77be1f2e77a8db2
  gRPC-Core: 943e491cb0d45598b0b0eb9e910c88080369290b
  GTMSessionFetcher: 3a63d75eecd6aa32c2fc79f578064e1214dfdec2
  IdensicMobileSDK: f61fd95d1f1aee77468d8e48ad8cc88ef625a9db
  image_crop: e0a67085d3ebf3cf46ca46d61c53a082507b0bc3
  image_gallery_saver: 259eab68fb271cfd57d599904f7acdc7832e7ef2
  image_picker_ios: 4a8aadfbb6dc30ad5141a2ce3832af9214a705b5
  IOSSecuritySuite: 45e8531b05ffa72b5661cbdb5b5b5648a8de1a84
  launch_review: 75d5a956ba8eaa493e9c9d4bf4c05e505e8d5ed0
  leveldb-library: e8eadf9008a61f9e1dde3978c086d2b6d9b9dc28
  Libuv-gRPC: 55e51798e14ef436ad9bc45d12d43b77b49df378
  local_auth: 1740f55d7af0a2e2a8684ce225fe79d8931e808c
  lock_to_win: d1158323d6b9450852185328c29ed09af4ddddf1
  MTBBarcodeScanner: f453b33c4b7dfe545d8c6484ed744d55671788cb
  nanopb: d4d75c12cd1316f4a64e3c6963f879ecd4b5e0d5
  package_info: 873975fc26034f0b863a300ad47e7f1ac6c7ec62
  path_provider_foundation: 29f094ae23ebbca9d3d0cec13889cd9060c0e943
  permission_handler_apple: e76247795d700c14ea09e3a2d8855d41ee80a2e6
  photo_manager: ff695c7a1dd5bc379974953a2b5c0a293f7c4c8a
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  qr_code_scanner: bb67d64904c3b9658ada8c402e8b4d406d5d796e
  qrcode_flutter: de88a439c5eebe01b38b647af520e674770d4462
  ReachabilitySwift: 7f151ff156cea1481a8411701195ac6a984f4979
  scan: aea35bb4aa59ccc8839c576a18cd57c7d492cc86
  share: 0b2c3e82132f5888bccca3351c504d0003b3b410
  shared_preferences_foundation: 5b919d13b803cadd15ed2dc053125c68730e5126
  sms_autofill: c461043483362c3f1709ee76eaae6eb570b31686
  sms_receiver: eea6100a77faa60a42b89f56549600cf3f233b1e
  sqflite: 31f7eba61e3074736dff8807a9b41581e4f7f15a
  Toast: 1f5ea13423a1e6674c4abdac5be53587ae481c4e
  uni_links: d97da20c7701486ba192624d99bffaaffcfc298a
  url_launcher_ios: 68d46cc9766d0c41dbdc884310529557e3cd7a86
  video_player_avfoundation: 81e49bb3d9fb63dccf9fa0f6d877dc3ddbeac126
  webview_flutter_wkwebview: b7e70ef1ddded7e69c796c7390ee74180182971f

PODFILE CHECKSUM: 1f8d9403d1244378768c439287780678b83c5de2

COCOAPODS: 1.15.2
