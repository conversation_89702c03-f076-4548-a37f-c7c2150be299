// import 'dart:async';
// import 'dart:io';
//
// import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:firebase_storage/firebase_storage.dart' as firebase_storage;
// import 'package:flutter/material.dart';
// import 'package:image_picker/image_picker.dart';
// import 'package:intl/intl.dart' show DateFormat;
// // import 'package:dash_chat/dash_chat.dart';
// import 'package:likewallet/ImageTheme.dart';
// import 'package:likewallet/Theme.dart';
// import 'package:likewallet/libraryman/app_local.dart';
// import 'package:likewallet/libraryman/auth.dart';
//
// import 'package:likewallet/screen_util.dart';
//
// class ChatSupport extends StatefulWidget {
//   ChatSupport({this.name, this.uid, this.avatar});
//   final String? name;
//   final String? uid;
//   final String? avatar;
//   @override
//   _ChatSupport createState() => _ChatSupport();
// }
//
// class _ChatSupport extends State<ChatSupport> {
//   _ChatSupport({this.name, this.uid, this.avatar});
//   final GlobalKey<DashChatState> _chatViewKey = GlobalKey<DashChatState>();
//   final String? name;
//   String? uid;
//   final String? avatar;
//
//   final ImagePicker _picker = ImagePicker();
//   late BaseAuth auth;
//   ChatUser user = ChatUser(
//     name: "Guest",
//     avatar:
//         "https://s3-ap-southeast-1.amazonaws.com/storage-pkg/icon/icon1586334220default-avatar-png-6.png",
//   );
//
//   List<ChatMessage> messages = [];
//   var m = [];
//
//   var i = 0;
//
//   @override
//   void initState() {
//     super.initState();
//     auth = new Auth();
//     auth.getCurrentUser().then((data) {
//       FirebaseFirestore.instance
//           .collection('users')
//           .doc(data!.uid)
//           .get()
//           .then((onValue) {
//         setState(() {
//           print(data.uid);
//           uid = data.uid;
//           user = ChatUser(
//               name: onValue.data()!["firstName"],
//               avatar: onValue.data()!["imageProfile"] ??
//                   'https://s3-ap-southeast-1.amazonaws.com/storage-pkg/icon/icon1586334220default-avatar-png-6.png',
//               uid: data.uid);
//         });
//       });
//     });
//   }
//
//   void systemMessage() {
//     Timer(Duration(milliseconds: 300), () {
//       if (i < 6) {
//         setState(() {
//           messages = [...messages, m[i]];
//         });
//         i++;
//       }
// //      Timer(Duration(milliseconds: 300), () {
// //        _chatViewKey.currentState.scrollController
// //          ..animateTo(
// //            _chatViewKey.currentState.scrollController.position.maxScrollExtent,
// //            curve: Curves.easeOut,
// //            duration: const Duration(milliseconds: 300),
// //          );
// //      });
//     });
//   }
//
//   void onSend(ChatMessage message) {
//     var documentReference = FirebaseFirestore.instance
//         .collection('messages')
//         .doc(uid)
//         .collection('messages')
//         .doc(DateTime.now().millisecondsSinceEpoch.toString());
//
//     FirebaseFirestore.instance.runTransaction((transaction) async {
//       await transaction.set(
//         documentReference,
//         message.toJson(),
//       );
//     });
//     setState(() {
//       messages = [...messages, message];
//       print(messages.length);
//     });
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//         backgroundColor: Colors.black12,
//         body: Stack(
//           children: <Widget>[
//             bg(),
//             StreamBuilder(
//                 stream: FirebaseFirestore.instance
//                     .collection('messages')
//                     .doc(uid)
//                     .collection('messages')
//                     .limit(100)
//                     .snapshots(),
//                 builder: (BuildContext context,
//                     AsyncSnapshot<QuerySnapshot<Map<String, dynamic>>>
//                         snapshot) {
//                   if (!snapshot.hasData) {
//                     return Center(
//                       child: CircularProgressIndicator(
//                         valueColor: AlwaysStoppedAnimation<Color>(
//                           Theme.of(context).primaryColor,
//                         ),
//                       ),
//                     );
//                   } else {
//                     Timer(Duration(milliseconds: 100), () {
//                       _chatViewKey.currentState!.scrollController.jumpTo(
//                           _chatViewKey.currentState!.scrollController.position
//                               .maxScrollExtent);
//                       // ..animateTo(
//                       //   _chatViewKey.currentState!.scrollController.position
//                       //       .maxScrollExtent,
//                       //   curve: Curves.easeOut,
//                       //   duration: const Duration(milliseconds: 500),
//                       // );
//                       // scrollController.jumpTo();
//                     });
//                     List<DocumentSnapshot<Map<String, dynamic>>> items =
//                         snapshot.data!.docs;
//                     var messages = items
//                         .map((i) => ChatMessage.fromJson(i.data()))
//                         .toList();
//                     return DashChat(
//                       key: _chatViewKey,
//                       inverted: false,
//                       onSend: onSend,
//                       user: user,
//                       inputToolbarPadding: EdgeInsets.only(
//                         bottom: mediaQuery(context, 'height', 0),
//                       ),
//                       inputDecoration: InputDecoration(
//                         isDense: true,
//                         filled: true,
//                         fillColor: LikeWalletAppTheme.nearlyWhite,
//                         hintText: AppLocalizations.of(context)!
//                             .translate('add_message_chat'),
//                         hintStyle: TextStyle(
//                           letterSpacing: 0.5,
//                           fontFamily:
//                               AppLocalizations.of(context)!.translate('font1'),
//                           fontWeight: FontWeight.normal,
//                           fontSize: mediaQuery(context, 'height', 41),
//                           color: LikeWalletAppTheme.black,
//                         ),
//                         border: InputBorder.none,
//                         focusedBorder: OutlineInputBorder(
//                           borderSide: BorderSide(color: Colors.white),
//                           borderRadius: BorderRadius.circular(10),
//                         ),
//                         enabledBorder: UnderlineInputBorder(
//                           borderSide: BorderSide(color: Colors.white),
//                           borderRadius: BorderRadius.circular(10),
//                         ),
//                       ),
//                       dateFormat: DateFormat('yyyy-MMM-dd'),
//                       timeFormat: DateFormat('HH:mm'),
//                       messages: messages,
//                       showUserAvatar: true,
//                       showAvatarForEveryMessage: true,
//                       scrollToBottom: false,
//                       onPressAvatar: (ChatUser user) {
//                         print("OnPressAvatar: ${user.name}");
//                       },
//                       onLongPressAvatar: (ChatUser user) {
//                         print("OnLongPressAvatar: ${user.name}");
//                       },
//                       inputMaxLines: 10,
//                       messageContainerPadding:
//                           EdgeInsets.only(left: 5.0, right: 5.0),
//                       alwaysShowSend: true,
//                       inputTextStyle: TextStyle(
//                         letterSpacing: 0.5,
//                         fontFamily:
//                             AppLocalizations.of(context)!.translate('font1'),
//                         fontWeight: FontWeight.normal,
//                         fontSize: mediaQuery(context, 'height', 41),
//                         color: LikeWalletAppTheme.black,
//                       ),
//                       inputContainerStyle: BoxDecoration(
//                         border: Border.all(width: 0.0),
//                         color: Colors.white,
//                       ),
//                       onQuickReply: (Reply reply) {
//                         setState(() {
//                           messages.add(ChatMessage(
//                               text: reply.value,
//                               createdAt: DateTime.now(),
//                               user: user));
//
//                           messages = [...messages];
//                         });
//
//                         Timer(Duration(milliseconds: 300), () {
// //                    _chatViewKey.currentState.scrollController
// //                      ..animateTo(
// //                        _chatViewKey.currentState.scrollController.position
// //                            .maxScrollExtent,
// //                        curve: Curves.easeOut,
// //                        duration: const Duration(milliseconds: 300),
// //                      );
//                           if (i == 0) {
//                             systemMessage();
//                             Timer(Duration(milliseconds: 600), () {
//                               systemMessage();
//                             });
//                           } else {
//                             systemMessage();
//                           }
//                         });
//                       },
//                       onLoadEarlier: () {
//                         print("laoding...");
//                       },
//                       shouldShowLoadEarlier: false,
//                       showTraillingBeforeSend: true,
//                       leading: <Widget>[
//                         IconButton(
//                           icon: Image.asset(
//                             LikeWalletImage.contact_us_takePhoto,
//                             height: mediaQuery(context, 'height', 56.6),
//                             width: mediaQuery(context, 'width', 69.74),
//                           ),
//                           onPressed: () async {
//                             XFile? result = await _picker.pickImage(
//                               source: ImageSource.camera,
//                               imageQuality: 100,
//                               maxHeight: 600,
//                             );
//                             print(result!.path);
//                             if (result.path.isNotEmpty) {
//                               firebase_storage.Reference storageRef =
//                                   firebase_storage.FirebaseStorage.instance
//                                       .ref("chat_images")
//                                       .child("$uid")
//                                       .child(DateTime.now().toString());
//                               final metadata =
//                                   firebase_storage.SettableMetadata(
//                                       contentType: 'image/jpeg',
//                                       customMetadata: {
//                                     'picked-file-path': result.path
//                                   });
//                               firebase_storage.UploadTask uploadTask =
//                                   storageRef.putFile(
//                                       File(result.path), metadata);
//                               firebase_storage.TaskSnapshot download =
//                                   await uploadTask;
//
//                               String url = await download.ref.getDownloadURL();
//
//                               print(url);
//                               ChatMessage message =
//                                   ChatMessage(text: "", user: user, image: url);
//                               var documentReference = FirebaseFirestore.instance
//                                   .collection('messages')
//                                   .doc(uid)
//                                   .collection('messages')
//                                   .doc(DateTime.now()
//                                       .millisecondsSinceEpoch
//                                       .toString());
//
//                               FirebaseFirestore.instance
//                                   .runTransaction((transaction) async {
//                                 await transaction.set(
//                                   documentReference,
//                                   message.toJson(),
//                                 );
//                               });
//                             }
//                           },
//                         ),
//                         IconButton(
//                           icon: Image.asset(
//                             LikeWalletImage.contact_us_photo,
//                             height: mediaQuery(context, 'height', 57.22),
//                             width: mediaQuery(context, 'width', 61.53),
//                           ),
//                           onPressed: () async {
//                             XFile? result = await _picker.pickImage(
//                               source: ImageSource.gallery,
//                               imageQuality: 90,
//                               maxHeight: 697,
//                               // maxWidth: 600,
//                             );
//
//                             print(result!.path);
//                             if (result.path.isNotEmpty) {
//                               firebase_storage.Reference storageRef =
//                                   firebase_storage.FirebaseStorage.instance
//                                       .ref("chat_images")
//                                       .child("$uid")
//                                       .child(DateTime.now().toString());
//                               final metadata =
//                                   firebase_storage.SettableMetadata(
//                                       contentType: 'image/jpeg',
//                                       customMetadata: {
//                                     'picked-file-path': result.path
//                                   });
//                               firebase_storage.UploadTask uploadTask =
//                                   storageRef.putFile(
//                                       File(result.path), metadata);
//                               firebase_storage.TaskSnapshot download =
//                                   await uploadTask;
//
//                               String url = await download.ref.getDownloadURL();
//
//                               print(url);
//                               ChatMessage message =
//                                   ChatMessage(text: "", user: user, image: url);
//
//                               var documentReference = FirebaseFirestore.instance
//                                   .collection('messages')
//                                   .doc(uid)
//                                   .collection('messages')
//                                   .doc(DateTime.now()
//                                       .millisecondsSinceEpoch
//                                       .toString());
//
//                               FirebaseFirestore.instance
//                                   .runTransaction((transaction) async {
//                                 transaction.set(
//                                   documentReference,
//                                   message.toJson(),
//                                 );
//                               });
//                             }
//                           },
//                         )
//                       ],
//                     );
//                   }
//                 }),
//             Positioned(
//               top: mediaQuery(context, 'height', 140),
//               child: backButton(context, LikeWalletAppTheme.gray),
//             ),
//           ],
//         ));
//   }
//
//   bg() {
//     return Image.asset(
//       LikeWalletImage.contact_us_bg,
//       fit: BoxFit.fill,
//       width: mediaQuery(context, "width", 1080),
//     );
//   }
// }
