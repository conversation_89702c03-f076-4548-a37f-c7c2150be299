// import 'package:adobe_xd/pinned.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
//
// class AlertLENDEXCustom {
//   Widget buttonSkip() {
//     return Container(
//         child: Stack(
//       alignment: Alignment.center,
//       children: [
//         SizedBox(
//           height: 120.h,
//           width: 298.w,
//           child: SvgPicture.string(
//             '<svg viewBox="387.0 592.0 298.0 120.0" ><defs><filter id="shadow"><feDropShadow dx="0" dy="12" stdDeviation="24"/></filter></defs><path transform="translate(387.0, 592.0)" d="M 57 0 L 241 0 C 272.480224609375 0 298 25.51976776123047 298 57 L 298 63 C 298 94.48023223876953 272.480224609375 120 241 120 L 57 120 C 25.51976776123047 120 0 94.48023223876953 0 63 L 0 57 C 0 25.51976776123047 25.51976776123047 0 57 0 Z" fill="#1b7fc3" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" filter="url(#shadow)"/></svg>',
//             allowDrawingOutsideViewBox: true,
//           ),
//         ),
//         SizedBox(
//           child: Text(
//             'SKIP',
//             style: TextStyle(
//               fontFamily: 'Noto Sans',
//               fontSize: 39.h,
//               color: const Color(0xfffdebc1),
//               letterSpacing: 3.9000000000000004.w,
//             ),
//             textAlign: TextAlign.center,
//           ),
//         ),
//       ],
//     ));
//   }
//
//   Widget bg_lendex() {
//     return SizedBox(
//       width: 1007.0.w,
//       height: 1510.0.h,
//       child: Stack(
//         children: <Widget>[
//           Pinned.fromSize(
//             bounds: Rect.fromLTWH(0.0, 0.0, 1007.0.w, 1510.0.h),
//             size: Size(1007.0.w, 1510.0.h),
//             pinLeft: true,
//             pinRight: true,
//             pinTop: true,
//             pinBottom: true,
//             child: SvgPicture.string(
//               '<svg viewBox="-19573.0 18257.0 1007.0 1510.0" ><defs><filter id="shadow"><feDropShadow dx="0" dy="-57" stdDeviation="65"/></filter><linearGradient id="gradient" x1="0.5" y1="0.0" x2="0.5" y2="0.981749"><stop offset="0.0" stop-color="#ff00dfe8"  /><stop offset="0.497537" stop-color="#ff0099b2"  /><stop offset="1.0" stop-color="#ff007f98"  /></linearGradient></defs><path transform="translate(-19573.0, 18257.0)" d="M 134 0 L 873 0 C 947.0061645507812 0 1007 59.99384307861328 1007 134 L 1007 1376 C 1007 1450.006103515625 947.0061645507812 1510 873 1510 L 134 1510 C 59.99384307861328 1510 0 1450.006103515625 0 1376 L 0 134 C 0 59.99384307861328 59.99384307861328 0 134 0 Z" fill="url(#gradient)" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" filter="url(#shadow)"/></svg>',
//               allowDrawingOutsideViewBox: true,
//               fit: BoxFit.fill,
//             ),
//           ),
//           Pinned.fromSize(
//             bounds: Rect.fromLTWH(0.0, 0.0, 1007.0.w, 472.4.h),
//             size: Size(1007.0.w, 1510.0.h),
//             pinLeft: true,
//             pinRight: true,
//             pinTop: true,
//             fixedHeight: true,
//             child: SvgPicture.string(
//               '<svg viewBox="-19573.0 18257.0 1007.0 472.4" ><defs><filter id="shadow"><feDropShadow dx="0" dy="-57" stdDeviation="65"/></filter></defs><path transform="translate(-19573.0, 18257.0)" d="M 134 0 L 873 0 C 947.0061645507812 0 1007 59.99384307861328 1007 134 L 1007 472.375 L 0 472.375 L 0 134 C 0 59.99384307861328 59.99384307861328 0 134 0 Z" fill="#ffffff" fill-opacity="0.1" stroke="none" stroke-width="1" stroke-opacity="0.1" stroke-miterlimit="4" stroke-linecap="butt" filter="url(#shadow)"/></svg>',
//               allowDrawingOutsideViewBox: true,
//               fit: BoxFit.fill,
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
