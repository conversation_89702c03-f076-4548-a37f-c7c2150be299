// import 'package:cached_network_image/cached_network_image.dart';
// import 'package:carousel_slider/carousel_slider.dart';
// import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:date_format/date_format.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_spinkit/flutter_spinkit.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:likewallet/ImageTheme.dart';
// import 'package:likewallet/Theme.dart';
// import 'package:likewallet/alert_lendex/alert_lendex_custom.dart';
// import 'package:likewallet/libraryman/app_local.dart';
// import 'package:likewallet/screen_util.dart';
// import 'package:likewallet/alert_lendex/alert_lendex.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:shared_preferences/shared_preferences.dart';
//
// class AlertLendex extends StatefulWidget {
//   @override
//   _AlertLendexState createState() => _AlertLendexState();
// }
//
// class _AlertLendexState extends State<AlertLendex> {
//   final List<String> imgList = [];
//   var title = [];
//   var detail = [];
//   var detail2 = [];
//   var detail3 = [];
//   int _current = 0;
//   var language_code;
//
//   @override
//   void initState() {
//     super.initState();
//     getLanguage().then((value) async {
//       await eventLoan();
//     });
//   }
//
//   Future<String> getLanguage() async {
//     var prefs = await SharedPreferences.getInstance();
//     language_code = await prefs.getString('language_code');
//     print(language_code);
//     return language_code;
//   }
//
//   eventLoan() async {
//     try {
//       await FirebaseFirestore.instance
//           .collection('event')
//           .doc('loan')
//           .collection('page')
//           .get()
//           .then((value) {
//         value.docs.forEach((data) {
//           setState(() {
//             title.add(data.data()['title']);
//             detail.add(data.data()['detail']);
//             detail2.add(data.data()['detail2']);
//             detail3.add(data.data()['detail3']);
//             imgList.add(data.data()['image']);
//           });
//           print(title);
//           print(detail);
//           print(detail2);
//           print(detail3);
//           print(imgList);
//         });
//       });
//     } catch (e) {
//       print(e);
//     }
//   }
//
//   changeTitle(index) {
//     return language_code == "en"
//         ? title[index][0]
//         : language_code == "th"
//             ? title[index][1]
//             : language_code == "km"
//                 ? title[index][2]
//                 : language_code == "vi"
//                     ? title[index][3]
//                     : title[index][4];
//   }
//
//   changeDetail(index) {
//     return language_code == "en"
//         ? detail[index][0]
//         : language_code == "th"
//             ? detail[index][1]
//             : language_code == "km"
//                 ? detail[index][2]
//                 : language_code == "vi"
//                     ? detail[index][3]
//                     : detail[index][4];
//   }
//
//   changeDetail2(index) {
//     return language_code == "en"
//         ? detail2[index][0]
//         : language_code == "th"
//             ? detail2[index][1]
//             : language_code == "km"
//                 ? detail2[index][2]
//                 : language_code == "vi"
//                     ? detail2[index][3]
//                     : detail2[index][4];
//   }
//
//   changeDetail3(index) {
//     return language_code == "en"
//         ? detail3[index][0]
//         : language_code == "th"
//             ? detail3[index][1]
//             : language_code == "km"
//                 ? detail3[index][2]
//                 : language_code == "vi"
//                     ? detail3[index][3]
//                     : detail3[index][4];
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return GestureDetector(
//       onTap: () => Navigator.of(context).pop(),
//       child: Scaffold(
//           backgroundColor: Colors.transparent, body: dialogContent(context)),
//     );
//   }
//
//   dialogContent(BuildContext context) {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.center,
//       mainAxisAlignment: MainAxisAlignment.center,
//       children: [
//         Row(
//           mainAxisAlignment: MainAxisAlignment.center,
//           crossAxisAlignment: CrossAxisAlignment.center,
//           children: [
//             Container(
//               width: 1007.0.w,
//               height: 1573.0.h,
//               child: Stack(
//                 alignment: Alignment.topCenter,
//                 children: [
//                   Positioned(
//                     bottom: 0,
//                     child: AlertLENDEXCustom().bg_lendex(),
//                   ),
//                   Align(
//                     alignment: Alignment.topCenter,
//                     child: AlertLENDEXCustom().buttonSkip(),
//                   ),
//                   Column(
//                     children: [
//                       SizedBox(height: 151.h),
//                       Row(
//                         mainAxisAlignment: MainAxisAlignment.center,
//                         children: map<Widget>(imgList, (index, url) {
//                           return Container(
//                             alignment: Alignment.topCenter,
//                             height: 17.sp,
//                             width: 17.sp,
//                             margin: EdgeInsets.symmetric(horizontal: 12.sp),
//                             decoration: BoxDecoration(
//                                 shape: BoxShape.circle,
//                                 color: _current == index
//                                     ? LikeWalletAppTheme.black
//                                     : LikeWalletAppTheme.gray.withOpacity(0.5)),
//                           );
//                         }),
//                       ),
//                       SizedBox(height: 57.h),
//                     ],
//                   ),
//                   // Expanded(
//                   //   child: Container(),
//                   // child:
//                   Align(
//                     alignment: Alignment.bottomCenter,
//                     child: CarouselSlider(
//                       options: CarouselOptions(
//                           height: 1350.0.h,
//                           autoPlay: false,
//                           viewportFraction: 1,
//                           aspectRatio: 1,
//                           enlargeCenterPage: true,
//                           // enlargeStrategy: CenterPageEnlargeStrategy.height,
//                           disableCenter: true,
//                           onPageChanged: (index, reason) {
//                             setState(() {
//                               _current = index;
//                             });
//                           }),
//                       items: imgList
//                           .map(
//                             (item) => Container(
//                               child: Stack(
//                                   alignment: Alignment.topCenter,
//                                   children: <Widget>[
//                                     Column(
//                                       children: [
//                                         Text(
//                                           changeTitle(_current),
//                                           style: TextStyle(
//                                             fontFamily: 'Prompt',
//                                             fontSize: 42.h,
//                                             color: const Color(0xff000000),
//                                             letterSpacing: 0.126,
//                                             fontWeight: FontWeight.w600,
//                                             shadows: [
//                                               Shadow(
//                                                 color: const Color(0x29000000),
//                                                 offset: Offset(0, 3.h),
//                                                 blurRadius: 4.h,
//                                               )
//                                             ],
//                                           ),
//                                         ),
//                                         SizedBox(height: 41.h),
//                                         Container(
//                                           alignment: Alignment.topCenter,
//                                           height: 200.h,
//                                           width: 740.w,
//                                           child: Text(
//                                             changeDetail(_current),
//                                             style: TextStyle(
//                                               fontFamily: 'Prompt',
//                                               fontSize: 39.h,
//                                               color: const Color(0x99000000),
//                                               letterSpacing: 0.117,
//                                             ),
//                                             textAlign: TextAlign.center,
//                                           ),
//                                         ),
//                                         SizedBox(height: 135.h),
//                                         SizedBox(
//                                           width: 640.w,
//                                           child: Text(
//                                             changeDetail2(_current),
//                                             style: TextStyle(
//                                               fontFamily: 'Prompt',
//                                               fontSize: 48.h,
//                                               color: const Color(0xfffdebc1),
//                                               letterSpacing: 0.2800000000000001,
//                                             ),
//                                             textAlign: TextAlign.center,
//                                           ),
//                                         ),
//                                         SizedBox(height: 39.h),
//                                         _current == 1
//                                             ? SizedBox(
//                                                 width: 640.w,
//                                                 child: Text(
//                                                   changeDetail3(_current),
//                                                   style: TextStyle(
//                                                     fontFamily: 'Prompt',
//                                                     fontSize: 48.h,
//                                                     color:
//                                                         const Color(0xfffdebc1),
//                                                     letterSpacing:
//                                                         0.2800000000000001,
//                                                   ),
//                                                   textAlign: TextAlign.center,
//                                                 ),
//                                               )
//                                             : Container(),
//                                         Expanded(child: Container()),
//                                         CachedNetworkImage(
//                                           imageUrl: item,
//                                           fit: BoxFit.fill,
//                                           height: 551.39.h,
//                                           width: 713.96.w,
//                                           placeholder: (context, url) =>
//                                               SpinKitFadingCircle(
//                                                   color:
//                                                       LikeWalletAppTheme.bule1,
//                                                   size: 100.h),
//                                           errorWidget: (context, url, error) =>
//                                               Icon(Icons.error),
//                                         ),
//                                       ],
//                                     ),
//                                   ]),
//                             ),
//                           )
//                           .toList(),
//                     ),
//                   ),
//                   // ),
//                 ],
//               ),
//             ),
//           ],
//         )
//       ],
//     );
//   }
//
//   List<T> map<T>(List list, Function handler) {
//     List<T> result = [];
//     for (var i = 0; i < list.length; i++) {
//       result.add(handler(i, list[i]));
//     }
//     return result;
//   }
// }
