import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

abstract class ICheckAlertLENDEX {
  Future<bool> callEventLoan({required String uid});
}

class CheckAlertLENDEX implements ICheckAlertLENDEX {
  final FirebaseAuth firebaseAuth = FirebaseAuth.instance;
  bool result = false;
  @override
  Future<bool> callEventLoan({required String uid}) async {
    try {
      await FirebaseFirestore.instance
          .collection('event')
          .doc('loan')
          .collection('checkEvent')
          .where('status', isEqualTo: 'true')
          .get()
          .then((value) async {
        if (value.docs.isNotEmpty) {
          await FirebaseFirestore.instance
              .collection('event')
              .doc('loan')
              .collection('checkEvent')
              .where('uid', arrayContains: uid)
              .get()
              .then((value) async {
            if (value.docs.length == 0) {
              await FirebaseFirestore.instance
                  .collection('event')
                  .doc('loan')
                  .collection('checkEvent')
                  .doc("1")
                  .update({
                "uid": FieldValue.arrayUnion([uid]),
              });
              return result = true;
            } else {
              return result = false;
            }
          });
        } else {
          return result = false;
        }
      });
    } catch (e) {
      print(e);
    }
    return result;
  }
}
