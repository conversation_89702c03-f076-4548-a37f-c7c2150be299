import 'package:flutter/material.dart';

import 'package:likewallet/menu/LockLIKE.dart';

class LikeWalletImage {
  LikeWalletImage._();

  static const String iconHome = 'assets/image/home.png';
  static const String iconBuzzer = 'assets/image/buzzer.png';
  static const String iconRefer = 'assets/image/home/<USER>';
  static const String iconFeedback = 'assets/image/feedback.png';
  static const String iconHistory = 'assets/image/history.png';
  static const String image_quick_pay = 'assets/image/home/<USER>';
  static const String icon_quick_pay = 'assets/image/home/<USER>';
  static const String icon_lock_to_win = 'assets/image/home/<USER>';
  static const String image_quick_pay_scan =
      'assets/image/home/<USER>';
  static const String image_quick_pay_store = 'assets/image/home/<USER>';
  static const String icon_lockLike = 'assets/image/LockEran.png';
  static const String icon_hourly = 'assets/image/HourlyRewards.png';
  static const String icon_playEarn = 'assets/image/PlayEran.png';
  static const String icon_rewards = 'assets/image/save-money.png';
  static const String icon_covid19 = 'assets/image/watch_covid.png';
  static const String icon_feedback = 'assets/image/Share_receipt.png';
  static const String icon_menu = 'assets/image/menu.png';
  static const String icon_scan_barcode = 'assets/image/qr-code.png';
  static const String icon_favorite_black = 'assets/image/favorite_black.png';
  static const String icon_favorite_white = 'assets/image/favorite_white.png';
  static const String icon_down_arrow = 'assets/image/arrow_down.png';

  static const String icon_user = 'assets/image/favorite_photo.png';
  static const String icon_add = 'assets/image/add_favorites.png';
  static const String icon_delete = 'assets/image/delete_favorites.png';
  static const String icon_edit_photo = 'assets/image/edit_favorite_photo.png';
  static const String icon_LENDEX = 'assets/image/LENDEX.png';
  static const String icon_line = 'assets/image/LINE-Logo64.png';
  static const String icon_facebook = 'assets/image/FB-Logo64.png';
  static const String icon_wechat = 'assets/image/wechat64.png';
  static const String icon_whatsapp = 'assets/image/whatsapp.png';
  static const String icon_twitter = 'assets/image/twitter64.png';
  static const String icon_telegram = 'assets/image/icons8-telegram-app64.png';
  static const String icon_youtube = 'assets/image/youtube64.png';
  static const String icon_Hourlye = 'assets/image/HourlyRewards.png';

  static const String icon_NextDis = 'assets/image/NextDis.png';
  static const String cover = 'assets/image/cover.png';
  static const String defaultProfile = 'assets/image/defaultProfile.png';
  static const String edit_photo = 'assets/image/edit_photo.png';
  static const String ads = 'assets/image/ads/ADS.png';
  static const String wacth_now = 'assets/image/ads/watchnow.png';

  static const String watch_bg = 'assets/image/ads/bg_watch_ads.png';
  static const String watch_bottom = 'assets/image/ads/Watch&Earn Bottom.png';
  static const String watch_logo = 'assets/image/ads/watch_logo.png';
  static const String watch_photo1 = 'assets/image/ads/1.png';
  static const String watch_photo2 = 'assets/image/ads/2.png';
  static const String watch_photo3 = 'assets/image/ads/3.png';

  static const String like_point = 'assets/image/logo.png';
  static const String like_point_text = 'assets/image/likewallet_text.png';
  static const String Paste = 'assets/image/Paste.png';
  static const String icon_scan_barcode2 = 'assets/image/QR-code2.png';
  static const String icon_transfer = 'assets/image/InternetBanking.png';

  static const String icon_refer = 'assets/image/refer/referIcon.png';

  static const String icon_plus = 'assets/image/history/plus.svg';
  static const String icon_minus = 'assets/image/history/minus.svg';

  static const String likewallet_text =
      'assets/image/choice_user/likeWallet_text.png';

  static const String contact_us_logo =
      'assets/image/contact_us/contact_us_logo.png';

  static const String contact_us_bg =
      'assets/image/contact_us/contact_us_bg.png';

  static const String contact_us_bg2 =
      'assets/image/contact_us/contact_us_bg2.png';

  static const String contact_us_chat = 'assets/image/contact_us/chat.png';
  static const String contact_us_telegram =
      'assets/image/contact_us/telegram.png';
  static const String contact_us_photo = 'assets/image/contact_us/getPhoto.png';
  static const String contact_us_takePhoto =
      'assets/image/contact_us/takePhoto.png';
  static const String contact_us_success =
      'assets/image/contact_us/success.png';

  static const String icon_smartphone = 'assets/image/login/smartphone.png';
  static const String icon_email = 'assets/image/login/email.png';
  static const String login_bg = 'assets/image/login/login_bg.png';
  static const String main_shoplike = 'assets/image/ShopLike.png';
  static const String background = 'assets/image/back.png';

  static const String icon_banking = 'assets/image/home/<USER>';
  static const String icon_banking_gray = 'assets/image/icon_banking_gray.png';

  static const String icon_locklike_image =
      'https://s3-ap-southeast-1.amazonaws.com/storage-pkg/icon/icon1606728231locklike_image.svg';

  static const String icon_likepoint =
      'assets/image/locklike/icon_likepoint.png';

  static const String logo_likepoint = 'assets/image/likepoint.png';

  static const String locklike_lock_unlock =
      'assets/image/locklike/controller_lock&unlock.png';
  static const String locklike_icon_lock = 'assets/image/locklike/lock.png';
  static const String locklike_icon_unlock = 'assets/image/locklike/unlock.png';
  static const String locklike_BG_slider =
      'assets/image/locklike/LOCK_LIKE_BOTTOM_BG.png';
  static const String icon_button_next = 'assets/image/locklike/next.png';
  static const String icon_button_cancel = 'assets/image/locklike/cancel.png';
  static const String icon_success_white = 'assets/image/locklike/success.png';
  static const String locklike_button_select =
      'assets/image/locklike/button_select.png';

  static const String icon_button_cancel_white =
      'assets/image/banking/buylike/button_cancel.png';
  static const String icon_button_next_black =
      'assets/image/banking/buylike/button_next.png';
  static const String qr_likepoint = 'assets/image/receive/qr_likewallet.png';
  static const String icon_copy_address =
      'assets/image/receive/copy_address.png';

  static const String icon_us = 'assets/image/banking/cash/us.png';
  static const String icon_laos = 'assets/image/banking/cash/laos.png';
  static const String icon_th = 'assets/image/banking/cash/th.png';

  static const String bg_take_photo =
      'assets/image/take_photo/bg_take_photo.png';
  static const String bg_take_photo2 =
      'assets/image/take_photo/bg_take_photo2.png';
  static const String button_take_photo =
      'assets/image/take_photo/button_take_photo.png';
  static const String border_camera =
      'assets/image/take_photo/border_camera.png';
  static const String focus_photo = 'assets/image/take_photo/focus_photo.png';
  static const String icon_promptpay =
      'assets/image/banking/cash/promptpay.png';

  static const String icon_truemoney =
      'assets/image/banking/cash/truemoney.png';

  static const String plus = 'assets/image/banking/cash/plus.png';
  static const String arrow_blue_dropdown =
      'assets/image/banking/cash/arrow_dropdown.png';

  static const String bg_menu = 'assets/image/banking/send/bg_menu.png';
  static const String icon_back = 'assets/image/banking/send/icon_back.png';
  static const String icon_equal = 'assets/image/banking/send/icon_equal.png';

  static const String icon_success = 'assets/image/banking/buylike/success.png';

  static const String icon_close_system =
      'assets/image/closed_system/ClosedSystem.png';

  static const String button_plus = 'assets/image/history/button_plus.svg';
  static const String button_minus = 'assets/image/history/button_minus.svg';

  static const String contact_us_line = 'assets/image/contact_us/line.png';
  static const String icon_more = 'assets/image/banking/icon_more.svg';
  static const String icon_dropdown = 'assets/image/banking/send/dropdown.svg';
  static const String icon_clear = 'assets/image/clear.png';

  static const String icon_notification =
      'assets/image/notification/notify.svg';
  static const String icon_news = 'assets/image/notification/news.svg';
  static const String arroy = "assets/image/spendlike/arroy.png";
  static const String cu = "assets/image/spendlike/cu.png";
  static const String cocobee = "assets/image/spendlike/cocobee.png";
  static const String spend_bg = "assets/image/spendlike/spend_bg.png";
  static const String icon_button_back_white =
      'assets/image/banking/send/backto.png';

  static const String icon_search = 'assets/image/store/icon_search.png';
  static const String icon_qrcode = 'assets/image/banking/buylike/qrcode.png';
  static const String icon_bank = 'assets/image/banking/buylike/bank.png';
  static const String icon_buylike = 'assets/image/banking/buylike/buylike.png';
  static const String logo_change_photo =
      'assets/image/change_phone/logo_change_photo.png';
  static const String logo_change_photo2 =
      'assets/image/change_phone/logo_change_photo2.png';

  static const String icon_setting =
      'assets/image/change_phone/icon_setting.png';
  static const String icon_check_photo_success =
      'assets/image/change_phone/icon_success.png';
  static const String icon_back_button = 'assets/image/backButton.png';
  static const String icon_take_photo =
      'assets/image/change_phone/takephoto.png';
  static const String icon_fingerprint =
      'assets/image/pin_code/fingerprint.png';
  static const String refer_head_animation =
      'assets/image/refer/refer_head_animation.png';
  static const String icon_refer_Qrcode = 'assets/image/refer/icon_refer.png';
  static const String feedback_animation =
      'assets/image/feedback/feedback_animation.gif';
  static const String home_bg = 'assets/image/home/<USER>';
  static const String icon_watch_ads = 'assets/image/home/<USER>';
  static const String icon_ldx = 'assets/image/home/<USER>';
  static const String icon_locklike = 'assets/image/home/<USER>';
  static const String icon_reward = 'assets/image/home/<USER>';
  static const String banking_fade = 'assets/image/home/<USER>';
  static const String button_back = 'assets/image/home/<USER>';

  static const String info_total = 'assets/image/info/total.png';
  static const String info_today = 'assets/image/info/today.png';
  static const String info_week = 'assets/image/info/week.png';
  static const String info_month = 'assets/image/info/month.png';

  static const String network_error = 'assets/image/network/network_error.png';

  static const String notify_close = 'assets/image/notification/close.png';
  static const String reward_gif = 'https://bit.ly/3aDJAJz';
  static const String icon_time = 'assets/image/reward/icon_time.png';
  static const String icon_Income = 'assets/image/home/<USER>';
  static const String icon_save_alt =
      'assets/image/banking/buylike/save_alt.png';
  static const String icon_loading = 'assets/image/reward/loading.png';
  static const String model_alert_update =
      'assets/image/alert_update/alert_update.png';
  static const String icon_lendex = 'assets/image/home/<USER>';
  static const String bg_lendex = 'assets/image/lendex/bg.png';
  static const String icon_lendex_home = 'assets/image/lendex/home.png';
  static const String icon_lendex_close = 'assets/image/lendex/close.png';
  static const String bg_home = 'assets/image/lendex/home.png';
  static const String button_key = 'assets/image/lendex/button_key.png';
  static const String button_point3 = 'assets/image/lendex/point3.png';
  static const String button_banking = 'assets/image/banking/banking.png';
  static const String icon_buzz_notify = 'assets/image/home/<USER>';
  static const String icon_title_name = 'assets/image/home/<USER>';
  static const String iconLikeWallet =
      'assets/image/banking/iconLikeWallet.png';
  static const String kycEX = 'assets/image/kyc/kycEX.png';
  static const String contactList = 'assets/image/contact-list.png';
  static const String iconPOI = 'assets/image/home/<USER>';
  static const String icon_back_poi = 'assets/image/poi_back.png';
  static const String locked_Likepoint_Rewards = 'assets/image/Locked_Likepoint_Rewards.svg';
  static const String locked_Likepoint_Rewards_home = 'assets/image/Locked_Likepoint_Rewards.png';
  static const String icon_select_poi = 'assets/image/icon_select_poi.svg';
  static const String icon_roll_back = 'assets/image/roll_back.svg';
  static const String coin_like = 'assets/image/coin_like.svg';
  static const String poi_line = 'assets/image/poi_line.svg';
  static const String dot_3 = 'assets/image/3_dot.svg';
  static const String circle_check = 'assets/image/circle_check.svg';
  static const String poi_detail_back = 'assets/image/poi_detail_back.svg';
  static const String close_bg_close = 'assets/image/close_bg_close.svg';
}
