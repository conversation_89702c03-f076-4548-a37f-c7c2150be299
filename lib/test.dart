import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/bank/banking.dart';
import 'package:likewallet/bank/contact/main.dart';
import 'package:likewallet/chat_in_app/index_chat.dart';
import 'package:likewallet/menu/reward/rewards_screen.dart';
import 'package:likewallet/middleware/callStoreDownload/callStoreDownload.dart';
import 'package:likewallet/middleware/check_auth.dart';
import 'package:likewallet/quickpay/stores.dart';
import 'package:likewallet/routes.dart';
import 'package:likewallet/screen/home.dart';
import 'package:likewallet/screen/index.dart';
import 'package:likewallet/splash_screen.dart';
import 'package:likewallet/support/chat.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'Theme.dart';

class DeepLink extends StatefulWidget {
  DeepLink({this.uri});
  final Uri? uri;

  @override
  _DeepLink createState() => _DeepLink();
}

class _DeepLink extends State<DeepLink> {
  late CheckAuth checkAuth;
  late StoreDownload storeDownload;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    checkAuth = CurCheckAuth();
    storeDownload = CallStoreDownload();
    deepLinkWay();
  }

  Future<bool> getStateLogin() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    return pref.getBool('login') ?? false;
  }

  void deepLinkWay() async {
    SchedulerBinding.instance!.addPostFrameCallback((timeStamp) async {
      if (widget.uri != null) {
        print(widget.uri);
        if (widget.uri.toString() ==
            'https://likewallet.io/contact/?openExternalBrowser=1') {
          print('การทำงานนี้');
          /// ย้ายไปไลน์ โอ๋เอ๋
          // Navigator.pushReplacement(
          //     context, MaterialPageRoute(builder: (context) => IndexChatPage()));
        } else if (widget.uri.toString() ==
            'https://likewallet.io/cashout/?openExternalBrowser=1') {
          Navigator.pushReplacement(
              context, MaterialPageRoute(builder: (context) => Banking()));
        } else if (widget.uri.toString() ==
            'https://likewallet.io/shop/?openExternalBrowser=1') {
          Navigator.pushReplacement(
              context, MaterialPageRoute(builder: (context) => stores()));
        } else if (widget.uri.toString() ==
            'https://likewallet.io/reward/?openExternalBrowser=1') {
          Navigator.pushReplacement(
              context, MaterialPageRoute(builder: (context) => Rewards()));
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
        debugShowCheckedModeBanner: false, home: Scaffold(body: Container()));
  }
}

class SecondPage extends StatefulWidget {
  SecondPage(this.uri);
  final Uri? uri;

  @override
  _SecondPage createState() => _SecondPage(uri);
}

class _SecondPage extends State<SecondPage> {
  final Uri? uri;
  _SecondPage(this.uri);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Text(
              "Deep link",
              style: TextStyle(fontSize: 22),
            ),
          ],
        ),
      ),
    );
  }
}
