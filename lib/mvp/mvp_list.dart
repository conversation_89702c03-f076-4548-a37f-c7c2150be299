import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dio/dio.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:likewallet/Theme.dart';
import 'package:http/http.dart' as http;
import 'package:likewallet/libraryman/auth.dart';
import 'package:likewallet/app_config.dart';
import 'package:likewallet/dapp/listdapp.dart';
import 'package:likewallet/libraryman/configuration_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

class MvpListPage extends StatefulWidget {
  MvpListPage({this.page});
  final String? page;
  _MvpListPage createState() => new _MvpListPage();
}

class _MvpListPage extends State<MvpListPage> {
  final fireStore = FirebaseFirestore.instance;
  late BaseAuth auth;
  Dio dio = new Dio();
  late IConfigurationService configETH;

  String permission = '';
  bool loading = false;

  @override
  void initState() {
    super.initState();
    auth = new Auth();
    checkPermission();
  }

  final List<String> items = new List<String>.generate(200, (i) => "Item $i");

  void checkPermission() async {
    setState(() => loading = true);

    try {
      SharedPreferences pref = await SharedPreferences.getInstance();
      configETH = new ConfigurationService(pref);
      String address = configETH.getAddress();
      var url = Uri.https(env.apiUrl, '/getTierBUfromRDS');
      final response = await http.post(url, body: {
        "address": address,
      });
      print(response.statusCode);
      final body = json.decode(response.body);
      if (response.statusCode == 200) {
        if (body['status'] == 200) {
          setState(() {
            permission = 'organization';
          });
          setState(() => loading = false);
        } else {
          setState(() {
            permission = 'member';
          });
          setState(() => loading = false);
        }
      }
    } catch (e) {
      setState(() => loading = false);
    }
  }

  Widget build(BuildContext context) {
    return new Scaffold(
      appBar: AppBar(
        backgroundColor: Color(0xff141322),
        title: new Text(
          'MVP LIST',
          style: TextStyle(color: Colors.white),
        ),
      ),
      body: loading
          ? Center(
              child: SpinKitFadingCircle(
              color: LikeWalletAppTheme.bule1,
              size: 70,
            ))
          : StreamBuilder(
              stream: fireStore
                  .collection('mvpList')
                  .where('permission', arrayContains: permission)
                  .snapshots(),
              builder: (BuildContext context,
                  AsyncSnapshot<QuerySnapshot<Map<String, dynamic>>> snapshot) {
                if (!snapshot.hasData) return new Text('');

                return new ListView(
                    children: snapshot.data!.docs.map((document) {
                  return Container(
                    margin: EdgeInsets.all(10),
                    height: 50.0,
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(18.0),
                          side: BorderSide(color: Colors.black),
                        ),
                        padding: EdgeInsets.all(10.0),
                        primary: Colors.white,
                        onPrimary: Colors.black,
                      ),
                      onPressed: () {
                        print('lottotesting');
                        auth.getTokenFirebase().then((token) async {
                          print(token);
                          Response response = await dio.post(
                            "https://" + env.apiUrl + "/createCustomToken",
                            data: {"token": token},
                          );
                          print(response);
                          if (response.data["statusCode"] == 200) {
                            print(response.data["token"]);
                            var urlWeb = document.data()['url'] +
                                response.data["token"] +
                                document.data()['path'];
                            print(urlWeb);
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => WebOpenURL(url: urlWeb),
                              ),
                            );
                          }
                        });
                      },
                      child: Text(document.data()['title']),
                    ),
                  );
                }).toList());
              }),
    );
  }
}
