import 'dart:async';
import 'package:flutter/material.dart';
import 'package:likewallet/bloc/bloc.dart';


class BlocAddress implements BlocBase {
  late String addr;
  StreamController<String> addressStream = StreamController<String>();

  BlocAddress() {
    addr = '';
  }

  @override
  void dispose() {
    addressStream.close();
  }
  setAddress(String value) {
    addr = value;
    addressStream.sink.add(addr);
  }
}