import 'dart:async';
import 'package:flutter/material.dart';
import 'package:likewallet/bloc/bloc.dart';


class BlocAmount implements BlocBase {
  late double amount;
  StreamController<double> amountStream = StreamController<double>();

  BlocAmount() {
    amount = 0;
  }

  @override
  void dispose() {
    amountStream.close();
  }

  setAmount(double value){
    amount = value;
    amountStream.sink.add(amount);
  }
}