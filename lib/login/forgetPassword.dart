import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:likewallet/Theme.dart';

import 'package:likewallet/login/confirmOTP.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/screen_util.dart';

import 'package:http/http.dart' as http;
import 'package:likewallet/app_config.dart';
import 'dart:convert';
import 'dart:async';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class forgetPassword extends StatefulWidget {
  _forgetPassword createState() => new _forgetPassword();
  static const routeName = '/test';
}

class _forgetPassword extends State<forgetPassword> {
  late String dropdownValue;

  TextEditingController email = new TextEditingController();
  bool _saving = false;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  void showColoredToast(msg, color) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_LONG,
        backgroundColor: color,
        textColor: Colors.white);
  }

  Future<dynamic> requestReset() async {
    print(email.text);
    try {
      var url = Uri.https(env.apiUrl, '/requestResetPassword');
      var response =
          await http.post(url, body: {'email': email.text.toString()});
      return response.body;
    } catch (e) {
      print(e);
    }
  }

  @override
  Widget build(BuildContext context) {
    return new MaterialApp(
      debugShowCheckedModeBanner: false,
      home: new DefaultTabController(
        length: 1,
        child: ModalProgressHUD(
          opacity: 0.1,
          inAsyncCall: _saving,
          progressIndicator: CustomLoading(),
          child: new Scaffold(
            resizeToAvoidBottomInset: false,
            // resizeToAvoidBottomPadding: false,
            appBar: new AppBar(
              backgroundColor: Color(0xff141322),
              centerTitle: true,
              title: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  SizedBox(height: MediaQuery.of(context).size.height * 0.01),
                  new Text(
                    AppLocalizations.of(context)!.translate('forget_title'),
//                  '',
                    style: TextStyle(
                      fontSize: MediaQuery.of(context).size.height *
                          Screen_util("height", 40),
                      color: Color(0xff707071).withOpacity(0.9),
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                    ),
                  ),
                ],
              ),
              bottom: new PreferredSize(
                preferredSize: new Size(MediaQuery.of(context).size.width,
                    MediaQuery.of(context).size.height * 0.1),
                child: new Container(
                  child: new TabBar(
                    labelColor: Color(0xff0FE8D8),
                    unselectedLabelColor: Color(0xff707071),
                    indicatorColor: Color(0xff0FE8D8),
                    tabs: <Widget>[
//                    Container(
//                      padding: EdgeInsets.only(
//                          top: MediaQuery.of(context).size.height * 0.02,
//                          bottom: MediaQuery.of(context).size.height * 0.02),
//                      child: Row(
//                        mainAxisAlignment: MainAxisAlignment.center,
//                        children: <Widget>[
//                          Icon(Icons.mobile_screen_share),
//                          Text(
//                            ' ' +
//                                AppLocalizations.of(context)
//                                    .translate('forget_tab_mobile'),
//                            style: TextStyle(
//                              fontSize: MediaQuery.of(context).size.height *
//                                  Screen_util("height", 40),
//                              fontFamily: AppLocalizations.of(context)
//                                  .translate('font1'),
//                            ),
//                          )
//                        ],
//                      ),
//                    ),
                      Container(
                        padding: EdgeInsets.only(
                            top: MediaQuery.of(context).size.height * 0.02,
                            bottom: MediaQuery.of(context).size.height * 0.02),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: <Widget>[
                            Icon(Icons.email),
                            Text(
                              ' ' +
                                  AppLocalizations.of(context)!
                                      .translate('forget_tab_email'),
                              style: TextStyle(
                                  fontSize: MediaQuery.of(context).size.height *
                                      Screen_util("height", 40),
                                  fontFamily: AppLocalizations.of(context)!
                                      .translate('font1')),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            body: new TabBarView(
              children: <Widget>[
//              Mobile(context),
                Email(context)
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget Mobile(context) {
    return new Container(
      color: Color(0xff141322),
      child: new Column(
        children: <Widget>[
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.05,
          ),
          Container(
            alignment: Alignment.center,
            height: MediaQuery.of(context).size.height * 0.07,
            width: MediaQuery.of(context).size.width * 0.9,
            decoration: BoxDecoration(
              color: Color(0xff141322),
              border: Border.all(
                color: Color(0xff0FE8D8),
                width: 0.5,
              ),
              borderRadius: BorderRadius.all(Radius.circular(5.0)),
            ),
            child: Row(
              children: <Widget>[
                new Container(
                  alignment: Alignment.center,
                  height: MediaQuery.of(context).size.height * 0.07,
                  width: MediaQuery.of(context).size.width * 0.2,
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<String>(
                      value: dropdownValue,
                      elevation: 15,
                      style: TextStyle(color: Color(0xffADACA0)),
                      iconEnabledColor: Colors.white,
                      iconDisabledColor: Colors.white,
                      onChanged: (String? newValue) {
                        setState(() {
                          dropdownValue = newValue.toString();
                        });
                      },
                      items: <String>['+66', '+88']
                          .map<DropdownMenuItem<String>>((String value) {
                        return DropdownMenuItem<String>(
                          value: value,
                          child: Text(value),
                        );
                      }).toList(),
                    ),
                  ),
                  decoration: BoxDecoration(
                      border: Border(
                    right: BorderSide(
                      //                   <--- left side
                      color: Color(0xff0FE8D8),
                      width: 0.3,
                    ),
                  )),
                ),
                new Container(
                  alignment: Alignment.center,
                  height: MediaQuery.of(context).size.height * 0.07,
                  width: MediaQuery.of(context).size.width * 0.65,
//                color: Colors.blue,
                  child: TextField(
                    style: TextStyle(color: Colors.white),
                    decoration: InputDecoration(
                      contentPadding: EdgeInsets.only(left: 10),
                      hintText: AppLocalizations.of(context)!
                          .translate('forget_mobile'),
                      hintStyle: TextStyle(
                        fontSize: MediaQuery.of(context).size.height *
                            Screen_util("height", 40),
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                        color: Colors.white,
                        fontWeight: FontWeight.normal,
                      ),
                      border: InputBorder.none,
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
              ],
            ),
          ),
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.1,
          ),
          new Container(
            alignment: Alignment.center,
            height: MediaQuery.of(context).size.height * 0.06,
            width: MediaQuery.of(context).size.width * 0.7,
            child: ButtonTheme(
              minWidth: MediaQuery.of(context).size.width * 0.86112,
              height: MediaQuery.of(context).size.height * 0.05658024691,
              child: TextButton(
                style: ButtonStyle(
                  shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                    RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                  ),
                  backgroundColor: MaterialStateProperty.resolveWith<Color>(
                        (Set<MaterialState> states) {
                      if (states.contains(MaterialState.disabled)) {
                        return Color(0xff00F1E0); // Disabled color
                      }
                      return Color(0xff00F1E0); // Regular color
                    },
                  ),
                ),
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => OTP_PAGE(
                        checkIf: 'reset',
                      ),
                    ),
                  );
                },
                child: Text(
                  AppLocalizations.of(context)!.translate('forget_send_button'),
                  style: TextStyle(
                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
                    color: Colors.black,
                    fontSize: MediaQuery.of(context).size.height * Screen_util("height", 40),
                    fontWeight: FontWeight.normal,
                  ),
                ),
              ),
            ),
          ),
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.02,
          ),
          new Container(
            decoration: BoxDecoration(
              color: Color(0xff141322),
              border: Border.all(
                color: Color(0xff0FE8D8),
                width: 0.3,
              ),
              borderRadius: BorderRadius.all(Radius.circular(5.0)),
            ),
            alignment: Alignment.center,
            height: MediaQuery.of(context).size.height * 0.06,
            width: MediaQuery.of(context).size.width * 0.7,
            child: ButtonTheme(
              minWidth: MediaQuery.of(context).size.width * 0.86112,
              height: MediaQuery.of(context).size.height * 0.05658024691,
              child: TextButton(
                style: ButtonStyle(
                  shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                    RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                  ),
                  backgroundColor: MaterialStateProperty.resolveWith<Color>(
                        (Set<MaterialState> states) {
                      if (states.contains(MaterialState.disabled)) {
                        return Color(0xff000000); // Disabled color
                      }
                      return Color(0xff000000); // Regular color
                    },
                  ),
                ),
                onPressed: () {
                  Navigator.pop(context);
                },
                child: Text(
                  AppLocalizations.of(context)!.translate('forget_cancel_button'),
                  style: TextStyle(
                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
                    color: Color(0xff0FE8D8),
                    fontSize: MediaQuery.of(context).size.height * Screen_util("height", 40),
                    fontWeight: FontWeight.normal,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget Email(context) {
    return new Container(
      color: Color(0xff141322),
      child: new Column(
        children: <Widget>[
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.05,
          ),
          new Container(
            decoration: BoxDecoration(
              color: Color(0xff141322),
              border: Border.all(
                color: Color(0xff0FE8D8),
                width: 0.5,
              ),
              borderRadius: BorderRadius.all(Radius.circular(5.0)),
            ),
            alignment: Alignment.center,
            height: MediaQuery.of(context).size.height * 0.07,
            width: MediaQuery.of(context).size.width * 0.8,
//                color: Colors.blue,
            child: TextField(
              controller: email,
              style: TextStyle(color: Colors.white),
              decoration: InputDecoration(
                contentPadding: EdgeInsets.only(left: 10),
                hintText:
                    AppLocalizations.of(context)!.translate('forget_email'),
                hintStyle: TextStyle(
                  fontSize: MediaQuery.of(context).size.height *
                      Screen_util("height", 40),
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  color: Colors.white70,
                ),
                border: InputBorder.none,
              ),
              keyboardType: TextInputType.text,
            ),
          ),
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.1,
          ),
          new Container(
            width: 750.w,
            height: 132.h,
            child: new TextButton(
                style: ButtonStyle(
                  backgroundColor: MaterialStateProperty.all<Color>(
                    Color(0xff00F1E0),
                  ),
                  shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                      RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10.0),
                  )),
                ),
                onPressed: () {
                  // setState(() {
                  //   _saving = true;
                  // });
                  requestReset().then((value) {
                    print(value);
                    var body = json.decode(value);
                    print(body["statusCode"]);
                    if (body["statusCode"] == 200) {
                      setState(() {
                        _saving = false;
                      });
                      showColoredToast(
                          AppLocalizations.of(context)!
                              .translate('send_email_reset'),
                          Colors.green);
                    } else {
                      setState(() {
                        _saving = false;
                      });
                      showColoredToast(
                          AppLocalizations.of(context)!
                              .translate('wrong_email'),
                          Colors.red);
                    }
                  });
                },
                child: new Text(
                  AppLocalizations.of(context)!.translate('forget_send_button'),
                  style: TextStyle(
                      color: Colors.black,
                      fontSize: MediaQuery.of(context).size.height *
                          Screen_util("height", 40),
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      fontWeight: FontWeight.normal),
                )),
          ),
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.02,
          ),
          new Container(
            decoration: BoxDecoration(
              color: Color(0xff141322),
              border: Border.all(
                color: Color(0xff0FE8D8),
                width: 0.5,
              ),
              borderRadius: BorderRadius.all(Radius.circular(5.0)),
            ),
            alignment: Alignment.center,
            height: MediaQuery.of(context).size.height * 0.06,
            width: MediaQuery.of(context).size.width * 0.7,
            child: ButtonTheme(
              minWidth: MediaQuery.of(context).size.width * 0.86112,
              height: MediaQuery.of(context).size.height * 0.05658024691,
              child: TextButton(
                style: ButtonStyle(
                  shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                    RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                  ),
                  backgroundColor: MaterialStateProperty.resolveWith<Color>(
                        (Set<MaterialState> states) {
                      if (states.contains(MaterialState.disabled)) {
                        return Color(0xff000000); // Disabled color
                      }
                      return Color(0xff000000); // Regular color
                    },
                  ),
                ),
                onPressed: () {
                  Navigator.pop(context);
                },
                child: Text(
                  AppLocalizations.of(context)!.translate('forget_cancel_button'),
                  style: TextStyle(
                    color: Color(0xff0FE8D8),
                    fontSize: MediaQuery.of(context).size.height * Screen_util("height", 40),
                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
                    fontWeight: FontWeight.normal,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
