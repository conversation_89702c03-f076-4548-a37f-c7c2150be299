//import 'package:flutter/cupertino.dart';
//import 'package:flutter/material.dart';
//
//import 'package:flutter/services.dart';
//
//import 'package:likewallet/Theme.dart';
//import 'package:likewallet/animationPage.dart';
//import 'package:likewallet/screen_util.dart';
//
//import 'package:likewallet/animationPage.dart';
//import 'package:likewallet/login/email_singin.dart';
//
//import 'package:shared_preferences/shared_preferences.dart';
//import 'package:likewallet/app_config.dart';
//import 'package:firebase_auth/firebase_auth.dart';
//import 'package:likewallet/libraryman/auth.dart';
//import 'package:http/http.dart' as http;
//import 'package:likewallet/login/confirmOTP.dart';
//import 'dart:convert';
//import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
//import 'package:likewallet/libraryman/custom_loading.dart';
//import 'package:fluttertoast/fluttertoast.dart';
//import 'package:likewallet/libraryman/app_local.dart';
//import 'package:likewallet/screen/registerForm.dart';
//import 'package:likewallet/libraryman/keyboard_done_widget.dart';
//import 'package:keyboard_visibility/keyboard_visibility.dart';
//import 'dart:io' show Platform;
//
//class SING_IN extends StatefulWidget {
//  SING_IN({this.auth, this.onSignedIn});
//  final BaseAuth auth;
//  final VoidCallback onSignedIn;
//  _SING_IN createState() => new _SING_IN();
//}
//
//setToken(String _token) async {
//  SharedPreferences pref = await SharedPreferences.getInstance();
//  pref.setString("_token", _token);
//  return true;
//}
//
//enum FormType { login, register }
//
//
//enum providerSMS { Firebase, Twilio, Nexmo ,Email }
//
//class _SING_IN extends State<SING_IN> {
//  final formKey = new GlobalKey<FormState>();
//  bool _saving = false;
//  TextEditingController _smsCodeController = TextEditingController();
//  TextEditingController _phoneNumberController = TextEditingController();
//
//  BaseAuth auth;
//  String verificationId;
//  String _email;
//  String _password;
//  FormType _formType = FormType.login;
//  int numberPhone = 0;
//  String dropdownValue = '+66';
//  FirebaseAuth _auth = FirebaseAuth.instance;
//  String phoneNumber;
//  providerSMS roundSMS = providerSMS.Firebase;
//
//  bool _activeID = false;
//  bool _activeTouchID = false;
//  SharedPreferences sharedPreferences;
//
//  FocusNode doneFocusNode = new FocusNode();
//  OverlayEntry overlayEntry;
//
//  //done button zone
//  showOverlay(BuildContext context) {
//    if (overlayEntry != null) return;
//    OverlayState overlayState = Overlay.of(context);
//    overlayEntry = OverlayEntry(builder: (context) {
//      return Positioned(
//          bottom: MediaQuery.of(context).viewInsets.bottom,
//          right: 0.0,
//          left: 0.0,
//          child: InputDoneView());
//    });
//
//    overlayState.insert(overlayEntry);
//  }
//
//  removeOverlay() {
//    if (overlayEntry != null) {
//      overlayEntry.remove();
//      overlayEntry = null;
//    }
//  }
//
//  @override
//  void dispose() {
//    doneFocusNode.dispose();
//    super.dispose();
//  }
//
//  @override
//  void initState() {
//    // TODO: implement initState
//    super.initState();
//
//    auth = Auth();
//    typeLogin();
//    TextEditingController controller = TextEditingController();
//
//    if (Platform.isIOS) {
//      doneFocusNode.addListener(() {
//        bool hasFocus = doneFocusNode.hasFocus;
//        if (hasFocus)
//          showOverlay(context);
//        else
//          removeOverlay();
//      });
//
//      KeyboardVisibilityNotification().addNewListener(onHide: () {
//        removeOverlay();
//      });
//    }
//  }
//
//  Future<bool> checkExistUser() async {
//    if (_phoneNumberController.text.substring(0,1) == '0') {
//      phoneNumber = dropdownValue +
//          _phoneNumberController.text
//              .substring(1, _phoneNumberController.text.length)
//              .toString();
//      return await auth.checkExistUser(phoneNumber);
//    } else {
//      showShortToast(AppLocalizations.of(context)!.translate('prefixzero'));
//    }
//
//  }
//  Future<List<String>> checkOldUser() async {
//    if (_phoneNumberController.text.substring(0,1) == '0') {
//      phoneNumber = dropdownValue +
//          _phoneNumberController.text
//              .substring(1, _phoneNumberController.text.length)
//              .toString();
//      return await auth.checkOldUserName(phoneNumber);
//    } else {
//      showShortToast(AppLocalizations.of(context)!.translate('prefixzero'));
//      return ['false','false'];
//    }
//
//  }
//  void showColoredToastColor(msg, color) {
//    Fluttertoast.showToast(
//        msg: msg,
//        toastLength: Toast.LENGTH_LONG,
//        backgroundColor: color,
//        textColor: Colors.white);
//  }
//  void showColoredToast(msg) {
//    Fluttertoast.showToast(
//        msg: msg,
//        toastLength: Toast.LENGTH_LONG,
//        backgroundColor: Colors.red,
//        textColor: Colors.white);
//  }
//
//  void showShortToast(msg) {
//    Fluttertoast.showToast(
//        msg: msg,
//        toastLength: Toast.LENGTH_SHORT,
//        backgroundColor: Colors.cyan,
//        textColor: Colors.white);
//  }
//
//  typeLogin() async {
//    sharedPreferences = await SharedPreferences.getInstance();
//    setState(() {
//      _activeID = sharedPreferences.getBool("activeID");
//      _activeTouchID = sharedPreferences.getBool("activeTouchID");
//      if (_activeID != null || _activeTouchID != null) {
//        if (_activeID) {
//          _activeID = sharedPreferences.getBool("activeID");
//        }
//        if (_activeTouchID) {
//          _activeTouchID = sharedPreferences.getBool("activeTouchID");
//        }
//      } else {
//        _activeID = false;
//        _activeTouchID = false;
//      }
//      print(_activeID);
//      print(_activeTouchID);
//    });
//  }
//
//  gridviewForPhone() {
//    return GestureDetector(
//        onTap: () {
//          FocusScopeNode currentFocus = FocusScope.of(context);
//          if (!currentFocus.hasPrimaryFocus) {
//            currentFocus.unfocus();
//          }
//        },
//        child: Stack(children: <Widget>[
//          background_image(context),
//          Positioned(
//              top: mediaQuery(context, 'height', 136),
//              left: mediaQuery(context, 'width', 35),
//              child: backButton(context, LikeWalletAppTheme.gray)),
//          Positioned(
//            top: MediaQuery.of(context).size.height * 0.09487179487,
//            child: Container(
//              width: MediaQuery.of(context).size.width,
//              alignment: Alignment.center,
//              child: _TEXT1_Phone(context),
//            ),
//          ),
//          Positioned(
//            top: MediaQuery.of(context).size.height * 0.16923076923,
//            child: Container(
//              width: MediaQuery.of(context).size.width,
//              alignment: Alignment.center,
//              child: _InputPhone_Phone(context),
//            ),
//          ),
//          Positioned(
//            top: MediaQuery.of(context).size.height * 0.27777350427,
//            child: Container(
//              width: MediaQuery.of(context).size.width,
//              alignment: Alignment.center,
//              child: _ButtonContinue_Phone(context),
//            ),
//          ),
//
////username password
////          if (_activeID == true)
//            Positioned(
//              top: MediaQuery.of(context).size.height * 0.44957264957,
//              child: Container(
//                width: MediaQuery.of(context).size.width,
//                alignment: Alignment.center,
//                child: _emailSingIN_Phone(context),
//              ),
//            ),
//
//          if (_activeTouchID == true)
//            Positioned(
//              top: MediaQuery.of(context).size.height * 0.64957264957,
//              child: Container(
//                width: MediaQuery.of(context).size.width,
//                alignment: Alignment.center,
//                child: _touchID_Phone(context),
//              ),
//            ),
//        ]));
//  }
//
//  @override
//  Widget build(BuildContext context) {
//    // TODO: implement build
//    final double shortestSide = MediaQuery.of(context).size.shortestSide;
//    final bool useMobileLayout = shortestSide < 600.0;
//    final Orientation orientation = MediaQuery.of(context).orientation;
//    return Scaffold(
//        resizeToAvoidBottomInset: false,
//        resizeToAvoidBottomPadding: false,
//        body: ModalProgressHUD(
//          child: gridviewForPhone(),
//          inAsyncCall: _saving,
//          progressIndicator: CustomLoading(),
//        ));
//  }
//
//  Widget _TEXT1_Phone(context) {
//    return new Container(
//      alignment: Alignment.center,
//      child: new Text(
//        AppLocalizations.of(context)!.translate('singin_title_mobile'),
//        textAlign: TextAlign.center,
//        style: TextStyle(
//            color: LikeWalletAppTheme.gray1,
//            fontFamily: AppLocalizations.of(context)!.translate('font1'),
//            fontWeight: FontWeight.normal,
//            fontSize: mediaQuery(context, 'height', 40)),
//      ),
//    );
//  }
//
//  Widget _InputPhone_Phone(context) {
//    return new Container(
//      alignment: Alignment.center,
//      height: mediaQuery(context, 'height', 156),
//      width: mediaQuery(context, 'width', 930),
//      decoration: BoxDecoration(
//        color: LikeWalletAppTheme.bule2,
//        border: Border.all(
//          color: LikeWalletAppTheme.bule1,
//          width: mediaQuery(context, 'height', 0.3),
//        ),
//        borderRadius: BorderRadius.all(Radius.circular(5.0)),
//      ),
//      child: Row(
//        children: <Widget>[
//          new Container(
//            width: mediaQuery(context, 'width', 220),
//            alignment: Alignment.center,
//            child: DropdownButtonHideUnderline(
//              child: new Theme(
//                data: Theme.of(context)
//                    .copyWith(canvasColor: LikeWalletAppTheme.bule2),
//                child: DropdownButton<String>(
//                  focusColor: LikeWalletAppTheme.bule1,
//                  iconSize: mediaQuery(context, 'height', 45),
//                  value: dropdownValue,
//                  elevation: 15,
//                  style: TextStyle(
//                    color: LikeWalletAppTheme.white,
//                    fontSize: mediaQuery(context, 'height', 45),
//                  ),
////                  iconEnabledColor: Color(0xffFFFFFF),
////                  iconDisabledColor: Color(0xffFFFFFF),
//                  onChanged: (String newValue) {
//                    setState(() {
//                      dropdownValue = newValue;
//                    });
//                  },
//                  items: <String>['+66', '+856', '+855']
//                      .map<DropdownMenuItem<String>>((String value) {
//                    return DropdownMenuItem<String>(
//                      value: value,
//                      child: Text(
//                        value,
//                        style: TextStyle(
//                          color: LikeWalletAppTheme.white,
//                          fontSize: mediaQuery(context, 'height', 45),
//                        ),
//                      ),
//                    );
//                  }).toList(),
//                ),
//              ),
//            ),
//            decoration: BoxDecoration(
//                border: Border(
//              right: BorderSide(
//                //                   <--- left side
//                color: LikeWalletAppTheme.bule1,
//                width: mediaQuery(context, 'height', 0.3),
//              ),
//            )),
//          ),
//          new Container(
//            width: mediaQuery(context, 'width', 700),
//            alignment: Alignment.center,
//            child: TextField(
//              controller: _phoneNumberController,
//              style: TextStyle(
//                color: LikeWalletAppTheme.white,
//                fontFamily: AppLocalizations.of(context)!.translate('font1'),
//                fontWeight: FontWeight.normal,
//                fontSize: mediaQuery(context, 'height', 45),
//              ),
//              inputFormatters: [
//                LengthLimitingTextInputFormatter(10),
//              ],
//              decoration: InputDecoration(
////                focusedBorder: OutlineInputBorder(
////                    borderSide: BorderSide(color: LikeWalletAppTheme.bule1)),
//                contentPadding: EdgeInsets.only(
//                  left: MediaQuery.of(context).size.width * 0.03,
//                ),
//                hintText: AppLocalizations.of(context)
//                    .translate('singin_input_mobile'),
//                hintStyle: TextStyle(
//                  color: LikeWalletAppTheme.white,
//                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
//                  fontWeight: FontWeight.normal,
//                  fontSize: mediaQuery(context, 'height', 45),
//                ),
//                border: InputBorder.none,
//              ),
//              keyboardType: TextInputType.number,
//              focusNode: doneFocusNode,
//            ),
//          ),
//        ],
//      ),
//    );
//  }
//
//  Widget _ButtonContinue_Phone(context) {
//    return Row(
//      mainAxisAlignment: MainAxisAlignment.center,
//      crossAxisAlignment: CrossAxisAlignment.center,
//      children: <Widget>[
//        new Container(
//          alignment: Alignment.center,
//          height: mediaQuery(context, 'height', 132),
//          width: mediaQuery(context, 'width', 445),
//          child: ButtonTheme(
//            minWidth: mediaQuery(context, 'width', 445),
//            height: mediaQuery(context, 'height', 132),
//            child: new FlatButton(
//                shape: new RoundedRectangleBorder(
//                  borderRadius: new BorderRadius.circular(8.0),
//                ),
//                disabledColor: LikeWalletAppTheme.bule1,
//                color: LikeWalletAppTheme.bule1,
//                onPressed: () {
//                  checkExistUser().then((data) {
//                    if (data) {
//                      _sendCodeToPhoneNumber();
//                    } else {
//                      checkOldUser().then((statusCode) {
//                        print(statusCode);
//                        if(statusCode[0] == '200'){
//                          showColoredToastColor(AppLocalizations.of(context)
//                              .translate('please_fill_more')
//                              .toString(),
//                              Colors.cyanAccent
//                          );
//                          Navigator.push(
//                            context,
//                            MaterialPageRoute(
//                                builder: (context) => REGISTER_FORM(
//                                  checkIf: 'nouser',
//                                  phone_number: _phoneNumberController.text,
//                                  prefix: dropdownValue,
//                                  oldFirstName: statusCode[1],
//                                  oldLastName: statusCode[2],
//                                )),
//                          );
//                        }else{
//                          showColoredToastColor(AppLocalizations.of(context)
//                              .translate('noUser')
//                              .toString(),
//                              Colors.red
//                          );
//                          Navigator.push(
//                            context,
//                            MaterialPageRoute(
//                                builder: (context) => REGISTER_FORM(
//                                  checkIf: 'nouser',
//                                  phone_number: _phoneNumberController.text,
//                                  prefix: dropdownValue,
//                                )),
//                          );
//                        }
//                      });
//
//
//                    }
//                  });
//                },
//                child: new Text(
//                  AppLocalizations.of(context)
//                      .translate('singin_button_mobile'),
//                  style: TextStyle(
//                      fontFamily:
//                          AppLocalizations.of(context)!.translate('font1'),
//                      color: Colors.black,
//                      fontSize: mediaQuery(context, 'width', 45),
//                      fontWeight: FontWeight.bold),
//                )),
//          ),
//        ),
//      ],
//    );
//  }
//
//  Widget _emailSingIN_Phone(context) {
//    return new Column(
//      mainAxisAlignment: MainAxisAlignment.center,
//      crossAxisAlignment: CrossAxisAlignment.center,
//      children: <Widget>[
//        new Container(
//          alignment: Alignment.center,
////          height: MediaQuery.of(context).size.height * 0.04,
//          width: MediaQuery.of(context).size.width * 0.9,
//          child: new Text(
//            AppLocalizations.of(context)!.translate('singin_title_email'),
//            textAlign: TextAlign.center,
//            style: TextStyle(
//              color: Color(0xff707071),
//              fontFamily: AppLocalizations.of(context)!.translate('font1'),
//              fontWeight: FontWeight.normal,
//              fontSize: mediaQuery(context, 'height', 45),
//            ),
//          ),
////        color: Colors.green,
//        ),
////          color: Colors.black,
//        new SizedBox(
//          height: MediaQuery.of(context).size.height * 0.01,
//        ),
//        new Container(
//            alignment: Alignment.center,
//            height: MediaQuery.of(context).size.height * 0.05,
//            width: MediaQuery.of(context).size.width * 0.9,
//            child: FlatButton(
//              child: new Image.asset(
//                'assets/image/key.png',
//                height: MediaQuery.of(context).size.height * 0.03343589743,
//              ),
//              onPressed: () => {
//                Navigator.push(
//                    context,
//                    EnterExitRoute(
//                        exitPage: SING_IN(), enterPage: EMAIL_SINGIN()))
//              },
//            )
////          color: Colors.black,
//            ),
//      ],
//    );
//  }
//
//  Widget _touchID_Phone(context) {
//    return new Column(
//      mainAxisAlignment: MainAxisAlignment.center,
//      crossAxisAlignment: CrossAxisAlignment.center,
//      children: <Widget>[
//        new Container(
//          alignment: Alignment.center,
//          height: MediaQuery.of(context).size.height * 0.04,
//          width: MediaQuery.of(context).size.width * 0.9,
//          child: new Text(
//            AppLocalizations.of(context)!.translate('singin_title_touchid'),
//            style: TextStyle(
//                color: Color(0xff707071),
//                fontFamily: AppLocalizations.of(context)!.translate('font1'),
//                fontWeight: FontWeight.normal,
//                fontSize: MediaQuery.of(context).size.height * 0.01623931623),
//          ),
////        color: Colors.green,
//        ),
//        new SizedBox(
//          height: MediaQuery.of(context).size.height * 0.01,
//        ),
//        new Container(
//            alignment: Alignment.center,
//            height: MediaQuery.of(context).size.height * 0.05,
//            width: MediaQuery.of(context).size.width * 0.9,
//            child: FlatButton(
//              child: new Image.asset(
//                'assets/image/thumbprint.png',
//                height: MediaQuery.of(context).size.height * 0.03259401709,
//              ),
//              onPressed: () => {Navigator.pushNamed(context, '/touchID')},
//            )
//
////          color: Colors.black,
//            ),
//      ],
//    );
//  }
//
//  /**
//   *
//   * phone_number
//   * */
//  /// Sign in using an sms code as input.
//  void _signInWithPhoneNumber(String smsCode) async {
//    final AuthCredential credential = PhoneAuthProvider.getCredential(
//      verificationId: verificationId,
//      smsCode: smsCode,
//    );
//    final AuthResult user = await _auth.signInWithCredential(credential);
//    print(_auth.currentUser());
//    user.user.getIdToken(refresh: true).then((_token) {
//      setToken(_token.toString());
//      print(_token);
//      Navigator.pushNamed(context, '/sing_in');
//    });
//  }
//
//  /// Sends the code to the specified phone number.
//  Future<void> _sendCodeToPhoneNumber() async {
//    if (_phoneNumberController.text.substring(0, 1) == '0') {
//
//      phoneNumber = dropdownValue +
//          _phoneNumberController.text
//              .substring(1, _phoneNumberController.text.length)
//              .toString();
//      print(phoneNumber);
//      if (roundSMS == providerSMS.Firebase) {
//        setState(() {
//          _saving = true;
//        });
//        final PhoneVerificationCompleted verificationCompleted =
//            (AuthCredential phoneAuthCredential) {
//          print('verified');
//          setState(() {
//            _saving = false;
//            roundSMS = providerSMS.Twilio;
//          });
//        };
//
//        final PhoneVerificationFailed verificationFailed =
//            (AuthException authException) {
//          setState(() {
//            _saving = false;
//          });
//          roundSMS = providerSMS.Twilio;
//          _sendCodeToPhoneNumber();
//          setState(() {
//            print(
//                'Phone number verification failed. Code: ${authException.code}. Message: ${authException.message}');
//          });
//        };
//
//        final PhoneCodeSent codeSent =
//            (String verificationId, [int forceResendingToken]) async {
//          this.verificationId = verificationId;
//          print("code sent to " + phoneNumber);
//
//          setState(() {
//            _saving = false;
//          });
//          Navigator.push(
//              context,
//              EnterExitRoute(
//                  exitPage: SING_IN(),
//                  enterPage: OTP_PAGE(
//                    auth: _auth,
//                    verificationId: verificationId,
//                    roundSMS: roundSMS,
//                    refCode: 'no',
//                    phoneNumber: phoneNumber,
//                    prefixNumber: dropdownValue,
//                  )));
//        };
//
//        final PhoneCodeAutoRetrievalTimeout codeAutoRetrievalTimeout =
//            (String verificationId) {
//          this.verificationId = verificationId;
//          print("time out");
//          setState(() {
//            _saving = false;
//          });
//          roundSMS = providerSMS.Twilio;
//        };
//
//        await FirebaseAuth.instance.verifyPhoneNumber(
//            phoneNumber: phoneNumber,
//            timeout: const Duration(seconds: 10),
//            verificationCompleted: verificationCompleted,
//            verificationFailed: verificationFailed,
//            codeSent: codeSent,
//            codeAutoRetrievalTimeout: codeAutoRetrievalTimeout);
//        //ส่งด้วย twilio
//      } else if (roundSMS == providerSMS.Twilio) {
//        setState(() {
//          _saving = true;
//        });
//
//        String urlCheck = env.apiUrl + '/checkEmail';
//
//        var checkEmail = await http.post(urlCheck, body: {'phone_number': phoneNumber});
//
//        var bodyCheck = json.decode(checkEmail.body);
//
//        if(bodyCheck['statusCode'] == 200) {
//          String url = env.apiUrl + '/authNoFirebase';
////      if (phoneNumber.length > 12) {
////        phoneNumber = phoneNumber.substring(0, phoneNumber.length - 1);
////      }
//          var response = await http.post(
//              url, body: {'phone_number': phoneNumber});
//          print('Response status: ${response.statusCode}');
//          print('Response body: ${response.body}');
//          var body = json.decode(response.body);
//
//          if (body['statusCode'] == 200) {
//            //success register and sign in
//            //to confirm
//            setState(() {
//              _saving = false;
//            });
//            print(phoneNumber.length);
//            Navigator.push(
//              context,
//              EnterExitRoute(
//                  exitPage: SING_IN(),
//                  enterPage: OTP_PAGE(
//                    roundSMS: roundSMS,
//                    phoneNumber: phoneNumber,
//                    refCode: 'no',
//                    prefixNumber: dropdownValue,
//                  )),
//            );
//          } else {
//            setState(() {
//              _saving = false;
//            });
//            //register failed
//          }
//        }else{
//          if(!mounted) return;
//          setState(() {
//            _saving = false;
//          });
//          showShortToast(AppLocalizations.of(context)!.translate('login_with_email_instead'));
//
//          Future.delayed(Duration(milliseconds: 2000)).then((value){
//            Navigator.push(context,
//                MaterialPageRoute(
//                    builder: (context) => EMAIL_SINGIN()
//                )
//            );
//          });
//
//        }
//      }
//    } else {
//      showShortToast(AppLocalizations.of(context)!.translate('prefixzero'));
//    }
//
//  }
//}