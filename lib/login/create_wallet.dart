import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/device_utils.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/login/confirmOTPCreate.dart';
import 'package:likewallet/login/sing_in.dart';
import 'package:likewallet/screen_util.dart';

import 'package:http/http.dart' as http;
import 'package:likewallet/app_config.dart';
import 'dart:convert';
import 'dart:async';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:flutter/services.dart';
import 'package:likewallet/animationPage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:likewallet/screen/registerForm.dart';
import 'package:likewallet/libraryman/keyboard_done_widget.dart';
import 'dart:io' show Platform;

class CreateWallet extends StatefulWidget {
  _CreateWallet createState() => new _CreateWallet();
  static const routeName = '/test';
}

setToken(String _token) async {
  SharedPreferences pref = await SharedPreferences.getInstance();
  pref.setString("_token", _token);
  return true;
}

enum FormType { login, register }

class _CreateWallet extends State<CreateWallet>
    with SingleTickerProviderStateMixin {
  int selected = 0;
  TextEditingController email = new TextEditingController();
  TextEditingController password = TextEditingController();
  bool _saving = false;

  final formKey = new GlobalKey<FormState>();
  TextEditingController _smsCodeController = TextEditingController();
  TextEditingController _phoneNumberController = TextEditingController();

  late BaseAuth auth;
  String verificationId = '';
  late String _email;
  late String _password;
  int numberPhone = 0;
  String dropdownValue = '+66';
  FirebaseAuth _auth = FirebaseAuth.instance;
  late String phoneNumber;
  providerSMS roundSMS = providerSMS.Twilio;
  late TabController _tabController;
  bool _activeID = false;
  bool _activeTouchID = false;
  late SharedPreferences sharedPreferences;
  KeyboardVisibilityController keyboardVisibilityController = KeyboardVisibilityController();
  FocusNode doneFocusNode = new FocusNode();
  late OverlayEntry? overlayEntry;

  //done button zone
  showOverlay(BuildContext context) {
    if (overlayEntry != null) return;
    OverlayState? overlayState = Overlay.of(context);
    overlayEntry = OverlayEntry(builder: (context) {
      return Positioned(
          bottom: MediaQuery.of(context).viewInsets.bottom,
          right: 0.0,
          left: 0.0,
          child: InputDoneView());
    });

    overlayState!.insert(overlayEntry!);
  }

  removeOverlay() {
    if (overlayEntry != null) {
      overlayEntry!.remove();
      overlayEntry = null;
    }
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
    _tabController.dispose();
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _tabController = new TabController(vsync: this, length: 1);
    auth = Auth();
    typeLogin();
    TextEditingController controller = TextEditingController();

    if (Platform.isIOS) {
      doneFocusNode.addListener(() {
        bool hasFocus = doneFocusNode.hasFocus;
        if (hasFocus)
          showOverlay(context);
        else
          removeOverlay();
      });

      keyboardVisibilityController.onChange.listen((bool visible) {
        removeOverlay();
      });
    }
  }

  Future<bool> checkExistUser() async {
    if (_phoneNumberController.text.substring(0, 1) == '0') {
      phoneNumber = dropdownValue +
          _phoneNumberController.text
              .substring(1, _phoneNumberController.text.length)
              .toString();
      return await auth.checkExistUser(phoneNumber);
    } else {
      setState(() {
        _saving = false;
      });
      showShortToast(AppLocalizations.of(context)!.translate('prefixzero'));
    }
    return Future.value();
  }

  void showColoredToastColor(msg, color) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_LONG,
        backgroundColor: color,
        textColor: Colors.white);
  }

  void showColoredToast(msg) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_LONG,
        backgroundColor: Colors.red,
        textColor: Colors.white);
  }

  void showShortToast(msg) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_SHORT,
        backgroundColor: Colors.cyan,
        textColor: Colors.white);
  }

  showShortToastColor(msg, color) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_SHORT,
        backgroundColor: color,
        textColor: Colors.white);
  }

  Future<List<String>> checkOldUser() async {
    if (_phoneNumberController.text.substring(0, 1) == '0') {
      phoneNumber = dropdownValue +
          _phoneNumberController.text
              .substring(1, _phoneNumberController.text.length)
              .toString();
      return await auth.checkOldUserName(phoneNumber);
    } else {
      showShortToast(AppLocalizations.of(context)!.translate('prefixzero'));
      return ['false', 'false'];
    }
  }

  typeLogin() async {
    sharedPreferences = await SharedPreferences.getInstance();
    setState(() {
      _activeID = sharedPreferences.getBool("activeID") ?? false;
      _activeTouchID = sharedPreferences.getBool("activeTouchID") ?? false;
      if (_activeID != null || _activeTouchID != null) {
        if (_activeID) {
          _activeID = sharedPreferences.getBool("activeID") ?? false;
        }
        if (_activeTouchID) {
          _activeTouchID = sharedPreferences.getBool("activeTouchID") ?? false;
        }
      } else {
        _activeID = false;
        _activeTouchID = false;
      }
      print(_activeID);
      print(_activeTouchID);
    });
  }

  Future<dynamic> requestReset() async {
    print(email.text);
    var url = Uri.https(env.apiUrl, '/requestResetPassword');
    var response = await http.post(url, body: {'email': email.text.toString()});

    return response.body;
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      home: new DefaultTabController(
        initialIndex: 0,
        length: 1,
        child: ModalProgressHUD(
          opacity: 0.1,
          inAsyncCall: _saving,
          progressIndicator: CustomLoading(),
          child: GestureDetector(
            onTap: () {
              FocusScopeNode currentFocus = FocusScope.of(context);
              if (!currentFocus.hasPrimaryFocus) {
                currentFocus.unfocus();
              }
            },
            child: new Scaffold(
                resizeToAvoidBottomInset: false,
                // resizeToAvoidBottomPadding: false,
                appBar: new AppBar(
                  backgroundColor: LikeWalletAppTheme.bule2,
                  centerTitle: true,
                  title: Stack(
                    alignment: Alignment.center,
                    children: <Widget>[
                      backButton(context, LikeWalletAppTheme.gray),
                      Container(
                        margin: EdgeInsets.only(
                            top: mediaQuery(context, 'height', 80)),
                        child: new Text(
                          AppLocalizations.of(context)!
                              .translate('login_create'),
                          style: TextStyle(
                            fontSize: MediaQuery.of(context).size.height *
                                Screen_util("height", 45),
                            color: Color(0xff707071).withOpacity(1),
                            fontFamily: AppLocalizations.of(context)!
                                .translate('font1'),
                          ),
                        ),
                      ),
                    ],
                  ),
                  bottom: new PreferredSize(
                    preferredSize: new Size(MediaQuery.of(context).size.width,
                        MediaQuery.of(context).size.height * 0.1),
                    child: new Container(
                      child: new TabBar(
                        controller: _tabController,
                        labelColor: LikeWalletAppTheme.bule1,
                        unselectedLabelColor:
                            LikeWalletAppTheme.white.withOpacity(0.7),
                        indicatorColor: LikeWalletAppTheme.bule1,
                        onTap: (value) {
                          setState(() {
                            selected = value;
                          });
                          print(selected);
                        },
                        tabs: <Widget>[
                          Container(
                            padding: EdgeInsets.only(
                                top: MediaQuery.of(context).size.height * 0.02,
                                bottom:
                                    MediaQuery.of(context).size.height * 0.02),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: <Widget>[
                                Image.asset(
                                  LikeWalletImage.icon_smartphone,
                                  height: mediaQuery(context, 'height', 94.97),
                                  color: selected == 0
                                      ? LikeWalletAppTheme.bule1
                                      : LikeWalletAppTheme.white
                                          .withOpacity(0.7),
                                ),
                                SizedBox(
                                  width: mediaQuery(context, 'width', 30),
                                ),
                                Text(
                                  AppLocalizations.of(context)!
                                      .translate('forget_tab_mobile'),
                                  style: TextStyle(
                                    fontWeight: FontWeight.w100,
//                                color: LikeWalletAppTheme.bule1,
                                    fontSize:
                                        MediaQuery.of(context).size.height *
                                            Screen_util("height", 45),
                                    fontFamily: AppLocalizations.of(context)!
                                        .translate('font1'),
                                  ),
                                )
                              ],
                            ),
                          ),
//                           Container(
//                             padding: EdgeInsets.only(
//                                 top: MediaQuery.of(context).size.height * 0.02,
//                                 bottom:
//                                     MediaQuery.of(context).size.height * 0.02),
//                             child: Row(
//                               mainAxisAlignment: MainAxisAlignment.center,
//                               children: <Widget>[
//                                 Image.asset(
//                                   LikeWalletImage.icon_email,
//                                   height: mediaQuery(context, 'height', 50.39),
//                                   color: selected == 1
//                                       ? LikeWalletAppTheme.bule1
//                                       : LikeWalletAppTheme.white
//                                           .withOpacity(0.7),
//                                 ),
//                                 SizedBox(
//                                   width: mediaQuery(context, 'width', 30),
//                                 ),
//                                 Text(
//                                   AppLocalizations.of(context)
//                                       .translate('forget_tab_email'),
//                                   style: TextStyle(
//                                     fontWeight: FontWeight.w100,
// //                                color: LikeWalletAppTheme.bule1,
//                                     fontSize:
//                                         MediaQuery.of(context).size.height *
//                                             Screen_util("height", 45),
//                                     fontFamily: AppLocalizations.of(context)
//                                         .translate('font1'),
//                                   ),
//                                 )
// //                            Icon(Icons.email),
// //                            Text(
// //                              ' ' +
// //                                  AppLocalizations.of(context)
// //                                      .translate('forget_tab_email'),
// //                              style: TextStyle(
// //                                  fontSize: MediaQuery.of(context).size.height *
// //                                      Screen_util("height", 40),
// //                                  fontFamily: AppLocalizations.of(context)
// //                                      .translate('font1')),
// //                            ),
//                               ],
//                             ),
//                           ),
                        ],
                      ),
                    ),
                  ),
                ),
                body: Stack(
                  children: <Widget>[
                    Container(
                      decoration: BoxDecoration(
                          image: DecorationImage(
                        image: AssetImage(LikeWalletImage.background),
                        fit: BoxFit.cover,
                      )),
                    ),
                    new TabBarView(
                      controller: _tabController,
                      children: <Widget>[
                        Mobile(context)
                        // , Email(context),
                      ],
                    ),
                  ],
                )),
          ),
        ),
      ),
    );
  }

  Widget Mobile(context) {
    return new Container(
      color: Colors.transparent,
      child: new Column(
        children: <Widget>[
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.05,
          ),
          _inputPhone(),
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.1,
          ),
          _buttonLogin()
//          SizedBox(
//            height: MediaQuery.of(context).size.height * 0.02,
//          ),
//          new Container(
//            decoration: BoxDecoration(
//              color: Color(0xff141322),
//              border: Border.all(
//                color: Color(0xff0FE8D8),
//                width: mediaQuery(context, 'width', 0.3),
//              ),
//              borderRadius: BorderRadius.all(Radius.circular(5.0)),
//            ),
//            alignment: Alignment.center,
//            height: MediaQuery.of(context).size.height * 0.06,
//            width: MediaQuery.of(context).size.width * 0.7,
//            child: ButtonTheme(
//              minWidth: MediaQuery.of(context).size.width * 0.86112,
//              height: MediaQuery.of(context).size.height * 0.05658024691,
//              child: new FlatButton(
//                  shape: new RoundedRectangleBorder(
//                    borderRadius: new BorderRadius.circular(8.0),
//                  ),
//                  disabledColor: Color(0xff000000),
//                  color: Color(0xff000000),
//                  onPressed: () => {Navigator.pop(context)},
//                  child: new Text(
//                    AppLocalizations.of(context)
//                        .translate('forget_cancel_button'),
//                    style: TextStyle(
//                        fontFamily:
//                            AppLocalizations.of(context)!.translate('font1'),
//                        color: Color(0xff0FE8D8),
//                        fontSize: MediaQuery.of(context).size.height *
//                            Screen_util("height", 40),
//                        fontWeight: FontWeight.normal),
//                  )),
//            ),
//          ),
        ],
      ),
    );
  }

  Widget _inputPhone() {
    return Container(
      alignment: Alignment.center,
      height: MediaQuery.of(context).size.height * 0.07,
      width: MediaQuery.of(context).size.width * 0.9,
      decoration: BoxDecoration(
        color: Color(0xff141322),
        border: Border.all(
            color: Color(0xff0FE8D8), width: mediaQuery(context, 'width', 0.4)),
        borderRadius: BorderRadius.all(Radius.circular(5.0)),
      ),
      child: Row(
        children: <Widget>[
          new Container(
            alignment: Alignment.center,
            height: MediaQuery.of(context).size.height * 0.07,
            width: MediaQuery.of(context).size.width * 0.2,
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                value: dropdownValue,
                elevation: 15,
                style: TextStyle(color: Color(0xffADACA0)),
                iconEnabledColor: Colors.white,
                iconDisabledColor: Colors.white,
                onChanged: (String? newValue) {
                  setState(() {
                    dropdownValue = newValue.toString();
                  });
                },
                items: <String>['+66', '+856', '+855']
                    .map<DropdownMenuItem<String>>((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(value),
                  );
                }).toList(),
              ),
            ),
            decoration: BoxDecoration(
                border: Border(
              right: BorderSide(
                  //                   <--- left side
                  color: Color(0xff0FE8D8),
                  width: mediaQuery(context, 'width', 0.4)),
            )),
          ),
          new Container(
            alignment: Alignment.center,
            height: MediaQuery.of(context).size.height * 0.07,
            width: MediaQuery.of(context).size.width * 0.65,
//                color: Colors.blue,
            child: TextField(
              controller: _phoneNumberController,
              style: TextStyle(color: Colors.white),
              decoration: InputDecoration(
                isDense: true,
                contentPadding: EdgeInsets.only(left: 20),
                hintText:
                    AppLocalizations.of(context)!.translate('forget_mobile'),
                hintStyle: TextStyle(
                  fontSize: MediaQuery.of(context).size.height *
                      Screen_util("height", 46),
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  color: LikeWalletAppTheme.white.withOpacity(0.7),
                  fontWeight: FontWeight.w100,
                ),
                border: InputBorder.none,
              ),
              keyboardType: TextInputType.number,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buttonLogin() {
    return new Container(
      alignment: Alignment.center,
      height: mediaQuery(context, 'height', 132),
      width: mediaQuery(context, 'width', 720),
      child: ButtonTheme(
        minWidth: MediaQuery.of(context).size.width * 0.86112,
        height: MediaQuery.of(context).size.height * 0.05658024691,
        child: TextButton(
          style: ButtonStyle(
            shape: MaterialStateProperty.all<RoundedRectangleBorder>(
              RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.0),
              ),
            ),
            backgroundColor: MaterialStateProperty.resolveWith<Color>(
                  (Set<MaterialState> states) {
                if (states.contains(MaterialState.disabled)) {
                  return Color(0xff00F1E0); // Disabled color
                }
                return Color(0xff00F1E0); // Regular color
              },
            ),
          ),
          onPressed: () {
            setState(() {
              _saving = true;
            });
            DeviceUtils.hideKeyboard(context);
            checkExistUser().then((data) {
              if (data) {
                //ส่ง OTP
                setState(() {
                  _saving = false;
                });
                showColoredToastColor(
                    AppLocalizations.of(context)!
                        .translate('phone_used')
                        .toString(),
                    Colors.red);
              } else {
                //ถ้าไม่มี user ให้ส่งไปหน้าสมัครแทน และไปดึงข้อมูลเก่าจาก likepoint
                checkOldUser().then((statusCode) {
                  print(statusCode);
                  if (statusCode[0] == '200') {
                    showColoredToastColor(
                        AppLocalizations.of(context)!
                            .translate('please_fill_more')
                            .toString(),
                        Colors.red);

                    Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) => REGISTER_FORM(
                            checkIf: 'nouser',
                            phone_number: _phoneNumberController.text,
                            prefix: dropdownValue,
                            oldFirstName: statusCode[1],
                            oldLastName: statusCode[2],
                          )),
                    );
                  } else {
                    // if (phoneNumber.substring(0, 3) == "+66") {
                    //   _sendCodeToPhoneNumberCreateWithMKT();
                    // } else {
                    _sendCodeToPhoneNumberCreate();
                    // }

                    // Navigator.push(
                    //   context,
                    //   MaterialPageRoute(
                    //       builder: (context) => REGISTER_FORM(
                    //             checkIf: 'nouser',
                    //             phone_number: _phoneNumberController.text,
                    //             prefix: dropdownValue,
                    //           )),
                    // );
                  }
                });
              }
            });
          },
          child: Text(
            AppLocalizations.of(context)!.translate('login_login'),
            style: TextStyle(
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              color: Colors.black,
              fontSize: MediaQuery.of(context).size.height * Screen_util("height", 42),
              fontWeight: FontWeight.w100,
            ),
          ),
        ),
      ),
    );
  }

  /// Sends the code to the specified phone number.
//   Future<void> _sendCodeToPhoneNumber() async {
//     if (_phoneNumberController.text.substring(0, 1) == '0') {
//       phoneNumber = dropdownValue +
//           _phoneNumberController.text
//               .substring(1, _phoneNumberController.text.length)
//               .toString();
//       // print(phoneNumber);
// //       if (roundSMS == providerSMS.Firebase) {
// //         setState(() {
// //           _saving = true;
// //         });
// //         final PhoneVerificationCompleted verificationCompleted =
// //             (AuthCredential phoneAuthCredential) {
// //           print('verified');
// //           _auth.signInWithCredential(phoneAuthCredential).then((value) {
// //             Navigator.push(
// //                 context,
// //                 EnterExitRoute(
// //                     exitPage: SING_IN(),
// //                     enterPage: SetPin(
// //                         refCode: 'no',
// //                         firstName: '',
// //                         lastName: '',
// //                         checkIf: '',
// //                         secret: "LikeWallet",
// //                         roundSMS: roundSMS,
// //                         codeVerify: '',
// //                         phoneNumber: phoneNumber,
// //                         pinAgain: false)));
// //           });
// //
// //           setState(() {
// //             _saving = false;
// //             // roundSMS = providerSMS.Twilio;
// //           });
// //         };
// //
// //         final PhoneVerificationFailed verificationFailed =
// //             (FirebaseAuthException authException) {
// //           setState(() {
// //             _saving = false;
// //           });
// //           roundSMS = providerSMS.Twilio;
// //           _sendCodeToPhoneNumber();
// //           setState(() {
// //             print(
// //                 'Phone number verification failed. Code: ${authException.code}. Message: ${authException.message}');
// //           });
// //         };
// //
// //         final PhoneCodeSent codeSent =
// //             (String verificationId, [int forceResendingToken]) async {
// //           this.verificationId = verificationId;
// //           print("code sent to " + phoneNumber);
// //
// //           setState(() {
// //             _saving = false;
// //           });
// //           Navigator.push(
// //               context,
// //               EnterExitRoute(
// //                   exitPage: SING_IN(),
// //                   enterPage: OTP_PAGE(
// //                     auth: _auth,
// //                     verificationId: verificationId,
// //                     roundSMS: roundSMS,
// //                     refCode: 'no',
// //                     phoneNumber: phoneNumber,
// //                     prefixNumber: dropdownValue,
// //                   )));
// //         };
// //
// //         final PhoneCodeAutoRetrievalTimeout codeAutoRetrievalTimeout =
// //             (String verificationId) {
// //           this.verificationId = verificationId;
// //           print("time out");
// //           setState(() {
// //             _saving = false;
// //           });
// // //          ไม่เปลี่ยน Provider ยกเลิกแบบ Middle เพราะไม่ปลอดภัยหลังจาก ยกเลิก Secret
// // //          roundSMS = providerSMS.Firebase;
// //         };
// //         try {
// //           await FirebaseAuth.instance.verifyPhoneNumber(
// //               phoneNumber: phoneNumber,
// //               timeout: const Duration(seconds: 5),
// //               verificationCompleted: verificationCompleted,
// //               verificationFailed: verificationFailed,
// //               codeSent: codeSent,
// //               codeAutoRetrievalTimeout: codeAutoRetrievalTimeout);
// //         } catch (e) {
// //           showShortToastColor(
// //               "Failed to Verify Phone Number: ${e}", Colors.red);
// //         }
// //         //ส่งด้วย twilio
// //       } else
//       setState(() => _saving = true);
//
//       if (roundSMS == providerSMS.Twilio) {
//         String url = env.apiUrl + '/authNoFirebase';
//         var response =
//             await http.post(url, body: {'phone_number': phoneNumber});
//         print('Response status: ${response.statusCode}');
//         print('Response body: ${response.body}');
//         var body = json.decode(response.body);
//
//         if (body['statusCode'] == 200) {
//           //success register and sign in
//           //to confirm
//           setState(() {
//             _saving = false;
//           });
//           print(phoneNumber.length);
//
//           Navigator.push(
//             context,
//             EnterExitRoute(
//                 exitPage: CreateWallet(),
//                 enterPage: OTP_PAGE(
//                   // roundSMS: roundSMS,
//                   phoneNumber: phoneNumber,
//                   refCode: 'no',
//                   prefixNumber: dropdownValue,
//                   checkIf: 'no',
//                 )),
//           );
//         } else {
//           setState(() {
//             _saving = false;
//           });
//           //register failed
//         }
//       }
//     } else {
//       showShortToast(AppLocalizations.of(context)!.translate('prefixzero'));
//     }
//   }

  Future _sendCodeToPhoneNumberCreate() async {
    if (_phoneNumberController.text.substring(0, 1) == '0') {
      phoneNumber = dropdownValue +
          _phoneNumberController.text
              .substring(1, _phoneNumberController.text.length)
              .toString();

      print(phoneNumber);

      if (roundSMS == providerSMS.Twilio) {
        var url = Uri.https(env.apiUrl, '/authNoFirebase');
        try {
          var response =
              await http.post(url, body: {'phone_number': phoneNumber});
          print('Response status: ${response.statusCode}');
          print('Response body: ${response.body}');
          var body = json.decode(response.body);

          if (body['statusCode'] == 200) {
            //success register and sign in
            //to confirm
            setState(() {
              _saving = false;
            });
            print(roundSMS);
            Navigator.push(
              context,
              EnterExitRoute(
                  exitPage: CreateWallet(),
                  enterPage: OTPCreate(
                      checkIf: 'register',
                      auth: _auth,
                      prefixNumber: dropdownValue,
                      phoneNumber: phoneNumber,
                      verificationId: verificationId,
                      roundSMS: roundSMS)),
            );
          } else {
            setState(() {
              _saving = false;
            });
            showShortToast(AppLocalizations.of(context)!
                .translate('bankingcash_error_url'));
            //register failed
          }
        } catch (e) {
          setState(() {
            _saving = false;
          });
          showShortToast(e.toString());
          //register failed
        }
      }
    } else {
      setState(() {
        _saving = false;
      });
      showShortToast(AppLocalizations.of(context)!.translate('prefixzero'));
    }
  }

  Future _sendCodeToPhoneNumberCreateWithMKT() async {
    if (_phoneNumberController.text.substring(0, 1) == '0') {
      phoneNumber = dropdownValue +
          _phoneNumberController.text
              .substring(1, _phoneNumberController.text.length)
              .toString();

      print(phoneNumber);

      var url = Uri.https(env.apiUrl, '/authNoFirebaseWithMKT');
      try {
        var response =
            await http.post(url, body: {'phone_number': phoneNumber});
        print('Response status: ${response.statusCode}');
        print('Response body: ${response.body}');
        var body = json.decode(response.body);

        if (response.statusCode == 200) {
          //success register and sign in
          //to confirm
          setState(() {
            _saving = false;
          });

          Navigator.push(
            context,
            EnterExitRoute(
                exitPage: CreateWallet(),
                enterPage: OTPCreate(
                  checkIf: 'register',
                  auth: _auth,
                  prefixNumber: dropdownValue,
                  phoneNumber: phoneNumber,
                  verificationId: verificationId,
                  roundSMS: providerSMS.MKT,
                  otpToken: body['result']['token'],
                  refCode: body['result']['ref_code'],
                )),
          );
        } else {
          setState(() {
            _saving = false;
          });
          showShortToast(
              AppLocalizations.of(context)!.translate('bankingcash_error_url'));
          //register failed
        }
      } catch (e) {
        setState(() {
          _saving = false;
        });
        showShortToast(e.toString());
        //register failed
      }
    }
  }
}
