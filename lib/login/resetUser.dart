import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:likewallet/libraryman/app_local.dart';

class resetUser extends StatefulWidget {
  _resetUser createState() => new _resetUser();
}

class _resetUser extends State<resetUser> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return new Scaffold(
      backgroundColor: Color(0xff141322),
      resizeToAvoidBottomInset: false,
      // resizeToAvoidBottomPadding: false,
      body: resetdetail(context),
    );
  }

  Widget resetdetail(context) {
    return new Container(
      color: Color(0xff141322),
      child: new Column(
        children: <Widget>[
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.1,
          ),
          Center(
            child: Container(
                alignment: Alignment.center,
                height: MediaQuery.of(context).size.height * 0.13,
                width: MediaQuery.of(context).size.width * 0.7,
                child: Column(
                  children: <Widget>[
                    Text(
                      AppLocalizations.of(context)!.translate('reset_title'),
                      style: TextStyle(
                          fontFamily: 'Proxima Nova',
                          fontSize: 25,
                          color: Color(0xffFFFFFF)),
                    ),
                    Text(
                      AppLocalizations.of(context)!.translate('reset_title2'),
                      style: TextStyle(
                          fontFamily: 'Proxima Nova',
                          fontSize: 16,
                          color: Color(0xff939395)),
                    ),
                  ],
                )),
          ),
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.02,
          ),
          Center(
            child: new Container(
              alignment: Alignment.center,
              height: MediaQuery.of(context).size.height * 0.07,
              width: MediaQuery.of(context).size.width * 0.9,
              decoration: BoxDecoration(
                color: Color(0xff141322),
                border: Border.all(
                  color: Color(0xff0FE8D8),
                  width: 0.3,
                ),
                borderRadius: BorderRadius.all(Radius.circular(5.0)),
              ),
              child: Row(
                children: <Widget>[
                  new Container(
                    alignment: Alignment.center,
                    height: MediaQuery.of(context).size.height * 0.05,
                    width: MediaQuery.of(context).size.width * 0.65,
//                color: Colors.blue,
                    child: TextField(
                      style: TextStyle(color: Colors.white),
                      decoration: InputDecoration(
                        contentPadding: EdgeInsets.only(left: 10),
                        hintText: AppLocalizations.of(context)!
                            .translate('reset_newuser'),
                        hintStyle: TextStyle(
                          fontSize: 16.0,
                          color: Colors.white,
                          fontFamily: 'Proxima Nova',
                          fontWeight: FontWeight.normal,
                        ),
                        border: InputBorder.none,
                      ),
                      keyboardType: TextInputType.text,
                    ),
                  ),
                ],
              ),
            ),
          ),
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.03,
          ),
          Center(
            child: new Container(
              alignment: Alignment.center,
              height: MediaQuery.of(context).size.height * 0.07,
              width: MediaQuery.of(context).size.width * 0.9,
              decoration: BoxDecoration(
                color: Color(0xff141322),
                border: Border.all(
                  color: Color(0xff0FE8D8),
                  width: 0.3,
                ),
                borderRadius: BorderRadius.all(Radius.circular(5.0)),
              ),
              child: Row(
                children: <Widget>[
                  new Container(
                    alignment: Alignment.center,
                    height: MediaQuery.of(context).size.height * 0.05,
                    width: MediaQuery.of(context).size.width * 0.65,
//                color: Colors.blue,
                    child: TextField(
                      style: TextStyle(color: Colors.white),
                      decoration: InputDecoration(
                        contentPadding: EdgeInsets.only(left: 10),
                        hintText: AppLocalizations.of(context)!
                            .translate('reset_newpassword'),
                        hintStyle: TextStyle(
                          fontSize: 16.0,
                          color: Colors.white,
                          fontFamily: 'Proxima Nova',
                          fontWeight: FontWeight.normal,
                        ),
                        border: InputBorder.none,
                      ),
                      keyboardType: TextInputType.text,
                    ),
                  ),
                ],
              ),
            ),
          ),
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.1,
          ),
          Center(
            child: new Container(
              alignment: Alignment.center,
              height: MediaQuery.of(context).size.height * 0.07,
              width: MediaQuery.of(context).size.width * 0.5,
              child: ButtonTheme(
                minWidth: MediaQuery.of(context).size.width / 1.2,
                height: MediaQuery.of(context).size.height,
                child: TextButton(
                  style: ButtonStyle(
                    shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                      RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                    ),
                    backgroundColor: MaterialStateProperty.resolveWith<Color>(
                          (Set<MaterialState> states) {
                        if (states.contains(MaterialState.disabled)) {
                          return Color(0xff00F1E0); // Disabled color
                        }
                        return Color(0xff00F1E0); // Regular color
                      },
                    ),
                  ),
                  onPressed: () {
                    Navigator.pushNamedAndRemoveUntil(
                        context, '/index', ModalRoute.withName('/'));
                  },
                  child: Text(
                    AppLocalizations.of(context)!.translate('reset_button'),
                    style: TextStyle(
                      color: Color(0xff000000).withOpacity(0.75),
                      fontFamily: 'Proxima Nova',
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}
