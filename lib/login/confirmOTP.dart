import 'dart:async';
import 'dart:convert';

import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:likewallet/Theme.dart';

import 'package:likewallet/animationPage.dart';
import 'package:likewallet/libraryman/secret_pass_first.dart';
import 'package:likewallet/login/sing_in.dart';
import 'package:likewallet/screen/registerForm.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:likewallet/screen/choice_user.dart';
import 'package:likewallet/app_config.dart';
import 'package:http/http.dart' as http;
import 'package:likewallet/libraryman/secret_pass.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:likewallet/libraryman/otp.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/libraryman/setPin.dart';
import 'package:sms_autofill/sms_autofill.dart';
import 'package:sms_receiver/sms_receiver.dart';

import 'package:sms_autofill/sms_autofill.dart';
import 'package:sms_receiver/sms_receiver.dart';
import 'package:likewallet/screen_util.dart';

import '../ImageTheme.dart';

class OTP_PAGE extends StatefulWidget {
//  OTP_PAGE({this.checkIf});
  OTP_PAGE(
      {this.refCode,
      this.checkIf,
      this.otpToken,
      this.auth,
      this.verificationId,
      this.roundSMS,
      this.phoneNumber,
      this.firstName,
      this.lastName,
      this.prefixNumber});

  final FirebaseAuth? auth;
  final String? verificationId;
  final String? otpToken;
  final providerSMS? roundSMS;
  final String? phoneNumber;
  final String? firstName;
  final String? lastName;
  final String? refCode;
  final String? prefixNumber;

  //  OTP_PAGE({this.auth, this.verificationId});

//  final FirebaseAuth auth;
//  final String verificationId;
//  final VoidCallback onSignedIn;
  final String? checkIf;
//  OTP_PAGE({Key key, @required this.text}) : super(key: key);
  @override
  _OTP_PAGE createState() => new _OTP_PAGE(
      checkIf: checkIf,
      auth: auth,
      verificationId: verificationId,
      roundSMS: roundSMS,
      phoneNumber: phoneNumber,
      otpToken: otpToken,
      firstName: firstName,
      lastName: lastName,
      refCode: refCode,
      prefixNumber: prefixNumber);
}

setToken(String _token) async {
  SharedPreferences pref = await SharedPreferences.getInstance();
  pref.setString("_token", _token);
  return true;
}

enum FormType { login, register }

class _OTP_PAGE extends State<OTP_PAGE> with CodeAutoFill {
  _OTP_PAGE(
      {this.checkIf,
      this.auth,
      this.verificationId,
      this.roundSMS,
      this.phoneNumber,
      this.otpToken,
      this.firstName,
      this.lastName,
      this.refCode,
      this.prefixNumber});
  final FirebaseAuth? auth;
  final String? verificationId;
  final providerSMS? roundSMS;
  final String? firstName;
  final String? lastName;
  final String? phoneNumber;
  String?
      otpToken; //ไม่ใช้ final เฉพาะอาจมีการส่งอีครั้งแล้ว บันทึกกลับตัวแปลนี้
  final String? checkIf;
  String? refCode;
  final String? prefixNumber;
  late providerSMS smsRound;
  late String verificationResend;
  late Timer timer;

  String _smsText = '';

  late String appSignature;
  int checkRound = 0;
  late AbstractOTP OTPAbstract;
  // List<String> otp = List<String>();
  List<String> otp = [];
  late String _code;
  String signature = "j2xaXCHKqya";

  @override
  void codeUpdated() {
    print('codeUpdated');

    setState(() {
      print(code);
      if (code!.length == 6) {
        setState(() {
          _saving = true;
        });
        passCode = code ?? '';
        passCodeColo = 6;
        for (var i = 0; i < code!.length; i++) {
          String subCode = code ?? '';
          print(subCode.substring(i, i + 1));
          otp.add(subCode.substring(i, i + 1));
        }
        print(smsRound);
        if (smsRound == providerSMS.Firebase) {
          _signInWithPhoneNumber(passCode.trim());
        } else if (smsRound == providerSMS.Twilio) {
          _verifyOTPNoFirebase(passCode.trim());
        }
      }
    });
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    smsRound = roundSMS!;
    OTPAbstract = OTPHandle();
    listenForCode();
  }

  @override
  void dispose() {
    // TODO: implement dispose
    SmsAutoFill().unregisterListener();
    cancel();

    // timer.cancel();

    super.dispose();
  }

  final formKey = new GlobalKey<FormState>();

  late String _email;
  late String _password;
  FormType _formType = FormType.login;
  int numberPhone = 0;
  late String dropdownValue;
//  FirebaseAuth _auth = FirebaseAuth.instance;
  int passCodeColo = 0;
  String passCode = '';
  bool _saving = false;

  void showShortToast(msg, Color color) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_SHORT,
        backgroundColor: color,
        textColor: Colors.white);
  }

  bool validateAndSave() {
    final form = formKey.currentState;
    if (form!.validate()) {
      form.save();
      return true;
    } else {
      return false;
    }
  }

  void setCode(pass) {
    if (pass.toString() == 'remove') {
      setState(() {
        if (passCodeColo <= 0) {
          passCodeColo = 0;
          passCode = '';
          setState(() {
            otp.clear();
          });
        } else {
          passCodeColo -= 1;
          passCode = passCode.substring(0, passCode.length - 1);
          setState(() {
            otp.removeLast();
          });
        }
      });
    } else {
      setState(() {
        if(passCodeColo < 6){
          passCodeColo += 1;
          passCode += pass.toString();
          setState(() {
            otp.add(pass.toString());
          });
        } else {
          passCodeColo = 6;
        }
      });
    }
    print(passCode);
    if (passCodeColo == 6) {
      //login
      setState(() {
        _saving = true;
      });
      print(smsRound);
      if (smsRound == providerSMS.Firebase) {
        _signInWithPhoneNumber(passCode.trim());
      } else if (smsRound == providerSMS.Twilio) {
        _verifyOTPNoFirebase(passCode.trim());
      } else if (smsRound == providerSMS.MKT) {
        _verifyOTPSmsMKT(
            otpToken: otpToken!,
            refCode: refCode!,
            phoneNumber: phoneNumber!,
            smsCode: passCode.trim().toString());
      }

//      _signInWithPhoneNumber(passCode.trim());
    }
  }

  // void _verifyOTPNoFirebase(String smsCode) async {
  //   print(smsCode);
  //   String url = env.apiUrl + '/verifyOTPNoFirebaseNewCenter';
  //   var response = await http
  //       .post(url, body: {'phone_number': phoneNumber, 'codeVerify': smsCode});
  //
  //   print('Response status: ${response.statusCode}');
  //   print('Response body: ${response.body}');
  //   var body = json.decode(response.body);
  //   if (body['statusCode'] == 200) {
  //     final customToken = body['token'];
  //     // body['dataCenter']
  //
  //     UserCredential user = await FirebaseAuth.instance
  //         .signInWithCustomToken(customToken.toString())
  //         .catchError((error) {
  //       setState(() => _saving = false);
  //       showShortToast(AppLocalizations.of(context)!.translate('otp_incorrect'),
  //           Colors.red);
  //     });
  //     //sign in firebase
  //     setState(() => _saving = false);
  //
  //     print('phoneNumber : ' + user.user.phoneNumber);
  //
  //     user.user.getIdToken().then((value) {
  //       if (checkIf == 'register') {
  //         // Navigator.push(
  //         //     context,
  //         //     EnterExitRoute(
  //         //         exitPage: OTP_PAGE(),
  //         //         enterPage: SecretPassFirst(
  //         //             checkIf: checkIf,
  //         //             roundSMS: smsRound,
  //         //             codeVerify: passCode,
  //         //             phoneNumber: phoneNumber,
  //         //             firstName: firstName,
  //         //             lastName: lastName,
  //         //             refCode: refCode)));
  //         Navigator.push(context,
  //             EnterExitRoute(exitPage: OTP_PAGE(), enterPage: REGISTER_FORM()));
  //       } else {
  //         Navigator.push(
  //           context,
  //           EnterExitRoute(
  //               exitPage: OTP_PAGE(),
  //               enterPage: SetPin(
  //                   refCode: refCode,
  //                   firstName: firstName,
  //                   lastName: lastName,
  //                   checkIf: checkIf,
  //                   secret: "LikeWallet",
  //                   roundSMS: smsRound,
  //                   codeVerify: passCode,
  //                   phoneNumber: phoneNumber,
  //                   pinAgain: false)),
  //         );
  //       }
  //     });
  //   } else {
  //     setState(() => _saving = false);
  //     showShortToast(
  //         AppLocalizations.of(context)!.translate('otp_incorrect'), Colors.red);
  //   }
  // }

  void _verifyOTPSmsMKT({
    required String smsCode,
    required String otpToken,
    required String refCode,
    required String phoneNumber,
  }) async {
    var url = Uri.https(env.apiUrl, '/verifyOTPSmsMKT');
    var response = await http.post(url, body: {
      "otp_token": otpToken,
      "otp_code": smsCode,
      "ref_code": refCode,
      "phone_number": phoneNumber
    });
    var body = json.decode(response.body);
    if (body['statusCode'] == 200) {
      final customToken = body['token'];

      UserCredential user = await FirebaseAuth.instance
          .signInWithCustomToken(customToken.toString())
          .catchError((error) {
        setState(() {
          _saving = false;
        });
        showShortToast(AppLocalizations.of(context)!.translate('otp_incorrect'),
            Colors.red);
      });
      //sign in firebase
      setState(() {
        _saving = false;
      });

      user.user!.getIdToken().then((value) {
        Navigator.pushReplacement(
          context,
          CupertinoPageRoute(
              builder: (context) => SetPin(
                  refCode: refCode,
                  firstName: firstName,
                  lastName: lastName,
                  checkIf: checkIf,
                  secret: "LikeWallet",
                  roundSMS: smsRound,
                  codeVerify: smsCode,
                  phoneNumber: phoneNumber,
                  pinAgain: false)),
        );
        // }
      });
    } else {
      setState(() {
        _saving = false;
      });
      showShortToast(
          AppLocalizations.of(context)!.translate('otp_incorrect'), Colors.red);
    }
  }

  void _verifyOTPNoFirebase(String smsCode) async {
    print(smsCode);

    var url = Uri.https(env.apiUrl, '/verifyOTPNoFirebaseNew');
    var response = await http
        .post(url, body: {'phone_number': phoneNumber, 'codeVerify': smsCode});

    print('Response status: ${response.statusCode}');
    print('Response body: ${response.body}');
    var body = json.decode(response.body);
    if (body['statusCode'] == 200) {

      final customToken = body['token'];

      print("UserCredential");
      UserCredential user = await FirebaseAuth.instance
          .signInWithCustomToken(customToken.toString())
          .catchError((error) {
        setState(() {
          _saving = false;
        });
        showShortToast(AppLocalizations.of(context)!.translate('otp_incorrect'),
            Colors.red);
      });
      //sign in firebase
      setState(() {
        _saving = false;
      });

      print('phoneNumber : ' + user.user!.phoneNumber.toString());

      user.user!.getIdToken().then((value) {
        if (checkIf == 'register') {
          Navigator.pushReplacement(
              context,
              CupertinoPageRoute(
                  builder: (context) => SecretPassFirst(
                      checkIf: checkIf,
                      roundSMS: smsRound,
                      codeVerify: passCode,
                      phoneNumber: phoneNumber,
                      firstName: firstName,
                      lastName: lastName,
                      refCode: refCode)));
          // Navigator.push(
          //     context,
          //     EnterExitRoute(
          //         exitPage: OTP_PAGE(),
          //         enterPage: ));
        } else {
          Navigator.pushReplacement(
            context,
            CupertinoPageRoute(
                builder: (context) => SetPin(
                    refCode: refCode,
                    firstName: firstName,
                    lastName: lastName,
                    checkIf: checkIf,
                    secret: "LikeWallet",
                    roundSMS: smsRound,
                    codeVerify: passCode,
                    phoneNumber: phoneNumber,
                    pinAgain: false)),
          );
        }
      });
    } else {
      setState(() {
        _saving = false;
      });
      showShortToast(
          AppLocalizations.of(context)!.translate('otp_incorrect'), Colors.red);
    }
  }

  // reSend() {
  //   if (checkRound < 1) {
  //     checkRound = 1;
  //     setState(() => _saving = true);
  //     OTPAbstract.sendCodeToPhoneNumber(phoneNumber).then((data) {
  //       smsRound = providerSMS.MKT;
  //       setState(() {
  //         _saving = false;
  //         otp.clear();
  //         passCodeColo = 0;
  //         checkRound = 0;
  //       });
  //     });
  //   }
  // }
  reSend() {
    if (checkRound < 1) {
      checkRound = 1;
      setState(() => _saving = true);
      if (phoneNumber!.substring(0, 3) == "+66") {
        OTPAbstract.sendCodeToPhoneNumberWithMKT(phoneNumber).then((data) {
          setState(() {
            otpToken = data['token'];
            refCode = data['ref_code'];
            print(otpToken);
            print(refCode);
            smsRound = providerSMS.MKT;
            _saving = false;
            otp.clear();
            passCodeColo = 0;
            checkRound = 0;
          });
        });
      } else {
        OTPAbstract.sendCodeToPhoneNumber(phoneNumber).then((data) {
          if (data) {
            setState(() {
              smsRound = providerSMS.Twilio;
              _saving = false;
              otp.clear();
              passCodeColo = 0;
              checkRound = 0;
            });
          } else {
            setState(() {
              smsRound = providerSMS.Twilio;
              _saving = false;
              otp.clear();
              passCodeColo = 0;
              checkRound = 0;
            });
          }
        });
      }
    }
  }

  void moveToRegister() {
    formKey.currentState!.reset();
    setState(() {
      _formType = FormType.register;
    });
  }

  void moveToLogin() {
    formKey.currentState!.reset();
    setState(() {
      _formType = FormType.login;
    });
  }

  gridviewForPhone( orientation) {
    return new Scaffold(
      resizeToAvoidBottomInset: false,
      // resizeToAvoidBottomPadding: false,
      backgroundColor: LikeWalletAppTheme.bule2_4,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment(-1.0, -0.5),
            end: Alignment(1.0, 1.0),
            colors: [LikeWalletAppTheme.bule2_7, LikeWalletAppTheme.bule2_7],
            stops: [0.0, 1.0],
          ),
        ),
        child: Stack(
//          width: double.infinity,
          alignment: Alignment.bottomCenter,
          children: <Widget>[
            new Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.max,
              children: <Widget>[
                SizedBox(
                  height: 130.h,
                ),
                backButton(context, Colors.grey),
                new Padding(
                  padding: EdgeInsets.only(),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      new Text(
                        AppLocalizations.of(context)!.translate('otp_n_code'),
                        style: TextStyle(
                            color: LikeWalletAppTheme.gray7.withOpacity(0.3),
                            fontFamily: AppLocalizations.of(context)!
                                .translate('font1'),
                            fontSize: MediaQuery.of(context).size.height *
                                0.01724137931),
                      ),
                      SizedBox(
                        width: MediaQuery.of(context).size.width * 0.02,
                      ),
                      ButtonTheme(
                        minWidth: MediaQuery.of(context).size.width * 0.1,
                        height:
                            MediaQuery.of(context).size.height * 0.0369458128,
                        child: checkRound == 0
                            ? new TextButton(
                                style: ButtonStyle(
                                    foregroundColor:
                                        MaterialStateProperty.all<Color>(
                                            Color(0xff000000)),
                                    backgroundColor: MaterialStateProperty.all(
                                        LikeWalletAppTheme.gray7
                                            .withOpacity(0.3)),
                                    shape: MaterialStateProperty.all(
                                        new RoundedRectangleBorder(
                                            borderRadius:
                                                new BorderRadius.circular(
                                                    5.0)))),
                                // shape: new RoundedRectangleBorder(borderRadius: new BorderRadius.circular(5.0),
                                // ),
                                // disabledColor: Color(0xff000000),
                                // primary: LikeWalletAppTheme.gray7.withOpacity(0.3),
                                onPressed: () {
                                  reSend();
                                },
                                child: new Text(
                                  AppLocalizations.of(context)!
                                      .translate('otp_resend'),
                                  style: TextStyle(
                                      fontFamily: AppLocalizations.of(context)!
                                          .translate('font1'),
                                      color: LikeWalletAppTheme.black,
                                      fontSize:
                                          MediaQuery.of(context).size.height *
                                              0.01600985221,
                                      fontWeight: FontWeight.w500),
                                ))
                            : new TextButton(
                                style: ButtonStyle(
                                  foregroundColor:
                                      MaterialStateProperty.all<Color>(
                                          Color(0xff000000).withOpacity(0.2)),
                                  backgroundColor:
                                      MaterialStateProperty.all<Color>(
                                          Color(0xff000000).withOpacity(0.2)),
                                  shape: MaterialStateProperty.all(
                                      new RoundedRectangleBorder(
                                          borderRadius:
                                              new BorderRadius.circular(5.0))),
                                ),
                                // shape: new RoundedRectangleBorder(
                                //   borderRadius: new BorderRadius.circular(5.0),
                                // ),
                                // disabledColor:
                                //     Color(0xff000000).withOpacity(0.2),
                                // color: Color(0xff000000).withOpacity(0.2),
                                onPressed: () {},
                                child: new Text(
                                  AppLocalizations.of(context)!
                                      .translate('otp_resend'),
                                  style: TextStyle(
                                      fontFamily: AppLocalizations.of(context)!
                                          .translate('font1'),
                                      color: Colors.white.withOpacity(0.2),
                                      fontSize:
                                          MediaQuery.of(context).size.height *
                                              0.01600985221,
                                      fontWeight: FontWeight.normal),
                                )),
                      ),
                    ],
                  ),
                ),
                new Padding(
                  padding: EdgeInsets.only(
                    top: mediaQuery(context, 'height', 117),
                  ),
                  child: new Text(
                    AppLocalizations.of(context)!.translate('otp_code'),
                    style: TextStyle(
                        color: LikeWalletAppTheme.white,
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                        fontWeight: FontWeight.normal,
                        fontSize:
                            MediaQuery.of(context).size.height * 0.02586206896),
                  ),
                ),
                new Container(
                    padding: EdgeInsets.only(
                        top: MediaQuery.of(context).size.width * 0.03),
                    child: new Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        new Padding(
                          padding: EdgeInsets.all(
                              MediaQuery.of(context).size.height * 0.002),
                          child: new Container(
                            width: mediaQuery(context, 'width', 117),
                            height: mediaQuery(context, 'height', 117),
                            child: new Align(
                              alignment: Alignment.center,
                              child: passCodeColo <= 0
                                  ? Text('')
                                  : Text(
                                      otp[0],
                                      style: TextStyle(
                                          fontSize: MediaQuery.of(context)
                                                  .size
                                                  .height *
                                              0.01923076923,
                                          fontFamily:
                                              AppLocalizations.of(context)!
                                                  .translate('font2'),
                                          fontWeight: FontWeight.normal,
                                          color: Colors.white),
                                    ),
                            ),
                            decoration: new BoxDecoration(
                              color: passCodeColo <= 0
                                  ? LikeWalletAppTheme.bule2_6
                                  : LikeWalletAppTheme.bule2_6,
                              shape: BoxShape.circle,
                            ),
                          ),
                        ),
                        new Padding(
                          padding: EdgeInsets.all(
                              MediaQuery.of(context).size.height * 0.002),
                          child: new Container(
                            width: mediaQuery(context, 'width', 117),
                            height: mediaQuery(context, 'height', 117),
                            child: new Align(
                              alignment: Alignment.center,
                              child: passCodeColo <= 1
                                  ? Text('')
                                  : Text(
                                      otp[1],
                                      style: TextStyle(
                                          fontSize: MediaQuery.of(context)
                                                  .size
                                                  .height *
                                              0.01923076923,
                                          fontFamily:
                                              AppLocalizations.of(context)!
                                                  .translate('font2'),
                                          fontWeight: FontWeight.normal,
                                          color: Colors.white),
                                    ),
                            ),
                            decoration: new BoxDecoration(
                              color: passCodeColo <= 1
                                  ? LikeWalletAppTheme.bule2_6
                                  : LikeWalletAppTheme.bule2_6,
                              shape: BoxShape.circle,
                            ),
                          ),
                        ),
                        new Padding(
                          padding: EdgeInsets.all(
                              MediaQuery.of(context).size.height * 0.002),
                          child: new Container(
                            width: mediaQuery(context, 'width', 117),
                            height: mediaQuery(context, 'height', 117),
                            child: new Align(
                              alignment: Alignment.center,
                              child: passCodeColo <= 2
                                  ? Text('')
                                  : Text(
                                      otp[2],
                                      style: TextStyle(
                                          fontSize: MediaQuery.of(context)
                                                  .size
                                                  .height *
                                              0.01923076923,
                                          fontFamily:
                                              AppLocalizations.of(context)!
                                                  .translate('font2'),
                                          fontWeight: FontWeight.normal,
                                          color: Colors.white),
                                    ),
                            ),
                            decoration: new BoxDecoration(
                              color: passCodeColo <= 2
                                  ? LikeWalletAppTheme.bule2_6
                                  : LikeWalletAppTheme.bule2_6,
                              shape: BoxShape.circle,
                            ),
                          ),
                        ),
                        new Padding(
                          padding: EdgeInsets.all(
                              MediaQuery.of(context).size.height * 0.001),
                          child: new Container(
                            width: mediaQuery(context, 'width', 117),
                            height: mediaQuery(context, 'height', 117),
                            child: new Align(
                              alignment: Alignment.center,
                              child: passCodeColo <= 3
                                  ? Text('')
                                  : Text(
                                      otp[3],
                                      style: TextStyle(
                                          fontSize: MediaQuery.of(context)
                                                  .size
                                                  .height *
                                              0.01923076923,
                                          fontFamily:
                                              AppLocalizations.of(context)!
                                                  .translate('font2'),
                                          fontWeight: FontWeight.normal,
                                          color: Colors.white),
                                    ),
                            ),
                            decoration: new BoxDecoration(
                              color: passCodeColo <= 2
                                  ? LikeWalletAppTheme.bule2_6
                                  : LikeWalletAppTheme.bule2_6,
                              shape: BoxShape.circle,
                            ),
                          ),
                        ),
                        new Padding(
                          padding: EdgeInsets.all(
                              MediaQuery.of(context).size.height * 0.002),
                          child: new Container(
                            width: mediaQuery(context, 'width', 117),
                            height: mediaQuery(context, 'height', 117),
                            child: new Align(
                              alignment: Alignment.center,
                              child: passCodeColo <= 4
                                  ? Text('')
                                  : Text(
                                      otp[4],
                                      style: TextStyle(
                                          fontSize: MediaQuery.of(context)
                                                  .size
                                                  .height *
                                              0.01923076923,
                                          fontFamily:
                                              AppLocalizations.of(context)!
                                                  .translate('font2'),
                                          fontWeight: FontWeight.normal,
                                          color: Colors.white),
                                    ),
                            ),
                            decoration: new BoxDecoration(
                              color: passCodeColo <= 4
                                  ? LikeWalletAppTheme.bule2_6
                                  : LikeWalletAppTheme.bule2_6,
                              shape: BoxShape.circle,
                            ),
                          ),
                        ),
                        new Padding(
                          padding: EdgeInsets.all(
                              MediaQuery.of(context).size.height * 0.002),
                          child: new Container(
                            width: mediaQuery(context, 'width', 117),
                            height: mediaQuery(context, 'height', 117),
                            child: new Align(
                              alignment: Alignment.center,
                              child: passCodeColo <= 5
                                  ? Text('')
                                  : Text(
                                      otp[5],
                                      style: TextStyle(
                                          fontSize: MediaQuery.of(context)
                                                  .size
                                                  .height *
                                              0.01923076923,
                                          fontFamily:
                                              AppLocalizations.of(context)!
                                                  .translate('font2'),
                                          fontWeight: FontWeight.normal,
                                          color: Colors.white),
                                    ),
                            ),
                            decoration: new BoxDecoration(
                              color: passCodeColo <= 5
                                  ? LikeWalletAppTheme.bule2_6
                                  : LikeWalletAppTheme.bule2_6,
                              shape: BoxShape.circle,
                            ),
                          ),
                        ),
                      ],
                    )),
              ],
            ),
            new Container(
              padding: EdgeInsets.only(
                  top: MediaQuery.of(context).size.height * 0.2),
              child: new Container(
                width: MediaQuery.of(context).size.width * 0.75,
//                  padding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.05),
                child: new Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    for (var a = 0; a < 3; a++)
                      new Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: <Widget>[
                          for (var i = 1; i <= 3; i++)
                            if (a == 0)
                              new Expanded(
//                                  padding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.02),
                                  child: Column(
                                children: <Widget>[
                                  TextButton(
                                    onPressed: () => setCode(i),
                                    child: Container(
                                      width: mediaQuery(context, 'width', 216),
                                      height: mediaQuery(context, 'height', 216),
                                      decoration: BoxDecoration(
                                        color: passCodeColo <= 0
                                            ? LikeWalletAppTheme.bule2_6
                                            : LikeWalletAppTheme.bule2_6,
                                        shape: BoxShape.circle,
                                      ),
                                      alignment: Alignment.center,
                                      child: Text(
                                        i.toString(),
                                        style: TextStyle(
                                          fontSize: MediaQuery.of(context).size.height * 0.04556650246,
                                          color: LikeWalletAppTheme.bule1,
                                          fontFamily: 'Nimbus Sans',
                                          fontWeight: FontWeight.w100,
                                        ),
                                      ),
                                    ),
                                  ),
                                  SizedBox(
                                      height:
                                          MediaQuery.of(context).size.height *
                                              0.02)
                                ],
                              ))
                            else if (a == 1)
                              new Expanded(
//                                  padding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.02),
                                  child: Column(
                                children: <Widget>[
                                  TextButton(
                                    onPressed: () => setCode(i + 3),
                                    child: Container(
                                      width: mediaQuery(context, 'width', 216),
                                      height: mediaQuery(context, 'height', 216),
                                      decoration: BoxDecoration(
                                        color: passCodeColo <= 0 ? LikeWalletAppTheme.bule2_6 : LikeWalletAppTheme.bule2_6,
                                        shape: BoxShape.circle,
                                      ),
                                      alignment: Alignment.center,
                                      child: Text(
                                        (i + 3).toString(),
                                        style: TextStyle(
                                          fontSize: MediaQuery.of(context).size.height * 0.04556650246,
                                          color: LikeWalletAppTheme.bule1,
                                          fontFamily: 'Nimbus Sans',
                                          fontWeight: FontWeight.w100,
                                        ),
                                      ),
                                    ),
                                  ),
                                  SizedBox(
                                      height:
                                          MediaQuery.of(context).size.height *
                                              0.02)
                                ],
                              ))
                            else if (a == 2)
                              new Expanded(
//                                    padding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.02),
                                  child: Column(
                                children: <Widget>[
                                  TextButton(
                                    onPressed: () => setCode(i + 6),
                                    child: Container(
                                      width: mediaQuery(context, 'width', 216),
                                      height: mediaQuery(context, 'height', 216),
                                      decoration: BoxDecoration(
                                        color: passCodeColo <= 0
                                            ? LikeWalletAppTheme.bule2_6
                                            : LikeWalletAppTheme.bule2_6,
                                        shape: BoxShape.circle,
                                      ),
                                      alignment: Alignment.center,
                                      child: Text(
                                        (i + 6).toString(),
                                        style: TextStyle(
                                          fontSize: MediaQuery.of(context).size.height * 0.04556650246,
                                          color: LikeWalletAppTheme.bule1,
                                          fontFamily: 'Nimbus Sans',
                                          fontWeight: FontWeight.w100,
                                        ),
                                      ),
                                    ),
                                  ),

                                  SizedBox(
                                      height:
                                          MediaQuery.of(context).size.height *
                                              0.02)
                                ],
                              ))
                        ],
                      ),
                    new Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        new Expanded(
//                            padding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.02),
                            child: TextButton(
                              onPressed: () {
                                setCode(0);
                              },
                              child: Text(''), // Add your text content here
                            ),
                        ),
                        new Expanded(
//                            padding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.02),
                            child: TextButton(
                              onPressed: () => setCode(0),
                              child: Container(
                                width: mediaQuery(context, 'width', 216),
                                height: mediaQuery(context, 'height', 216),
                                decoration: BoxDecoration(
                                  color: passCodeColo <= 0
                                      ? LikeWalletAppTheme.bule2_6
                                      : LikeWalletAppTheme.bule2_6,
                                  shape: BoxShape.circle,
                                ),
                                alignment: Alignment.center,
                                child: Text(
                                  '0',
                                  style: TextStyle(
                                    fontSize:
                                    MediaQuery.of(context).size.height * 0.04556650246,
                                    color: LikeWalletAppTheme.bule1,
                                    fontFamily: 'Nimbus Sans',
                                    fontWeight: FontWeight.w100,
                                  ),
                                ),
                              ),
                            ),
                        ),
                        new Expanded(
//                            padding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.01),
                          child: new TextButton(
                            onPressed: () => setCode('remove'),
                            child: Image.asset(LikeWalletImage.icon_clear,
                                color: LikeWalletAppTheme.bule1,
                                height: mediaQuery(context, 'height', 51.84)),
                          ),
                        ),
                      ],
                    )
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  gridviewForTablet(orientation) {
    return new Scaffold(
      resizeToAvoidBottomInset: false,
      // resizeToAvoidBottomPadding: false,
      backgroundColor: Color(0xff13D7C8),
      body: new Container(
          width: double.infinity,
          alignment: Alignment.bottomCenter,
          child: new Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.max,
            children: <Widget>[
              new Padding(
                padding: EdgeInsets.only(
                    top: MediaQuery.of(context).size.width * 0.2),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    new Text(
                      'Didn’t get the code?    ',
                      style:
                          TextStyle(color: Color(0xff000000), fontSize: 28.0),
                    ),
                    ButtonTheme(
                      minWidth: MediaQuery.of(context).size.width * 0.1,
                      height: MediaQuery.of(context).size.width * 0.07,
                      child: new TextButton(
                          style: ButtonStyle(
                              shape: MaterialStateProperty.all(
                                  new RoundedRectangleBorder(
                                borderRadius: new BorderRadius.circular(5.0),
                              )),
                              foregroundColor: MaterialStateProperty.all<Color>(
                                  Color(0xff000000)),
                              backgroundColor: MaterialStateProperty.all<Color>(
                                  Color(0xff000000))),
                          // shape: new RoundedRectangleBorder(borderRadius: new BorderRadius.circular(5.0),),
                          // disabledColor: Color(0xff000000),
                          // color: Color(0xff000000),
                          onPressed: () => {
//
                              },
                          child: new Text(
                            'Resend',
                            style: TextStyle(
                                fontSize: 28,
                                color: Colors.white,
                                fontWeight: FontWeight.normal),
                          )),
                    ),
                  ],
                ),
              ),
              new Padding(
                padding: EdgeInsets.only(
                    top: MediaQuery.of(context).size.width * 0.15),
                child: new Text(
                  'Enter Your Passcode',
                  style: TextStyle(color: Color(0xff5B5E5C), fontSize: 28.0),
                ),
              ),
              new Padding(
                  padding: EdgeInsets.only(
                      top: MediaQuery.of(context).size.width * 0.03),
                  child: new Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      new Padding(
                        padding: EdgeInsets.all(2.0),
                        child: new Container(
                          width: MediaQuery.of(context).size.width * 0.05,
                          height: MediaQuery.of(context).size.width * 0.05,
                          decoration: new BoxDecoration(
                            color: passCodeColo == 0
                                ? Colors.white
                                : Color(0xff3C3C3C),
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                      new Padding(
                        padding: EdgeInsets.all(2.0),
                        child: new Container(
                          width: MediaQuery.of(context).size.width * 0.05,
                          height: MediaQuery.of(context).size.width * 0.05,
                          decoration: new BoxDecoration(
                            color: passCodeColo <= 1
                                ? Colors.white
                                : Color(0xff3C3C3C),
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                      new Padding(
                        padding: EdgeInsets.all(2.0),
                        child: new Container(
                          width: MediaQuery.of(context).size.width * 0.05,
                          height: MediaQuery.of(context).size.width * 0.05,
                          decoration: new BoxDecoration(
                            color: passCodeColo <= 2
                                ? Colors.white
                                : Color(0xff3C3C3C),
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                      new Padding(
                        padding: EdgeInsets.all(2.0),
                        child: new Container(
                          width: MediaQuery.of(context).size.width * 0.05,
                          height: MediaQuery.of(context).size.width * 0.05,
                          decoration: new BoxDecoration(
                            color: passCodeColo <= 3
                                ? Colors.white
                                : Color(0xff3C3C3C),
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                      new Padding(
                        padding: EdgeInsets.all(2.0),
                        child: new Container(
                          width: MediaQuery.of(context).size.width * 0.05,
                          height: MediaQuery.of(context).size.width * 0.05,
                          decoration: new BoxDecoration(
                            color: passCodeColo <= 4
                                ? Colors.white
                                : Color(0xff3C3C3C),
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                      new Padding(
                        padding: EdgeInsets.all(2.0),
                        child: new Container(
                          width: MediaQuery.of(context).size.width * 0.05,
                          height: MediaQuery.of(context).size.width * 0.05,
                          decoration: new BoxDecoration(
                            color: passCodeColo <= 5
                                ? Colors.white
                                : Color(0xff3C3C3C),
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                    ],
                  )),
              new Padding(
                padding: EdgeInsets.only(
                    top: MediaQuery.of(context).size.width * 0.2),
                child: new Container(
                  child: new Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      for (var a = 0; a < 3; a++)
                        new Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: <Widget>[
                            for (var i = 1; i <= 3; i++)
                              if (a == 0)
                                new Padding(
                                    padding: EdgeInsets.all(
                                        MediaQuery.of(context).size.width *
                                            0.05),
                                    child: TextButton(
                                      onPressed: () => setCode(i),
                                      child: new Text(
                                        i.toString(),
                                        style: TextStyle(fontSize: 50.0),
                                      ),
                                    ))
                              else if (a == 1)
                                new Padding(
                                    padding: EdgeInsets.all(
                                        MediaQuery.of(context).size.width *
                                            0.05),
                                    child: TextButton(
                                      onPressed: () => setCode(i + 3),
                                      child: new Text(
                                        (i + 3).toString(),
                                        style: TextStyle(fontSize: 50.0),
                                      ),
                                    ))
                              else if (a == 2)
                                new Padding(
                                    padding: EdgeInsets.all(
                                        MediaQuery.of(context).size.width *
                                            0.05),
                                    child: TextButton(
                                      onPressed: () => setCode(i + 6),
                                      child: new Text(
                                        (i + 6).toString(),
                                        style: TextStyle(fontSize: 50.0),
                                      ),
                                    ))
                          ],
                        ),
                      new Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: <Widget>[
                          new Padding(
                              padding: EdgeInsets.only(
                                  right: MediaQuery.of(context).size.width *
                                      0.21)),
                          new Padding(
                              padding: EdgeInsets.all(
                                  MediaQuery.of(context).size.width * 0.04),
                              child: new TextButton(
                                onPressed: () => setCode(0),
                                child: new Text(
                                  '0',
                                  style: TextStyle(fontSize: 50.0),
                                ),
                              )),
                          new Padding(
                              padding: EdgeInsets.all(
                                  MediaQuery.of(context).size.width * 0.05),
                              child: TextButton(
                                  onPressed: () => setCode('remove'),
                                  child: new Icon(
                                    Icons.backspace,
                                    size: 50,
                                  )))
                        ],
                      )
                    ],
                  ),
                ),
              )
            ],
          )),
    );
  }

  @override
  Widget build(BuildContext context) {
    final double shortestSide = MediaQuery.of(context).size.shortestSide;
    final bool useMobileLayout = shortestSide < 600.0;
    final orientation = MediaQuery.of(context).orientation;
    return Scaffold(
        body: ModalProgressHUD(
      opacity: 0.1,
      child: useMobileLayout
          ? gridviewForPhone(orientation)
          : gridviewForPhone(orientation),
      inAsyncCall: _saving,
      progressIndicator: CustomLoading(),
    ));
  }

  /**
   *
   * phone_number
   * */
  /// Sign in using an sms code as input.
  Future<void> _reSend() async {
    setState(() {
      _saving = true;
    });
    final PhoneVerificationCompleted verificationCompleted =
        (AuthCredential phoneAuthCredential) {
      auth!.signInWithCredential(phoneAuthCredential).then((value) {
        Navigator.pushReplacement(
            context,
            CupertinoPageRoute(
                builder: (context) => SetPin(
                    refCode: 'no',
                    firstName: '',
                    lastName: '',
                    checkIf: '',
                    secret: "LikeWallet",
                    roundSMS: smsRound,
                    codeVerify: '',
                    phoneNumber: phoneNumber,
                    pinAgain: false)));
      });

      print('verified');
      setState(() {
        _saving = false;
      });
    };

    final PhoneVerificationFailed verificationFailed =
        (FirebaseAuthException authException) {
      setState(() {
        _saving = false;
      });
      setState(() {
        print(
            'Phone number verification failed. Code: ${authException.code}. Message: ${authException.message}');
      });
    };

    final PhoneCodeSent codeSent =
        (String? verificationId, [int? forceResendingToken]) async {
      this.verificationResend = verificationId.toString();
      print("code sent to " + phoneNumber.toString());

      setState(() {
        _saving = false;
      });
    };

    final PhoneCodeAutoRetrievalTimeout codeAutoRetrievalTimeout =
        (String verificationId) {
      this.verificationResend = verificationId;
      print(verificationId);
      print("time out");
      setState(() {
        _saving = false;
      });
    };

    await FirebaseAuth.instance.verifyPhoneNumber(
        phoneNumber: phoneNumber.toString(),
        timeout: const Duration(seconds: 5),
        verificationCompleted: verificationCompleted,
        verificationFailed: verificationFailed,
        codeSent: codeSent,
        codeAutoRetrievalTimeout: codeAutoRetrievalTimeout);
  }

  void _signInWithPhoneNumber(String smsCode) async {
    final AuthCredential credential = PhoneAuthProvider.credential(
      verificationId: verificationId.toString(),
      smsCode: smsCode,
    );

    final UserCredential user =
        await auth!.signInWithCredential(credential).catchError((error) {
      setState(() {
        _saving = false;
      });
      showShortToast(
          AppLocalizations.of(context)!.translate('otp_incorrect'), Colors.red);
    });
    setState(() {
      _saving = false;
    });
    // print(auth!.currentUser);
    user.user!.getIdTokenResult().then((_token) {
      setToken(_token.token.toString());
      // print(_token.token);
      setState(() {
        _saving = false;
      });

      if (checkIf == 'register') {
        Navigator.pushReplacement(
          context,
          CupertinoPageRoute(
              builder: (context) => SecretPassFirst(
                    checkIf: checkIf,
                    roundSMS: smsRound,
                    firstName: firstName,
                    lastName: lastName,
                    refCode: refCode,
                  )),
        );
      } else {
        Navigator.pushReplacement(
          context,
          CupertinoPageRoute(
              builder: (context) => SetPin(
                  refCode: refCode,
                  firstName: firstName,
                  lastName: lastName,
                  checkIf: checkIf,
                  secret: "LikeWallet",
                  roundSMS: smsRound,
                  codeVerify: passCode,
                  phoneNumber: phoneNumber,
                  pinAgain: false)),
        );
      }
    });
    // ตั้งค่า setFirstTime เป็น ture
    SharedPreferences pref = await SharedPreferences.getInstance();
    pref.setBool('setFirstTime', true);
  }

  Widget backPage() {
    return Positioned(
        top: MediaQuery.of(context).size.height * Screen_util("height", 139.33),
        child: GestureDetector(
          child: new Container(
            alignment: Alignment.centerLeft,
            width: MediaQuery.of(context).size.width,
            child: new IconButton(
              icon: new Icon(
                Icons.arrow_back_ios,
                size: MediaQuery.of(context).size.height *
                    Screen_util("height", 44.47),
              ),
              color: Color(0xff707071),
              onPressed: () => {Navigator.of(context).pop()},
            ),
          ),
        ));
  }
}

class CircleButton extends StatelessWidget {
  final GestureTapCallback? onTap;

  const CircleButton({Key? key, this.onTap, color}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    double size = 20.0;

    return new InkResponse(
      onTap: onTap,
      child: new Container(
        width: size,
        height: size,
        decoration: new BoxDecoration(
          color: Colors.white,
          shape: BoxShape.circle,
        ),
      ),
    );
  }
}
