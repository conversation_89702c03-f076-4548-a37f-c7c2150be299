import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/alert_migrate/already_migrate.dart';
import 'package:likewallet/device_utils.dart';
import 'package:likewallet/kycSumSub/kyc_black_list_page.dart';
import 'package:likewallet/libraryman/serviceHTTP.dart';
import 'package:likewallet/login/confirmOTP.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/middleware/check_maintenance/check_maintenance.dart';
import 'package:likewallet/screen_util.dart';

import 'package:http/http.dart' as http;
import 'package:likewallet/app_config.dart';
import 'dart:convert';
import 'dart:async';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:likewallet/libraryman/setPin.dart';

import 'package:flutter/services.dart';
import 'package:likewallet/animationPage.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:likewallet/screen/registerForm.dart';
import 'package:likewallet/libraryman/keyboard_done_widget.dart';
import 'dart:io' show Platform;

class SING_IN extends StatefulWidget {
  _SING_IN createState() => new _SING_IN();
  static const routeName = '/test';
}

setToken(String _token) async {
  SharedPreferences pref = await SharedPreferences.getInstance();
  pref.setString("_token", _token);
  return true;
}

enum FormType { login, register }

enum providerSMS { Firebase, Twilio, Nexmo, Email, MKT }

class _SING_IN extends State<SING_IN> with SingleTickerProviderStateMixin {
  int selected = 0;
  TextEditingController email = new TextEditingController();
  TextEditingController password = TextEditingController();
  TextEditingController otp = TextEditingController();
  bool _saving = false;

  final formKey = new GlobalKey<FormState>();
  TextEditingController _smsCodeController = TextEditingController();
  TextEditingController _phoneNumberController = TextEditingController();

  late BaseAuth auth;
  late String verificationId;
  late String _email;
  late String _password;
  int numberPhone = 0;
  String dropdownValue = '+66';
  FirebaseAuth _auth = FirebaseAuth.instance;
  late String phoneNumber;
  providerSMS roundSMS = providerSMS.Firebase;
  late TabController _tabController;
  bool _activeID = false;
  bool _activeTouchID = false;
  late SharedPreferences sharedPreferences;
  late CheckAbout checkAbout;
  FocusNode doneFocusNode = new FocusNode();
  late OverlayEntry? overlayEntry;
  bool status = false;
  late AbstractServiceHTTP apiCall;
  KeyboardVisibilityController keyboardVisibilityController = KeyboardVisibilityController();
  //done button zone
  showOverlay(BuildContext context) {
    if (overlayEntry != null) return;
    OverlayState? overlayState = Overlay.of(context);
    overlayEntry = OverlayEntry(builder: (context) {
      return Positioned(
          bottom: MediaQuery.of(context).viewInsets.bottom,
          right: 0.0,
          left: 0.0,
          child: InputDoneView());
    });

    overlayState!.insert(overlayEntry!);
  }

  removeOverlay() {
    if (overlayEntry != null) {
      overlayEntry!.remove();
      overlayEntry = null;
    }
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
    _tabController.dispose();
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _tabController = new TabController(vsync: this, length: 2);
    auth = Auth();
    checkAbout = OnCheckAbout();
    typeLogin();
    TextEditingController controller = TextEditingController();
    apiCall = ServiceHTTP();
    if (Platform.isIOS) {
      doneFocusNode.addListener(() {
        bool hasFocus = doneFocusNode.hasFocus;
        if (hasFocus)
          showOverlay(context);
        else
          removeOverlay();
      });

      keyboardVisibilityController.onChange.listen((bool visible) {
        removeOverlay();
      });
    }
  }

  Future<bool> checkExistUser() async {
    bool found = false;
    if (_phoneNumberController.text.substring(0, 1) == '0') {
      phoneNumber = dropdownValue +
          _phoneNumberController.text
              .substring(1, _phoneNumberController.text.length)
              .toString();
      bool existUser = await auth.checkExistUser(phoneNumber);
      final phone = await FirebaseFirestore.instance
          .collection('addressDNS')
          .where('phoneNumber', isEqualTo: phoneNumber)
          .get();
      if (existUser || phone.docs.isNotEmpty) {
        found = true;
      }
      return found;
    } else {
      phoneNumber = dropdownValue + _phoneNumberController.text.toString();
      bool existUser = await auth.checkExistUser(phoneNumber);
      final phone = await FirebaseFirestore.instance
          .collection('addressDNS')
          .where('phoneNumber', isEqualTo: phoneNumber)
          .get();
      if (existUser || phone.docs.isNotEmpty) {
        found = true;
      }
      return found;
    }
  }

  Future<bool> checkBlacklist() async {
    if (_phoneNumberController.text.substring(0, 1) == '0') {
      phoneNumber = dropdownValue +
          _phoneNumberController.text
              .substring(1, _phoneNumberController.text.length)
              .toString();
    } else {
      phoneNumber = dropdownValue + _phoneNumberController.text.toString();
    }

    final blacklist =
    await checkAbout.checkBlackList(type: 'blackList', phone: phoneNumber);
    final blacklistLDX = await checkAbout.checkBlackList(
        type: 'blackList_LDX', phone: phoneNumber);
    final blacklistFIN = await checkAbout.checkBlackList(
        type: 'blackList_FIN', phone: phoneNumber);

    if (blacklist == 'blackList') {
      status = true;
    } else if (blacklistLDX == 'blackList_LDX') {
      status = true;
    } else if (blacklistFIN == 'blackList_FIN') {
      status = false;
    } else {
      status = false;
    }

    return status;
  }

  Future<bool> checkTwoFA() async {
    final users = await auth.getCurrentUser();
    final userData = await FirebaseFirestore.instance
        .collection('users')
        .doc(users!.uid)
        .get();

    if (userData.data()!["isTwoStep"].toString() == 'true') {
      return true;
    } else {
      return false;
    }
  }

  signInPass(onValue) async {
    final status = await checkBlacklistEmail(phone: onValue.phoneNumber);

    if (status) {
      //ติด blacklist
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
            builder: (context) => BlackListPage(
              page: '',
            )),
      );
    } else {
      if (!mounted) return;
      setState(() {
        _saving = false;
      });
      // TODO :: CHECK MIGRATE
      bool migrateStatus = await checkMigrate();
      if(migrateStatus) {
        Navigator.pushReplacement(
            context, MaterialPageRoute(builder: (context) => AlreadyMigrate()));
      }else {
        Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) => SetPin(
                checkIf: 'login',
                roundSMS: providerSMS.Email,
                phoneNumber: onValue.phoneNumber,
                pinAgain: false,
                secret: 'RANDOM',
              )),
        );
      }
      // TODO :: CHECK MIGRATE
    }
  }

  Future<bool> checkMigrate() async {
    try {
      final user = await auth.getCurrentUser();
      var id = user!.uid;
      final ds =
      await FirebaseFirestore.instance.collection('migration').doc(id).get();
      ds.data()!;
      var migrated = ds.data()!['migrated'].toString();
      return migrated.isNotEmpty;
    }catch (e) {
      print(e);
      return false;
    }
  }


  Future<bool> checkBlacklistEmail({String? phone}) async {
    final blacklist = await checkAbout.checkBlackList(
        type: 'blackList', phone: phone.toString());
    final blacklistLDX = await checkAbout.checkBlackList(
        type: 'blackList_LDX', phone: phone.toString());
    final blacklistFIN = await checkAbout.checkBlackList(
        type: 'blackList_FIN', phone: phone.toString());

    if (blacklist == 'blackList') {
      status = true;
    } else if (blacklistLDX == 'blackList_LDX') {
      status = true;
    } else if (blacklistFIN == 'blackList_FIN') {
      status = false;
    } else {
      status = false;
    }
    return status;
  }

  void showColoredToastColor(msg, color) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_LONG,
        backgroundColor: color,
        textColor: Colors.white);
  }

  void showColoredToast(msg) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_LONG,
        backgroundColor: Colors.red,
        textColor: Colors.white);
  }

  void showShortToast(msg) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_SHORT,
        backgroundColor: Colors.red,
        textColor: Colors.white);
  }

  showShortToastColor(msg, color) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_SHORT,
        backgroundColor: color,
        textColor: Colors.white);
  }

  Future<List<String>> checkOldUser() async {
    if (_phoneNumberController.text.substring(0, 1) == '0') {
      phoneNumber = dropdownValue +
          _phoneNumberController.text
              .substring(1, _phoneNumberController.text.length)
              .toString();
    } else {
      setState(() => _saving = false);
      phoneNumber = dropdownValue + _phoneNumberController.text.toString();
    }
    print(phoneNumber);
    return await auth.checkOldUserName(phoneNumber);
    // } else {
    //   setState(() => _saving = false);
    //   // showShortToast(AppLocalizations.of(context)!.translate('prefixzero'));
    //   return ['false', 'false'];
    // }
  }

  typeLogin() async {
    sharedPreferences = await SharedPreferences.getInstance();
    setState(() {
      _activeID = sharedPreferences.getBool("activeID") ?? false;
      _activeTouchID = sharedPreferences.getBool("activeTouchID") ?? false;
      if (_activeID != null || _activeTouchID != null) {
        if (_activeID) {
          _activeID = sharedPreferences.getBool("activeID") ?? false;
        }
        if (_activeTouchID) {
          _activeTouchID = sharedPreferences.getBool("activeTouchID") ?? false;
        }
      } else {
        _activeID = false;
        _activeTouchID = false;
      }
      print(_activeID);
      print(_activeTouchID);
    });
  }

  Future<dynamic> requestReset() async {
    print(email.text);
    var url = Uri.https(env.apiUrl, '/requestResetPassword');
    var response = await http.post(url, body: {'email': email.text.toString()});

    return response.body;
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      home: new DefaultTabController(
        initialIndex: 0,
        length: 2,
        child: ModalProgressHUD(
          opacity: 0.1,
          inAsyncCall: _saving,
          progressIndicator: CustomLoading(),
          child: GestureDetector(
            onTap: () {
              FocusScopeNode currentFocus = FocusScope.of(context);
              if (!currentFocus.hasPrimaryFocus) {
                currentFocus.unfocus();
              }
            },
            child: new Scaffold(
                resizeToAvoidBottomInset: false,
                // resizeToAvoidBottomPadding: false,
                appBar: new AppBar(
                  backgroundColor: LikeWalletAppTheme.bule2,
                  centerTitle: true,
                  title: Stack(
                    alignment: Alignment.center,
                    children: <Widget>[
                      backButton(context, LikeWalletAppTheme.gray),
                      Container(
                        margin: EdgeInsets.only(
                            top: mediaQuery(context, 'height', 80)),
                        child: new Text(
                          AppLocalizations.of(context)!
                              .translate('login_choose'),
                          style: TextStyle(
                            fontSize: MediaQuery.of(context).size.height *
                                Screen_util("height", 45),
                            color: Color(0xff707071).withOpacity(1),
                            fontFamily: AppLocalizations.of(context)!
                                .translate('font1'),
                          ),
                        ),
                      ),
                    ],
                  ),
                  bottom: new PreferredSize(
                    preferredSize: new Size(MediaQuery.of(context).size.width,
                        MediaQuery.of(context).size.height * 0.1),
                    child: new Container(
                      child: new TabBar(
                        controller: _tabController,
                        labelColor: LikeWalletAppTheme.bule1,
                        unselectedLabelColor:
                        LikeWalletAppTheme.white.withOpacity(0.7),
                        indicatorColor: LikeWalletAppTheme.bule1,
                        onTap: (value) {
                          DeviceUtils.hideKeyboard(context);
                          setState(() {
                            selected = value;
                          });
                          print(selected);
                        },
                        tabs: <Widget>[
                          Container(
                            padding: EdgeInsets.only(
                                top: MediaQuery.of(context).size.height * 0.02,
                                bottom:
                                MediaQuery.of(context).size.height * 0.02),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: <Widget>[
                                Image.asset(
                                  LikeWalletImage.icon_smartphone,
                                  height: mediaQuery(context, 'height', 94.97),
                                  color: selected == 0
                                      ? LikeWalletAppTheme.bule1
                                      : LikeWalletAppTheme.white
                                      .withOpacity(0.7),
                                ),
                                SizedBox(
                                  width: mediaQuery(context, 'width', 30),
                                ),
                                Text(
                                  AppLocalizations.of(context)!
                                      .translate('forget_tab_mobile'),
                                  style: TextStyle(
                                    fontWeight: FontWeight.w100,
//                                color: LikeWalletAppTheme.bule1,
                                    fontSize:
                                    MediaQuery.of(context).size.height *
                                        Screen_util("height", 45),
                                    fontFamily: AppLocalizations.of(context)!
                                        .translate('font1'),
                                  ),
                                )
                              ],
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.only(
                                top: MediaQuery.of(context).size.height * 0.02,
                                bottom:
                                MediaQuery.of(context).size.height * 0.02),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: <Widget>[
                                Image.asset(
                                  LikeWalletImage.icon_email,
                                  height: mediaQuery(context, 'height', 50.39),
                                  color: selected == 1
                                      ? LikeWalletAppTheme.bule1
                                      : LikeWalletAppTheme.white
                                      .withOpacity(0.7),
                                ),
                                SizedBox(
                                  width: mediaQuery(context, 'width', 30),
                                ),
                                Text(
                                  AppLocalizations.of(context)!
                                      .translate('forget_tab_email'),
                                  style: TextStyle(
                                    fontWeight: FontWeight.w100,
//                                color: LikeWalletAppTheme.bule1,
                                    fontSize:
                                    MediaQuery.of(context).size.height *
                                        Screen_util("height", 45),
                                    fontFamily: AppLocalizations.of(context)!
                                        .translate('font1'),
                                  ),
                                )
//                            Icon(Icons.email),
//                            Text(
//                              ' ' +
//                                  AppLocalizations.of(context)
//                                      .translate('forget_tab_email'),
//                              style: TextStyle(
//                                  fontSize: MediaQuery.of(context).size.height *
//                                      Screen_util("height", 40),
//                                  fontFamily: AppLocalizations.of(context)
//                                      .translate('font1')),
//                            ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                body: Stack(
                  children: <Widget>[
                    Container(
                      decoration: BoxDecoration(
                          image: DecorationImage(
                            image: AssetImage(LikeWalletImage.background),
                            fit: BoxFit.cover,
                          )),
                    ),
                    new TabBarView(
                      controller: _tabController,
                      children: <Widget>[Mobile(context), Email(context)],
                    ),
                  ],
                )),
          ),
        ),
      ),
    );
  }

  Widget Mobile(context) {
    return new Container(
      color: Colors.transparent,
      child: new Column(
        children: <Widget>[
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.05,
          ),
          _inputPhone(),
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.1,
          ),
          _buttonLogin()
//          SizedBox(
//            height: MediaQuery.of(context).size.height * 0.02,
//          ),
//          new Container(
//            decoration: BoxDecoration(
//              color: Color(0xff141322),
//              border: Border.all(
//                color: Color(0xff0FE8D8),
//                width: mediaQuery(context, 'width', 0.3),
//              ),
//              borderRadius: BorderRadius.all(Radius.circular(5.0)),
//            ),
//            alignment: Alignment.center,
//            height: MediaQuery.of(context).size.height * 0.06,
//            width: MediaQuery.of(context).size.width * 0.7,
//            child: ButtonTheme(
//              minWidth: MediaQuery.of(context).size.width * 0.86112,
//              height: MediaQuery.of(context).size.height * 0.05658024691,
//              child: new FlatButton(
//                  shape: new RoundedRectangleBorder(
//                    borderRadius: new BorderRadius.circular(8.0),
//                  ),
//                  disabledColor: Color(0xff000000),
//                  color: Color(0xff000000),
//                  onPressed: () => {Navigator.pop(context)},
//                  child: new Text(
//                    AppLocalizations.of(context)
//                        .translate('forget_cancel_button'),
//                    style: TextStyle(
//                        fontFamily:
//                            AppLocalizations.of(context)!.translate('font1'),
//                        color: Color(0xff0FE8D8),
//                        fontSize: MediaQuery.of(context).size.height *
//                            Screen_util("height", 40),
//                        fontWeight: FontWeight.normal),
//                  )),
//            ),
//          ),
        ],
      ),
    );
  }

  Widget _inputPhone() {
    return Container(
      alignment: Alignment.center,
      height: MediaQuery.of(context).size.height * 0.07,
      width: MediaQuery.of(context).size.width * 0.9,
      decoration: BoxDecoration(
        color: Color(0xff141322),
        border: Border.all(
            color: Color(0xff0FE8D8), width: mediaQuery(context, 'width', 0.4)),
        borderRadius: BorderRadius.all(Radius.circular(5.0)),
      ),
      child: Row(
        children: <Widget>[
          new Container(
            alignment: Alignment.center,
            height: MediaQuery.of(context).size.height * 0.07,
            width: MediaQuery.of(context).size.width * 0.2,
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                value: dropdownValue,
                elevation: 15,
                style: TextStyle(color: Color(0xffADACA0)),
                iconEnabledColor: Colors.white,
                iconDisabledColor: Colors.white,
                onChanged: (String? newValue) {
                  setState(() {
                    dropdownValue = newValue.toString();
                  });
                },
                items: <String>['+66', '+856', '+855']
                    .map<DropdownMenuItem<String>>((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(value),
                  );
                }).toList(),
              ),
            ),
            decoration: BoxDecoration(
                border: Border(
                  right: BorderSide(
                    //                   <--- left side
                      color: Color(0xff0FE8D8),
                      width: mediaQuery(context, 'width', 0.4)),
                )),
          ),
          new Container(
            alignment: Alignment.center,
            height: MediaQuery.of(context).size.height * 0.07,
            width: MediaQuery.of(context).size.width * 0.65,
//                color: Colors.blue,
            child: TextField(
              controller: _phoneNumberController,
              style: TextStyle(color: Colors.white),
              decoration: InputDecoration(
                isDense: true,
                contentPadding: EdgeInsets.only(left: 20),
                hintText:
                AppLocalizations.of(context)!.translate('forget_mobile'),
                hintStyle: TextStyle(
                  fontSize: MediaQuery.of(context).size.height *
                      Screen_util("height", 46),
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  color: LikeWalletAppTheme.white.withOpacity(0.7),
                  fontWeight: FontWeight.w100,
                ),
                border: InputBorder.none,
              ),
              keyboardType: TextInputType.number,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buttonLogin() {
    return Container(
      width: 700.w,
      height: 132.h,
      child: TextButton(
          style: ButtonStyle(
            backgroundColor: MaterialStateProperty.all<Color>(
              Color(0xff00F1E0),
            ),
            shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(5.0),
                )),
          ),
          onPressed: () async {
            DeviceUtils.hideKeyboard(context);
            // if (_phoneNumberController.text.substring(0, 1) == '0') {
            if (dropdownValue == '+66' &&
                _phoneNumberController.text.length >= 9 ||
                dropdownValue == '+855' &&
                    _phoneNumberController.text.length >= 9 ||
                dropdownValue == '+856' &&
                    _phoneNumberController.text.length >= 9) {
              setState(() => _saving = true);
              try {
                final status = await checkBlacklist();
                print("blacklist" + status.toString());
                if (status) {
                  Navigator.pushReplacement(
                      context,
                      MaterialPageRoute(
                          builder: (context) => BlackListPage(
                            page: '',
                          )));
                } else {

                  auth.checkExistUser(_phoneNumberController.text).then((value) {
                    print("checkExistUser");
                    print(value.toString());
                  });

                  checkExistUser().then((data) {
                    if (data) {
                      //ส่ง OTP
                      print('พบเบอร์');
                      _sendCodeToPhoneNumber();
                    } else {
                      //ถ้าไม่มี user ให้ส่งไปหน้าสมัครแทน และไปดึงข้อมูลเก่าจาก likepoint
                      checkOldUser().then((statusCode) {
                        print(statusCode);
                        if (statusCode[0] == '200') {
                          showColoredToastColor(
                              AppLocalizations.of(context)!
                                  .translate('please_fill_more')
                                  .toString(),
                              Colors.cyanAccent);
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => REGISTER_FORM(
                                  checkIf: 'nouser',
                                  phone_number: _phoneNumberController.text,
                                  prefix: dropdownValue,
                                  oldFirstName: statusCode[1],
                                  oldLastName: statusCode[2],
                                )),
                          );
                        } else {
                          setState(() => _saving = false);
                          showColoredToastColor(
                              AppLocalizations.of(context)!
                                  .translate('noUser')
                                  .toString(),
                              Colors.red);

                          // _sendCodeToPhoneNumberCreate();
                          // Navigator.push(
                          //   context,
                          //   MaterialPageRoute(
                          //       builder: (context) => REGISTER_FORM(
                          //             checkIf: 'nouser',
                          //             phone_number: _phoneNumberController.text,
                          //             prefix: dropdownValue,
                          //           )),
                          // );
                        }
                      });
                    }
                  });
                }
              } catch (e) {
                setState(() => _saving = false);
                print(e);
              }
            } else {
              setState(() => _saving = false);
              showShortToast(
                  AppLocalizations.of(context)!.translate('contact_us_Toast'));
            }
            // } else {
            //   setState(() => _saving = false);
            //   showShortToast(
            //       AppLocalizations.of(context)!.translate('prefixzero'));
            // }
          },
          child: new Text(
            AppLocalizations.of(context)!.translate('login_login'),
            style: TextStyle(
                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                color: Colors.black,
                fontSize: MediaQuery.of(context).size.height *
                    Screen_util("height", 42),
                fontWeight: FontWeight.w100),
          )),
    );
  }

  Widget Email(context) {
    return new Container(
      color: Colors.transparent,
      child: new Column(
        children: <Widget>[
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.05,
          ),
          _inputEmail(),
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.1,
          ),
          _buttonLoginemail()
        ],
      ),
    );
  }

  Widget _buttonLoginemail() {
    return new Container(
      alignment: Alignment.center,
      height: mediaQuery(context, 'height', 132),
      width: mediaQuery(context, 'width', 720),
      child: ButtonTheme(
        minWidth: MediaQuery.of(context).size.width * 0.86112,
        height: MediaQuery.of(context).size.height * 0.05658024691,
        child: TextButton(
          style: ButtonStyle(
            shape: MaterialStateProperty.all<RoundedRectangleBorder>(
              RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.0),
              ),
            ),
            backgroundColor: MaterialStateProperty.resolveWith<Color>(
                  (Set<MaterialState> states) {
                if (states.contains(MaterialState.disabled)) {
                  return Color(0xff00F1E0); // Disabled color
                }
                return Color(0xff00F1E0); // Regular color
              },
            ),
          ),
          onPressed: () {
            FocusScopeNode currentFocus = FocusScope.of(context);
            if (!currentFocus.hasPrimaryFocus) {
              currentFocus.unfocus();

              if (!mounted) return;
              setState(() {
                _saving = true;
              });

              try {
                auth
                    .singInWithEmailAndPassword(
                    email.text.trim(), password.text.trim())
                    .then((onValue) async {
                  print(onValue!.email);
                  final statusTwoFA = await checkTwoFA();
                  print('statusTwoFA is ' + statusTwoFA.toString());
                  if (statusTwoFA) {
                    auth.getCurrentUser().then((value) async {
                      final token = await value!.getIdToken();
                      apiCall
                          .verifyTwoFA(token: token!, otp: otp.text)
                          .then((twofa) {
                        if (twofa["result"] == 'true') {
                          signInPass(onValue);
                        } else {
                          auth.signOut();
                          setState(() {
                            _saving = false;
                          });
                          showShortToastColor(
                              AppLocalizations.of(context)!
                                  .translate('is_wrong'),
                              LikeWalletAppTheme.red);
                        }
                      });
                    });
                  } else {
                    setState(() {
                      _saving = false;
                    });
                    signInPass(onValue);
                  }

                  //here
                }).catchError((error) {
                  if (!mounted) return;
                  setState(() {
                    _saving = false;
                  });
                  showShortToastColor(
                      AppLocalizations.of(context)!
                          .translate('is_wrong'),
                      LikeWalletAppTheme.red);
                });
              } catch (e) {
                if (!mounted) return;
                setState(() {
                  _saving = false;
                });
                showShortToastColor(
                    AppLocalizations.of(context)!
                        .translate('is_wrong'),
                    LikeWalletAppTheme.red);
              }
            }
          },
          child: Text(
            AppLocalizations.of(context)!.translate('login_login'),
            style: TextStyle(
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              color: Colors.black,
              fontSize: MediaQuery.of(context).size.height * Screen_util("height", 42),
              fontWeight: FontWeight.w100,
            ),
          ),
        ),
      ),
    );
  }

  Widget _inputEmail() {
    return new Container(
      alignment: Alignment.center,
//      height:MediaQuery.of(context).size.height*0.07,
//      width:MediaQuery.of(context).size.width*0.9,
      child: Form(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            new Container(
              decoration: BoxDecoration(
                color: LikeWalletAppTheme.bule2,
                border: Border.all(
                  color: LikeWalletAppTheme.bule1,
                  width: mediaQuery(context, 'height', 0.3),
                ),
                borderRadius: BorderRadius.all(Radius.circular(5.0)),
              ),
              alignment: Alignment.center,
              height: mediaQuery(context, 'height', 156),
              width: mediaQuery(context, 'width', 930),
//                color: Colors.blue,
              child: TextFormField(
                controller: email,
                style: TextStyle(
                    fontSize: mediaQuery(context, 'height', 47),
                    fontFamily:
                    AppLocalizations.of(context)!.translate('font1'),
                    color: LikeWalletAppTheme.white),
                decoration: InputDecoration(
                  isDense: true,
//                  focusedBorder: OutlineInputBorder(
//                      borderSide: BorderSide(
//                          color: LikeWalletAppTheme.bule1,
//                          width: mediaQuery(context, 'width', 1))),
                  contentPadding: EdgeInsets.only(
                    left: MediaQuery.of(context).size.width * 0.03,
                  ),
                  hintText: AppLocalizations.of(context)!
                      .translate('singinEmail_name'),
                  hintStyle: TextStyle(
                      fontWeight: FontWeight.w100,
                      fontSize: mediaQuery(context, 'height', 46),
                      fontFamily:
                      AppLocalizations.of(context)!.translate('font1'),
                      color: LikeWalletAppTheme.white),
                  border: InputBorder.none,
//                  enabledBorder: OutlineInputBorder(
//                      borderSide: BorderSide(
//                          width: mediaQuery(context, 'width', 0.3),
//                          color: LikeWalletAppTheme.bule1)),
                  hoverColor: Colors.white,
                  disabledBorder: InputBorder.none,
                  focusColor: Colors.white,
                  alignLabelWithHint: true,
                  fillColor: Colors.white,
                ),
                keyboardType: TextInputType.text,
              ),
            ),
            Padding(
              padding: EdgeInsets.only(
                top: MediaQuery.of(context).size.width * 0.05,
              ),
            ),
            new Container(
              decoration: BoxDecoration(
                color: LikeWalletAppTheme.bule2,
                border: Border.all(
                  color: LikeWalletAppTheme.bule1,
                  width: mediaQuery(context, 'height', 0.3),
                ),
                borderRadius: BorderRadius.all(Radius.circular(5.0)),
              ),
              alignment: Alignment.center,
              height: mediaQuery(context, 'height', 156),
              width: mediaQuery(context, 'width', 930),
//                color: Colors.blue,
              child: TextFormField(
                obscureText: true,
                controller: password,
                style: TextStyle(color: Colors.white),
                decoration: InputDecoration(
                  isDense: true,
//                  focusedBorder: OutlineInputBorder(
//                      borderSide: BorderSide(
//                          color: LikeWalletAppTheme.bule1,
//                          width: mediaQuery(context, 'width', 1))),
                  contentPadding: EdgeInsets.only(
                    left: MediaQuery.of(context).size.width * 0.03,
                  ),
                  hintText: AppLocalizations.of(context)!
                      .translate('singinEmail_lastname'),
                  hintStyle: TextStyle(
                      fontSize: mediaQuery(context, 'height', 47),
                      fontFamily:
                      AppLocalizations.of(context)!.translate('font1'),
                      color: LikeWalletAppTheme.white),
                  border: InputBorder.none,
//                  enabledBorder: OutlineInputBorder(
//                      borderSide: BorderSide(
//                          width: mediaQuery(context, 'width', 0.3),
//                          color: LikeWalletAppTheme.bule1)),
                  hoverColor: Colors.white,
                  disabledBorder: InputBorder.none,
                  focusColor: Colors.white,
                  alignLabelWithHint: true,
                  fillColor: Colors.white,
                ),
                keyboardType: TextInputType.text,
              ),
            ),
            Padding(
              padding: EdgeInsets.only(
                top: MediaQuery.of(context).size.width * 0.05,
              ),
            ),
            new Container(
              decoration: BoxDecoration(
                color: LikeWalletAppTheme.bule2,
                border: Border.all(
                  color: LikeWalletAppTheme.bule1,
                  width: mediaQuery(context, 'height', 0.3),
                ),
                borderRadius: BorderRadius.all(Radius.circular(5.0)),
              ),
              alignment: Alignment.center,
              height: mediaQuery(context, 'height', 156),
              width: mediaQuery(context, 'width', 930),
//                color: Colors.blue,
              child: TextFormField(
                controller: otp,
                style: TextStyle(color: Colors.white),
                decoration: InputDecoration(
                  isDense: true,
                  contentPadding: EdgeInsets.only(
                    left: MediaQuery.of(context).size.width * 0.03,
                    top: MediaQuery.of(context).size.width * 0.03,
                  ),
                  hintText: AppLocalizations.of(context)!
                      .translate('two_step_2fa_login_otp'),
                  hintStyle: TextStyle(
                      fontSize: mediaQuery(context, 'height', 47),
                      fontFamily:
                      AppLocalizations.of(context)!.translate('font1'),
                      color: LikeWalletAppTheme.white),
                  border: InputBorder.none,
                  hoverColor: Colors.white,
                  disabledBorder: InputBorder.none,
                  focusColor: Colors.white,
                  alignLabelWithHint: true,
                  fillColor: Colors.white,
                  suffixIcon: IconButton(
                    onPressed: () {
                      Clipboard.getData('text/plain').then((value) {
                        otp.text = value!.text.toString();
                      });
                    },
                    icon: Icon(
                      Icons.paste,
                      color: LikeWalletAppTheme.bule3,
                    ),
                  ),
                ),
                keyboardType: TextInputType.text,
              ),
            ),
            SizedBox(height: 30.h),
            //ปุ่มลืมรหัสผ่าน
            InkWell(
              onTap: () {
                Navigator.pushNamed(context, '/forgetPassword');
              },
              child: new Container(
                alignment: Alignment.bottomRight,
                height: mediaQuery(context, 'height', 50),
                width: mediaQuery(context, 'width', 930),
                child: new Text(
                    AppLocalizations.of(context)!
                        .translate('singinEmail_forget'),
                    style: TextStyle(
                        fontSize: MediaQuery.of(context).size.height *
                            Screen_util("height", 36),
                        fontFamily:
                        AppLocalizations.of(context)!.translate('font1'),
                        color: Colors.white70)),
              ),
            )
          ],
        ),
      ),
    );
  }

  Future<void> _checkMigration() async {
    var checkPhone = _phoneNumberController.text.substring(0, 1);
    if (_phoneNumberController.text.substring(0, 1) == '0') {
      checkPhone = dropdownValue +
          _phoneNumberController.text
              .substring(1, _phoneNumberController.text.length)
              .toString();
    } else {
      checkPhone = dropdownValue + _phoneNumberController.text.toString();
    }

    final phoneMigration = await FirebaseFirestore.instance
        .collection('migration')
        .where('phone_number', isEqualTo: checkPhone)
        .get();

    if (phoneMigration.docs.isNotEmpty) {
      Navigator.pushReplacement(
          context, MaterialPageRoute(builder: (context) => AlreadyMigrate()));
    }
  }


  /// Sends the code to the specified phone number.
  Future<void> _sendCodeToPhoneNumber() async {
    if (_phoneNumberController.text.substring(0, 1) == '0') {
      phoneNumber = dropdownValue +
          _phoneNumberController.text
              .substring(1, _phoneNumberController.text.length)
              .toString();
      print(phoneNumber);
    } else {
      phoneNumber = dropdownValue + _phoneNumberController.text.toString();
    }
    await _checkMigration();
    final result =
    await FirebaseFirestore.instance.collection('switch').doc('OTP').get();
    if (result.data()!['status'] == true) {
      roundSMS = providerSMS.Firebase;
      if (roundSMS == providerSMS.Firebase) {
        final PhoneVerificationCompleted verificationCompleted =
            (AuthCredential phoneAuthCredential) {
          print('verified');
          _auth.signInWithCredential(phoneAuthCredential).then((value) async {
            // TODO :: CHECK MIGRATE
            bool migrateStatus = await checkMigrate();
            if(migrateStatus) {
              Navigator.pushReplacement(
                  context, MaterialPageRoute(builder: (context) => AlreadyMigrate()));
            }else {
              Navigator.push(
                  context,
                  EnterExitRoute(
                      exitPage: SING_IN(),
                      enterPage: SetPin(
                          refCode: 'no',
                          firstName: '',
                          lastName: '',
                          checkIf: '',
                          secret: "LikeWallet",
                          roundSMS: roundSMS,
                          codeVerify: '',
                          phoneNumber: phoneNumber,
                          pinAgain: false)));
            }
            // TODO :: CHECK MIGRATE
          }).then((value) => setState(() => _saving = false));
        };

        final PhoneVerificationFailed verificationFailed =
            (FirebaseAuthException authException) {
          setState(() {
            _saving = false;
          });
          roundSMS = providerSMS.Twilio;
          setState(() {
            showColoredToast(
                AppLocalizations.of(context)!.translate('otp_failed'));
            print(
                'Phone number verification failed. Code: ${authException.code}. Message: ${authException.message}');
          });
        };

        final PhoneCodeSent codeSent =
            (String? verificationId, [int? forceResendingToken]) async {
          this.verificationId = verificationId.toString();
          print("code sent to " + phoneNumber.toString());

          Navigator.pushReplacement(
              context,
              CupertinoPageRoute(
                  builder: (context) => OTP_PAGE(
                    auth: _auth,
                    verificationId: verificationId,
                    roundSMS: roundSMS,
                    refCode: 'no',
                    phoneNumber: phoneNumber,
                    prefixNumber: dropdownValue,
                  ))).then((value) => setState(() => _saving = false));
        };

        final PhoneCodeAutoRetrievalTimeout codeAutoRetrievalTimeout =
            (String verificationId) {
          this.verificationId = verificationId;
          print("time out");
          setState(() => _saving = false);
//          ไม่เปลี่ยน Provider ยกเลิกแบบ Middle เพราะไม่ปลอดภัยหลังจาก ยกเลิก Secret
        };
//          roundSMS = providerSMS.Firebase;
        try {
          await FirebaseAuth.instance.verifyPhoneNumber(
              phoneNumber: phoneNumber,
              timeout: const Duration(seconds: 5),
              verificationFailed: verificationFailed,
              codeSent: codeSent,
              codeAutoRetrievalTimeout: codeAutoRetrievalTimeout,
              verificationCompleted: verificationCompleted);
        } catch (e) {
          setState(() => _saving = false);
          showShortToastColor(
              "Failed to Verify Phone Number: ${e}", Colors.red);
        }
        //ส่งด้วย twilio
      }
    } else {
      roundSMS = providerSMS.Twilio;
      // if (phoneNumber.substring(0, 3) == '+66') {
      //   var url = Uri.https(env.apiUrl, '/authNoFirebaseWithMKT');
      //   try {
      //     var response =
      //         await http.post(url, body: {'phone_number': phoneNumber});
      //     print('Response status: ${response.statusCode}');
      //     print('Response body: ${response.body}');
      //
      //     if (response.statusCode == 200) {
      //       var body = json.decode(response.body);
      //       //success register and sign in
      //       //to confirm
      //       setState(() => _saving = false);
      //
      //       Navigator.pushReplacement(
      //           context,
      //           CupertinoPageRoute(
      //               builder: (context) => ConfirmOTPMKT(
      //                     roundSMS: roundSMS,
      //                     otpToken: body['result']['token'],
      //                     phoneNumber: phoneNumber,
      //                     refCode: body['result']['ref_code'],
      //                     prefixNumber: dropdownValue,
      //                     checkIf: 'no',
      //                   )));
      //     } else {
      //       setState(() => _saving = false);
      //       showShortToast(
      //           AppLocalizations.of(context)!.translate('otp_failed'));
      //       //register failed
      //     }
      //   } catch (e) {
      //     setState(() => _saving = false);
      //     showShortToast("Failed to Verify Phone Number: ${e}");
      //   }
      // }
      //เลือกส่งของต่างประเทศ
      // if ((phoneNumber.substring(0, 4) == '+855' ||
      //         phoneNumber.substring(0, 4) == '+856') &&
      //     roundSMS == providerSMS.Twilio) {
      //   print('Sender with twilio');
      var url = Uri.https(env.apiUrl, '/authNoFirebase');
      try {
        var response =
        await http.post(url, body: {'phone_number': phoneNumber});
        print('Response status: ${response.statusCode}');
        print('Response body: ${response.body}');
        var body = json.decode(response.body);
        if (body['statusCode'] == 200) {
          //success register and sign in
          //to confirm
          setState(() => _saving = false);

          Navigator.pushReplacement(
              context,
              CupertinoPageRoute(
                  builder: (context) => OTP_PAGE(
                    roundSMS: roundSMS,
                    phoneNumber: phoneNumber,
                    refCode: 'no',
                    prefixNumber: dropdownValue,
                    checkIf: 'no',
                  )));
        } else {
          setState(() => _saving = false);
          showShortToast(AppLocalizations.of(context)!.translate('otp_failed'));
          //register failed
        }
      } catch (e) {
        setState(() => _saving = false);
        showShortToast("Failed to Verify Phone Number: ${e}");
      }
    }
    // }

    // }
    // else {
    //   setState(() => _saving = false);
    //   showShortToast(AppLocalizations.of(context)!.translate('prefixzero'));
    // }
  }
}