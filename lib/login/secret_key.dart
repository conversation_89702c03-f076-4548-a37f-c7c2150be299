
//import 'package:flutter/material.dart';
//import 'package:flutter/cupertino.dart';
//import 'package:likewallet/libraryman/app_local.dart';
//import 'package:likewallet/screen_util.dart';
//
//import 'package:likewallet/libraryman/custom_loading.dart';
//
//class secretKey extends StatefulWidget {
//  final String checkIf;
//  secretKey({this.checkIf});
//  _secretKey createState() => new _secretKey();
//}
//
//class _secretKey extends State<secretKey> {
//  _secretKey({this.checkIf});
//  final String checkIf;
//  @override
//  void initState() {
//    // TODO: implement initState
//    super.initState();
//  }
//
//  @override
//  Widget build(BuildContext context) {
//    return new Scaffold(
//      resizeToAvoidBottomPadding: false,
//      body: new Container(
//        width: double.infinity,
//        decoration: BoxDecoration(
//            image: DecorationImage(
//          image: AssetImage('assets/image/back.png'),
//          fit: BoxFit.cover,
//        )),
//        child: resetdetail(context),
//      ),
//    );
//  }
//
//  Widget resetdetail(context) {
//    return new Container(
//      child: new Column(
//        children: <Widget>[
//          SizedBox(
//            height: MediaQuery.of(context).size.height * 0.17,
//          ),
//          Center(
//            child: Container(
//                alignment: Alignment.center,
//                height: MediaQuery.of(context).size.height * 0.05,
//                width: MediaQuery.of(context).size.width * 0.7,
//                child: Column(
//                  children: <Widget>[
//                    Text(
//                      AppLocalizations.of(context)!.translate('secret_title1'),
//                      style: TextStyle(
//                          fontFamily:
//                              AppLocalizations.of(context)!.translate('font1'),
//                          fontSize: MediaQuery.of(context).size.height *
//                              Screen_util("height", 45),
//                          color: Color(0xff707071)),
//                    ),
//                  ],
//                )),
//          ),
//          SizedBox(
//            height: MediaQuery.of(context).size.height * 0.02,
//          ),
//          Center(
//            child: new Container(
//              alignment: Alignment.center,
//              height: MediaQuery.of(context).size.height * 0.07,
//              width: MediaQuery.of(context).size.width * 0.9,
//              decoration: BoxDecoration(
//                color: Color(0xff141322),
//                border: Border.all(
//                  color: Color(0xff0FE8D8),
//                  width: 0.3,
//                ),
//                borderRadius: BorderRadius.all(Radius.circular(5.0)),
//              ),
//              child: Row(
//                children: <Widget>[
//                  new Container(
//                    alignment: Alignment.center,
//                    height: MediaQuery.of(context).size.height * 0.05,
//                    width: MediaQuery.of(context).size.width * 0.65,
////                color: Colors.blue,
//                    child: TextField(
//                      style: TextStyle(color: Colors.white),
//                      decoration: InputDecoration(
//                        contentPadding: EdgeInsets.only(left: 10),
//                        hintText: AppLocalizations.of(context)
//                            .translate('รหัสลับของท่าน'),
//                        hintStyle: TextStyle(
//                          fontSize: MediaQuery.of(context).size.height *
//                              Screen_util("height", 45),
//                          color: Colors.white,
//                          fontFamily:
//                              AppLocalizations.of(context)!.translate('font1'),
//                          fontWeight: FontWeight.normal,
//                        ),
//                        border: InputBorder.none,
//                      ),
//                      keyboardType: TextInputType.text,
//                    ),
//                  ),
//                ],
//              ),
//            ),
//          ),
//          SizedBox(
//            height: MediaQuery.of(context).size.height * 0.05,
//          ),
//          Center(
//            child: new Container(
//              alignment: Alignment.center,
//              height: MediaQuery.of(context).size.height * 0.07,
//              width: MediaQuery.of(context).size.width * 0.5,
//              child: ButtonTheme(
//                minWidth: MediaQuery.of(context).size.width / 1.2,
//                height: MediaQuery.of(context).size.height,
//                child: new FlatButton(
//                    shape: new RoundedRectangleBorder(
//                      borderRadius: new BorderRadius.circular(8.0),
//                    ),
//                    disabledColor: Color(0xff00F1E0),
//                    color: Color(0xff00F1E0),
//                    onPressed: () => {
//                          Navigator.pushNamedAndRemoveUntil(
//                              context, '/home', ModalRoute.withName('/'))
//                        },
//                    child: new Text(
//                      AppLocalizations.of(context)!.translate('secret_button'),
//                      style: TextStyle(
//                          color: Color(0xff000000).withOpacity(0.75),
//                          fontFamily:
//                              AppLocalizations.of(context)!.translate('font1'),
//                          fontSize: MediaQuery.of(context).size.height *
//                              Screen_util("height", 49),
//                          fontWeight: FontWeight.bold),
//                    )),
//              ),
//            ),
//          ),
//          SizedBox(
//            height: MediaQuery.of(context).size.height * 0.07,
//          ),
//          Center(
//            child: Container(
//                alignment: Alignment.center,
//                height: MediaQuery.of(context).size.height * 0.05,
//                width: MediaQuery.of(context).size.width * 0.7,
//                child: Column(
//                  children: <Widget>[
//                    Text(
//                      AppLocalizations.of(context)!.translate('secret_button'),
//                      style: TextStyle(
//                          fontFamily:
//                              AppLocalizations.of(context)!.translate('font1'),
//                          fontSize: MediaQuery.of(context).size.height *
//                              Screen_util("height", 45),
//                          color: Color(0xff707071)),
//                    ),
//                  ],
//                )),
//          ),
//        ],
//      ),
//    );
//  }
//}