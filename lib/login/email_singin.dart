import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:likewallet/Theme.dart';

import 'package:likewallet/login/forgetPassword.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/screen_util.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:likewallet/libraryman/secret_pass.dart';
import 'package:likewallet/login/forgetPassword.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/login/sing_in.dart';
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import '../ImageTheme.dart';

class EMAIL_SINGIN extends StatefulWidget {
  _EMAIL_SINGIN createState() => new _EMAIL_SINGIN();
}

class _EMAIL_SINGIN extends State<EMAIL_SINGIN> {
  late String dropdownValue;

  TextEditingController email = TextEditingController();
  TextEditingController password = TextEditingController();
  bool _saving = false;
  late BaseAuth auth;

  void showShortToast(msg, Color color) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_SHORT,
        backgroundColor: color,
        textColor: Colors.white);
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    auth = Auth();
  }

  TextEditingController controller = TextEditingController();
  buildForPhone() {
    return Scaffold(
        body: Stack(children: <Widget>[
      background_image(context),
      SingleChildScrollView(
        child: new Column(
//          mainAxisSize: MainAxisSize.max,
          children: <Widget>[
            new SizedBox(
              height: mediaQuery(context, 'height', 136),
            ),
            _TEXT1_Phone(context),
            new SizedBox(
              height: MediaQuery.of(context).size.height * 0.05,
            ),
            _InputPhone_Phone(),
            new SizedBox(
              height: MediaQuery.of(context).size.height * 0.05,
            ),
            _ButtonContinue_Phone(),
            new SizedBox(
              height: MediaQuery.of(context).size.height * 0.02,
            ),
          ],
        ),
      ),
    ]));
  }

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return ModalProgressHUD(
        opacity: 0.1,
        inAsyncCall: _saving,
        progressIndicator: CustomLoading(),
        child: Scaffold(body: buildForPhone()));
  }

  Widget _TEXT1_Phone(context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Container(
          padding: EdgeInsets.only(
            left: mediaQuery(context, 'width', 35),
          ),
          width: MediaQuery.of(context).size.width / 4,
          child: backButton(context, LikeWalletAppTheme.gray1),
        ),
        Container(
            width: MediaQuery.of(context).size.width / 2,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                new Image.asset(
                  LikeWalletImage.like_point,
                  height: mediaQuery(context, 'height', 180),
                ),
                SizedBox(
                  width: mediaQuery(context, 'height', 30),
                ),
                new Image.asset(
                  LikeWalletImage.like_point_text,
                  height: mediaQuery(context, 'height', 38.9),
                ),
              ],
            )),
        Container(
          width: MediaQuery.of(context).size.width / 4,
        ),
      ],

//        color: Colors.green,
    );
  }

  Widget _InputPhone_Phone() {
    return new Container(
      alignment: Alignment.center,
//      height:MediaQuery.of(context).size.height*0.07,
//      width:MediaQuery.of(context).size.width*0.9,
      child: Form(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            new Container(
              decoration: BoxDecoration(
                color: LikeWalletAppTheme.bule2,
                border: Border.all(
                  color: LikeWalletAppTheme.bule1,
                  width: mediaQuery(context, 'height', 0.3),
                ),
                borderRadius: BorderRadius.all(Radius.circular(5.0)),
              ),
              alignment: Alignment.center,
              height: mediaQuery(context, 'height', 156),
              width: mediaQuery(context, 'width', 930),
//                color: Colors.blue,
              child: TextFormField(
                controller: email,
                style: TextStyle(
                    fontSize: mediaQuery(context, 'height', 47),
                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
                    color: LikeWalletAppTheme.white),
                decoration: InputDecoration(
//                  focusedBorder: OutlineInputBorder(
//                      borderSide: BorderSide(
//                          color: LikeWalletAppTheme.bule1,
//                          width: mediaQuery(context, 'width', 1))),
                  contentPadding: EdgeInsets.only(
                    left: MediaQuery.of(context).size.width * 0.03,
                  ),
                  hintText: AppLocalizations.of(context)!
                            .translate('singinEmail_name'),
                  hintStyle: TextStyle(
                      fontSize: mediaQuery(context, 'height', 47),
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      color: LikeWalletAppTheme.white),
                  border: InputBorder.none,
//                  enabledBorder: OutlineInputBorder(
//                      borderSide: BorderSide(
//                          width: mediaQuery(context, 'width', 0.3),
//                          color: LikeWalletAppTheme.bule1)),
                  hoverColor: Colors.white,
                  disabledBorder: InputBorder.none,
                  focusColor: Colors.white,
                  alignLabelWithHint: true,
                  fillColor: Colors.white,
                ),
                keyboardType: TextInputType.text,
              ),
            ),
            Padding(
              padding: EdgeInsets.only(
                top: MediaQuery.of(context).size.width * 0.05,
              ),
            ),
            new Container(
              decoration: BoxDecoration(
                color: LikeWalletAppTheme.bule2,
                border: Border.all(
                  color: LikeWalletAppTheme.bule1,
                  width: mediaQuery(context, 'height', 0.3),
                ),
                borderRadius: BorderRadius.all(Radius.circular(5.0)),
              ),
              alignment: Alignment.center,
              height: mediaQuery(context, 'height', 156),
              width: mediaQuery(context, 'width', 930),
//                color: Colors.blue,
              child: TextFormField(
                obscureText: true,
                controller: password,
                style: TextStyle(color: Colors.white),
                decoration: InputDecoration(
//                  focusedBorder: OutlineInputBorder(
//                      borderSide: BorderSide(
//                          color: LikeWalletAppTheme.bule1,
//                          width: mediaQuery(context, 'width', 1))),
                  contentPadding: EdgeInsets.only(
                    left: MediaQuery.of(context).size.width * 0.03,
                  ),
                  hintText: AppLocalizations.of(context)!
                            .translate('singinEmail_lastname'),
                  hintStyle: TextStyle(
                      fontSize: mediaQuery(context, 'height', 47),
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      color: LikeWalletAppTheme.white),
                  border: InputBorder.none,
//                  enabledBorder: OutlineInputBorder(
//                      borderSide: BorderSide(
//                          width: mediaQuery(context, 'width', 0.3),
//                          color: LikeWalletAppTheme.bule1)),
                  hoverColor: Colors.white,
                  disabledBorder: InputBorder.none,
                  focusColor: Colors.white,
                  alignLabelWithHint: true,
                  fillColor: Colors.white,
                ),
                keyboardType: TextInputType.text,
              ),
            ),
            //ปุ่มลืมรหัสผ่าน
            TextButton(
              onPressed: () {
                Navigator.pushNamed(context, '/forgetPassword');
              },
              child: new Container(
                alignment: Alignment.bottomRight,
                height: mediaQuery(context, 'height', 50),
                width: mediaQuery(context, 'width', 930),
                child: new Text(
                    AppLocalizations.of(context)!
                            .translate('singinEmail_forget'),
                    style: TextStyle(
                        fontSize: MediaQuery.of(context).size.height *
                            Screen_util("height", 36),
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                        color: Colors.white70)),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _ButtonContinue_Phone() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: <Widget>[
        new Container(
          alignment: Alignment.center,
          height: mediaQuery(context, 'height', 132),
          width: mediaQuery(context, 'width', 930),
          child: ButtonTheme(
            minWidth: mediaQuery(context, 'width', 930),
            height: mediaQuery(context, 'height', 132),
            child:TextButton(
              style: ButtonStyle(
                shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(5.0),
                  ),
                ),
                backgroundColor: MaterialStateProperty.resolveWith<Color>(
                      (Set<MaterialState> states) {
                    if (states.contains(MaterialState.disabled)) {
                      return LikeWalletAppTheme.bule1; // Disabled color
                    }
                    return LikeWalletAppTheme.bule1; // Regular color
                  },
                ),
              ),
              onPressed: () {
                if (!mounted) return;
                setState(() {
                  _saving = true;
                });
                try {
                  auth
                      .singInWithEmailAndPassword(email.text.trim(), password.text.trim())
                      .then((onValue) {
                    print(onValue!.email);
                    if (!mounted) return;
                    setState(() {
                      _saving = false;
                    });
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => SecretPass(
                          checkIf: 'login',
                          roundSMS: providerSMS.Email,
                          phoneNumber: onValue.phoneNumber,
                        ),
                      ),
                    );
                  }).catchError((error) {
                    if (!mounted) return;
                    setState(() {
                      _saving = false;
                    });
                    showShortToast(
                      AppLocalizations.of(context)!.translate('is_wrong'),
                      LikeWalletAppTheme.red,
                    );
                  });
                } catch (e) {
                  if (!mounted) return;
                  setState(() {
                    _saving = false;
                  });
                  showShortToast(
                    AppLocalizations.of(context)!.translate('is_wrong'),
                    LikeWalletAppTheme.red,
                  );
                }
              },
              child: Text(
                AppLocalizations.of(context)!.translate('singinEmail_button'),
                style: TextStyle(
                  color: Color(0xff000000).withOpacity(0.75),
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  fontSize: MediaQuery.of(context).size.height * Screen_util("height", 49),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
