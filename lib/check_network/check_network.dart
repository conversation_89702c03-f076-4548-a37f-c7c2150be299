import 'package:connectivity_plus/connectivity_plus.dart';

abstract class CheckNetWorkService {
  Future<bool> getStatusNetwork();
}

class NetWorkService implements CheckNetWorkService {
  @override
  Future<bool> getStatusNetwork() async {
    print('fsfssf');
    var connectivityResult = await (Connectivity().checkConnectivity());
    if (connectivityResult == ConnectivityResult.none) {
      return true;
    } else {
      return false;
    }
  }
}
