import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/screen_util.dart';
import 'package:shared_preferences/shared_preferences.dart';

class HowToLockLike extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return __HowToLockLike();
  }
}

class __HowToLockLike extends State<HowToLockLike> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        alignment: Alignment.center,
        children: <Widget>[
          background(context),
          Image.asset(
            AppLocalizations.of(context)!.translate('howto_image_lock'),
            fit: BoxFit.cover,
            width: mediaQuery(context, 'width', 1080),
          ),
          Positioned(
              top: mediaQuery(context, 'height', 2000),
              child: GestureDetector(
                onTap: () {
                  Navigator.of(context).pop();
                  setHowto();
                },
                child: Image.asset(
                  AppLocalizations.of(context)!.translate('howto_image_gotit'),
                  width: mediaQuery(context, 'width', 254),
                ),
              ))
        ],
      ),
    );
  }

  setHowto() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    pref.setBool("setFirstTime", true);
  }
}
