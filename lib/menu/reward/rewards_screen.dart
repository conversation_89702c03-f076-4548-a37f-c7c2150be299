import 'dart:convert';
import 'dart:ui';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/Theme.dart';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:likewallet/bank/confirm_transection.dart';
import 'package:likewallet/close_maintenance.dart';
import 'package:likewallet/main.dart';
import 'package:likewallet/menu/LockLIKE_New.dart';
import 'package:likewallet/middleware//check_maintenance/check_maintenance.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:likewallet/libraryman/ethcontractv2.dart';
import 'package:likewallet/menu/reward/reward_blinking.dart';
import 'package:likewallet/menu/reward/reward_service.dart';
import 'package:likewallet/middleware/getLanguage.dart';
import 'package:likewallet/model/button.dart';
import 'package:likewallet/model/pageStatus.dart';
import 'package:likewallet/routes.dart';
import 'package:likewallet/screen/home.dart';
import 'package:likewallet/screen/navigationbar/Info/info_screen.dart';
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:lottie/lottie.dart';

import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'dart:math' as math;
import 'package:likewallet/libraryman/ethcontract.dart';
import 'package:likewallet/libraryman/address_service.dart';
import 'package:likewallet/libraryman/configuration_service.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:http/http.dart' as http;
import 'package:likewallet/app_config.dart';
import 'package:likewallet/setmodel/next_rewards_model.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:intl/intl.dart' as formatIntl;
import 'package:likewallet/screen/NavigationBar.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as riverpod;

class Rewards extends StatefulWidget {
  _Rewards createState() => new _Rewards();
}

enum statusClaim { INACTIVE, ACTIVE }

class _Rewards extends State<Rewards> with TickerProviderStateMixin {
  final f = new formatIntl.NumberFormat("###,###");
  final fdecimal = new formatIntl.NumberFormat("###,###.#####");
  var day = DateTime.now();
  late RewardService serviceReward;
  late BaseAuth auth;
  late String dropdownValue;
  double nextRewards = 0;
  double upcomingRewards = 0;
  double rewards = 0.0;
  double totalLocked = 0.0;
  late IAddressService addressService;
  late IConfigurationService configETH;
  late BaseETHV2 eth;
  statusClaim isChaim = statusClaim.INACTIVE;
  String addressETH = '';
  late String mnemonic;
  late String getPK;
  bool _saving = false;
  bool claimable = false;
  bool claimLockable = false;
  int tabMenu = 0;
  int buttonClaim = 0;
  int buttonClaimAndLock = 0;
  int countLoop = 0;
  late Timer timer;
  double Income = 0;
  late List<NextRewards> listRewards;
  String totalLock = '';
  String phone = '';
  String address = '';
  String rewardTime = '0';
  String image =
      'https://s3-ap-southeast-1.amazonaws.com/storage-pkg/icon/icon163091206951_Animated%20Loop_Rewards%202.mp4.lottie.json';
  bool awaitUnlock = false;
  late CheckAbout checkAbout;
  late OnLanguage language;
  late AnimationController _animationController;
  late AnimationController controller;
  bool youLock = false;

  getReward() async {
    await FirebaseFirestore.instance
        .collection('reward_time')
        .doc('oBAkQv2YMT0lfg9ku2Fw')
        .get()
        .then((DocumentSnapshot<Map<String, dynamic>> ds) {
      setState(() {
        rewardTime = ds.data()!['time'];
      });
      print("เวลาเเจก :" + ds.data()!['time']);
    });
    FirebaseFirestore.instance
        .collection('imageURL')
        .doc('page')
        .collection('reward_page')
        .doc('reward_image_head')
        .get()
        .then(
          (value) => setState(() => image = value.data()!['image']),
        );
  }

  changeMenu(_number) async {
    setState(() {
      tabMenu = _number;
    });
  }

  getPhone() {
    auth.getCurrentUser().then((decodedToken) async {
      SharedPreferences pref = await SharedPreferences.getInstance();
      configETH = new ConfigurationService(pref);
      phone = decodedToken!.phoneNumber.toString();
      address = configETH.getAddress().toLowerCase();
    });
  }

  getButtonStatus() async {
    try {
      print('getButtonStatus' + context.read(tierLevel).state);
      final res = await FirebaseFirestore.instance
          .collection('tierController')
          .doc('controller')
          .collection(context.read(tierLevel).state)
          .doc('button')
          .get();
      final data = ButtonStatus.fromJson(res.data()!);
      setState(() {
        claimable = data.claim;
        claimLockable = data.claimLock;
      });
    } catch (e) {
      print(e);
    }
  }

  @override
  void initState() {
    super.initState();
    auth = Auth();
    checkAbout = OnCheckAbout();
    language = CallLanguage();
    checkFirst();
    // eth = EthContractV2();
    // eth.getGas().then((value) => print("gas $value "));
  }

  checkFirst() async {
    PageMaintenance statusPage = await checkAbout.checkTierPermission(
        tierLevel: context.read(tierLevel).state, page: 'reward');
    if (statusPage.status == 'active') {
      //เริ่มทำงาน
      print('active');
      await getButtonStatus();
      setInitState();
    } else {
      //ปิด maintenance
      print('inactive');
      final lang = await language.getLanguage();
      final title = await checkAbout.selectLanguage(
          language: lang, detail: statusPage.title);
      final detail = await checkAbout.selectLanguage(
          language: lang, detail: statusPage.detail);
      final detailTime = await checkAbout.selectLanguage(
          language: lang, detail: statusPage.detail_time);
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
            builder: (context) => CloseMaintenance(
                  title: title,
                  detail: detail,
                  detailTime: detailTime,
                  url: statusPage.url,
                )),
      );
    }
  }

  // checkFirst() async {
  //   bool result = await checkAbout.firstStep(
  //       docNameMain: 'reward',
  //       docNameCon: 'airdrop',
  //       contract: env.contractAirdrop,
  //       context: context);
  //   if (result) {
  //     setInitState();
  //   } else {
  //     print('เกิดข้อผิดพลาด');
  //   }
  // }

  setInitState() {
    getReward();
    _animationController =
        new AnimationController(vsync: this, duration: Duration(seconds: 1));
    _animationController.repeat();
    controller =
        AnimationController(vsync: this, duration: Duration(seconds: 10));

    new Future.delayed(new Duration(milliseconds: 500), () {
      eth = new EthContractV2();
      setInit();
    });

    timer = Timer.periodic(new Duration(seconds: 1), (timerx) {
      if (countLoop == 0) {
        if (timerOut <= 0) {
          countLoop = 1;
          timer.cancel();
        }
      }
    });
    getPhone();
  }

  @override
  void dispose() {
    controller.dispose();
    _animationController.dispose();
    timer.cancel();
    super.dispose();
  }

  void showColoredToast(msg, colors) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_LONG,
        backgroundColor: colors,
        textColor: Colors.white);
  }

  void showShortToast(msg) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_SHORT,
        backgroundColor: Colors.cyan,
        textColor: Colors.white);
  }

  setInit() async {
    setState(() {
      _saving = true;
    });
    var url = Uri.https(env.apiUrl, '/nextRewards');
    var response = await http.post(url, body: {'application': 'LIKEWALLET'});

    print('Response status: ${response.statusCode}');
    print('Response body: ${response.body}');
    var body = jsonDecode(response.body);

    //รอบการแจกถัดไป
    if (body['statusCode'] == 200) {
      print(body["result"]);
      final body2 = NextRewards.fromJson(body["result"]);

      final currentTime = DateTime.now();

      //คำนวณเวลา
      final diffTime = (((double.parse(body2.round_time) / 1000) -
          (currentTime.millisecondsSinceEpoch + 25200000) / 1000));

      print(double.parse(body2.round_time));
      print(currentTime.millisecondsSinceEpoch + 25200);
      int valueSend = diffTime.toString().indexOf('.');
      print(diffTime.toString().substring(0, valueSend));
      if (!mounted) return;
      setState(() {
        totalLocked = double.parse(body2.total_locked);
        nextRewards = double.parse(body2.rewards);
        Income = nextRewards / totalLocked * 365 * 100;
        controller = AnimationController(
          vsync: this,
          duration: Duration(
              seconds:
                  int.parse(diffTime.toString().substring(0, valueSend)) <= 0.0
                      ? 0
                      : int.parse(diffTime.toString().substring(0, valueSend))),
        );
        {
          if (controller.isAnimating)
            controller.stop();
          else {
            controller.reverse(
                from: controller.value == 0.0 ? 1.0 : controller.value);
          }
        }
        ;
      });

//      final body2 = body["result"];
//      listRewards = body2.map<NextRewards>((json) => NextRewards.fromJson(json)).toList();
//      listRewards.forEach((f) {
//        setState(() {
//          nextRewards = double.parse(f.total_locked);
//          upcomingRewards = double.parse(f.rewards);
//        });
//
//      });

      SharedPreferences pref = await SharedPreferences.getInstance();
      configETH = new ConfigurationService(pref);
      addressService = new AddressService(configETH);
      mnemonic = pref.getString('seed') ?? '';

//    getPK = addressService.getPrivateKey(mnemonic);
      getPK = await configETH.getPrivateKey();

      setState(() {
        addressETH = configETH.getAddress();
      });

//    addressService.getPublicAddress(getPK).then((address) {
      print(addressETH);
      pref.setString('ethAddr', addressETH.toString());
      eth.getUnlockDate(address: addressETH).then((data) {
        print('unlock' + data.toString());
        print('unlock' + data[3]);
        if (data[3] == '1') {
          setState(() => youLock = true);
        }
      });
      //check lock time
      eth.getDepositTime(address: addressETH).then((timeLock) {
        print('time lock : ' + timeLock);
        print(
            'round_time : ' + (int.parse(body2.round_time) / 1000).toString());
        if ((int.parse(body2.created_time) / 1000) < int.parse(timeLock)) {
          print('time next');
          //upcomingRewards = 0;
          eth.getBalanceLock(address: addressETH).then((lock) {
            print('lock: ' + lock.toString());
            print("totalLocked: " + totalLocked.toString());
            print('nextRewards: ' + nextRewards.toString());

            if (!mounted) return;
            setState(() {
              upcomingRewards = lock / totalLocked * nextRewards;
            });
          });
        } else if (int.parse(timeLock) == 0) {
          if (!mounted) return;
          setState(() {
            upcomingRewards = 0;
          });
        } else {
          //check balance of lock
          print('time get rewards');
          eth.getBalanceLock(address: addressETH).then((lock) {
            print('lock: ' + lock.toString());
            print("totalLocked: " + totalLocked.toString());
            print('nextRewards: ' + nextRewards.toString());

            if (!mounted) return;
            setState(() {
              upcomingRewards = lock / totalLocked * nextRewards;
            });
          });
        }
      });
      //check status claim
      eth.checkClaim(address: addressETH).then((lastTime) {
        print('lastTime ' + lastTime.toString());
        //check current round airdrop
        eth.getRound().then((roundAirdrop) {
          print('roundAirdrop ' + roundAirdrop.toString());
          if (int.parse(lastTime) >= int.parse(roundAirdrop)) {
            eth.isWithdraw(address: addressETH).then((dataBack) {
              print('dataBack ' + dataBack.toString());
              if (dataBack == 1) {
                if (!mounted) return;
                setState(() {
                  isChaim = statusClaim.INACTIVE;
                  rewards = 0.0;
                  _saving = false;
                });
              } else {
                if (!mounted) return;
                setState(() {
                  isChaim = statusClaim.ACTIVE;
                  _saving = false;
                });
              }
            });
          } else {
            //check reward
            //check ว่ามีการถอนไว้หรือเปล่า
            eth.isWithdraw(address: addressETH).then((dataBack) {
              if (dataBack == 1) {
                setState(() {
                  isChaim = statusClaim.INACTIVE;
                  rewards = 0.0;
                  _saving = false;
                });
              } else {
                eth.checkRewards(address: addressETH).then((balance) {
                  print("reward" + balance.toString());
                  if (!mounted) return;
                  setState(() {
                    rewards = double.parse(balance.toString());
                    _saving = false;
                  });
                });
                if (!mounted) return;
                setState(() {
                  isChaim = statusClaim.ACTIVE;
                });
              }
            });
          }
        });
      });

//    });getPublicAddress
      print(body2);
    }
  }

//claim
  _claimRewards() {
    eth.ClaimRewards(pk: getPK).then((tx) {
      print("tx: $tx");
      new Future.delayed(new Duration(seconds: 4), () {
        if (!mounted) return;
        setState(() {
          buttonClaim = 2;
          rewards = 0.0;
        });
        setInit();
        // _saving = false;
      });
    }).catchError((onError) {
      buttonClaim = 0;
    });
  }

//claim
  _claimRewardsAndLock() {
    eth.claimRewardsAndLock(pk: getPK).then((tx) {
      new Future.delayed(new Duration(seconds: 4), () {
        setState(() {
          if (!mounted) return;
          buttonClaimAndLock = 2;
          rewards = 0.0;
        });
        setInit();
      });
    }).catchError((onError) {
      buttonClaimAndLock = 0;
    });
  }

  int get timerOut {
    return (controller.duration! * controller.value).inMilliseconds;
  }

  get timerString {
    Duration duration = controller.duration! * controller.value;
    var inHours = (duration.inHours % 60).toString().padLeft(2, '0');
    var inMinutes = (duration.inMinutes % 60).toString().padLeft(2, '0');
    var inSeconds = (duration.inSeconds % 60).toString().padLeft(2, '0');
    var time = day.add(Duration(
        days: 0,
        hours: int.parse(inHours),
        minutes: int.parse(inMinutes),
        seconds: int.parse(inSeconds)));
    return '${time.hour.toString()}:${time.minute.toString()}';
  }

  TextEditingController addressText = TextEditingController();

  Future<String> getNewReward() async {
    // TODO: implement getNewReward
    var url = Uri.https(env.apiUrl, '/nextRewards');
    var response = await http.post(url, body: {'application': 'LIKEWALLET'});
    // // print('Response status: ${response.statusCode}');
    // // print('Response body: ${response.body}');
    //
    var body = jsonDecode(response.body);
    print(body['result']);
    return body;
  }

  @override
  Widget build(BuildContext context) {
    String messageUpcoming =
        AppLocalizations.of(context)!.translate('hourlyRewards_please');
    screenUtil(context);
    return ModalProgressHUD(
      inAsyncCall: _saving,
      opacity: 0.1,
      progressIndicator: CustomLoading(),
      child: Scaffold(
        backgroundColor: Color(0xFF161329),
        body: Stack(
          children: [
            // Positioned(
            //   top: 0,
            //   // child: gifHead(),
            // ),
            Positioned(
                top: mediaQuery(context, "height", 139.33),
                left: mediaQuery(context, "width", 0),
                child: backButton(context, LikeWalletAppTheme.gray)),
            Column(
              children: [
                SizedBox(height: 645.h),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 75.w),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Text(
                            AppLocalizations.of(context)!
                                .translate('hourlyRewards_title1'),
                            style: TextStyle(
                              fontFamily: AppLocalizations.of(context)!
                                  .translate('font1'),
                              fontSize: 39.h,
                              color: const Color(0xb252ffff).withOpacity(0.6),
                              letterSpacing: 1.17,
                              height: 1.0769230769230769,
                            ),
                            textAlign: TextAlign.left,
                          ),
                          SizedBox(
                            height: 11.h,
                          ),
                          Text(
                            f.format(nextRewards).toString(),
                            style: TextStyle(
                              fontFamily: AppLocalizations.of(context)!
                                  .translate('font2'),
                              fontSize: 47.h,
                              color: const Color(0xb252ffff).withOpacity(0.6),
                              letterSpacing: 1.17.w,
                            ),
                            textAlign: TextAlign.left,
                          ),
                        ],
                      ),
                      // Column(
                      //   crossAxisAlignment: CrossAxisAlignment.end,
                      //   mainAxisAlignment: MainAxisAlignment.end,
                      //   children: [
                      //     Text(
                      //       AppLocalizations.of(context)
                      //           .translate('hourlyRewards_coming'),
                      //       textAlign: TextAlign.right,
                      //       style: TextStyle(
                      //         fontFamily: AppLocalizations.of(context)
                      //             .translate('font1'),
                      //         fontSize: 39.h,
                      //         color: const Color(0xb252ffff),
                      //         letterSpacing: 1.17,
                      //         height: 1.0769230769230769,
                      //       ),
                      //     ),
                      //     SizedBox(
                      //       height: 28.h,
                      //     ),
                      //     Text(
                      //       isChaim == statusClaim.ACTIVE
                      //           ? upcomingRewards == 0.0
                      //               ? "0"
                      //               : '${fdecimal.format(upcomingRewards)}'
                      //           : "0",
                      //       style: TextStyle(
                      //         fontFamily: AppLocalizations.of(context)
                      //             .translate('font2'),
                      //         fontSize: 80.h,
                      //         color: const Color(0xb252ffff),
                      //         letterSpacing: 0,
                      //         fontWeight: FontWeight.w400,
                      //         height: 1.0769230769230769,
                      //       ),
                      //       textAlign: TextAlign.left,
                      //     ),
                      //   ],
                      // ),
                    ],
                  ),
                ),
                InkWell(
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => InfoScreen(
                          phone: phone,
                          address: address,
                        ),
                      ),
                    );
                  },
                  child: Container(
                    width: 60.w,
                    height: 70.h,
                    padding: EdgeInsets.only(top: 15.h, bottom: 18.h),
                    alignment: Alignment.topCenter,
                    child: SvgPicture.string(
                      '<svg viewBox="507.0 888.0 65.9 33.4" ><path transform="translate(242.47, 484.07)" d="M 330.4030151367188 405.9169921875 C 330.1220092773438 404.5660095214844 328.7980041503906 403.697998046875 327.4460144042969 403.97900390625 L 317.4020080566406 406.0679931640625 C 316.8819885253906 406.1759948730469 316.4100036621094 406.4469909667969 316.0539855957031 406.8420104980469 C 315.1300048828125 407.8680114746094 315.2130126953125 409.4490051269531 316.239013671875 410.3729858398438 L 319.2720031738281 413.10400390625 C 319.1499938964844 413.1849975585938 319.0320129394531 413.2730102539062 318.9230041503906 413.3770141601562 L 309.3269958496094 422.5969848632812 L 293.0740051269531 409.2040100097656 L 265.406005859375 432.9039916992188 C 264.3569946289062 433.8030090332031 264.2349853515625 435.3810119628906 265.1340026855469 436.4289855957031 C 265.6279907226562 437.0069885253906 266.3290100097656 437.3030090332031 267.0329895019531 437.3030090332031 C 267.6090087890625 437.3030090332031 268.18701171875 437.1050109863281 268.6589965820312 436.7009887695312 L 293.135986328125 415.7340087890625 L 309.5830078125 429.2860107421875 L 322.3880004882812 416.9830017089844 C 322.5880126953125 416.7900085449219 322.7380065917969 416.5679931640625 322.8599853515625 416.3349914550781 L 325.2550048828125 418.4909973144531 C 325.6499938964844 418.8469848632812 326.1489868164062 419.0679931640625 326.6780090332031 419.1210021972656 C 328.052001953125 419.2590026855469 329.2770080566406 418.2569885253906 329.4150085449219 416.8829956054688 L 330.4419860839844 406.677001953125 C 330.4679870605469 406.4230041503906 330.4549865722656 406.1669921875 330.4030151367188 405.9169921875 Z" fill="#0fe8d8" stroke="none" stroke-width="1" stroke-miterlimit="10" stroke-linecap="butt" /></svg>',
                      allowDrawingOutsideViewBox: true,
                    ),
                  ),
                ),
                Container(
                    height: mediaQuery(context, 'height', 1386),
                    child: Stack(
                      alignment: Alignment.topCenter,
                      children: [
                        Positioned(
                          bottom: 104.h,
                          width: 850.w,
                          child: Text(
                            AppLocalizations.of(context)!
                                .translate('hourlyRewards_detail'),
                            style: TextStyle(
                              fontFamily: AppLocalizations.of(context)!
                                  .translate('font1'),
                              fontSize: 34.h,
                              color: const Color(0xff7b75fd),
                              letterSpacing: 1.02.w,
                              fontWeight: FontWeight.w300,
                            ),
                            textAlign: TextAlign.left,
                          ),
                        ),
                        Positioned(
                          top: 111.h,
                          child: body(),
                        ),
                        Positioned(
                          top: 0,
                          child: Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(50.0),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.16),
                                    offset: Offset(0, 20.sp),
                                    blurRadius: 35.sp,
                                  ),
                                ],
                              ),
                              child: time()),
                        ),
                      ],
                    )),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget body() {
    return Container(
        width: 1010.0.w,
        height: 1274.0.h,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(33.0.w),
          gradient: LinearGradient(
            begin: Alignment(0.0, -1.0),
            end: Alignment(0.0, 1.0),
            colors: [const Color(0xff273352), const Color(0x00273352)],
            stops: [0.0, 1.0],
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0x1a000000),
              offset: Offset(0, -3),
              blurRadius: 12,
            ),
          ],
        ),
        child: Column(
          children: [
            SizedBox(
              height: 216.h,
            ),
            Text(
              AppLocalizations.of(context)!
                  .translate('hourlyRewards_your_claim'),
              style: TextStyle(
                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                fontSize: 44.h,
                color: const Color(0xff0fe8d8),
                letterSpacing: 1.3199999999999998.w,
                fontWeight: FontWeight.w300,
              ),
              textAlign: TextAlign.center,
            ),
            Text(
              fdecimal.format(rewards).toString(),
              style: TextStyle(
                fontFamily: 'Roboto',
                fontSize: 90.h,
                color: const Color(0xff52ffff),
                fontWeight: FontWeight.w500,
                height: 1.5666666666666667,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(
              height: 95.h,
            ),
            // //TODO เเสดงปุ่มยังเคลมไม่ได้
            isChaim == statusClaim.INACTIVE
                ? _buttonUnClaim()
                : rewards == 0
                    ? _buttonUnClaim()
                    : Container(),
            SizedBox(
              height: 30.h,
            ),
            //TODO เเสดงรายได้รายปี
            buttonClaimAndLock == 2
                ? MyBlinkingButton(
                    Income: Income,
                  )
                : Container(),
            //TODO เเสดงปุ่มเคลม
            buttonClaimAndLock == 2
                ? Container()
                : isChaim == statusClaim.INACTIVE
                    ? Container()
                    : rewards == 0
                        ? Container()
                        : _buttonClaim(),
            SizedBox(
              height: 34.h,
            ),
            //TODO เเสดงปุ่มเคลมเเละล็อค
            buttonClaim == 2
                ? Container()
                : isChaim == statusClaim.INACTIVE
                    ? Container()
                    : rewards == 0
                        ? Container()
                        : youLock
                            ? Container()
                            : _buttonClaimAndLock(),
            //TODO เเสดงรายได้รายปี
            buttonClaim == 2
                ? MyBlinkingButton(
                    Income: Income,
                  )
                : Container(),
          ],
        ));
  }

  Widget _buttonUnClaim() {
    return Container(
      alignment: Alignment.center,
      child: ButtonTheme(
        minWidth: 884.w,
        height: 132.h,
        child: TextButton(
          style: ButtonStyle(
            shape: MaterialStateProperty.all<RoundedRectangleBorder>(
              RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.0),
              ),
            ),
            backgroundColor: MaterialStateProperty.resolveWith<Color>(
                  (Set<MaterialState> states) {
                if (states.contains(MaterialState.disabled)) {
                  return Color(0xff56596C); // Disabled color
                }
                return Color(0xff56596C); // Regular color
              },
            ),
          ),
          onPressed: () {
            // Handle onPressed event here
          },
          child: Text(
            AppLocalizations.of(context)!.translate('hourlyRewards_button_unclaimed'),
            style: LikeWalletAppTheme.textStyle(
                context, 52, Color(0xff262730), FontWeight.bold, 'font2'),
          ),
        ),
        disabledColor: LikeWalletAppTheme.gray.withOpacity(0.64),
      ),
    );
  }

  Widget _buttonClaim() {
    return AnimatedContainer(
      duration: Duration(milliseconds: 300),
      width: buttonClaim == 1
          ? 132.0.w
          : buttonClaim == 2
              ? 884.0.w
              : 884.0.w,
      height: 132.0.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(buttonClaim == 1
            ? 200.0.w
            : buttonClaim == 2
                ? 16.0.w
                : 16.0.w),
        gradient: buttonClaim == 2
            ? LinearGradient(
                begin: Alignment(-1.0, -0.09),
                end: Alignment(1.01, 0.0),
                colors: [
                  const Color(0xff485dfa),
                  const Color(0xff7b75fd),
                  const Color(0xffc28dff)
                ],
                stops: [0.0, 0.507, 1.0],
              )
            : LinearGradient(
                begin: Alignment(-1.0, -0.09),
                end: Alignment(1.01, 0.0),
                colors: [
                  const Color(0xff52fcf0),
                  const Color(0xff11e7db),
                  const Color(0xff22c4e6)
                ],
                stops: [0.0, 0.389, 1.0],
              ),
        boxShadow: [
          BoxShadow(
            color: const Color(0x59000000),
            offset: Offset(0, 3),
            blurRadius: 18,
          ),
        ],
      ),
      child: buttonClaim == 1
          ? AnimatedBuilder(
              animation: _animationController,
              child: Container(
                padding: EdgeInsets.all(20.w),
                color: Colors.transparent,
                child: Image.asset(LikeWalletImage.icon_loading,
                    fit: BoxFit.fitHeight),
              ),
              builder: (context, child) => Transform.rotate(
                child: child,
                angle: _animationController.value * 2.0 * math.pi,
              ),
            )
          : claimable ?
      ButtonTheme(
              minWidth: mediaQuery(context, "width", 884),
              height: 132.h,
              child: TextButton(
                style: ButtonStyle(
                  shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                    RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                  ),
                  backgroundColor: MaterialStateProperty.resolveWith<Color>(
                        (Set<MaterialState> states) {
                      if (states.contains(MaterialState.disabled)) {
                        return Colors.transparent; // Disabled color
                      }
                      return Colors.transparent; // Regular color
                    },
                  ),
                ),
                onPressed: buttonClaim == 0
                    ? () {
                  setState(() {
                    buttonClaim = 1;
                  });
                  _claimRewards();
                }
                    : null, // Null onPressed when buttonClaim is not 0
                child: buttonClaim == 2
                    ? SizedBox(
                  height: 48.86,
                  width: 61.92,
                  child: Stack(
                    children: <Widget>[
                      Container(
                        height: 48.9,
                        width: 61.9,
                        child: SvgPicture.string(
                          '<svg viewBox="539.0 2110.7 61.9 48.9" ><path transform="translate(439.25, 1997.69)" d="M 121.3377151489258 161.8282623291016 L 101.6040267944336 142.0951538085938 C 99.13432312011719 139.6254730224609 99.13432312011719 135.6208038330078 101.6040267944336 133.1511077880859 C 104.07373046875 130.681396484375 108.0783996582031 130.681396484375 110.5481033325195 133.1511077880859 L 121.3861846923828 143.9897766113281 L 150.8975524902344 114.7973861694336 C 153.3804168701172 112.3408432006836 157.3851013183594 112.3635864257812 159.8410186767578 114.8458557128906 C 162.2969818115234 117.328727722168 162.2754211425781 121.3333892822266 159.7919616699219 123.7893218994141 L 121.3377151489258 161.8282623291016 Z" fill="#0fe8d8" stroke="none" stroke-width="1" stroke-miterlimit="10" stroke-linecap="butt" /></svg>',
                          allowDrawingOutsideViewBox: true,
                          fit: BoxFit.fill,
                        ),
                      ),
                    ],
                  ),
                )
                    : Text(
                  AppLocalizations.of(context)!
                      .translate('hourlyRewards_button_claim'),
                  style: TextStyle(
                    fontSize: 15,
                    color: Color(0xff262730),
                    fontWeight: FontWeight.bold,
                    fontFamily: 'font1',
                  ),
                ),
              ),

        disabledColor: LikeWalletAppTheme.gray.withOpacity(0.64),
            )
      :ButtonTheme(
        minWidth: mediaQuery(context, "width", 884),
        height: 132.h,
        child:TextButton(
          style: ButtonStyle(
            shape: MaterialStateProperty.all<RoundedRectangleBorder>(
              RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.0),
              ),
            ),
            foregroundColor: MaterialStateProperty.all<Color>(
              Color(0xff262730).withOpacity(0.3),
            ),
            textStyle: MaterialStateProperty.all<TextStyle>(
              TextStyle(
                fontWeight: FontWeight.bold,
                fontFamily: 'font1',
                fontSize: 52,
              ),
            ),
          ),
          onPressed: null,
          child: Text(
            AppLocalizations.of(context)!.translate('hourlyRewards_button_claim'),
          ),
        ),
        disabledColor: LikeWalletAppTheme.gray.withOpacity(0.64),
      ),
    );
  }

  Widget _buttonClaimAndLock() {
    return AnimatedContainer(
      duration: Duration(milliseconds: 300),
      width: buttonClaimAndLock == 1
          ? 132.0.w
          : buttonClaimAndLock == 2
              ? 884.0.w
              : 884.0.w,
      height: 132.0.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(buttonClaimAndLock == 1
            ? 200.0.w
            : buttonClaimAndLock == 2
                ? 16.0.w
                : 16.0.w),
        gradient: buttonClaimAndLock == 2
            ? LinearGradient(
                begin: Alignment(-1.0, -0.09),
                end: Alignment(1.01, 0.0),
                colors: [
                  const Color(0xff485dfa),
                  const Color(0xff7b75fd),
                  const Color(0xffc28dff)
                ],
                stops: [0.0, 0.507, 1.0],
              )
            : LinearGradient(
                begin: Alignment(-1.0, -0.09),
                end: Alignment(1.01, 0.0),
                colors: [
                  const Color(0xff52fcf0),
                  const Color(0xff11e7db),
                  const Color(0xff22c4e6)
                ],
                stops: [0.0, 0.389, 1.0],
              ),
        boxShadow: [
          BoxShadow(
            color: const Color(0x59000000),
            offset: Offset(0, 3),
            blurRadius: 18,
          ),
        ],
      ),
      child: buttonClaimAndLock == 1
          ? AnimatedBuilder(
              animation: _animationController,
              child: Container(
                padding: EdgeInsets.all(20.w),
                color: Colors.transparent,
                child: Image.asset(LikeWalletImage.icon_loading,
                    fit: BoxFit.fitHeight),
              ),
              builder: (context, child) => Transform.rotate(
                child: child,
                angle: _animationController.value * 2.0 * math.pi,
              ),
            )
          : claimLockable ?
            ButtonTheme(
              minWidth: mediaQuery(context, "width", 884),
              height: mediaQuery(context, "height", 132),
              child: TextButton(
                style: ButtonStyle(
                  shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                    RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                  ),
                  backgroundColor: MaterialStateProperty.resolveWith<Color>(
                        (Set<MaterialState> states) {
                      return Color(0x00000000); // Transparent background color
                    },
                  ),
                ),
                onPressed: () {
                  if (buttonClaimAndLock == 0) {
                    setState(() {
                      buttonClaimAndLock = 1;
                    });
                    _claimRewardsAndLock();
                  }
                },
                child: buttonClaimAndLock == 2
                    ? SizedBox(
                  height: 48.86.h,
                  width: 61.92.w,
                  child: Stack(
                    children: <Widget>[
                      Container(
                        height: 48.9.h,
                        width: 61.9.w,
                        child: SvgPicture.string(
                          '<svg viewBox="539.0 2110.7 61.9 48.9" ><path transform="translate(439.25, 1997.69)" d="M 121.3377151489258 161.8282623291016 L 101.6040267944336 142.0951538085938 C 99.13432312011719 139.6254730224609 99.13432312011719 135.6208038330078 101.6040267944336 133.1511077880859 C 104.07373046875 130.681396484375 108.0783996582031 130.681396484375 110.5481033325195 133.1511077880859 L 121.3861846923828 143.9897766113281 L 150.8975524902344 114.7973861694336 C 153.3804168701172 112.3408432006836 157.3851013183594 112.3635864257812 159.8410186767578 114.8458557128906 C 162.2969818115234 117.328727722168 162.2754211425781 121.3333892822266 159.7919616699219 123.7893218994141 L 121.3377151489258 161.8282623291016 Z" fill="#0fe8d8" stroke="none" stroke-width="1" stroke-miterlimit="10" stroke-linecap="butt" /></svg>',
                          allowDrawingOutsideViewBox: true,
                          fit: BoxFit.fill,
                        ),
                      ),
                    ],
                  ),
                )
                    : Text(
                  AppLocalizations.of(context)!
                      .translate('hourlyRewards_button_claim_and_lock'),
                  style: LikeWalletAppTheme.textStyle(
                      context, 52, Color(0xff262730), FontWeight.bold, 'font1'),
                ),
              ),
              disabledColor: LikeWalletAppTheme.gray.withOpacity(0.64),
            )
      : ButtonTheme(
        minWidth: mediaQuery(context, "width", 884),
        height: mediaQuery(context, "height", 132),
        child: TextButton(
          onPressed: null,
          style: ButtonStyle(
            shape: MaterialStateProperty.all<RoundedRectangleBorder>(
              RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.0),
              ),
            ),
            foregroundColor: MaterialStateProperty.resolveWith<Color>(
                  (Set<MaterialState> states) {
                if (states.contains(MaterialState.disabled)) {
                  return Color(0xff262730).withOpacity(0.3); // Disabled text color
                }
                return Color(0xff262730); // Regular text color
              },
            ),
          ),
          child: Text(
            AppLocalizations.of(context)!
                .translate('hourlyRewards_button_claim_and_lock'),
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              fontFamily: 'font1',
            ),
          ),
        ),
        disabledColor: LikeWalletAppTheme.gray.withOpacity(0.64),
      ),
    );
  }

  Widget time() {
    return Stack(
      alignment: Alignment.center,
      children: [
        Container(
          alignment: Alignment.topCenter,
          height: 240.h,
          width: 884.w,
          child: SvgPicture.string(
            '<svg viewBox="0.0 0.0 884.0 240.0" ><defs><filter id="shadow"><feDropShadow dx="0" dy="24" stdDeviation="35"/></filter><linearGradient id="gradient" x1="0.5" y1="0.0" x2="0.5" y2="1.0"><stop offset="0.0" stop-color="#ff222541"  /><stop offset="1.0" stop-color="#ff1d2038"  /></linearGradient></defs><path  d="M 120 0 L 764 0 C 830.274169921875 0 884 53.725830078125 884 120 C 884 186.274169921875 830.274169921875 240 764 240 L 120 240 C 53.725830078125 240 0 186.274169921875 0 120 C 0 53.725830078125 53.725830078125 0 120 0 Z" fill="url(#gradient)" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" filter="url(#shadow)"/></svg>',
            allowDrawingOutsideViewBox: true,
            fit: BoxFit.fill,
          ),
        ),
        Column(
          children: [
            Text(
              AppLocalizations.of(context)!.translate('hourlyRewards_next'),
              style: TextStyle(
                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                fontSize: 32.h,
                color: const Color(0xff7b75fd),
                letterSpacing: 3.2.w,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(
              height: 28.h,
            ),
            Text(
              rewardTime +
                  " " +
                  AppLocalizations.of(context)!.translate('hourlyRewards_time'),
              style: TextStyle(
                fontFamily: 'Roboto',
                fontSize: 60.h,
                color: const Color(0xffffffff),
                fontWeight: FontWeight.w400,
              ),
              textAlign: TextAlign.left,
            ),
          ],
        ),
        Positioned(
            right: 88.7.w,
            bottom: -25.h,
            width: 173.81.w,
            height: 173.81.h,
            child: Image.asset(
              LikeWalletImage.icon_time,
              fit: BoxFit.contain,
            ))
      ],
    );
  }

  Widget gifHead() {
    return Container(
      child: Lottie.network(
        image,
        fit: BoxFit.fill,
        height: mediaQuery(context, 'height', 787),
        width: MediaQuery.of(context).size.width,
      ),

      // child: CachedNetworkImage(
      //   imageUrl: image,
      //   imageBuilder: (context, imageProvider) => Container(
      //     decoration: BoxDecoration(
      //       image: DecorationImage(image: imageProvider, fit: BoxFit.fitWidth),
      //     ),
      //   ),
      // ),
    );
  }
}
