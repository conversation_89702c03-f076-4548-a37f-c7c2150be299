import 'package:flutter/material.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class MyBlinkingButton extends StatefulWidget {
  MyBlinkingButton({this.Income});
  double? Income;
  @override
  _MyBlinkingButtonState createState() => _MyBlinkingButtonState();
}

class _MyBlinkingButtonState extends State<MyBlinkingButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  bool status = false;
  @override
  void initState() {
    _animationController =
        new AnimationController(vsync: this, duration: Duration(seconds: 1));
    _animationController.repeat();
    Future.delayed(Duration(seconds: 4)).then((_) async {
      _animationController.stop();
      setState(() {
        status = true;
      });
    });
    super.initState();
  }

  Widget textIncome() {
    return Text.rich(
      TextSpan(
        style: TextStyle(
          fontFamily: AppLocalizations.of(context)!.translate('font1'),
          fontSize: 30.sp,
          color: Color(0xffb889fe),
          letterSpacing: 3.sp,
        ),
        children: [
          TextSpan(
            text: AppLocalizations.of(context)!
                            .translate('hourlyRewards_Income1') +
                " ",
          ),
          TextSpan(
            text: widget.Income!.toStringAsFixed(2).toString() +
                "% " +
                AppLocalizations.of(context)!.translate('apy'),
            style: TextStyle(
              color: Colors.white,
              fontSize: 65.sp,
              letterSpacing: 3.sp,
              fontFamily: AppLocalizations.of(context)!.translate('font2'),
            ),
          ),
          TextSpan(
            text: '  \n',
            style: TextStyle(
              fontSize: 65.sp,
              color: Color(0xffb889fe),
              letterSpacing: 6.5.sp,
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
            ),
          ),
          TextSpan(
            text:
                AppLocalizations.of(context)!.translate('hourlyRewards_Income2'),
          ),
        ],
      ),
      textAlign: TextAlign.center,
    );
  }

  @override
  Widget build(BuildContext context) {
    return status
        ? textIncome()
        : FadeTransition(
            opacity: _animationController,
            child: textIncome(),
          );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
}
