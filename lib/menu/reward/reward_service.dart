import 'dart:convert';
import 'dart:math' as math;
import 'package:likewallet/libraryman/ethcontract.dart';
import 'package:likewallet/libraryman/address_service.dart';
import 'package:likewallet/libraryman/configuration_service.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:http/http.dart' as http;
import 'package:likewallet/app_config.dart';
import 'package:likewallet/setmodel/next_rewards_model.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:intl/intl.dart' as formatIntl;
import 'package:likewallet/screen/NavigationBar.dart';

abstract class RewardService {
  late BaseETH eth;
  late bool status;
  // Future<String> getNewReward() async {
  //   // TODO: implement getNewReward
  //   var response = await http
  //       .post(env.apiUrl + '/nextRewards', body: {'application': 'LIKEWALLET'});
  //   // // print('Response status: ${response.statusCode}');
  //   // // print('Response body: ${response.body}');
  //   //
  //   var body = jsonDecode(response.body);
  //   // // print(body['result']);
  //   return body;
  // }
  Future checkRewards(addressETH) async {
    //check status claim
    //check รอบที่ผู้ใช้รับปัจจุบัน
    var balanceRewards = await eth.checkRewards(address: addressETH);
    print('balanceLock ' + balanceRewards.toString());
    var lastTime = await eth.checkClaim(address: addressETH);
    print('lastTime ' + lastTime.toString());
    //check current round airdrop
    //เช็ครอบที่มีการแจกรางวัลจากทีมงาน
    var roundAirdrop = await eth.getRound();
    print('roundAirdrop ' + roundAirdrop.toString());
    //ถ้ารอบที่ผู้รับมากกว่าหรือเท่ากับรอบปัจจุบันให้ทำการฝากเหรียญเพิ่มได้
    if (int.parse(lastTime) >= int.parse(roundAirdrop) ||
        balanceRewards == 0.0) {
      //claim ไปแล้ว
      return false;
    } else {
      //ยังไม่ claim
      return true;
    }
  }
  //Lock Like

  Future lockLikepoint(totalLock, amountLock, getPK) async {
    //เอาจำนวนที่ล็อคตัด commas ออก
    totalLock = double.parse(amountLock.toString().replaceAll(",", ""));

    print(totalLock);
    eth.lockLikePoint(pk: getPK, value: totalLock).then((transaction) {
      List<String> tx = transaction.split(":");
      //transaction approve ขอสิทธิ์
      String approveTx = tx[0];
      //transaction Lock เหรียญ
      String lockTx = tx[1];
      print('approve tx : $approveTx');
      print('lock tx : $lockTx');
      //delay 2 วิเพื่อให้ ธุรกรรมเสร็จก่อน เพราะ tomochain block time คือ 2 sec
      new Future.delayed(new Duration(seconds: 4), () {});
    });
  }
}
