import 'package:flutter/material.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:likewallet/model/listgame.dart';
import 'package:likewallet/quickpay/favorites/list.dart';
import 'package:likewallet/screen_util.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:likewallet/screen/home.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/libraryman/open_web.dart' as WebappOpen;
import 'package:likewallet/libraryman/serviceHTTP.dart';

import 'package:likewallet/model/listgame.dart';
import 'package:likewallet/screen_util.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:likewallet/screen/home.dart';
import 'package:likewallet/libraryman/open_web.dart' as WebappOpen;
import 'package:likewallet/libraryman/serviceHTTP.dart';

class PlayGame extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return _PlayGame();
  }
}

class _PlayGame extends State<PlayGame> {
  int count = 0;
  bool _saving = false;
  late AbstractServiceHTTP APIHttp;
  late List<listgame> _list;
  @override
  void initState() {
    super.initState();

    APIHttp = ServiceHTTP();
  }

  Future<List?> getGame() async {
    //เรียก API ร้านค้า
    _list = await APIHttp.getGame() as List<listgame>;
    return _list;
  }

  @override
  Widget build(BuildContext context) {
    return Material(
        child: ModalProgressHUD(
            opacity: 0.1,
            inAsyncCall: _saving,
            progressIndicator: CustomLoading(),
            child: Scaffold(
                backgroundColor: LikeWalletAppTheme.bule2,
                body: new Stack(
                  alignment: Alignment.center,
                  children: <Widget>[
                    //head
                    Positioned(
                        top: mediaQuery(context, 'height', 136),
                        child: backButton(context, LikeWalletAppTheme.white)),
                    //head
                    Positioned(
                        top: mediaQuery(context, 'height', 200),
                        child: _gameHead()),
                    Positioned(
                      top: mediaQuery(context, 'height', 300),
                      child: _gameList(),
                    )
                  ],
                ))));
  }

  Widget _gameHead() {
    return Container(
      child: new Text('PlayGame',
          style: LikeWalletAppTheme.textStyle(
              context, 59, LikeWalletAppTheme.bule1, FontWeight.bold, 'font1')),
    );
  }

  Widget _gameList() {
    return Container(
        height: mediaQuery(context, "height", 350),
        child: FutureBuilder(
          builder: (context, projectSnap) {
            if (projectSnap.connectionState == ConnectionState.none &&
                projectSnap.hasData == null) {
              //print('project snapshot data is: ${projectSnap.data}');
              return Container();
            }
            List<listgame> game = projectSnap.data as List<listgame>;
            return new ListView.builder(
                scrollDirection: Axis.horizontal,
                shrinkWrap: true,
                //                          padding: EdgeInsets.all(10.0),
                itemCount: game.length,
                //                          physics: const ClampingScrollPhysics(),
                // itemExtent: 10.0,
                // reverse: true, //makes the list appear in descending order
                itemBuilder: (BuildContext context, int i) {
                  //                  print(group[index]);
                  //                  print(_list[i].shopGroup);
                  listgame project = game[i];
                  return GestureDetector(
                      onTap: () {
                        print('test');
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => WebappOpen.WebOpen(
                                      url: project.url.toString() + 'dev.html',
                                    )));
                      },
                      child: Card(
                        child: Image.network(
                          project.images.toString(),
                          height: mediaQuery(context, "height", 600),
                        ),
                      ));
                });
          },
          future: getGame(),
        ));
  }
//      Stack(
//        alignment: Alignment.center,
//        children: <Widget>[
//          Positioned(
//              top: MediaQuery.of(context).size.height *
//                  Screen_util("height", 100),
//              child: Row(
//                children: <Widget>[
//
//                  Container(),
//                  GestureDetector(
//                      onTap: () {
//                        WebappOpen.WebOpen(url: ,);
//                      },
//                      child: Card(
//                        child: Image.asset(
//                          'assets/image/slot/slotmachine_card.jpg',
//                          height: MediaQuery.of(context).size.height *
//                              Screen_util("height", 600),
//                        ),
//                      )
//                  )
//                ],
//              ))
//        ],
//      ),

}
