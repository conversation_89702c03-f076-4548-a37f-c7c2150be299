// import 'dart:convert';
// import 'dart:io';
//
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:likewallet/screen/NavigationBar.dart';
// import 'package:likewallet/screen_util.dart';
// import 'dart:async';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:likewallet/bank/confirm_transection.dart';
// import 'dart:math' as math;
// import 'package:likewallet/libraryman/ethcontract.dart';
// import 'package:likewallet/libraryman/address_service.dart';
// import 'package:likewallet/libraryman/configuration_service.dart';
// import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
// import 'package:http/http.dart' as http;
// import 'package:likewallet/app_config.dart';
// import 'package:likewallet/setmodel/next_rewards_model.dart';
// import 'package:fluttertoast/fluttertoast.dart';
// import 'package:likewallet/libraryman/custom_loading.dart';
// import 'package:intl/intl.dart' as formatIntl;
// import 'package:ads/ads.dart';
// import 'package:likewallet/libraryman/app_local.dart';
// import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:likewallet/libraryman/auth.dart';
// import 'package:firebase_admob/firebase_admob.dart';
// import 'package:likewallet/libraryman/AppAds.dart';
//
// import 'dart:async';
// import 'dart:io';
// import 'package:mqtt_client/mqtt_client.dart';
// import 'package:mqtt_client/mqtt_server_client.dart';
//
// class adsRewards_old extends StatefulWidget {
//   _adsRewards createState() => new _adsRewards();
// }
//
// enum statusClaim { INACTIVE, ACTIVE }
//
// class _adsRewards extends State<adsRewards_old> with TickerProviderStateMixin {
//   final f = new formatIntl.NumberFormat("###,###");
//   String dropdownValue;
//   double nextRewards = 0;
//   double upcomingRewards = 0;
//   double rewards = 0.0;
//   double totalLocked = 0.0;
//   IAddressService addressService;
//   IConfigurationService configETH;
//   BaseETH eth;
//   statusClaim isChaim = statusClaim.INACTIVE;
//   String addressETH;
//   String mnemonic;
//   String getPK;
//   bool _saving = false;
//   int tabMenu = 0;
//   BaseAuth auth;
//   String messageUpcoming = "Please, wait next round";
//   List<NextRewards> listRewards;
//   bool ads_1 = false;
//   bool ads_2 = false;
//   bool ads_3 = false;
//   bool ads_4 = false;
//   int rewardsToday = 0;
//   MqttServerClient client;
//   //ads
//
//   var videoListener;
//
//   changeMenu(_number) async {
//     setState(() {
//       tabMenu = _number;
//     });
//   }
//
//   AnimationController _animationController;
//   AnimationController controller;
//
//   void _showDialog(int _num) {
//     // flutter defined function
//     showDialog(
//       context: context,
//       builder: (BuildContext context) {
//         // return object of type Dialog
//         return AlertDialog(
//           title:
//               new Text(AppLocalizations.of(context)!.translate('rewards_ads')),
//           content: new Text(
//               AppLocalizations.of(context)!.translate('confirm_ads_body')),
//           actions: <Widget>[
//             // usually buttons at the bottom of the dialog
//             new FlatButton(
//               child: new Text(AppLocalizations.of(context)!.translate('yes_ok')),
//               onPressed: () {
//                 Navigator.of(context).pop();
//                 showAds();
//               },
//             ),
//             new FlatButton(
//               child: new Text(AppLocalizations.of(context)!.translate('no_kyc')),
//               onPressed: () {
//                 Navigator.of(context).pop();
//               },
//             ),
//           ],
//         );
//       },
//     );
//   }
//
//   /// The subscribed callback
//   void onSubscribed(String topic) {
//     print('EXAMPLE::Subscription confirmed for topic $topic');
//   }
//
//   /// The unsolicited disconnect callback
//   void onDisconnected() {
//     print('EXAMPLE::OnDisconnected client callback - Client disconnection');
//   }
//
//   void callMQTT() async {
//     client = MqttServerClient('mosquitto.likepoint.io', '');
//     client.logging(on: true);
//     client.keepAlivePeriod = 20;
//     client.onDisconnected = onDisconnected;
//     client.onSubscribed = onSubscribed;
//     final connMess = MqttConnectMessage()
//         .withClientIdentifier('likewallet')
//         .startClean() // Non persistent session for testing
//         .withWillQos(MqttQos.atLeastOnce);
//     print('EXAMPLE::Mosquitto client connecting....');
//     client.connectionMessage = connMess;
//
//     try {
//       await client.connect();
//     } on Exception catch (e) {
//       print('EXAMPLE::client exception - $e');
//       client.disconnect();
//     }
//
//     /// Check we are connected
//     if (client.connectionStatus.state == MqttConnectionState.connected) {
//       print('EXAMPLE::Mosquitto client connected');
//     } else {
//       print(
//           'EXAMPLE::ERROR Mosquitto client connection failed - disconnecting, state is ${client.connectionStatus.state}');
//       client.disconnect();
//     }
//   }
//
//   @override
//   void initState() {
//     super.initState();
//     _animationController =
//         new AnimationController(vsync: this, duration: Duration(seconds: 1));
//     _animationController.repeat();
//
//     controller = AnimationController(
//       vsync: this,
//       duration: Duration(seconds: 10),
//     );
//
//     new Future.delayed(new Duration(milliseconds: 500), () {
//       eth = new EthContract();
//       setInit();
//     });
//     auth = new Auth();
//     ////########ads admob ########///
//     ///########################/////
//     //
//     callMQTT();
//
//     //video ads
//     AppAds.init();
//
//     /// Assign the listener.
//     /// You just show the Banner, Fullscreen and Video Ads separately.
//     videoListener =
//         (RewardedVideoAdEvent event, {String rewardType, int rewardAmount}) {
//       print('event : $event');
//
//       if (event == RewardedVideoAdEvent.rewarded) {
//         print("The video ad has been rewarded.");
//       }
//     };
//
//     videoListener =
//         (RewardedVideoAdEvent event, {String rewardType, int rewardAmount}) {
//       switch (event) {
//         case RewardedVideoAdEvent.loaded:
//           print("An ad has loaded successfully in memory.");
//           if (!mounted) return;
//           setState(() {
//             _saving = false;
//           });
//           break;
//         case RewardedVideoAdEvent.failedToLoad:
//           print("The ad failed to load into memory.");
//           if (!mounted) return;
//           setState(() {
//             _saving = false;
//           });
//           break;
//         case RewardedVideoAdEvent.opened:
//           print("The ad is now open.");
//           if (!mounted) return;
//           setState(() {
//             _saving = false;
//           });
//           break;
//         case RewardedVideoAdEvent.leftApplication:
//           print("You've left the app after clicking the Ad.");
//
//           if (!mounted) return;
//           setState(() {
//             _saving = false;
//           });
//           break;
//         case RewardedVideoAdEvent.closed:
//           print("You've closed the Ad and returned to the app.");
//           if (!mounted) return;
//           setState(() {
//             _saving = false;
//           });
//           break;
//         case RewardedVideoAdEvent.rewarded:
//           print("The ad has sent a reward amount.");
//           auth.getCurrentUser().then((decodedToken) async {
//             String _year = new DateTime.now().year.toString();
//             String _month = new DateTime.now().month.toString();
//             String _day = new DateTime.now().day.toString();
//             String _date = _day + '-' + _month + '-' + _year;
//             var uidAds = await FirebaseFirestore.instance
//                 .collection('logs')
//                 .doc('rewardsAds')
//                 .collection(_date)
//                 .doc()
//                 .set({
//               'address': addressETH,
//               'phone_number': decodedToken.phoneNumber,
//               'uid': decodedToken.uid,
//               'rewards': 5,
//               'status': 'inactive',
//               'datetime': new DateTime.now().millisecondsSinceEpoch
//             }).then((data) {
//               //reload rewards
//               checkRewards();
//               final builder1 = MqttClientPayloadBuilder();
//               var d = "{\"address\" : \"" +
//                   addressETH +
//                   "\", \"date\":\"" +
//                   _date +
//                   "\" }";
//               builder1.addString(d.toString());
//               print('EXAMPLE:: <<<< PUBLISH 1 >>>>');
//               client.publishMessage(
//                   'claimRewardsADS', MqttQos.atLeastOnce, builder1.payload);
//             });
//
// //            ads.dispose();
//           });
//           if (!mounted) return;
//           setState(() {
//             _saving = false;
//           });
//           break;
//         case RewardedVideoAdEvent.started:
//           print("You've just started playing the Video ad.");
//           if (!mounted) return;
//           setState(() {
//             _saving = false;
//           });
//           break;
//         case RewardedVideoAdEvent.completed:
//           if (!mounted) return;
//           setState(() {
//             _saving = false;
//           });
//
//           print("You've just finished playing the Video ad.");
//           break;
//         default:
//           if (!mounted) return;
//           setState(() {
//             _saving = false;
//           });
//           print("There's a 'new' RewardedVideoAdEvent?!");
//       }
//     };
//
//     ///
//     ///   /// #### end ads admob#####////
//     //  /// ##########################
//   }
//
//   void showAds() {
//     if (!mounted) return;
//     setState(() {
//       _saving = true;
//     });
//
// //    ads.showVideoAd(
// //      adUnitId: videoUnitId,
// //      state: this,
// //      keywords: ['game', 'ethereum', 'bitcoin', 'isuzu', 'pubg'],
// //      contentUrl: 'https://www.prachakij.com',
// //      childDirected: true,
// ////      testDevices: ["Redmi Note 6 Pro"],
// //      testDevices: null,
// //      testing: true,
// //      listener: videoListener,
// //    );
//     AppAds.showVideo(state: this, videoListener: videoListener);
//   }
//
//   ///ads admob ////
//   ///
//   ///
//   /// end ads admobb
//   ///
//   @override
//   void dispose() {
//     controller.dispose();
//     _animationController.dispose();
// //    ads?.dispose();
//     super.dispose();
//   }
//
//   void showColoredToast(msg, colors) {
//     Fluttertoast.showToast(
//         msg: msg,
//         toastLength: Toast.LENGTH_LONG,
//         backgroundColor: colors,
//         textColor: Colors.white);
//   }
//
//   void showShortToast(msg) {
//     Fluttertoast.showToast(
//         msg: msg,
//         toastLength: Toast.LENGTH_SHORT,
//         backgroundColor: Colors.cyan,
//         textColor: Colors.white);
//   }
//
//   setInit() async {
//     SharedPreferences pref = await SharedPreferences.getInstance();
//
//     configETH = new ConfigurationService(pref);
//     addressService = new AddressService(configETH);
//     mnemonic = pref.getString('seed');
//
// //    getPK = addressService.getPrivateKey(mnemonic);
//     getPK = configETH.getPrivateKey();
//
//     setState(() {
//       addressETH = configETH.getAddress();
//     });
//     checkRewards();
//
//     //get address
//
// //    addressService.getPublicAddress(getPK).then((address) {
//     print(addressETH);
//   }
//
//   void checkRewards() {
//     if (!mounted) return;
//     setState(() {
//       rewardsToday = 0;
//     });
//
//     String _year = new DateTime.now().year.toString();
//     String _month = new DateTime.now().month.toString();
//     String _day = new DateTime.now().day.toString();
//     String _date = _day + '-' + _month + '-' + _year;
//
//     var searchRewards = FirebaseFirestore.instance
//         .collection('logs')
//         .doc('rewardsAds')
//         .collection(_date);
//
//     searchRewards
//         .where('address', isEqualTo: addressETH)
//         .snapshots()
//         .listen((data) {
//       data.docs.forEach((doc) {
//         if (!mounted) return;
//         setState(() {
//           rewardsToday += doc.data()["rewards"];
//         });
//       });
//     });
//   }
//
// //claim
//   _claimRewards() {
//     if (!mounted) return;
//     setState(() {
//       _saving = true;
//     });
//
//     eth.ClaimRewards(pk: getPK).then((tx) {
//       if (!mounted) return;
//       setState(() {
//         showColoredToast('You got rewards tx :' + tx, Colors.green);
//
//         new Future.delayed(new Duration(seconds: 8), () {
//           setInit();
//           _saving = false;
//         });
//       });
//     }).catchError((onError) {
//       _saving = false;
//     });
//   }
//
//   String get timerString {
//     Duration duration = controller.duration * controller.value;
//     return '${(duration.inHours % 60).toString().padLeft(2, '0')}:${(duration.inMinutes % 60).toString().padLeft(2, '0')}:${(duration.inSeconds % 60).toString().padLeft(2, '0')}';
//   }
//
//   TextEditingController addressText = TextEditingController();
//
//   buildForPhone(Orientation orientation) {
//     return Scaffold(
//       backgroundColor: Colors.white,
//       body: tabMenu == 1
//           ? confirmTransection()
//           : SingleChildScrollView(
//               child: Stack(
//               children: <Widget>[
//                 new Container(
//                   height: MediaQuery.of(context).size.height,
//                   decoration: BoxDecoration(
//                       gradient: LinearGradient(
//                     begin: Alignment.topRight,
//                     end: Alignment.bottomLeft,
//                     stops: [0.4, 0.6, 0.7, 0.9, 0.95],
//                     colors: [
//                       // Colors are easy thanks to Flutter's Colors class.
//                       Color(0xff111112).withOpacity(0.9),
//                       Color(0xff111112).withOpacity(0.8),
//                       Color(0xff111112).withOpacity(0.75),
//                       Color(0xff111112).withOpacity(0.73),
//                       Color(0xff111112).withOpacity(0.69)
//                     ],
//                   )),
//                 ),
//                 Positioned(
//                     top: MediaQuery.of(context).size.height *
//                         Screen_util("height", 139.33),
//                     child: GestureDetector(
//                       child: new Container(
//                         alignment: Alignment.centerLeft,
//                         width: MediaQuery.of(context).size.width,
//                         child: new IconButton(
//                           icon: new Icon(
//                             Icons.arrow_back_ios,
//                             size: MediaQuery.of(context).size.height *
//                                 Screen_util("height", 44.47),
//                           ),
//                           color: Color(0xff707071),
//                           onPressed: () => {Navigator.of(context).pop()},
//                         ),
//                       ),
//                     )),
//                 Positioned(
//                   top: MediaQuery.of(context).size.height * 0.1,
//                   child: new Column(
//                     children: <Widget>[
//                       Container(
//                         width: MediaQuery.of(context).size.width * 0.95,
//                         alignment: Alignment.centerRight,
//                         child: new Text(
//                           AppLocalizations.of(context)!.translate('rewards_ads'),
//                           textAlign: TextAlign.right,
//                           style: TextStyle(
//                               fontFamily: 'Proxima Nova',
//                               color: Color(0xffFFFFFF),
//                               fontSize: 15,
//                               fontWeight: FontWeight.normal),
//                         ),
//                       ),
//                       Container(
//                         width: MediaQuery.of(context).size.width * 0.95,
//                         alignment: Alignment.centerRight,
//                         child: new Text(
//                           AppLocalizations.of(context)!.translate('watch_ads'),
//                           textAlign: TextAlign.right,
//                           style: TextStyle(
//                               fontFamily: 'Roboto',
//                               color: Color(0xffFFFFFF),
//                               fontSize: 30,
//                               fontWeight: FontWeight.normal),
//                         ),
//                       ),
//                     ],
//                   ),
//                 ),
//                 Positioned(
//                     top: MediaQuery.of(context).size.height * 0.25,
//                     left: MediaQuery.of(context).size.height * 0.025,
//                     right: MediaQuery.of(context).size.height * 0.025,
//                     child: new Container(
//                       alignment: Alignment.center,
//                       height: MediaQuery.of(context).size.height * 0.085,
//                       width: MediaQuery.of(context).size.width * 0.9,
//                       decoration: BoxDecoration(
//                         color: Color(0xff000000),
//                         border: Border.all(
//                           color: Color(0xff000000),
//                           width: 1.0,
//                         ),
//                         borderRadius: BorderRadius.all(Radius.circular(15.0)),
//                       ),
//                     )),
//                 Positioned(
//                   top: MediaQuery.of(context).size.height * 0.155,
//                   left: MediaQuery.of(context).size.height * 0.013,
//                   child: Container(
//                     alignment: Alignment.center,
//                     height: MediaQuery.of(context).size.height * 0.18,
//                     width: MediaQuery.of(context).size.width * 0.5,
//                     decoration: BoxDecoration(
//                       image: DecorationImage(
//                           fit: BoxFit.fill,
//                           image: AssetImage('assets/image/NextDis.png')),
//                     ),
//                   ),
//                 ),
//                 Positioned(
//                   top: MediaQuery.of(context).size.height * 0.265,
//                   left: MediaQuery.of(context).size.height * 0.02,
//                   child: Container(
//                       padding: EdgeInsets.only(
//                         left: MediaQuery.of(context).size.width * 0.1,
//                       ),
//                       alignment: Alignment.center,
//                       height: MediaQuery.of(context).size.height * 0.06,
//                       width: MediaQuery.of(context).size.width * 0.9,
//                       child: Column(
//                         children: <Widget>[
//                           new Text(
//                             AppLocalizations.of(context)!.translate('watch_ads'),
//                             textAlign: TextAlign.right,
//                             style: TextStyle(
//                                 fontFamily: 'Proxima Nova',
//                                 color: Color(0xff029A93),
//                                 fontSize: 11,
//                                 fontWeight: FontWeight.normal),
//                           ),
//                           AnimatedBuilder(
//                             animation: controller,
//                             builder: (context, child) {
//                               return controller.isAnimating
//                                   ? Text(
//                                       timerString,
//                                       textAlign: TextAlign.right,
//                                       style: TextStyle(
//                                           fontFamily: 'Roboto',
//                                           color: Color(0xffB4E60D),
//                                           fontSize: 20,
//                                           fontWeight: FontWeight.normal),
//                                     )
//                                   : FadeTransition(
//                                       opacity: _animationController,
//                                       child: Text(
//                                         AppLocalizations.of(context)
//                                             .translate('distributing'),
//                                         textAlign: TextAlign.right,
//                                         style: TextStyle(
//                                             fontFamily: 'Roboto',
//                                             color: Color(0xffB4E60D),
//                                             fontSize: 20,
//                                             fontWeight: FontWeight.w100),
//                                       ),
//                                     );
//                             },
//                           ),
//                         ],
//                       )),
//                 ),
//                 Positioned(
//                   top: MediaQuery.of(context).size.height * 0.4,
//                   left: MediaQuery.of(context).size.height * 0.02,
//                   child: Container(
//                       padding: EdgeInsets.only(
//                         left: MediaQuery.of(context).size.width * 0.1,
//                       ),
//                       alignment: Alignment.center,
//                       height: MediaQuery.of(context).size.height * 0.1,
//                       width: MediaQuery.of(context).size.width * 0.9,
//                       child: Column(
//                         children: <Widget>[
//                           new Text(
//                             rewardsToday.toString() + " LIKE",
//                             textAlign: TextAlign.right,
//                             style: TextStyle(
//                                 fontFamily: 'Proxima Nova',
//                                 color: Color(0xff029A93),
//                                 fontSize: 15,
//                                 fontWeight: FontWeight.normal),
//                           ),
//                         ],
//                       )),
//                 ),
//                 Positioned(
//                   top: MediaQuery.of(context).size.height * 0.45,
//                   left: MediaQuery.of(context).size.height * 0.02,
//                   child: Container(
//                       padding: EdgeInsets.only(
//                         left: MediaQuery.of(context).size.width * 0.1,
//                       ),
//                       alignment: Alignment.center,
//                       height: MediaQuery.of(context).size.height * 0.3,
//                       width: MediaQuery.of(context).size.width * 0.9,
//                       child: Column(
//                         children: <Widget>[
//                           GestureDetector(
//                             onTap: () {
//                               _showDialog(1);
//                             },
//                             child: new Card(
//                                 color: Color(0xff150e23).withOpacity(0.4),
//                                 child: ListTile(
//                                   title: new Text(
//                                     'Watch ads get 5 LIKE',
//                                     style: TextStyle(color: Colors.white),
//                                   ),
//                                   leading: new Image.asset(
//                                       'assets/image/HourlyRewards.png'),
//                                   trailing: new Image.asset(
//                                       'assets/image/NextDis.png'),
//                                 )),
//                           ),
//                         ],
//                       )),
//                 ),
//                 Container(
//                   alignment: Alignment.bottomCenter,
//                   height: MediaQuery.of(context).size.height,
//                   child: NavigationBar(),
//                 )
// //              Positioned(
// //                top: MediaQuery.of(context).size.height * 0.55,
// //                left: MediaQuery.of(context).size.height * 0.02,
// //                child: Container(
// //                    padding: EdgeInsets.only(
// //                      left: MediaQuery.of(context).size.width * 0.1,
// //                    ),
// //                    alignment: Alignment.center,
// //                    height: MediaQuery.of(context).size.height * 0.3,
// //                    width: MediaQuery.of(context).size.width * 0.9,
// //                    child: Column(
// //                      children: <Widget>[
// //                        GestureDetector(
// //                          onTap: () {
// //                            _showDialog(2);
// //                          },
// //                          child: new Card(
// //                              color: Color(0xff150e23).withOpacity(0.4),
// //                              child: ListTile(
// //                                title: new Text('Watch ads get 5 LIKE', style: TextStyle(color: Colors.white),),
// //                                leading: new Image.asset('assets/image/HourlyRewards.png'),
// //                                trailing: new Image.asset('assets/image/NextDis.png'),
// //                              )
// //                          ),
// //                        ),
// //
// //
// //                      ],
// //                    )),
// //              ),
//               ],
//             )),
//     );
//   }
//
//   buildForTablet(Orientation orientation) {}
//
//   @override
//   Widget build(BuildContext context) {
//     final double shortestSide = MediaQuery.of(context).size.shortestSide;
//     final bool useMobileLayout = shortestSide < 600.0;
//     final Orientation orientation = MediaQuery.of(context).orientation;
//     return Scaffold(
//       body: ModalProgressHUD(
//         opacity: 0.1,
//         child: useMobileLayout
//             ? buildForPhone(orientation)
//             : buildForTablet(orientation),
//         inAsyncCall: _saving,
//         progressIndicator: CustomLoading(),
//       ),
//     );
//   }
// }
