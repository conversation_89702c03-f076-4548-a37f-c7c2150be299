// import 'dart:convert';
// import 'dart:ui';
// import 'package:carousel_slider/carousel_slider.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:likewallet/ImageTheme.dart';
// import 'package:likewallet/Theme.dart';
// import 'package:likewallet/animationPage.dart';
// import 'package:likewallet/screen_util.dart';
// import 'package:likewallet/libraryman/app_local.dart';
// import 'dart:io';
//
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:likewallet/screen/NavigationBar.dart';
// import 'package:likewallet/screen_util.dart';
// import 'dart:async';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:likewallet/bank/confirm_transection.dart';
// import 'dart:math' as math;
// import 'package:likewallet/libraryman/ethcontract.dart';
// import 'package:likewallet/libraryman/address_service.dart';
// import 'package:likewallet/libraryman/configuration_service.dart';
// import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
// import 'package:http/http.dart' as http;
// import 'package:likewallet/app_config.dart';
// import 'package:likewallet/setmodel/next_rewards_model.dart';
// import 'package:fluttertoast/fluttertoast.dart';
// import 'package:likewallet/libraryman/custom_loading.dart';
// import 'package:intl/intl.dart' as formatIntl;
// import 'package:ads/ads.dart';
// import 'package:likewallet/libraryman/app_local.dart';
// import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:likewallet/libraryman/auth.dart';
// import 'package:firebase_admob/firebase_admob.dart';
// import 'package:likewallet/libraryman/AppAds.dart';
//
// import 'dart:async';
// import 'dart:io';
// import 'package:mqtt_client/mqtt_client.dart';
// import 'package:mqtt_client/mqtt_server_client.dart';
//
// class adsRewards extends StatefulWidget {
//   _adsRewards createState() => new _adsRewards();
// }
//
// enum statusClaim { INACTIVE, ACTIVE }
//
// class _adsRewards extends State<adsRewards> with TickerProviderStateMixin {
//   int counting = 0;
//   bool showfavorite = true;
//
//   String point = '200';
//
//   final f = new formatIntl.NumberFormat("###,###");
//   String dropdownValue;
//   double nextRewards = 0;
//   double upcomingRewards = 0;
//   double rewards = 0.0;
//   double totalLocked = 0.0;
//   IAddressService addressService;
//   IConfigurationService configETH;
//   BaseETH eth;
//   statusClaim isChaim = statusClaim.INACTIVE;
//   String addressETH;
//   String mnemonic;
//   String getPK;
//   bool _saving = false;
//   int tabMenu = 0;
//   BaseAuth auth;
//   String messageUpcoming = "Please, wait next round";
//   List<NextRewards> listRewards;
//   bool ads_1 = false;
//   bool ads_2 = false;
//   bool ads_3 = false;
//   bool ads_4 = false;
//   int startTime = 0;
//   int rewardsToday = 0;
//   MqttServerClient client;
//   //ads
//   int countAds = 0;
//   List imgList = [
//     LikeWalletImage.watch_photo1,
//     LikeWalletImage.watch_photo2,
//     LikeWalletImage.watch_photo3,
//   ];
//   var videoListener;
//
//   changeMenu(_number) async {
//     setState(() {
//       tabMenu = _number;
//     });
//   }
//
//   AnimationController _animationController;
//   AnimationController controller;
//
//   void showWacthAds(BuildContext context) {
//     Dialog simpleDialog = Dialog(
//       elevation: 500,
//       backgroundColor: Colors.transparent,
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.circular(30.0),
//       ),
//       child: Container(
//         height: mediaQuery(context, 'height', 554.63),
//         width: mediaQuery(context, 'width', 929.64),
//         color: Colors.transparent,
//         margin: EdgeInsets.only(bottom: mediaQuery(context, 'height', 600)),
//         child: new ClipRect(
//           child: new BackdropFilter(
//             filter: new ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0),
//             child: Container(
//               decoration: BoxDecoration(
//                 color: LikeWalletAppTheme.bule2_3.withOpacity(0.6),
//                 borderRadius: BorderRadius.all(Radius.circular(20.0)),
//               ),
//               height: mediaQuery(context, 'height', 554.63),
//               width: mediaQuery(context, 'width', 929.64),
//               child: Column(
//                 mainAxisAlignment: MainAxisAlignment.center,
//                 children: <Widget>[
//                   Text(
//                     AppLocalizations.of(context)!.translate('rewards_ads'),
//                     style: TextStyle(
//                       letterSpacing: 0.3,
//                       fontFamily:
//                           AppLocalizations.of(context)!.translate('font1'),
//                       color: LikeWalletAppTheme.gray.withOpacity(1),
//                       fontSize: mediaQuery(context, "height", 56),
//                       fontWeight: FontWeight.bold,
//                     ),
//                   ),
//                   Container(
//                     margin: EdgeInsets.only(
//                         bottom: mediaQuery(context, 'height', 80)),
//                     width: mediaQuery(context, 'width', 777.62),
//                     child: Text(
//                       AppLocalizations.of(context)
//                           .translate('confirm_ads_body'),
//                       textAlign: TextAlign.center,
//                       style: TextStyle(
//                         letterSpacing: 0.3,
//                         fontFamily:
//                             AppLocalizations.of(context)!.translate('font1'),
//                         color: LikeWalletAppTheme.gray.withOpacity(1),
//                         fontSize: mediaQuery(context, "height", 42),
//                         fontWeight: FontWeight.w500,
//                       ),
//                     ),
//                   ),
//                   Container(
//                       width: mediaQuery(context, 'width', 777.62),
//                       decoration: BoxDecoration(
//                         border: Border(
//                           top: BorderSide(
//                             //                   <--- left side
//                             color: LikeWalletAppTheme.white.withOpacity(0.4),
//                             width: mediaQuery(context, 'width', 1),
//                           ),
//                         ),
//                       ),
//                       child: Row(
//                         children: <Widget>[
//                           GestureDetector(
//                             onTap: () {
//                               Navigator.of(context).pop();
//                               showAds();
//                             },
//                             child: Container(
//                               alignment: Alignment.center,
//                               decoration: BoxDecoration(
//                                 border: Border(
//                                   right: BorderSide(
//                                     //                   <--- left side
//                                     color: LikeWalletAppTheme.white
//                                         .withOpacity(0.4),
//                                     width: mediaQuery(context, 'width', 1),
//                                   ),
//                                 ),
//                               ),
//                               height: mediaQuery(context, 'height', 127.66),
//                               width: mediaQuery(context, 'width', 777.62) / 2,
//                               child: Text(
//                                 AppLocalizations.of(context)
//                                     .translate('yes_ok'),
//                                 textAlign: TextAlign.center,
//                                 style: TextStyle(
//                                   letterSpacing: 0.3,
//                                   fontFamily: AppLocalizations.of(context)
//                                       .translate('font1'),
//                                   color:
//                                       LikeWalletAppTheme.bule1.withOpacity(1),
//                                   fontSize: mediaQuery(context, "height", 52),
//                                   fontWeight: FontWeight.w600,
//                                 ),
//                               ),
//                             ),
//                           ),
//                           GestureDetector(
//                             onTap: () {
//                               Navigator.of(context).pop();
//                             },
//                             child: Container(
//                               alignment: Alignment.center,
//                               height: mediaQuery(context, 'height', 127.66),
//                               width: mediaQuery(context, 'width', 777.62) / 2,
//                               child: Text(
//                                 AppLocalizations.of(context)!.translate('no_ok'),
//                                 textAlign: TextAlign.center,
//                                 style: TextStyle(
//                                   letterSpacing: 0.3,
//                                   fontFamily: AppLocalizations.of(context)
//                                       .translate('font1'),
//                                   color:
//                                       LikeWalletAppTheme.bule1.withOpacity(1),
//                                   fontSize: mediaQuery(context, "height", 52),
//                                   fontWeight: FontWeight.w600,
//                                 ),
//                               ),
//                             ),
//                           ),
//                         ],
//                       )),
//                 ],
//               ),
//             ),
//           ),
//         ),
//       ),
//     );
//     showDialog(
//         context: context, builder: (BuildContext context) => simpleDialog);
//   }
//
//   void _showDialog(int _num) {
//     // flutter defined function
//     showDialog(
//       context: context,
//       builder: (BuildContext context) {
//         // return object of type Dialog
//         return AlertDialog(
//           backgroundColor: Color(0xff141322),
//           title: new Text(AppLocalizations.of(context)!.translate('rewards_ads'),
//               style: TextStyle(color: Color(0xff6C6B6D))),
//           content: new Text(
//               AppLocalizations.of(context)!.translate('confirm_ads_body'),
//               style: TextStyle(color: Color(0xff6C6B6D))),
//           actions: <Widget>[
//             // usually buttons at the bottom of the dialog
//             new FlatButton(
//               child: new Text(AppLocalizations.of(context)!.translate('yes_ok')),
//               onPressed: () {
//                 Navigator.of(context).pop();
//                 showAds();
//               },
//             ),
//             new FlatButton(
//               child: new Text(
//                   AppLocalizations.of(context)!.translate('close_app_no')),
//               onPressed: () {
//                 Navigator.of(context).pop();
//               },
//             ),
//           ],
//         );
//       },
//     );
//   }
//
//   /// The subscribed callback
//   void onSubscribed(String topic) {
// //    print('EXAMPLE::Subscription confirmed for topic $topic');
//   }
//
//   /// The unsolicited disconnect callback
//   void onDisconnected() {
//     print('EXAMPLE::OnDisconnected client callback - Client disconnection');
//   }
//
//   void callMQTT() async {
//     client = MqttServerClient('mosquitto.likepoint.io', '');
//     client.logging(on: true);
//     client.keepAlivePeriod = 20;
//     client.onDisconnected = onDisconnected;
//     client.onSubscribed = onSubscribed;
//     final connMess = MqttConnectMessage()
//         .withClientIdentifier('likewallet')
//         .startClean() // Non persistent session for testing
//         .withWillQos(MqttQos.atLeastOnce);
//     print('EXAMPLE::Mosquitto client connecting....');
//     client.connectionMessage = connMess;
//
//     try {
//       await client.connect();
//     } on Exception catch (e) {
// //      print('EXAMPLE::client exception - $e');
//       client.disconnect();
//     }
//
//     /// Check we are connected
//     if (client.connectionStatus.state == MqttConnectionState.connected) {
//       print('EXAMPLE::Mosquitto client connected');
//     } else {
//       print(
//           'EXAMPLE::ERROR Mosquitto client connection failed - disconnecting, state is ${client.connectionStatus.state}');
//       client.disconnect();
//     }
//   }
//
//   @override
//   void initState() {
//     super.initState();
//     _animationController =
//         new AnimationController(vsync: this, duration: Duration(seconds: 1));
//     _animationController.repeat();
//
//     controller = AnimationController(
//       vsync: this,
//       duration: Duration(seconds: 10),
//     );
//
//     new Future.delayed(new Duration(milliseconds: 500), () {
//       eth = new EthContract();
//       setInit();
//     });
//     auth = new Auth();
//     ////########ads admob ########///
//     ///########################/////
//     //
//
//     callMQTT();
//
//     //video ads
//     AppAds.init();
//
//     /// Assign the listener.
//     /// You just show the Banner, Fullscreen and Video Ads separately.
//     videoListener =
//         (RewardedVideoAdEvent event, {String rewardType, int rewardAmount}) {
//       print('event : $event');
//
//       if (event == RewardedVideoAdEvent.rewarded) {
//         print("The video ad has been rewarded.");
//       }
//     };
//
//     videoListener =
//         (RewardedVideoAdEvent event, {String rewardType, int rewardAmount}) {
//       switch (event) {
//         case RewardedVideoAdEvent.loaded:
//           print("An ad has loaded successfully in memory.");
//           if (!mounted) return;
//           setState(() {
//             _saving = false;
//           });
//           break;
//         case RewardedVideoAdEvent.failedToLoad:
//           print("The ad failed to load into memory.");
//           if (!mounted) return;
//           setState(() {
//             _saving = false;
//           });
//           break;
//         case RewardedVideoAdEvent.opened:
//           print("The ad is now open.");
//           if (!mounted) return;
//           setState(() {
//             _saving = false;
//           });
//           break;
//         case RewardedVideoAdEvent.leftApplication:
//           print("You've left the app after clicking the Ad.");
//
//           if (!mounted) return;
//           setState(() {
//             _saving = false;
//           });
//           break;
//         case RewardedVideoAdEvent.closed:
//           print("You've closed the Ad and returned to the app.");
//           if (!mounted) return;
//           setState(() {
//             _saving = false;
//           });
//           break;
//         case RewardedVideoAdEvent.rewarded:
//           print("The ad has sent a reward amount.");
//           auth.getCurrentUser().then((decodedToken) async {
//             String _year = new DateTime.now().year.toString();
//             String _month = new DateTime.now().month.toString();
//             String _day = new DateTime.now().day.toString();
//             String _date = _day + '-' + _month + '-' + _year;
//             var uidAds = await FirebaseFirestore.instance
//                 .collection('logs')
//                 .doc('rewardsAds')
//                 .collection(_date)
//                 .doc()
//                 .set({
//               'address': addressETH,
//               'phone_number': decodedToken.phoneNumber,
//               'uid': decodedToken.uid,
//               'rewards': 5,
//               'status': 'inactive',
//               'datetime': new DateTime.now().millisecondsSinceEpoch
//             }).then((data) {
//               //reload rewards
//               checkRewards();
//               final builder1 = MqttClientPayloadBuilder();
//               var d = "{\"address\" : \"" +
//                   addressETH +
//                   "\", \"date\":\"" +
//                   _date +
//                   "\" }";
//               builder1.addString(d.toString());
//               print('EXAMPLE:: <<<< PUBLISH 1 >>>>');
//               client.publishMessage(
//                   'claimRewardsADS', MqttQos.atLeastOnce, builder1.payload);
//             });
//
// //            ads.dispose();
//           });
//           if (!mounted) return;
//           setState(() {
//             _saving = false;
//           });
//           break;
//         case RewardedVideoAdEvent.started:
//           print("You've just started playing the Video ad.");
//           if (!mounted) return;
//           setState(() {
//             _saving = false;
//           });
//           break;
//         case RewardedVideoAdEvent.completed:
//           if (!mounted) return;
//           setState(() {
//             _saving = false;
//           });
//
//           print("You've just finished playing the Video ad.");
//           break;
//         default:
//           if (!mounted) return;
//           setState(() {
//             _saving = false;
//           });
//           print("There's a 'new' RewardedVideoAdEvent?!");
//       }
//     };
//
//     ///
//     ///   /// #### end ads admob#####////
//     //  /// ##########################
//   }
//
//   void _showDialogTimeout() {
//     // flutter defined function
//     showDialog(
//       context: context,
//       builder: (BuildContext context) {
//         // return object of type Dialog
//         return AlertDialog(
//           backgroundColor: Color(0xff141322),
//           title: new Text(
//             AppLocalizations.of(context)!.translate('no_ads_title'),
//             style: TextStyle(color: Color(0xff6C6B6D)),
//           ),
//           content: new Text(
//             AppLocalizations.of(context)!.translate('no_ads_body'),
//             style: TextStyle(color: Color(0xff6C6B6D)),
//           ),
//           actions: <Widget>[
//             // usually buttons at the bottom of the dialog
//             new FlatButton(
//               child: new Text(
//                 AppLocalizations.of(context)!.translate('close_name'),
//                 style: TextStyle(color: Color(0xff6C6B6D)),
//               ),
//               onPressed: () {
//                 Navigator.of(context).pop();
//               },
//             ),
//           ],
//         );
//       },
//     );
//   }
//
//   void checkAds() {
//     startTime = DateTime.now().millisecondsSinceEpoch;
//     Future.delayed(Duration(seconds: 10)).then((expire) {
//       if (_saving == true) {
//         setState(() {
//           _saving = false;
//         });
//         _showDialogTimeout();
//       }
//     });
//   }
//
//   void showColoredToast(msg, colors) {
//     Fluttertoast.showToast(
//         msg: msg,
//         toastLength: Toast.LENGTH_LONG,
//         backgroundColor: colors,
//         textColor: Colors.white);
//   }
//
//   void showAds() async {
//     if (!mounted) return;
//     setState(() {
//       _saving = true;
//     });
//     checkAds();
//
//     //ตรวจสอบวันนี้ ว่าครบ 5 ครั้งหรือยัง
//     String _year = new DateTime.now().year.toString();
//     String _month = new DateTime.now().month.toString();
//     String _day = new DateTime.now().day.toString();
//     String _date = _day + '-' + _month + '-' + _year;
//
//     var searchRewards = FirebaseFirestore.instance
//         .collection('logs')
//         .doc('rewardsAds')
//         .collection(_date);
//
//     print('print logs');
//     searchRewards.where('address', isEqualTo: addressETH).get().then((data) {
//       countAds = data.docs.length;
//       print(countAds);
//       //ตรวจสอบว่ามีรายการน้อยกว่า 5 ไหม ถ้ายังไม่ถึงดูต่อได้จ้า
//       if (data.docs.length < 1) {
//         //แสดงวิดีโอ
//         AppAds.showVideo(state: this, videoListener: videoListener);
//       } else {
//         //โยน error ออกไปบอก user
//         setState(() {
//           _saving = false;
//         });
//         showColoredToast(
//             AppLocalizations.of(context)!.translate('limit_ads'), Colors.red);
//       }
//     });
//   }
//
//   ///ads admob ////
//   ///
//   ///
//   /// end ads admobb
//   ///
//   @override
//   void dispose() {
//     controller.dispose();
//     _animationController.dispose();
//     AppAds.dispose();
// //    ads?.dispose();
//     super.dispose();
//   }
//
//   void showShortToast(msg) {
//     Fluttertoast.showToast(
//         msg: msg,
//         toastLength: Toast.LENGTH_SHORT,
//         backgroundColor: Colors.cyan,
//         textColor: Colors.white);
//   }
//
//   setInit() async {
//     SharedPreferences pref = await SharedPreferences.getInstance();
//
//     configETH = new ConfigurationService(pref);
//     addressService = new AddressService(configETH);
//     mnemonic = pref.getString('seed');
//
// //    getPK = addressService.getPrivateKey(mnemonic);
//     getPK = configETH.getPrivateKey();
//
//     setState(() {
//       addressETH = configETH.getAddress();
//     });
//     checkRewards();
//
//     //get address
//
// //    addressService.getPublicAddress(getPK).then((address) {
//     print(addressETH);
//   }
//
//   void checkRewards() {
//     if (!mounted) return;
//     setState(() {
//       rewardsToday = 0;
//     });
//
//     String _year = new DateTime.now().year.toString();
//     String _month = new DateTime.now().month.toString();
//     String _day = new DateTime.now().day.toString();
//     String _date = _day + '-' + _month + '-' + _year;
//     var searchRewards = FirebaseFirestore.instance
//         .collection('logs')
//         .doc('rewardsAds')
//         .collection(_date);
//
//     searchRewards
//         .where('address', isEqualTo: addressETH)
//         .snapshots()
//         .listen((data) {
//       data.docs.forEach((doc) {
//         if (!mounted) return;
//         setState(() {
//           counting = data.docs.length;
//           rewardsToday += doc.data()["rewards"];
//         });
//       });
//     });
//   }
//
// //claim
//   _claimRewards() {
//     if (!mounted) return;
//     setState(() {
//       _saving = true;
//     });
//
//     eth.ClaimRewards(pk: getPK).then((tx) {
//       if (!mounted) return;
//       setState(() {
//         showColoredToast('You got rewards tx :' + tx, Colors.green);
//
//         new Future.delayed(new Duration(seconds: 8), () {
//           setInit();
//           _saving = false;
//         });
//       });
//     }).catchError((onError) {
//       _saving = false;
//     });
//   }
//
//   String get timerString {
//     Duration duration = controller.duration * controller.value;
//     return '${(duration.inHours % 60).toString().padLeft(2, '0')}:${(duration.inMinutes % 60).toString().padLeft(2, '0')}:${(duration.inSeconds % 60).toString().padLeft(2, '0')}';
//   }
//
//   TextEditingController addressText = TextEditingController();
//
//   @override
//   Widget build(BuildContext context) {
//     // TODO: implement build
//     return Scaffold(
//         body: ModalProgressHUD(
//             opacity: 0.1,
//             inAsyncCall: _saving,
//             progressIndicator: CustomLoading(),
//             child: Stack(alignment: Alignment.topCenter, children: <Widget>[
//               Container(),
//               _slider(),
//               BG(),
//               Positioned(
//                 top: mediaQuery(context, 'height', 100),
//                 left: mediaQuery(context, 'width', 75),
//                 child: backButton(context, LikeWalletAppTheme.gray),
//               ),
//               Positioned(
//                 top: mediaQuery(context, 'height', 236),
//                 left: mediaQuery(context, 'width', 75),
//                 child: _ads(),
//               ),
//               Positioned(
//                 top: mediaQuery(context, 'height', 480),
//                 child: _title(),
//               ),
// //              Positioned(
// //                top: mediaQuery(context, 'height', 951),
// //                child: _wacthNow(),
// //              ),
// //              Positioned(
// //                top: mediaQuery(context, 'height', 1371),
// //                child: _status(),
// //              ),
//             ])));
//   }
//
//   Widget _ads() {
//     return Align(
//         alignment: Alignment.centerLeft,
//         child: Container(
//           width: mediaQuery(context, 'width', 555.44),
//           child: Image.asset(LikeWalletImage.ads),
//         ));
//   }
//
//   Widget _title() {
//     return Column(
//       children: <Widget>[
//         Container(
//           margin: EdgeInsets.only(
//             bottom: mediaQuery(context, 'hieght', 30),
//           ),
//           child: Text(
//             AppLocalizations.of(context)!.translate('watch_get5like'),
//             textAlign: TextAlign.center,
//             style: TextStyle(
//               letterSpacing: 0.5,
//               fontFamily: AppLocalizations.of(context)!.translate('font1'),
//               color: LikeWalletAppTheme.white.withOpacity(1),
//               fontSize: mediaQuery(context, "height", 70),
//               fontWeight: FontWeight.w300,
//             ),
//           ),
//         ),
//         _wacthNow(),
//         _status()
//       ],
//     );
//   }
//
//   Widget _wacthNow() {
//     return FlatButton(
//         onPressed: () {
//           showWacthAds(context);
// //          _showDialog(1);
// //          Navigator.push(context,
// //              EnterExitRoute(exitPage: adsRewards(), enterPage: _ADS_BODY()));
//         },
//         child: Container(
//           margin: EdgeInsets.only(
//             bottom: mediaQuery(context, 'hieght', 30),
//           ),
//           height: mediaQuery(context, 'height', 379.67),
//           width: mediaQuery(context, 'width', 545),
//           child: Image.asset(
//             LikeWalletImage.wacth_now,
// //        color: Colors.indigo,
//           ),
//         ));
//   }
//
//   Widget _status() {
//     return Container(
//       child: Text(
//         AppLocalizations.of(context)!.translate('watch_status') +
//             " $counting " +
//             AppLocalizations.of(context)!.translate('watch_time'),
//         textAlign: TextAlign.center,
//         style: TextStyle(
//           letterSpacing: 1,
//           fontFamily: AppLocalizations.of(context)!.translate('font1'),
//           color: LikeWalletAppTheme.white.withOpacity(0.4),
//           fontSize: mediaQuery(context, "height", 36),
//           fontWeight: FontWeight.w500,
//         ),
//       ),
//     );
//   }
//
//   Widget _buttom() {
//     return Container(
//       width: mediaQuery(context, 'width', 1080),
//       child: Image.asset(
//         LikeWalletImage.watch_logo,
//       ),
//     );
//   }
//
//   Widget _ADS_BODY() {
//     return Scaffold(
//         body: Container(
//             alignment: Alignment.center,
//             child: FlatButton(
//               onPressed: () {
//                 Navigator.of(context).pop();
//                 //popup show
//                 Future.delayed(const Duration(milliseconds: 1500), () {
//                   setState(() {
//                     showfavorite = false;
//                   });
//                   //popup close
//                   Future.delayed(const Duration(milliseconds: 1500), () {
//                     setState(() {
//                       showfavorite = true;
//                     });
//                   });
//                 });
//               },
//               child: Icon(Icons.clear),
//             )));
//   }
//
//   Widget BG() {
//     return Container(
//       decoration: BoxDecoration(
//         boxShadow: [
//           BoxShadow(
//             color: Color(0xff037184).withOpacity(0.5),
//             spreadRadius: 5,
//             blurRadius: 10,
//             offset: Offset(0, 6), // changes position of shadow
//           ),
//         ],
//       ),
//       child: Image.asset(
//         LikeWalletImage.watch_bg,
//         fit: BoxFit.fill,
//         width: double.infinity,
//         height: mediaQuery(context, 'height', 1876.42),
//       ),
//     );
//   }
//
//   Widget _slider() {
//     return Positioned(
//         bottom: 0,
//         child: Stack(
//           alignment: Alignment.bottomCenter,
//           children: <Widget>[
//             Image.asset(
//               LikeWalletImage.watch_bottom,
//               fit: BoxFit.cover,
//               height: mediaQuery(context, 'height', 464),
//               width: MediaQuery.of(context).size.width,
//             ),
//             Positioned(
//               bottom: 0.0,
//               left: 0.0,
//               child: Container(
//                   alignment: Alignment.bottomRight,
//                   height: mediaQuery(context, 'height', 464),
//                   width: mediaQuery(context, 'width', 1080),
//                   child: CarouselSlider(
//                     options: CarouselOptions(
//                       aspectRatio: 17 / 9,
//                       viewportFraction: 1.0,
//                       initialPage: 0,
//                       enableInfiniteScroll: true,
//                       reverse: false,
//                       autoPlay: true,
//                       autoPlayInterval: Duration(seconds: 5),
//                       autoPlayAnimationDuration: Duration(milliseconds: 800),
//                       autoPlayCurve: Curves.fastOutSlowIn,
//                       enlargeCenterPage: true,
//                     ),
//                     items: imgList.map((i) {
//                       return Builder(
//                         builder: (BuildContext context) {
//                           return Container(
//                               width: mediaQuery(context, 'width', 1080),
//                               alignment: Alignment.bottomCenter,
//                               margin: i == imgList[0]
//                                   ? EdgeInsets.only(
//                                       right: mediaQuery(context, 'width', 90),
//                                     )
//                                   : EdgeInsets.only(
// //                                top: mediaQuery(context, 'height', 50),
//                                       right: mediaQuery(context, 'width', 90),
//                                     ),
// //                              margin: EdgeInsets.symmetric(horizontal: 5.0),
// //                            decoration: BoxDecoration(color: Colors.amber),
//                               child: Image.asset(
//                                 i,
//                               ));
//                         },
//                       );
//                     }).toList(),
//                   )),
//             ),
//           ],
//         )
// //          child:
//         );
//   }
// }
