import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/menu/howto.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/animationPage.dart';
import 'package:pattern_formatter/numeric_formatter.dart';
import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:likewallet/libraryman/address_service.dart';
import 'package:likewallet/libraryman/configuration_service.dart';
import 'package:likewallet/libraryman/ethcontract.dart';
// import 'package:likewallet/libraryman/sharmir.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:intl/intl.dart' as formatIntl;
import '../screen_util.dart';
import 'package:likewallet/libraryman/keyboard_done_widget.dart';
import 'dart:io' show Platform;

//import 'package:flutter_money_formatter/flutter_money_formatter.dart';
class lockLike extends StatefulWidget {
  _lockLike createState() => new _lockLike();
}

enum statusWithdraw { INACTIVE, ACTIVE }

class _lockLike extends State<lockLike> with TickerProviderStateMixin {
  final f = new formatIntl.NumberFormat("###,###");
  TextEditingController addressText = TextEditingController();
  TextEditingController amountController = TextEditingController();
  StreamController<String> streamAmountUnlock = StreamController<String>();
  StreamController<String> streamAmountLock = StreamController<String>();

  FocusNode lockFocusNode = new FocusNode();
  late OverlayEntry? overlayEntry;

  bool _lock = true;
  bool _all = false;
  late IAddressService addressService;
  late IConfigurationService configETH;
  // late SharmirInterface callSharmir;
  late BaseETH eth;
  late String pketh;
  late String mnemonic;
  late String ethAddr;
  String amountUnlock = '0';
  String amountLock = '0';
  late String totalLock;
  late String getPK;

  late String addressETH;
  late String unlockHour;
  late String unlockMin;
  late statusWithdraw isWithdraw;
  double diffTime = 0;
  bool _saving = false;
  bool isUnlock = false;
  late Timer _timer;
  late AnimationController _animationController;
  late AnimationController controller;
  KeyboardVisibilityController keyboardVisibilityController =
      KeyboardVisibilityController();

  @override
  void dispose() {
    controller.dispose();
    _animationController.dispose();
    _timer.cancel();

    streamAmountLock.close();
    streamAmountUnlock.close();
    lockFocusNode.dispose();
    super.dispose();
  }

  getFirstHowto() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    bool setFirstTime = pref.getBool('setFirstTime') ?? false;
    print(setFirstTime);
    if (setFirstTime == false) {
      Navigator.push(context, ScaleRoute(page: HowToLockLike()));
    }
  }

  @override
  void initState() {
    getFirstHowto();
    // TODO: implement initState
    super.initState();
    _animationController =
        new AnimationController(vsync: this, duration: Duration(seconds: 1));

    _animationController.repeat();
    controller = AnimationController(
      vsync: this,
      duration: Duration(seconds: 4),
    );
    {
      if (controller.isAnimating)
        controller.stop();
      else {
        controller.reverse(
            from: controller.value == 0.0 ? 1.0 : controller.value);
      }
    }
    ;

    new Future.delayed(new Duration(milliseconds: 10000), () {
      _timer = new Timer.periodic(
          new Duration(seconds: 60), (Timer timer) => setInit(source: 'loop'));
    });
    if (!mounted) return;
    setState(() {
      _saving = true;
    });
    new Future.delayed(new Duration(milliseconds: 500), () {
      eth = new EthContract();
      setInit();
    });

    //keyboard show done
    if (Platform.isIOS) {
      lockFocusNode.addListener(() {
        bool hasFocus = lockFocusNode.hasFocus;
        if (hasFocus)
          showOverlay(context);
        else
          removeOverlay();
      });

      keyboardVisibilityController.onChange.listen((bool visible) {
        removeOverlay();
      });
    }
  }

  //done button zone
  showOverlay(BuildContext context) {
    if (overlayEntry != null) return;
    OverlayState? overlayState = Overlay.of(context);
    overlayEntry = OverlayEntry(builder: (context) {
      return Positioned(
          bottom: MediaQuery.of(context).viewInsets.bottom,
          right: 0.0,
          left: 0.0,
          child: InputDoneView());
    });

    overlayState!.insert(overlayEntry!);
  }

  removeOverlay() {
    if (overlayEntry != null) {
      overlayEntry!.remove();
      overlayEntry = null;
    }
  }

  Future setInit({source}) async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    configETH = new ConfigurationService(pref);
    addressService = new AddressService(configETH);

    mnemonic = await configETH.getMnemonic();
    if (!mounted) return;
    setState(() {
      addressETH = configETH.getAddress();
    });
    getPK = addressService.getPrivateKey(mnemonic);
//    addressService.getPublicAddress(getPK).then((address) {
    print(addressETH);

//      if (!mounted) return;
//      setState(() {
//        addressETH = addressETH.toString();
//      });
    //ดึงเวลาปลดล็อค
    eth.getUnlockDate(address: addressETH).then((data) {
      print('unlock' + data.toString());
      print(data[3]);
      if (data[3] == '1') {
        if (!mounted) return;
        setState(() {
          isWithdraw = statusWithdraw.ACTIVE;
        });
      } else {
        isWithdraw = statusWithdraw.INACTIVE;
      }
      if (int.parse(data[0]) > 0 || int.parse(data[1]) > 0) {
        if (!mounted) return;
        setState(() {
          isUnlock = true;
          unlockHour = data[0];
          unlockMin = data[1];
          print('manzer');
          print(double.parse(data[2]));
          diffTime = double.parse(data[2]);
        });
      } else {
        if (!mounted) return;
        setState(() {
          isUnlock = false;
          diffTime = double.parse(data[2]);
        });

        print(diffTime);
      }
    });
    //เช็คยอด
    eth.getBalance(address: addressETH).then((balance) {
      print(balance);

      amountUnlock = balance.toString();
      streamAmountUnlock.sink.add(amountUnlock);
    });
    //เช็คยอดการล็อค
    eth.getBalanceLock(address: addressETH).then((balanceLock) {
      print(balanceLock);
      amountLock = balanceLock.toString();
      streamAmountLock.sink.add(amountLock);
      if (!mounted) return;
      setState(() {
        if (source != 'loop') {
          _saving = false;
        }
      });
    });
//    });getPublicAddress
  }

  _onChanged(bool value) async {
    if (!mounted) return;
    setState(() {
      _saving = true;
    });

    if (!value) {
      if (!mounted) return;
      setState(() {
        _saving = false;
      });
      showSimpleCustomDialog(context, 'unlock');
    } else {
      if (!mounted) return;
      setState(() {
        _saving = false;
      });
    }
  }

  _onChangedLock(bool value) async {
    if (!mounted) return;
    setState(() {
      _saving = true;
      _all = false;
    });

    if (value) {
      if (!mounted) return;
      setState(() {
        _saving = false;
      });
      showSimpleCustomDialog(context, 'lock');
    } else {
      if (!mounted) return;
      setState(() {
        _saving = false;
      });
    }
  }

//กดเคลมรับจากที่ล็อคกลับมา
  _unlockLikePoint() async {
    if (!mounted) return;
    setState(() {
      _saving = true;
    });
    eth.unlockLikePoint(pk: getPK, value: amountLock).then((transaction) {
      print('withdraw tx : $transaction');
      amountController.text = '0';
      new Future.delayed(new Duration(seconds: 5), () {
        setInit();
        totalLock = '';
        if (!mounted) return;
        setState(() {
          _lock = false;
          _saving = false;
          print(_lock);
        });
      });
    });
  }

//ร้องขอการปลดล็อค
  _requestUnlok() async {
    if (!mounted) return;
    setState(() {
      _saving = true;
    });

    eth.requestUnlock(pk: getPK).then((transaction) {
      print('withdraw tx : $transaction');
      amountController.text = '0';
      new Future.delayed(new Duration(seconds: 5), () {
        setInit();
        if (!mounted) return;
        setState(() {
          _lock = false;
          _saving = false;
          print(_lock);
        });
      });
    });
  }

  _getUnlockDate() async {
    if (!mounted) return;
    setState(() {
      _saving = true;
    });

    eth.getUnlockDate(address: addressETH).then((timeUnlock) {
      print('getUnlockDate : $timeUnlock');
      amountController.text = '0';
      setInit();
      if (!mounted) return;
      setState(() {
        _lock = false;
        _saving = false;
        print(_lock);
      });
    });
  }

  //Lock Like
  _lockLikepoint() async {
    if (!mounted) return;
    setState(() {
      _saving = true;
      _all = true;
    });
    //เอาจำนวนที่ล็อคตัด commas ออก
    totalLock = amountController.text.replaceAll(",", "");
    print(totalLock);
    eth.lockLikePoint(pk: getPK, value: totalLock).then((transaction) {
      List<String> tx = transaction.split(":");
      //transaction approve ขอสิทธิ์
      String approveTx = tx[0];
      //transaction Lock เหรียญ
      String lockTx = tx[1];
      print('approve tx : $approveTx');
      print('lock tx : $lockTx');
      //delay 2 วิเพื่อให้ ธุรกรรมเสร็จก่อน เพราะ tomochain block time คือ 2 sec
      new Future.delayed(new Duration(seconds: 2), () {
        setInit();
        amountController.text = '0';
        totalLock = '';
        if (!mounted) return;
        setState(() {
          _saving = false;
          _all = false;
          _lock = true;
        });
      });
    });
  }

  _allValue() {
    amountController.text = amountUnlock;
    totalLock = amountUnlock;
  }

  buildForPhone() {
    return SingleChildScrollView(
        child: Stack(
      alignment: Alignment.center,
      children: <Widget>[
        //พื้นหลังง
        background(context),
        //ขหัวเรื่อง
        Positioned(
          top: mediaQuery(context, "height", 0),
          child: Container(
            height: mediaQuery(context, "height", 468),
            width: mediaQuery(context, "width", 1080),
            color: LikeWalletAppTheme.bule1,
            child: _head(),
          ),
        ),
        //ปุ่มกกลับ
        Positioned(
            top: mediaQuery(context, "height", 105),
            child: backButton(context, LikeWalletAppTheme.gray)),
        //จำนวน like รวม
        Positioned(
            top: mediaQuery(context, "height", 577), child: _totallike()),
        //จำนวน like ที่ล็อค
        Positioned(top: mediaQuery(context, "height", 864), child: _locklike()),
        //ปลดล็อค like ที่ล็อค
        Positioned(
            top: mediaQuery(context, "height", 1071), child: _unlocklike()),
        //ใส่จำนวน like ที่ต้องการล็อค
        Positioned(
            top: mediaQuery(context, "height", 1278), child: _amountlike()),
        //ปุ่มล็อคlike
        Positioned(
          top: mediaQuery(context, "height", 1536),
          child: _switchlike(),
        ),
        //ปุ่มเรียกจำนวน like ทั้งหมด
        Positioned(
          top: mediaQuery(context, "height", 1530),
          child: _alllike(),
        ),
        //ปุ่มเคลม
        Positioned(
          top: mediaQuery(context, "height", 1748),
          child: _clamButton(),
        ),
        Positioned(
          top: mediaQuery(context, "height", 1900),
          child: _howto(),
        ),
        //เมนูบาร์
//        Container(
//          alignment: Alignment.bottomCenter,
//          height: MediaQuery.of(context).size.height,
//          child: NavigationBar(),
//        )  Container(
//          alignment: Alignment.bottomCenter,
//          height: MediaQuery.of(context).size.height,
//          child: NavigationBar(),
//        )
      ],
    ));
  }

  Widget _head() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: <Widget>[
        Expanded(child: Container()),
        Container(
            child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            new Text(
              AppLocalizations.of(context)!.translate('locklike_title1'),
              textAlign: TextAlign.start,
              style: TextStyle(
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  color: LikeWalletAppTheme.gray,
                  fontSize: mediaQuery(context, "height", 44),
                  fontWeight: FontWeight.normal),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: <Widget>[
                new Text(
                  AppLocalizations.of(context)!.translate('locklike_title2'),
                  style: TextStyle(
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      color: LikeWalletAppTheme.gray,
                      fontSize: mediaQuery(context, "height", 44),
                      fontWeight: FontWeight.normal),
                ),
                new Text(
                  AppLocalizations.of(context)!.translate('locklike_title3'),
                  style: TextStyle(
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      color: LikeWalletAppTheme.Purple,
                      fontSize: mediaQuery(context, "height", 44),
                      fontWeight: FontWeight.normal),
                ),
                new Text(
                  AppLocalizations.of(context)!.translate('locklike_title4'),
                  style: TextStyle(
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      color: LikeWalletAppTheme.gray,
                      fontSize: mediaQuery(context, "height", 44),
                      fontWeight: FontWeight.normal),
                ),
                Container(
//                          width: MediaQuery.of(context).size.height *
//                              0.05473504273,
                    child: Image.asset(
                  LikeWalletImage.icon_Hourlye,
                  height: mediaQuery(context, "height", 128.08),
                )),
              ],
            )
          ],
        )),
        Expanded(child: Container())
      ],
    );
  }

  Widget _totallike() {
    return Container(
        alignment: Alignment.center,
        child: Column(
          children: <Widget>[
            new Text(
              AppLocalizations.of(context)!.translate('locklike_total'),
              textAlign: TextAlign.right,
              style: LikeWalletAppTheme.LocklikeStyle(
                  context, 39, LikeWalletAppTheme.gray),
            ),
            StreamBuilder(
              stream: streamAmountUnlock.stream,
              initialData: '0',
              builder: (context, snapshot) {
                return new Text(
                  f.format(double.parse(snapshot.data.toString())).toString(),
                  textAlign: TextAlign.right,
                  style: LikeWalletAppTheme.LocklikeStyle(
                      context, 89, LikeWalletAppTheme.gray),
                );
              },
            ),
          ],
        ));
  }

  Widget _locklike() {
    return Container(
        alignment: Alignment.center,
        width: mediaQuery(context, 'width', 930),
        child: Column(
          children: <Widget>[
            new Text(
              AppLocalizations.of(context)!.translate('locklike_locked'),
              textAlign: TextAlign.right,
              style: LikeWalletAppTheme.LocklikeStyle(
                  context, 39, LikeWalletAppTheme.gray),
            ),
            StreamBuilder(
              stream: streamAmountLock.stream,
              initialData: '0',
              builder: (context, snapshot) {
                return new Text(
                  f.format(double.parse(snapshot.data.toString())).toString(),
                  textAlign: TextAlign.right,
                  style: LikeWalletAppTheme.LocklikeStyle(
                      context, 89, LikeWalletAppTheme.bule1),
                );
              },
            ),
          ],
        ));
  }

  Widget _unlocklike() {
    return Container(
        alignment: Alignment.center,
        height: mediaQuery(context, 'height', 132),
        width: mediaQuery(context, 'width', 930),
        child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: isWithdraw == statusWithdraw.INACTIVE
                ? isUnlock
                    ? _showUnlockTime()
                    : <Widget>[
                        new Text(
                          AppLocalizations.of(context)!
                              .translate('locklike_unlocked_switch'),
                          textAlign: TextAlign.left,
                          style: LikeWalletAppTheme.LocklikeStyle(
                              context, 35, LikeWalletAppTheme.gray),
                        ),
                        Padding(
                          padding: EdgeInsets.only(left: 20, right: 20),
                          child: Transform.scale(
                            scale: mediaQuery(context, "height", 3.5),
                            child: Switch(
                              value: _lock,
                              onChanged: _onChanged,
                              activeTrackColor: LikeWalletAppTheme.bule1,
                              activeColor: LikeWalletAppTheme.white,
                              //                        inactiveThumbColor: Color(0xff929194),
                              inactiveTrackColor: LikeWalletAppTheme.gray,
                            ),
                          ),
                        ),
                        new Text(
                          AppLocalizations.of(context)!
                              .translate('locklike_locked_switch'),
                          textAlign: TextAlign.right,
                          style: LikeWalletAppTheme.LocklikeStyle(
                              context, 35, LikeWalletAppTheme.gray),
                        ),

                        //isUnlock
                      ]
                : isUnlock
                    ? _showUnlockTime()
                    : _showWithdrawText()));
  }

  Widget _amountlike() {
    return Container(
        alignment: Alignment.center,
        child: Column(
          children: <Widget>[
            new Text(
              AppLocalizations.of(context)!.translate('locklike_amount'),
              textAlign: TextAlign.right,
              style: LikeWalletAppTheme.LocklikeStyle(
                  context, 41, LikeWalletAppTheme.gray),
            ),
            Container(
              margin: EdgeInsets.only(
                top: mediaQuery(context, "height", 20),
              ),
              alignment: Alignment.center,
              height: mediaQuery(context, "height", 132),
              width: mediaQuery(context, "width", 930),
              decoration: BoxDecoration(
                color: LikeWalletAppTheme.black,
                border: Border.all(
                  color: LikeWalletAppTheme.black,
                  width: 0.3,
                ),
                borderRadius: BorderRadius.all(Radius.circular(5.0)),
              ),
              child: TextField(
                inputFormatters: [ThousandsFormatter(allowFraction: true)],
                controller: amountController,
                showCursor: false,
                textAlign: TextAlign.center,
                style: LikeWalletAppTheme.LocklikeStyle(
                    context, 85, LikeWalletAppTheme.white),
                decoration: InputDecoration(
                  contentPadding: EdgeInsets.all(1),
                  hintText: '0',
                  hintStyle: LikeWalletAppTheme.LocklikeStyle(
                      context, 85, LikeWalletAppTheme.white),
                  border: InputBorder.none,
                ),
                keyboardType: TextInputType.number,
                focusNode: lockFocusNode,
              ),
            ),
          ],
        ));
  }

  Widget _switchlike() {
    return Container(
        child: Transform.scale(
      scale: mediaQuery(context, "height", 3.5),
      child: Switch(
        value: _all,
        onChanged: _onChangedLock,
//                        (bool value) => setState(() => {
//                          _activeTouchID = value,
//                          print(_activeTouchID),
//                        }),
        activeTrackColor: LikeWalletAppTheme.bule1,
        activeColor: LikeWalletAppTheme.white,
        //                        inactiveThumbColor: Color(0xff929194),
        inactiveTrackColor: LikeWalletAppTheme.gray,
      ),
    ));
  }

  Widget _alllike() {
    return Container(
        width: mediaQuery(context, "width", 930),
        alignment: Alignment.centerRight,
        child: new GestureDetector(
          onTap: () {
            _allValue();
          },
          child: ClipOval(
            child: Container(
              alignment: Alignment.center,
              color: LikeWalletAppTheme.gray.withOpacity(1),
              height: mediaQuery(context, "height", 100),
              width: mediaQuery(context, "height", 100),
              child: new Text(
                AppLocalizations.of(context)!.translate('locklike_all'),
                style: LikeWalletAppTheme.LocklikeStyle(
                    context, 34, LikeWalletAppTheme.black),
              ),
            ),
          ),
        ));
  }

  Widget _clamButton() {
    return Container(
        height: mediaQuery(context, 'height', 132),
        width: mediaQuery(context, 'width', 930),
        child: isWithdraw == statusWithdraw.ACTIVE
            ? diffTime <= 0
                ? _showWithdraw()
                : TextButton(
                    style: ButtonStyle(
                      shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                        RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                      ),
                      backgroundColor: MaterialStateProperty.resolveWith<Color>(
                        (Set<MaterialState> states) {
                          if (states.contains(MaterialState.disabled)) {
                            return LikeWalletAppTheme.gray
                                .withOpacity(0.64); // Disabled color
                          }
                          return LikeWalletAppTheme.gray
                              .withOpacity(0.64); // Regular color
                        },
                      ),
                    ),
                    onPressed: () {},
                    child: Text(
                      AppLocalizations.of(context)!
                          .translate('nothing_to_withdraw'),
                      style: LikeWalletAppTheme.LocklikeStyle(
                        context,
                        49,
                        LikeWalletAppTheme.white,
                      ),
                    ),
                  )
            : TextButton(
                style: ButtonStyle(
                  shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                    RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                  ),
                  backgroundColor: MaterialStateProperty.resolveWith<Color>(
                    (Set<MaterialState> states) {
                      if (states.contains(MaterialState.disabled)) {
                        return LikeWalletAppTheme.gray
                            .withOpacity(0.64); // Disabled color
                      }
                      return LikeWalletAppTheme.gray
                          .withOpacity(0.64); // Regular color
                    },
                  ),
                ),
                onPressed: () {},
                child: Text(
                  AppLocalizations.of(context)!
                      .translate('nothing_to_withdraw'),
                  style: LikeWalletAppTheme.LocklikeStyle(
                      context, 49, LikeWalletAppTheme.white),
                ),
              ));
  }

  Widget _howto() {
    return Container(
        width: mediaQuery(context, "width", 930),
        alignment: Alignment.centerRight,
        child: TextButton.icon(
            onPressed: () {
              Navigator.push(context, ScaleRoute(page: HowToLockLike()));
            },
            icon: Icon(
              Icons.help_outline,
              color: Colors.grey,
            ),
            label: Text(
              AppLocalizations.of(context)!.translate("locklike_howto"),
              style: TextStyle(color: Colors.grey),
            )));
  }

  Widget _showWithdraw() {
    return TextButton(
      style: ButtonStyle(
        shape: MaterialStateProperty.all<RoundedRectangleBorder>(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8.0),
          ),
        ),
        backgroundColor: MaterialStateProperty.resolveWith<Color>(
              (Set<MaterialState> states) {
            if (states.contains(MaterialState.disabled)) {
              return LikeWalletAppTheme.bule2.withOpacity(0.8); // Disabled color
            }
            return LikeWalletAppTheme.bule2.withOpacity(0.8); // Regular color
          },
        ),
      ),
      onPressed: () {
        _unlockLikePoint();
      },
      child: Text(
        AppLocalizations.of(context)!.translate('withdraw'),
        style: LikeWalletAppTheme.LocklikeStyle(
          context,
          59,
          LikeWalletAppTheme.lemon,
        ),
      ),
    );
  }

  List<Widget> _showWithdrawText() {
    return <Widget>[
      FadeTransition(
        opacity: _animationController,
        child: Text(
          AppLocalizations.of(context)!.translate('locklike_youcan'),
          textAlign: TextAlign.right,
          style: LikeWalletAppTheme.LocklikeStyle(
              context, 49, LikeWalletAppTheme.lemon),
        ),
      )
    ];
  }

  List<Widget> _showUnlockTime() {
    return <Widget>[
      new Text(
        'Unlock in ' + unlockHour + ' hour ' + unlockMin + ' min',
        style: TextStyle(color: Colors.white),
      )
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: ModalProgressHUD(
            opacity: 0.1,
            child: buildForPhone(),
            inAsyncCall: _saving,
            progressIndicator: CustomLoading()));
  }

  void showSimpleCustomDialog(BuildContext context, action) {
    Dialog simpleDialog = Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Container(
          height: mediaQuery(context, 'height', 750),
          width: mediaQuery(context, 'width', 927),
          child: Padding(
            padding: EdgeInsets.all(
              mediaQuery(context, 'height', 50),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Container(
                  padding: EdgeInsets.only(
                    top: mediaQuery(context, 'height', 50),
                    bottom: mediaQuery(context, 'height', 100),
                  ),
                  child: Text(
                    action == 'unlock'
                        ? AppLocalizations.of(context)!
                            .translate('locklike_modal_unlock1')
//                    ? AppLocalizations.of(context)!.translate('hourlyRewards_modal_unlock1') +' $amountLock '+ AppLocalizations.of(context)!.translate('hourlyRewards_modal_unlock2')
                        : AppLocalizations.of(context)!
                                .translate('locklike_modal_unlock2') +
                            ' ' +
                            amountController.text +
                            ' LIKE',
//                    : '$action ' + amountController.text + ' ',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                        color: LikeWalletAppTheme.gray,
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                        fontSize: MediaQuery.of(context).size.height *
                            Screen_util("height", 50),
                        fontWeight: FontWeight.normal),
                  ),
                ),
                new Container(
                  alignment: Alignment.center,
                  height: mediaQuery(context, 'height', 132),
                  width: mediaQuery(context, 'width', 816),
                  child: ButtonTheme(
                    height: MediaQuery.of(context).size.height,
                    minWidth: MediaQuery.of(context).size.width,
                    child: TextButton(
                      style: ButtonStyle(
                        shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                          RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                        ),
                        backgroundColor: MaterialStateProperty.resolveWith<Color>(
                              (Set<MaterialState> states) {
                            if (states.contains(MaterialState.disabled)) {
                              return LikeWalletAppTheme.bule1; // Disabled color
                            }
                            return LikeWalletAppTheme.bule1; // Regular color
                          },
                        ),
                      ),
                      onPressed: () {
                        if (action == 'unlock') {
                          // _unlockLikePoint();
                          _requestUnlok();
                          Navigator.pop(context);
                        } else {
                          totalLock = amountController.text.replaceAll(",", "");
                          if (totalLock != '') {
                            if (double.parse(totalLock) > 0) {
                              _lockLikepoint().then((callback) {
                                Navigator.pop(context);
                              });
                            } else {
                              setState(() {
                                _saving = false;
                              });
                            }
                          } else {
                            setState(() {
                              _saving = false;
                            });
                          }
                        }
                      },
                      child: Text(
                        AppLocalizations.of(context)!.translate('locklike_modal_confirm'),
                        style: TextStyle(
                          color: Colors.black,
                          fontFamily: AppLocalizations.of(context)!.translate('font1'),
                          fontSize: MediaQuery.of(context).size.height * Screen_util("height", 53),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  height: MediaQuery.of(context).size.height * 0.02,
                ),
                new Container(
                  alignment: Alignment.center,
                  height: mediaQuery(context, 'height', 132),
                  width: mediaQuery(context, 'width', 816),
                  child: ButtonTheme(
                    height: MediaQuery.of(context).size.height,
                    minWidth: MediaQuery.of(context).size.width,
                    child: TextButton(
                      style: ButtonStyle(
                        shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                          RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                        ),
                        backgroundColor: MaterialStateProperty.resolveWith<Color>(
                              (Set<MaterialState> states) {
                            if (states.contains(MaterialState.disabled)) {
                              return LikeWalletAppTheme.gray.withOpacity(0.2); // Disabled color
                            }
                            return LikeWalletAppTheme.gray.withOpacity(0.2); // Regular color
                          },
                        ),
                      ),
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      child: Text(
                        AppLocalizations.of(context)!.translate('locklike_modal_cancel'),
                        style: TextStyle(
                          color: Colors.black,
                          fontFamily: AppLocalizations.of(context)!.translate('font1'),
                          fontSize: MediaQuery.of(context).size.height *
                              Screen_util("height", 53),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          )),
    );
    showDialog(
        context: context, builder: (BuildContext context) => simpleDialog);
  }
}
