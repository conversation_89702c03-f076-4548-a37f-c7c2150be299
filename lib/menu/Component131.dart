// import 'package:flutter/material.dart';
// import 'package:adobe_xd/gradient_xd_transform.dart';
// import 'package:flutter_svg/flutter_svg.dart';
//
// class Locklike_BG extends StatelessWidget {
//   Locklike_BG({
//     Key? key,
//   }) : super(key: key);
//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       height: double.infinity,
//       child: SvgPicture.string(
//         _svg_1plqny,
//         allowDrawingOutsideViewBox: true,
//       ),
//     );
//   }
// }
//
// const String _svg_1plqny =
//     '<svg viewBox="0.0 0.0 1080.0 1876.4" ><defs><filter id="shadow"><feDropShadow dx="0" dy="25" stdDeviation="65"/></filter><radialGradient id="gradient" gradientTransform="matrix(0.0 1.0 -3.018655 0.0 2.009327 0.0)" fx="0.5" fy="0.5" fr="0.0" cx="0.5" cy="0.5" r="0.5"><stop offset="0.0" stop-color="#2f4558" /><stop offset="0.29242" stop-color="#2f4558" /><stop offset="1.0" stop-color="#101a2f" /></radialGradient></defs><path transform="translate(0.0, -273.58)" d="M 1080 273.578125 L 1080 2150 L 0 2150 C 0 2150 0 2085.976806640625 0 273.578125 L 1080 273.578125 Z" fill="url(#gradient)" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" filter="url(#shadow)"/></svg>';
