import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_countdown_timer/flutter_countdown_timer.dart';
import 'package:flutter_countdown_timer/index.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:likewallet/model/lotto/chack_your_lotto.dart';
import 'package:likewallet/model/lotto/your_lotto.dart';
import 'package:likewallet/model/lotto/your_lotto_list_pre_round.dart';

class LottoComponent {
  LottoComponent._();

  static Widget headLogo() {
    return Padding(
      padding: const EdgeInsets.only(top: 20, left: 10),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            child: Image.network(
              'https://s3-ap-southeast-1.amazonaws.com/storage-pkg/icon/icon1625543339Group%2034348.png',
              height: 395.45.h,
              width: 279.37.w,
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(top: 0, right: 30),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text('หมายเลขที่ถูกรางวัล'),
                SizedBox(
                  height: 10,
                ),
                Container(
                  width: 288.w,
                  height: 80.h,
                  child: Text(
                    'รอบที่เพิ่งออก',
                    textAlign: TextAlign.center,
                    textScaleFactor: 1,
                    style: TextStyle(color: Colors.black, fontSize: 12),
                  ),
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),

          // Container(
          //   child: Text(''),
          // )
        ],
      ),
    );
  }

  static Widget blockSize() {
    return Container(
      margin: EdgeInsets.only(top: 300.h),
      height: 400.h,
      width: double.infinity,
      color: Color(0xFF707070).withOpacity(0.3),
    );
  }

  static Widget circleBlock({required String text}) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20.w),
      height: 117.89.h,
      width: 117.89.h,
      child: Stack(alignment: Alignment.center, children: [
        SvgPicture.string(
          '<svg viewBox="428.4 578.0 117.9 117.9" ><defs><filter id="shadow"><feDropShadow dx="6" dy="24" stdDeviation="24"/></filter></defs><path transform="translate(1395.0, 1813.56)" d="M -907.6489868164062 -1235.557983398438 C -875.094970703125 -1235.557983398438 -848.7039794921875 -1209.1669921875 -848.7039794921875 -1176.613037109375 C -848.7039794921875 -1144.057983398438 -875.094970703125 -1117.66796875 -907.6489868164062 -1117.66796875 C -940.2030029296875 -1117.66796875 -966.593994140625 -1144.057983398438 -966.593994140625 -1176.613037109375 C -966.593994140625 -1209.1669921875 -940.2030029296875 -1235.557983398438 -907.6489868164062 -1235.557983398438 L -907.6489868164062 -1235.557983398438 Z" fill="#000000" stroke="none" stroke-width="1" stroke-miterlimit="10" stroke-linecap="butt" filter="url(#shadow)"/></svg>',
          allowDrawingOutsideViewBox: true,
          // fit: BoxFit.none,
        ),
        Text(
          '${text}',
          style: TextStyle(
              color: Colors.white, fontSize: 15, fontWeight: FontWeight.bold),
        ),
      ]),
    );
  }

  static Widget preRound({required double round}) {
    return Row(
      children: [
        Text(
          'ครั้งที่   ',
          style: TextStyle(
              color: Colors.black, fontSize: 16, fontWeight: FontWeight.normal),
        ),
        Text(
          "#" + round.toStringAsFixed(0),
          style: TextStyle(
              color: Colors.white, fontSize: 16, fontWeight: FontWeight.normal),
        )
      ],
    );
  }

  static Widget preRoundTotalReward({required String reward}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'รางวัลทั้งหมด',
          style: TextStyle(
              color: Colors.black, fontSize: 16, fontWeight: FontWeight.normal),
        ),
        Text(
          reward + " LIKE",
          style: TextStyle(
              color: Colors.black, fontSize: 16, fontWeight: FontWeight.normal),
        )
      ],
    );
  }

  static Widget amountPreWonLottoTitle() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        Flexible(
          flex: 1,
          fit: FlexFit.tight,
          child: Text(
            'รางวัลที่',
            textAlign: TextAlign.left,
            style: TextStyle(
                color: Colors.black, fontSize: 14, fontWeight: FontWeight.bold),
          ),
        ),
        Flexible(
          flex: 1,
          fit: FlexFit.tight,
          child: Text(
            'จำนวนคนถูก',
            textAlign: TextAlign.center,
            style: TextStyle(
                color: Colors.black, fontSize: 14, fontWeight: FontWeight.bold),
          ),
        ), //
        Flexible(
          flex: 1,
          fit: FlexFit.tight,
          child: Text(
            'จะได้รับ (LIKE)',
            textAlign: TextAlign.right,
            style: TextStyle(
                color: Colors.black, fontSize: 14, fontWeight: FontWeight.bold),
          ),
        ), //
      ],
    );
  }

  static Widget amountPreWonLotto(
      {required String num, required String amount, required String reward}) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 10.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          Flexible(
            flex: 1,
            fit: FlexFit.tight,
            child: Text(
              "รางวัลที่ " + num,
              textAlign: TextAlign.left,
              style: TextStyle(
                  color: Colors.black,
                  fontSize: 14,
                  fontWeight: FontWeight.normal),
            ),
          ),
          Flexible(
            flex: 1,
            fit: FlexFit.tight,
            child: Text(
              amount,
              textAlign: TextAlign.center,
              style: TextStyle(
                  color: Colors.black,
                  fontSize: 14,
                  fontWeight: FontWeight.normal),
            ),
          ), //
          Flexible(
            flex: 1,
            fit: FlexFit.tight,
            child: Text(
              reward,
              textAlign: TextAlign.right,
              style: TextStyle(
                  color: Colors.black,
                  fontSize: 14,
                  fontWeight: FontWeight.normal),
            ),
          ), //
        ],
      ),
    );
  }

  static Widget yourAmountLottoPreRound(
      {required String amount,
      List<YourLotto>? data,
      required List<String> wonPreNumber,
      required BuildContext context,
      required bool check,
      required double totalReward,
      required Function() onPressedFor,
      required Function() onPressed}) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 10.h),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "ตั๋วเลขของคุณ",
                    textAlign: TextAlign.left,
                    style: TextStyle(
                        color: Colors.black,
                        fontSize: 14,
                        fontWeight: FontWeight.normal),
                  ),
                  Text(
                    amount,
                    textAlign: TextAlign.left,
                    style: TextStyle(
                        color: Colors.black,
                        fontSize: 14,
                        fontWeight: FontWeight.normal),
                  ),
                ],
              ),
              TextButton(
                onPressed: onPressedFor,
                child: Container(
                  width: 568.0.w,
                  height: 100.0.h,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(50.0),
                    color: const Color(0xff16192f),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0x29000000),
                        offset: Offset(3, 3),
                        blurRadius: 5,
                      ),
                    ],
                  ),
                  child: Text(
                    'ดูเลขล็อตโต้ของคุณ',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.normal),
                  ),
                ),
              ),
            ],
          ),
          buttonClaim(
              check: check, totalReward: totalReward, onPressed: onPressed),
        ],
      ),
    );
  }

  static Widget buttonClaim(
      {required bool check,
      required double totalReward,
      required Function() onPressed}) {
    return check
        ? Column(
            children: [
              Text(
                "ยินดีด้วย คุณชนะรางวัล!",
                textAlign: TextAlign.left,
                style: TextStyle(
                    color: Colors.black,
                    fontSize: 14,
                    fontWeight: FontWeight.normal),
              ),
              // SizedBox(height: 5),
              // Text(
              //   totalReward.toString() + " LIKE",
              //   textAlign: TextAlign.left,
              //   style: TextStyle(
              //       color: Colors.black,
              //       fontSize: 14,
              //       fontWeight: FontWeight.normal),
              // ),
              SizedBox(height: 5),
              TextButton(
                onPressed: onPressed,
                child: Container(
                  // width: 568.0.w,
                  height: 132.0.h,
                  padding: EdgeInsets.symmetric(horizontal: 16),
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.0),
                    color: Colors.black,
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0x29000000),
                        offset: Offset(3, 3),
                        blurRadius: 5,
                      ),
                    ],
                  ),
                  child: Text(
                    'เคลมรางวัลของคุณ',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.normal),
                  ),
                ),
              ),
            ],
          )
        : Container();
  }

  static Widget textTitle({required String at}) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 10.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            "ครั้งที่ ",
            textAlign: TextAlign.left,
            style: TextStyle(
                color: Colors.black,
                fontSize: 16,
                fontWeight: FontWeight.normal),
          ),
          Text(
            "#" + at,
            textAlign: TextAlign.left,
            style: TextStyle(
                color: Colors.black,
                fontSize: 16,
                fontWeight: FontWeight.normal),
          ),
        ],
      ),
    );
  }

  static Widget rules({required String text}) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 10.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 280.0.w,
            height: 60.0.h,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(50.0),
              color: const Color(0xff707070).withOpacity(0.1),
            ),
            child: Text(
              'กติกา',
              textAlign: TextAlign.center,
              style: TextStyle(
                  color: Colors.black,
                  fontSize: 10,
                  fontWeight: FontWeight.normal),
            ),
          ),
        ],
      ),
    );
  }

  static Widget annoncesTime(
      {required String dateTime,
      required CountdownTimerController controller}) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 10.h),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "เวลาประกาศรางวัล:",
                textAlign: TextAlign.left,
                style: TextStyle(
                    color: Colors.black.withOpacity(0.6),
                    fontSize: 16,
                    fontWeight: FontWeight.normal),
              ),
              Text(
                dateTime,
                textAlign: TextAlign.left,
                style: TextStyle(
                    color: Colors.black.withOpacity(0.6),
                    fontSize: 16,
                    fontWeight: FontWeight.normal),
              ),
            ],
          ),
          SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "เหลือเวลาอีก",
                textAlign: TextAlign.left,
                style: TextStyle(
                    color: Colors.black.withOpacity(0.6),
                    fontSize: 16,
                    fontWeight: FontWeight.normal),
              ),
              CountdownTimer(
                controller: controller,
                widgetBuilder: (_, CurrentRemainingTime? time) {
                  if (time == null) {
                    return Text('Game over');
                  }
                  return Text(
                      '${time.days == null ? 0 : time.days} วัน ${time.hours == null ? 0 : time.hours} ชั่วโมง ${time.min == null ? 0 : time.min} นาที ${time.sec == null ? 0 : time.sec} วินาที',
                      style: TextStyle(
                          color: Colors.black,
                          fontSize: 16,
                          fontWeight: FontWeight.normal));
                },
              ),
            ],
          ),
          SizedBox(height: 20),
        ],
      ),
    );
  }

  static Widget amountWonLottoTitle() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        Flexible(
          flex: 1,
          fit: FlexFit.tight,
          child: Text(
            'จำนวนเลขตรง',
            textAlign: TextAlign.left,
            style: TextStyle(
                color: Colors.black, fontSize: 14, fontWeight: FontWeight.bold),
          ),
        ),
        Flexible(
          flex: 1,
          fit: FlexFit.tight,
          child: Text(
            'จำนวนผู้ถูกรางวัล',
            textAlign: TextAlign.center,
            style: TextStyle(
                color: Colors.black, fontSize: 14, fontWeight: FontWeight.bold),
          ),
        ), //
        Flexible(
          flex: 1,
          fit: FlexFit.tight,
          child: Text(
            'รางวัล (LIKE)',
            textAlign: TextAlign.right,
            style: TextStyle(
                color: Colors.black, fontSize: 14, fontWeight: FontWeight.bold),
          ),
        ), //
      ],
    );
  }

  static Widget amountWonLotto(
      {required String num, required String amount, required String reward}) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 10.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          Flexible(
            flex: 1,
            fit: FlexFit.tight,
            child: Text(
              num + " ตัว",
              textAlign: TextAlign.left,
              style: TextStyle(
                  color: Colors.black,
                  fontSize: 14,
                  fontWeight: FontWeight.normal),
            ),
          ),
          Flexible(
            flex: 1,
            fit: FlexFit.tight,
            child: Text(
              amount,
              textAlign: TextAlign.center,
              style: TextStyle(
                  color: Colors.black,
                  fontSize: 14,
                  fontWeight: FontWeight.normal),
            ),
          ), //
          Flexible(
            flex: 1,
            fit: FlexFit.tight,
            child: Text(
              reward,
              textAlign: TextAlign.right,
              style: TextStyle(
                  color: Colors.black,
                  fontSize: 14,
                  fontWeight: FontWeight.normal),
            ),
          ), //
        ],
      ),
    );
  }

  static Widget containerBuyTicket({required String amount}) {
    return // Adobe XD layer: 'Rectangle 5891' (shape)
        Column(
      children: [
        SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              "จำนวนตั๋วของคุณ",
              textAlign: TextAlign.left,
              style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.normal),
            ),
            Text(
              amount,
              textAlign: TextAlign.left,
              style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.normal),
            ),
          ],
        ),
      ],
    );
  }

  static Widget yourLottoNumber({required List<YourLotto>? data}) {
    return data!.length > 0
        ? Column(
            children: [
              Container(
                margin: EdgeInsets.symmetric(vertical: 16),
                child: Text(
                  "เลขของคุณ",
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      color: Colors.black,
                      fontSize: 16,
                      fontWeight: FontWeight.normal),
                ),
              ),
              Card(
                  color: const Color(0xFF707070),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Container(
                    alignment: Alignment.center,
                    padding: EdgeInsets.all(16.0),
                    child: ListView.builder(
                        shrinkWrap: true,
                        itemCount: data.length,
                        scrollDirection: Axis.vertical,
                        itemBuilder: (BuildContext context, int index) {
                          return Center(
                            child: Text(
                              data[index].number.join().toString(),
                              style:
                                  TextStyle(color: Colors.white, fontSize: 20),
                            ),
                          );
                        }),
                  ))
            ],
          )
        : Container();
  }
}
