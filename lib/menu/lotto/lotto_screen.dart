import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_countdown_timer/flutter_countdown_timer.dart';

import 'package:flutter_countdown_timer/index.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:likewallet/libraryman/address_service.dart';
import 'package:likewallet/libraryman/configuration_service.dart';
import 'package:likewallet/menu/lotto/lotto_component.dart';
import 'package:likewallet/menu/lotto/lotto_service.dart';
import 'package:likewallet/model/lotto/annonces_time.dart';
import 'package:likewallet/model/lotto/chack_your_lotto.dart';
import 'package:likewallet/model/lotto/lotto_now_round.dart';
import 'package:likewallet/model/lotto/lotto_round.dart';
import 'package:likewallet/model/lotto/your_lotto.dart';
import 'package:likewallet/model/lotto/your_lotto_list.dart';
import 'package:likewallet/model/lotto/your_lotto_list_pre_round.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../Theme.dart';

class LottoScreen extends StatefulWidget {
  _LottoScreen createState() => new _LottoScreen();
}

class _LottoScreen extends State<LottoScreen> with TickerProviderStateMixin {
  late IConfigurationService configETH;
  late IAddressService addressService;
  late LotteryServiceFunction lotteryService;
  late CountdownTimerController controller;
  var getPK;
  var address;
  bool showTicket = true;
  bool buyTicket = false;
  bool checkMultiClaim = false;
  double? lastRound;
  double? preRound;
  double totalReward = 0.0;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    firstStepInitState();
  }

  firstStepInitState() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    configETH = new ConfigurationService(pref);
    lotteryService = LotteryService();
    addressService = new AddressService(configETH);
    var mnemonic = await configETH.getMnemonic();
    setState(() {
      getPK = addressService.getPrivateKey(mnemonic);
      address = configETH.getAddress();
      // address = addressService.ge tPublicAddress(getPK);
    });

    AnnoncesTime annoncesTime = await lotteryService.annoncesTime();
    controller = CountdownTimerController(
        endTime: annoncesTime.date.millisecondsSinceEpoch, onEnd: onEnd);

    if (!mounted) return;
    setState(() {
      // addressETH = configETH.getAddress();
      // addressETH = '******************************************';
    });
  }

  void onEnd() {
    print('onEnd');
  }

  Future<bool> checkApprove() async {
    address = configETH.getAddress();
    bool result = false;
    if (address != null) {
      result = await lotteryService.checkApprove(address: address);
    }
    return result;
  }

  Future<AnnoncesTime> getAnnoncesTime() async {
    AnnoncesTime annoncesTime = await lotteryService.annoncesTime();
    return annoncesTime;
  }

  Future<double> getPreviousRoundNumber() async {
    return await lotteryService.lastRound() - 1;
  }

  Future<double> getNowRoundNumber() async {
    lastRound = await lotteryService.lastRound();
    return lastRound!;
  }

  Future<LottoRound> getRoundLast() async {
    lastRound = await lotteryService.lastRound();
    double previousRound = lastRound! - 1;
    LottoRound lottoRoundLast =
        await lotteryService.lottoRound(round: previousRound.toInt());
    return lottoRoundLast;
  }

  Future<double> getBalanceTicket() async {
    var balanceTicket = await lotteryService.balanceTicket(address: address);
    return balanceTicket;
  }

  Future<YourLottoListPreRound> getYourLottoPreRound() async {
    late YourLottoListPreRound yourLottoListPreRound;
    if (getPK != null) {
      preRound = await lotteryService.lastRound() - 1;
      final data = await lotteryService.lottoPreNumber(
          pketh: getPK, round: preRound!.toInt());
      // for (int i = 0; i < data[0].length; i++) {
      //   print(data[0][i]['reward'] / 10e17);
      //   if (data[0][i]['reward'] / 10e17 > 0) {
      //     print('test');
      //   }
      // }
      checkMultiClaim = await lotteryService.checkMultiClaim(data[1]);
      yourLottoListPreRound = YourLottoListPreRound.fromJson(data[0]);
    }
    return yourLottoListPreRound;
  }

  Future<YourLottoList> getYourLottoRound() async {
    late YourLottoList yourLottoList;
    if (getPK != null) {
      var round = await lotteryService.lastRound();
      yourLottoList =
          await lotteryService.lottoNumber(pketh: getPK, round: round.toInt());
    }
    return yourLottoList;
  }

  Future<YourLottoList> getRewardClaim() async {
    late YourLottoList yourLottoList;
    if (getPK != null) {
      var round = await lotteryService.lastRound();
      yourLottoList =
          await lotteryService.lottoNumber(pketh: getPK, round: round.toInt());
    }
    return yourLottoList;
  }

  Future<LottoNowRound> gatLottoNowRound() async {
    LottoNowRound lottoNowRound = await lotteryService.lottoNowRound();
    return lottoNowRound;
  }

  Future<void> _pullRefresh() async {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // backgroundColor: Color(0xFFD8D8D8),
      appBar: AppBar(
        backgroundColor: Colors.black,
      ),
      body: getPK == null && address == null
          ? Container()
          : RefreshIndicator(
              onRefresh: _pullRefresh,
              child: Container(
                height: double.infinity,
                width: double.infinity,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          color: Color(0xFFD8D8D8),
                          borderRadius: BorderRadius.only(
                              bottomLeft: Radius.circular(20.0),
                              bottomRight: Radius.circular(20.0)),
                        ),
                        child: Stack(
                          children: [
                            LottoComponent.blockSize(),
                            LottoComponent.headLogo(),
                            bodyPreRound(),
                          ],
                        ),
                      ),
                      SizedBox(
                        height: 100.h,
                      ),
                      bodyNowRound(),
                      // FutureBuilder(
                      //   future: getYourLottoRound(),
                      //   builder: (
                      //     BuildContext context,
                      //     AsyncSnapshot<YourLottoList> snapshot,
                      //   ) {
                      //     print(snapshot.connectionState);
                      //     if (snapshot.connectionState ==
                      //         ConnectionState.waiting) {
                      //       return Container();
                      //     } else if (snapshot.connectionState ==
                      //         ConnectionState.done) {
                      //       if (snapshot.hasError) {
                      //         return const Text('Error');
                      //       } else if (snapshot.hasData) {
                      //         print(snapshot.data);
                      //         // return Text(snapshot.data.toString());
                      //         return yourLotto(data: snapshot.data!.data);
                      //       } else {
                      //         return const Text('Empty data');
                      //       }
                      //     } else {
                      //       return Text('State: ${snapshot.connectionState}');
                      //     }
                      //   },
                      // ),
                    ],
                  ),
                ),
              ),
            ),
    );
  }

  bodyPreRound() {
    return Container(
      padding: EdgeInsets.only(top: 640.h),
      margin: EdgeInsets.only(bottom: 16),
      child: FutureBuilder(
        future: getRoundLast(),
        builder: (
          BuildContext context,
          AsyncSnapshot<LottoRound> snapshot,
        ) {
          print(snapshot.connectionState);
          if (snapshot.connectionState == ConnectionState.waiting) {
            return Center(
              child: SpinKitThreeBounce(
                color: Colors.grey,
                size: 100.h,
              ),
            );
          } else if (snapshot.connectionState == ConnectionState.done) {
            if (snapshot.hasError) {
              return const Text('Error');
            } else if (snapshot.hasData) {
              return lottoPreTime(data: snapshot.data!);
            } else {
              return const Text('Empty data');
            }
          } else {
            return Text('State: ${snapshot.connectionState}');
          }
        },
      ),
    );
  }

  bodyNowRound() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          FutureBuilder(
            future: getNowRoundNumber(),
            builder: (
              BuildContext context,
              AsyncSnapshot<double> snapshot,
            ) {
              print(snapshot.connectionState);
              if (snapshot.connectionState == ConnectionState.waiting) {
                return SpinKitThreeBounce(
                  color: Colors.grey,
                  size: 100.h,
                );
              } else if (snapshot.connectionState == ConnectionState.done) {
                if (snapshot.hasError) {
                  return const Text('Error');
                } else if (snapshot.hasData) {
                  return LottoComponent.textTitle(
                      at: snapshot.data!.toStringAsFixed(0));
                } else {
                  return const Text('Empty data');
                }
              } else {
                return Text('State: ${snapshot.connectionState}');
              }
            },
          ),
          LottoComponent.rules(text: 'กติกา'),
          SizedBox(height: 20),
          FutureBuilder(
            future: getAnnoncesTime(),
            builder: (
              BuildContext context,
              AsyncSnapshot<AnnoncesTime> snapshot,
            ) {
              print(snapshot.connectionState);
              if (snapshot.connectionState == ConnectionState.waiting) {
                return Center(
                  child: SpinKitThreeBounce(
                    color: Colors.grey,
                    size: 100.h,
                  ),
                );
              } else if (snapshot.connectionState == ConnectionState.done) {
                if (snapshot.hasError) {
                  return const Text('Error');
                } else if (snapshot.hasData) {
                  return LottoComponent.annoncesTime(
                      dateTime: snapshot.data!.formattedDate,
                      controller: controller);
                } else {
                  return const Text('Empty data');
                }
              } else {
                return Text('State: ${snapshot.connectionState}');
              }
            },
          ),
          lottoNowTime(),
          FutureBuilder(
            future: getYourLottoRound(),
            builder: (
              BuildContext context,
              AsyncSnapshot<YourLottoList> snapshot,
            ) {
              print(snapshot.connectionState);
              if (snapshot.connectionState == ConnectionState.waiting) {
                return Center(
                  child: SpinKitThreeBounce(
                    color: Colors.grey,
                    size: 70.h,
                  ),
                );
              } else if (snapshot.connectionState == ConnectionState.done) {
                if (snapshot.hasError) {
                  return const Text('Error');
                } else if (snapshot.hasData) {
                  return snapshot.data!.data!.length > 0
                      ? LottoComponent.yourLottoNumber(
                          data: snapshot.data!.data)
                      : Container();
                } else {
                  return const Text('Empty data');
                }
              } else {
                return Text('State: ${snapshot.connectionState}');
              }
            },
          )
        ],
      ),
    );
  }

  Widget lottoNowTime() {
    return FutureBuilder(
      future: gatLottoNowRound(),
      builder: (
        BuildContext context,
        AsyncSnapshot<LottoNowRound> snapshot,
      ) {
        print(snapshot.connectionState);
        if (snapshot.connectionState == ConnectionState.waiting) {
          return SpinKitThreeBounce(
            color: Colors.grey,
            size: 100.h,
          );
        } else if (snapshot.connectionState == ConnectionState.done) {
          if (snapshot.hasError) {
            return const Text('Error');
          } else if (snapshot.hasData) {
            return Column(children: [
              LottoComponent.amountPreWonLottoTitle(),
              LottoComponent.amountPreWonLotto(
                  num: "1",
                  amount: "4",
                  reward: snapshot.data!.totalNowReward4),
              LottoComponent.amountPreWonLotto(
                  num: "2",
                  amount: "3",
                  reward: snapshot.data!.totalNowReward3),
              LottoComponent.amountPreWonLotto(
                  num: "3",
                  amount: "2",
                  reward: snapshot.data!.totalNowReward2),
              Container(
                  width: 1000.0.w,
                  padding: EdgeInsets.symmetric(horizontal: 16),
                  margin: EdgeInsets.symmetric(vertical: 16),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20.0),
                    color: Color(0xcc000000),
                  ),
                  child: Column(
                    children: [
                      FutureBuilder(
                        future: getBalanceTicket(),
                        builder: (
                          BuildContext context,
                          AsyncSnapshot<double> snapshot,
                        ) {
                          print(snapshot.connectionState);
                          if (snapshot.connectionState ==
                              ConnectionState.waiting) {
                            return Center(
                              child: SpinKitThreeBounce(
                                color: Colors.grey,
                                size: 100.h,
                              ),
                            );
                          } else if (snapshot.connectionState ==
                              ConnectionState.done) {
                            if (snapshot.hasError) {
                              return const Text('Error');
                            } else if (snapshot.hasData) {
                              return LottoComponent.containerBuyTicket(
                                  amount: snapshot.data!.toStringAsFixed(0));
                            } else {
                              return const Text('Empty data');
                            }
                          } else {
                            return Text('State: ${snapshot.connectionState}');
                          }
                        },
                      ),
                      FutureBuilder(
                        future: checkApprove(),
                        builder: (
                          BuildContext context,
                          AsyncSnapshot<bool> snapshot,
                        ) {
                          print(snapshot.connectionState);
                          if (snapshot.connectionState ==
                              ConnectionState.waiting) {
                            return CircularProgressIndicator();
                          } else if (snapshot.connectionState ==
                              ConnectionState.done) {
                            if (snapshot.hasError) {
                              return const Text('Error');
                            } else if (snapshot.hasData) {
                              return TextButton(
                                  onPressed: () async {
                                    final value =
                                        await lotteryService.buyTicket();
                                    // print(value);
                                    if (value) {
                                      await Future.delayed(
                                          Duration(seconds: 1));
                                      setState(() {});
                                    }
                                  },
                                  child: snapshot.data!
                                      ? Container(
                                          width: 840.0.w,
                                          height: 120.h,
                                          alignment: Alignment.center,
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 16),
                                          margin: EdgeInsets.symmetric(
                                              vertical: 16),
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(20.0),
                                            color: const Color(0xFFFFFFFF),
                                          ),
                                          child: Text(
                                            "ยืนยัน & สร้างเลขล็อตโต้ของฉัน",
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                                color: Colors.black,
                                                fontSize: 16,
                                                fontWeight: FontWeight.normal),
                                          ),
                                        )
                                      : TextButton(
                                          onPressed: () async {
                                            final value = await lotteryService
                                                .approveBuy();
                                            if (value) {
                                              print('อนุมัติเเล้ว');
                                            }
                                          },
                                          child: Container(
                                            width: 840.0.w,
                                            height: 120.h,
                                            alignment: Alignment.center,
                                            padding: EdgeInsets.symmetric(
                                                horizontal: 16),
                                            margin: EdgeInsets.symmetric(
                                                vertical: 16),
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(20.0),
                                              color: const Color(0xFFFFFFFF),
                                            ),
                                            child: Text(
                                              "อนุมัติการสุ่ม",
                                              textAlign: TextAlign.center,
                                              style: TextStyle(
                                                  color: Colors.black,
                                                  fontSize: 16,
                                                  fontWeight:
                                                      FontWeight.normal),
                                            ),
                                          ),
                                        ));
                            } else {
                              return const Text('Empty data');
                            }
                          } else {
                            return Text('State: ${snapshot.connectionState}');
                          }
                        },
                      ),
                    ],
                  ))
            ]);
          } else {
            return const Text('Empty data');
          }
        } else {
          return Text('State: ${snapshot.connectionState}');
        }
      },
    );
  }

  List<String> winnerNumber = [];
  List<int> tokenWin = [];
  Widget lottoPreTime({required LottoRound data}) {
    winnerNumber = data.winnerNumber;

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            height: 117.89.h,
            // width: 200,
            alignment: Alignment.center,
            child: ListView.builder(
                itemCount: data.winnerNumber.length,
                scrollDirection: Axis.horizontal,
                shrinkWrap: true,
                itemBuilder: (BuildContext context, int index) {
                  return Center(
                    child: LottoComponent.circleBlock(
                      text: data.winnerNumber[index].toString(),
                    ),
                  );
                }),
          ),
          SizedBox(
            height: 50.h,
          ),
          FutureBuilder(
            future: getPreviousRoundNumber(),
            builder: (
              BuildContext context,
              AsyncSnapshot<double> snapshot,
            ) {
              print(snapshot.connectionState);
              if (snapshot.connectionState == ConnectionState.waiting) {
                return Center(
                  child: SpinKitThreeBounce(
                    color: Colors.grey,
                    size: 100.h,
                  ),
                );
              } else if (snapshot.connectionState == ConnectionState.done) {
                if (snapshot.hasError) {
                  return const Text('Error');
                } else if (snapshot.hasData) {
                  return LottoComponent.preRound(round: snapshot.data!);
                } else {
                  return const Text('Empty data');
                }
              } else {
                return Text('State: ${snapshot.connectionState}');
              }
            },
          ),
          LottoComponent.preRoundTotalReward(reward: data.totalPrev),
          Divider(),
          LottoComponent.amountPreWonLottoTitle(),
          LottoComponent.amountPreWonLotto(
              num: '4', amount: data.matchFour, reward: data.totalReward4),
          LottoComponent.amountPreWonLotto(
              num: '3', amount: data.matchThree, reward: data.totalReward3),
          LottoComponent.amountPreWonLotto(
              num: '2', amount: data.matchTwo, reward: data.totalReward2),
          Divider(),
          FutureBuilder(
            future: getYourLottoPreRound(),
            builder: (
              BuildContext context,
              AsyncSnapshot<YourLottoListPreRound> snapshot,
            ) {
              print(snapshot.connectionState);
              if (snapshot.connectionState == ConnectionState.waiting) {
                return Center(
                  child: SpinKitThreeBounce(
                    color: Colors.grey,
                    size: 70.h,
                  ),
                );
              } else if (snapshot.connectionState == ConnectionState.done) {
                if (snapshot.hasError) {
                  return const Text('Error');
                } else if (snapshot.hasData) {
                  return LottoComponent.yourAmountLottoPreRound(
                      amount: snapshot.data!.data!.length.toString(),
                      data: snapshot.data!.data,
                      wonPreNumber: winnerNumber,
                      check: checkMultiClaim,
                      totalReward: totalReward,
                      context: context,
                      onPressedFor: () {
                        print(winnerNumber.join());
                        List<CheckYourLotto> yourLotto = [];

                        for (var i = 0; i < snapshot.data!.data!.length; i++) {
                          var num = 0;
                          print(snapshot.data!.data![i].number[0].toString());
                          if (snapshot.data!.data![0].toString() ==
                              snapshot.data!.data![i].number[0].toString()) {
                            num += 1;
                          }
                          if (winnerNumber[1].toString() ==
                              snapshot.data!.data![i].number[1].toString()) {
                            num += 1;
                          }
                          if (winnerNumber[2].toString() ==
                              snapshot.data!.data![i].number[2].toString()) {
                            num += 1;
                          }
                          if (winnerNumber[3].toString() ==
                              snapshot.data!.data![i].number[3].toString()) {
                            num += 1;
                          }
                          var dataYourLotto = {
                            'number': snapshot.data!.data![i].number
                                .join()
                                .toString(),
                            "amountWin": num
                          };
                          yourLotto.add(CheckYourLotto.fromJson(dataYourLotto));
                        }
                        print(yourLotto);
                        showAlertDialog(context: context, data: yourLotto);
                      },
                      onPressed: () async {
                        tokenWin.clear();
                        for (var i = 0; i < snapshot.data!.data!.length; i++) {
                          // print(snapshot.data!.data![i].reward);
                          // print(snapshot.data!.data![i].tokenId);
                          if (snapshot.data!.data![i].reward.toInt() > 0) {
                            print(snapshot.data!.data![i].tokendId);
                            double x =
                                double.parse(snapshot.data!.data![i].tokendId);
                            tokenWin.add(x.toInt());
                          }
                        }
                        print(tokenWin);
                        await lotteryService.multipleClaimButton(tokenWin);
                      });
                } else {
                  return const Text('Empty data');
                }
              } else {
                return Text('State: ${snapshot.connectionState}');
              }
            },
          ),
        ],
      ),
    );
  }

  static Future showAlertDialog(
      {required BuildContext context, List<CheckYourLotto>? data}) {
    return showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('เลขของคุณ'),
          content: Container(
            height: 200,
            width: 500,
            child: ListView.builder(
                itemCount: data!.length,
                itemBuilder: (BuildContext context, int index) {
                  return Container(
                    color: data[index].amountWin < 2
                        ? Colors.transparent
                        : Colors.red,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "${index + 1} . " + data[index].number.toString(),
                          style: TextStyle(color: Colors.black, fontSize: 15),
                        ),
                        Text(
                          data[index].amountWin < 2
                              ? ""
                              : "ถูก " +
                                  data[index].amountWin.toString() +
                                  " ตัว",
                          style: TextStyle(color: Colors.black, fontSize: 15),
                        ),
                      ],
                    ),
                  );
                }),
          ),
          actions: [
            TextButton(
              child: Text('เข้าใจแล้ว'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  Widget lottoLastTime({required LottoNowRound data}) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('รางวัล 4 ตัว' + data.totalNowReward4.toString()),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('รางวัล 3 ตัว' + data.totalNowReward3.toString()),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('รางวัล 2 ตัว' + data.totalNowReward2.toString()),
          ],
        ),
      ],
    );
  }

  Widget yourLotto({required List<YourLotto>? data}) {
    // print(data!.length);
    // return Container();
    return Container(
      height: 100,
      width: double.infinity,
      child: ListView.builder(
          itemCount: data!.length,
          itemBuilder: (BuildContext context, int index) {
            return Text(
              data[index].number.toString(),
              style: TextStyle(color: Colors.green, fontSize: 15),
            );
          }),
    );
  }
}
