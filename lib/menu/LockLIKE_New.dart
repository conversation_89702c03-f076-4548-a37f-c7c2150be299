import 'dart:convert';
import 'dart:ui';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/Theme.dart';
import 'package:flutter/services.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:likewallet/middleware//check_maintenance/check_maintenance.dart';
import 'package:likewallet/close_maintenance.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'dart:async';
import 'package:likewallet/main.dart';
import 'package:likewallet/middleware/getLanguage.dart';
import 'package:likewallet/model/button.dart';
import 'package:likewallet/model/level.dart';
import 'package:likewallet/model/pageStatus.dart';
import 'package:likewallet/screen_util.dart';
import 'package:flutter/rendering.dart';
import 'package:likewallet/tabslide/logout.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:intl/intl.dart' as formatIntl;

import 'package:carousel_slider/carousel_slider.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:pattern_formatter/numeric_formatter.dart';
import 'package:likewallet/libraryman/address_service.dart';
import 'package:likewallet/libraryman/configuration_service.dart';
import 'package:likewallet/libraryman/ethcontract.dart';
import 'package:likewallet/libraryman/ethcontractv2.dart';
// import 'package:likewallet/libraryman/sharmir.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:io' show Platform;
import 'package:likewallet/libraryman/keyboard_done_widget.dart';
import 'package:rxdart/rxdart.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as riverpod;
import 'package:web3dart/web3dart.dart';
import 'package:likewallet/app_config.dart';
import 'package:http/http.dart' as http;
import '../routes.dart';

class lockLike extends StatefulWidget {
  _lockLike createState() => new _lockLike();
}

enum statusWithdraw { INACTIVE, ACTIVE }

class _lockLike extends State<lockLike> with TickerProviderStateMixin {
  @override
  late AnimationController _animationController;
  final f = new formatIntl.NumberFormat("###,###.##");

  TextEditingController addressText = TextEditingController();
  StreamController<String> streamAmountUnlock = StreamController<String>();
  StreamController<String> streamAmountLock = BehaviorSubject<String>();
  StreamController<String> streamAmountAutoLock = BehaviorSubject<String>();
  StreamController<String> streamTotalLike = StreamController<String>();

  FocusNode lockFocusNode = new FocusNode();
  late OverlayEntry? overlayEntry;

  bool _lock = true;
  bool _all = false;
  late IAddressService addressService;
  late IConfigurationService configETH;
  // late SharmirInterface callSharmir;
  late BaseETH eth;
  late BaseETHV2 eth2;
  late String pketh;
  late String mnemonic;
  late String ethAddr;
  String amountUnlock = '0';
  late String getPK;

  late String addressETH;
  late String unlockHour;
  late String unlockMin;
  late statusWithdraw isWithdraw;
  double diffTime = 0;
  bool _saving = false;
  bool isUnlock = false;
  late Timer _timer;

  bool selected = true;
  bool onSubmit = false;
  bool success = false;
  bool blink_lock = false;
  bool afterSuccess = false;
  bool onTapUnlock = false;
  // bool onTapUnlockAll = false;
  bool awaitUnlock = false;
  String AmountLock = "0";
  String totalShowUnlock = "0";
  String AmountUnLock = "0";
  String sendAmountUnLock = "0";
  String awaitShowUnlock = "0";
  String totalLike = "0";
  String locklLke = "40,000";
  String available = "5,000";
  String totalLock = "";
  String locked_balance = '0';
  double lockedAmountAll = 0.0;
  String onTapUnlockAll = 'no';
  bool statusBorrow = false;
  TextEditingController amountLock = TextEditingController();
  TextEditingController amountUnLock = TextEditingController();
  bool tap = false;
  double dept = 0.0;
  double lockNew = 0.0;
  double lockAuto = 0.0;
  double lockAll = 0.0;
  final FocusNode focusNode = FocusNode();
  LevelLimit? levelLockLimit;
  bool lockable = false;
  bool unLockable = false;
  int amountLimitLevel = 0;
  KeyboardVisibilityController keyboardVisibilityController = KeyboardVisibilityController();
////////////////////////////
  late CheckAbout checkAbout;
  late OnLanguage language;
  late BaseAuth auth;
////////////////////////////
  @override
  void initState() {
    super.initState();
    checkAbout = OnCheckAbout();
    language = CallLanguage();
    _animationController =
        new AnimationController(vsync: this, duration: Duration(seconds: 0));
    _animationController.forward();
    checkFirst();
    getLevel();
    getLockAll();
  }

  showUnlocked(balance) {
    showDialog(
        context: context,
        builder: (BuildContext context) =>
            alertUnlock(context, f.format(balance).toString()));
  }

  getLevel() async {
    try {
      print('getLevel' + context.read(tierLevel).state);
      final res = await FirebaseFirestore.instance
          .collection('tierController')
          .doc('controller')
          .collection(context.read(tierLevel).state)
          .doc('level')
          .get();
      levelLockLimit = LevelLimit.fromJson(res.data()!);
      if (context.read(userLevel).state == "1") {
        setState(() => amountLimitLevel = levelLockLimit!.level1);
      }
      if (context.read(userLevel).state == "2") {
        setState(() => amountLimitLevel = levelLockLimit!.level2);
      }
      if (context.read(userLevel).state == "3") {
        setState(() => amountLimitLevel = levelLockLimit!.level3);
      }
      if (context.read(userLevel).state == "4") {
        setState(() => amountLimitLevel = levelLockLimit!.level4);
      }
    } catch (e) {
      print(e);
    }
  }

  getButtonStatus() async {
    try {
      print('getButtonStatus' + context.read(tierLevel).state);
      final res = await FirebaseFirestore.instance
          .collection('tierController')
          .doc('controller')
          .collection(context.read(tierLevel).state)
          .doc('button')
          .get();
      final data = ButtonStatus.fromJson(res.data()!);
      setState(() {
        selected = data.lock;
        lockable = data.lock;
        unLockable = data.unlock;
      });
    } catch (e) {
      print(e);
    }
  }

  checkFirst() async {
    PageMaintenance statusPage = await checkAbout.checkTierPermission(
        tierLevel: context.read(tierLevel).state, page: 'locklike');
    if (statusPage.status == 'active') {
      //เริ่มทำงาน
      print('active');
      await getButtonStatus();
      setInitState();
    } else {
      //ปิด maintenance
      print('inactive');
      final lang = await language.getLanguage();
      final title = await checkAbout.selectLanguage(
          language: lang, detail: statusPage.title);
      final detail = await checkAbout.selectLanguage(
          language: lang, detail: statusPage.detail);
      final detailTime = await checkAbout.selectLanguage(
          language: lang, detail: statusPage.detail_time);
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
            builder: (context) => CloseMaintenance(
                  title: title,
                  detail: detail,
                  detailTime: detailTime,
                  url: statusPage.url,
                )),
      );
    }
  }

  setInitState() {
    new Future.delayed(Duration.zero, () {
      if (!mounted) return;
      setState(() {
        _saving = true;
      });
      // new Future.delayed(new Duration(milliseconds: 10000), () {
      //   _timer = new Timer.periodic(new Duration(seconds: 60),
      //       (Timer timer) => setInit(source: 'loop'));
      // });

      new Future.delayed(new Duration(milliseconds: 500), () {
        eth = new EthContract();
        eth2 = new EthContractV2();
        setInit();
      });

      //keyboard show done
      if (Platform.isIOS) {
        lockFocusNode.addListener(() {
          bool hasFocus = lockFocusNode.hasFocus;
          if (hasFocus)
            showOverlay(context);
          else
            removeOverlay();
        });
        keyboardVisibilityController.onChange.listen((bool visible) {
          removeOverlay();
        });
      }
    });
  }

//done button zone
  showOverlay(BuildContext context) {
    if (overlayEntry != null) return;
    OverlayState? overlayState = Overlay.of(context);
    overlayEntry = OverlayEntry(builder: (context) {
      return Positioned(
          bottom: MediaQuery.of(context).viewInsets.bottom,
          right: 0.0,
          left: 0.0,
          child: InputDoneView());
    });

    overlayState!.insert(overlayEntry!);
  }

  removeOverlay() {
    if (overlayEntry != null) {
      overlayEntry!.remove();
      overlayEntry = null;
    }
  }

  _allValue() {
    if (!mounted) return;
    setState(() {
      amountLock.text = amountUnlock;
      AmountLock = amountUnlock;
      totalLock = amountUnlock;
      onSubmit = false;
      selected = true;
    });
  }

  Future checkRewards() async {
    //check status claim
    //check รอบที่ผู้ใช้รับปัจจุบัน
    try {
      var balanceRewards = await eth.checkRewards(address: addressETH);
      print('balanceLock ' + balanceRewards.toString());
      var lastTime = await eth.checkClaim(address: addressETH);
      print('lastTime ' + lastTime.toString());
      //check current round airdrop
      //เช็ครอบที่มีการแจกรางวัลจากทีมงาน
      var roundAirdrop = await eth.getRound();
      print('roundAirdrop ' + roundAirdrop.toString());
      //ถ้ารอบที่ผู้รับมากกว่าหรือเท่ากับรอบปัจจุบันให้ทำการฝากเหรียญเพิ่มได้
      if (int.parse(lastTime) >= int.parse(roundAirdrop) ||
          balanceRewards == 0.0) {
        //claim ไปแล้ว
        return false;
      } else {
        //ยังไม่ claim
        return true;
      }
    } catch (e) {
      return false;
    }
  }

  getBalanceLock() async {
    eth2.getBalanceLock(address: addressETH).then((balanceLock) async {
      setState(() {
        _saving = true;
      });
      locked_balance = f.format(balanceLock + dept).toString();
      streamAmountLock.sink.add(locked_balance);
    });
    await Future.delayed(Duration(seconds: 3));
    setState(() {
      _saving = false;
    });
  }

  getLockAll() async {
    // setState(() {
    //   _saving = true;
    // });

    /// lock & earn + LCMP
    auth = Auth();
  }

  Future setInit({source}) async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    configETH = new ConfigurationService(pref);
    addressService = new AddressService(configETH);
    mnemonic = await configETH.getMnemonic();
    // print(mnemonic + "mnemonic");
    if (!mounted) return;
    setState(() {
      addressETH = configETH.getAddress();
      // addressETH = '******************************************';
    });
    getPK = addressService.getPrivateKey(mnemonic);
    print(addressETH);
    eth2.getBalanceLockWei(address: addressETH).then((amount) {
      setState(() {
        totalShowUnlock = amount.toString();
        print("totalShowUnlock :$totalShowUnlock");
      });
    });
    //ดึงเวลาปลดล็อค
    eth2.getUnlockDate(address: addressETH).then((data) {
      print('unlock' + data.toString());
      print('unlock' + data[3]);
      if (data[3] == '1') {
        if (!mounted) return;
        setState(() {
          awaitShowUnlock = (double.parse(data[4]) / 10e17).toString();
          print(" awaitShowUnlock :$awaitShowUnlock");
          awaitUnlock = true;
          isWithdraw = statusWithdraw.ACTIVE;
        });
      }
      if (int.parse(data[0]) >= 0 || int.parse(data[1]) >= 0) {
        if (!mounted) return;
        setState(() {
          isUnlock = true;
          unlockHour = data[0];
          unlockMin = data[1];
          //ถ้าหมดเวลาและถอนได้แล้ว
          if (int.parse(unlockHour) == 0 &&
              int.parse(unlockMin) == 0 &&
              data[3] == '1') {
            //คำสั่งถอน
            _unlockLikePoint();
          }
          print(unlockHour);
          print('manzer');
          print(double.parse(data[2]));
          diffTime = double.parse(data[2]);
        });
      } else {
        if (!mounted) return;
        setState(() {
          isUnlock = false;
          diffTime = double.parse(data[2]);
        });

        print(diffTime);
      }
    });
    //เช็คยอด
    eth.getBalance(address: addressETH).then((balance) {
      print(balance);
      amountUnlock = balance.toString();
      streamAmountUnlock.sink.add(amountUnlock);
    });
    //เช็คยอดการล็อค
    eth2.getBalanceLockAuto(address: addressETH).then((balanceLockAuto) async {
      eth2.getBalanceLock(address: addressETH).then((balanceLock) async {
        await eth2.getLoanV2(address: addressETH).then((loan) {
          if (loan[11].toString() == '1') {
            setState(() => statusBorrow = true);
          }
          dept = double.parse(
              EtherAmount.inWei(loan[6] as BigInt).getInEther.toString());
          print("เงินต้น" +
              EtherAmount.inWei(loan[6] as BigInt).getInEther.toString());
        });
        locked_balance = f.format(balanceLock + dept).toString();
        double total = context.read(amount).state +
            double.parse(locked_balance.replaceAll(',', ''));
        print(balanceLock);
        totalLock = balanceLock.toString();

        streamAmountLock.sink.add(locked_balance);
        lockAuto = balanceLockAuto.toDouble() - (balanceLock + dept);
        streamAmountAutoLock.sink.add(lockAuto.toString());
        Future.delayed(Duration(seconds: 1)).then((value) {
          totalLike =
              (double.parse(amountUnlock) + double.parse(totalLock)).toString();
          streamTotalLike.sink.add(context.read(totalAmount).state.toString());
        });
        //get lockedBalance
        try {
          auth = Auth();
          User? snapshot = await auth.getCurrentUser();
          var url = Uri.https(env.apiUrl, '/getBalanceByphoneNumber');
          final response = await http
              .post(url, body: {"phoneNumber": snapshot!.phoneNumber});
          if (response.statusCode == 200) {
            var body = json.decode(response.body);
            setState(() {
              lockedAmountAll = body['lockedBalance'];
            });
            print(body['lockedBalance'].toString() + ': ยอดล็อครวม');
          }
        } catch (e) {
          // showShortToast(e.toString(), Colors.red);
        }
        if (!mounted) return;
        setState(() {
          if (source != 'loop') {
            _saving = false;
          }
        });
      });
    });
  }

  //ร้องขอการปลดล็อค
  Future<bool> _requestUnlock(getPK, amount, onTapUnlockAll) async {
    String transaction = await eth2.requestUnlock(
        pk: getPK, value: amount.replaceAll(',', ''), all: onTapUnlockAll);
    print('withdraw tx : $transaction');
    await new Future.delayed(new Duration(seconds: 5));
    setState(() {
      _saving = false;
    });
    setInit();
    return true;
  }

//กดเคลมรับจากที่ล็อคกลับมา
  Future<bool> _unlockLikePoint() async {
    // String lock = await streamAmountLock.stream.first;
    var transaction = await eth2.unlockLikePointV2(pk: getPK);
    print('withdraw tx : $transaction');
    await new Future.delayed(new Duration(seconds: 5));
    setInit();
    return true;
  }
  //
  // Future<bool> _unlockLikePoint(balance, String pk) async {
  //   String lock = balance;
  //   var transaction = await eth2.unlockLikePointV2(pk: pk);
  //   print('withdraw tx : $transaction');
  //
  //   return true;
  // }

  //Lock Like
  _lockLikepoint(getPK, totalLock) async {
    try {
      eth.lockLikePoint(pk: getPK, value: totalLock).then((transaction) {
        List<String> tx = transaction.split(":");
        //transaction approve ขอสิทธิ์
        String approveTx = tx[0];
        //transaction Lock เหรียญ
        String lockTx = tx[1];
        print('approve tx : $approveTx');
        print('lock tx : $lockTx');
        //delay 2 วิเพื่อให้ ธุรกรรมเสร็จก่อน เพราะ tomochain block time คือ 2 sec
        new Future.delayed(new Duration(seconds: 4), () {
          setInit();
          setState(() => _saving = false);
          amountLock.text = '0';
          totalLock = '';
        });
      });
    } catch (e) {
      showShortToast(e, Colors.red);
      setState(() => _saving = false);
    }
  }

  Future startBlinking() {
    setState(() {
      // _animationController =
      //     new AnimationController(vsync: this, duration: Duration(seconds: 1));
      // _animationController.repeat();
      setState(() {
        amountLock.text = '';
      });
      Future.delayed(const Duration(milliseconds: 5000), () {
        // _animationController.stop();
        setInitState();
        setState(() {
          afterSuccess = false;
        });
      });
    });
    return Future.value();
  }

  checkLevel({var value, int? limit, FocusScopeNode? currentFocus}) {
    print(value);
    if (value.isNotEmpty) {
      final change = value.replaceAll(',', '');
      AmountLock = amountLock.text;

      ///เช็คว่า ล็อคเต็มจำนวนเเล้ว
      if (lockedAmountAll > limit!) {
        showShortToast(
            AppLocalizations.of(context)!.translate('limit_lock_detail3'),
            Colors.red);
        amountLock.text = '0';
        if (!currentFocus!.hasPrimaryFocus) {
          currentFocus.unfocus();
        }
      } else if (lockedAmountAll +
              double.parse(amountLock.text.toString().replaceAll(',', '')) >
          limit) {
        var sum = limit - lockedAmountAll;
        if (sum > 0) {
          warningLimit(
              context, f.format(limit).toString(), f.format(sum).toString());
          amountLock.text = '0';
        }
        if (!currentFocus!.hasPrimaryFocus) {
          currentFocus.unfocus();
        }
        amountLock.text = '0';
      } else {
        ///เช็คว่า จำนวนมากกว่า limit
        if (double.parse(change) > limit) {
          var sum = limit - lockedAmountAll;
          if (sum > 0) {
            warningLimit(
                context, f.format(limit).toString(), f.format(sum).toString());
            amountLock.text = '0';
          }
        }
      }
    } else {
      print('ว่าง');
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    // _timer.cancel();
    streamAmountLock.close();
    streamAmountUnlock.close();
    lockFocusNode.dispose();
    super.dispose();
  }

  ///เรียกตัวทำสไลด์ภาพ
  late CarouselSlider carouselSlider;
  List imgList = [
    'assets/image/locklike/2.png',
    'assets/image/locklike/3.png',
    'assets/image/locklike/1.png',
  ];

//  List<T> map<T>(List list, Function handler) {
//    List<T> result = [];
//    for (var i = 0; i < list.length; i++) {
//      result.add(handler(i, list[i]));
//    }
//    return result;
//  }

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return GestureDetector(
        onTap: () {
          FocusScopeNode currentFocus = FocusScope.of(context);
          if (!currentFocus.hasPrimaryFocus) {
            currentFocus.unfocus();
            tap = false;
          }
        },
        child: Scaffold(
            body: ModalProgressHUD(
                opacity: 0.1,
                inAsyncCall: _saving,
                progressIndicator: CustomLoading(),
                child: SingleChildScrollView(
                    child: Stack(
                  alignment: Alignment.topCenter,
                  children: <Widget>[
                    Container(height: mediaQuery(context, 'height', 2340)),
                    _slider(),

                    ///พื้นหลัง
                    BG(),

                    ///ภาายพื้นหลัง
                    _image(),

                    ///ปุ่มกลับ
                    _backButton(),

                    ///รายละเอียกจำนวนเหรียญ
                    Positioned(
                      top: mediaQuery(context, 'height', 194),
                      child: Column(
                        children: <Widget>[
                          _head_detail(),
                          SizedBox(
                            height: mediaQuery(context, 'height', 100),
                          ),
                          _controllerLock(),
                        ],
                      ),
                    ),

                    ///Controller ล็อค เเละ ปลดล็อค
//                    _controllerLock(),
                    ///ปลดล็อค
                    // if (selected == false && onTapUnlock == false)
                    //   _textUnlock(),

                    ///ปุ่มทั้งหมด
                    if (selected == true && onSubmit == false) _buttonAll(),
                    if (selected == false &&
                        onTapUnlock == false &&
                        awaitUnlock == false)
                      _buttonUnLock(),
                    if (selected == false && awaitUnlock) _buttonCancelUnLock(),
                    if (AmountLock.isNotEmpty &&
                        AmountLock != "0" &&
                        AmountLock != "" &&
                        onSubmit == false &&
                        selected == true)
                      _buttonNext(),
                    if (success == true) showSuccess(),
//                        _textAfterSuccess(),

                    /// เเถบสไลด์ภาพด้านล่าง
                  ],
                )))));
  }

  Widget BG() {
    return Container(
//      width: mediaQuery(context, 'height', 1876.42),
      height: mediaQuery(context, 'height', 1876.42),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment(0.0, -1.0),
          end: Alignment(0.0, 1.0),
          colors: [const Color(0xff141322), const Color(0xff2f4558)],
          stops: [0.0, 1.0],
        ),
      ),
    );
  }

  Widget _image() {
    return Positioned(
      top: 0,
      left: 0,
      child: SvgPicture.network(
        LikeWalletImage.icon_locklike_image,
        fit: BoxFit.contain,
        height: mediaQuery(context, 'height', 792),
        width: mediaQuery(context, 'width', 830.81),
      ),
//      Image.asset(
//        LikeWalletImage.icon_locklike_image,
//        fit: BoxFit.fill,
//        height: mediaQuery(
//          context,
//          'height',
//          891.81,
//        ),
//        width: mediaQuery(context, 'width', 830.81),
//      ),
    );
  }

  Widget _backButton() {
    return Positioned(
        top: mediaQuery(context, 'height', 100),
        left: mediaQuery(context, 'width', 0),
        child: backButton(context, LikeWalletAppTheme.gray));
  }

  Widget _head_detail() {
    return Container(
        width: MediaQuery.of(context).size.width,
        padding: EdgeInsets.only(
          right: mediaQuery(context, 'width', 75),
        ),
        alignment: Alignment.centerRight,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: <Widget>[
            Text(AppLocalizations.of(context)!.translate('lock_likepoint_all'),
                style: TextStyle(
                    color: LikeWalletAppTheme.bule1_2,
                    letterSpacing: 1,
                    fontFamily:
                        AppLocalizations.of(context)!.translate('font1'),
                    fontWeight: FontWeight.w100,
                    fontSize: mediaQuery(context, "height", 39))),
            StreamBuilder(
              stream: streamTotalLike.stream,
              initialData: '0',
              builder: (context, snapshot) {
                return new Text(
                  f.format(double.parse(snapshot.data.toString())).toString(),
                  textAlign: TextAlign.right,
                  style: TextStyle(
                      color: LikeWalletAppTheme.bule1_2,
                      letterSpacing: 1,
                      fontFamily: 'Proxima Nova',
                      fontWeight: FontWeight.w100,
                      fontSize: mediaQuery(context, "height", 50)),
                );
              },
            ),
            SizedBox(
              height: mediaQuery(context, 'height', 76),
            ),
            Text(
                AppLocalizations.of(context)!
                    .translate('lock_likepoint_locklike'),
                style: TextStyle(
                    color: LikeWalletAppTheme.bule1_2,
                    letterSpacing: 1,
                    fontFamily:
                        AppLocalizations.of(context)!.translate('font1'),
                    fontWeight: FontWeight.w100,
                    fontSize: mediaQuery(context, "height", 39))),
            FadeTransition(
              opacity: _animationController,
              child: StreamBuilder(
                stream: streamAmountLock.stream,
                initialData: '0',
                builder: (context, snapshot) {
                  return new Text(
                    snapshot.data.toString(),
                    textAlign: TextAlign.right,
                    style: TextStyle(
                        letterSpacing: 0.1,
                        color: LikeWalletAppTheme.bule1_2,
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font2'),
                        fontWeight: FontWeight.w300,
                        fontSize: mediaQuery(context, "height", 90)),
                  );
                },
              ),
            ),
            // SizedBox(
            //   height: mediaQuery(context, 'height', 64),
            // ),
            // Text(
            //     AppLocalizations.of(context)!
            //         .translate('lock_likepoint_locklike'),
            //     style: TextStyle(
            //         color: LikeWalletAppTheme.bule1_2,
            //         letterSpacing: 1,
            //         fontFamily:
            //             AppLocalizations.of(context)!.translate('font1'),
            //         fontWeight: FontWeight.w100,
            //         fontSize: mediaQuery(context, "height", 39))),
            // FadeTransition(
            //   opacity: _animationController,
            //   child: StreamBuilder(
            //     stream: streamAmountAutoLock.stream,
            //     initialData: '0',
            //     builder: (context, snapshot) {
            //       return new Text(
            //         snapshot.data.toString(),
            //         textAlign: TextAlign.right,
            //         style: TextStyle(
            //             letterSpacing: 0.1,
            //             color: LikeWalletAppTheme.bule1_2,
            //             fontFamily:
            //                 AppLocalizations.of(context)!.translate('font2'),
            //             fontWeight: FontWeight.w300,
            //             fontSize: mediaQuery(context, "height", 90)),
            //       );
            //     },
            //   ),
            // ),
            SizedBox(
              height: mediaQuery(context, 'height', 64),
            ),
            Text(
                AppLocalizations.of(context)!
                    .translate('lock_likepoint_balance'),
                style: TextStyle(
                    color: LikeWalletAppTheme.bule1_2,
                    letterSpacing: 1,
                    fontFamily:
                        AppLocalizations.of(context)!.translate('font1'),
                    fontWeight: FontWeight.w100,
                    fontSize: mediaQuery(context, "height", 39))),
            StreamBuilder(
              stream: streamAmountUnlock.stream,
              initialData: '0',
              builder: (context, snapshot) {
                return new Text(
                  f.format(double.parse(snapshot.data.toString())).toString(),
                  textAlign: TextAlign.right,
                  style: TextStyle(
                      color: LikeWalletAppTheme.bule1_2,
                      letterSpacing: 1,
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font2'),
                      fontWeight: FontWeight.w300,
                      fontSize: mediaQuery(context, "height", 45)),
                );
              },
            ),
          ],
        ));
//    );
  }

  Widget _controllerLock() {
    return Stack(
      alignment: Alignment.center,
      children: <Widget>[
        /// กรอบเมนู lock unlock
        Positioned(
          child: Container(
//                color: Colors.indigo,
            height: mediaQuery(context, 'height', 1000),
            width: mediaQuery(context, 'width', 930),
          ),
        ),

        /// กรอบเมนู lock unlock
        Positioned(
          top: 0,
          child: Container(
              height: mediaQuery(context, 'height', 620.32),
              width: mediaQuery(context, 'width', 930),
              child: Stack(
                alignment: Alignment.topCenter,
                children: <Widget>[
                  Positioned(
                    top: mediaQuery(context, 'height', 80),
                    child: CustomPaint(
                      painter: MyPainter(),
                      child: Container(
                        width: mediaQuery(context, 'width', 930),
                        height: mediaQuery(context, 'height', 473),
                        alignment: Alignment.center,
                      ),
                    ),
                  ),
                  Positioned(
                    top: 0,
                    child: Container(
                      width: mediaQuery(context, 'width', 834),
                      height: mediaQuery(context, 'height', 160),
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(65.0),
                        color: LikeWalletAppTheme.bule1_4,
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0x3e000000),
                            offset: Offset(3, 3),
                            blurRadius: 6,
                          ),
                        ],
                      ),
                    ),
                  ),
                  Positioned(
                      top: mediaQuery(context, 'height', 487),
                      child: Container(
                        width: mediaQuery(context, 'height', 133),
                        height: mediaQuery(context, 'height', 133),
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: LikeWalletAppTheme.bule1_4,
                            width: mediaQuery(context, 'width', 3),
                          ),
                          gradient: LinearGradient(
                            begin: Alignment(0.0, -1.0),
                            end: Alignment(0.0, 1.0),
                            colors: [
                              const Color(0xff213245),
                              const Color(0xff26394C)
                            ],
                            stops: [0.0, 1],
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0x3e000000),
                              offset: Offset(0, 0),
                              blurRadius: 4,
                            ),
                          ],
                        ),
                      )),
                ],
              )),
        ),

        /// ปุ่มเเสดงสถานะ ล็อคเพิ่ม เเละ ปลดล็อค
        AnimatedPositioned(
            top: mediaQuery(context, 'height', 13),
            left: selected
                ? mediaQuery(context, 'width', 62)
                : mediaQuery(context, 'width', 469),
            duration: Duration(milliseconds: 300),
            child: Container(
                width: mediaQuery(context, 'width', 834),
                child: Row(
                  children: <Widget>[
                    Container(
                      width: mediaQuery(context, 'width', 399.0),
                      height: mediaQuery(context, 'height', 132.0),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(65.0),
                        gradient: RadialGradient(
                          center: Alignment(0.0, 0.0),
                          radius: 0.5,
                          colors: [
                            const Color(0xcc4fb3f0),
                            const Color(0xcc0588fa)
                          ],
                          stops: [0.0, 1.0],
                          // transform: GradientXDTransform(1.0, 0.0, 0.0, 1.0,
                          //     0.0, 0.0, Alignment(0.0, 0.0)),
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0x3e000000),
                            offset: Offset(3, 3),
                            blurRadius: 6,
                          ),
                        ],
                      ),
                    )
                  ],
                ))),

        /// หัวข้อล็อคเพิ่ม เเละ ปลดล็อค
        Positioned(
            top: 0,
            child: Container(
                height: mediaQuery(context, 'height', 162.0),
//                    color: Colors.indigo,
                width: mediaQuery(context, 'width', 834),
                child: Stack(
                  children: <Widget>[
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        GestureDetector(
                          onTap: lockable ? () {
                            setState(() {
                              if (selected == false) {
                                selected = true;
                                onSubmit = false;
                                onTapUnlockAll = 'no';
                                onTapUnlock = false;
                              }
                            });
                          } : null,
                          child: Container(
                            alignment: Alignment.center,
                            height: mediaQuery(context, 'height', 162.0),
                            width: mediaQuery(context, 'width', 834) / 2,
                            child: Text(
                                AppLocalizations.of(context)!
                                    .translate('lock_lock_more'),
                                style: TextStyle(
                                    shadows: [
                                      Shadow(
                                        blurRadius: 3.0,
                                        color: Colors.black.withOpacity(0.18),
                                        offset: Offset(0.3, 0.6),
                                      ),
                                    ],
                                    color: LikeWalletAppTheme.black
                                        .withOpacity(0.6),
                                    letterSpacing: 0.5,
                                    fontFamily: AppLocalizations.of(context)!
                                        .translate('font1'),
                                    fontWeight: FontWeight.w700,
                                    fontSize:
                                        mediaQuery(context, "height", 45))),
                          ),
                        ),
                        Expanded(
                          child: Container(),
                        ),
                        GestureDetector(
                          onTap: unLockable ? () {
                            setState(() {
                              if (selected == true) {
                                selected = false;
                                onSubmit = false;
                                onTapUnlock = false;
                              }
                            });
                          } : null,
                          child: Container(
                            alignment: Alignment.center,
                            height: mediaQuery(context, 'height', 162.0),
                            width: mediaQuery(context, 'width', 834) / 2,
                            child: Text(
                                AppLocalizations.of(context)!
                                    .translate('lock_unlock'),
                                style: TextStyle(
                                    shadows: [
                                      Shadow(
                                        blurRadius: 3.0,
                                        color: Colors.black.withOpacity(0.18),
                                        offset: Offset(0.3, 0.6),
                                      ),
                                    ],
                                    color: LikeWalletAppTheme.black
                                        .withOpacity(0.6),
                                    letterSpacing: 0.5,
                                    fontFamily: AppLocalizations.of(context)!
                                        .translate('font1'),
                                    fontWeight: FontWeight.w700,
                                    fontSize:
                                        mediaQuery(context, "height", 45))),
                          ),
                        ),
                      ],
                    ),
                  ],
                ))),

        /// status lock
        Positioned(
          top: mediaQuery(context, 'height', 515),
          child: selected
              ? Container(
                  child: Image.asset(
                    afterSuccess
                        ? LikeWalletImage.locklike_icon_unlock
                        : LikeWalletImage.locklike_icon_lock,
                    height: mediaQuery(context, 'height', 81.19),
                    width: mediaQuery(context, 'width', 70.12),
                  ),
                )
              : unLockable ?
                GestureDetector(
                  onTap: () {
                    print(statusBorrow);
                    setState(() {
                      if (awaitUnlock == false) {
                        if (amountUnLock.text == '' ||
                            amountUnLock.text == '0' ||
                            amountUnLock.text == null) {
                          showShortToast(
                              AppLocalizations.of(context)!
                                  .translate('alert_unlock_limit_0'),
                              Colors.red);
                        } else {
                          if (double.parse(
                                  locked_balance.replaceAll(',', '')) <=
                              double.parse(
                                  amountUnLock.text.replaceAll(',', ''))) {
                            showShortToast(
                                AppLocalizations.of(context)!
                                    .translate('alert_unlock_limit'),
                                Colors.red);
                          } else {
                            if (statusBorrow) {
                              print('มียอดกู้');
                              Dialog simpleDialog = Dialog(
                                elevation: 500,
                                backgroundColor: Colors.transparent,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(30.0),
                                ),
                                child: Container(
                                  height: mediaQuery(context, 'height', 554.63),
                                  width: mediaQuery(context, 'width', 929.64),
                                  color: Colors.transparent,
                                  margin: EdgeInsets.only(
                                      bottom:
                                          mediaQuery(context, 'height', 600)),
                                  child: new ClipRect(
                                    child: new BackdropFilter(
                                      filter: new ImageFilter.blur(
                                          sigmaX: 10.0, sigmaY: 10.0),
                                      child: Container(
                                        decoration: BoxDecoration(
                                          color: LikeWalletAppTheme.white
                                              .withOpacity(0.6),
                                          borderRadius: BorderRadius.all(
                                              Radius.circular(20.0)),
                                        ),
                                        height: mediaQuery(
                                            context, 'height', 554.63),
                                        width: mediaQuery(
                                            context, 'width', 929.64),
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: <Widget>[
                                            Text(
                                              AppLocalizations.of(context)!
                                                  .translate(
                                                      'notify_title_notification'),
                                              style: TextStyle(
                                                letterSpacing: 0.3,
                                                fontFamily: AppLocalizations.of(
                                                        context)!
                                                    .translate('font1'),
                                                color: LikeWalletAppTheme.black
                                                    .withOpacity(1),
                                                fontSize: mediaQuery(
                                                    context, "height", 56),
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                            Container(
                                              margin: EdgeInsets.only(
                                                  bottom: mediaQuery(
                                                      context, 'height', 80)),
                                              width: mediaQuery(
                                                  context, 'width', 777.62),
                                              child: Text(
                                                AppLocalizations.of(context)!
                                                    .translate(
                                                        'alert_lendex_borrow_unlock'),
                                                textAlign: TextAlign.center,
                                                style: TextStyle(
                                                  letterSpacing: 0.3,
                                                  fontFamily:
                                                      AppLocalizations.of(
                                                              context)!
                                                          .translate('font1'),
                                                  color: LikeWalletAppTheme
                                                      .black
                                                      .withOpacity(1),
                                                  fontSize: mediaQuery(
                                                      context, "height", 42),
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                            ),
                                            Container(
                                                width: mediaQuery(
                                                    context, 'width', 777.62),
                                                decoration: BoxDecoration(
                                                  border: Border(
                                                    top: BorderSide(
                                                      //                   <--- left side
                                                      color: LikeWalletAppTheme
                                                          .black
                                                          .withOpacity(0.4),
                                                      width: mediaQuery(
                                                          context, 'width', 1),
                                                    ),
                                                  ),
                                                ),
                                                child: Row(
                                                  children: <Widget>[
                                                    GestureDetector(
                                                      onTap: () async {
                                                        // Navigator.pop(context);
                                                        // await Navigator
                                                        //     .pushReplacement(
                                                        //   context,
                                                        //   MaterialPageRoute(
                                                        //     builder: (context) =>
                                                        //         AccountLenDexScreen(),
                                                        //   ),
                                                        // );
                                                      },
                                                      child: Container(
                                                        alignment:
                                                            Alignment.center,
                                                        decoration:
                                                            BoxDecoration(
                                                          border: Border(
                                                            right: BorderSide(
                                                              //                   <--- left side
                                                              color: LikeWalletAppTheme
                                                                  .black
                                                                  .withOpacity(
                                                                      0.4),
                                                              width: mediaQuery(
                                                                  context,
                                                                  'width',
                                                                  1),
                                                            ),
                                                          ),
                                                        ),
                                                        height: mediaQuery(
                                                            context,
                                                            'height',
                                                            127.66),
                                                        width: mediaQuery(
                                                                context,
                                                                'width',
                                                                777.62) /
                                                            2,
                                                        child: Text(
                                                          AppLocalizations.of(
                                                                  context)!
                                                              .translate(
                                                                  'network_error_button'),
                                                          textAlign:
                                                              TextAlign.center,
                                                          style: TextStyle(
                                                            letterSpacing: 0.3,
                                                            fontFamily:
                                                                AppLocalizations.of(
                                                                        context)!
                                                                    .translate(
                                                                        'font1'),
                                                            color:
                                                                LikeWalletAppTheme
                                                                    .bule1_7
                                                                    .withOpacity(
                                                                        1),
                                                            fontSize:
                                                                mediaQuery(
                                                                    context,
                                                                    "height",
                                                                    52),
                                                            fontWeight:
                                                                FontWeight.w600,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                    GestureDetector(
                                                      onTap: () {
                                                        Navigator.pop(context);
                                                      },
                                                      child: Container(
                                                        alignment:
                                                            Alignment.center,
                                                        height: mediaQuery(
                                                            context,
                                                            'height',
                                                            127.66),
                                                        width: mediaQuery(
                                                                context,
                                                                'width',
                                                                777.62) /
                                                            2,
                                                        child: Text(
                                                          AppLocalizations.of(
                                                                  context)!
                                                              .translate(
                                                                  'logout_no'),
                                                          textAlign:
                                                              TextAlign.center,
                                                          style: TextStyle(
                                                            letterSpacing: 0.3,
                                                            fontFamily:
                                                                AppLocalizations.of(
                                                                        context)!
                                                                    .translate(
                                                                        'font1'),
                                                            color:
                                                                LikeWalletAppTheme
                                                                    .bule1_7
                                                                    .withOpacity(
                                                                        1),
                                                            fontSize:
                                                                mediaQuery(
                                                                    context,
                                                                    "height",
                                                                    52),
                                                            fontWeight:
                                                                FontWeight.w600,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                )),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              );
                              showDialog(
                                  context: context,
                                  builder: (BuildContext context) =>
                                      WillPopScope(
                                          onWillPop: () {
                                            return Future.value();
                                          },
                                          child: simpleDialog));
                            } else {
                              if (awaitUnlock == false) {
                                onTapUnlock = true;
                                AmountUnLock = amountUnLock.text;
                                sendAmountUnLock = amountUnLock.text;
                                onTapUnlockAll = 'no';
                              }
                            }
                          }
                        }
                      }
                    });
                  },
                  child: Container(
                    child: Image.asset(
                      LikeWalletImage.locklike_icon_unlock,
                      height: mediaQuery(context, 'height', 81.19),
                      width: mediaQuery(context, 'width', 70.12),
                    ),
                  ),
                )
          : GestureDetector(
            onTap: null,
            child: Container(
              child: Image.asset(
                LikeWalletImage.locklike_icon_unlock,
                height: mediaQuery(context, 'height', 81.19),
                width: mediaQuery(context, 'width', 70.12),
                color: Colors.transparent.withOpacity(0.3),
              ),
            ),
          ),
        ),
        if (selected == true) _bodylocklike(),
        if (selected == false) _bodyunlike(),
      ],
    );
  }

  Widget _buttonAll() {
    return Positioned(
      top: mediaQuery(context, 'height', 1405),
      right: mediaQuery(context, 'width', 105),
      child:
      lockable ?
      GestureDetector(
        onTap: () {
          _allValue();
        },
        child: Container(
          alignment: Alignment.center,
          width: mediaQuery(context, 'height', 164.0),
          height: mediaQuery(context, 'width', 76.0),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(38.0),
            color: const Color(0xff17171e),
            boxShadow: [
              BoxShadow(
                color: const Color(0x4d000000),
                offset: Offset(0, 3),
                blurRadius: 6,
              ),
            ],
          ),
          child: Text(
            AppLocalizations.of(context)!.translate('lock_button_all'),
            style: TextStyle(
              fontFamily: 'Prompt',
              fontSize: mediaQuery(context, 'height', 30),
              color: const Color(0xb200ffff),
              letterSpacing: 0.5,
              fontWeight: FontWeight.w300,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      )
          :GestureDetector(
        onTap: null,
        child: Container(
          alignment: Alignment.center,
          width: mediaQuery(context, 'height', 164.0),
          height: mediaQuery(context, 'width', 76.0),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(38.0),
            color: const Color(0xff17171e).withOpacity(0.3),
            boxShadow: [
              BoxShadow(
                color: const Color(0x4d000000),
                offset: Offset(0, 3),
                blurRadius: 6,
              ),
            ],
          ),
          child: Text(
            AppLocalizations.of(context)!.translate('lock_button_all'),
            style: TextStyle(
              fontFamily: 'Prompt',
              fontSize: mediaQuery(context, 'height', 30),
              color: const Color(0xb200ffff).withOpacity(0.3),
              letterSpacing: 0.5,
              fontWeight: FontWeight.w300,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),

//            Stack(
//              children: <Widget>[
//                Transform.translate(
//                  offset: Offset(851.0, 1376.5),
//                  child: SizedBox(
//                    width: 126.0,
//                    child:
//                  ),
//                ),
//              ],
//            ),
//          ],
//        )
    );
  }

  Widget _buttonUnLock() {
    return Positioned(
      top: mediaQuery(context, 'height', 1405),
      right: mediaQuery(context, 'width', 105),
      child:
      unLockable ?
      GestureDetector(
        onTap: () {
          setState(() {
            onTapUnlock = true;
            onTapUnlockAll = 'yes';
            AmountUnLock = locked_balance;
            sendAmountUnLock = totalShowUnlock;
            print("sendAmountUnLock" + sendAmountUnLock);
          });
          // _allValue();
        },
        child: Container(
          alignment: Alignment.center,
          width: mediaQuery(context, 'height', 164.0),
          height: mediaQuery(context, 'width', 76.0),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(38.0),
            color: const Color(0xff17171e),
            boxShadow: [
              BoxShadow(
                color: const Color(0x4d000000),
                offset: Offset(0, 3),
                blurRadius: 6,
              ),
            ],
          ),
          child: Text(
            AppLocalizations.of(context)!.translate('lock_button_all'),
            style: TextStyle(
              fontFamily: 'Prompt',
              fontSize: mediaQuery(context, 'height', 30),
              color: const Color(0xb200ffff),
              letterSpacing: 0.5,
              fontWeight: FontWeight.w300,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      )
      :GestureDetector(
        onTap: null,
        child: Container(
          alignment: Alignment.center,
          width: mediaQuery(context, 'height', 164.0),
          height: mediaQuery(context, 'width', 76.0),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(38.0),
            color: const Color(0xff17171e).withOpacity(0.3),
            boxShadow: [
              BoxShadow(
                color: const Color(0x4d000000),
                offset: Offset(0, 3),
                blurRadius: 6,
              ),
            ],
          ),
          child: Text(
            AppLocalizations.of(context)!.translate('lock_button_all'),
            style: TextStyle(
              fontFamily: 'Prompt',
              fontSize: mediaQuery(context, 'height', 30),
              color: const Color(0xb200ffff).withOpacity(0.3),
              letterSpacing: 0.5,
              fontWeight: FontWeight.w300,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),

//            Stack(
//              children: <Widget>[
//                Transform.translate(
//                  offset: Offset(851.0, 1376.5),
//                  child: SizedBox(
//                    width: 126.0,
//                    child:
//                  ),
//                ),
//              ],
//            ),
//          ],
//        )
    );
  }

  Widget _buttonCancelUnLock() {
    return Positioned(
      top: mediaQuery(context, 'height', 1405),
      right: mediaQuery(context, 'width', 105),
      child: GestureDetector(
        onTap: () {
          setState(() => _saving = true);

          checkRewards().then((resultClaim) async {
            if (resultClaim) {
              setState(() => _saving = false);
              await GetRewardsPopup(context);
              // Navigator.push(
              //     context,
              //     EnterExitRoute(
              //         exitPage: lockLike(),
              //         enterPage: hourlyRewards()));
            } else {
              setState(() => _saving = true);
              _lockLikepoint(getPK, "0").then((callback) {
                setState(() {
                  _saving = false;
                  awaitUnlock = false;
                  onTapUnlock = false;
                  amountUnLock.text = "0";
                });
              });
            }
          });
        },
        child: Container(
          alignment: Alignment.center,
          width: mediaQuery(context, 'height', 164.0),
          height: mediaQuery(context, 'width', 76.0),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(38.0),
            color: const Color(0xff17171e),
            boxShadow: [
              BoxShadow(
                color: const Color(0x4d000000),
                offset: Offset(0, 3),
                blurRadius: 6,
              ),
            ],
          ),
          child: Text(
            AppLocalizations.of(context)!.translate('lock_button_cancel'),
            style: TextStyle(
              fontFamily: 'Prompt',
              fontSize: mediaQuery(context, 'height', 30),
              color: const Color(0xb200ffff),
              letterSpacing: 0.5,
              fontWeight: FontWeight.w300,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),

//            Stack(
//              children: <Widget>[
//                Transform.translate(
//                  offset: Offset(851.0, 1376.5),
//                  child: SizedBox(
//                    width: 126.0,
//                    child:
//                  ),
//                ),
//              ],
//            ),
//          ],
//        )
    );
  }

  Widget _buttonNext() {
    return Positioned(
      top: mediaQuery(context, 'height', 1690),
      right: mediaQuery(context, 'width', 105),
      child: GestureDetector(
          onTap: () {
            setState(() {
              if (amountLock.text.isNotEmpty) {
                if (double.parse(
                        amountLock.text.toString().replaceAll(',', '')) >
                    double.parse(amountUnlock)) {
                  showShortToast(
                      AppLocalizations.of(context)!
                          .translate('lock_not_available'),
                      Colors.red);
                } else if (amountLock.text.toString() == '0') {
                  showShortToast(
                      AppLocalizations.of(context)!
                          .translate('alert_unlock_limit_0'),
                      Colors.red);
                } else {
                  if (context.read(tierLevel).state == 'tierBU') {
                    if (awaitUnlock) {
                      setState(() {
                        selected = false;
                        amountLock.text = '0';
                      });
                      print(selected);
                      showShortToast(
                          AppLocalizations.of(context)!
                              .translate('alert_lock_await_unlock'),
                          Colors.red);
                    } else {
                      onSubmit = true;
                      AmountLock = amountLock.text;
                    }
                  } else if (context.read(tierLevel).state == 'tier1' ||
                      context.read(tierLevel).state == 'normal') {
                    if (lockedAmountAll > amountLimitLevel) {
                      showShortToast(
                          AppLocalizations.of(context)!
                              .translate('limit_lock_detail3'),
                          Colors.red);
                      amountLock.text = '0';
                    } else {
                      if (awaitUnlock) {
                        setState(() {
                          selected = false;
                          amountLock.text = '0';
                        });
                        print(selected);
                        showShortToast(
                            AppLocalizations.of(context)!
                                .translate('alert_lock_await_unlock'),
                            Colors.red);
                      } else {
                        onSubmit = true;
                        AmountLock = amountLock.text;
                      }
                    }
                  }
                }
              } else {
                showShortToast(
                    AppLocalizations.of(context)!
                        .translate('alert_unlock_limit_0'),
                    Colors.red);
              }
            });
          },
          child: Container(
              child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Text(
                AppLocalizations.of(context)!.translate('lock_button_next'),
                style: TextStyle(
                    color: LikeWalletAppTheme.bule1,
                    letterSpacing: 0.1,
                    fontFamily:
                        AppLocalizations.of(context)!.translate('font1'),
                    fontWeight: FontWeight.w300,
                    fontSize: mediaQuery(context, "height", 36)),
              ),
              SizedBox(
                width: mediaQuery(context, 'width', 20),
              ),
              Image.asset(
                LikeWalletImage.icon_button_next,
                height: mediaQuery(context, 'height', 103),
              ),
            ],
          ))),

//            Stack(
//              children: <Widget>[
//                Transform.translate(
//                  offset: Offset(851.0, 1376.5),
//                  child: SizedBox(
//                    width: 126.0,
//                    child:
//                  ),
//                ),
//              ],
//            ),
//          ],
//        )
    );
  }

  Widget _textUnlock() {
    return Positioned(
        top: mediaQuery(context, 'height', 1470),
        child: SizedBox(
            width: 230.0,
            child: awaitUnlock
                ? Container()
                : Text(
                    AppLocalizations.of(context)!.translate('lock_tap_to_lock'),
                    style: TextStyle(
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      fontSize: mediaQuery(context, 'height', 45),
                      color: LikeWalletAppTheme.black,
                      letterSpacing: 0.1,
                      fontWeight: FontWeight.w300,
                      shadows: [
                        Shadow(
                          blurRadius: 3.0,
                          color: LikeWalletAppTheme.black.withOpacity(0.2),
                          offset: Offset(0.3, 0.6),
                        ),
                      ],
                    ),
                    textAlign: TextAlign.center,
                  )));
  }

  Widget _bodylocklike() {
    return Positioned(
        bottom: 0,
        child: Container(
            height: mediaQuery(context, 'height', 800),
            child: Stack(
              alignment: Alignment.topCenter,
              children: <Widget>[
                AnimatedPositioned(
                  top: mediaQuery(context, 'height', 60),
                  duration: Duration(milliseconds: 300),
                  left: onSubmit
                      ? mediaQuery(context, 'width', 930) / 4.4
                      : mediaQuery(context, 'width', 1080),
                  child: onSubmit
                      ? Container(
//                    color: Colors.red,
                          width: onSubmit
                              ? mediaQuery(context, 'width', 500)
                              : mediaQuery(context, 'width', 500),
                          child: Column(
                            children: <Widget>[
                              Text(
                                AmountLock +
                                    " " +
                                    AppLocalizations.of(context)!
                                        .translate('lock_symbol'),
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                    shadows: [
                                      Shadow(
                                        blurRadius: 5.0,
                                        color: LikeWalletAppTheme.black
                                            .withOpacity(0.5),
                                        offset: Offset(0.0, 0.0),
                                      ),
                                    ],
                                    color: LikeWalletAppTheme.bule1,
                                    letterSpacing: 0.5,
                                    fontFamily: AppLocalizations.of(context)!
                                        .translate('font1'),
                                    fontWeight: FontWeight.w300,
                                    fontSize:
                                        mediaQuery(context, "height", 45)),
                              ),
                              Text(
                                AppLocalizations.of(context)!
                                    .translate('lock_detail_lock'),
                                style: TextStyle(
                                    shadows: [
                                      Shadow(
                                        blurRadius: 5.0,
                                        color: LikeWalletAppTheme.black
                                            .withOpacity(0.5),
                                        offset: Offset(0.0, 0.0),
                                      ),
                                    ],
                                    color: LikeWalletAppTheme.bule1,
                                    letterSpacing: 0.5,
                                    fontFamily: AppLocalizations.of(context)!
                                        .translate('font1'),
                                    fontWeight: FontWeight.w300,
                                    fontSize:
                                        mediaQuery(context, "height", 45)),
                              ),
                            ],
                          ))
                      : Container(),
                ),
                AnimatedContainer(
//                  alignment: Alignment.topCenter,
                  padding:
                      EdgeInsets.only(top: mediaQuery(context, "height", 0)),
                  width: mediaQuery(context, 'width', 930),
                  duration: Duration(milliseconds: 300),
                  alignment: onSubmit ? Alignment.topLeft : Alignment.topCenter,
                  child: AnimatedContainer(
                    alignment: Alignment.center,
//                    color: Colors.indigo,
                    height: mediaQuery(context, 'height', 300),
                    width: onSubmit
                        ? mediaQuery(context, 'width', 0)
                        : mediaQuery(context, 'width', 600),
                    duration: Duration(milliseconds: 300),
                    child: onSubmit
                        ? Container()
                        : TextFormField(
                            onTap: () {
                              tap = lockable;
                            },
                            readOnly: lockable ? false : true,
                            controller: amountLock,
                            textAlign: TextAlign.center,
                            inputFormatters: [
                              ThousandsFormatter(allowFraction: true)
                            ],
                            textInputAction: TextInputAction.next,
                            keyboardType: TextInputType.number,
                            cursorColor: LikeWalletAppTheme.bule1_5,
                            onChanged: (value) {
                              FocusScopeNode currentFocus =
                                  FocusScope.of(context);
                              print(value);
                              print(context.read(userLevel).state);
                              if (context.read(tierLevel).state == 'tierBU') {
                                AmountLock = amountLock.text;
                              } else {
                                checkLevel(
                                    value: value,
                                    limit: amountLimitLevel,
                                    currentFocus: currentFocus);
                              }
                            },
//                            onFieldSubmitted: (term) {
//                              focusNode.unfocus();
//                              FocusScope.of(context).requestFocus(focusNode);
//                              setState(() {
//                                onSubmit = true;
//                                AmountLock = amountLock.text;
//                              });
//                            },
                            style: TextStyle(
                                color: LikeWalletAppTheme.white,
                                letterSpacing: 0.1,
                                fontFamily: AppLocalizations.of(context)!
                                    .translate('font2'),
                                fontWeight: FontWeight.w300,
                                fontSize: mediaQuery(context, "height", 85)),
                            decoration: InputDecoration(
                              border: InputBorder.none,
                              hintText: tap ? '' : '0',
                              hintStyle: TextStyle(
                                  color: LikeWalletAppTheme.gray,
                                  letterSpacing: 0.1,
                                  fontFamily: AppLocalizations.of(context)!
                                      .translate('font2'),
                                  fontWeight: FontWeight.w300,
                                  fontSize: mediaQuery(context, "height", 85)),
                            ),
                            validator: (value) {
                              if (value!.isEmpty) {
                                return 'Please enter some text';
                              }
                              return null;
                            },
                          ),
                  ),
                ),
                AnimatedPositioned(
                  bottom: mediaQuery(context, 'height', 0),
                  duration: Duration(milliseconds: 300),
                  left: onSubmit
                      ? mediaQuery(context, 'width', 0)
                      : mediaQuery(context, 'width', 1080),
                  child: Container(
//          color: Colors.red,
                      width: mediaQuery(context, 'width', 930),
                      child: Row(
                        children: <Widget>[
                          GestureDetector(
                              onTap: () {
                                setState(() {
                                  onSubmit = false;
                                });
                              },
                              child: Container(
                                  width: mediaQuery(context, 'width', 930) / 2,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: <Widget>[
                                      Image.asset(
                                        LikeWalletImage.icon_button_cancel,
                                        height:
                                            mediaQuery(context, 'height', 103),
                                      ),
                                      SizedBox(
                                        width: mediaQuery(context, 'width', 20),
                                      ),
                                      Text(
                                        AppLocalizations.of(context)!
                                            .translate('lock_button_cancel'),
                                        style: TextStyle(
                                            color: LikeWalletAppTheme.bule1,
                                            letterSpacing: 0.1,
                                            fontFamily:
                                                AppLocalizations.of(context)!
                                                    .translate('font1'),
                                            fontWeight: FontWeight.w300,
                                            fontSize: mediaQuery(
                                                context, "height", 36)),
                                      )
                                    ],
                                  ))),
                          GestureDetector(
                              onTap: () {
                                if (!mounted) return;
                                totalLock = amountLock.text.replaceAll(",", "");
                                if (totalLock != '') {
                                  if (double.parse(totalLock) > 0) {
                                    setState(() {
                                      _saving = true;
                                      Future.delayed(
                                          const Duration(milliseconds: 1000),
                                          () async {
                                        bool resultRewards =
                                            await checkRewards();
                                        print('resultRewards : ' +
                                            resultRewards.toString());
                                        if (resultRewards == null) {
                                          setState(() => _saving = false);
                                          if (context.read(checkNet).state) {
                                            showShortToast(
                                                AppLocalizations.of(context)!
                                                    .translate(
                                                        'alert_network_error'),
                                                Colors.red);
                                          }
                                        } else {
                                          if (resultRewards) {
                                            setState(() {
                                              _saving = false;
                                            });
                                            await GetRewardsPopup(context);
                                          } else {
                                            success = true;
                                            //call function lock like
                                            //เอาจำนวนที่ล็อคตัด commas ออก
                                            totalLock = double.parse(amountLock
                                                    .text
                                                    .replaceAll(",", ""))
                                                .toString();
                                            print(totalLock);
                                            _lockLikepoint(getPK, totalLock)
                                                .then((callback) {
                                              // _saving = false;
                                              success = false;
                                              onSubmit = false;
                                              afterSuccess = true;
                                              startBlinking();
                                              AmountLock = '0';
                                            });
                                          }
                                        }
                                      });
                                    });
                                  } else {
                                    setState(() {
                                      _saving = false;
                                    });
                                  }
                                } else {
                                  setState(() {
                                    _saving = false;
                                  });
                                }
                              },
                              child: Container(
                                  width: mediaQuery(context, 'width', 930) / 2,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: <Widget>[
                                      Text(
                                        AppLocalizations.of(context)!
                                            .translate('lock_button_confirm'),
                                        style: TextStyle(
                                            color: LikeWalletAppTheme.bule1,
                                            letterSpacing: 0.1,
                                            fontFamily:
                                                AppLocalizations.of(context)!
                                                    .translate('font1'),
                                            fontWeight: FontWeight.w300,
                                            fontSize: mediaQuery(
                                                context, "height", 36)),
                                      ),
                                      SizedBox(
                                        width: mediaQuery(context, 'width', 20),
                                      ),
                                      Image.asset(
                                        LikeWalletImage.icon_button_next,
                                        height:
                                            mediaQuery(context, 'height', 103),
                                      ),
                                    ],
                                  ))),
                        ],
                      )),
                )
              ],
            )));
  }

  Widget _bodyunlike() {
    return Positioned(
        bottom: 0,
        child: Container(
            height: mediaQuery(context, 'height', 800),
//            color: Colors.red,
            child: Stack(
              alignment: Alignment.topCenter,
              children: <Widget>[
                AnimatedPositioned(
                  top: mediaQuery(context, 'height', 20),
                  duration: Duration(milliseconds: 300),
                  left: onTapUnlock
                      ? mediaQuery(context, 'width', 930) / 5.5
                      : mediaQuery(context, 'width', 1080),
                  child: onTapUnlock
                      ? Container(
                          alignment: Alignment.center,
//                          color: Colors.red,
                          width: onTapUnlock
                              ? mediaQuery(context, 'width', 600)
                              : mediaQuery(context, 'width', 600),
                          child: Column(
                            children: <Widget>[
                              new Text(
                                f
                                        .format(double.parse(
                                            AmountUnLock.replaceAll(',', '')))
                                        .toString() +
                                    " " +
                                    AppLocalizations.of(context)!
                                        .translate('lock_symbol'),
                                textAlign: TextAlign.right,
                                style: TextStyle(
                                    shadows: [
                                      Shadow(
                                        blurRadius: 5.0,
                                        color: LikeWalletAppTheme.black
                                            .withOpacity(0.5),
                                        offset: Offset(0.0, 0.0),
                                      ),
                                    ],
                                    color: LikeWalletAppTheme.bule1,
                                    letterSpacing: 0.5,
                                    fontFamily: AppLocalizations.of(context)!
                                        .translate('font1'),
                                    fontWeight: FontWeight.w300,
                                    fontSize:
                                        mediaQuery(context, "height", 45)),
                              ),
                              Text(
                                AppLocalizations.of(context)!
                                    .translate('lock_detail_unlock'),
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                    shadows: [
                                      Shadow(
                                        blurRadius: 5.0,
                                        color: LikeWalletAppTheme.black
                                            .withOpacity(0.5),
                                        offset: Offset(0.0, 0.0),
                                      ),
                                    ],
                                    color: LikeWalletAppTheme.bule1,
                                    letterSpacing: 0.5,
                                    fontFamily: AppLocalizations.of(context)!
                                        .translate('font1'),
                                    fontWeight: FontWeight.w300,
                                    fontSize:
                                        mediaQuery(context, "height", 45)),
                              ),
                            ],
                          ))
                      : Container(),
                ),
                AnimatedContainer(
                  padding:
                      EdgeInsets.only(top: mediaQuery(context, "height", 0)),
                  width: mediaQuery(context, 'width', 930),
                  duration: Duration(milliseconds: 300),
                  alignment:
                      onTapUnlock ? Alignment.topLeft : Alignment.topCenter,
                  child: Container(
                    alignment: Alignment.center,
                    height: mediaQuery(context, 'height', 300),
                    width: onTapUnlock
                        ? mediaQuery(context, 'width', 600)
                        : mediaQuery(context, 'width', 600),
//                    duration: Duration(milliseconds: 300),
                    child: onTapUnlock
                        ? Container()
                        : awaitUnlock
                            ? new Text(
                                f
                                        .format(double.parse(awaitShowUnlock
                                            .replaceAll(',', '')))
                                        .toString() +
                                    " " +
                                    AppLocalizations.of(context)!
                                        .translate('lock_symbol') +
                                    "\n" +
                                    AppLocalizations.of(context)!
                                        .translate('lock_detail_wait_unlock') +
                                    " " +
                                    unlockHour +
                                    ":" +
                                    unlockMin +
                                    " " +
                                    AppLocalizations.of(context)!
                                        .translate('lock_detail_wait_hr'),
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                    shadows: [
                                      Shadow(
                                        blurRadius: 5.0,
                                        color: LikeWalletAppTheme.black
                                            .withOpacity(0.5),
                                        offset: Offset(0.0, 0.0),
                                      ),
                                    ],
                                    color: LikeWalletAppTheme.bule1,
                                    letterSpacing: 0.5,
                                    fontFamily: AppLocalizations.of(context)!
                                        .translate('font1'),
                                    fontWeight: FontWeight.w300,
                                    fontSize:
                                        mediaQuery(context, "height", 45)),
                              )
                            : TextFormField(
                                onTap: () {
                                  tap = unLockable;
                                },
                                readOnly: unLockable ? false : true,
                                controller: amountUnLock,
                                textAlign: TextAlign.center,
                                inputFormatters: [ThousandsFormatter()],
                                textInputAction: TextInputAction.next,
                                keyboardType: TextInputType.number,
                                cursorColor: LikeWalletAppTheme.bule1_5,
                                onChanged: (value) {
                                  setState(() {
                                    AmountUnLock = amountUnLock.text;
                                    print(AmountUnLock);
                                  });
                                },
//                            onFieldSubmitted: (term) {
//                              focusNode.unfocus();
//                              FocusScope.of(context).requestFocus(focusNode);
//                              setState(() {
//                                onSubmit = true;
//                                AmountLock = amountLock.text;
//                              });
//                            },
                                style: TextStyle(
                                    color: LikeWalletAppTheme.white,
                                    letterSpacing: 0.1,
                                    fontFamily: AppLocalizations.of(context)!
                                        .translate('font2'),
                                    fontWeight: FontWeight.w300,
                                    fontSize:
                                        mediaQuery(context, "height", 85)),
                                decoration: InputDecoration(
                                  border: InputBorder.none,
                                  hintText: tap ? '' : '0',
                                  hintStyle: TextStyle(
                                      color: LikeWalletAppTheme.gray,
                                      letterSpacing: 0.1,
                                      fontFamily: AppLocalizations.of(context)!
                                          .translate('font2'),
                                      fontWeight: FontWeight.w300,
                                      fontSize:
                                          mediaQuery(context, "height", 85)),
                                ),
                                validator: (value) {
                                  if (value!.isEmpty) {
                                    return 'Please enter some text';
                                  }
                                  return null;
                                },
                              ),
                    // : StreamBuilder(
                    //     stream: streamAmountLock.stream,
                    //     initialData: '0',
                    //     builder: (context, AsyncSnapshot snapshot) {
                    //       return new Text(
                    //         f
                    //             .format(double.parse(snapshot.data))
                    //             .toString(),
                    //         textAlign: TextAlign.center,
                    //         style: TextStyle(
                    //             color: LikeWalletAppTheme.white,
                    //             letterSpacing: 0.05,
                    //             fontFamily: AppLocalizations.of(context)
                    //                 .translate('font1'),
                    //             fontWeight: FontWeight.w300,
                    //             fontSize:
                    //                 mediaQuery(context, "height", 85)),
                    //       );
                    //     },
                    //   ),
                  ),
                ),
                AnimatedPositioned(
                  bottom: mediaQuery(context, 'height', 80),
                  duration: Duration(milliseconds: 300),
                  left: onTapUnlock
                      ? mediaQuery(context, 'width', 0)
                      : mediaQuery(context, 'width', 1080),
                  child: Container(
//          color: Colors.red,
                      width: mediaQuery(context, 'width', 930),
                      child: Row(
                        children: <Widget>[
                          GestureDetector(
                              onTap: () {
                                setState(() {
                                  onTapUnlock = false;
                                  onTapUnlockAll = 'no';
                                  // onTapUnlockAll = true;
                                });
                              },
                              child: Container(
                                  width: mediaQuery(context, 'width', 930) / 2,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: <Widget>[
                                      Image.asset(
                                        LikeWalletImage.icon_button_cancel,
                                        height:
                                            mediaQuery(context, 'height', 103),
                                      ),
                                      SizedBox(
                                        width: mediaQuery(context, 'width', 20),
                                      ),
                                      Text(
                                        AppLocalizations.of(context)!
                                            .translate('lock_button_cancel'),
                                        style: TextStyle(
                                            color: LikeWalletAppTheme.bule1,
                                            letterSpacing: 0.1,
                                            fontFamily:
                                                AppLocalizations.of(context)!
                                                    .translate('font1'),
                                            fontWeight: FontWeight.w300,
                                            fontSize: mediaQuery(
                                                context, "height", 36)),
                                      )
                                    ],
                                  ))),
                          GestureDetector(
                              onTap: () {
//                                _unlockLikePoint();

                                setState(() {
                                  _saving = true;
                                });
                                checkRewards().then((resultClaim) async {
                                  if (resultClaim) {
                                    setState(() {
                                      _saving = false;
                                    });
                                    await GetRewardsPopup(context);
                                    // Navigator.push(
                                    //     context,
                                    //     EnterExitRoute(
                                    //         exitPage: lockLike(),
                                    //         enterPage: hourlyRewards()));
                                  } else {
                                    await _requestUnlock(getPK,
                                            sendAmountUnLock, onTapUnlockAll)
                                        .then((data) {
                                      setState(() {
                                        success = true;
                                        Future.delayed(
                                            const Duration(milliseconds: 1000),
                                            () async {
                                          setState(() {
                                            onTapUnlock = false;
                                            success = false;
                                            awaitUnlock = false;
                                            amountUnLock.text = '';
                                          });
                                        });
                                        //                                });
                                      });
                                      print('unlock');
                                    });
                                    print('กำลังทำงานใหม่');
                                    getBalanceLock();
                                  }
                                });
                              },
                              child: Container(
                                  alignment: Alignment.center,
                                  width: mediaQuery(context, 'width', 930) / 2,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: <Widget>[
                                      Text(
                                        AppLocalizations.of(context)!
                                            .translate('lock_button_confirm'),
                                        style: TextStyle(
                                            color: LikeWalletAppTheme.bule1,
                                            letterSpacing: 0.1,
                                            fontFamily:
                                                AppLocalizations.of(context)!
                                                    .translate('font1'),
                                            fontWeight: FontWeight.w300,
                                            fontSize: mediaQuery(
                                                context, "height", 36)),
                                      ),
                                      SizedBox(
                                        width: mediaQuery(context, 'width', 20),
                                      ),
                                      Image.asset(
                                        LikeWalletImage.icon_button_next,
                                        height:
                                            mediaQuery(context, 'height', 103),
                                      ),
                                    ],
                                  ))),
                        ],
                      )),
                )
              ],
            )));
  }

  Widget _slider() {
    return Positioned(
        bottom: 0,
        child: Stack(
          alignment: Alignment.bottomCenter,
          children: <Widget>[
            Image.asset(
              LikeWalletImage.locklike_BG_slider,
              fit: BoxFit.cover,
              height: mediaQuery(context, 'height', 478),
              width: MediaQuery.of(context).size.width,
            ),
            Positioned(
              bottom: 0.0,
              left: 0.0,
              child: Container(
                  alignment: Alignment.bottomLeft,
                  height: mediaQuery(context, 'height', 478),
                  width: mediaQuery(context, 'width', 1080),
                  child: CarouselSlider(
                    options: CarouselOptions(
                      aspectRatio: 20 / 9,
                      viewportFraction: 1.0,
                      initialPage: 0,
                      enableInfiniteScroll: true,
                      reverse: true,
                      autoPlay: true,
                      autoPlayInterval: Duration(seconds: 5),
                      autoPlayAnimationDuration: Duration(milliseconds: 800),
                      autoPlayCurve: Curves.fastOutSlowIn,
                      enlargeCenterPage: true,
                    ),
                    items: imgList.map((i) {
                      return Builder(
                        builder: (BuildContext context) {
                          return Container(
//                              color: Colors.lightBlue,
                              alignment: Alignment.bottomLeft,
                              margin: i == imgList[0]
                                  ? EdgeInsets.only(
                                      right: mediaQuery(context, 'width', 110))
                                  : i == imgList[1]
                                      ? EdgeInsets.only(
                                          left: mediaQuery(context, 'width', 0),
                                        )
                                      : i == imgList[2]
                                          ? EdgeInsets.only(right: 25)
                                          : EdgeInsets.only(left: 0),
                              child: Image.asset(
                                i,
                                fit: i == imgList[1]
                                    ? BoxFit.fitHeight
                                    : BoxFit.fitHeight,
                                height: i == imgList[1]
                                    ? mediaQuery(context, 'height', 363.89)
                                    : mediaQuery(context, 'height', 476.03),
                                width: mediaQuery(context, 'width', 1080),
                              ));
                        },
                      );
                    }).toList(),
                  )),
            ),
          ],
        )
//          child:
        );
  }

  Widget showSuccess() {
    return Positioned(
        top: mediaQuery(context, 'height', 1370),
        right: mediaQuery(context, 'width', 15),
        child: Image.asset(
          LikeWalletImage.icon_success_white,
          height: mediaQuery(context, 'height', 223),
          width: mediaQuery(context, 'height', 223),
        ));
  }
}

const double _kRadius = 5;
const double _kBorderWidth = 0.8;

class MyPainter extends CustomPainter {
  MyPainter();

  @override
  void paint(Canvas canvas, Size size) {
    final rrectBorder =
        RRect.fromRectAndRadius(Offset.zero & size, Radius.circular(_kRadius));
    final rrectShadow =
        RRect.fromRectAndRadius(Offset(0, 0) & size, Radius.circular(_kRadius));

    final shadowPaint = Paint()
      ..strokeWidth = _kBorderWidth
      ..color = Colors.black.withOpacity(0.4)
      ..style = PaintingStyle.stroke
      ..maskFilter = MaskFilter.blur(BlurStyle.normal, 3);
    final borderPaint = Paint()
      ..strokeWidth = _kBorderWidth
      ..color = LikeWalletAppTheme.bule1_4
      ..style = PaintingStyle.stroke;

    canvas.drawRRect(rrectShadow, shadowPaint);
    canvas.drawRRect(rrectBorder, borderPaint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}
