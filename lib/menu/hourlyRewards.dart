// import 'dart:convert';
//
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:likewallet/ImageTheme.dart';
// import 'package:likewallet/Theme.dart';
//
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
//
// import 'package:likewallet/screen_util.dart';
// import 'package:likewallet/libraryman/app_local.dart';
//
// import 'dart:async';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:likewallet/bank/confirm_transection.dart';
// import 'dart:math' as math;
// import 'package:likewallet/libraryman/ethcontract.dart';
// import 'package:likewallet/libraryman/address_service.dart';
// import 'package:likewallet/libraryman/configuration_service.dart';
// import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
// import 'package:http/http.dart' as http;
// import 'package:likewallet/app_config.dart';
// import 'package:likewallet/setmodel/next_rewards_model.dart';
// import 'package:fluttertoast/fluttertoast.dart';
// import 'package:likewallet/libraryman/custom_loading.dart';
// import 'package:intl/intl.dart' as formatIntl;
// import 'package:likewallet/screen/NavigationBar.dart';
//
// class hourlyRewards extends StatefulWidget {
//   _hourlyRewards createState() => new _hourlyRewards();
// }
//
// enum statusClaim { INACTIVE, ACTIVE }
//
// class _hourlyRewards extends State<hourlyRewards>
//     with TickerProviderStateMixin {
//   final f = new formatIntl.NumberFormat("###,###");
//
//   final fdecimal = new formatIntl.NumberFormat("###,###.#####");
//
//   late String dropdownValue;
//   double nextRewards = 0;
//   double upcomingRewards = 0;
//   double rewards = 0.0;
//   double totalLocked = 0.0;
//   late IAddressService addressService;
//   late IConfigurationService configETH;
//   late BaseETH eth;
//   statusClaim isChaim = statusClaim.INACTIVE;
//   late String addressETH;
//   late String mnemonic;
//   late String getPK;
//   bool _saving = false;
//   int tabMenu = 0;
//
//   int countLoop = 0;
//   late Timer timer;
//
//   late List<NextRewards> listRewards;
//
//   changeMenu(_number) async {
//     setState(() {
//       tabMenu = _number;
//     });
//   }
//
//   late AnimationController _animationController;
//   late AnimationController controller;
//
//   @override
//   void initState() {
//     super.initState();
//     _animationController =
//         new AnimationController(vsync: this, duration: Duration(seconds: 1));
//
//     _animationController.repeat();
//
//     controller = AnimationController(
//       vsync: this,
//       duration: Duration(seconds: 10),
//     );
//
//     new Future.delayed(new Duration(milliseconds: 500), () {
//       eth = new EthContract();
//       setInit();
//     });
//
//     timer = Timer.periodic(new Duration(seconds: 1), (timerx) {
//       if (countLoop == 0) {
//         if (timerOut <= 0) {
//           countLoop = 1;
//           timer.cancel();
//         }
//       }
//     });
//   }
//
//   @override
//   void dispose() {
//     controller.dispose();
//     _animationController.dispose();
//     timer.cancel();
//
//     super.dispose();
//   }
//
//   void showColoredToast(msg, colors) {
//     Fluttertoast.showToast(
//         msg: msg,
//         toastLength: Toast.LENGTH_LONG,
//         backgroundColor: colors,
//         textColor: Colors.white);
//   }
//
//   void showShortToast(msg) {
//     Fluttertoast.showToast(
//         msg: msg,
//         toastLength: Toast.LENGTH_SHORT,
//         backgroundColor: Colors.cyan,
//         textColor: Colors.white);
//   }
//
//   setInit() async {
//     setState(() {
//       _saving = true;
//     });
//
//     var url = Uri.https(env.apiUrl, '/nextRewards');
//     var response = await http.post(url, body: {'application': 'LIKEWALLET'});
//
//     print('Response status: ${response.statusCode}');
//     print('Response body: ${response.body}');
//     var body = jsonDecode(response.body);
//
//     //รอบการแจกถัดไป
//     if (body['statusCode'] == 200) {
//       print(body["result"]);
//       final body2 = NextRewards.fromJson(body["result"]);
//
//       final currentTime = DateTime.now();
//
//       //คำนวณเวลา
//       final diffTime = (((double.parse(body2.round_time) / 1000) -
//           (currentTime.millisecondsSinceEpoch + 25200000) / 1000));
//
//       print(double.parse(body2.round_time));
//       print(currentTime.millisecondsSinceEpoch + 25200);
//       int valueSend = diffTime.toString().indexOf('.');
//       print(diffTime.toString().substring(0, valueSend));
//       if (!mounted) return;
//       setState(() {
//         totalLocked = double.parse(body2.total_locked);
//         nextRewards = double.parse(body2.rewards);
//
//         controller = AnimationController(
//           vsync: this,
//           duration: Duration(
//               seconds:
//                   int.parse(diffTime.toString().substring(0, valueSend)) <= 0.0
//                       ? 0
//                       : int.parse(diffTime.toString().substring(0, valueSend))),
//         );
//         {
//           if (controller.isAnimating)
//             controller.stop();
//           else {
//             controller.reverse(
//                 from: controller.value == 0.0 ? 1.0 : controller.value);
//           }
//         }
//         ;
//       });
//
// //      final body2 = body["result"];
// //      listRewards = body2.map<NextRewards>((json) => NextRewards.fromJson(json)).toList();
// //      listRewards.forEach((f) {
// //        setState(() {
// //          nextRewards = double.parse(f.total_locked);
// //          upcomingRewards = double.parse(f.rewards);
// //        });
// //
// //      });
//
//       SharedPreferences pref = await SharedPreferences.getInstance();
//       configETH = new ConfigurationService(pref);
//       addressService = new AddressService(configETH);
//       mnemonic = pref.getString('seed') ?? '';
//
// //    getPK = addressService.getPrivateKey(mnemonic);
//       getPK = await configETH.getPrivateKey();
//
//       setState(() {
//         addressETH = configETH.getAddress();
//       });
//
// //    addressService.getPublicAddress(getPK).then((address) {
//       print(addressETH);
// //      pref.setString('ethAddr', addressETH.toString());
//
//       //check lock time
//       eth.getDepositTime(address: addressETH).then((timeLock) {
//         print('time lock : ' + timeLock);
//         print(
//             'round_time : ' + (int.parse(body2.round_time) / 1000).toString());
//         if ((int.parse(body2.created_time) / 1000) < int.parse(timeLock)) {
//           print('time next');
//           //upcomingRewards = 0;
//           eth.getBalanceLock(address: addressETH).then((lock) {
//             print('lock: ' + lock.toString());
//             print("totalLocked: " + totalLocked.toString());
//             print('nextRewards: ' + nextRewards.toString());
//
//             if (!mounted) return;
//             setState(() {
//               upcomingRewards = lock / totalLocked * nextRewards;
//             });
//           });
//         } else if (int.parse(timeLock) == 0) {
//           if (!mounted) return;
//           setState(() {
//             upcomingRewards = 0;
//           });
//         } else {
//           //check balance of lock
//           print('time get rewards');
//           eth.getBalanceLock(address: addressETH).then((lock) {
//             print('lock: ' + lock.toString());
//             print("totalLocked: " + totalLocked.toString());
//             print('nextRewards: ' + nextRewards.toString());
//
//             if (!mounted) return;
//             setState(() {
//               upcomingRewards = lock / totalLocked * nextRewards;
//             });
//           });
//         }
//       });
//       //check status claim
//       eth.checkClaim(address: addressETH).then((lastTime) {
//         print('lastTime ' + lastTime.toString());
//         //check current round airdrop
//         eth.getRound().then((roundAirdrop) {
//           print('roundAirdrop ' + roundAirdrop.toString());
//           if (int.parse(lastTime) >= int.parse(roundAirdrop)) {
//             eth.isWithdraw(address: addressETH).then((dataBack) {
//               print('dataBack ' + dataBack.toString());
//               if (dataBack == 1) {
//                 if (!mounted) return;
//                 setState(() {
//                   isChaim = statusClaim.INACTIVE;
//                   rewards = 0.0;
//                   _saving = false;
//                 });
//               } else {
//                 if (!mounted) return;
//                 setState(() {
//                   isChaim = statusClaim.ACTIVE;
//                   _saving = false;
//                 });
//               }
//             });
//           } else {
//             //check reward
//             //check ว่ามีการถอนไว้หรือเปล่า
//             eth.isWithdraw(address: addressETH).then((dataBack) {
//               if (dataBack == 1) {
//                 setState(() {
//                   isChaim = statusClaim.INACTIVE;
//                   rewards = 0.0;
//                   _saving = false;
//                 });
//               } else {
//                 eth.checkRewards(address: addressETH).then((balance) {
//                   if (!mounted) return;
//                   setState(() {
//                     rewards = double.parse(balance.toString());
//                     _saving = false;
//                   });
//                 });
//                 if (!mounted) return;
//                 setState(() {
//                   isChaim = statusClaim.ACTIVE;
//                 });
//               }
//             });
//           }
//         });
//       });
//
// //    });getPublicAddress
//       print(body2);
//     }
//   }
//
// //claim
//   _claimRewards() {
//     if (!mounted) return;
//     setState(() {
//       rewards = 0.0;
//       _saving = true;
//     });
//
//     eth.ClaimRewards(pk: getPK).then((tx) {
//       if (!mounted) return;
//       setState(() {
//         new Future.delayed(new Duration(seconds: 4), () {
//           setInit();
//           _saving = false;
//         });
//       });
//     }).catchError((onError) {
//       _saving = false;
//     });
//   }
//
//   int get timerOut {
//     return (controller.duration! * controller.value).inMilliseconds;
//   }
//
//   String get timerString {
//     Duration duration = controller.duration! * controller.value;
//     return '${(duration.inHours % 60).toString().padLeft(2, '0')}:${(duration.inMinutes % 60).toString().padLeft(2, '0')}:${(duration.inSeconds % 60).toString().padLeft(2, '0')}';
//   }
//
//   TextEditingController addressText = TextEditingController();
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       body: ModalProgressHUD(
//         child: buildForPhone(),
//         inAsyncCall: _saving,
//         opacity: 0.1,
//         progressIndicator: CustomLoading(),
//       ),
//     );
//   }
//
//   buildForPhone() {
//     String messageUpcoming =
//         AppLocalizations.of(context)!.translate('hourlyRewards_please');
//
//     return Scaffold(
//       body: tabMenu == 1
//           ? confirmTransection()
//           : SingleChildScrollView(
//               child: Stack(
//               alignment: Alignment.center,
//               children: <Widget>[
//                 //สีพื้นหลัง
//                 background(context),
//                 //พื้นหลังสีดำ เเละ นับถอยหลัง
//                 Positioned(
//                     top: mediaQuery(context, "height", 0),
//                     child: _container_black()),
//                 //ปุ่มกกลับ
//                 Positioned(
//                   top: mediaQuery(context, 'height', 139.9),
//                   left: mediaQuery(context, 'width', 75),
//                   child: backButton(context, LikeWalletAppTheme.gray),
//                 ),
//                 //ยอดรวมรางวัลของกลุ่ม
//                 Positioned(
//                     top: mediaQuery(context, "height", 168), child: _upcome()),
//                 //icon
//                 Positioned(
//                     top: mediaQuery(context, "height", 355.5),
//                     left: mediaQuery(context, "width", 51.34),
//                     child: _icon()),
//                 // ส่วนเเบ่งรางวัล
//                 Positioned(
//                   top: mediaQuery(context, "height", 769),
//                   child: _upcomelike(messageUpcoming),
//                 ),
//                 //รางวัลยังไม่ได้เคลม
//                 Positioned(
//                   top: mediaQuery(context, "height", 1161),
//                   child: _unclaim(),
//                 ),
//                 //ปุ่มเคลม
//                 Positioned(
//                   top: mediaQuery(context, "height", 1429),
//                   child: _button(),
//                 ),
//
//                 //อธิบายรายละเอียด
//                 Positioned(
//                     top: mediaQuery(context, "height", 1688), child: _detail()),
// //                //เมนูbar ด้านล่าง
// //                Container(
// //                  alignment: Alignment.bottomCenter,
// //                  height: MediaQuery.of(context).size.height,
// //                  child: NavigationBar(),
// //                )
//               ],
//             )),
//     );
//   }
//
//   @override
// // TODO: implement widget
//   Widget _upcome() {
//     return new Column(
//       children: <Widget>[
//         Container(
//           width: mediaQuery(context, "width", 940),
//           alignment: Alignment.centerRight,
//           child: new Text(
//             AppLocalizations.of(context)!.translate('hourlyRewards_title1'),
//             textAlign: TextAlign.right,
//             style: LikeWalletAppTheme.textStyle(
//                 context,
//                 40,
//                 LikeWalletAppTheme.gray.withOpacity(0.6),
//                 FontWeight.normal,
//                 'font1'),
//           ),
//         ),
//         Container(
//           width: mediaQuery(context, "width", 940),
//           alignment: Alignment.centerRight,
//           child: new Text(
//             f.format(nextRewards).toString(),
//             textAlign: TextAlign.right,
//             style: LikeWalletAppTheme.textStyle(
//                 context,
//                 90,
//                 LikeWalletAppTheme.white.withOpacity(0.7),
//                 FontWeight.w400,
//                 'font2'),
//           ),
//         ),
//       ],
//     );
//   }
//
//   Widget _container_black() {
//     return new Container(
//       alignment: Alignment.center,
//       height: mediaQuery(context, "height", 674.9),
//       width: mediaQuery(context, "width", 1080),
//       decoration: BoxDecoration(
//         //การไล่สีพื้นหลัง
//         gradient: LinearGradient(
//           begin: Alignment.topCenter,
//           end: Alignment.bottomCenter,
//           stops: [0.0, 0.3, 0.4, 0.6, 0.7],
//           colors: [
//             // Colors are easy thanks to Flutter's Colors class.
//             LikeWalletAppTheme.bule2.withOpacity(0.7),
//             LikeWalletAppTheme.bule2.withOpacity(0.8),
//             LikeWalletAppTheme.bule2.withOpacity(0.8),
//             LikeWalletAppTheme.bule2.withOpacity(0.8),
//             LikeWalletAppTheme.bule2.withOpacity(1)
//           ],
//         ),
//         boxShadow: [
//           BoxShadow(
//             color: LikeWalletAppTheme.black.withOpacity(0.5),
//             blurRadius: 8.0, // has the effect of softening the shadow
//             spreadRadius: 0.0, // has the effect of extending the shadow
//             offset: Offset(
//               0.0, // horizontal, move right 10
//               2.0, // vertical, move down 10
//             ),
//           )
//         ],
//         borderRadius: BorderRadius.all(Radius.circular(15.0)),
//       ),
//       child: _counting(),
//     );
//   }
//
//   Widget _icon() {
//     return Container(
//       alignment: Alignment.center,
//       child: Image.asset(
//         LikeWalletImage.icon_NextDis,
//         height: mediaQuery(context, "height", 318.59),
//         width: mediaQuery(context, "width", 468.06),
//       ),
//     );
//   }
//
//   Widget _counting() {
//     return Container(
//         width: mediaQuery(context, "width", 600),
//         alignment: Alignment.center,
//         padding: EdgeInsets.only(
//             left: mediaQuery(context, 'width', 30),
//             bottom: mediaQuery(context, 'height', 30)),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           mainAxisAlignment: MainAxisAlignment.end,
//           children: <Widget>[
//             new Text(
//               AppLocalizations.of(context)!.translate('hourlyRewards_next'),
//               textAlign: TextAlign.right,
//               style: LikeWalletAppTheme.textStyle(context, 32,
//                   LikeWalletAppTheme.bule1, FontWeight.normal, 'font1'),
//             ),
//             AnimatedBuilder(
//               animation: controller,
//               builder: (context, child) {
//                 return
//                     // controller.isAnimating
//                     //   ?
//                     Text(
//                   timerString,
//                   textAlign: TextAlign.right,
//                   style: LikeWalletAppTheme.textStyle(context, 60,
//                       LikeWalletAppTheme.lemon, FontWeight.normal, 'font2'),
//                 );
//                 // : FadeTransition(
//                 //     opacity: _animationController,
//                 //     child: Text(
//                 //       AppLocalizations.of(context)
//                 //           .translate('hourlyRewards_distributing'),
//                 //       textAlign: TextAlign.right,
//                 //       style: LikeWalletAppTheme.textStyle(
//                 //           context,
//                 //           45,
//                 //           LikeWalletAppTheme.lemon,
//                 //           FontWeight.normal,
//                 //           'font2'),
//                 //     ),
//                 //   );
//               },
//             ),
//           ],
//         ));
//   }
//
//   Widget _upcomelike(messageUpcoming) {
//     return Container(
//         alignment: Alignment.center,
//         child: Column(
//           children: <Widget>[
//             new Text(
//               AppLocalizations.of(context)!.translate('hourlyRewards_coming'),
//               textAlign: TextAlign.center,
//               style: LikeWalletAppTheme.textStyle(
//                   context,
//                   45,
//                   LikeWalletAppTheme.gray.withOpacity(0.7),
//                   FontWeight.w100,
//                   'font2'),
//             ),
//             new Text(
//               //ถ้าสามารถเคลมได้ให้แสดงข้อความเหรียญที่จะได้ในรอบถัดไป
//               isChaim == statusClaim.ACTIVE
//                   ? upcomingRewards == 0.0
//                       ? messageUpcoming
//                       : '${fdecimal.format(upcomingRewards)}'
//                   : "0",
//               style: isChaim == statusClaim.ACTIVE
//                   ? upcomingRewards == 0.0
//                       ? LikeWalletAppTheme.textStyle(context, 45,
//                           LikeWalletAppTheme.white, FontWeight.normal, 'font2')
//                       : LikeWalletAppTheme.textStyle(context, 69,
//                           LikeWalletAppTheme.white, FontWeight.normal, 'font2')
//                   : LikeWalletAppTheme.textStyle(context, 69,
//                       LikeWalletAppTheme.white, FontWeight.normal, 'font2'),
//             ),
//           ],
//         ));
//   }
//
//   Widget _unclaim() {
//     return Container(
//         alignment: Alignment.center,
//         child: Column(
//           children: <Widget>[
//             new Text(
//               AppLocalizations.of(context)!.translate('hourlyRewards_unclaimed'),
//               textAlign: TextAlign.center,
//               style: LikeWalletAppTheme.textStyle(context, 45,
//                   LikeWalletAppTheme.bule1, FontWeight.w100, 'font2'),
//             ),
//             new Text(
//               fdecimal.format(rewards).toString(),
//               style: LikeWalletAppTheme.textStyle(context, 80,
//                   LikeWalletAppTheme.bule1, FontWeight.normal, 'font2'),
//             ),
//           ],
//         ));
//   }
//
//   Widget _button() {
//     return Container(
//       alignment: Alignment.center,
//       child: ButtonTheme(
//         minWidth: mediaQuery(context, "width", 930),
//         height: mediaQuery(context, "height", 132),
//         child: isChaim == statusClaim.INACTIVE
//             ? new FlatButton(
//                 shape: new RoundedRectangleBorder(
//                   borderRadius: new BorderRadius.circular(8.0),
//                 ),
//                 disabledColor: Color(0xff707071).withOpacity(0.64),
//                 color: Color(0xff707071).withOpacity(0.64),
//                 onPressed: () => {},
//                 child: new Text(
//                   AppLocalizations.of(context)!
//                             .translate('hourlyRewards_button_unclaimed'),
//                   style: LikeWalletAppTheme.textStyle(context, 52,
//                       LikeWalletAppTheme.gray, FontWeight.bold, 'font2'),
//                 ))
//             : rewards == 0
//                 ? new FlatButton(
//                     shape: new RoundedRectangleBorder(
//                       borderRadius: new BorderRadius.circular(8.0),
//                     ),
//                     disabledColor: LikeWalletAppTheme.gray.withOpacity(0.64),
//                     color: LikeWalletAppTheme.gray.withOpacity(0.64),
//                     onPressed: () {},
//                     child: new Text(
//                       AppLocalizations.of(context)!
//                             .translate('hourlyRewards_button_unclaimed'),
//                       style: LikeWalletAppTheme.textStyle(
//                           context,
//                           52,
//                           LikeWalletAppTheme.white.withOpacity(1),
//                           FontWeight.bold,
//                           'font2'),
//                     ))
//                 : new FlatButton(
//                     shape: new RoundedRectangleBorder(
//                       borderRadius: new BorderRadius.circular(8.0),
//                     ),
//                     disabledColor: LikeWalletAppTheme.bule1,
//                     color: LikeWalletAppTheme.bule1,
//                     onPressed: () {
//                       _claimRewards();
//                     },
//                     child: new Text(
//                       AppLocalizations.of(context)!
//                             .translate('hourlyRewards_button_claim'),
//                       style: LikeWalletAppTheme.textStyle(context, 52,
//                           LikeWalletAppTheme.black, FontWeight.bold, 'font2'),
//                     )),
//       ),
//     );
//   }
//
//   Widget _detail() {
//     return Container(
//         width: mediaQuery(context, "width", 903),
//         child: Column(
//           children: <Widget>[
//             new Text(
//               AppLocalizations.of(context)!.translate('hourlyRewards_detail'),
//               textAlign: TextAlign.left,
//               style: LikeWalletAppTheme.textStyle(
//                   context,
//                   38,
//                   LikeWalletAppTheme.white.withOpacity(0.5),
//                   FontWeight.normal,
//                   'font2'),
//             ),
//           ],
//         ));
//   }
// }
//
// background(BuildContext context) {
//   return Container(
//       height: MediaQuery.of(context).size.height,
//       decoration: BoxDecoration(
//         gradient: LinearGradient(
//           begin: Alignment.topCenter,
//           end: Alignment.bottomCenter,
//           stops: [0.3, 0.4, 0.5, 0.6, 0.7],
//           colors: [
//             // Colors are easy thanks to Flutter's Colors class.
//             LikeWalletAppTheme.bule2.withOpacity(0.95),
//             LikeWalletAppTheme.bule2.withOpacity(0.95),
//             LikeWalletAppTheme.bule2.withOpacity(0.95),
//             LikeWalletAppTheme.bule2.withOpacity(0.92),
//             LikeWalletAppTheme.bule2.withOpacity(0.9)
//           ],
//         ),
//       ));
// }
