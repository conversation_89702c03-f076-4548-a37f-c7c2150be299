class News {
  var title;
  var detail;
  var status;
  var timestamp;
  var image;
  var url;

  News(this.title, this.detail, this.status, this.timestamp, this.image,
      this.url);

  News.fromJson(Map<String, dynamic> json)
      : title = json['title'],
        detail = json['detail'],
        status = json['status'],
        timestamp = json['timestamp'],
        image = json['image'],
        url = json['url'];

  Map<String, dynamic> toJson() => {
        'title': title,
        'detail': detail,
        'status': status,
        'timestamp': timestamp,
        'image': image,
        'url': url,
      };
}
