class WhiteListPPP7 {
  var status;
  var tier;
  var level;
  var token_Line;

  WhiteListPPP7(this.status, this.tier, this.level, this.token_Line);

  WhiteListPPP7.fromJson(Map<String, dynamic> json)
      : status = json['status'],
        tier = json['tier'],
        level = json['level'],
        token_Line = json['token_Line'];

  Map<String, dynamic> toJson() => {
        'status': status,
        'tier': tier,
        'level': level,
        'token_Line': token_Line
      };
}
