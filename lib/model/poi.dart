import 'dart:convert';

List<PoiM> poiMFromJson(List<dynamic> str) => List<PoiM>.from(str.map((x) => PoiM.fromJson(x)));

String poiMToJson(List<PoiM> data) => json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class PoiM {
  PoiM({
    this.taxPoint,
    required this.payBuActivity,
    this.gojoyPointPool,
    this.poolPercent,
    required this.timeUnlock,
    required this.typeBuGr,
    required this.bagBu,
    required this.idLdx,
    required this.transferTime,
    required this.address,
    required this.typeBagBu,
    required this.expireDateTranfer,
    required this.idmember,
    required this.poolAddress,
    this.idActivity,
    required this.fullname,
    required this.typeBu,
    required this.tx,
    this.gojoyPointUser,
    required this.checkCancel,
    required this.gojoyIdActivity,
    required this.notePlus,
    required this.useBuGr,
    required this.telLdx,
    required this.fromAddress,
    required this.name,
    required this.useBagName,
    required this.nameActivity,
    required this.fullnameLdx,
    required this.nameLdx,
    required this.statusCopy,
    required this.status,
    required this.userTransfer,
    required this.lnameLdx,
    required this.statusPlus,
    required this.payBuMember,
    required this.lname,
    required this.isGojoy,
    required this.timeConvert,
    required this.createUser,
    required this.useBu,
    required this.isAutoClaim,
    this.taxPer,
    required this.bu,
    required this.statusCheck,
    this.sumall,
    this.dayLock,
    required this.gojoyNameActivity,
    required this.pool,
    required this.selectKey,
    this.point,
    required this.active,
    required this.dayN,
    required this.updateUser,
    required this.createTime,
    required this.updateTime,
    required this.running,
    required this.timeUnlockStatus,
    required this.popupContinueLock,
  });

  dynamic taxPoint;
  String payBuActivity;
  dynamic gojoyPointPool;
  dynamic poolPercent;
  String timeUnlock;
  String typeBuGr;
  String bagBu;
  String idLdx;
  String transferTime;
  String address;
  String typeBagBu;
  String expireDateTranfer;
  String idmember;
  String poolAddress;
  dynamic idActivity;
  String fullname;
  String typeBu;
  String tx;
  dynamic gojoyPointUser;
  String checkCancel;
  String gojoyIdActivity;
  String notePlus;
  String useBuGr;
  String telLdx;
  String fromAddress;
  String name;
  String useBagName;
  String nameActivity;
  String fullnameLdx;
  String nameLdx;
  String statusCopy;
  String status;
  String userTransfer;
  String lnameLdx;
  String statusPlus;
  String payBuMember;
  String lname;
  String isGojoy;
  String timeConvert;
  String createUser;
  String useBu;
  String isAutoClaim;
  dynamic taxPer;
  String bu;
  String statusCheck;
  dynamic sumall;
  dynamic dayLock;
  String gojoyNameActivity;
  String pool;
  String selectKey;
  dynamic point;
  String active;
  String dayN;
  String updateUser;
  String createTime;
  String updateTime;
  String running;
  String timeUnlockStatus;
  bool popupContinueLock;

  factory PoiM.fromJson(Map<String, dynamic> json) => PoiM(
    taxPoint: json["tax_point"],
    payBuActivity: json["pay_bu_activity"],
    gojoyPointPool: json["gojoy_point_pool"],
    poolPercent: json["pool_percent"],
    timeUnlock: json["time_unlock"],
    typeBuGr: json["type_bu_gr"],
    bagBu: json["bag_bu"],
    idLdx: json["id_ldx"],
    transferTime: json["transfer_time"],
    address: json["address"],
    typeBagBu: json["type_bag_bu"],
    expireDateTranfer: json["expire_date_tranfer"],
    idmember: json["idmember"],
    poolAddress: json["pool_address"],
    idActivity: json["id_activity"],
    fullname: json["fullname"],
    typeBu: json["type_bu"],
    tx: json["tx"],
    gojoyPointUser: json["gojoy_point_user"],
    checkCancel: json["checkCancel"],
    gojoyIdActivity: json["gojoy_id_activity"],
    notePlus: json["note_plus"],
    useBuGr: json["use_bu_gr"],
    telLdx: json["tel_ldx"],
    fromAddress: json["fromAddress"],
    name: json["name"],
    useBagName: json["use_bag_name"],
    nameActivity: json["name_activity"],
    fullnameLdx: json["fullname_ldx"],
    nameLdx: json["name_ldx"],
    statusCopy: json["status_copy"],
    status: json["status"],
    userTransfer: json["user_transfer"],
    lnameLdx: json["lname_ldx"],
    statusPlus: json["status_plus"],
    payBuMember: json["pay_bu_member"],
    lname: json["lname"],
    isGojoy: json["isGojoy"],
    timeConvert: json["time_convert"],
    createUser: json["create_user"],
    useBu: json["use_bu"],
    isAutoClaim: json["isAutoClaim"],
    taxPer: json["tax_per"],
    bu: json["bu"],
    statusCheck: json["status_check"],
    sumall: json["sumall"],
    dayLock: json["day_lock"],
    gojoyNameActivity: json["gojoy_name_activity"],
    pool: json["pool"],
    selectKey: json["select_key"],
    point: json["point"],
    active: json["active"],
    dayN: json["day_n"],
    updateUser: json["update_user"],
    createTime: json["create_time"],
    updateTime: json["update_time"],
    running: json["running"],
    timeUnlockStatus: json["timeunlock_status"],
    popupContinueLock: json["popupContinueLock"],
  );

  Map<String, dynamic> toJson() => {
    "tax_point": taxPoint,
    "pay_bu_activity": payBuActivity,
    "gojoy_point_pool": gojoyPointPool,
    "pool_percent": poolPercent,
    "time_unlock": timeUnlock,
    "type_bu_gr": typeBuGr,
    "bag_bu": bagBu,
    "id_ldx": idLdx,
    "transfer_time": transferTime,
    "address": address,
    "type_bag_bu": typeBagBu,
    "expire_date_tranfer": expireDateTranfer,
    "idmember": idmember,
    "pool_address": poolAddress,
    "id_activity": idActivity,
    "fullname": fullname,
    "type_bu": typeBu,
    "tx": tx,
    "gojoy_point_user": gojoyPointUser,
    "checkCancel": checkCancel,
    "gojoy_id_activity": gojoyIdActivity,
    "note_plus": notePlus,
    "use_bu_gr": useBuGr,
    "tel_ldx": telLdx,
    "fromAddress": fromAddress,
    "name": name,
    "use_bag_name": useBagName,
    "name_activity": nameActivity,
    "fullname_ldx": fullnameLdx,
    "name_ldx": nameLdx,
    "status_copy": statusCopy,
    "status": status,
    "user_transfer": userTransfer,
    "lname_ldx": lnameLdx,
    "status_plus": statusPlus,
    "pay_bu_member": payBuMember,
    "lname": lname,
    "isGojoy": isGojoy,
    "time_convert": timeConvert,
    "create_user": createUser,
    "use_bu": useBu,
    "isAutoClaim": isAutoClaim,
    "tax_per": taxPer,
    "bu": bu,
    "status_check": statusCheck,
    "sumall": sumall,
    "day_lock": dayLock,
    "gojoy_name_activity": gojoyNameActivity,
    "pool": pool,
    "select_key": selectKey,
    "point": point,
    "active": active,
    "day_n": dayN,
    "update_user": updateUser,
    "create_time": createTime,
    "update_time": updateTime,
    "running": running,
    "timeUnlockStatus": timeUnlockStatus,
    "popupContinueLock": popupContinueLock,
  };
}
