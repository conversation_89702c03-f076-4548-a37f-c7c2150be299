class ActivityLock {
  String timeConvert;
  var point;
  String transferTime;
  String createTime;
  String nameActivity;

  ActivityLock(
    this.timeConvert,
    this.point,
    this.transferTime,
    this.createTime,
    this.nameActivity,
  );

  ActivityLock.fromJson(Map<String, dynamic> json)
      : timeConvert = json['time_convert'],
        point = json['point'],
        transferTime = json['transfer_time'],
        createTime = json['create_time'],
        nameActivity = json['name_activity'];

  Map<String, dynamic> toJson() => {
        'timeConvert': timeConvert,
        'point': point,
        'transferTime': transferTime,
        'createTime': createTime,
        'nameActivity': nameActivity,
      };
}
