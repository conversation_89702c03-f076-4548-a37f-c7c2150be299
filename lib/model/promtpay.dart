class PromptPayBuy {
  String? qrcode;
  String? partnerTxnUid;
  String? requestDt;
  String? merchantId;
  String? terminalId;
  String? QRpartnerTxnUid;
  String? qrType;
  PromptPayBuy({this.qrcode, this.partnerTxnUid, this.requestDt, this.merchantId, this.terminalId, this.QRpartnerTxnUid, this.qrType});

  factory PromptPayBuy.fromJson(Map<String, dynamic> json) {
    return PromptPayBuy(
      qrcode: json["qrcode"] as String,
      partnerTxnUid: json["partnerTxnUid"] as String,
      requestDt: json["requestDt"] as String,
      merchantId: json["merchantId"] as String,
      terminalId:json["terminalId"] as String,
      QRpartnerTxnUid: json["QRpartnerTxnUid"] as String,
      qrType:json["qrType"] as String,
    );
  }
}
