class YourLotto {
  List number;
  String tokendId;
  double reward;

  YourLotto(
    this.number,
    this.tokendId,
    this.reward,
  );

  YourLotto.fromJson(Map<String, dynamic> json)
      : number = json['number'],
        tokendId = json['tokendId'],
        reward = json['reward'];

  Map<String, dynamic> toJson() => {
        'number': number,
        'tokendId': tokendId,
        'reward': reward,
      };
}
