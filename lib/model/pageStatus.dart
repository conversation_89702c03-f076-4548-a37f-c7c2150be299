class PageMaintenance {
  var status;
  var title;
  var detail;
  var detail_time;
  var url;
  bool permission;

  PageMaintenance(this.status, this.title, this.detail, this.detail_time,
      this.url, this.permission);

  PageMaintenance.fromJson(Map<String, dynamic> json)
      : status = json['status'],
        title = json['title'],
        detail = json['detail'],
        detail_time = json['detail_time'],
        url = json['url'],
        permission = json['permission'];

  Map<String, dynamic> toJson() => {
        'status': status,
        'title': title,
        'detail': detail,
        'detail_time': detail_time,
        'url': url,
        'permission': permission
      };
}
