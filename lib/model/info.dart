class Info {
  var likewallet;
  var ldx;
  var pkg;
  var other;
  var total;

  Info(this.likewallet, this.ldx, this.pkg, this.other, this.total);

  Info.fromJson(Map<String, dynamic> json)
      : likewallet = json['likewallet'],
        ldx = json['ldx'],
        pkg = json['pkg'],
        other = json['other'],
        total = json['total'];

  Map<String, dynamic> toJson() => {
        'likewallet': likewallet,
        'ldx': ldx,
        'pkg': pkg,
        'other': other,
        'total': total,
      };
}
