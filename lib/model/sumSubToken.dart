class SumSubToken {
  // var statusCode;
  var message;
  var uid;
  var token;
  SumSubToken(this.token);

  SumSubToken.fromJson(Map<String, dynamic> json)
      : message = json['message'],
        uid = json['uid'],
        token = json['token'];

  Map<String, dynamic> toJson() => {
        'message': message,
        'uid': uid,
        'token': token,
      };
}

class StatusSumSub {
  var statusCode;
  var status;
  // var message;
  var result;

  StatusSumSub({this.statusCode, this.status, this.result});

  StatusSumSub.fromJson(Map<String, dynamic> json)
      : statusCode = json['statusCode'],
        status = json['status'],
        // message = json['message'],
        result = json['Result'];

  Map<String, dynamic> toJson() => {
        'statusCode': statusCode,
        'status': status,
        // 'message': message,
        'result': result
      };
}

class Result {
  var reviewRejectType;
  var reviewAnswer;
  var clientComment;
  var moderationComment;
  var rejectLabels;

  Result(this.reviewRejectType, this.reviewAnswer, this.clientComment,
      this.moderationComment, this.rejectLabels);

  Result.fromJson(Map<String, dynamic> json)
      : reviewRejectType = json['reviewRejectType'],
        reviewAnswer = json['reviewAnswer'],
        clientComment = json['clientComment'],
        rejectLabels = json['rejectLabels'];

  Map<String, dynamic> toJson() => {
        'reviewRejectType': reviewRejectType,
        'reviewAnswer': reviewAnswer,
        'clientComment': clientComment,
        'rejectLabels': rejectLabels,
      };
}
