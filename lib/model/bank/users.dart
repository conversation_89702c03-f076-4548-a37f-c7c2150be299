// Copyright 2021, the Chromium project authors.  Please see the AUTHORS file
// for details. All rights reserved. Use of this source code is governed by a
// BSD-style license that can be found in the LICENSE file.

import 'package:flutter/foundation.dart';
// firstName lastName numberID phone_number refCode selfCode wallet_number
@immutable
class UsersModel {
  UsersModel({
    required this.firstName,
    required this.lastName,
    required this.numberID,
    required this.phone_number,
    required this.refCode,
    required this.selfCode,
    required this.wallet_number
  });

  UsersModel.fromJson(Map<String, Object?> json)
      : this(
    firstName: json['firstName']! as String,
    lastName: json['lastName']! as String,
    numberID: json['numberID']! as String,
    phone_number: json['phone_number']! as String,
    refCode: json['refCode']! as String,
    selfCode: json['selfCode']! as String,
    wallet_number: json['wallet_number']! as String,
  );

  final String firstName;
  final String lastName;
  final String numberID;
  final String phone_number;
  final String refCode;
  final String selfCode;
  final String wallet_number;

  Map<String, Object?> toJson() {
    return {
      'firstName': firstName,
      'lastName': lastName,
      'numberID': numberID,
      'phone_number': phone_number,
      'refCode': refCode,
      'selfCode': selfCode,
      'wallet_number': wallet_number,
    };
  }
}