// Copyright 2021, the Chromium project authors.  Please see the AUTHORS file
// for details. All rights reserved. Use of this source code is governed by a
// BSD-style license that can be found in the LICENSE file.

import 'package:flutter/foundation.dart';

@immutable
class ExchangeFiat {
  ExchangeFiat(
      {required this.main,
      required this.rate,
      required this.rowSheetAt,
      required this.to,
      required this.uid});

  ExchangeFiat.fromJson(Map<String, Object?> json)
      : this(
          main: json['main']! as String,
          rate: json['rate'],
          rowSheetAt: json['rowSheetAt']! as int,
          to: json['to']! as String,
          uid: json['uid']! as String,
        );

  final String main;
  final rate;
  final int rowSheetAt;
  final String to;
  final String uid;

  Map<String, Object?> toJson() {
    return {
      'main': main,
      'rate': rate,
      'rowSheetAt': rowSheetAt,
      'to': to,
      'uid': uid,
    };
  }
}
