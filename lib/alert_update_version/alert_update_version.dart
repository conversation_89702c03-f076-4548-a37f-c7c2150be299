// import 'package:flutter/material.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:likewallet/ImageTheme.dart';
// import 'package:likewallet/libraryman/app_local.dart';
// import 'package:likewallet/screen_util.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:open_store/open_store.dart';
//
// class AlertUpdateVersion extends StatelessWidget {
//
//
//   AlertUpdateVersion();
//
//   @override
//   Widget build(BuildContext context) {
//     return GestureDetector(
//       onTap: () {
//         Navigator.of(context).pop();
//       },
//       child: Scaffold(
//         backgroundColor: Colors.transparent,
//         body: dialogContent(context),
//       ),
//     );
//   }
// }
//
// dialogContent(BuildContext context) {
//   return Column(
//     crossAxisAlignment: CrossAxisAlignment.center,
//     mainAxisAlignment: MainAxisAlignment.center,
//     children: [
//       Row(
//         mainAxisAlignment: MainAxisAlignment.center,
//         crossAxisAlignment: CrossAxisAlignment.center,
//         children: [
//           Container(
//               width: mediaQuery(context, 'width', 989),
//               child: Stack(
//                 alignment: Alignment.bottomCenter,
//                 children: [
//                   Container(
//                     child: Image.asset(
//                       LikeWalletImage.model_alert_update,
//                       fit: BoxFit.fitWidth,
//                       // height: 1342.h,
//                       width: 989.w,
//                     ),
//                   ),
//                   Positioned(
//                     bottom: 0,
//                     child: Column(
//                       children: [
//                         Text(
//                           AppLocalizations.of(context)!
//                               .translate('alert_update_title'),
//                           style: TextStyle(
//                             fontFamily:
//                                 AppLocalizations.of(context)!.translate('font1'),
//                             fontSize: 70.h,
//                             color: const Color(0xffffffff),
//                             fontWeight: FontWeight.w300,
//                           ),
//                           textAlign: TextAlign.left,
//                         ),
//                         SizedBox(
//                           height: 69.h,
//                         ),
//                         SizedBox(
//                           width: 581.0,
//                           child: Text(
//                             AppLocalizations.of(context)
//                                 !.translate('alert_update_detail'),
//                             style: TextStyle(
//                               fontFamily: AppLocalizations.of(context)
//                                   !.translate('font1'),
//                               fontSize: 42.h,
//                               color: const Color(0x80ffffff),
//                               letterSpacing: 1.26.w,
//                               fontWeight: FontWeight.w300,
//                               height: 1.4761904761904763,
//                             ),
//                             textAlign: TextAlign.center,
//                           ),
//                         ),
//                         SizedBox(
//                           height: 117.h,
//                         ),
//                         InkWell(
//                           onTap: () => _launchURL(),
//                           child: Container(
//                             padding: EdgeInsets.symmetric(horizontal: 20.w),
//                             width: 878.w,
//                             height: 131.h,
//                             alignment: Alignment.center,
//                             child: Stack(
//                               alignment: Alignment.center,
//                               children: [
//                                 SvgPicture.string(
//                                   '<svg viewBox="101.0 1497.0 878.0 131.0" ><defs><linearGradient id="gradient" x1="1.0" y1="0.5" x2="0.0" y2="0.5"><stop offset="0.0" stop-color="#ff5b67fd"  /><stop offset="0.315271" stop-color="#ff5b67fd"  /><stop offset="1.0" stop-color="#ff3948fd"  /></linearGradient></defs><path transform="translate(101.0, 1497.0)" d="M 16 0 L 862 0 C 870.8365478515625 0 878 7.163443565368652 878 16 L 878 115 C 878 123.836555480957 870.8365478515625 131 862 131 L 16 131 C 7.163443565368652 131 0 123.836555480957 0 115 L 0 16 C 0 7.163443565368652 7.163443565368652 0 16 0 Z" fill="url(#gradient)" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
//                                   allowDrawingOutsideViewBox: true,
//                                   width: 878.w,
//                                   fit: BoxFit.fitWidth,
//                                 ),
//                                 Text(
//                                   AppLocalizations.of(context)
//                                       !.translate('alert_update_button'),
//                                   style: TextStyle(
//                                     fontFamily: AppLocalizations.of(context)
//                                         !.translate('font1'),
//                                     fontSize: 46.h,
//                                     color: const Color(0xffffffff),
//                                     letterSpacing: 1.38,
//                                   ),
//                                   textAlign: TextAlign.center,
//                                 ),
//                               ],
//                             ),
//                           ),
//                         ),
//                         SizedBox(
//                           height: 60.h,
//                         )
//                       ],
//                     ),
//                   ),
//                 ],
//               )),
//         ],
//       )
//     ],
//   );
// }
//
// _launchURL() async {
//   OpenStore.instance.open(
//     appStoreId: '1492241404', // AppStore id of your app
//     androidAppBundleId: 'likewallet.likewallet', // Android app bundle package name
//   );
// }
