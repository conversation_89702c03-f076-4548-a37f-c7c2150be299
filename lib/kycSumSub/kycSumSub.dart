// // import 'dart:convert';
// // import 'dart:io';
// //
// // import 'package:flutter/material.dart';
// // import 'package:fluttertoast/fluttertoast.dart';
// // import 'package:flutter_riverpod/flutter_riverpod.dart' as riverpod;
// // import 'package:likewallet/ImageTheme.dart';
// // import 'package:likewallet/Theme.dart';
// // import 'package:likewallet/animationPage.dart';
// // import 'package:likewallet/close_maintenance.dart';
// // import 'package:likewallet/device_utils.dart';
// // import 'package:likewallet/libraryman/app_local.dart';
// // import 'package:likewallet/main.dart';
// // import 'package:likewallet/middleware/callHttp.dart';
// // import 'package:likewallet/middleware/check_maintenance/check_maintenance.dart';
// // import 'package:likewallet/middleware/getLanguage.dart';
// // import 'package:likewallet/model/pageStatus.dart';
// // import 'package:likewallet/model/sumSubToken.dart';
// // import 'package:likewallet/routes.dart';
// //
// // import 'package:likewallet/animationPage.dart';
// // import 'package:likewallet/libraryman/app_local.dart';
// //
// // import 'package:likewallet/screen_util.dart';
// // import 'package:likewallet/screen/takephoto.dart';
// // import 'package:likewallet/app_config.dart';
// // import 'package:likewallet/tabslide/logout.dart';
// // import 'package:http/http.dart' as http;
// // import 'package:shared_preferences/shared_preferences.dart';
// // import 'package:camera/camera.dart';
// // import 'package:likewallet/libraryman/auth.dart';
// // import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
// // import 'package:likewallet/libraryman/custom_loading.dart';
// //
// // class KYCSumSub extends StatefulWidget {
// //   _KYCSumSub createState() => new _KYCSumSub();
// // }
// //
// // class _KYCSumSub extends State<KYCSumSub> {
// //   BaseAuth auth;
// //   CameraDescription firstCamera;
// //   CheckAbout checkAbout;
// //   OnLanguage language;
// //   CallHttp callHttp;
// //
// //   @override
// //   void initState() {
// //     super.initState();
// //     if (!mounted) return;
// //     checkAbout = OnCheckAbout();
// //     language = CallLanguage();
// //     callHttp = OnCallHttp();
// //     checkFirst();
// //   }
// //
// //   void showColoredToast(msg) {
// //     Fluttertoast.showToast(
// //         msg: msg,
// //         toastLength: Toast.LENGTH_LONG,
// //         backgroundColor: Colors.red,
// //         textColor: Colors.white);
// //   }
// //
// //   @override
// //   dispose() {
// //     super.dispose();
// //   }
// //
// //   checkFirst() async {
// //     PageMaintenance statusPage = await checkAbout.checkTierPermission(
// //         tierLevel: context.read(tierLevel).state, page: 'kyc');
// //     if (statusPage.status == 'active') {
// //       //เริ่มทำงาน
// //       print('active');
// //       setInitState();
// //     } else {
// //       //ปิด maintenance
// //       print('inactive');
// //       final lang = await language.getLanguage();
// //       final title = await checkAbout.selectLanguage(
// //           language: lang, detail: statusPage.title);
// //       final detail = await checkAbout.selectLanguage(
// //           language: lang, detail: statusPage.detail);
// //       final detailTime = await checkAbout.selectLanguage(
// //           language: lang, detail: statusPage.detail_time);
// //       Navigator.pushReplacement(
// //         context,
// //         MaterialPageRoute(
// //             builder: (context) => CloseMaintenance(
// //                   title: title,
// //                   detail: detail,
// //                   detailTime: detailTime,
// //                   url: statusPage.url,
// //                 )),
// //       );
// //     }
// //   }
// //
// //   setInitState() async {
// //     final response = await callHttp.callApiHTTP(
// //         url: env.apiSumSub + "/create_token",
// //         jsonMap: {"phone": "+66970020318"});
// //     SumSubToken jsonResponse = response;
// //     final onTokenExpiration = () async {
// //       // call your backend to fetch a new access token (this is just an example)
// //       return Future<String>.delayed(
// //           Duration(seconds: 2), () => "new_access_token");
// //     };
// //     // final SNSStatusChangedHandler onStatusChanged = (SNSMobileSDKStatus newStatus, SNSMobileSDKStatus prevStatus) {
// //     //   print("The SDK status was changed: $prevStatus -> $newStatus");
// //     // };
// //     // print(jsonResponse.meassage);
// //     // final snsMobileSDK = SNSMobileSDK.builder("https://test-api.sumsub.com", "testm")
// //     //     .withAccessToken(response.token, onTokenExpiration)
// //     //     .withHandlers(onStatusChanged: onStatusChanged)
// //     //     .withDebug(true)
// //     //     .withSupportEmail("<EMAIL>")
// //     //     .withLocale(Locale("en")) // Optional, for cases when you need to override system locale
// //     //     .build();
// //     //
// //     // final SNSMobileSDKResult result = await snsMobileSDK.launch();
// //     //
// //     // print("Completed with result: $result");
// //   }
// //
// //   Widget build(BuildContext context) {
// //     return new WillPopScope(
// //       onWillPop: () async => false,
// //       child: ModalProgressHUD(
// //         opacity: 0.1,
// //         inAsyncCall: false,
// //         child: GestureDetector(
// //           onTap: () {
// //             DeviceUtils.hideKeyboard(context);
// //           },
// //           child: Scaffold(
// //             backgroundColor: LikeWalletAppTheme.bule2,
// //             body: SingleChildScrollView(
// //               child: Container(
// //                   width: MediaQuery.of(context).size.width,
// //                   height: MediaQuery.of(context).size.height,
// //                   child: KYCForm()),
// //             ),
// //           ),
// //         ),
// //       ),
// //     );
// //   }
// //
// //   Widget KYCForm() {
// //     return new Stack(
// //       alignment: Alignment.center,
// //       children: <Widget>[
// //         //ปุ่ม back
// //         Positioned(
// //             top: MediaQuery.of(context).size.height *
// //                 Screen_util("height", 139.33),
// //             left: MediaQuery.of(context).size.width * Screen_util("width", 75),
// //             child: _backButton()),
// //       ],
// //     );
// //   }
// //
// //   Widget _backButton() {
// //     return InkWell(
// //       child: Container(
// //         padding: EdgeInsets.all(
// //           mediaQuery(context, 'height', 20.47),
// //         ),
// //         height: mediaQuery(context, 'height', 84.47),
// //         width: mediaQuery(context, 'height', 84.47),
// //         child: Image.asset(
// //           LikeWalletImage.icon_back_button,
// //           height: mediaQuery(context, "height", 44.47),
// //           color: LikeWalletAppTheme.gray,
// //         ),
// //       ),
// //       onTap: () => {
// //         setState(() {
// //           Navigator.pop(context);
// //         })
// //       },
// //     );
// //   }
// // }
//
import 'dart:async';
import 'dart:convert';
import 'dart:ui';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_idensic_mobile_sdk_plugin/flutter_idensic_mobile_sdk_plugin.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/alert_update_version/alert_update_version.dart';
import 'package:likewallet/bank/completeTX.dart';
import 'package:likewallet/kycSumSub/kyc_black_list_page.dart';
import 'package:likewallet/kycSumSub/kyc_success_page.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:likewallet/middleware/callFireStore.dart';
import 'package:likewallet/middleware/callHttp.dart';
import 'package:likewallet/middleware/check_maintenance/check_maintenance.dart';
import 'package:likewallet/middleware/getLanguage.dart';
import 'package:likewallet/model/kyc/kyc_text.dart';
import 'package:likewallet/model/sumSubToken.dart';
import 'package:likewallet/app_config.dart';
import 'package:likewallet/screen/home.dart';
import 'package:likewallet/screen_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:shared_preferences/shared_preferences.dart';

// https://kyc.likewalletapp.com/user/create_token
Future launchSNSMobileSDK({String? token}) async {
  final String apiUrl = "https://api.sumsub.com"; // https://api.sumsub.com
  final String flowName =
      "msdk-basic-kyc"; // or set up your own with the dashboard
  final String accessToken = token!; // generate one on the backend

  final onTokenExpiration = () async {
    // call your backend to fetch a new access token (this is just an example)
    return Future<String>.delayed(
        Duration(seconds: 2), () => "new_access_token");
  };

  final SNSStatusChangedHandler onStatusChanged =
      (SNSMobileSDKStatus newStatus, SNSMobileSDKStatus prevStatus) {
    print("The SDK status was changed: $prevStatus -> $newStatus");
  };

  // final snsMobileSDK = SNSMobileSDK.builder(apiUrl, flowName)
  //     .withAccessToken(accessToken, onTokenExpiration)
  //     .withHandlers(onStatusChanged: onStatusChanged)
  //     .withDebug(true)
  //     .withSupportEmail("<EMAIL>")
  //     .withLocale(Locale(
  //         "th")) // Optional, for cases when you need to override system locale
  //     .build();

  final snsMobileSDK = SNSMobileSDK.init(accessToken, onTokenExpiration)
      .withHandlers(
    // optional handlers
      onStatusChanged: onStatusChanged)
      .withDebug(true) // set debug mode if required
      .withLocale(Locale(
      "th")) // optional, for cases when you need to override the system locale
      .build();

  final SNSMobileSDKResult result = await snsMobileSDK.launch();

  print("Completed with result: $result");
}

class KYCSumSub extends StatefulWidget {
  KYCSumSub({this.uid, this.phone});
  final String? uid;
  final String? phone;

  @override
  _KYCSumSub createState() => _KYCSumSub();
}

class _KYCSumSub extends State<KYCSumSub> {
  var setDismissTimer = false;
  bool _loading = false;
  String statusKYC = '';
  late KycText text;
  late CallHttp http;
  late OnCallFireStore fireStore;
  late OnLanguage language;
  late SharedPreferences sharedPreferences;
  late BaseAuth auth;
  late CheckAbout checkAbout;
  @override
  void initState() {
    super.initState();
    http = OnCallHttp();
    fireStore = CallFireStore();
    language = CallLanguage();
    auth = Auth();
    checkAbout = OnCheckAbout();
    checkStatus();
  }

  void checkStatus() async {
    sharedPreferences = await SharedPreferences.getInstance();
    User? user = await auth.getCurrentUser();
    final blacklist = await checkAbout.checkBlackList(
        type: 'blackList', phone: user!.phoneNumber.toString());
    final blacklistLDX = await checkAbout.checkBlackList(
        type: 'blackList_LDX', phone: user.phoneNumber.toString());
    final blacklistFIN = await checkAbout.checkBlackList(
        type: 'blackList_FIN', phone: user.phoneNumber.toString());

    if (blacklist == 'blackList') {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
            builder: (context) => BlackListPage(
                  page: 'kyc',
                )),
      );
    } else if (blacklistLDX == 'blackList_LDX') {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
            builder: (context) => BlackListPage(
                  page: 'kyc',
                )),
      );
    } else if (blacklistFIN == 'blackList_FIN') {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
            builder: (context) => BlackListPage(
                  page: 'kyc',
                )),
      );
    } else {
      print(user);
      await FirebaseFirestore.instance
          .collection('kyc')
          .doc(user.uid)
          .get()
          .then((value) {
        if (value.exists) {
          print(value.data()!["active"]);
          if (value.data()!["active"] == 2) {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(builder: (context) => KYCSuccessPage()),
            );
          } else {
            print('1');
            callSumSub();
          }
        } else {
          print('2');
          callSumSub();
        }
      }).catchError((error) {
        print('3');
        callSumSub();
      });
    }
  }

  Future<KycText?> getTextKYC({String? page}) async {
    var lang = await language.getLanguage();
    var res = await fireStore.getDataFireStoreCol2(
        collection1: 'allText',
        doc1: lang.toString(),
        collection2: 'kyc',
        doc2: page.toString());
    setState(() {
      text = KycText.fromJson(res);
    });
  }

  callSumSub() async {
    print(widget.uid.toString() + widget.phone.toString());
    setState(() => _loading = true);
    SumSubToken res = await http.getSumSubAccessToken(
        url: 'https://kyc.likewalletapp.com/user/create_token',
        jsonMap: {"uid": widget.uid, "phone": widget.phone});
    // print(res);
    // print(res.token);
    if (res.token == null) {
      Navigator.of(context).pop();
      // showDialog(
      //     context: context, builder: (BuildContext context) => showAlert());
    } else {
      launchSNSMobileSDK(token: res.token.toString())
          .whenComplete(() => Navigator.of(context).pop());
    }
  }

  @override
  Widget build(BuildContext context) {
    return ModalProgressHUD(
      inAsyncCall: _loading,
      opacity: 0.1,
      progressIndicator: Center(
        child: SpinKitFadingCircle(
          color: LikeWalletAppTheme.bule1,
          size: 200.h,
        ),
      ),
      child: Scaffold(
          backgroundColor: LikeWalletAppTheme.bule2,
          body: Center(
            child: SpinKitFadingCircle(
              color: LikeWalletAppTheme.bule1,
              size: 200.h,
            ),
          )),
    );
  }

  showAlert() {
    Dialog simpleDialog = Dialog(
      elevation: 500,
      backgroundColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(30.0),
      ),
      child: Container(
        // height: mediaQuery(context, 'height', 554.63),
        width: mediaQuery(context, 'width', 929.64),
        color: Colors.transparent,
        margin: EdgeInsets.only(bottom: mediaQuery(context, 'height', 600)),
        child: new ClipRect(
          child: new BackdropFilter(
            filter: new ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
            child: Container(
              decoration: BoxDecoration(
                color: LikeWalletAppTheme.white.withOpacity(0.6),
                borderRadius: BorderRadius.all(Radius.circular(20.0)),
              ),
              height: mediaQuery(context, 'height', 554.63),
              width: mediaQuery(context, 'width', 929.64),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  SizedBox(height: 30.h),
                  Text(
                    'เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง',
                    style: TextStyle(
                      letterSpacing: 0.3,
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      color: LikeWalletAppTheme.black.withOpacity(1),
                      fontSize: mediaQuery(context, "height", 49),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Expanded(child: Container()),
                  Container(
                    margin: EdgeInsets.only(
                        bottom: mediaQuery(context, 'height', 30)),
                    width: mediaQuery(context, 'width', 777.62),
                    child: Text(
                      'กรุณาลองใหม่อีกครั้ง',
                      textAlign: TextAlign.center,
                      maxLines: 4,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        letterSpacing: 0.3,
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                        color: LikeWalletAppTheme.black.withOpacity(1),
                        height: mediaQuery(context, "height", 3.5),
                        fontSize: mediaQuery(context, "height", 42),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Expanded(child: Container()),
                  Container(
                      width: mediaQuery(context, 'width', 777.62),
                      decoration: BoxDecoration(
                        border: Border(
                          top: BorderSide(
                            //                   <--- left side
                            color: LikeWalletAppTheme.black.withOpacity(0.4),
                            width: mediaQuery(context, 'width', 1),
                          ),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: <Widget>[
                          GestureDetector(
                            onTap: () {
                              Navigator.of(context).pop();
                            },
                            child: Container(
                              alignment: Alignment.center,
                              height: mediaQuery(context, 'height', 127.66),
                              width: mediaQuery(context, 'width', 777.62) / 2,
                              child: Text(
                                AppLocalizations.of(context)!
                                    .translate('messages_ok'),
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  letterSpacing: 0.3,
                                  fontFamily: AppLocalizations.of(context)!
                                      .translate('font1'),
                                  color:
                                      LikeWalletAppTheme.bule1_7.withOpacity(1),
                                  fontSize: mediaQuery(context, "height", 52),
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                        ],
                      )),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
