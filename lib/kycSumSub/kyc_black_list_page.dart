import 'package:clippy_flutter/buttcheek.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/chat_in_app/index_chat.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/change_phone/NavigationBar.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:likewallet/main.dart';
import 'package:likewallet/routes.dart';
import 'package:likewallet/screen/ContractUS.dart';
import 'package:likewallet/screen/index.dart';
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/support/chat.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class BlackListPage extends StatefulWidget {
  BlackListPage({required this.page});

  final String page;

  _BlackListPage createState() => new _BlackListPage();
}

class _BlackListPage extends State<BlackListPage> {
  late BaseAuth auth;

  @override
  void initState() {
    super.initState();
    auth = new Auth();
  }

  Widget build(BuildContext context) {
    return new Scaffold(
        body: Stack(
      alignment: Alignment.center,
      children: [
        _bg(),
        widget.page == 'kyc'
            ? Container()
            : Positioned(
                top: mediaQuery(context, 'height', 130),
                left: mediaQuery(context, 'width', 75),
                child: InkWell(
                  onTap: () {
                    auth.signOut();
                    AppRoutes.makeFirst(context, IndexLike());
                  },
                  child: Container(
                    height: 70.h,
                    width: 70.h,
                    padding: EdgeInsets.symmetric(vertical: 10.h),
                    child: Image.asset(
                      LikeWalletImage.icon_back_button,
                      height: 45.h,
                    ),
                  ),
                ),
              ),
        Positioned(
          top: mediaQuery(context, 'height', 375),
          left: mediaQuery(context, 'width', 337),
          child: Container(
            child: Image.asset(
              LikeWalletImage.kycEX,
              height: mediaQuery(context, 'height', 243.88),
            ),
          ),
        ),

        Positioned(
            top: mediaQuery(context, 'height', 469),
            right: mediaQuery(context, 'width', 337.6),
            child: Image.asset(
              LikeWalletImage.icon_setting,
              height: mediaQuery(context, 'height', 233),
            )),
        Positioned(
          top: mediaQuery(context, 'height', 859),
          width: mediaQuery(context, 'width', 825),
          child: Text(
            AppLocalizations.of(context)!.translate('blacklist_page_detail'),
            textAlign: TextAlign.center,
            style: TextStyle(
                color: LikeWalletAppTheme.bule1,
                fontSize: mediaQuery(context, 'height', 42),
                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                letterSpacing: 0.3,
                fontWeight: FontWeight.w100),
          ),
        ),
        Positioned(
          top: mediaQuery(context, 'height', 1059),
          width: mediaQuery(context, 'width', 825),
          child: InkWell(
            onTap: () => {
              /// ย้ายไปไลน์ โอ๋เอ๋
              // Navigator.push(
              //   context,
              //   MaterialPageRoute(builder: (context) => IndexChatPage()),
              // )
              Navigator.push(context,
                  MaterialPageRoute(builder: (context) => ContactUSPage()))
            },
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 20.w),
              width: 878.w,
              height: 131.h,
              alignment: Alignment.center,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  SvgPicture.string(
                    '<svg viewBox="101.0 1497.0 878.0 131.0" ><defs><linearGradient id="gradient" x1="1.0" y1="0.5" x2="0.0" y2="0.5"><stop offset="0.0" stop-color="#ff5b67fd"  /><stop offset="0.315271" stop-color="#ff5b67fd"  /><stop offset="1.0" stop-color="#ff3948fd"  /></linearGradient></defs><path transform="translate(101.0, 1497.0)" d="M 16 0 L 862 0 C 870.8365478515625 0 878 7.163443565368652 878 16 L 878 115 C 878 123.836555480957 870.8365478515625 131 862 131 L 16 131 C 7.163443565368652 131 0 123.836555480957 0 115 L 0 16 C 0 7.163443565368652 7.163443565368652 0 16 0 Z" fill="url(#gradient)" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                    allowDrawingOutsideViewBox: true,
                    width: 878.w,
                    fit: BoxFit.fitWidth,
                  ),
                  Text(
                    AppLocalizations.of(context)!.translate('support_text'),
                    style: TextStyle(
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      fontSize: 46.h,
                      color: const Color(0xffffffff),
                      letterSpacing: 1.38,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
        widget.page == 'kyc'
            ? Positioned(
                top: mediaQuery(context, 'height', 1259),
                width: mediaQuery(context, 'width', 825),
                child: InkWell(
                  onTap: () => {Navigator.of(context).pop()},
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 20.w),
                    width: 878.w,
                    height: 131.h,
                    alignment: Alignment.center,
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        SvgPicture.string(
                          '<svg viewBox="101.0 1497.0 878.0 131.0" ><defs><linearGradient id="gradient" x1="1.0" y1="0.5" x2="0.0" y2="0.5"><stop offset="0.0" stop-color="#ff5b67fd"  /><stop offset="0.315271" stop-color="#ff5b67fd"  /><stop offset="1.0" stop-color="#ff3948fd"  /></linearGradient></defs><path transform="translate(101.0, 1497.0)" d="M 16 0 L 862 0 C 870.8365478515625 0 878 7.163443565368652 878 16 L 878 115 C 878 123.836555480957 870.8365478515625 131 862 131 L 16 131 C 7.163443565368652 131 0 123.836555480957 0 115 L 0 16 C 0 7.163443565368652 7.163443565368652 0 16 0 Z" fill="url(#gradient)" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                          allowDrawingOutsideViewBox: true,
                          width: 878.w,
                          color: Colors.grey,
                          fit: BoxFit.fitWidth,
                        ),
                        Text(
                          AppLocalizations.of(context)!
                              .translate('borrow_cancel'),
                          style: TextStyle(
                            fontFamily: AppLocalizations.of(context)!
                                .translate('font1'),
                            fontSize: 46.h,
                            color: const Color(0xffffffff),
                            letterSpacing: 1.38,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
              )
            : Container()
        // Positioned(
        //     top: mediaQuery(context, 'height', 135),
        //     left: mediaQuery(context, 'width', 0),
        //     child: backButton(context, Colors.black)),
      ],
    ));
  }

  Widget _bg() {
    return Column(
      children: [
        Container(
          alignment: Alignment.bottomLeft,
          height: mediaQuery(context, 'height', 619),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment(1.0, -0.95),
              end: Alignment(-1.0, 0.9),
              colors: [const Color(0xff30f3ca), const Color(0xff24c6e4)],
              stops: [0.0, 1.0],
            ),
          ),
        ),
        Container(
          height: MediaQuery.of(context).size.height -
              mediaQuery(context, 'height', 619),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment(0.0, -1.0),
              end: Alignment(0.0, 1.0),
              colors: [const Color(0xff25263c), const Color(0xff141322)],
              stops: [0.0, 1.0],
            ),
            boxShadow: [
              BoxShadow(
                color: const Color(0xcc00887f),
                offset: Offset(0, -mediaQuery(context, 'height', 12)),
                blurRadius: mediaQuery(context, 'height', 35),
              ),
            ],
          ),
        ),

        // Container(
        //   height: MediaQuery.of(context).size.height -
        //       mediaQuery(context, 'height', 619),
        //   color: LikeWalletAppTheme.bule2,
        // ),
      ],
    );
  }
}
