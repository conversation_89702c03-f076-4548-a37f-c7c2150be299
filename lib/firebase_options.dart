// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyC-p2YK544BFMY2AwJVAdBI_iKpTphbuPc',
    appId: '1:126028778629:web:0908fe760ede2180eaedec',
    messagingSenderId: '126028778629',
    projectId: 'newlikewallet',
    authDomain: 'newlikewallet.firebaseapp.com',
    databaseURL: 'https://newlikewallet.firebaseio.com',
    storageBucket: 'newlikewallet.appspot.com',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDo3bORQN3qzFDBm0IvjrU__LC_Ub5f2Bg',
    appId: '1:126028778629:android:be11f9d589d464cceaedec',
    messagingSenderId: '126028778629',
    projectId: 'newlikewallet',
    databaseURL: 'https://newlikewallet.firebaseio.com',
    storageBucket: 'newlikewallet.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCBJnbXCtuVMuSupZZJblWyPwIemW7NfUw',
    appId: '1:126028778629:ios:d1288a6e8e496852eaedec',
    messagingSenderId: '126028778629',
    projectId: 'newlikewallet',
    databaseURL: 'https://newlikewallet.firebaseio.com',
    storageBucket: 'newlikewallet.appspot.com',
    androidClientId: '126028778629-11j5l2tsnohoqt98cv4utru42kg5ts7d.apps.googleusercontent.com',
    iosClientId: '126028778629-hndqvgvf7f1l52p56t9offbubp38p9a7.apps.googleusercontent.com',
    iosBundleId: 'likewallet.likewallet',
  );

}