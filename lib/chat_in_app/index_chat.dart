// import 'dart:convert';
//
// import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:flutter/material.dart';
// import 'package:firebase_analytics/firebase_analytics.dart';
// import 'package:firebase_analytics/observer.dart';
// import 'package:likewallet/LDX/library.dart';
// import 'package:likewallet/libraryman/auth.dart';
// import 'package:load/load.dart';
// import 'package:likewallet/app_config.dart';
// import 'package:likewallet/chat_in_app/channel_chat.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:stream_chat_flutter/stream_chat_flutter.dart';
//
// class IndexChatPage extends StatefulWidget {
//   @override
//   _IndexChatPageState createState() => _IndexChatPageState();
// }
//
// class _IndexChatPageState extends State<IndexChatPage> {
//   static FirebaseAnalytics analytics = FirebaseAnalytics.instance;
//   static FirebaseAnalyticsObserver observer =
//       FirebaseAnalyticsObserver(analytics: analytics);
//   late BaseAuth auth;
//   final client = StreamChatClient(
//     "45duwcxxp4vy",
//     logLevel: Level.INFO,
//     connectTimeout: Duration(milliseconds: 6000),
//     receiveTimeout: Duration(milliseconds: 6000),
//   );
//
//   bool isLoading = true;
//   late Channel channel;
//   String id = "";
//   String name = "";
//   String token = "";
//
//   @override
//   void initState() {
//     super.initState();
//     print("getPhone");
//     auth = Auth();
//     getDataUser();
//     analytics.setCurrentScreen(screenName: "IndexChat");
//   }
//
//   getDataUser() async {
//     try {
//       final user = await auth.getCurrentUser();
//       id = user!.uid;
//       final ds = await FirebaseFirestore.instance
//           .collection('addressDNS')
//           .doc(id)
//           .get();
//       name = ds.data()!['name'];
//       getToken(id, name);
//     } catch (e) {
//       print(e);
//     }
//   }
//
//   getToken(String id, String name) async {
//     print(id);
//     print(name);
//     try {
//       final user = User(id: id, extraData: {"name": name});
//       Map map = {
//         "username": id,
//       };
//       final response = await apiRequest(
//           'https://new.likepoint.io/generateTokenChatInApp', map);
//       // print(response);
//       var jsonResponse = json.decode(response);
//       // print({'username': id});
//       // print({'name': name});s
//       //
//       // final response = await AppApi.post(AppUrl.generatorTokenStreamChat,
//       //     {'username': id, "menu": "genTokenStreamChat"});
//       // print(response);
//       int status = int.parse(jsonResponse['status'].toString());
//       if (status == 200) {
//         setState(() {
//           token = jsonResponse['result']['token'];
//           // print(token);
//         });
//         await client.connectUser(user, token);
//         connectChannel();
//       } else {
//         alerterror(context, "ERROR : can't get token");
//       }
//       // setState(() {
//       //
//       // });
//     } catch (e) {
//       print(e);
//       alerterror(context, "ERROR : getToken in IndexChatPage");
//       // AppService.sendError(e, 'ERROR : getToken in IndexChatPage');
//     }
//   }
//
//   connectChannel() async {
//     try {
//       setState(() {
//         channel = client.channel('messaging', id: "likewallet_$id", extraData: {
//           "members": ["Admin_LikeWallet", id],
//           name: name
//         });
//         isLoading = false;
//       });
//       await channel.watch();
//     } catch (e) {
//       alerterror(context, "ERROR : connectChannel in IndexChatPage");
//     }
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     if (isLoading == true) {
//       return Scaffold(
//         backgroundColor: const Color(0xff1f1c2f),
//         body: Center(
//           child: CircularProgressIndicator(),
//         ),
//       );
//     }
//
//     return Scaffold(
//       appBar: AppBar(
//         backgroundColor: const Color(0xff1f1c2f),
//         title: Text("แชทกับแอดมิน"),
//       ),
//       body: StreamChat(
//         client: client,
//         child: StreamChannel(
//           channel: channel,
//           child: MaterialApp(
//             debugShowCheckedModeBanner: false,
//             theme: ThemeData(
//               fontFamily: 'Prompt',
//               scaffoldBackgroundColor: Colors.white70,
//               primaryColor: Color(0xFFFF8500),
//               accentColor: Colors.amber,
//             ),
//             home: ChannelPage(),
//           ),
//         ),
//       ),
//     );
//   }
// }
//
// // class ChannelPage extends StatelessWidget {
// //   /// Creates the page that shows the list of messages
// //   const ChannelPage({
// //     Key? key,
// //   }) : super(key: key);
// //
// //   final Location location = Location();
// //
// //   Future<bool> setupLocation() async {
// //     if (location == null) {
// //       location = Location();
// //     }
// //     var _serviceEnabled = await location!.serviceEnabled();
// //     if (!_serviceEnabled) {
// //       _serviceEnabled = await location!.requestService();
// //       if (!_serviceEnabled) {
// //         return false;
// //       }
// //     }
// //
// //     var _permissionGranted = await location!.hasPermission();
// //     if (_permissionGranted == PermissionStatus.denied) {
// //       _permissionGranted = await location!.requestPermission();
// //       if (_permissionGranted != PermissionStatus.granted) {
// //         return false;
// //       }
// //     }
// //     return true;
// //   }
// //
// //   @override
// //   Widget build(BuildContext context) {
// //     return Scaffold(
// //       body: Column(
// //         children: <Widget>[
// //           Expanded(
// //             child: MessageListView(),
// //           ),
// //           MessageInput(
// //             actionsLocation: ActionsLocation.right,
// //             actions: [
// //               InkWell(
// //                 child: Icon(
// //                   Icons.location_history,
// //                   size: 20.0,
// //                   color: Colors.grey,
// //                 ),
// //                 onTap: () {
// //                   print("AAAA");
// //                   // Do something here
// //                 },
// //               ),
// //               // IconButton(
// //               //   icon: Icon(Icons.location_history),
// //               //   onPressed: ()  {
// //               //     print("AAAA");
// //               //   },
// //               // ),
// //             ],
// //           ),
// //         ],
// //       ),
// //     );
// //   }
// // }
