// import 'dart:async';
//
// import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:geolocator/geolocator.dart';
// import 'package:likewallet/LDX/library.dart';
// import 'package:likewallet/libraryman/auth.dart';
// import 'package:likewallet/middleware/api_http.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:stream_chat_flutter/stream_chat_flutter.dart';
//
// class ChannelPage extends StatefulWidget {
//   @override
//   _ChannelPageState createState() => _ChannelPageState();
// }
//
// class _ChannelPageState extends State<ChannelPage> {
//   GlobalKey<MessageInputState> _messageInputKey = GlobalKey(); // ne
//   late BaseAuth auth;
//
//   getCurrentLocation() async {
//     try {
//       try {
//         print(Geolocator.checkPermission());
//         await Geolocator.requestPermission();
//
//         Position position = await Geolocator.getCurrentPosition(
//             desiredAccuracy: LocationAccuracy.high);
//         print(position.toString());
//
//         _messageInputKey.currentState?.addAttachment(
//           Attachment(
//             type: 'location',
//             uploadState: UploadState.success(),
//             extraData: {
//               'lat': position.latitude,
//               'long': position.longitude,
//             },
//           ),
//         );
//         // setState(() {
//         //   currentPosition = LatLng(position.latitude, position.longitude);
//         //   isLoading = false;
//         // });
//       } on PlatformException catch (e) {
//         if (e.code == 'PERMISSION_DENIED') {
//           alerterror(
//               context, "ERROR : Permission denied to getCurrentLocation");
//           print('Permission denied');
//         } else {
//           alerterror(
//               context, "ERROR : Permission denied to getCurrentLocation");
//         }
//       }
//     } catch (e) {
//       alerterror(context, "ERROR : Permission denied to getCurrentLocation");
//     }
//   }
//
//   // final Location location = Location();
//   // List<dynamic> _listEvent = [];
//   //
//   // static FirebaseAnalytics analytics = FirebaseAnalytics.instance;
//   // static FirebaseAnalyticsObserver observer =
//   // FirebaseAnalyticsObserver(analytics: analytics);
//
//   @override
//   void initState() {
//     super.initState();
//     // analytics.setCurrentScreen(screenName: "ListEvent");
//     // getNews();
//   }
//
//   Widget _buildLocationMessage(
//     BuildContext context,
//     Message details,
//     List<Attachment> _,
//   ) {
//     final lat = details.attachments.first.extraData['lat'] as double;
//     final long = details.attachments.first.extraData['long'] as double;
//     return wrapAttachmentWidget(
//       context,
//       MapImageThumbnail(
//         lat: lat,
//         long: long,
//       ),
//       RoundedRectangleBorder(),
//       true,
//     );
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       resizeToAvoidBottomInset: false,
//       body: Column(
//         children: <Widget>[
//           Expanded(
//             child: MessageListView(
//               messageBuilder: (context, details, messages, defaultMessage) {
//                 return defaultMessage.copyWith(
//                   showEditMessage: false,
//                   showThreadReplyMessage: true,
//                   showReplyMessage: true,
//                   onReplyTap: (message) {
//                     print(message);
//                   },
//                   customAttachmentBuilders: {'location': _buildLocationMessage},
//                 );
//               },
//             ),
//           ),
//           Container(
//             child: MessageInput(
//               key: _messageInputKey,
//               actionsLocation: ActionsLocation.right,
//               attachmentThumbnailBuilders: {
//                 'location': (context, attachment) {
//                   return MapImageThumbnail(
//                     lat: attachment.extraData['lat'] as double,
//                     long: attachment.extraData['long'] as double,
//                   );
//                 },
//               },
//               onMessageSent: (message) async {
//                 // var room = channelId?.substring(5);
//                 // var name = await AppService.getPref('string', 'name');
//                 auth = Auth();
//                 final user = await auth.getCurrentUser();
//                 var id = user!.uid;
//                 final ds = await FirebaseFirestore.instance
//                     .collection('addressDNS')
//                     .doc(id)
//                     .get();
//                 var name = ds.data()!['name'];
//                 int timeBreak = 15;
//                 var now = DateTime.now();
//                 var prefs = await SharedPreferences.getInstance();
//                 if (prefs.getString('lastSend') == null) {
//                   var yesterday = now
//                       .subtract(new Duration(days: 1))
//                       .millisecondsSinceEpoch;
//                   await prefs.setString('lastSend', yesterday.toString());
//                 }
//                 var lastSend = prefs.getString('lastSend');
//                 var formatLastSend =
//                     DateTime.fromMillisecondsSinceEpoch(int.parse(lastSend!));
//                 var minuteLastSend =
//                     formatLastSend.add(new Duration(minutes: timeBreak));
//                 print(now.millisecondsSinceEpoch);
//                 print(minuteLastSend.millisecondsSinceEpoch);
//                 if (now.millisecondsSinceEpoch >
//                     minuteLastSend.millisecondsSinceEpoch) {
//                   var room = '-1001421698064';
//                   // List<dynamic> body = [
//                   //   {
//                   //     "chatid": room.toString(),
//                   //     "fromdata": {
//                   //       "message":
//                   //           "=================================\nข้อความ : ${message.text}\nจาก UID : $id  \nชื่อลูกค้า : $name\nlink : https://chat-in-app-admin.web.app/chat-like-wallet",
//                   //       "namegroup": "$id $name",
//                   //       "namecus": name,
//                   //       "linkG":
//                   //           "https://chat-in-app-admin.web.app/chat-like-wallet"
//                   //     }
//                   //   }
//                   // ];
//                   List<dynamic> body = [
//                     {
//                       "chatid": room,
//                       "fromdata": {
//                         "message": "${message.text}",
//                         "namegroup": "$id $name",
//                         "namecus": name,
//                         "linkG":
//                             "https://chat-in-app-admin.web.app/chat-like-wallet"
//                       },
//                       "type": "LIKEWALLET"
//                     }
//                   ];
//                   print("body" + body.toString());
//                   AppHttps.postTG_BCT(AppHttps.sendTelegramPKG, body);
//                   await prefs.setString(
//                       'lastSend', now.millisecondsSinceEpoch.toString());
//                 }
//               },
//               actions: [
//                 InkWell(
//                   child: Icon(
//                     Icons.location_history,
//                     size: 28,
//                     color: Colors.grey,
//                   ),
//                   onTap: () {
//                     getCurrentLocation();
//                   },
//                 ),
//                 // IconButton(
//                 //   icon: Icon(Icons.location_history),
//                 //   onPressed: ()  {
//                 //     print("AAAA");
//                 //   },
//                 // ),
//               ],
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
//
// class MapImageThumbnail extends StatelessWidget {
//   const MapImageThumbnail({
//     Key? key,
//     required this.lat,
//     required this.long,
//   }) : super(key: key);
//   final String mapsKey = "AIzaSyAmIZ39-kRoLGdQ7uqdyctMQGQgizSGyLs";
//   final double lat;
//   final double long;
//
//   String get _constructUrl => Uri(
//         scheme: 'https',
//         host: 'maps.googleapis.com',
//         port: 443,
//         path: '/maps/api/staticmap',
//         queryParameters: {
//           'center': '$lat,$long',
//           'zoom': '18',
//           'size': '700x500',
//           'maptype': 'roadmap',
//           'key': mapsKey,
//           'markers': 'color:red|$lat,$long'
//         },
//       ).toString();
//
//   @override
//   Widget build(BuildContext context) {
//     return Image.network(
//       _constructUrl,
//       height: 300.0,
//       width: 600.0,
//       fit: BoxFit.fill,
//     );
//   }
// }
