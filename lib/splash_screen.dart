import 'dart:async';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:likewallet/kycSumSub/kyc_black_list_page.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:likewallet/libraryman/maintenance.dart';
import 'package:likewallet/main.dart';
import 'package:likewallet/middleware/check_maintenance/check_maintenance.dart';
import 'package:likewallet/middleware/getLanguage.dart';
import 'package:likewallet/model/pageStatus.dart';
import 'package:likewallet/screen/index.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:likewallet/libraryman/pinProtect.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:likewallet/libraryman/applang.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as riverpod;
import 'package:provider/provider.dart' as old_provider hide ReadContext;

class SplashScreen extends StatefulWidget {
  @override
  _SplashScreenState createState() => _SplashScreenState();
}

enum AuthStatus { notSignedIn, signedIn }
enum SignMnemonic { active, inactive }
enum _sheetType { gallery, camera }

class _SplashScreenState extends State<SplashScreen> {
  AuthStatus authStatus = AuthStatus.notSignedIn;
  SignMnemonic signMnemonic = SignMnemonic.inactive;
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final List mnemonic = [];
  late SharedPreferences sharedPreferences;
  late CheckAbout checkAbout;
  late OnLanguage language;
  late BaseAuth auth;

  buildForPhone() {
    return Stack(
      children: <Widget>[
        new Positioned(
          child: new Container(
            decoration: BoxDecoration(
                image: DecorationImage(
                    image: AssetImage('assets/image/back.png'),
                    fit: BoxFit.cover)),
            width: double.infinity,
            alignment: Alignment.bottomCenter,
            child: new Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[],
            ),
          ),
        ),
      ],
    );
  }

  @override
  void initState() {
    super.initState();
    checkAbout = OnCheckAbout();
    language = CallLanguage();
    auth = new Auth();
    checkLogin();
    checkLang();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<bool> destroyApp() async {
    final storage = new FlutterSecureStorage();
    SharedPreferences pref = await SharedPreferences.getInstance();
    //save old lang
    final String? lang = pref.getString('language_code') ?? 'en';
    await storage.deleteAll();
    setState(() {
      pref.setString('language_code', lang.toString());
    });
    return true;
  }

  void checkLogin() async {
    String tier = '';
    SharedPreferences pref = await SharedPreferences.getInstance();
    // if (pref.getBool('first_run') ?? true) {
    //   print("first run : true");
    //   destroyApp().then((result) async {
    //     pref.setBool('first_run', false);
    //   });
    // }
    try {
      bool check = pref.getBool('login') ?? false;
      print(check);
      var snapshot = await auth.getCurrentUser();
      //นำเบอร์โทรไปเช็ค
      final blacklist = await checkAbout.checkBlackList(
          type: 'blackList', phone: snapshot!.phoneNumber.toString());
      final blacklistLDX = await checkAbout.checkBlackList(
          type: 'blackList_LDX', phone: snapshot.phoneNumber.toString());
      final blacklistFIN = await checkAbout.checkBlackList(
          type: 'blackList_FIN', phone: snapshot.phoneNumber.toString());

      if (blacklist == 'blackList') {
        tier = blacklist;

        context.read(tierLevel).state = tier;
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
              builder: (context) => BlackListPage(
                    page: '',
                  )),
        );
      } else if (blacklistLDX == 'blackList_LDX') {
        tier = blacklistLDX;
        context.read(tierLevel).state = tier;
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
              builder: (context) => BlackListPage(
                    page: '',
                  )),
        );
      } else if (blacklistFIN == 'blackList_FIN') {
        tier = blacklistFIN;
        context.read(tierLevel).state = tier;
        PageMaintenance statusPage = await checkAbout.checkTierPermission(
            tierLevel: tier, page: "likewallet");
        print("home :" + statusPage.status.toString());
        if (statusPage.status == 'active') {
          //เรียกฟังค์ชั่นได้เริ่มต้น
          if (check) {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(
                  builder: (context) => PinProtect(
                        redirectPage: 'HOME',
                      )),
            );
            // AppRoutes.makeFirst(
            //     context,
            //     PinProtect(
            //       redirectPage: 'HOME',
            //     ));
          } else {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(builder: (context) => IndexLike()),
            );
            // AppRoutes.makeFirst(context, IndexLike());
          }
        } else {
          print('เกิดข้อผิดพลาด');
          final lang = await language.getLanguage();
          final detail = await checkAbout.selectLanguage(
              language: lang, detail: statusPage.detail);
          final title = await checkAbout.selectLanguage(
              language: lang, detail: statusPage.title);
          final detailTime = await checkAbout.selectLanguage(
              language: lang, detail: statusPage.detail_time);
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
                builder: (context) => Maintenance(
                      title: title,
                      detail: detail,
                      detailTime: detailTime,
                      url: statusPage.url,
                    )),
          );
        }
      } else {
        tier = await checkAbout.checkTier(
            email: snapshot.email,
            phone: snapshot.phoneNumber,
            list: 'whitelist',
            context: context);
        print("tierLevel $tier");
        //set tierLevel
        if (tier.isNotEmpty) {
          context.read(tierLevel).state = tier;
          if (tier == 'tier1') {
            _firebaseMessaging.subscribeToTopic('notifyTier1');
          } else if (tier == 'normal') {
            _firebaseMessaging.subscribeToTopic('notifyNormal');
          }
        }

        PageMaintenance statusPage = await checkAbout.checkTierPermission(
            tierLevel: tier, page: "likewallet");
        print("home :" + statusPage.status.toString());
        if (statusPage.status == 'active') {
          //เรียกฟังค์ชั่นได้เริ่มต้น
          if (check) {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(
                  builder: (context) => PinProtect(
                        redirectPage: 'HOME',
                      )),
            );
            // AppRoutes.makeFirst(
            //     context,
            //     PinProtect(
            //       redirectPage: 'HOME',
            //     ));
          } else {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(builder: (context) => IndexLike()),
            );
            // AppRoutes.makeFirst(context, IndexLike());
          }
        } else {
          print('เกิดข้อผิดพลาด');
          final lang = await language.getLanguage();
          final detail = await checkAbout.selectLanguage(
              language: lang, detail: statusPage.detail);
          final title = await checkAbout.selectLanguage(
              language: lang, detail: statusPage.title);
          final detailTime = await checkAbout.selectLanguage(
              language: lang, detail: statusPage.detail_time);
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
                builder: (context) => Maintenance(
                      title: title,
                      detail: detail,
                      detailTime: detailTime,
                      url: statusPage.url,
                    )),
          );
        }
        // FirebaseFirestore.instance
        //     .collection('tierController')
        //     .doc('controller')
        //     .get()
        //     .then((DocumentSnapshot ds) {
        //   if (ds.data()["status"] == 'active') {
        //     if (check) {
        //       Navigator.pushReplacement(
        //         context,
        //         MaterialPageRoute(
        //             builder: (context) => PinProtect(
        //                   redirectPage: 'HOME',
        //                 )),
        //       );
        //       // AppRoutes.makeFirst(
        //       //     context,
        //       //     PinProtect(
        //       //       redirectPage: 'HOME',
        //       //     ));
        //     } else {
        //       Navigator.pushReplacement(
        //         context,
        //         MaterialPageRoute(builder: (context) => IndexLike()),
        //       );
        //       // AppRoutes.makeFirst(context, IndexLike());
        //     }
        //   } else {
        //     if (check) {
        //       Navigator.pushReplacement(
        //         context,
        //         MaterialPageRoute(builder: (context) => Maintenance()),
        //       );
        //       // AppRoutes.makeFirst(context, Maintenance());
        //     } else {
        //       Navigator.pushReplacement(
        //         context,
        //         MaterialPageRoute(builder: (context) => Maintenance()),
        //       );
        //       // AppRoutes.makeFirst(context, Maintenance());
        //       //disable
        //     }
        //   }
        // });
      }
    } catch (e) {
      pref.setBool('login', false);
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(builder: (context) => IndexLike()),
      );
      // AppRoutes.makeFirst(context, IndexLike());
    }
  }

  checkLang() async {
    sharedPreferences = await SharedPreferences.getInstance();
    print(sharedPreferences.getString('language_code'));
  }

  @override
  Widget build(BuildContext context) {
    return old_provider.Consumer<AppLanguage>(builder: (context, model, child) {
      return Scaffold(body: buildForPhone());
    });
  }
}
