/// Flutter icons IconHome
/// Copyright (C) 2021 by original authors @ fluttericon.com, fontello.com
/// This font was generated by FlutterIcon.com, which is derived from Fontello.
///
/// To use this font, place it in your fonts/ directory and include the
/// following in your pubspec.yaml
///
/// flutter:
///   fonts:
///    - family:  IconHome
///      fonts:
///       - asset: fonts/IconHome.ttf
///
///
///
import 'package:flutter/widgets.dart';

class IconHome {
  IconHome._();

  static const _kFontFam = 'IconHome';
  static const String? _kFontPkg = null;

  static const IconData path_43609 =
      IconData(0xe800, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData path_43608 =
      IconData(0xe801, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData path_58819 =
      IconData(0xe802, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData group_24548 =
      IconData(0xe803, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData path_58781 =
      IconData(0xe804, fontFamily: _kFontFam, fontPackage: _kFontPkg);
}
