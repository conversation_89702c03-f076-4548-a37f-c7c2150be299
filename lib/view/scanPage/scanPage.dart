import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:likewallet/controller/scanController/scanController.dart';
import 'package:likewallet/controller/transferController/transferController.dart';
import 'package:likewallet/service/components.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';

class ScanPage extends StatefulWidget {
  const ScanPage({Key? key}) : super(key: key);

  @override
  State<ScanPage> createState() => _ScanPageState();
}

class _ScanPageState extends State<ScanPage> {
  // QR code scanner controller
  QRViewController? controller;
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');

  // Get the scan controller
  late ScanController scanCtrl;

  // Get the transfer controller
  var transferCtrl = Get.find<TransferController>();

  @override
  void initState() {
    super.initState();

    // Initialize scan controller
    if (!Get.isRegistered<ScanController>()) {
      scanCtrl = Get.put(ScanController());
    } else {
      scanCtrl = Get.find<ScanController>();
    }
  }

  @override
  void dispose() {
    controller?.dispose();
    super.dispose();
  }

  // For hot reload (only available in debug mode)
  @override
  void reassemble() {
    super.reassemble();
    if (Platform.isAndroid) {
      controller!.pauseCamera();
    } else if (Platform.isIOS) {
      controller!.resumeCamera();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => ModalProgressHUD(
      inAsyncCall: scanCtrl.isLoading.value,
      child: Scaffold(
        backgroundColor: Color(0xff141322),
        body: Column(
          children: [
            // 🔲 Top dark bar
            Container(
              height: MediaQuery.of(context).size.height * 0.17,
              color: Color(0xff141322),
              padding: EdgeInsets.only(top: 50.h),
              child: Column(
                children: [
                  Stack(
                    alignment: Alignment.center,
                    children: [
                      Align(
                        alignment: Alignment.centerLeft,
                        child: IconButton(
                          iconSize: 16.sp,
                          icon: Icon(Icons.arrow_back_ios_new_rounded, color: Colors.white.withOpacity(0.5)),
                          onPressed: () => Get.back(),
                        ),
                      ),
                      // 🔻 ดัน title มาชิดขอบบนของเส้น
                      Positioned(
                        bottom: 0.h, // ปรับชิดขอบบนของเส้น
                        child: Text(
                          "scanpay_title".tr,
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.bold,
                            color: Colors.cyanAccent,
                            fontFamily: 'Proxima Nova',
                          ),
                        ),
                      ),
                    ],
                  ),

                  // 🔻 เส้น divider (ตรงกลางระหว่างหัวกับเลือกรูปภาพ)
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 80.w),
                    child: Divider(color: Colors.white24, thickness: 1),
                  ),

                  // 🖼 "เลือกรูปภาพ" ชิดขอบล่างของเส้น
                  Padding(
                    padding: EdgeInsets.only(top: 4.h), // ชิดเส้นล่าง
                    child: Align(
                      alignment: Alignment.center,
                      child: Text(
                        "scanpay_select".tr,
                        style: TextStyle(
                          fontSize: 10.sp,
                          color: Colors.white,
                          decoration: TextDecoration.underline,
                          fontFamily: 'Proxima Nova',
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),


            // 🔍 QR Scanner View
            Expanded(
              flex: 6,
              child: Stack(
                children: [
                  QRView(
                    key: qrKey,
                    onQRViewCreated: _onQRViewCreated,
                    cameraFacing: CameraFacing.back,
                  ),
                ],
              ),
            ),

            // 🔲 Bottom dark bar + Instruction
            Container(
              height: MediaQuery.of(context).size.height * 0.5,
              color: Color(0xff141322),
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 36.w, vertical: 48.h),
                child: Text(
                  "scanpay_details".tr,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.5),
                    fontSize: 13.sp,
                    height: 1.4,
                    fontFamily: 'Proxima Nova',
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    ));
  }


  void _onQRViewCreated(QRViewController controller) {
    this.controller = controller;
    controller.scannedDataStream.listen((scanData) async {
      // Pause camera to prevent multiple scans
      controller.pauseCamera();

      // Process the QR data
      await scanCtrl.processQRData(scanData.code);

      print(scanData.code);
      transferCtrl.setAddress(scanData.code.toString());
      // Resume camera after a delay if still on this screen
      Future.delayed(Duration(seconds: 2), () {
        if (mounted) {
          controller.resumeCamera();
        }
      });
    });
  }
}