import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:likewallet/controller/profile/loginCotroller.dart';
import 'package:likewallet/service/components.dart';
import 'package:likewallet/service/loading.dart';
import 'package:likewallet/view/login/confirmOTP.dart';

class Signin extends StatefulWidget {
  const Signin({super.key});

  @override
  State<Signin> createState() => _SigninState();
}

class _SigninState extends State<Signin> with SingleTickerProviderStateMixin {

  int selected = 0;
  TextEditingController email = TextEditingController();
  TextEditingController password = TextEditingController();
  TextEditingController otp = TextEditingController();
  final TextEditingController _phoneNumberController = TextEditingController();

  late TabController _tabController;

  String dropdownValue = '+66';


  LoginController loginCtrl = Get.find<LoginController>();

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _tabController = TabController(vsync: this, length: 2);
  }
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScopeNode currentFocus = FocusScope.of(context);
        if (!currentFocus.hasPrimaryFocus) {
          currentFocus.unfocus();
        }
      },
      child: Scaffold(
          body: Stack(
            children: [
              Container(
                decoration: const BoxDecoration(
                    image: DecorationImage(
                      image: AssetImage(LikeWalletImage.background),
                      fit: BoxFit.cover,
                    )),
              ),
              Column(
                children: [
                  Container(
                    width: MediaQuery.of(context).size.width,
                    height: MediaQuery.of(context).size.height * 0.2,
                    color: LikeWalletAppTheme.bule2,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Container(
                              width: 60.w,
                              height: 60.w,
                              alignment: Alignment.centerRight,
                              child: backButton(context, LikeWalletAppTheme.gray),
                            ),
                            Container(
                              child: Center(
                                child: Text(
                                  'login_choose'.tr,
                                  style: TextStyle(
                                    fontSize: 16.sp,
                                    color: const Color(0xff707071).withOpacity(1),
                                    fontFamily: 'Proxima Nova',
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(width: 60.w, height: 60.w,)
                          ],
                        ),
                        PreferredSize(
                          preferredSize: Size(MediaQuery.of(context).size.width,
                              MediaQuery.of(context).size.height * 0.1),
                          child: Container(
                            child: TabBar(
                              controller: _tabController,
                              labelColor: LikeWalletAppTheme.bule1,
                              unselectedLabelColor:
                              LikeWalletAppTheme.white.withOpacity(0.7),
                              indicatorColor: LikeWalletAppTheme.bule1,
                              onTap: (value) {
                                FocusScope.of(context).unfocus();
                                setState(() {
                                  selected = value;
                                });
                                print(selected);
                              },
                              tabs: [
                                Container(
                                  padding: EdgeInsets.only(
                                      top: MediaQuery.of(context).size.height * 0.02,
                                      bottom:
                                      MediaQuery.of(context).size.height * 0.02),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Image.asset(
                                        LikeWalletImage.icon_smartphone,
                                        height: mediaQuery(context, 'height', 94.97),
                                        color: selected == 0
                                            ? LikeWalletAppTheme.bule1
                                            : LikeWalletAppTheme.white
                                            .withOpacity(0.7),
                                      ),
                                      SizedBox(
                                        width: mediaQuery(context, 'width', 30),
                                      ),
                                      Text(
                                        'forget_tab_mobile'.tr,
                                        style: TextStyle(
                                          fontWeight: FontWeight.w100,
                    //                                color: LikeWalletAppTheme.bule1,
                                          fontSize: 16.sp,
                                          fontFamily: 'Proxima Nova',
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                                Container(
                                  padding: EdgeInsets.only(
                                      top: MediaQuery.of(context).size.height * 0.02,
                                      bottom:
                                      MediaQuery.of(context).size.height * 0.02),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Image.asset(
                                        LikeWalletImage.icon_email,
                                        height: mediaQuery(context, 'height', 50.39),
                                        color: selected == 1
                                            ? LikeWalletAppTheme.bule1
                                            : LikeWalletAppTheme.white
                                            .withOpacity(0.7),
                                      ),
                                      SizedBox(
                                        width: mediaQuery(context, 'width', 30),
                                      ),
                                      Text(
                                        'forget_tab_email'.tr,
                                        style: TextStyle(
                                          fontWeight: FontWeight.w100,
                                          fontSize: 16.sp,
                                          fontFamily: 'Proxima Nova',
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    height: MediaQuery.of(context).size.height * 0.8,
                    width: MediaQuery.of(context).size.width,
                    child: TabBarView(
                      controller: _tabController,
                      children: [Mobile(context), Email(context)],
                    ),
                  ),
                ],
              )
            ],
          )),
    );
  }

  Widget Mobile(context) {
    return Container(
      color: Colors.transparent,
      child: Column(
        children: [
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.05,
          ),
          _inputPhone(),
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.1,
          ),
          _buttonLogin()
        ],
      ),
    );
  }

  Widget _inputPhone() {
    return Container(
      alignment: Alignment.center,
      height: MediaQuery.of(context).size.height * 0.07,
      width: MediaQuery.of(context).size.width * 0.9,
      decoration: BoxDecoration(
        color: const Color(0xff141322),
        border: Border.all(
            color: const Color(0xff0FE8D8), width: mediaQuery(context, 'width', 0.4)),
        borderRadius: const BorderRadius.all(Radius.circular(5.0)),
      ),
      child: Row(
        children: [
          Container(
            alignment: Alignment.center,
            height: MediaQuery.of(context).size.height * 0.07,
            width: MediaQuery.of(context).size.width * 0.2,
            decoration: BoxDecoration(
                border: Border(
                  right: BorderSide(
                    //                   <--- left side
                      color: const Color(0xff0FE8D8),
                      width: mediaQuery(context, 'width', 0.4)),
                )),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                value: dropdownValue,
                elevation: 15,
                style: const TextStyle(color: Color(0xffADACA0)),
                iconEnabledColor: Colors.white,
                iconDisabledColor: Colors.white,
                onChanged: (String? newValue) {
                  setState(() {
                    dropdownValue = newValue.toString();
                  });
                },
                items: <String>['+66', '+856', '+855']
                    .map<DropdownMenuItem<String>>((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(value),
                  );
                }).toList(),
              ),
            ),
          ),
          Container(
            alignment: Alignment.center,
            height: MediaQuery.of(context).size.height * 0.07,
            width: MediaQuery.of(context).size.width * 0.65,
//                color: Colors.blue,
            child: TextField(
              controller: _phoneNumberController,
              style: const TextStyle(color: Colors.white),
              decoration: InputDecoration(
                isDense: true,
                contentPadding: const EdgeInsets.only(left: 20),
                hintText:
                'forget_mobile'.tr,
                hintStyle: TextStyle(
                  fontSize: 16.sp,
                  fontFamily: 'Proxima Nova',
                  color: LikeWalletAppTheme.white.withOpacity(0.7),
                  fontWeight: FontWeight.w100,
                ),
                border: InputBorder.none,
              ),
              keyboardType: TextInputType.number,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buttonLogin() {
    return InkWell(
      onTap: () async {
        FocusScope.of(context).unfocus();

        // Get.to(() => IDCardPage());
        // return ;

        showDialog(
          context: context,
          barrierDismissible: false,
          useSafeArea: false,
          builder: (_) => const CustomLoading(),
        );

        if ((dropdownValue == '+66' && _phoneNumberController.text.length >= 9) ||
            (dropdownValue == '+855' && _phoneNumberController.text.length >= 9) ||
            (dropdownValue == '+856' && _phoneNumberController.text.length >= 9)) {
        }

        String rawPhone = _phoneNumberController.text.replaceAll(' ', '');
        if (rawPhone.startsWith('0')) {
          rawPhone = rawPhone.substring(1); // ลบ 0 ตัวแรก
        }
        final sendPhone = dropdownValue + rawPhone;

        // Get.to(() => Confirmotp());
        // return;
        String? checkPage = await loginCtrl.requestOTP(sendPhone);
        Navigator.pop(context);
        if(checkPage == 'otp') {
          debugPrint('otp');
          Get.to(() => const Confirmotp());
        }else if(checkPage == 'register') {
          debugPrint('register');
          // Get.to(() => Register(
          //   phone: sendPhone,
          // ));
        }
      },
      child: Container(
        width: MediaQuery.of(context).size.width * 0.7,
        height: MediaQuery.of(context).size.height * 0.057,
        decoration: const BoxDecoration(
          color: Color(0xff00F1E0),
          borderRadius: BorderRadius.all(Radius.circular(5.0)),
        ),
        child: Center(
          child: Text(
            'login_login'.tr,
            style: TextStyle(
                fontFamily: 'Proxima Nova',
                color: Colors.black,
                fontSize: 16.sp,
                fontWeight: FontWeight.w100),
          ),
        ),
      ),
    );
  }

  Widget Email(context) {
    return Container(
      color: Colors.transparent,
      child: Column(
        children: [
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.05,
          ),
          _inputEmail(),
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.1,
          ),
          Container(
            alignment: Alignment.center,
            height: mediaQuery(context, 'height', 132),
            width: mediaQuery(context, 'width', 720),
            child: Container(
              width: MediaQuery.of(context).size.width * 0.7,
              height: MediaQuery.of(context).size.height * 0.057,
              decoration: const BoxDecoration(
                color: Color(0xff00F1E0),
                borderRadius: BorderRadius.all(Radius.circular(5.0)),
              ),
              alignment: Alignment.center,
              child: Text(
                'login_login'.tr,
                style: TextStyle(
                  fontFamily: 'Proxima Nova',
                  color: Colors.black,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w100,
                ),
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget _inputEmail() {
    return Container(
      alignment: Alignment.center,
//      height:MediaQuery.of(context).size.height*0.07,
//      width:MediaQuery.of(context).size.width*0.9,
      child: Form(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              decoration: BoxDecoration(
                color: LikeWalletAppTheme.bule2,
                border: Border.all(
                  color: LikeWalletAppTheme.bule1,
                  width: mediaQuery(context, 'height', 0.3),
                ),
                borderRadius: const BorderRadius.all(Radius.circular(5.0)),
              ),
              alignment: Alignment.center,
              height: mediaQuery(context, 'height', 156),
              width: mediaQuery(context, 'width', 930),
//                color: Colors.blue,
              child: TextFormField(
                controller: email,
                style: TextStyle(
                    fontSize: mediaQuery(context, 'height', 47),
                    fontFamily:
                    'Proxima Nova',
                    color: LikeWalletAppTheme.white),
                decoration: InputDecoration(
                  isDense: true,
//                  focusedBorder: OutlineInputBorder(
//                      borderSide: BorderSide(
//                          color: LikeWalletAppTheme.bule1,
//                          width: mediaQuery(context, 'width', 1))),
                  contentPadding: EdgeInsets.only(
                    left: MediaQuery.of(context).size.width * 0.03,
                  ),
                  hintText: 'singinEmail_name'.tr,
                  hintStyle: TextStyle(
                      fontWeight: FontWeight.w100,
                      fontSize: mediaQuery(context, 'height', 46),
                      fontFamily:
                      'Proxima Nova',
                      color: LikeWalletAppTheme.white),
                  border: InputBorder.none,
//                  enabledBorder: OutlineInputBorder(
//                      borderSide: BorderSide(
//                          width: mediaQuery(context, 'width', 0.3),
//                          color: LikeWalletAppTheme.bule1)),
                  hoverColor: Colors.white,
                  disabledBorder: InputBorder.none,
                  focusColor: Colors.white,
                  alignLabelWithHint: true,
                  fillColor: Colors.white,
                ),
                keyboardType: TextInputType.text,
              ),
            ),
            Padding(
              padding: EdgeInsets.only(
                top: MediaQuery.of(context).size.width * 0.05,
              ),
            ),
            Container(
              decoration: BoxDecoration(
                color: LikeWalletAppTheme.bule2,
                border: Border.all(
                  color: LikeWalletAppTheme.bule1,
                  width: mediaQuery(context, 'height', 0.3),
                ),
                borderRadius: const BorderRadius.all(Radius.circular(5.0)),
              ),
              alignment: Alignment.center,
              height: mediaQuery(context, 'height', 156),
              width: mediaQuery(context, 'width', 930),
//                color: Colors.blue,
              child: TextFormField(
                obscureText: true,
                controller: password,
                style: const TextStyle(color: Colors.white),
                decoration: InputDecoration(
                  isDense: true,
//                  focusedBorder: OutlineInputBorder(
//                      borderSide: BorderSide(
//                          color: LikeWalletAppTheme.bule1,
//                          width: mediaQuery(context, 'width', 1))),
                  contentPadding: EdgeInsets.only(
                    left: MediaQuery.of(context).size.width * 0.03,
                  ),
                  hintText: 'singinEmail_lastname'.tr,
                  hintStyle: TextStyle(
                      fontSize: mediaQuery(context, 'height', 47),
                      fontFamily:
                      'Proxima Nova',
                      color: LikeWalletAppTheme.white),
                  border: InputBorder.none,
//                  enabledBorder: OutlineInputBorder(
//                      borderSide: BorderSide(
//                          width: mediaQuery(context, 'width', 0.3),
//                          color: LikeWalletAppTheme.bule1)),
                  hoverColor: Colors.white,
                  disabledBorder: InputBorder.none,
                  focusColor: Colors.white,
                  alignLabelWithHint: true,
                  fillColor: Colors.white,
                ),
                keyboardType: TextInputType.text,
              ),
            ),
            Padding(
              padding: EdgeInsets.only(
                top: MediaQuery.of(context).size.width * 0.05,
              ),
            ),
            Container(
              decoration: BoxDecoration(
                color: LikeWalletAppTheme.bule2,
                border: Border.all(
                  color: LikeWalletAppTheme.bule1,
                  width: mediaQuery(context, 'height', 0.3),
                ),
                borderRadius: const BorderRadius.all(Radius.circular(5.0)),
              ),
              alignment: Alignment.center,
              height: mediaQuery(context, 'height', 156),
              width: mediaQuery(context, 'width', 930),
//                color: Colors.blue,
              child: TextFormField(
                controller: otp,
                style: const TextStyle(color: Colors.white),
                decoration: InputDecoration(
                  isDense: true,
                  contentPadding: EdgeInsets.only(
                    left: MediaQuery.of(context).size.width * 0.03,
                    top: MediaQuery.of(context).size.width * 0.03,
                  ),
                  hintText: 'two_step_2fa_login_otp'.tr,
                  hintStyle: TextStyle(
                      fontSize: mediaQuery(context, 'height', 47),
                      fontFamily:
                      'Proxima Nova',
                      color: LikeWalletAppTheme.white),
                  border: InputBorder.none,
                  hoverColor: Colors.white,
                  disabledBorder: InputBorder.none,
                  focusColor: Colors.white,
                  alignLabelWithHint: true,
                  fillColor: Colors.white,
                  suffixIcon: IconButton(
                    onPressed: () {
                      // Clipboard.getData('text/plain').then((value) {
                      //   otp.text = value!.text.toString();
                      // });
                    },
                    icon: const Icon(
                      Icons.paste,
                      color: LikeWalletAppTheme.bule3,
                    ),
                  ),
                ),
                keyboardType: TextInputType.text,
              ),
            ),
            SizedBox(height: 30.h),
            //ปุ่มลืมรหัสผ่าน
            InkWell(
              onTap: () {
                Navigator.pushNamed(context, '/forgetPassword');
              },
              child: Container(
                alignment: Alignment.bottomRight,
                height: mediaQuery(context, 'height', 75),
                width: mediaQuery(context, 'width', 930),
                child: Text(
                    'singinEmail_forget'.tr,
                    style: TextStyle(
                        fontSize: 15.sp,
                        fontFamily:
                        'Proxima Nova',
                        color: Colors.white70)),
              ),
            )
          ],
        ),
      ),
    );
  }
}
