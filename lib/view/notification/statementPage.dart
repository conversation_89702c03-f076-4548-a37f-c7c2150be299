import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:likewallet/controller/historyController/statementController.dart';

class StatementPage extends StatelessWidget {
  const StatementPage({super.key});
  static final ctrl = Get.put(StatementController());

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white.withOpacity(0.4),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: 20.w),
            child: Column(
              children: [
                SizedBox(height: 48.h),
                Row(
                  children: [
                    Expanded(child: _radioWithLabel("All")),
                    <PERSON><PERSON><PERSON><PERSON>(width: 20.w),
                    Expanded(child: _radioWithLabel("Lock history")),
                  ],
                ),
                <PERSON><PERSON><PERSON><PERSON>(height: 12.h),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "statement_specify".tr,
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w300,
                        color: Colors.black87,
                      ),
                    ),
                    <PERSON><PERSON><PERSON><PERSON>(height: 6.h),
                    GestureDetector(
                      onTap: () => ctrl.pickDate(context),
                      child: AbsorbPointer(
                        child: Obx(() => TextField(
                          readOnly: true,
                          controller: TextEditingController(text: ctrl.formattedDate),
                          decoration: InputDecoration(
                            hintText: '',
                            filled: true,
                            fillColor: Colors.white,
                            contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 16.h),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8.r),
                              borderSide: BorderSide(color: Colors.grey.shade400),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8.r),
                              borderSide: BorderSide(color: Colors.grey.shade400),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8.r),
                              borderSide: BorderSide(color: Colors.blueAccent, width: 1.5),
                            ),
                          ),
                          style: TextStyle(fontSize: 14.sp, color: Colors.black87),
                        )),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 24.h),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 80.w),
                  child: Container(
                    height: 40.h,
                    decoration: BoxDecoration(
                      color: const Color(0xff201F2D),
                      borderRadius: BorderRadius.circular(20.r),
                    ),
                    child: Center(
                      child: Text(
                        "statement_or".tr,
                        textAlign: TextAlign.center,
                        style: TextStyle(color: Color(0xff08e8de), fontSize: 14.sp),
                      ),
                    ),
                  ),
                ),
                SizedBox(height: 24.h),
                Wrap(
                  spacing: 16.w,
                  runSpacing: 16.h,
                  alignment: WrapAlignment.center,
                  children: [
                    _periodBox("Last", "30", "Days"),
                    _periodBox("Past", "3", "Months"),
                    _periodBox("Past", "6", "Months"),
                    _periodBox("Past", "1", "Year"),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _topTab(String text, {bool isActive = false}) {
    return Expanded(
      child: Container(
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: isActive ? Colors.transparent : Colors.transparent,
        ),
        child: Text(
          text,
          style: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.bold,
            color: isActive ? Color(0xff08e8de) : Colors.white54,
          ),
        ),
      ),
    );
  }

  Widget _radioWithLabel(String label) {
    return Obx(() => Row(
      children: [
        Radio<String>(
          value: label,
          groupValue: StatementPage.ctrl.selectedRadio.value,
          onChanged: (value) => StatementPage.ctrl.selectedRadio.value = value!,
          activeColor: Colors.blue,
        ),
        Expanded(
          child: Text(
            label,
            style: TextStyle(fontSize: 14.sp),
          ),
        ),
      ],
    ));
  }

  Widget _periodBox(String prefix, String value, String suffix, {VoidCallback? onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 160.w,
        height: 100.h,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black12,
              blurRadius: 6,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Center(
          child: RichText(
            text: TextSpan(
              text: '$prefix ',
              style: TextStyle(fontSize: 14.sp, color: Colors.black),
              children: [
                TextSpan(
                  text: value,
                  style: TextStyle(fontSize: 22.sp, fontWeight: FontWeight.bold),
                ),
                TextSpan(
                  text: ' $suffix',
                  style: TextStyle(fontSize: 14.sp),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}