import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:likewallet/service/components.dart';
import 'package:likewallet/service/getStorage.dart';
import 'package:likewallet/view/contactUs/contactUsPage.dart';
import 'package:likewallet/view/home/<USER>';
import 'package:likewallet/view/navigationBar/curvedNavigationBar.dart';
import 'package:likewallet/view/newsPage/newsPage.dart';
import 'package:likewallet/view/notification/historyPage.dart';
import 'package:likewallet/view/profile/profile.dart';

class MainScreen extends StatefulWidget {
  final int? selectPage;

  const MainScreen({super.key, this.selectPage});

  @override
  _MainScreenState createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  late int _page;
  final GlobalKey<CurvedNavigationBarState> _bottomNavigationKey = GlobalKey();
  final List<Widget> _pageOption = [
    const HomePage(), // หน้า Home
    NewsPage(), // หน้า News
    const HistoryPage(), // หน้า Notification
    Container(), // none-usable
  ];

  @override
  void initState() {
    super.initState();
    // Initialize page from selectPage parameter if provided
    _page = widget.selectPage ?? 0;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: closeButtonPay,
      child: Scaffold(
        // floatingActionButtonLocation: FloatingActionButtonLocation.endDocked,
        // floatingActionButton: _page == 0 ? _buildPayButton() : const SizedBox(),
        body: Stack(
          children: [
            _pageOption[_page],
            Positioned(
              bottom: 0,
              child: SizedBox(
                height: 80.h,
                width: MediaQuery.of(context).size.width,
                child: CurvedNavigationBar(
                  key: _bottomNavigationKey,
                  index: _page,
                  items: [
                    _buildNavItem(IconHome.path_43609, 0),
                    _buildNavItem(IconHome.path_43608, 1),
                    // _buildNotificationItem(),
                    _buildNavItem(IconHome.group_24548, 2),
                    GestureDetector(
                      onTap: () {
                        showDialog(
                            context: context,
                            useSafeArea: false,
                            builder: (context) => const ContactUsPage());
                      },
                        child: Container(child: _buildNavItem(IconHome.path_58781, 3),
                        ),
                    ),
                    // _buildChatItem(),
                  ],
                  color: colorNav() ? const Color(0xff141322) : Colors.white,
                  buttonBackgroundColor: colorNav() ? const Color(0xff141322) : Colors.white,
                  backgroundColor: Colors.transparent,
                  animationCurve: Curves.easeInOut,
                  animationDuration: const Duration(milliseconds: 300),
                  onTap: (index) {
                    print(index);
                    if (index == 3) {
                      // Get.to(() => ContactUsPage());
                    } else {
                      setState(() => _page = index);
                    }
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPayButton() {
    return FloatingActionButton(
      onPressed: () => print('click Pay Button'),
      backgroundColor: Colors.transparent,
      elevation: 0,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Positioned(
            bottom: 50.h,
            right: -30.w,
            child: Container(
              height: 200.h,
              width: 200.h,
              alignment: Alignment.center,
              child: Stack(
                clipBehavior: Clip.none,
                children: [
                  GestureDetector(
                    onTap: () => print('click Main Pay Icon'),
                    child: AnimatedSwitcher(
                      duration: const Duration(milliseconds: 140),
                      transitionBuilder: (child, animation) => ScaleTransition(scale: animation, child: child),
                      child: Container(
                        key: const ValueKey(false),
                        child: Image.asset(
                          LikeWalletImage.icon_quick_pay,
                          height: 90.h,
                          width: 90.h,
                        ),
                      ),
                    ),
                  ),
                  Positioned(
                    top: 70.h,
                    left: 10.w,
                    child: AnimatedSwitcher(
                      duration: const Duration(milliseconds: 140),
                      transitionBuilder: (child, animation) => ScaleTransition(scale: animation, child: child),
                      child: GestureDetector(
                        onTap: () => print('click Scan Pay'),
                        child: Container(
                          key: const ValueKey(true),
                          height: 35.h,
                          width: 35.h,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.16),
                                offset: const Offset(0, 3),
                                blurRadius: 5.0,
                                spreadRadius: 1.0,
                              ),
                            ],
                          ),
                          child: Image.asset(
                            LikeWalletImage.image_quick_pay_scan,
                            height: 35.h,
                            width: 35.h,
                          ),
                        ),
                      ),
                    ),
                  ),
                  Positioned(
                    top: 5.h,
                    right: 40.w,
                    child: AnimatedSwitcher(
                      duration: const Duration(milliseconds: 140),
                      transitionBuilder: (child, animation) => ScaleTransition(scale: animation, child: child),
                      child: GestureDetector(
                        onTap: () => print('click Store Pay'),
                        child: Container(
                          key: const ValueKey(true),
                          height: 35.h,
                          width: 35.h,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.16),
                                offset: const Offset(0, 3),
                                blurRadius: 5.0,
                                spreadRadius: 1.0,
                              ),
                            ],
                          ),
                          child: Image.asset(
                            LikeWalletImage.image_quick_pay_store,
                            height: 35.h,
                            width: 35.h,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem(IconData icon, int index) {
    return SizedBox(
      height: 50.h,
      width: 50.h,
      child: Icon(
        icon,
        size: 24.h,
        color: _page == 0 ? Colors.white : Colors.black,
      ),
    );
  }

  // Widget _buildNotificationItem() {
  //   return SizedBox(
  //     height: 50.h,
  //     width: 50.h,
  //     child: StreamBuilder<QuerySnapshot>(
  //       stream: fireStore
  //           .collection('notificationByUser')
  //           .doc(uid)
  //           .collection('notify')
  //           .where("status", isEqualTo: "unread")
  //           .snapshots(),
  //       builder: (context, snapshot) {
  //         if (snapshot.hasError || snapshot.connectionState == ConnectionState.waiting) {
  //           return Icon(
  //             IconHome.path_43608,
  //             size: 28.h,
  //             color: _page == 1 ? Colors.white : Colors.black,
  //           );
  //         }
  //         final count = snapshot.data!.docs.length;
  //         return Stack(
  //           alignment: Alignment.center,
  //           children: [
  //             Icon(
  //               IconHome.path_43608,
  //               size: 28.h,
  //               color: _page == 1 ? Colors.white : Colors.black,
  //             ),
  //             if (count > 0)
  //               Positioned(
  //                 top: 5.h,
  //                 right: 5.w,
  //                 child: Container(
  //                   height: 20.h,
  //                   width: 20.h,
  //                   alignment: Alignment.center,
  //                   decoration: const BoxDecoration(
  //                     shape: BoxShape.circle,
  //                     color: Color(0xffFFC400),
  //                   ),
  //                   child: Text(
  //                     count.toString(),
  //                     style: TextStyle(
  //                       fontFamily: 'Proxima Nova',
  //                       fontSize: 12.sp,
  //                       color: Colors.white,
  //                       fontWeight: FontWeight.w500,
  //                     ),
  //                   ),
  //                 ),
  //               ),
  //           ],
  //         );
  //       },
  //     ),
  //   );
  // }
  //
  // Widget _buildChatItem() {
  //   return SizedBox(
  //     height: 50.h,
  //     width: 50.h,
  //     child: StreamBuilder<QuerySnapshot>(
  //       stream: fireStore
  //           .collection('messages')
  //           .doc(uid)
  //           .collection('messages')
  //           .where("status", isEqualTo: "unread")
  //           .snapshots(),
  //       builder: (context, snapshot) {
  //         if (snapshot.hasError || snapshot.connectionState == ConnectionState.waiting) {
  //           return Icon(
  //             IconHome.path_58781,
  //             size: 28.h,
  //             color: _page == 3 ? Colors.white : Colors.black,
  //           );
  //         }
  //         final count = snapshot.data!.docs.length;
  //         return Stack(
  //           alignment: Alignment.center,
  //           children: [
  //             Icon(
  //               IconHome.path_58781,
  //               size: 28.h,
  //               color: _page == 3 ? Colors.white : Colors.black,
  //             ),
  //             if (count > 0)
  //               Positioned(
  //                 top: 5.h,
  //                 right: 5.w,
  //                 child: Container(
  //                   height: 20.h,
  //                   width: 20.h,
  //                   alignment: Alignment.center,
  //                   decoration: const BoxDecoration(
  //                     shape: BoxShape.circle,
  //                     color: Color(0xffFFC400),
  //                   ),
  //                   child: Text(
  //                     count.toString(),
  //                     style: TextStyle(
  //                       fontFamily: 'Proxima Nova',
  //                       fontSize: 12.sp,
  //                       color: Colors.white,
  //                       fontWeight: FontWeight.w500,
  //                     ),
  //                   ),
  //                 ),
  //               ),
  //           ],
  //         );
  //       },
  //     ),
  //   );
  // }

  colorNav() {
    return _page == 0 || _page == 3 ? true : false;
  }

  void closeButtonPay() {
    // TODO: เพิ่ม logic สำหรับปิดปุ่มจ่ายเงิน
  }
}