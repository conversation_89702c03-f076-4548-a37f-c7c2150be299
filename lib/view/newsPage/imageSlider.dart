import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:likewallet/controller/profile/profileController.dart';
import 'package:likewallet/model/newsModel/newsModel.dart';
import 'package:likewallet/service/components.dart';

class ImageSlider extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return _ImageSlider();
  }
}

class _ImageSlider extends State<ImageSlider> {
  final List<News> dataNews = [];
  final ProfileController profileCtrl = Get.find<ProfileController>();

  int _current = 0;

  @override
  void initState() {
    super.initState();
    _getNews();
  }

  _getNews() async {
    // Clear existing data
    setState(() => dataNews.clear());

    try {
      // Get user tier level
      String tierLevel = profileCtrl.tierLevel;

      if (tierLevel == 'tier1' || tierLevel == 'tierBU') {
        final res = await FirebaseFirestore.instance
            .collection('notificationNews')
            .doc('tier1')
            .collection('news')
            .where('status', isEqualTo: 'active')
            .orderBy('timestamp', descending: true)
            .get();

        res.docs.forEach((value) {
          setState(() => dataNews.add(News.fromJson(value.data())));
        });
      } else {
        final res = await FirebaseFirestore.instance
            .collection('notificationNews')
            .doc('normal')
            .collection('news')
            .where('status', isEqualTo: 'active')
            .orderBy('timestamp', descending: true)
            .get();

        res.docs.forEach((value) {
          setState(() => dataNews.add(News.fromJson(value.data())));
        });
      }
    } catch (e) {
      print('Error loading news: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Expanded(
        child: dataNews.isEmpty
            ? Center(
          child: SpinKitFadingCircle(
            color: LikeWalletAppTheme.bule1,
            size: 50,
          ),
        )
            : SingleChildScrollView(
          physics: NeverScrollableScrollPhysics(),
          child: Column(
            mainAxisSize:
            MainAxisSize.min, // ✅ ให้ Column สูงเท่าที่ลูกใช้จริง
            children: [
              SizedBox(
                height: 0.4.sh,
                child: CarouselSlider.builder(
                  itemCount: dataNews.length,
                  itemBuilder: (context, index, realIndex) {
                    final item = dataNews[index];
                    return InkWell(
                      onTap: () {
                        if (item.url != null && item.url!.isNotEmpty) {
                          print('URL: ${item.url}');
                          // Get.to(() => WebOpenNoTitle(...));
                        }
                      },
                      child: Container(
                        margin: EdgeInsets.symmetric(
                            horizontal: 8.w), // 👈 ห่างขอบแต่ละ slide
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16.r),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black26,
                              blurRadius: 5,
                              offset: Offset(0, 3),
                            ),
                          ],
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(16.r),
                          child: CachedNetworkImage(
                            imageUrl: item.image ?? '',
                            fit: BoxFit.cover,
                            placeholder: (context, url) => Center(
                              child: SpinKitFadingCircle(
                                color: LikeWalletAppTheme.bule1,
                                size: 50,
                              ),
                            ),
                            errorWidget: (context, url, error) =>
                                Icon(Icons.error),
                          ),
                        ),
                      ),
                    );
                  },
                  options: CarouselOptions(
                    height: 0.3.sh,
                    viewportFraction: 1,
                    enlargeCenterPage: true, // 👈 ขยาย slide ปัจจุบัน
                    autoPlay: false,
                    onPageChanged: (index, reason) {
                      setState(() {
                        _current = index;
                      });
                    },
                  ),
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: dataNews.map((url) {
                  int index = dataNews.indexOf(url);
                  return Container(
                    width: 6.0.w,
                    height: 6.0.h,
                    margin: EdgeInsets.symmetric(
                        vertical: 10.0.h, horizontal: 4.0.w),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: _current == index
                          ? Color(0xff08e8de)
                          : Color.fromRGBO(0, 0, 0, 0.4),
                    ),
                  );
                }).toList(),
              ),
              SizedBox(
                height: 0.5.sh, // ✅ ปรับความสูงให้เหมาะสม
                child: SingleChildScrollView(
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 48.w, vertical: 24.h),
                    width: 1.sw,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Center(
                          child: Text(
                            'แจ้งปรับการใช้งานระบบสะสมคะแนน',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: Colors.black,
                              fontSize: 14.sp,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        SizedBox(height: 24.h),
                        Text(
                          'LikeWalletจะทำการอัปเกรดสิทธิ\nประโยชน์ให้ผู้ใช้งาน เพื่อประสบการณ์ที่\nหลากหลายขึ้น',
                          style: TextStyle(
                            color: Colors.black.withOpacity(0.7),
                            fontSize: 14.sp,
                          ),
                        ),
                        SizedBox(height: 24.h),
                        Text(
                          'ผู้ใช้งานสามารถสะสมและใช้คะแนนจากการร่วมกิจกรรมต่างๆต่อเนื่องได้ที่แอป\nAAM และ Prachakij',
                          style: TextStyle(
                            color: Colors.black.withOpacity(0.7),
                            fontSize: 14.sp,
                          ),
                        ),
                        SizedBox(height: 24.h),
                        Text(
                          'โดย LikeWallet จะโอนคะแนนสะสม Likepoint ทั้งหมดในทุกกิจกรรมของผู้ใช้งาน ไปยังแอป AAM หรือ Prachakij ที่ผู้ใช้เลือกเป็นแอปอักเกรดคะแนน Likepoint จะถูกโอนไปเป็น AAMpoint (เอเอเอ็มพอยท์) หรือ PMSpoint (พีเอ็มเอสพอยท์) โดยจะมีจำนวนและมูลค่าเท่ากันกับ Likepoint ที่มีอยู่เดิม และยังคงสามารถร่วมกิจกรรมสะสมคะแนนต่างๆได้เช่นเดิม ตามที่แอป AAM และ Prachakij กำหนด',
                          style: TextStyle(
                            color: Colors.black.withOpacity(0.7),
                            fontSize: 14.sp,
                          ),
                        ),
                        SizedBox(height: 24.h),
                        Text(
                          'ซึ่ง LikeWallet จะมีข้อความแจ้งระบบพร้อมอัปเกรดในช่องทางนี้ พร้อมคำแนะนำในการอัปเกรดโดยละเอียด เร็วๆนี้ค่ะ',
                          style: TextStyle(
                            color: Colors.black.withOpacity(0.7),
                            fontSize: 14.sp,
                          ),
                        ),
                        SizedBox(height: 150.h),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ));
  }
}