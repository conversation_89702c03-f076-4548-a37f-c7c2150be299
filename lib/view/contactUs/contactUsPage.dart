import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:likewallet/service/components.dart';

class ContactUsPage extends StatelessWidget {
  const ContactUsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Container(
        alignment: Alignment.center,
        padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              LikeWalletImage.like_point,
              height: 60.h,
            ),
            SizedBox(height: 20.h),
            Text(
              'ติดต่อเราได้ง่ายขึ้นกับไลค์วอลเลท',
              style: TextStyle(
                fontFamily: 'Proxima Nova',
                fontSize: 20.sp,
                color: const Color(0xff00c5c2),
                // letterSpacing: 2.8.sp,
                fontWeight: FontWeight.w300,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(
              width: MediaQuery.of(context).size.width * 0.8,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 10.h),
                  Text(
                    'ไลค์วอลเลท เสนอช่องทางที่สะดวกกว่าในการติดต่อ แอดมินของเราเพียงคลิกลิงค์นี้',
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      fontSize: 16.sp,
                      color: const Color(0xff00c5c2),
                      // letterSpacing: 2.8.sp,
                      fontWeight: FontWeight.w300,
                    ),
                    // textAlign: TextAlign.center,
                  ),
                  GestureDetector(
                    onTap: () => print('click LINE Link'),
                    child: Text(
                      'https://lin.ee/FGA6voL',
                      style: TextStyle(
                        fontFamily: 'Proxima Nova',
                        fontSize: 16.sp,
                        color: Colors.blue,
                        decoration: TextDecoration.underline,
                      ),
                      // textAlign: TextAlign.center,
                    ),
                  ),
                  Text(
                    'หรือเพิ่มเพื่อน @likewallet ใน LINE\nเราพร้อมดูแลคุณค่ะ',
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      fontSize: 16.sp,
                      color: const Color(0xff00c5c2),
                      // letterSpacing: 2.8.sp,
                      fontWeight: FontWeight.w300,
                    ),
                    // textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 16.h),
                  // Divider(color: Colors.grey.withOpacity(0.3)),
                  SizedBox(height: 16.h),
                  Text(
                    'Make Contacting Our Admin Easier',
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      fontSize: 20.sp,
                      color: const Color(0xff00c5c2),
                      // letterSpacing: 2.8.sp,
                      fontWeight: FontWeight.w300,
                    ),
                    // textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    'To make contacting our admin easier, simply click',
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      fontSize: 16.sp,
                      color: const Color(0xff00c5c2),
                      // letterSpacing: 2.8.sp,
                      fontWeight: FontWeight.w300,
                    ),
                    // textAlign: TextAlign.center,
                  ),
                  GestureDetector(
                    onTap: () => print('click LINE Link'),
                    child: Text(
                      'https://lin.ee/FGA6voL',
                      style: TextStyle(
                        fontFamily: 'Proxima Nova',
                        fontSize: 16.sp,
                        color: Colors.blue,
                        decoration: TextDecoration.underline,
                      ),
                      // textAlign: TextAlign.center,
                    ),
                  ),
                  Text(
                    'or add us on LINE: @likewallet\nWe\'re here to help.',
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      fontSize: 16.sp,
                      color: const Color(0xff00c5c2),
                      // letterSpacing: 2.8.sp,
                      fontWeight: FontWeight.w300,
                    ),
                    // textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 24.h),
                ],
              ),
            ),
            InkWell(
              onTap: () => Get.back(),
              child: Container(
                width: MediaQuery.of(context).size.width * 0.3,
                height: 48.h,
                decoration: BoxDecoration(
                  color: const Color(0xff00F1E0),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                alignment: Alignment.center,
                child: Text(
                  'promtpay_thank'.tr,
                  style: TextStyle(
                    fontFamily: 'Proxima Nova',
                    fontSize: 16.sp,
                    color: Colors.black,
                    fontWeight: FontWeight.w100,
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}