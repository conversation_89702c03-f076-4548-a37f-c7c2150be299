import 'package:clippy_flutter/clippy_flutter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:likewallet/controller/transferController/transferController.dart';
import 'package:likewallet/controller/transferController/onContractController.dart';
import 'package:likewallet/controller/scanController/scanController.dart';
import 'package:likewallet/model/contacts/contactsModel.dart';
import 'package:likewallet/service/components.dart';
import 'package:likewallet/service/getStorage.dart';
import 'package:likewallet/view/scanPage/scanPage.dart';
import 'package:likewallet/view/transferPoint/confirmTransection.dart';
import 'package:likewallet/view/scanPage/scanPageKYC.dart';

class TransferPage extends StatefulWidget {
  const TransferPage({super.key});

  @override
  State<TransferPage> createState() => _TranferPageState();
}

class _TranferPageState extends State<TransferPage> {

  String dropdownValue = '+66';
  bool showfavorite = false;
  bool showsymbol = false;
  bool showbutton = false;
  bool syncContact = Storage.get(StorageKeys.syncContact) ?? false;
  bool actionSyncContact = Storage.get(StorageKeys.actionSyncContact) ?? false;
  String amountValue = '0';
  double balanceLIKE = 1000.0; // Mock balance

  final transferCtrl = Get.find<TransferController>();
  late ContactController contactController;

  @override
  void initState() {
    super.initState();
    // Initialize ContactController and assign it to TransferController
    contactController = Get.put(ContactController());
    transferCtrl.contact = contactController;

    // Initialize ScanController
    Get.put(ScanController());
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => SingleChildScrollView(
        child: GestureDetector(
          onTap: () {
            FocusScope.of(context).unfocus();
            setState(() {
              showsymbol = false;
              showfavorite = false;
            });
          },
          child: Stack(
            alignment: Alignment.topCenter,
            children: [
              Container(
                padding: EdgeInsets.only(top: MediaQuery.of(context).size.height * 0.32),
                height: MediaQuery.of(context).size.height * 0.9,
                width: double.infinity,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    stops: [0.0, 0.2, 0.5],
                    colors: [Colors.white, Colors.white, Color(0xffF5F5F5)],
                  ),
                ),
                child: Column(
                  children: [
                    _head(),
                    SizedBox(height: 7.h),
                    transferCtrl.selectedInput.isEmpty
                        ? _selectInput()
                        : transferCtrl.selectedInput.value == 'address'
                            ? _inputAddress()
                            : _inputNumber(),
                    SizedBox(height: 3.h),
                    Container(
                      margin: EdgeInsets.only(top: 0.h),
                      height: 288.h,
                      width: 378.w,
                      child: Stack(
                        children: [
                          Positioned(
                            top: 12.h,
                            left: 0.w,
                            child: _amountLike(),
                          ),
                          Positioned(
                            top: 136.h,
                            left: 0.w,
                            child: _exchangeRate(),
                          ),
                          Positioned(
                            top: 95.h,
                            left: 176.w,
                            child: _iconEqual(),
                          ),
                        ],
                      ),
                    ),
                    syncContact && !showbutton
                        ? transferCtrl.shopID != null
                            ? Container()
                            : Column(children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Container(
                                      padding: EdgeInsets.only(left: 30.w),
                                      alignment: Alignment.centerLeft,
                                      child: Text(
                                        'QUICK CONTACTS',
                                        style: TextStyle(
                                          fontFamily: 'Proxima Nova',
                                          fontSize: 13.sp,
                                          color: const Color(0xff3c3c43),
                                          letterSpacing: 0.4.sp,
                                        ),
                                      ),
                                    ),
                                    GestureDetector(
                                      onTap: () {
                                        print('Show all contacts');
                                      },
                                      child: Container(
                                        margin: EdgeInsets.only(right: 28.w),
                                        height: 12.h,
                                        width: 12.w,
                                        child: Transform.rotate(
                                          angle: 3.1,
                                          child: SvgPicture.string(
                                            '<svg viewBox="-281.5 3213.3 24.5 36.3" ><path  d="M -259.5249938964844 3249.675048828125 C -260.1029968261719 3249.675048828125 -260.6839904785156 3249.47607421875 -261.156005859375 3249.068115234375 L -281.5329895019531 3231.508056640625 L -261.156005859375 3213.947021484375 C -260.1109924316406 3213.0458984375 -258.531005859375 3213.162109375 -257.6300048828125 3214.208984375 C -256.72900390625 3215.2548828125 -256.8460083007812 3216.8330078125 -257.8919982910156 3217.73388671875 L -273.8739929199219 3231.508056640625 L -257.8919982910156 3245.281005859375 C -256.8460083007812 3246.18310546875 -256.72900390625 3247.760986328125 -257.6300048828125 3248.806884765625 C -258.125 3249.381103515625 -258.822998046875 3249.675048828125 -259.5249938964844 3249.675048828125 Z" fill="#231f20" stroke="none" stroke-width="1" stroke-miterlimit="10" stroke-linecap="butt" /></svg>',
                                            fit: BoxFit.fitHeight,
                                            color: Colors.grey,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                Container(
                                  margin: EdgeInsets.only(left: 20.w),
                                  height: 120.h,
                                  width: double.infinity,
                                  child: Obx(
                                    () => transferCtrl.contactLoading.value
                                        ? Center(
                                            child: CircularProgressIndicator(),
                                          )
                                        : transferCtrl.getContactMobile.isEmpty
                                            ? Center(
                                                child: Text(
                                                  'ไม่พบข้อมูล',
                                                  style: TextStyle(
                                                    fontFamily: 'Proxima Nova',
                                                    fontSize: 14.sp,
                                                    color:
                                                        const Color(0xcc3c3c43),
                                                  ),
                                                ),
                                              )
                                            : ListView.builder(
                                                itemCount: transferCtrl
                                                    .getContactMobile.length,
                                                scrollDirection: Axis.horizontal,
                                                itemBuilder: (context, index) {
                                                  return GestureDetector(
                                                    onTap: () {
                                                      if (transferCtrl
                                                          .getContactMobile[index]
                                                          .address
                                                          .isNotEmpty) {
                                                        print(
                                                            'Show contact details: ${transferCtrl.getContactMobile[index].name}');
                                                      } else {
                                                        print(
                                                            'No address found for ${transferCtrl.getContactMobile[index].name}');
                                                      }
                                                    },
                                                    child: Container(
                                                      margin:
                                                          EdgeInsets.symmetric(
                                                              horizontal: 10.w,
                                                              vertical: 10.h),
                                                      height: double.infinity,
                                                      child: Column(
                                                        children: [
                                                          Container(
                                                            height: 58.h,
                                                            width: 62.w,
                                                            child: Stack(
                                                              children: [
                                                                Container(
                                                                  alignment:
                                                                      Alignment
                                                                          .center,
                                                                  height: 53.h,
                                                                  width: 62.w,
                                                                  decoration:
                                                                      BoxDecoration(
                                                                    color: transferCtrl
                                                                        .getContactMobile[
                                                                            index]
                                                                        .color,
                                                                    borderRadius:
                                                                        BorderRadius
                                                                            .circular(
                                                                                40.r),
                                                                  ),
                                                                  child: Text(transferCtrl
                                                                      .getContactMobile[
                                                                          index]
                                                                      .name
                                                                      .substring(
                                                                          0, 1)),
                                                                ),
                                                                Positioned(
                                                                  bottom: 0,
                                                                  right: 5.w,
                                                                  child:
                                                                      Image.asset(
                                                                    LikeWalletImage
                                                                        .iconLikeWallet,
                                                                    height: 24.h,
                                                                    width: 24.w,
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                          SizedBox(height: 8.h),
                                                          Container(
                                                            width: 62.w,
                                                            child: Text(
                                                              transferCtrl
                                                                  .getContactMobile[
                                                                      index]
                                                                  .name,
                                                              style: TextStyle(
                                                                fontFamily:
                                                                    'Proxima Nova',
                                                                fontSize: 11.sp,
                                                                color: const Color(
                                                                    0xcc3c3c43),
                                                                letterSpacing:
                                                                    0.3.sp,
                                                                height: 1.1,
                                                              ),
                                                              textAlign: TextAlign
                                                                  .center,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  );
                                                },
                                              ),
                                  ),
                                ),
                              ])
                        : Container(),
                  ],
                ),
              ),
              _buttonNext(),
              // _buttonBackStore(),
              _showSymbol(),
              _favoriteForm(),
              if (!actionSyncContact)
                Padding(
                  padding: EdgeInsets.symmetric(vertical: 320.h),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(20.r),
                              gradient: const LinearGradient(
                                begin: Alignment.bottomCenter,
                                end: Alignment.topCenter,
                                stops: [0, 1.0],
                                colors: [Color(0xff505DFF), Color(0xff3948FD)],
                              ),
                            ),
                            width: 396.w,
                            child: Column(
                              children: [
                                Container(
                                  margin: EdgeInsets.only(top: 40.h),
                                  child: Image.asset(
                                    LikeWalletImage.contactList,
                                    fit: BoxFit.fitWidth,
                                    width: 156.w,
                                  ),
                                ),
                                Container(
                                  padding: EdgeInsets.all(20.sp),
                                  child: Text(
                                    'contact_list_title'.tr,
                                    style: TextStyle(
                                      fontFamily: 'Proxima Nova',
                                      fontSize: 18.sp,
                                      color: Colors.white,
                                      fontWeight: FontWeight.w300,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                                Container(
                                  padding: EdgeInsets.symmetric(horizontal: 28.sp),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      GestureDetector(
                                        onTap: () async {
                                          print('Import contacts');
                                            syncContact = true;
                                            actionSyncContact = true;

                                          Storage.save(
                                              StorageKeys.syncContact, true);
                                          Storage.save(
                                              StorageKeys.actionSyncContact,
                                              true);

                                          setState(() {});
                                          // Fetch contacts using the TransferController
                                          await transferCtrl.getContacts(
                                              context: context);


                                        },
                                        child: Container(
                                          width: 70.w,
                                          height: 26.h,
                                          decoration: BoxDecoration(
                                            color: Colors.transparent,
                                            border: Border.all(color: Colors.green),
                                            borderRadius:
                                                BorderRadius.circular(8.r),
                                          ),
                                          child: Center(
                                            child: Text(
                                              'contact_import'.tr,
                                              style: TextStyle(
                                                color: Colors.green,
                                                fontSize: 14.sp,
                                                fontFamily: 'Proxima Nova',
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                      GestureDetector(
                                        onTap: () {
                                          print('Disable contacts');
                                            syncContact = false;
                                            actionSyncContact = true;

                                          Storage.save(
                                              StorageKeys.syncContact, false);
                                          Storage.save(
                                              StorageKeys.actionSyncContact,
                                              true);

                                          setState(() {});

                                        },
                                        child: Container(
                                          width: 70.w,
                                          height: 26.h,
                                          decoration: BoxDecoration(
                                            color: Colors.transparent,
                                            border: Border.all(color: Colors.red),
                                            borderRadius:
                                                BorderRadius.circular(8.r),
                                          ),
                                          child: Center(
                                            child: Text(
                                              'contact_disable'.tr,
                                              style: TextStyle(
                                                color: Colors.red,
                                                fontSize: 14.sp,
                                                fontFamily: 'Proxima Nova',
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                SizedBox(height: 47.h),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _head() {
    return Container(
      width: 372.w,
      child: Row(
        children: [
          Container(
            height: 48.h,
            alignment: Alignment.bottomCenter,
            child: Text(
              'bankingSend_to'.tr,
              style: TextStyle(
                fontFamily: 'Proxima Nova',
                fontSize: 16.sp,
                color: LikeWalletAppTheme.gray1,
                fontWeight: FontWeight.normal,
              ),
            ),
          ),
          const Spacer(),
          GestureDetector(
            onTap: () => Get.to(() => ScanPage()),
            child: Container(
              height: 48.h,
              width: 62.w,
              child: Image.asset(
                LikeWalletImage.icon_scan_barcode,
                scale: 3.8,
              ),
            ),
          ),
          GestureDetector(
            onTap: () {
              print('Toggle favorite');
              setState(() => showfavorite = !showfavorite);
            },
            child: Container(
              height: 41.h,
              width: 41.w,
              alignment: Alignment.center,
              child: Image.asset(
                LikeWalletImage.icon_favorite_black,
                height: 41.h,
                width: 41.w,
              ),
            ),
          ),
          GestureDetector(
            onTap: () => print('Paste address'),
            child: Container(
              width: 62.w,
              alignment: Alignment.center,
              child: ClipOval(
                child: Container(
                  alignment: Alignment.center,
                  color: LikeWalletAppTheme.gray6,
                  height: 41.h,
                  width: 41.w,
                  child: Text(
                    'bankingSend_to_paste'.tr,
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      fontSize: 12.sp,
                      color: LikeWalletAppTheme.black,
                      fontWeight: FontWeight.normal,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _inputAddress() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(width: 0.1.w, color: LikeWalletAppTheme.gray),
        borderRadius: BorderRadius.circular(2.r),
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: LikeWalletAppTheme.black.withOpacity(0.1),
            spreadRadius: 0.4.sp,
            blurRadius: 1.2.sp,
          ),
        ],
      ),
      alignment: Alignment.centerLeft,
      height: 46.h,
      width: 374.w,
      child: Row(
        children: [
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(left: 20.w),
              child: TextFormField(
                onChanged: (value) {
                  setState(() {
                    transferCtrl.toAddress = value;
                    showbutton = value.isNotEmpty;
                  });
                },
                controller: transferCtrl.addressText,
                style: TextStyle(
                  fontFamily: 'Proxima Nova',
                  fontSize: 17.sp,
                  color: LikeWalletAppTheme.gray1,
                  fontWeight: FontWeight.w100,
                ),
                keyboardType: TextInputType.text,
                textAlignVertical: TextAlignVertical.center,
                decoration: InputDecoration(
                  isDense: true,
                  focusedBorder: InputBorder.none,
                  border: InputBorder.none,
                  hintText: 'bankingSend_to_address'.tr,
                  hintStyle: TextStyle(
                    fontFamily: 'Proxima Nova',
                    fontSize: 17.sp,
                    color: LikeWalletAppTheme.gray1,
                    fontWeight: FontWeight.w100,
                  ),
                ),
              ),
            ),
          ),
          GestureDetector(
            onTap: () {

              transferCtrl.addressText.clear();
              transferCtrl.toAddress = 'noaddress';
              transferCtrl.changeSelectedInput('');

            },
            child: Container(
              height: 14.h,
              margin: EdgeInsets.only(right: 16.w),
              child: Image.asset(LikeWalletImage.icon_back),
            ),
          ),
        ],
      ),
    );
  }

  Widget _inputNumber() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(width: 0.1.w, color: LikeWalletAppTheme.gray),
        borderRadius: BorderRadius.circular(2.r),
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: LikeWalletAppTheme.black.withOpacity(0.1),
            spreadRadius: 0.4.sp,
            blurRadius: 1.2.sp,
          ),
        ],
      ),
      alignment: Alignment.centerLeft,
      height: 46.h,
      width: 374.w,
      child: Row(
        children: [
          Container(
            decoration: BoxDecoration(
              color: LikeWalletAppTheme.gray6,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(2.r),
                bottomLeft: Radius.circular(2.r),
              ),
            ),
            width: 77.w,
            alignment: Alignment.center,
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                value: dropdownValue,
                style: const TextStyle(color: Color(0xffADACA0)),
                iconEnabledColor: LikeWalletAppTheme.black,
                icon: Icon(Icons.keyboard_arrow_down, size: 16.sp),
                onChanged: (String? newValue) {
                  setState(() {
                    dropdownValue = newValue!;
                  });
                },
                items: ['+66', '+855', '+856']
                    .map<DropdownMenuItem<String>>((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(
                      value,
                      style: const TextStyle(color: LikeWalletAppTheme.black),
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(left: 20.w),
              child: TextFormField(
                onChanged: (value) {
                  setState(() {
                    showbutton = value.isNotEmpty;
                  });
                },
                controller: transferCtrl.phoneNumber,
                style: TextStyle(
                  fontFamily: 'Proxima Nova',
                  fontSize: 17.sp,
                  color: LikeWalletAppTheme.gray1,
                  fontWeight: FontWeight.w100,
                ),
                keyboardType: TextInputType.phone,
                textAlignVertical: TextAlignVertical.center,
                decoration: InputDecoration(
                  isDense: true,
                  focusedBorder: InputBorder.none,
                  border: InputBorder.none,
                  hintText: 'bankingSend_to_mobile_number'.tr,
                  hintStyle: TextStyle(
                    fontFamily: 'Proxima Nova',
                    fontSize: 17.sp,
                    color: LikeWalletAppTheme.gray1,
                    fontWeight: FontWeight.w100,
                  ),
                ),
              ),
            ),
          ),
          GestureDetector(
            onTap: () {

                transferCtrl.phoneNumber.clear();
                transferCtrl.toAddress = 'noaddress';
                transferCtrl.changeSelectedInput('');

            },
            child: Container(
              height: 14.h,
              margin: EdgeInsets.only(right: 16.w),
              child: Image.asset(LikeWalletImage.icon_back),
            ),
          ),
        ],
      ),
    );
  }

  Widget _amountLike() {
    return Container(
      padding: EdgeInsets.only(left: 0.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 22.w),
            width: 268.w,
            height: 116.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6.r),
              color: LikeWalletAppTheme.white,
              boxShadow: [
                BoxShadow(
                  color: const Color(0x1a000000),
                  offset: const Offset(0.4, 0.4),
                  blurRadius: 2.8.sp,
                  spreadRadius: 0.4.sp,
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                  child: Text(
                    'bankingSend_to_amount'.tr,
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      fontSize: 14.sp,
                      color: LikeWalletAppTheme.gray4.withOpacity(0.3),
                      letterSpacing: 0.1.sp,
                    ),
                  ),
                ),
                Container(
                  alignment: Alignment.centerLeft,
                  height: 61.h,
                  child: TextFormField(
                    textAlignVertical: TextAlignVertical.center,
                    textAlign: TextAlign.start,
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      fontSize: 33.sp,
                      color: LikeWalletAppTheme.gray1,
                    ),
                    keyboardType: TextInputType.number,
                    controller: transferCtrl.amountSend,
                    decoration: InputDecoration(
                      focusedBorder: InputBorder.none,
                      enabledBorder: InputBorder.none,
                      hintText: '0',
                      hintStyle: TextStyle(
                        fontFamily: 'Proxima Nova',
                        fontSize: 33.sp,
                        color: LikeWalletAppTheme.gray1,
                      ),
                    ),
                    onChanged: (value) {
                      setState(() {
                        amountValue = value;
                        showbutton = value.isNotEmpty;
                      });
                    },
                  ),
                ),
                Expanded(
                  child: Text(
                    Symbol == 'THB'
                        ? 'symbol_th'.tr
                        : Symbol == 'USD'
                            ? 'symbol_us'.tr
                            : Symbol == 'LAK'
                                ? 'symbol_lak'.tr
                                : Symbol == 'VND'
                                    ? 'symbol_vn'.tr
                                    : Symbol == 'GOLD'
                                        ? 'symbol_gold'.tr
                                        : 'symbol_like'.tr,
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      fontSize: 13.sp,
                      color: LikeWalletAppTheme.gray4.withOpacity(0.3),
                      letterSpacing: 0.1.sp,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 16.w),
            child: _switchSymbol(),
          ),
        ],
      ),
    );
  }

  Widget _exchangeRate() {
    return Container(
      padding: EdgeInsets.only(left: 0.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Container(
            width: 268.w,
            height: 116.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6.r),
              color: LikeWalletAppTheme.white,
              boxShadow: [
                BoxShadow(
                  color: const Color(0x1a000000),
                  offset: const Offset(0.4, 0.4),
                  blurRadius: 2.8.sp,
                  spreadRadius: 0.4.sp,
                ),
              ],
            ),
            child: Stack(
              children: [
                Positioned(
                  left: 22.w,
                  top: 6.h,
                  child: Text(
                    'banking_send_equal'.tr,
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      fontSize: 14.sp,
                      color: LikeWalletAppTheme.gray4.withOpacity(0.3),
                      letterSpacing: 0.1.sp,
                    ),
                  ),
                ),
                Positioned(
                  left: 22.w,
                  top: 27.h,
                  child: Container(
                    alignment: Alignment.centerLeft,
                    height: 61.h,
                    child: Text(
                      amountValue.isEmpty
                          ? '0'
                          : (double.parse(amountValue) * transferCtrl.rateCurrency.value * transferCtrl.rate.value)
                              .toStringAsFixed(0),
                      style: TextStyle(
                        fontFamily: 'Proxima Nova',
                        fontSize: 33.sp,
                        color: LikeWalletAppTheme.gray1,
                      ),
                    ),
                  ),
                ),
                Positioned(
                  left: 22.w,
                  bottom: 6.h,
                  child: Text(
                    'cash_likepoint'.tr,
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      fontSize: 13.sp,
                      color: LikeWalletAppTheme.gray4.withOpacity(0.3),
                      letterSpacing: 0.1.sp,
                    ),
                  ),
                ),
                Positioned(
                  right: 18.w,
                  bottom: 6.h,
                  child: Text(
                    'LIKE',
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      fontSize: 20.sp,
                      color: LikeWalletAppTheme.black,
                      fontWeight: FontWeight.w500,
                      letterSpacing: 0.1.sp,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _iconEqual() {
    return Image.asset(
      LikeWalletImage.icon_equal,
      height: 91.h,
    );
  }

  Widget _switchSymbol() {
    return GestureDetector(
      onTap: () {
        setState(() {
          showsymbol = !showsymbol;
          showbutton = true;
        });
      },
      child: Container(
        margin: EdgeInsets.only(left: 0.w),
        height: 64.h,
        width: 108.w,
        child: Row(
          children: [
            Obx(() => Text(
              transferCtrl.symbol.value,
              style: TextStyle(
                fontFamily: 'Proxima Nova',
                fontSize: 28.sp,
                color: LikeWalletAppTheme.black,
                fontWeight: FontWeight.w700,
                shadows: [
                  Shadow(
                    blurRadius: 2.8.sp,
                    color: LikeWalletAppTheme.gray.withOpacity(0.2),
                    offset: const Offset(0, 0),
                  ),
                ],
              ),
            )),
            SizedBox(width: 8.w),
            SvgPicture.asset(
              LikeWalletImage.icon_dropdown,
              fit: BoxFit.contain,
              height: 9.h,
              width: 9.w,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buttonNext() {
    return AnimatedPositioned(
      bottom: 10.h,
      height: transferCtrl.amountSend.text.trim().isEmpty || transferCtrl.amountSend.text == '0' ? 0.h: 100.h,
      duration: const Duration(milliseconds: 500),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          GestureDetector(
            onTap: () {
              setState(() {
                showbutton = false;
                transferCtrl.amountSend.clear();
                amountValue = '0';
              });
            },
            child: Container(
              width: 186.w,
              height: 53.h,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Image.asset(
                    LikeWalletImage.icon_button_cancel_white,
                    height: 53.h,
                  ),
                  Text(
                    'bankingbuy_back'.tr,
                    style: TextStyle(
                      fontFamily: 'Noto Sans',
                      fontSize: 14.sp,
                      color: LikeWalletAppTheme.gray.withOpacity(0.8),
                    ),
                  ),
                ],
              ),
            ),
          ),
          GestureDetector(
            onTap: () {
              print("click next");

              // Validate input
              if (transferCtrl.amountSend.text.isEmpty ||
                  (transferCtrl.toAddress == 'noaddress' && transferCtrl.phoneNumber.text.isEmpty)) {
                Get.snackbar(
                  'Input Error',
                  'Please enter amount and recipient information',
                  snackPosition: SnackPosition.BOTTOM,
                );
                return;
              }

              // Prepare transaction data
              transferCtrl.prepareTransactionData();

              // Navigate to confirmation page
              Get.to(() => const ConfirmTransection());
            },
            child: Container(
              width: 186.w,
              height: 53.h,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    'bankingbuy_button'.tr,
                    style: TextStyle(
                      fontFamily: 'Noto Sans',
                      fontSize: 14.sp,
                      color: LikeWalletAppTheme.gray.withOpacity(0.8),
                    ),
                  ),
                  Image.asset(
                    LikeWalletImage.icon_button_next_black,
                    height: 53.h,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buttonBackStore() {
    return transferCtrl.shopID == null
        ? Container()
        : Positioned(
            top: 600.h,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                GestureDetector(
                  onTap: () => print('Back to store'),
                  child: Container(
                    width: 186.w,
                    height: 53.h,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Image.asset(
                          LikeWalletImage.icon_button_back_white,
                          height: 53.h,
                        ),
                        Text(
                          'Back to Stores',
                          style: TextStyle(
                            fontFamily: 'Noto Sans',
                            fontSize: 14.sp,
                            color: LikeWalletAppTheme.gray.withOpacity(0.8),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Container(width: 186.w, height: 53.h),
              ],
            ),
          );
  }

  Widget _showSymbol() {
    return AnimatedPositioned(
      top: 480.h,
      right: 42.w,
      height: showsymbol ? 320.h : 0.h,
      duration: const Duration(milliseconds: 200),
      child: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            for (var i = 0; i < transferCtrl.availableSymbols.length; i++)
              Obx(() => transferCtrl.availableSymbols[i] != transferCtrl.symbol.value
                ? GestureDetector(
                  onTap: () {
                    setState(() {
                      // Save symbol to controller and storage
                      transferCtrl.saveSymbol(transferCtrl.availableSymbols[i]);

                      showsymbol = false;
                      showbutton = transferCtrl.amountSend.text.isNotEmpty;
                    });
                  },
                  child: Container(
                    width: 68.w,
                    padding: EdgeInsets.symmetric(vertical: 8.h),
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      border: Border(
                        top: BorderSide(
                          color: LikeWalletAppTheme.gray.withOpacity(0.5),
                          width: 1.2.w,
                        ),
                      ),
                    ),
                    child: Text(
                      transferCtrl.availableSymbols[i],
                      style: TextStyle(
                        fontFamily: 'Proxima Nova',
                        fontSize: 18.sp,
                        color: LikeWalletAppTheme.black,
                        fontWeight: FontWeight.normal,
                        shadows: [
                          Shadow(
                            blurRadius: 0,
                            color: LikeWalletAppTheme.black.withOpacity(0.5),
                          ),
                        ],
                      ),
                    ),
                  ),
                ) : SizedBox.shrink()
              ),
          ],
        ),
      ),
    );
  }

  Widget _favoriteForm() {
    return AnimatedPositioned(
      duration: const Duration(milliseconds: 300),
      bottom: showfavorite ? 0.h : -264.h,
      child: Stack(
        children: [
          Container(
            width: MediaQuery.of(context).size.width,
            height: 264.h,
            child: Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                width: double.infinity,
                height: 240.h,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(10.r),
                    topRight: Radius.circular(10.r),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.16),
                      offset: const Offset(0, -1.2),
                      blurRadius: 2.sp,
                      spreadRadius: 0.4.sp,
                    ),
                  ],
                  color: const Color(0xfFFFFFFF),
                ),
                child: ListView(
                  scrollDirection: Axis.horizontal,
                  children: [
                    _buildFavoriteItem('Alice', '0x1234567890abcdef', null),
                    _buildFavoriteItem('Bob', '0xabcdef1234567890',
                        'https://via.placeholder.com/150'),
                  ],
                ),
              ),
            ),
          ),
          Positioned(
            top: 0.h,
            right: 20.w,
            child: GestureDetector(
              onTap: () => print('Add favorite'),
              child: Image.asset(
                LikeWalletImage.icon_add,
                height: 50.h,
                width: 50.w,
              ),
            ),
          ),
          Positioned(
            top: 0.h,
            right: 80.w,
            child: GestureDetector(
              onTap: () => print('Toggle delete favorites'),
              child: Image.asset(
                LikeWalletImage.icon_delete,
                height: 50.h,
                width: 50.w,
              ),
            ),
          ),
          Positioned(
            top: 40.h,
            left: 20.w,
            child: Row(
              children: [
                Container(
                  width: 8.w,
                  height: 8.h,
                  decoration: const BoxDecoration(
                    color: Color(0xffB4E60D),
                    shape: BoxShape.circle,
                  ),
                ),
                Text(
                  'stores_favorites'.tr,
                  style: TextStyle(
                    fontFamily: 'Proxima Nova',
                    fontSize: 14.sp,
                    color: LikeWalletAppTheme.black,
                    fontWeight: FontWeight.w100,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFavoriteItem(String name, String address, String? photoUrl) {
    return Container(
      padding: EdgeInsets.all(4.sp),
      child: Column(
        children: [
          Padding(padding: EdgeInsets.symmetric(vertical: 28.h)),
          GestureDetector(
            onTap: () => print('Send to favorite address: $address'),
            child: photoUrl == null
                ? CircleAvatar(
                    backgroundColor: Colors.black12.withOpacity(0.1),
                    radius: 50.sp,
                    backgroundImage: AssetImage(LikeWalletImage.icon_user),
                  )
                : CircleAvatar(
                    radius: 50.sp,
                    backgroundImage: NetworkImage(photoUrl),
                  ),
          ),
          Container(
            padding: EdgeInsets.symmetric(vertical: 8.h),
            child: Text(
              name,
              style: TextStyle(
                fontFamily: 'Proxima Nova',
                fontSize: 14.sp,
                color: LikeWalletAppTheme.gray1,
                fontWeight: FontWeight.normal,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _selectInput() {
    return Container(
      height: 46.h,
      width: 374.w,
      decoration: BoxDecoration(
        color: LikeWalletAppTheme.gray6,
        boxShadow: [
          BoxShadow(
            color: LikeWalletAppTheme.black.withOpacity(0.1),
            spreadRadius: 0.4.sp,
            blurRadius: 1.2.sp,
          ),
        ],
        borderRadius: BorderRadius.circular(2.r),
      ),
      child: Row(
        children: [
          Diagonal(
            clipHeight: mediaQuery(context, 'height', 935.38) / 15,
            axis: Axis.vertical,
            position: DiagonalPosition.TOP_RIGHT,
            child: Container(
              width: mediaQuery(context, 'width', 518.67),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(5.0),
                    bottomLeft: Radius.circular(5.0)),
              ),
              child: GestureDetector(
                onTap: () {
                  print("click");

                  transferCtrl.changeSelectedInput('address');
                },
                child: Container(
                  alignment: Alignment.center,
                  height: mediaQuery(context, 'height', 115),
                  width: mediaQuery(context, 'width', 935.38) / 2,
                  child: Text(
                    'bankingSend_to_address'.tr,
                    style: TextStyle(
                      fontFamily: "Proxima Nova",
                      fontSize: mediaQuery(context, 'height', 42),
                      color: const Color(0x993c3c43),
                    ),
                    textAlign: TextAlign.left,
                  ),
                ),
              ),
            ),
          ),
          GestureDetector(
            onTap: () => transferCtrl.changeSelectedInput('noaddress'),
            child: Container(
              alignment: Alignment.center,
              height: 46.h,
              width: 171.w,
              child: Text(
                'bankingSend_to_mobile_number'.tr,
                style: TextStyle(
                  fontFamily: 'Proxima Nova',
                  fontSize: 17.sp,
                  color: const Color(0x993c3c43),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
