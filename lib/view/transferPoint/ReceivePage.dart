import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:likewallet/service/components.dart';
import 'package:likewallet/service/getStorage.dart';
import 'package:qr_flutter/qr_flutter.dart';

class ReceiveLIKEPage extends StatelessWidget {
  final String walletAddress = Storage.get(StorageKeys.addressETH);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(height: 300.h),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 70.w),
          child: Text(
            walletAddress,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontFamily: 'Proxima Nova',
              fontSize: 14.sp,
              color: Colors.grey,
            ),
          ),
        ),
        SizedBox(height: 24.h),
        Container(
          padding: EdgeInsets.all(8.sp),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: QrImageView(
            data: walletAddress,
            size: 230.h,
            embeddedImage:
            AssetImage(LikeWalletImage.qr_likepoint),
            embeddedImageStyle: QrEmbeddedImageStyle(
              size: Size(
                55.h,
                55.h,
              ),
            ),
            errorCorrectionLevel: QrErrorCorrectLevel.Q,
            gapless: false,
            version: 9,
          ),
        ),
        SizedBox(height: 36.h),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _iconAction('assets/image/Save_receipt.png', 'Save\nQR code'),
            _iconAction('assets/image/receive/copy_address.png', 'Address\ncopied!'),
            _iconAction('assets/image/Share_receipt.png', 'Send\nAddress'),
          ],
        ),
      ],
    );
  }

  Widget _iconAction(String assetPath, String label) {
    return Column(
      children: [
        CircleAvatar(
          backgroundColor: Colors.white,
          child: Image.asset(
            assetPath,
            fit: BoxFit.contain,
          ),
        ),
        SizedBox(height: 8.h),
        Text(
          label,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontFamily: 'Proxima Nova',
            fontSize: 12.sp,
            color: Colors.grey.shade700,
          ),
        ),
      ],
    );
  }
}
