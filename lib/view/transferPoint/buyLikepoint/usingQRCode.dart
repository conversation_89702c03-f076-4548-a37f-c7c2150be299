import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:likewallet/controller/buyLikepointController/buyLikepointController.dart';
import 'package:likewallet/service/components.dart';
import 'package:likewallet/view/contactUs/contactUsPage.dart';
import 'package:likewallet/view/navigationBar/mainNavigator.dart';
import 'package:likewallet/view/transferPoint/buyLikepoint/confirmBuyLikepoint.dart';

class BuyLikepointUsingQRCode extends StatefulWidget {
  const BuyLikepointUsingQRCode({super.key});

  @override
  State<BuyLikepointUsingQRCode> createState() => _BuyLikepointUsingQRCodeState();
}

class _BuyLikepointUsingQRCodeState extends State<BuyLikepointUsingQRCode> {

  final BuyLikepointController buyLikepointCtrl = Get.put(BuyLikepointController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: Container(
        height: 0.14.sh,
        width: 1.sw,
        color: const Color(0xffFFFFFF),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            TextButton.icon(
              onPressed: () {
                // AppRoutes.makeFirst(
                //     context, HomeLikewallet(selectPage: 0));
                Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => MainScreen(selectPage: 0)));
              },
              icon: Icon(IconHome.path_43609,
                  size: mediaQuery(context, 'height', 60),
                  color: Color(0xffB3B3B4)),
              label: Container(),
            ),
            TextButton.icon(
              onPressed: () {
                // AppRoutes.makeFirst(
                //     context, HomeLikewallet(selectPage: 1));
                Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => MainScreen(selectPage: 1)));

              },
              icon: Icon(IconHome.path_43608,
                  size: mediaQuery(context, 'height', 60),
                  color: Color(0xffB3B3B4)),
              label: Container(),
            ),
            TextButton.icon(
              onPressed: () {
                // AppRoutes.makeFirst(
                //     context, HomeLikewallet(selectPage: 2));
                Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => MainScreen(selectPage: 2)));

              },
              icon: Icon(IconHome.group_24548,
                  size: mediaQuery(context, 'height', 60),
                  color: Color(0xffB3B3B4)),
              label: Container(),
            ),
            TextButton.icon(
              onPressed: () {
                /// ย้ายไปไลน์ โอ๋เอ๋

                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) =>
                            ContactUsPage()));
              },
              icon: Icon(IconHome.path_58781,
                  size: mediaQuery(context, 'height', 60),
                  color: Color(0xffB3B3B4)),
              label: Container(),
            ),
          ],
        ),
      ),
      body: SingleChildScrollView(
        child: GestureDetector(
            onTap: () {
              FocusScopeNode currentFocus = FocusScope.of(context);
              if (!currentFocus.hasPrimaryFocus) {
                currentFocus.unfocus();
              }
              buyLikepointCtrl.hideSymbolDropdown();
            },
            child: Stack(
              alignment: Alignment.topLeft,
              clipBehavior: Clip.none,
              fit: StackFit.passthrough,
              children: [
                _buy_like(context),
                _head(context),
                // _showSymbol(),
                _buttonNext(context),
              ],
            )),
      ),
    );
  }

  Widget _head(context) {
    return Stack(
      children: [
        Container(
          color: const Color(0xff141322),
          height: 97.h,
          child: Container(
            padding: EdgeInsets.only(bottom: 12.5.h),
            alignment: Alignment.bottomCenter,
            width: 1.sw,
            child: Text(
              'buylike_CONFIRM'.tr,
              style: TextStyle(
                  letterSpacing: 0.5,
                  fontFamily: 'Proxima Nova',
                  color: const Color(0xffFFFFFF).withOpacity(1),
                  fontSize: 14.sp,
                  fontWeight: FontWeight.normal),
            ),
          ),
        ),
        Container(
          height: 1.sh,
        ),
        Positioned(
          top: 0.086.sh,
          child: GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Container(
                height: 0.05.sh,
                width: 0.16.sw,
                decoration: BoxDecoration(
                  color: const Color(0xffB4E60D),
                  borderRadius: const BorderRadius.only(
                      bottomRight: Radius.circular(40.0),
                      topRight: Radius.circular(40.0)),
                  boxShadow: [
                    BoxShadow(
                      spreadRadius: 0,
                      blurRadius: 9,
                      color: const Color(0xff707071).withOpacity(0.1),
                      offset: const Offset(
                        0.0,
                        3.0,
                      ),
                    ),
                  ],
                ),
                alignment: Alignment.centerLeft,
                // margin: EdgeInsets.only(left: ),
                padding: EdgeInsets.symmetric(
                    vertical: mediaQuery(context, 'height', 32.2),
                    horizontal: mediaQuery(context, 'width', 71.3)),
                child: Image.asset(
                  height: 0.08.sh,
                  width: 0.17.sw,
                  LikeWalletImage.icon_back_button,
                )),
          ),
        ),
      ],
    );
  }

  Widget _buy_like(context) {
    return Align(
      alignment: Alignment.center,
      child: Container(
        padding: EdgeInsets.only(
          top: 125.h,
        ),
        height: 1.sh,
        width: 1.sw,
        decoration: const BoxDecoration(
          color: Color(0xffF5F5F5),
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            stops: [0.0, 0.2, 0.5],
            colors: [
              Colors.white,
              Colors.white,
              LikeWalletAppTheme.white1
            ],
          ),
        ),
        child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ///Text Amount
              Container(
                  // height: 825.h,
                  padding: EdgeInsets.only(bottom: 20.h),
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      Container(
                        margin: EdgeInsets.only(
                            top: 18.h,
                            left: 37.5.h),
                        child: Column(
                          children: [
                            _amountLike(),
                            SizedBox(
                              height: 10.h,
                            ),
                            _exchangeRate(),
                            SizedBox(
                              height: 10.h,
                            ),
                          ],
                        ),
                      ),
                      Positioned(
                        right: 135.w,
                        child: _iconEqual(),
                      ),

                    ],
                  )),
            ]),
      ),
    );
  }

  /// Amount input field
  Widget _amountLike() {
    return Container(
      padding: EdgeInsets.only(
        left: 0.w,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.only(
              top: 12.h,
              left: 28.w,
            ),
            width: 255.w,
            height: 105.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8.h),
              color: LikeWalletAppTheme.white,
              boxShadow: [
                const BoxShadow(
                  color: Color(0x1a000000),
                  offset: Offset(1, 1),
                  blurRadius: 7,
                  spreadRadius: 1,
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  margin: EdgeInsets.only(bottom: 3.h),
                  child: Text(
                    'buylike_amount'.tr,
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      fontSize: 14.h,
                      color: LikeWalletAppTheme.gray1,
                      letterSpacing: 0.3,
                    ),
                    textAlign: TextAlign.left,
                  ),
                ),
                Container(
                  alignment: Alignment.centerLeft,
                  height: 33.h,
                  child: TextFormField(
                    onTap: () {
                      buyLikepointCtrl.hideSymbolDropdown();
                    },
                    onChanged: (value) {
                      // Controller handles this via listener
                      buyLikepointCtrl.showButton();
                    },
                    inputFormatters: [
                      // CurrencyTextInputFormatter(
                      //   decimalDigits: 0,
                      // )
                    ],
                    keyboardType: TextInputType.number,
                    textAlignVertical: TextAlignVertical.center,
                    textAlign: TextAlign.start,
                    style: TextStyle(
                        fontSize: 30.h,
                        fontFamily: 'Proxima Nova',
                        color: LikeWalletAppTheme.gray1),
                    controller: buyLikepointCtrl.amountSend,
                    focusNode: buyLikepointCtrl.amountFocusNode,
                    decoration: InputDecoration(
                      focusedBorder: InputBorder.none,
                      enabledBorder: InputBorder.none,
                      hintText: '0',
                      hintStyle: TextStyle(
                          fontSize: 30.h,
                          fontFamily: 'Proxima Nova',
                          color: LikeWalletAppTheme.gray),
                      contentPadding: EdgeInsets.only(
                        bottom: 10.h,
                      ),
                    ),
                  ),
                ),
                Obx(() => Container(
                  margin: EdgeInsets.only(top: 5.h),
                  child: Text(
                    buyLikepointCtrl.Symbol.value == 'THB'
                        ? 'symbol_th'.tr
                        : buyLikepointCtrl.Symbol.value == 'USD'
                        ? 'symbol_us'.tr
                        : buyLikepointCtrl.Symbol.value == 'LAK'
                        ? 'symbol_lak'.tr
                        : buyLikepointCtrl.Symbol.value == 'VND'
                        ? 'symbol_vn'.tr
                        : buyLikepointCtrl.Symbol.value == 'GOLD'
                        ? 'symbol_gold'.tr
                        : buyLikepointCtrl.Symbol.value == 'LIKE'
                        ? 'symbol_like'.tr
                        : 'symbol_th'.tr,
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      fontSize: 14.h,
                      color: LikeWalletAppTheme.gray1,
                    ),
                    textAlign: TextAlign.left,
                  ),
                )),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Exchange rate display
  Widget _exchangeRate() {
    return Container(
      padding: EdgeInsets.only(
        left: 0.w,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.only(
              top: 12.h,
              left: 28.w,
            ),
            width: 255.w,
            height: 105.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8.h),
              color: LikeWalletAppTheme.white,
              boxShadow: [
                const BoxShadow(
                  color: Color(0x1a000000),
                  offset: Offset(1, 1),
                  blurRadius: 7,
                  spreadRadius: 1,
                ),
              ],
            ),
            child: Stack(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      // margin: EdgeInsets.only(bottom: 3.h),
                      child: Text(
                        'buylike_get'.tr,
                        style: TextStyle(
                          fontFamily: 'Proxima Nova',
                          fontSize: 14.h,
                          color: LikeWalletAppTheme.gray1,
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    Container(
                        margin: EdgeInsets.only(bottom: 8.h),
                        alignment: Alignment.centerLeft,
                        height: 33.h,
                        child: Obx(() => Text(
                          buyLikepointCtrl.amountValue.value == ''
                              ? '0'
                              : buyLikepointCtrl.validateFirstNumber(buyLikepointCtrl.amountValue.value) == true
                              ? buyLikepointCtrl.f
                              .format((double.parse(buyLikepointCtrl.amountValue.value) * buyLikepointCtrl.rate.value))
                              .toString()
                              : buyLikepointCtrl.amountValue.value.substring(0, 1) == '.'
                              ? buyLikepointCtrl.f.format(
                              (double.parse('0' + buyLikepointCtrl.amountValue.value) *
                                  buyLikepointCtrl.rate.value))
                              : '0',
                          style: TextStyle(
                              fontSize: 30.h,
                              fontFamily: 'Proxima Nova',
                              color: LikeWalletAppTheme.gray1),
                        ))),
                    Container(
                      child: Text(
                        'buylike_likepoint'.tr,
                        style: TextStyle(
                          fontFamily: 'Proxima Nova',
                          fontSize: 14.h,
                          color: LikeWalletAppTheme.gray1,
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),

                  ],
                ),
                Positioned(
                  right: 10.w,
                  bottom: 5.h,
                  child: Container(
                    child: Text(
                      'LIKE',
                      style: TextStyle(
                        fontFamily: 'Proxima Nova',
                        fontSize: 27.h,
                        color: LikeWalletAppTheme.black,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.left,
                    ),
                  ),
                ),
              ],
            )
          ),
        ],
      ),
    );
  }

  Widget _iconEqual() {
    return Image.asset(
      LikeWalletImage.icon_equal,
      height: 90.h,
    );
  }

  /// Currency symbol dropdown
  Widget _showSymbol() {
    return Obx(() => AnimatedPositioned(
        top: 150.h,
        right: 70.w,
        height: buyLikepointCtrl.showsymbol.value ? 400.h : 0.h,
        duration: const Duration(milliseconds: 200),
        child: SingleChildScrollView(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                for (var i = 0; i < buyLikepointCtrl.symbol.length; i++)
                  if (buyLikepointCtrl.symbol[i] != buyLikepointCtrl.Symbol.value)
                    GestureDetector(
                        onTap: () {
                          buyLikepointCtrl.changeCurrency(buyLikepointCtrl.symbol[i]);
                        },
                        child: Container(
                            width: 85.w,
                            padding: EdgeInsets.only(
                              top: 17.5.w,
                              bottom: 17.5.w,
                            ),
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              border: Border(
                                top: BorderSide(
                                  color: LikeWalletAppTheme.gray.withOpacity(0.5),
                                  width: 1.5.w,
                                ),
                              ),
                            ),
                            child: Text(
                              buyLikepointCtrl.symbol[i],
                              style: TextStyle(
                                  fontSize: 22.5.h,
                                  color: LikeWalletAppTheme.black,
                                  fontWeight: FontWeight.normal,
                                  fontFamily: 'Proxima Nova',
                                  shadows: [
                                    Shadow(
                                      blurRadius: 0,
                                      color:
                                      LikeWalletAppTheme.black.withOpacity(0.5),
                                      offset: const Offset(0.0, 0.0),
                                    ),
                                  ]),
                            )))
              ],
            ))));
  }

  /// Next button
  Widget _buttonNext(context) {
    return Obx(() => AnimatedPositioned(
        top: buyLikepointCtrl.showbutton.value ? 0.45.sh : 2.sh,
        duration: const Duration(milliseconds: 500),
        child: Container(
          width: 1.sw,
          padding: EdgeInsets.only(
            left: 30.w,
            right: 30.w,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              GestureDetector(
                onTap: () {
                  buyLikepointCtrl.resetForm();
                },
                child: Container(
                    height: 66.5.h,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Image.asset(
                          LikeWalletImage.icon_button_cancel_white,
                          height: 62.h,
                        ),
                        Text(
                          'buylike_cancel'.tr,
                          style: TextStyle(
                              fontFamily: 'Proxima Nova',
                              fontSize: 16.h,
                              color: LikeWalletAppTheme.gray.withOpacity(0.8)),
                          textAlign: TextAlign.right,
                        )
                      ],
                    )),
              ),
              GestureDetector(
                onTap: () {
                  // buyLikepointCtrl.navigateToConfirmation(context);
                  Get.to(() => Confirmbuylikepoint());
                },
                child: Container(
                    height: 66.5.h,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Text(
                          'buylike_next'.tr,
                          style: TextStyle(
                              fontFamily: 'Proxima Nova',
                              fontSize: 16.h,
                              color: LikeWalletAppTheme.gray.withOpacity(0.8)),
                          textAlign: TextAlign.right,
                        ),
                        Image.asset(
                          LikeWalletImage.icon_button_next_black,
                          height: 62.h,
                        ),
                      ],
                    )),
              ),
            ],
          ),
        )));
  }
}

