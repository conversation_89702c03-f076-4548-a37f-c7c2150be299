import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:likewallet/controller/buyLikepointController/buyLikepointController.dart';
import 'package:likewallet/service/components.dart';
import 'package:likewallet/view/contactUs/contactUsPage.dart';
import 'package:likewallet/view/navigationBar/mainNavigator.dart';

class UploadBankSlip extends StatefulWidget {
  const UploadBankSlip({super.key});

  @override
  State<UploadBankSlip> createState() => _UploadBankSlipState();
}

class _UploadBankSlipState extends State<UploadBankSlip> {
  // Controller
  late final BuyLikepointController buyLikepointCtrl;

  @override
  void initState() {
    super.initState();
    if (Get.isRegistered<BuyLikepointController>()) {
      buyLikepointCtrl = Get.find<BuyLikepointController>();
    } else {
      buyLikepointCtrl = Get.put(BuyLikepointController());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: Container(
        height: 0.14.sh,
        width: 1.sw,
        color: const Color(0xffFFFFFF),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            TextButton.icon(
              onPressed: () {
                // AppRoutes.makeFirst(
                //     context, HomeLikewallet(selectPage: 0));
                Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => MainScreen(selectPage: 0)));
              },
              icon: Icon(IconHome.path_43609,
                  size: mediaQuery(context, 'height', 60),
                  color: Color(0xffB3B3B4)),
              label: Container(),
            ),
            TextButton.icon(
              onPressed: () {
                // AppRoutes.makeFirst(
                //     context, HomeLikewallet(selectPage: 1));
                Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => MainScreen(selectPage: 1)));

              },
              icon: Icon(IconHome.path_43608,
                  size: mediaQuery(context, 'height', 60),
                  color: Color(0xffB3B3B4)),
              label: Container(),
            ),
            TextButton.icon(
              onPressed: () {
                // AppRoutes.makeFirst(
                //     context, HomeLikewallet(selectPage: 2));
                Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => MainScreen(selectPage: 2)));

              },
              icon: Icon(IconHome.group_24548,
                  size: mediaQuery(context, 'height', 60),
                  color: Color(0xffB3B3B4)),
              label: Container(),
            ),
            TextButton.icon(
              onPressed: () {
                /// ย้ายไปไลน์ โอ๋เอ๋

                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) =>
                            ContactUsPage()));
              },
              icon: Icon(IconHome.path_58781,
                  size: mediaQuery(context, 'height', 60),
                  color: Color(0xffB3B3B4)),
              label: Container(),
            ),
          ],
        ),
      ),
      body: SingleChildScrollView(
        child: GestureDetector(
            onTap: () {
              FocusScopeNode currentFocus = FocusScope.of(context);
              if (!currentFocus.hasPrimaryFocus) {
                currentFocus.unfocus();
              }
              buyLikepointCtrl.hideSymbolDropdown();
            },
            child: Stack(
              alignment: Alignment.topLeft,
              clipBehavior: Clip.none,
              fit: StackFit.passthrough,
              children: [
                _body(),
                _head(context),
              ],
            )),
      ),
    );
  }

  Widget _head(context) {
    return Stack(
      children: [
        Container(
          color: const Color(0xff141322),
          height: 97.h,
          child: Container(
            padding: EdgeInsets.only(bottom: 12.5.h),
            alignment: Alignment.bottomCenter,
            width: 1.sw,
            child: Text(
              'buylike_bank1'.tr,
              style: TextStyle(
                  fontWeight: FontWeight.w700,
                  fontFamily: 'Proxima Nova',
                  color: const Color(0xffFFFFFF).withOpacity(1),
                  fontSize: 16.sp,
              ),
            ),
          ),
        ),
        Container(
          height: 1.sh,
        ),
        Positioned(
          top: 0.086.sh,
          child: GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Container(
                height: 0.05.sh,
                width: 0.16.sw,
                decoration: BoxDecoration(
                  color: const Color(0xffB4E60D),
                  borderRadius: const BorderRadius.only(
                      bottomRight: Radius.circular(40.0),
                      topRight: Radius.circular(40.0)),
                  boxShadow: [
                    BoxShadow(
                      spreadRadius: 0,
                      blurRadius: 9,
                      color: const Color(0xff707071).withOpacity(0.1),
                      offset: const Offset(
                        0.0,
                        3.0,
                      ),
                    ),
                  ],
                ),
                alignment: Alignment.centerLeft,
                // margin: EdgeInsets.only(left: ),
                padding: EdgeInsets.symmetric(
                    vertical: mediaQuery(context, 'height', 32.2),
                    horizontal: mediaQuery(context, 'width', 71.3)),
                child: Image.asset(
                  height: 0.08.sh,
                  width: 0.17.sw,
                  LikeWalletImage.icon_back_button,
                )),
          ),
        ),
      ],
    );
  }

  Widget _body(){
    return Obx(() => Stack(
      children: [
        Container(
          height: 1.sh,
          width: 1.sw,
          color: Color(0xffF5F5F5),
          child: Column(
            children: [
              SizedBox(height: 0.17.sh),
              Text(
                'buylike_bank2'.tr,
                style: TextStyle(
                  fontWeight: FontWeight.w700,
                  fontSize: 16.sp,
                  fontFamily: 'Proxima Nova',
                  color: LikeWalletAppTheme.gray4.withOpacity(1),
                ),
              ),
              SizedBox(height: 32.h),
              Text(
                'buylike_bank3'.tr,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 14.sp,
                  fontFamily: 'Proxima Nova',
                  color: LikeWalletAppTheme.gray.withOpacity(0.3),
                ),
              ),
              Text(
                buyLikepointCtrl.nameAcc.value,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 18.sp,
                  fontFamily: 'Proxima Nova',
                  color: LikeWalletAppTheme.gray,
                ),
              ),
              SizedBox(height: 20.h),
              Text(
                'buylike_bank4'.tr,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 14.sp,
                  fontFamily: 'Proxima Nova',
                  color: LikeWalletAppTheme.gray.withOpacity(0.3),
                ),
              ),
              Text(
                buyLikepointCtrl.bankAcc.value,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 18.sp,
                  fontFamily: 'Proxima Nova',
                  color: LikeWalletAppTheme.gray,
                ),
              ),
              SizedBox(height: 20.h),
              Text(
                'buylike_bank5'.tr,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 14.sp,
                  fontFamily: 'Proxima Nova',
                  color: LikeWalletAppTheme.gray.withOpacity(0.3),
                ),
              ),
              Text(
                buyLikepointCtrl.numAcc.value,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 18.sp,
                  fontFamily: 'Proxima Nova',
                  color: LikeWalletAppTheme.gray,
                ),
              ),
              SizedBox(height: 40.h),
              ///Button Upload
              _buttonUpload(),

              ///Button Done
              buyLikepointCtrl.isSlipUploaded.value ? _buttonDone() : Container(),
            ],
          ),
        ),

        // Loading overlay
        if (buyLikepointCtrl.isUploading.value)
          Container(
            height: 1.sh,
            width: 1.sw,
            color: Colors.black.withOpacity(0.5),
            child: Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(LikeWalletAppTheme.bule1),
              ),
            ),
          ),
      ],
    ));
  }

  Widget _buttonUpload() {
    return Obx(() => Container(
        width: 330.w,
        height: 87.h,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(43.5),
          color: LikeWalletAppTheme.white,
          boxShadow: [
            BoxShadow(
              color: LikeWalletAppTheme.gray.withOpacity(0.3),
              offset: Offset(0, 2.h),
              blurRadius: 3.h,
            ),
          ],
        ),
        child: Stack(
          children: <Widget>[
            Container(
                alignment: Alignment.center,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    Container(
                      margin: EdgeInsets.only(
                        left: 24.w,
                      ),
                      child: Text(
                        buyLikepointCtrl.isSlipUploaded.value
                            ? 'buylike_bank7'.tr
                            : 'buylike_bank6'.tr,
                        style: TextStyle(
                          letterSpacing: 0,
                          fontWeight: FontWeight.bold,
                          fontSize: 17.sp,
                          fontFamily: 'Proxima Nova',
                          color: LikeWalletAppTheme.gray.withOpacity(1),
                        ),
                      ),
                    ),
                    buyLikepointCtrl.isSlipUploaded.value ? _buttonBack() : Container(),
                    _buttonOK(),
                  ],
                )),
          ],
        )));
  }

  Widget _buttonOK() {
    return Obx(() => GestureDetector(
        onTap: () {
          if (buyLikepointCtrl.isSlipUploaded.value) {
            // If image is already uploaded, submit the transaction
            Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => MainScreen(selectPage: 0)));
          } else {
            // If no image uploaded yet, pick an image
            buyLikepointCtrl.pickImage();
          }
        },
        child: buyLikepointCtrl.isSlipUploaded.value
            ? Container(
                margin: EdgeInsets.only(
                  right: 18.w,
                ),
                alignment: Alignment.center,
                height: 53.h,
                width: 53.h,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: LikeWalletAppTheme.lemon1,
                ),
                child: Image.asset(
                  LikeWalletImage.icon_success,
                  height: 17.h,
                ))
            : Container(
                margin: EdgeInsets.only(
                  right: 18.w,
                ),
                alignment: Alignment.center,
                height: 53.h,
                width: 53.h,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: LikeWalletAppTheme.white,
                  boxShadow: [
                    BoxShadow(
                      color: LikeWalletAppTheme.gray.withOpacity(0.3),
                      offset: Offset(0, 1),
                      spreadRadius: 0,
                      blurRadius: 3,
                    ),
                  ],
                ),
                child: Image.asset(
                  LikeWalletImage.contact_us_photo,
                  height: 22.h,
                ))));
  }

  Widget _buttonBack() {
    return GestureDetector(
      onTap: () {
        // Reset upload status
        buyLikepointCtrl.resetUpload();
      },
      child: Container(
        alignment: Alignment.centerRight,
        width: 43.w,
        child: Image.asset(
          LikeWalletImage.icon_back,
          height: 9.h,
        ),
      ),
    );
  }

  Widget _buttonDone() {
    return Container(
      margin: EdgeInsets.only(top: 40.h),
      child: GestureDetector(
        onTap: () {
          // Submit the transaction
          Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => MainScreen(selectPage: 0)));
        },
        child: Container(
          alignment: Alignment.center,
          height: 60.h,
          width: 60.h,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Color(0xff2B3038).withOpacity(1),
            boxShadow: [
              BoxShadow(
                color: LikeWalletAppTheme.gray.withOpacity(0.5),
                offset: Offset(0, 1),
                spreadRadius: 1,
                blurRadius: 5,
              ),
            ],
          ),
          child: Text(
            'buylike_bank_done'.tr,
            style: TextStyle(
              color: Color(0xffB4E60D),
              fontFamily: 'Proxima Nova',
              fontWeight: FontWeight.normal,
              fontSize: 14.sp,
            ),
          ),
        ),
      ),
    );
  }
}
