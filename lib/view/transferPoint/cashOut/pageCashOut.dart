import 'dart:async';
import 'package:delayed_display/delayed_display.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' as formatIntl;
import 'package:likewallet/controller/profile/profileController.dart';
import 'package:likewallet/service/components.dart';


class PageCashOut extends StatefulWidget {
  const PageCashOut({Key? key}) : super(key: key);

  @override
  State<PageCashOut> createState() => _PageCashOutState();
}

class _PageCashOutState extends State<PageCashOut> with TickerProviderStateMixin {
  List<bool> permission = [false, false, false, false];
  String selectedAsset = 'LIKE';
  String selectedCurrency = 'THB - Thai Baht';
  double likeAmount = 0.00;
  double thbAmount = 0.00;
  // late CheckAbout checkAbout;
  late TabController _tabController;
  final f = formatIntl.NumberFormat("###,###.##");

  final TextEditingController _amountController = TextEditingController();

  final profileCtrl = Get.find<ProfileController>();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    // Placeholder for checkPermission
    // checkPermission(); // Uncomment and implement if needed
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: LikeWalletAppTheme.white1,
      body: Column(
        children: [
          Container(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height * 0.3,
            decoration: BoxDecoration(
              color: Color(0xFF2B2A38),
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(30),
                bottomRight: Radius.circular(30),
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  height: 190.h,
                  width: MediaQuery.of(context).size.width * 0.88,
                  alignment: Alignment.bottomCenter,
                  child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Image.asset(LikeWalletImage.icon_title_name, height: 72.07.h),
                        SizedBox(width: 26.w),
                        Text(
                          "${profileCtrl.resProfile["firstName"]} ${profileCtrl.resProfile["lastName"]}",
                          style: TextStyle(
                            fontFamily: 'Proxima Nova',
                            fontSize: 39.h,
                            color: const Color(0x4dffffff),
                            // letterSpacing: 1.17.,
                          ),
                          textAlign: TextAlign.left,
                        ),
                      ]),
                ),
                Container(
                  width: MediaQuery.of(context).size.width * 0.88,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        width: MediaQuery.of(context).size.width * 0.5,
                        height: MediaQuery.of(context).size.height * 0.06,
                        // color: Colors.green.withOpacity(0.5),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                Text(
                                  '9,960,754,048.09',
                                  style: TextStyle(
                                    fontFamily: 'Proxima Nova',
                                    fontSize: 20,
                                    color: Colors.white,
                                    // letterSpacing: 1.17,
                                  ),
                                  textAlign: TextAlign.left,
                                ),
                                SizedBox(width: 10.w),
                                Text(
                                  'LIKE',
                                  style: TextStyle(
                                    fontFamily: 'Proxima Nova',
                                    fontSize: 17,
                                    color: Colors.white,
                                    // letterSpacing: 1.17,
                                  ),
                                  textAlign: TextAlign.left,
                                ),
                              ],
                            ),
                            Text(
                              '= 99,607,540 THB',
                              style: TextStyle(
                                fontFamily: 'Proxima Nova',
                                fontSize: 17,
                                color: Colors.white.withOpacity(0.5),
                                // letterSpacing: 1.17,
                              ),
                              textAlign: TextAlign.left,
                            ),
                          ],
                        ),
                      ),
                      Container(
                        width: MediaQuery.of(context).size.width * 0.34,
                        height: MediaQuery.of(context).size.height * 0.06,
                        padding: EdgeInsets.only(left: 18.w),
                        decoration: BoxDecoration(
                          // color: Colors.red,
                          border: Border(
                            left: BorderSide(
                              color: Colors.white.withOpacity(0.5),
                              width: 1.0,
                            ),
                          ),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  '0.05271',
                                  style: TextStyle(
                                    fontFamily: 'Proxima Nova',
                                    fontSize: 14,
                                    color: Colors.white.withOpacity(0.5),
                                  ),
                                  textAlign: TextAlign.left,
                                ),
                                Text(
                                  'BTC',
                                  style: TextStyle(
                                    fontFamily: 'Proxima Nova',
                                    fontSize: 14,
                                    color: Colors.white.withOpacity(0.5),
                                  ),
                                  textAlign: TextAlign.left,
                                ),
                              ],
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                '4.75271',
                                style: TextStyle(
                                  fontFamily: 'Proxima Nova',
                                  fontSize: 14,
                                  color: Colors.white.withOpacity(0.5),
                                ),
                                textAlign: TextAlign.left,
                              ),
                                Text(
                                  'G(gram)',
                                  style: TextStyle(
                                    fontFamily: 'Proxima Nova',
                                    fontSize: 14,
                                    color: Colors.white.withOpacity(0.5),
                                  ),
                                  textAlign: TextAlign.left,
                                ),
                              ],
                            )
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  width: MediaQuery.of(context).size.width,
                  height: 160.h,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(84.0),
                    color: LikeWalletAppTheme.bule2_4,
                    boxShadow: [
                      BoxShadow(
                        color: LikeWalletAppTheme.black.withOpacity(0.35),
                        offset: const Offset(0, -1),
                        spreadRadius: 2,
                        blurRadius: 5,
                      ),
                    ],
                  ),
                  child: tab(),
                ),
              ],
            )
          ),
          SizedBox(height: 50.h),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                Container(
                  color: Colors.green,
                ),
                Container(),
                // Receive(),
                Container(
                  color: Colors.red,
                ),
                _buildBuyTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget tab() {
    return TabBar(
      controller: _tabController,
      isScrollable: true,
      indicatorColor: Colors.yellow,
      labelStyle: TextStyle(
        fontSize: 42.sp,
        fontFamily: "Proxima Nova",
        fontWeight: FontWeight.bold,
      ),
      unselectedLabelStyle: TextStyle(
        fontSize: 42.sp,
        fontFamily: "Proxima Nova",
        fontWeight: FontWeight.normal,
      ),
      labelColor: LikeWalletAppTheme.white,
      indicator: BoxDecoration(
        color: LikeWalletAppTheme.lemon,
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          stops: const [0.0, 1],
          colors: [
            LikeWalletAppTheme.lemon.withOpacity(1),
            LikeWalletAppTheme.lemon.withOpacity(0.7),
          ],
        ),
        borderRadius: BorderRadius.circular(84),
      ),
      tabs: [
        Tab(
          child: Container(
            height: 129.75.h,
            width: 160.w,
            alignment: Alignment.center,
            child: Text(
              'banking_send'.tr,
              maxLines: 1,
            ),
          ),
        ),
        Tab(
          child: Container(
            height: 129.75.h,
            width: 160.w,
            alignment: Alignment.center,
            child: Text(
              'banking_receive'.tr,
              maxLines: 1,
            ),
          ),
        ),
        Tab(
          child: Container(
            height: 129.75.h,
            width: 180.w,
            alignment: Alignment.center,
            child: Text(
              'banking_buy'.tr,
              maxLines: 1,
            ),
          ),
        ),
        Tab(
          child: Container(
            height: 129.75.h,
            width: 160.w,
            alignment: Alignment.center,
            child: Text(
              'banking_cash'.tr,
              maxLines: 1,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBuyTab() {
    return Container(
      width: MediaQuery.of(context).size.width,
      height: MediaQuery.of(context).size.height,
      alignment: Alignment.topCenter,
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.6,
        decoration: BoxDecoration(
          color: LikeWalletAppTheme.white,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.8,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                width: MediaQuery.of(context).size.width * 0.8,
                height: MediaQuery.of(context).size.height * 0.07,
                alignment: Alignment.centerLeft,
                child: Text(
                  'Select assets',
                  style: TextStyle(fontSize: 16, color: Color(0xFF1A1818)),
                ),
              ),
              SizedBox(height: 8.h),
              Container(
                width: MediaQuery.of(context).size.width * 0.8,
                height: MediaQuery.of(context).size.height * 0.07,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  color: Color(0xFFFFFFFF),
                  border: Border.all(
                      width: mediaQuery(context, 'width', 1),
                      color: Color(0xFF000000).withOpacity(0.2)
                  )
                ),
                  alignment: Alignment.center,
                  child: Container(
                    width: MediaQuery.of(context).size.width * 0.75,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Image.asset(
                            'assets/icon/icon_circle_black.png',
                            width: 30,
                            height: 30,
                          ),
                          SizedBox(
                            width: 10,
                          ),
                          Text(
                            'Like',
                            style: TextStyle(fontSize: 16, color: Color(0xFF1A1818)),
                          ),
                        ],
                      ),
                      Icon(Icons.arrow_drop_down),
                    ],
                  ),
                )
              ),
              SizedBox(height: 16.h),
              Container(
                width: MediaQuery.of(context).size.width * 0.8,
                height: MediaQuery.of(context).size.height * 0.12,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    // color: Color(0xFFFFFFFF),
                    // color: Colors.red.withOpacity(0.5),
                    border: Border.all(
                        width: mediaQuery(context, 'width', 1),
                        color: Color(0xFF000000).withOpacity(0.2)
                    )
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      width: MediaQuery.of(context).size.width * 0.75,
                      // color: Colors.red.withOpacity(0.5),
                      alignment: Alignment.centerLeft,
                      child: Container(
                        width: 55,
                        height: 28,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(
                              color: Color(0xFFB8D54C)
                          )
                        ),
                        alignment: Alignment.center,
                        child: Text(
                          'Max',
                          style: TextStyle(fontSize: 16, color: Color(0xFFB8D54C)),
                        ),
                      ),
                    ),
                    Container(
                      width: MediaQuery.of(context).size.width * 0.75,
                      // height: MediaQuery.of(context).size.height * 0.05,
                      // color: Colors.red.withOpacity(0.5),
                      child: Stack(
                        children: [
                          Positioned(
                            bottom: 0,
                            child: Container(
                              width: MediaQuery.of(context).size.width * 0.75,
                              child: Divider(
                                color: Color(0xFF000000).withOpacity(0.2),
                                thickness: 1,
                              ),
                            ),
                          ),
                          if (_amountController.text == '' )
                            Positioned(
                              left: 0,
                              bottom: MediaQuery.of(context).size.height * 0.02,
                              child: Text(
                                "Pass Amount",
                                style: TextStyle(fontSize: 16, color: Color(0xFF1A1818).withOpacity(0.2)),
                              ),
                            ),
                          Container(
                            width: MediaQuery.of(context).size.width * 0.75,
                            child: Row(
                              children: [
                                Expanded(
                                  child: TextField(
                                    controller: _amountController,
                                    textAlign: TextAlign.end, // ทำให้ข้อความที่ป้อนและ hint ชิดขวา
                                    decoration: InputDecoration(
                                      // isDense: true,
                                      border: InputBorder.none,

                                      hintText: '0.00',
                                      hintStyle: TextStyle(
                                        fontSize: 19,
                                        color: Color(0xFF000000).withOpacity(0.2),
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    keyboardType: TextInputType.number,
                                    onChanged: (value) {


                                      setState(() {});
                                    },
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Text('LIKE', style: TextStyle(
                                  fontSize: 16,
                                  color: Color(0xFF000000).withOpacity(0.2),

                                ),), // คำว่า LIKE จะอยู่ถาวร
                              ],
                            ),
                          ),
                        ],
                      )
                    )
                  ],
                )
              ),
              SizedBox(height: 16.h),
              Container(
                width: MediaQuery.of(context).size.width * 0.8,
                height: MediaQuery.of(context).size.height * 0.05,
                alignment: Alignment.centerLeft,
                child: Text(
                  'Select currency',
                  style: TextStyle(fontSize: 16, color: Color(0xFF1A1818)),
                ),
              ),
              SizedBox(height: 8.h),
              Container(
                  width: MediaQuery.of(context).size.width * 0.8,
                  height: MediaQuery.of(context).size.height * 0.05,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: Color(0xFFFFFFFF),
                      border: Border.all(
                          width: mediaQuery(context, 'width', 1),
                          color: Color(0xFF000000).withOpacity(0.2)
                      )
                  ),
                  alignment: Alignment.center,
                  child: Container(
                    width: MediaQuery.of(context).size.width * 0.75,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'THB - Thai Baht',
                          style: TextStyle(fontSize: 16, color: Color(0xFF1A1818)),
                        ),
                        Icon(Icons.arrow_drop_down),
                      ],
                    ),
                  )
              ),
              SizedBox(height: 16.h),
              Container(
                  width: MediaQuery.of(context).size.width * 0.8,
                  height: MediaQuery.of(context).size.height * 0.12,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: Color(0xFFFFFFFF),
                      border: Border.all(
                          width: mediaQuery(context, 'width', 1),
                          color: Color(0xFF000000).withOpacity(0.2)
                      )
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        width: 55,
                        height: 28,
                      ),
                      Container(
                          width: MediaQuery.of(context).size.width * 0.75,
                          // height: MediaQuery.of(context).size.height * 0.05,
                          // color: Colors.red.withOpacity(0.5),
                          child: Stack(
                            children: [
                              Positioned(
                                bottom: 0,
                                child: Container(
                                  width: MediaQuery.of(context).size.width * 0.75,
                                  child: Divider(
                                    color: Color(0xFF000000).withOpacity(0.2),
                                    thickness: 1,
                                  ),
                                ),
                              ),
                              if (_amountController.text == '' )
                                Positioned(
                                  left: 0,
                                  bottom: MediaQuery.of(context).size.height * 0.02,
                                  child: Text(
                                    "Pass Amount",
                                    style: TextStyle(fontSize: 16, color: Color(0xFF1A1818).withOpacity(0.2)),
                                  ),
                                ),
                              Container(
                                width: MediaQuery.of(context).size.width * 0.75,
                                child: Row(
                                  children: [
                                    Expanded(
                                      child: TextField(
                                        controller: _amountController,
                                        textAlign: TextAlign.end, // ทำให้ข้อความที่ป้อนและ hint ชิดขวา
                                        decoration: InputDecoration(
                                          border: InputBorder.none,
                                          hintText: '0.00',
                                          hintStyle: TextStyle(
                                            fontSize: 19,
                                            color: Color(0xFF000000).withOpacity(0.2),
                                            fontWeight: FontWeight.bold,
                                          ),
                                          // contentPadding: const EdgeInsets.symmetric(horizontal: 0, vertical: 10),
                                        ),
                                        keyboardType: TextInputType.number,
                                        onChanged: (value) {
                                          setState(() {});
                                        },
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Text('Baht', style: TextStyle(
                                      fontSize: 16,
                                      color: Color(0xFF000000).withOpacity(0.2),
                                    ),), // คำว่า LIKE จะอยู่ถาวร
                                  ],
                                ),
                              ),
                            ],
                          )
                      )
                    ],
                  )
              ),
              SizedBox(height: 16.h),
              Container(
                width: MediaQuery.of(context).size.width * 0.75,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('FEE', style: TextStyle(fontSize: 16, color: Color(0xFF1A1818))),
                    Row(
                      children: [
                        Text('0.00', style: TextStyle(fontSize: 16, color: Color(0xFF1A1818))),
                        Text('Baht', style: TextStyle(fontSize: 16, color: Color(0xFF1A1818))),
                      ],
                    )
                  ],
                ),
              ),
              SizedBox(height: 16.h),
              Container(
                width: MediaQuery.of(context).size.width * 0.75,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('You will receive', style: TextStyle(fontSize: 16, color: Color(0xFF1A1818))),
                    Row(
                      children: [
                        Text('0.00', style: TextStyle(fontSize: 16, color: Color(0xFF1A1818))),
                        Text('Baht', style: TextStyle(fontSize: 16, color: Color(0xFF1A1818))),
                      ],
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Placeholder for checkPermission
  void checkPermission() {
    // Implement permission logic here
    setState(() {
      permission = [true, true, true, true]; // Example
    });
  }
}