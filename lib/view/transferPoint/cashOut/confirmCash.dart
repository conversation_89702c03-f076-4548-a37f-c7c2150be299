import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dio/dio.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:likewallet/controller/cashOutController/cashOutController.dart';
import 'package:likewallet/controller/profile/profileController.dart';
import 'package:likewallet/service/components.dart';
import 'package:likewallet/service/globalConfig.dart';
import 'package:likewallet/view/transferPoint/completeTX.dart';

class ConfirmCash extends StatefulWidget {
  final double amount;
  final double fee;
  final double rate;
  final String symbol;
  final double totalSell;
  final String nameAccount;
  final String accountNumber;
  final String typePay;

  const ConfirmCash({
    Key? key,
    required this.amount,
    required this.fee,
    required this.rate,
    required this.symbol,
    required this.totalSell,
    required this.nameAccount,
    required this.accountNumber,
    required this.typePay,
  }) : super(key: key);

  @override
  State<ConfirmCash> createState() => _ConfirmCashState();
}

class _ConfirmCashState extends State<ConfirmCash> {
  final cashOutCtrl = Get.find<CashOutController>();
  final profileCtrl = Get.find<ProfileController>();
  
  final TextEditingController noteController = TextEditingController();
  bool isLoading = false;
  final Dio dio = Dio();
  
  @override
  void dispose() {
    noteController.dispose();
    super.dispose();
  }
  
  // Sign transaction and get transaction hash
  Future<String> signTransaction() async {
    try {
      setState(() {
        isLoading = true;
      });
      
      // Get user authentication
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        setState(() {
          isLoading = false;
        });
        Get.snackbar(
          'Error',
          'User not authenticated',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return '0';
      }
      
      // Get Firebase token
      final idToken = await user.getIdToken();
      
      // Make API request to sign transaction
      final response = await dio.post(
        '${AppEnv.apiUrl}/signTransaction',
        data: {
          "_token": idToken,
          "amount": widget.totalSell.toString(),
        }
      );
      
      if (response.data != null && response.data["statusCode"] == 200) {
        setState(() {
          isLoading = false;
        });
        return response.data["result"]["tx"] ?? '0';
      } else {
        setState(() {
          isLoading = false;
        });
        Get.snackbar(
          'Error',
          'Failed to sign transaction',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return '0';
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      print('Error signing transaction: $e');
      Get.snackbar(
        'Error',
        'Failed to sign transaction',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return '0';
    }
  }
  
  // Submit withdrawal request
  Future<void> submitWithdrawal() async {
    try {
      setState(() {
        isLoading = true;
      });
      
      // Sign transaction first
      final tx = await signTransaction();
      if (tx == '0') {
        return;
      }
      
      // Log transaction
      FirebaseFirestore.instance
          .collection('logs')
          .doc('withdrawAPP')
          .collection('newlikewallet')
          .doc()
          .set({
        'tx': tx,
        'owner': widget.accountNumber.toString()
      });
      
      // Get user authentication
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        setState(() {
          isLoading = false;
        });
        Get.snackbar(
          'Error',
          'User not authenticated',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return;
      }
      
      // Get Firebase token
      final idToken = await user.getIdToken();
      
      // Make API request to submit withdrawal
      final response = await dio.post(
        '${AppEnv.apiUrl}/sellLikepoint',
        data: {
          '_token': idToken,
          'paymentMethod': 'bank',
          'bankName': widget.typePay.toString(),
          'accountNumber': widget.accountNumber.toString(),
          'tx': tx.toString(),
          'amount': ((widget.amount * widget.rate) + (widget.fee * widget.rate)).toString(),
          'baht': widget.amount.toString(),
          'fee': widget.fee.toString(),
          'note': noteController.text.isNotEmpty ? noteController.text.toString() : 'no note'
        }
      );
      
      if (response.data != null && response.data["statusCode"] == 200) {
        setState(() {
          isLoading = false;
        });
        
        // Navigate to success page
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => CompleteTX(
              title: 'Withdrawal Successful',
              detail: 'Your withdrawal request has been submitted successfully.',
              buttonText: 'Back to Home',
            ),
          ),
        );
      } else {
        setState(() {
          isLoading = false;
        });
        Get.snackbar(
          'Error',
          'Failed to submit withdrawal',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      print('Error submitting withdrawal: $e');
      Get.snackbar(
        'Error',
        'Failed to submit withdrawal',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Confirm Withdrawal'),
        backgroundColor: LikeWalletAppTheme.bule1,
      ),
      body: isLoading
          ? Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.all(16.h),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Transaction details card
                    Card(
                      elevation: 2,
                      child: Padding(
                        padding: EdgeInsets.all(16.h),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Transaction Details',
                              style: TextStyle(
                                fontSize: 18.sp,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            SizedBox(height: 16.h),
                            _buildDetailRow('Amount', '${widget.amount} ${widget.symbol}'),
                            _buildDetailRow('Fee', '${widget.fee} ${widget.symbol}'),
                            _buildDetailRow('Total', '${widget.amount + widget.fee} ${widget.symbol}'),
                            _buildDetailRow('LIKE to be sold', '${widget.totalSell}'),
                            Divider(height: 32.h),
                            Text(
                              'Bank Details',
                              style: TextStyle(
                                fontSize: 18.sp,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            SizedBox(height: 16.h),
                            _buildDetailRow('Bank', widget.typePay),
                            _buildDetailRow('Account Name', widget.nameAccount),
                            _buildDetailRow('Account Number', widget.accountNumber),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(height: 24.h),
                    // Note field
                    TextField(
                      controller: noteController,
                      decoration: InputDecoration(
                        labelText: 'Note (Optional)',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                    ),
                    SizedBox(height: 32.h),
                    // Confirm button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: submitWithdrawal,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: LikeWalletAppTheme.bule1,
                          padding: EdgeInsets.symmetric(vertical: 16.h),
                        ),
                        child: Text(
                          'Confirm Withdrawal',
                          style: TextStyle(
                            fontSize: 16.sp,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
  
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[600],
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
