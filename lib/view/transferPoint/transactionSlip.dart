import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:delayed_display/delayed_display.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:likewallet/controller/slipController/slipController.dart';
import 'package:likewallet/controller/walletController/walletDataController.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:likewallet/service/components.dart';


class TransactionSlipPage extends StatefulWidget {

  TransactionSlipPage({Key? key}) : super(key: key);

  @override
  State<TransactionSlipPage> createState() => _TransactionSlipPageState();
}

class _TransactionSlipPageState extends State<TransactionSlipPage> {
  final SlipController slipController = Get.find<SlipController>();

  final WalletDataController walletController = Get.find<WalletDataController>();


  @override
  initState() {
    super.initState();
    // Initialize the slip controller
    // Use a longer delay to ensure the UI is fully rendered
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Check if auto-save is enabled
      if (slipController.autoSaveSlip.value) {
        // Wait for UI to be fully rendered and animation to complete
        Future.delayed(Duration(milliseconds: 2000), () {
          saveSlip();
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Obx(() => Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          InkWell(
            onTap: () {
              Navigator.pushReplacementNamed(context, '/bank');
              // Navigator.popAndPushNamed(context, '/home');
            },
            child: Container(
              alignment: Alignment.bottomCenter,
              width: 1.sw,
              height: 108.h,
              padding: EdgeInsets.only(bottom: 20.h),
              decoration: BoxDecoration(
                color: const Color(0xff141322),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(
                    LikeWalletImage.button_back,
                    height: 20.h,
                    color: Colors.white,
                  ),
                  SizedBox(
                    width: 10.w,
                  ),
                  Text(
                    'bill_back'.tr,
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      fontSize: 15.sp,
                      color: const Color(0x99ffffff),
                    ),
                    textAlign: TextAlign.left,
                  ),
                ],
              ),
            ),
          ),
          SizedBox(
            height: 24.h,
          ),
          DelayedDisplay(
            slidingBeginOffset: const Offset(0.0, 0.0),
            delay: Duration(milliseconds: 1300),
            fadingDuration: const Duration(milliseconds: 500),
            child: Container(
              // width: 800.0.w,
              height: 0.65.sh,
              decoration: BoxDecoration(
                // color: Colors.red,
                boxShadow: [
                  BoxShadow(
                    color: const Color(0x29000000),
                    offset: Offset(0, 2),
                    blurRadius: 25,
                  ),
                ],
                // color: Colors.white,
                borderRadius: BorderRadius.circular(21.0.w),
              ),
              child: coverReceipt(),

            ),
          ),
          SizedBox(
            height: 10.h,
          ),
          controlButton(context),
        ],
      )),
    );
  }

  Widget coverReceipt() {
    return Column(
      children: [
        RepaintBoundary(
          key: slipController.slipKey,
          child: Container(
            // height: 0.5.sh,
            width: 0.9.sw,
            child: Stack(
              children: [
                Image.asset(
                  'assets/image/receive/receipt.png',
                  fit: BoxFit.fitWidth,
                  width: 1.sw,
                  // height: 0.5.sh,
                ),
                Padding(
                  padding: EdgeInsets.only(left: 0.15.sw),
                  child: Container(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          height: 40.h,
                        ),
                        Container(
                          child: Text(
                            'receipt_from'.tr,
                            style: TextStyle(
                                fontFamily: 'Proxima Nova',
                                color: Colors.black,
                                fontSize: 13.sp,
                                fontWeight: FontWeight.normal),
                          ),
                        ),
                        Container(
                          child: Text(
                            slipController.fromName.value != 'no'
                                ? slipController.fromName.value
                                : slipController.fromAddress.value.substring(0, 5) +
                                '...' +
                                slipController.fromAddress.value.substring(slipController.fromAddress.value.length - 5),
                            style: TextStyle(
                                color: Colors.white,
                                fontFamily: 'Proxima Nova',
                                fontWeight: FontWeight.normal,
                                fontSize: 18.sp),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        SizedBox(
                          height: 18.h,
                        ),
                        Container(
                          width: 190.w,
                          child: Text(
                            'receipt_to'.tr,
                            style: TextStyle(
                                fontFamily: 'Proxima Nova',
                                color: Colors.black,
                                fontSize: 13.sp,
                                fontWeight: FontWeight.normal),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Container(
                          child: Text(
                            slipController.toName.value != ''
                                ? slipController.toName.value
                                : slipController.toAddress.value.substring(0, 5) +
                                '...' +
                                slipController.toAddress.value.substring(slipController.toAddress.value.length - 5),
                            style: TextStyle(
                                color: Colors.white,
                                fontFamily: 'Proxima Nova',
                                fontWeight: FontWeight.normal,
                                fontSize: 18.sp),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        SizedBox(
                          height: 18.h,
                        ),
                        Container(
                          width: 120.w,
                          child: Text(
                            'receipt_amount'.tr,
                            style: TextStyle(
                                fontFamily: 'Proxima Nova',
                                color: Colors.black,
                                fontSize: 13.sp,
                                fontWeight: FontWeight.normal),
                          ),
                        ),
                        Container(
                          child: slipController.isVending.value
                              ? Text(
                            slipController.formatter.format(double.parse(slipController.amount.value)) + ' LIKE',
                            style: TextStyle(
                                color: Colors.white,
                                fontFamily: 'Proxima Nova',
                                fontWeight: FontWeight.normal,
                                fontSize: 18.sp),
                          )
                              : Text(
                            slipController.formatter.format(double.parse(slipController.amount.value) *
                                slipController.rateCurrency.value * slipController.rate.value) +
                                ' LIKE',
                            style: TextStyle(
                                color: Colors.white,
                                fontFamily: 'Proxima Nova',
                                fontWeight: FontWeight.normal,
                                fontSize: 18.sp),
                          ),
                        ),
                        SizedBox(
                          height: 18.h,
                        ),
                        Container(
                          width: 190.w,
                          child: Text(
                            'receipt_fee'.tr,
                            style: TextStyle(
                                fontFamily: 'Proxima Nova',
                                color: Colors.black,
                                fontSize: 13.sp,
                                fontWeight: FontWeight.normal),
                          ),
                        ),
                        Container(
                          child: Text(
                            slipController.fee.value,
                            style: TextStyle(
                                color: Colors.white,
                                fontFamily: 'Proxima Nova',
                                fontWeight: FontWeight.normal,
                                fontSize: 18.sp),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        SizedBox(
                          height: 18.h,
                        ),
                        // Container(
                        //   width: 190.w,
                        // ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              child: Text(
                                'receipt_note'.tr,
                                style: TextStyle(
                                  color: Colors.black,
                                  fontFamily: 'Proxima Nova',
                                  fontWeight: FontWeight.normal,
                                  fontSize: 13.sp,
                                ),
                              ),
                            ),
                            SizedBox(
                              height: 10.h,
                            ),
                            Container(
                              width: 0.7.sw,
                              child: Text(
                                slipController.note.value,
                                style: TextStyle(
                                  color: Colors.white,
                                  letterSpacing: 0.5,
                                  fontFamily:
                                  'Proxima Nova',
                                  fontWeight: FontWeight.normal,
                                  fontSize: 12.sp,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                        Text(
                          'receipt_title'.tr,
                          textAlign: TextAlign.start,
                          style: TextStyle(
                            color: Colors.black,
                            fontFamily: 'Proxima Nova',
                            fontWeight: FontWeight.normal,
                            fontSize: 12.sp,
                          ),
                        ),
                        Text(
                          '${slipController.date.value} ${slipController.month.value} ${slipController.year.value}',
                          textAlign: TextAlign.start,
                          style: TextStyle(
                            color: Colors.black,
                            fontFamily: 'Proxima Nova',
                            fontWeight: FontWeight.normal,
                            fontSize: 12.sp,
                          ),
                        ),
                        Text(
                          slipController.time.value,
                          textAlign: TextAlign.start,
                          style: TextStyle(
                            color: Colors.black,
                            fontFamily: 'Proxima Nova',
                            fontWeight: FontWeight.normal,
                            fontSize: 12.sp,
                          ),
                        ),
                      ],
                    ),
                  ),
                )
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget controlButton(context) {
    return Container(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          // Share button
          _buildActionButton(
            icon: 'assets/image/Share_receipt.png',
            label: 'receipt_share'.tr,
            onTap: () async {
              try {
                EasyLoading.show(status: 'Preparing to share...');
                await slipController.shareSlip();
                EasyLoading.dismiss();
              } catch (e) {
                EasyLoading.dismiss();
                Get.snackbar(
                  'Error',
                  'Failed to share receipt: $e',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
              }
            },
          ),
          SizedBox(
            width: 40.w,
          ),
          // Save button
          _buildActionButton(
            icon: 'assets/image/Save_receipt.png',
            label: 'receipt_save'.tr,
            onTap: () async {
              saveSlip();
            },
          ),
        ],
      ),
    );
  }

  void saveSlip() async {
    try {
      print('Attempting to save slip...');

      // Check if the slip key has a current context (UI is rendered)
      if (slipController.slipKey.currentContext == null) {
        print('Error: Slip UI is not yet rendered. Key has no context.');
        // Wait a bit longer and try again
        await Future.delayed(Duration(milliseconds: 1000));
        if (slipController.slipKey.currentContext == null) {
          throw Exception('UI not rendered after delay');
        }
      }

      EasyLoading.show(status: 'Saving slip...');

      // Capture the slip
      final pngBytes = await slipController.captureSlip();
      print('Slip captured successfully, size: ${pngBytes.length} bytes');

      EasyLoading.dismiss();

      // Only show snackbar if not called from initState
      bool calledFromInitState = StackTrace.current.toString().contains('initState');
      if (!calledFromInitState) {
        Get.snackbar(
          'Success',
          'Receipt saved to gallery',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      }
    } catch (e, stackTrace) {
      print('Failed to save receipt: $e');
      print('Stack trace: $stackTrace');
      EasyLoading.dismiss();

      // Only show error if not called from initState
      bool calledFromInitState = StackTrace.current.toString().contains('initState');
      if (!calledFromInitState) {
        Get.snackbar(
          'Error',
          'Failed to save receipt: $e',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    }
  }

  Widget _buildActionButton({
    required String icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 40.w,
            height: 40.w,
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  offset: Offset(0, 5),
                ),
              ],
            ),
            child: Image.asset(
              icon,
              width: 40.w,
              height: 40.w,
              fit: BoxFit.contain,
            ),
          ),
          SizedBox(height: 10.h),
          Text(
            label,
            style: TextStyle(
              fontFamily: 'Proxima Nova',
              fontSize: 14.sp,
              color: Colors.black54,
            ),
          ),
        ],
      ),
    );
  }
}
