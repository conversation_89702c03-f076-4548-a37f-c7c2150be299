class Vending {
  String ref_code;
  String machine_id;
  int amount;
  String addr;
  String function;
  String payment_id;

  Vending(this.ref_code, this.machine_id, this.amount, this.addr, this.function, this.payment_id);

  factory Vending.fromJson(dynamic json) {
    return Vending(json['ref_code'] as String, json['machine_id'] as String, json['amount'] as int, json['addr'] as String, json['function'] as String, json['payment_id'] as String);
  }

  @override
  String toString() {
    return '{ ${this.ref_code},  ${this.machine_id},  ${this.amount},  ${this.addr},  ${this.function},  ${this.payment_id} }';
  }
}