
class NewsModal{
  String? url;
  String? title;
  String? startAt;
  String? endAt;
  String? id;
  String? path;

  NewsModal({
    this.url,
    this.title,
    this.startAt,
    this.endAt,
    this.id,
    this.path
  });
  NewsModal.fromJson(Map<String, dynamic> json)
      : url =  json['url'] as String,
        title = json['title'] as String,
        startAt =  json['startAt'] as String,
        endAt =  json ['endAt'] as String,
        id = json ['id'] as String,
        path =  json ['path'] as String;

  Map<String?, dynamic> toJson() =>
      {
        url: url,
        title : title,
        startAt: startAt,
        endAt : endAt,
        id: id,
        path : path,
      };

}
