// class CashRateText {
//   String cash_rate_start;
//   String cash_rate_over;
//   String cash_rate_end;
//   CashRateText({
//     this.cash_rate_start,
//     this.cash_rate_over,
//     this.cash_rate_end,
//   });
//
//   CashRateText.fromJson(Map<dynamic, dynamic> json) {
//     cash_rate_start = json['cash_rate_start'];
//     cash_rate_over = json['cash_rate_over'];
//     cash_rate_end = json['cash_rate_end'];
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = Map<String, dynamic>();
//
//     try {
//       data['cash_rate_start'] = this.cash_rate_start;
//       data['cash_rate_over'] = this.cash_rate_over;
//       data['cash_rate_end'] = this.cash_rate_end;
//     } catch (e, stack) {
//       print('ERROR caught when trying to convert CashRateText to JSON:');
//       print(e);
//       print(stack);
//     }
//     return data;
//   }
// }
class CashRateText {
  String cash_rate_start = "";
  String cash_rate_over = "";
  String cash_rate_end = "";

  CashRateText(
    this.cash_rate_start,
    this.cash_rate_over,
    this.cash_rate_end,
  );

  factory CashRateText.fromJson(dynamic json) {
    return CashRateText(json['cash_rate_start'] as String,
        json['cash_rate_over'] as String, json['cash_rate_end'] as String);
  }
}
