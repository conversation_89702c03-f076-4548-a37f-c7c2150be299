import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/routes.dart';
import 'package:likewallet/screen/index.dart';
import 'package:likewallet/screen_util.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_messaging/firebase_messaging.dart';

import '../libraryman/applang.dart';

class AlreadyMigrate extends StatelessWidget {

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  Future<bool> destroyApp() async {
    final storage = new FlutterSecureStorage();
    SharedPreferences pref = await SharedPreferences.getInstance();
    //save old lang
    final String? lang = pref.getString('language_code');
    final bool? agreementPolicy = pref.getBool('agreementPolicy');
    final bool? agreementTermsAndCondition = pref.getBool('agreementTermsAndCondition');
    bool check = await pref.clear();
    _firebaseMessaging.unsubscribeFromTopic('notifyTier1');
    _firebaseMessaging.unsubscribeFromTopic('notifyAll');
    _firebaseMessaging.unsubscribeFromTopic('notifyNormal');
    await storage.deleteAll();

    pref.setString('language_code', lang ?? 'en');
    pref.setBool('agreementPolicy', agreementPolicy!);
    pref.setBool('agreementTermsAndCondition', agreementTermsAndCondition!);

    return true;
  }


  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        exit(0);
      },
      child: Scaffold(
        backgroundColor: Colors.black,
        body: Container(
          alignment: Alignment.center,
          decoration: BoxDecoration(color: Colors.black),
          child: Container(
            padding: EdgeInsets.only(top: 700.h, left: 50.w, right: 50.w),
            child: Column(
              children: [
                Image.asset(LikeWalletImage.like_point,
                    height: mediaQuery(context, 'height', 137)),
                SizedBox(
                  height: 30,
                ),
                Text(
                  "LIKE ทั้งหมดของคุณได้รับการอัปเกรดเสร็จเรียบร้อยแล้ว",
                  style: TextStyle(
                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
                    fontSize: mediaQuery(context, 'height', 35),
                    color: const Color(0xff00c5c2),
                    letterSpacing: mediaQuery(context, 'height', 6.3),
                    fontWeight: FontWeight.w300,
                  ),
                  textAlign: TextAlign.center,
                ),
                Text(
                  "ผู้ใช้งานสามารถสะสมและใช้คะแนน AAMpoint หรือ PMSpoint ที่โอนย้ายไปได้ต่อเนื่องที่แอป AAM และ Prachakij",
                  style: TextStyle(
                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
                    fontSize: mediaQuery(context, 'height', 35),
                    color: const Color(0xff00c5c2),
                    letterSpacing: mediaQuery(context, 'height', 6.3),
                    fontWeight: FontWeight.w300,
                  ),
                  textAlign: TextAlign.center,
                ),
                Text(
                  "และบัญชีLikeWalletของคุณได้ถูกยกเลิก เพื่อความสะดวกในการร่วมกิจกรรมต่างๆของเรา",
                  style: TextStyle(
                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
                    fontSize: mediaQuery(context, 'height', 35),
                    color: const Color(0xff00c5c2),
                    letterSpacing: mediaQuery(context, 'height', 6.3),
                    fontWeight: FontWeight.w300,
                  ),
                  textAlign: TextAlign.center,
                ),
                Text(
                  "ขอบคุณที่สนับสนุน LikeWallet ด้วยดีมาตลอดนะคะ",
                  style: TextStyle(
                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
                    fontSize: mediaQuery(context, 'height', 35),
                    color: const Color(0xff00c5c2),
                    letterSpacing: mediaQuery(context, 'height', 6.3),
                    fontWeight: FontWeight.w300,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(
                  height: 30,
                ),
                Text(
                  "All of your LIKE has been upgraded successfully.",
                  style: TextStyle(
                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
                    fontSize: mediaQuery(context, 'height', 35),
                    color: const Color(0xff00c5c2),
                    letterSpacing: mediaQuery(context, 'height', 6.3),
                    fontWeight: FontWeight.w300,
                  ),
                  textAlign: TextAlign.center,
                ),
                Text(
                  "User can continue to accumulate and use transferred AAMpoint or PMSpoint on the AAM and Prachakij apps.",
                  style: TextStyle(
                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
                    fontSize: mediaQuery(context, 'height', 35),
                    color: const Color(0xff00c5c2),
                    letterSpacing: mediaQuery(context, 'height', 6.3),
                    fontWeight: FontWeight.w300,
                  ),
                  textAlign: TextAlign.center,
                ),
                Text(
                  "Your LikeWallet account has been cancelled for your convenience in participating in our activities.",
                  style: TextStyle(
                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
                    fontSize: mediaQuery(context, 'height', 35),
                    color: const Color(0xff00c5c2),
                    letterSpacing: mediaQuery(context, 'height', 6.3),
                    fontWeight: FontWeight.w300,
                  ),
                  textAlign: TextAlign.center,
                ),
                Text(
                  "Thank you for your continued support of LikeWallet.",
                  style: TextStyle(
                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
                    fontSize: mediaQuery(context, 'height', 35),
                    color: const Color(0xff00c5c2),
                    letterSpacing: mediaQuery(context, 'height', 6.3),
                    fontWeight: FontWeight.w300,
                  ),
                  textAlign: TextAlign.center,
                ),
                Container(
                  margin: EdgeInsets.only(top: 30),
                  alignment: Alignment.center,
                  child: ButtonTheme(
                    minWidth: MediaQuery.of(context).size.width * 0.86112,
                    height: MediaQuery.of(context).size.height * 0.05658024691,
                    child: TextButton(
                      style: ButtonStyle(
                        shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                          RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                        ),
                        backgroundColor: MaterialStateProperty.resolveWith<Color>(
                              (Set<MaterialState> states) {
                            if (states.contains(MaterialState.disabled)) {
                              return Color(0xff00F1E0); // Disabled color
                            }
                            return Color(0xff00F1E0); // Regular color
                          },
                        ),
                      ),
                      onPressed: () {
                        destroyApp().then((result) async {
                          AppLanguage appLanguage = AppLanguage();
                          await appLanguage.fetchLocale();
                          AppRoutes.makeFirst(context, IndexLike());
                        });
                      },
                      child: Text(
                        AppLocalizations.of(context)!.translate('logout_title'),
                        style: TextStyle(
                          fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                          color: Colors.black,
                          fontSize: MediaQuery.of(context).size.height *
                              Screen_util("height", 42),
                          fontWeight: FontWeight.w100,
                        ),
                      ),
                    ),
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

}