import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/screen_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:open_store/open_store.dart';

class AlertMigrate extends StatelessWidget {
  AlertMigrate();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: dialogContent(context),
    );
  }
}

dialogContent(BuildContext context) {
  return Center(
    child: Container(
        width: mediaQuery(context, 'width', 989),
        decoration: BoxDecoration(
          color: Colors.black,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Stack(
          alignment: Alignment.topCenter,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(10),
              child: Image.asset(
                LikeWalletImage.banking_fade,
                fit: BoxFit.fitWidth,
                width: 989.w,
              ),
            ),
            Container(
              height: 1500.h,
              alignment: Alignment.topLeft,
              padding: EdgeInsets.only(top: 100.h, left: 40.w, right: 40.w),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // TH
                    Text(
                      "แจ้งปรับการใช้งานระบบสะสมคะแนน",
                      style: TextStyle(
                        fontFamily:
                        AppLocalizations.of(context)!.translate('font1'),
                        fontSize: 50.h,
                        color: const Color(0xffffffff),
                        fontWeight: FontWeight.w300,
                      ),
                      textAlign: TextAlign.left,
                    ),
                    SizedBox(
                      height: 69.h,
                    ),
                    SizedBox(
                      width: 581.0,
                      child: Text(
                        "LikeWallet จะทำการอัปเกรดสิทธิประโยชน์ให้ผู้ใช้งาน เพื่อประสบการณ์ที่หลากหลายขึ้น",
                        style: TextStyle(
                          fontFamily: AppLocalizations.of(context)
                          !.translate('font1'),
                          fontSize: 42.h,
                          color: const Color(0x80ffffff),
                          letterSpacing: 1.26.w,
                          fontWeight: FontWeight.w300,
                          height: 1.4761904761904763,
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    SizedBox(
                      height: 69.h,
                    ),
                    SizedBox(
                      width: 581.0,
                      child: Text(
                        "ผู้ใช้งานสามารถสะสมและใช้คะแนนจากการร่วมกิจกรรมต่างๆต่อเนื่องได้ที่แอป AAM และ Prachakij",
                        style: TextStyle(
                          fontFamily: AppLocalizations.of(context)
                          !.translate('font1'),
                          fontSize: 42.h,
                          color: const Color(0x80ffffff),
                          letterSpacing: 1.26.w,
                          fontWeight: FontWeight.w300,
                          height: 1.4761904761904763,
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    SizedBox(
                      height: 69.h,
                    ),
                    SizedBox(
                      width: 581.0,
                      child: Text(
                        "โดย LikeWallet จะโอนคะแนนสะสม Likepoint ทั้งหมดในทุกกิจกรรมของผู้ใช้งาน ไปยังแอป AAM หรือ Prachakij ที่ผู้ใช้เลือกเป็นแอปอัปเกรดคะแนน Likepoint  จะถูกโอนไปเป็น AAMpoint (เอเอเอ็มพอยท์) หรือ PMSpoint (พีเอ็มเอสพอยท์) โดยจะมีจำนวนและมูลค่าเท่ากันกับ Likepoint ที่มีอยู่เดิม และจะยังคงสามารถร่วมกิจกรรมสะสมคะแนนต่างๆได้เช่นเดิม ตามที่แอป AAM และ Prachakij กำหนด",
                        style: TextStyle(
                          fontFamily: AppLocalizations.of(context)
                          !.translate('font1'),
                          fontSize: 42.h,
                          color: const Color(0x80ffffff),
                          letterSpacing: 1.26.w,
                          fontWeight: FontWeight.w300,
                          height: 1.4761904761904763,
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    SizedBox(
                      height: 69.h,
                    ),
                    SizedBox(
                      width: 581.0,
                      child: Text(
                        "โดย LikeWallet จะมีข้อความแจ้งระบบพร้อมอัปเกรดในช่องทางนี้ พร้อมคำแนะนำในการอัปเกรดโดยละเอียด เร็วๆนี้ค่ะ",
                        style: TextStyle(
                          fontFamily: AppLocalizations.of(context)
                          !.translate('font1'),
                          fontSize: 42.h,
                          color: const Color(0x80ffffff),
                          letterSpacing: 1.26.w,
                          fontWeight: FontWeight.w300,
                          height: 1.4761904761904763,
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    // TH

                    SizedBox(
                      height: 69.h,
                    ),

                    // EN
                    Text(
                      "LikeWallet Point Upgrade Announcement",
                      style: TextStyle(
                        fontFamily:
                        AppLocalizations.of(context)!.translate('font1'),
                        fontSize: 50.h,
                        color: const Color(0xffffffff),
                        fontWeight: FontWeight.w300,
                      ),
                      textAlign: TextAlign.left,
                    ),
                    SizedBox(
                      height: 69.h,
                    ),
                    SizedBox(
                      width: 581.0,
                      child: Text(
                        "LikeWallet is upgrading our rewards program to provide users with a more diverse and rewarding experience.",
                        style: TextStyle(
                          fontFamily: AppLocalizations.of(context)
                          !.translate('font1'),
                          fontSize: 42.h,
                          color: const Color(0x80ffffff),
                          letterSpacing: 1.26.w,
                          fontWeight: FontWeight.w300,
                          height: 1.4761904761904763,
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    SizedBox(
                      height: 69.h,
                    ),
                    SizedBox(
                      width: 581.0,
                      child: Text(
                        "Users can continue to earn and redeem points for participating in various activities on the AAM and Prachakij apps.",
                        style: TextStyle(
                          fontFamily: AppLocalizations.of(context)
                          !.translate('font1'),
                          fontSize: 42.h,
                          color: const Color(0x80ffffff),
                          letterSpacing: 1.26.w,
                          fontWeight: FontWeight.w300,
                          height: 1.4761904761904763,
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    SizedBox(
                      height: 69.h,
                    ),
                    SizedBox(
                      width: 581.0,
                      child: Text(
                        "LikeWallet will transfer all accumulated LikePoint from all user activities to the AAM or Prachakij app of your choice. LikePoint will be converted to AAMpoint or PMSpoint with the same amount and value as the original LikePoint you are holding. Users will continue to be able to participate in various point-earning activities as defined by the AAM and Prachakij apps.",
                        style: TextStyle(
                          fontFamily: AppLocalizations.of(context)
                          !.translate('font1'),
                          fontSize: 42.h,
                          color: const Color(0x80ffffff),
                          letterSpacing: 1.26.w,
                          fontWeight: FontWeight.w300,
                          height: 1.4761904761904763,
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    SizedBox(
                      height: 69.h,
                    ),
                    SizedBox(
                      width: 581.0,
                      child: Text(
                        "LikeWallet will provide system upgrade notifications and detailed upgrade instructions through this channel soon.",
                        style: TextStyle(
                          fontFamily: AppLocalizations.of(context)
                          !.translate('font1'),
                          fontSize: 42.h,
                          color: const Color(0x80ffffff),
                          letterSpacing: 1.26.w,
                          fontWeight: FontWeight.w300,
                          height: 1.4761904761904763,
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    // EN


                    SizedBox(
                      height: 117.h,
                    ),
                    InkWell(
                      onTap: () => {
                      Navigator.of(context).pop(true)
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(horizontal: 20.w),
                        width: 878.w,
                        height: 131.h,
                        alignment: Alignment.center,
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            SvgPicture.string(
                              '<svg viewBox="101.0 1497.0 878.0 131.0" ><defs><linearGradient id="gradient" x1="1.0" y1="0.5" x2="0.0" y2="0.5"><stop offset="0.0" stop-color="#ff5b67fd"  /><stop offset="0.315271" stop-color="#ff5b67fd"  /><stop offset="1.0" stop-color="#ff3948fd"  /></linearGradient></defs><path transform="translate(101.0, 1497.0)" d="M 16 0 L 862 0 C 870.8365478515625 0 878 7.163443565368652 878 16 L 878 115 C 878 123.836555480957 870.8365478515625 131 862 131 L 16 131 C 7.163443565368652 131 0 123.836555480957 0 115 L 0 16 C 0 7.163443565368652 7.163443565368652 0 16 0 Z" fill="url(#gradient)" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                              allowDrawingOutsideViewBox: true,
                              width: 878.w,
                              fit: BoxFit.fitWidth,
                            ),
                            Text(
                              AppLocalizations.of(context)!.translate('close_name'),
                              style: TextStyle(
                                fontFamily: AppLocalizations.of(context)
                                !.translate('font1'),
                                fontSize: 46.h,
                                color: const Color(0xffffffff),
                                letterSpacing: 1.38,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        )),
  );
}

_launchURL() async {
  OpenStore.instance.open(
    appStoreId: '1492241404', // AppStore id of your app
    androidAppBundleId:
        'likewallet.likewallet', // Android app bundle package name
  );
}
