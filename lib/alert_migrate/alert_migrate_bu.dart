import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/screen_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:url_launcher/url_launcher.dart';

class AlertMigrateBU extends StatelessWidget {
  AlertMigrateBU();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: dialogContent(context),
    );
  }
}

dialogContent(BuildContext context) {
  return Center(
    child: Container(
        width: mediaQuery(context, 'width', 989),
        decoration: BoxDecoration(
          color: Colors.black,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Stack(
          alignment: Alignment.topCenter,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(10),
              child: Image.asset(
                LikeWalletImage.banking_fade,
                fit: BoxFit.fitWidth,
                width: 989.w,
              ),
            ),
            Container(
              height: 1500.h,
              alignment: Alignment.topLeft,
              padding: EdgeInsets.only(top: 100.h, left: 40.w, right: 40.w),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // TH
                    Text(
                      "“ระบบสะสมคะแนนใหม่พร้อมแล้ว”",
                      style: TextStyle(
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                        fontSize: 50.h,
                        color: const Color(0xffffffff),
                        fontWeight: FontWeight.w300,
                      ),
                      textAlign: TextAlign.left,
                    ),
                    Text(
                      "มาเพิ่มสิทธิประโยชน์ไปด้วยกัน",
                      style: TextStyle(
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                        fontSize: 50.h,
                        color: const Color(0xffffffff),
                        fontWeight: FontWeight.w300,
                      ),
                      textAlign: TextAlign.left,
                    ),
                    SizedBox(
                      height: 69.h,
                    ),
                    SizedBox(
                      width: 581.0,
                      child:
                      RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: "ผู้ใช้งานสามารถเลือกแอปที่จะอัปเกรดการใช้งาน ระหว่าง แอป ",
                              style: TextStyle(
                                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                                fontSize: 37.h,
                                color: const Color(0x80ffffff),
                                letterSpacing: 1.26.w,
                                fontWeight: FontWeight.w300,
                                height: 1.****************,
                              ),
                            ),
                            TextSpan(
                              text: "AAM",
                              style: TextStyle(
                                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                                fontSize: 37.h,
                                color: const Color(0xffffffff),
                                letterSpacing: 1.26.w,
                                fontWeight: FontWeight.w300,
                                height: 1.****************,
                                decoration: TextDecoration.underline,
                              ),
                              recognizer: TapGestureRecognizer()
                                ..onTap = () {
                                  _openUrl("https://example.com", context);
                                },
                            ),
                            TextSpan(
                              text: " หรือ ",
                              style: TextStyle(
                                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                                fontSize: 37.h,
                                color: const Color(0x80ffffff),
                                letterSpacing: 1.26.w,
                                fontWeight: FontWeight.w300,
                                height: 1.****************,
                              ),
                            ),
                            TextSpan(
                              text: "Prachakij",
                              style: TextStyle(
                                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                                fontSize: 37.h,
                                color: const Color(0xffffffff),
                                letterSpacing: 1.26.w,
                                fontWeight: FontWeight.w300,
                                height: 1.****************,
                                decoration: TextDecoration.underline,
                              ),
                              recognizer: TapGestureRecognizer()
                                ..onTap = () {
                                  _openUrl("https://example.com", context);
                                },
                            ),
                            TextSpan(
                              text: " เพื่อโอนย้าย Likepoint ทั้งหมดที่มีอยู่ใน LikeWallet ในทุกกิจกรรม ไปที่แอป AAM (โอนย้ายจาก Likepoint เป็น AAMpoint) หรือ แอป Prachakij (โอนย้ายจาก Likepoint เป็น PMSpoint) ตามที่ท่านต้องการ",
                              style: TextStyle(
                                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                                fontSize: 37.h,
                                color: const Color(0x80ffffff),
                                letterSpacing: 1.26.w,
                                fontWeight: FontWeight.w300,
                                height: 1.****************,
                              ),
                            ),
                          ],
                        ),
                        textAlign: TextAlign.left,
                      ),
                      // Text(
                      //   "ผู้ใช้งานสามารถเลือกแอปที่จะอัปเกรดการใช้งาน ระหว่าง แอป AAM หรือ Prachakij เพื่อโอนย้าย Likepoint ทั้งหมดที่มีอยู่ใน LikeWallet ในทุกกิจกรรม ไปที่แอป AAM (โอนย้ายจาก Likepointเป็น AAMpoint) หรือ แอป Prachakij (โอนย้ายจาก Likepoint เป็น PMSpoint) ตามที่ท่านต้องการ",
                      //   style: TextStyle(
                      //     fontFamily:
                      //         AppLocalizations.of(context)!.translate('font1'),
                      //     fontSize: 42.h,
                      //     color: const Color(0x80ffffff),
                      //     letterSpacing: 1.26.w,
                      //     fontWeight: FontWeight.w300,
                      //     height: 1.****************,
                      //   ),
                      //   textAlign: TextAlign.left,
                      // ),
                    ),
                    SizedBox(
                      height: 69.h,
                    ),
                    SizedBox(
                      width: 581.0,
                      child: Text(
                        "โดยจำนวนและมูลค่าของPointที่ย้ายไปจะคงอยู่เท่าเดิม",
                        style: TextStyle(
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font1'),
                          fontSize: 42.h,
                          color: const Color(0x80ffffff),
                          letterSpacing: 1.26.w,
                          fontWeight: FontWeight.w300,
                          height: 1.****************,
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    SizedBox(
                      height: 69.h,
                    ),
                    SizedBox(
                      width: 581.0,
                      child: Text(
                        "ผู้ใช้งานสามารถร่วมกิจกรรรมสะสมคะแนน และใช้คะแนนต่อไป ได้ตามที่แอป AAM หรือ Prachakij กำหนด",
                        style: TextStyle(
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font1'),
                          fontSize: 42.h,
                          color: const Color(0x80ffffff),
                          letterSpacing: 1.26.w,
                          fontWeight: FontWeight.w300,
                          height: 1.****************,
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    SizedBox(
                      height: 69.h,
                    ),
                    SizedBox(
                      width: 581.0,
                      child: Text(
                        "ด้วยการ \n1. ดาวน์โหลดแอป AAM หรือ Prachakij \n2. ลงทะเบียนเข้าใช้งานแอปที่เลือก ด้วยเบอร์โทรศัพท์เดียวกันกับที่ผูกไว้กับLikeWallet\n3. ตรวจสอบยอดLIKE ที่ปรากฎอยู่มุมบนขวาของหน้าหลัก\n4. ตรวจสอบและยอมรับเงื่อนไขการให้บริการ\n5. กด“อัปเกรดคะแนน”\n6. คะแนน LIKE ทั้งหมดจะถูกเปลี่ยนเป็น AAMpoint หรือ PMSpoint",
                        style: TextStyle(
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font1'),
                          fontSize: 42.h,
                          color: const Color(0x80ffffff),
                          letterSpacing: 1.26.w,
                          fontWeight: FontWeight.w300,
                          height: 1.****************,
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    SizedBox(
                      height: 69.h,
                    ),
                    SizedBox(
                        width: 581.0,
                        child: Text(
                          "หลังจากการกดอัปเกรด และ Likepoint ของคุณโอนย้ายเป็น AAMpoint หรือ PMSpoint แล้ว บัญชี LikeWallet ของผู้ใช้งานจะถูกยกเลิกโดยอัตโนมัติและข้อมูลส่วนบุคคลของผู้ใช้งาน จะถูกย้ายไปยังแอปที่คุณทำการอัปเกรดคะแนนสะสม",
                          style: TextStyle(
                            fontFamily: AppLocalizations.of(context)
                            !.translate('font1'),
                            fontSize: 42.h,
                            color: const Color(0x80ffffff),
                            letterSpacing: 1.26.w,
                            fontWeight: FontWeight.w300,
                            height: 1.****************,
                          ),
                          textAlign: TextAlign.left,
                        ),
                        ),
                    SizedBox(
                      height: 69.h,
                    ),
                    SizedBox(
                      width: 581.0,
                      child: Text(
                        "หากผู้ใช้งานมีข้อสงสัย หรือติดปัญหาระหว่างการอัปเกรดสามารถติดต่อทีมงานได้ที่เมนู “แชทกับแอดมิน” ในLikeWallet โดยกดปุ่มเครื่องหมาย ? มุมล่างขวาบนหน้าหลักของ LikeWallet ได้ตลอดเวลาค่ะ",
                        style: TextStyle(
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font1'),
                          fontSize: 42.h,
                          color: const Color(0x80ffffff),
                          letterSpacing: 1.26.w,
                          fontWeight: FontWeight.w300,
                          height: 1.****************,
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    // TH

                    SizedBox(
                      height: 69.h,
                    ),

                    // EN
                    Text(
                      "“New Rewards System is Here!”",
                      style: TextStyle(
                        fontFamily:
                        AppLocalizations.of(context)!.translate('font1'),
                        fontSize: 50.h,
                        color: const Color(0xffffffff),
                        fontWeight: FontWeight.w300,
                      ),
                      textAlign: TextAlign.left,
                    ),
                    Text(
                      "Enhance Your Benefits with Us",
                      style: TextStyle(
                        fontFamily:
                        AppLocalizations.of(context)!.translate('font1'),
                        fontSize: 50.h,
                        color: const Color(0xffffffff),
                        fontWeight: FontWeight.w300,
                      ),
                      textAlign: TextAlign.left,
                    ),
                    SizedBox(
                      height: 69.h,
                    ),
                    SizedBox(
                      width: 581.0,
                      child: RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: "Users can now choose between ",
                              style: TextStyle(
                                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                                fontSize: 37.h,
                                color: const Color(0x80ffffff),
                                letterSpacing: 1.26.w,
                                fontWeight: FontWeight.w300,
                                height: 1.****************,
                              ),
                            ),
                            TextSpan(
                              text: "AAM",
                              style: TextStyle(
                                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                                fontSize: 37.h,
                                color: const Color(0xffffffff),
                                letterSpacing: 1.26.w,
                                fontWeight: FontWeight.w300,
                                height: 1.****************,
                                decoration: TextDecoration.underline,
                              ),
                              recognizer: TapGestureRecognizer()
                                ..onTap = () {
                                  _openUrl("https://example.com", context);
                                },
                            ),
                            TextSpan(
                              text: " and ",
                              style: TextStyle(
                                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                                fontSize: 37.h,
                                color: const Color(0x80ffffff),
                                letterSpacing: 1.26.w,
                                fontWeight: FontWeight.w300,
                                height: 1.****************,
                              ),
                            ),
                            TextSpan(
                              text: "Prachakij",
                              style: TextStyle(
                                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                                fontSize: 37.h,
                                color: const Color(0xffffffff),
                                letterSpacing: 1.26.w,
                                fontWeight: FontWeight.w300,
                                height: 1.****************,
                                decoration: TextDecoration.underline,
                              ),
                              recognizer: TapGestureRecognizer()
                                ..onTap = () {
                                  _openUrl("https://example.com", context);
                                },
                            ),
                            TextSpan(
                              text: " apps to upgrade your rewards experience. Transfer all your LikePoint accumulated across all LikeWallet activities to the AAM app (convert LikePoint to AAMpoint) or the Prachakij app (convert LikePoint to PMSpoint) as you prefer.",
                              style: TextStyle(
                                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                                fontSize: 37.h,
                                color: const Color(0x80ffffff),
                                letterSpacing: 1.26.w,
                                fontWeight: FontWeight.w300,
                                height: 1.****************,
                              ),
                            ),
                          ],
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    SizedBox(
                      height: 69.h,
                    ),
                    SizedBox(
                      width: 581.0,
                      child: Text(
                        "The amount and value of your transferred points will remain the same.",
                        style: TextStyle(
                          fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                          fontSize: 42.h,
                          color: const Color(0x80ffffff),
                          letterSpacing: 1.26.w,
                          fontWeight: FontWeight.w300,
                          height: 1.****************,
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    SizedBox(
                      height: 69.h,
                    ),
                    SizedBox(
                      width: 581.0,
                      child: Text(
                        "Continue earning and redeeming points according to the terms and conditions of the AAM or Prachakij app.",
                        style: TextStyle(
                          fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                          fontSize: 42.h,
                          color: const Color(0x80ffffff),
                          letterSpacing: 1.26.w,
                          fontWeight: FontWeight.w300,
                          height: 1.****************,
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    SizedBox(
                      height: 69.h,
                    ),
                    SizedBox(
                        width: 581.0,
                        child: Text(
                        "Here's how to upgrade:",
                        style: TextStyle(
                          fontFamily: AppLocalizations.of(context)
                          !.translate('font1'),
                          fontSize: 42.h,
                          color: const Color(0x80ffffff),
                          letterSpacing: 1.26.w,
                          fontWeight: FontWeight.w300,
                          height: 1.****************,
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    SizedBox(
                      height: 69.h,
                    ),
                    SizedBox(
                      width: 581.0,
                      child: Text(
                        "1. Download the AAM or Prachakij app.\n2. Log in to the chosen app using the same phone number linked to your LikeWallet account.\n3. Check your LIKE balance displayed in the top right corner of the homepage.\n4. Review and accept the terms of service.\n5. Tap “อัปเกรดคะแนน.” \n6. All LIKE will be converted to AAMpoint or PMSpoint.",
                        style: TextStyle(
                          fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                          fontSize: 42.h,
                          color: const Color(0x80ffffff),
                          letterSpacing: 1.26.w,
                          fontWeight: FontWeight.w300,
                          height: 1.****************,
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    SizedBox(
                      height: 69.h,
                    ),
                    SizedBox(
                      width: 581.0,
                      child: Text(
                        "After upgrading and transferring your LikePoint to AAMpoints or PMSpoints, your LikeWallet account will be automatically deleted. Your personal information will be transferred to the app you upgraded your points to.",
                        style: TextStyle(
                          fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                          fontSize: 42.h,
                          color: const Color(0x80ffffff),
                          letterSpacing: 1.26.w,
                          fontWeight: FontWeight.w300,
                          height: 1.****************,
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    SizedBox(
                      height: 69.h,
                    ),
                    SizedBox(
                      width: 581.0,
                      child: Text(
                        "For any inquiries or issues during the upgrade process, contact our team via the “Chat with Admin” menu in LikeWallet by tapping the “?” icon in the bottom right corner of LikeWallet home page.",
                        style: TextStyle(
                          fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                          fontSize: 42.h,
                          color: const Color(0x80ffffff),
                          letterSpacing: 1.26.w,
                          fontWeight: FontWeight.w300,
                          height: 1.****************,
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    // EN

                    SizedBox(
                      height: 117.h,
                    ),
                    InkWell(
                      onTap: () => {Navigator.of(context).pop()},
                      child: Container(
                        padding: EdgeInsets.symmetric(horizontal: 20.w),
                        width: 878.w,
                        height: 131.h,
                        alignment: Alignment.center,
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            SvgPicture.string(
                              '<svg viewBox="101.0 1497.0 878.0 131.0" ><defs><linearGradient id="gradient" x1="1.0" y1="0.5" x2="0.0" y2="0.5"><stop offset="0.0" stop-color="#ff5b67fd"  /><stop offset="0.315271" stop-color="#ff5b67fd"  /><stop offset="1.0" stop-color="#ff3948fd"  /></linearGradient></defs><path transform="translate(101.0, 1497.0)" d="M 16 0 L 862 0 C 870.8365478515625 0 878 7.163443565368652 878 16 L 878 115 C 878 123.836555480957 870.8365478515625 131 862 131 L 16 131 C 7.163443565368652 131 0 123.836555480957 0 115 L 0 16 C 0 7.163443565368652 7.163443565368652 0 16 0 Z" fill="url(#gradient)" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                              allowDrawingOutsideViewBox: true,
                              width: 878.w,
                              fit: BoxFit.fitWidth,
                            ),
                            Text(
                              AppLocalizations.of(context)!
                                  .translate('close_name'),
                              style: TextStyle(
                                fontFamily: AppLocalizations.of(context)!
                                    .translate('font1'),
                                fontSize: 46.h,
                                color: const Color(0xffffffff),
                                letterSpacing: 1.38,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        )),
  );
}

void _openUrl(String url, BuildContext context) async {
  // Close the about dialog.
  Navigator.pop(context);

  if (await canLaunch(url)) {
    await launch(url);
  } else {
    throw 'Could not launch $url';
  }
}
