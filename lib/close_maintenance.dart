import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/middleware/check_maintenance/check_maintenance.dart';
import 'package:likewallet/middleware/getLanguage.dart';
import 'package:likewallet/screen_util.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CloseMaintenance extends StatefulWidget {
  CloseMaintenance({this.url, this.title, this.detail, this.detailTime});

  final String? title;
  final String? url;
  final String? detail;
  final String? detailTime;

  @override
  _CloseMaintenance createState() => _CloseMaintenance();
}

class _CloseMaintenance extends State<CloseMaintenance> {
  @override
  Widget build(BuildContext context) {
    // TODO: implement build
//    throw UnimplementedError();
    return WillPopScope(child: _closeSystem(), onWillPop: () { return Future.value(); });
  }

  Widget _closeSystem() {
    return Scaffold(
      body: Stack(
        alignment: Alignment.center,
        children: <Widget>[
          widget.url == ''
              ? SpinKitFadingCircle(
                  color: LikeWalletAppTheme.bule1,
                  size: mediaQuery(context, 'height', 100),
                )
              : Image.network(
                  widget.url!,
                  fit: BoxFit.fill,
                  width: MediaQuery.of(context).size.width,
                ),
          Positioned(
              top: mediaQuery(context, 'height', 139),
              left: mediaQuery(context, 'width', 75),
              child: backButton(
                context,
                Colors.white,
              )),
          Positioned(
            top: mediaQuery(context, 'height', 905),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  width: mediaQuery(context, 'width', 600),
                  child: Text(
                    widget.title!,
                    style: TextStyle(
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      fontSize: mediaQuery(context, 'height', 42),
                      color: const Color(0xff00c5c2),
                      letterSpacing: mediaQuery(context, 'height', 6.3),
                      fontWeight: FontWeight.w300,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Container(
                  width: mediaQuery(context, 'width', 600),
                  child: Text(
                    widget.detail!,
                    style: TextStyle(
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      fontSize: mediaQuery(context, 'height', 42),
                      color: const Color(0xff00c5c2),
                      letterSpacing: mediaQuery(context, 'height', 6.3),
                      fontWeight: FontWeight.w300,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                SizedBox(
                  height: mediaQuery(context, 'height', 42),
                ),
                Container(
                  width: mediaQuery(context, 'width', 600),
                  child: Text(
                    widget.detailTime.toString(),
                    style: TextStyle(
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      fontSize: mediaQuery(context, 'height', 42),
                      color: const Color(0xff00c5c2),
                      letterSpacing: mediaQuery(context, 'height', 6.3),
                      fontWeight: FontWeight.w300,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
