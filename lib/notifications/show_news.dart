import 'package:flutter/material.dart';
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/libraryman/open_web_notitle.dart';

  dialogContent({BuildContext? context, url, path, title}) {
    return Stack(
      alignment: Alignment.center,
      children: <Widget>[
        GestureDetector(
          onTap: () {
            Navigator.push(context as BuildContext,
                MaterialPageRoute(builder: (context) =>  WebOpenNoTitle(url: path, title: title,))
            );
          },
          child: Container(
//              height: mediaQuery(context, "height", 1024),
              width: mediaQuery(context, "width", 800),
              decoration: new BoxDecoration(
                color: Colors.white,
                shape: BoxShape.rectangle,
                borderRadius: BorderRadius.circular(0),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black26,
                    blurRadius: 10.0,
                    offset: const Offset(0.0, 10.0),
                  ),
                ],
              ),
              child: Image.network(url)),
        ),
        Positioned(
            bottom: 90,
            child: GestureDetector(
              onTap: () => {Navigator.of(context as BuildContext).pop()},
              child: Container(
                  padding: EdgeInsets.all(10.0),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(width: 1.0, color: Colors.white),
                  ),
                  child: Icon(
                    Icons.clear,
                    color: Colors.white,
                  )),
            )),
      ],
    );
  }
