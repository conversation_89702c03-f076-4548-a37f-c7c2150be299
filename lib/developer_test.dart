import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dio/dio.dart';

import 'package:likewallet/libraryman/auth.dart';
import 'package:likewallet/app_config.dart';
import 'package:likewallet/dapp/listdapp.dart';
import 'package:likewallet/libraryman/configuration_service.dart';
import 'package:likewallet/libraryman/lottery.dart';
import 'package:likewallet/middleware/get_mnemonic.dart';
import 'package:shared_preferences/shared_preferences.dart';

class DeveloperTest extends StatefulWidget {
  DeveloperTest({this.page});
  final String? page;
  _DeveloperTest createState() => new _DeveloperTest();
}

class _DeveloperTest extends State<DeveloperTest> {
  final fireStore = FirebaseFirestore.instance;
  late BaseAuth auth;
  Dio dio = new Dio();
  late IConfigurationService configETH;
  String result = '';
  LotteryInterface lottery = Lottery();
  late SharedPreferences pref;
  String mnemonic = '';
  String pketh = '';
  String address = '';
  GetMnemonic seed = new MnemonicRetrieve();
  @override
  void initState() {
    super.initState();
    auth = new Auth();
    init();
  }
  init() async {
    pref = await SharedPreferences.getInstance();
    configETH = new ConfigurationService(pref);
    mnemonic = await configETH.getMnemonic();
    String value = await seed.getMnemonic();
    mnemonic = value.split(":")[0];
    pketh = value.split(":")[1];
    address = configETH.getAddress();
    print(address);
    print('already');
  }

  final List<String> items = new List<String>.generate(200, (i) => "Item $i");

  Widget build(BuildContext context) {
    return new Scaffold(
      appBar: AppBar(
        backgroundColor: Color(0xff141322),
        title: new Text(
          'DeveloperTest LIST',
          style: TextStyle(color: Colors.white),
        ),
      ),
      body: Container(
        child: Column(
          children: [
            Text(result),
            TextButton(onPressed: () {
              lottery.ERC20balanceOf(address: address).then((value) {
                setState(() {
                  result = value.toString();
                });
              });
            }, child: Text('balanceOf')
            ,style: ButtonStyle(
                backgroundColor: MaterialStateProperty.all<Color>(Colors.black12)
              ),
            ),
            TextButton(onPressed: () {
              lottery.allowance(addressOwner: address, addressUsed: '******************************************').then((value) {
                setState(() {
                  result = value.toString();
                });
              });
            }, child: Text('allowance')
              ,style: ButtonStyle(
                  backgroundColor: MaterialStateProperty.all<Color>(Colors.black12)
              ),
            ),
            TextButton(onPressed: () {
              lottery.NFTbalanceOf(pk: pketh).then((value) {
                setState(() {
                  result = value.toString();
                });
              });
            }, child: Text('NFTbalanceOf')
              ,style: ButtonStyle(
                  backgroundColor: MaterialStateProperty.all<Color>(Colors.black12)
              ),
            ),
            TextButton(onPressed: () {
              lottery.tokenOfOwnerByIndex(pk: pketh, i: 5).then((value) {
                setState(() {
                  result = value.toString();
                });
              });
            }, child: Text('tokenOfOwnerByIndex')
              ,style: ButtonStyle(
                  backgroundColor: MaterialStateProperty.all<Color>(Colors.black12)
              ),
            ),
            TextButton(onPressed: () {
              lottery.getLotteryIssueIndex(i: 300).then((value) {
                setState(() {
                  result = value.toString();
                });
              });
            }, child: Text('lotteryIssueIndex')
              ,style: ButtonStyle(
                  backgroundColor: MaterialStateProperty.all<Color>(Colors.black12)
              ),
            ),

            TextButton(onPressed: () {
              lottery.getLotteryNumbers(i: 30).then((value) {
                setState(() {
                  result = value.toString();
                });
              });
            }, child: Text('getLotteryNumbers')
              ,style: ButtonStyle(
                  backgroundColor: MaterialStateProperty.all<Color>(Colors.black12)
              ),
            ),

            TextButton(onPressed: () {
              lottery.getRewardView(i: 200).then((value) {
                setState(() {
                  result = value.toString();
                });
              });
            }, child: Text('getRewardView')
              ,style: ButtonStyle(
                  backgroundColor: MaterialStateProperty.all<Color>(Colors.black12)
              ),
            ),


            TextButton(onPressed: () {
              lottery.timelockhold(address: address, i: 0).then((value) {
                setState(() {
                  result = value.toString();
                });
              });
            }, child: Text('timelockhold')
              ,style: ButtonStyle(
                  backgroundColor: MaterialStateProperty.all<Color>(Colors.black12)
              ),
            ),

          ],
        ),
      ),
    );
  }
}
