import 'dart:convert';
import 'dart:ui';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/routes.dart';
import 'package:likewallet/screen/home.dart';
import 'package:likewallet/app_config.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'libraryman/app_local.dart';
import 'screen_util.dart';

class LikeWalletAppTheme {
  LikeWalletAppTheme._();

  static void configLoadingError() {
    EasyLoading.instance
      ..displayDuration = const Duration(milliseconds: 2000)
      ..indicatorType = EasyLoadingIndicatorType.fadingCircle
      ..loadingStyle = EasyLoadingStyle.dark
      ..indicatorSize = 45.0
      ..radius = 10.0
      ..progressColor = Colors.white
      ..backgroundColor = Colors.red
      ..indicatorColor = Colors.white
      ..textColor = Colors.white
      ..maskColor = Colors.blue.withOpacity(0.5)
      ..userInteractions = true
      ..dismissOnTap = false;
  }

  static void configLoadingSuccess() {
    EasyLoading.instance
      ..displayDuration = const Duration(milliseconds: 2000)
      ..indicatorType = EasyLoadingIndicatorType.fadingCircle
      ..loadingStyle = EasyLoadingStyle.dark
      ..indicatorSize = 45.0
      ..radius = 10.0
      ..progressColor = Colors.white
      ..backgroundColor = Colors.white
      ..indicatorColor = Colors.white
      ..textColor = Colors.white
      ..maskColor = Colors.blue.withOpacity(0.5)
      ..userInteractions = true
      ..dismissOnTap = false;
  }

  static const Color nearlyWhite = Color(0xFFFAFAFA);
  static const Color white = Color(0xFFFFFFFF);
  static const Color white1 = Color(0xFFF5F5F5);
  static const Color white2 = Color(0xFF21252F);
  static const Color black = Color(0xFF000000);
  static const Color green = Color(0xFF789613);
  static const Color background = Color(0xFFF2F3F8);
  static const Color darkerText = Color(0xfFF17262A);
  static const Color bule1 = Color(0xff0FE8D8);
  static const Color bule1_1 = Color(0xff74FFE4);
  static const Color bule1_2 = Color(0xff00E7FF);
  static const Color bule1_3 = Color(0xff00DBDB);
  static const Color bule1_4 = Color(0xff00FFFF);

  static const Color bule1_5 = Color(0xff00EAF4);
  static const Color bule1_6 = Color(0xff00C5C2);
  static const Color bule1_7 = Color(0xff0567D5);

  static const Color bule1_8 = Color(0xff0FE8D8);

  static const Color bule1_9 = Color(0xff1FFCEB);
  static const Color bule1_10 = Color(0xff00FFFF);

  static const Color bule2 = Color(0xff141322);
  static const Color bule2_2 = Color(0xff373747);
  static const Color bule2_3 = Color(0xff08092B);
  static const Color bule2_4 = Color(0xff2B2A38);
  static const Color bule2_5 = Color(0xff312F48);
  static const Color bule2_6 = Color(0xff181827);
  static const Color bule2_7 = Color(0xff201F2D);
  static const Color bule2_8 = Color(0xff3C3C43);
  static const Color bule2_9 = Color(0xff21202E);
  static const Color bule2_10 = Color(0xff282736);
  static const Color bule2_11 = Color(0xff343641);

  static const Color red = Colors.red;
  static const Color bule3 = Color(0xff146D6E);
  static const Color bule4 = Color(0xff76FAF1);
  static const Color gray = Color(0xff707071);
  static const Color gray1 = Color(0xff939395);
  static const Color gray2 = Color(0xff6C6B6D);
  static const Color gray3 = Color(0xffA5A5A5);
  static const Color gray4 = Color(0xff3C3C3C);
  static const Color Purple = Color(0xff6E80FF);
  static const Color gray5 = Color(0xffB3B3B4);
  static const Color gray6 = Color(0xffDEDEDE);
  static const Color gray7 = Color(0xffEBEBF5);

  static const Color lemon = Color(0xffB4E60D);
  static const Color lemon1 = Color(0xffD9F286);

  static const String fontName = 'Roboto';
  static const String width = 'MediaQuery.of(context).size.width';
  static const String height = 'MediaQuery.of(context).size.height';

  static tabslide(context) {
    return TextStyle(
      fontFamily: AppLocalizations.of(context)!.translate('font1'),
      fontWeight: FontWeight.w100,
      fontSize: mediaQuery(context, 'height', 41),
      color: gray1,
    );
  }

  static currencyStyle(context) {
    return TextStyle(
      fontFamily: AppLocalizations.of(context)!.translate('font1'),
      fontWeight: FontWeight.normal,
      fontSize: mediaQuery(context, 'height', 45),
      color: gray1,
    );
  }

  static languageStyle(context) {
    return TextStyle(
      fontFamily: AppLocalizations.of(context)!.translate('font1'),
      fontWeight: FontWeight.w100,
      fontSize: mediaQuery(context, 'height', 45),
      color: gray,
    );
  }

  static LocklikeStyle(context, double fontsize, Color color) {
    return TextStyle(
        fontFamily: AppLocalizations.of(context)!.translate('font1'),
        color: color,
        fontSize: mediaQuery(context, "height", fontsize),
        fontWeight: FontWeight.normal);
  }

  static textStyle(
    context,
    double fontsize,
    Color color,
    width,
    String font,
  ) {
    return TextStyle(
      fontFamily: AppLocalizations.of(context)!.translate(font),
      color: color,
      letterSpacing: 0.3,
      fontSize: mediaQuery(context, "height", fontsize),
      fontWeight: width,
    );
  }

  static const TextStyle headline = TextStyle(
    fontFamily: fontName,
    fontWeight: FontWeight.normal,
    fontSize: 15,
    color: darkerText,
  );

  static profile(context) {
    return TextStyle(
      fontFamily: AppLocalizations.of(context)!.translate('font1'),
      fontWeight: FontWeight.normal,
      fontSize: mediaQuery(context, 'height', 48),
      color: white,
    );
  }

  static profile_lable(context) {
    return TextStyle(
      fontFamily: AppLocalizations.of(context)!.translate('font1'),
      fontWeight: FontWeight.normal,
      fontSize: mediaQuery(context, 'height', 48),
      color: gray,
    );
  }
}

background(BuildContext context) {
  return Center(
    child: Center(
      child: Center(
        child: Center(
          child: Center(
            child: Container(
                height: MediaQuery.of(context).size.height,
                decoration: BoxDecoration(
                    gradient: LinearGradient(
                  begin: Alignment.topRight,
                  end: Alignment.bottomLeft,
                  stops: [0.4, 0.6, 0.7, 0.9, 0.95],
                  colors: [
                    // Colors are easy thanks to Flutter's Colors class.
                    Color(0xff111112).withOpacity(0.9),
                    Color(0xff111112).withOpacity(0.8),
                    Color(0xff111112).withOpacity(0.75),
                    Color(0xff111112).withOpacity(0.73),
                    Color(0xff111112).withOpacity(0.69)
                  ],
                ))),
          ),
        ),
      ),
    ),
  );
}

background_image(BuildContext context) {
  return new Container(
    decoration: BoxDecoration(
        image: DecorationImage(
      image: AssetImage('assets/image/back.png'),
      fit: BoxFit.cover,
    )),
  );
}

backButton(BuildContext context, Color color) {
  return Row(
    children: [
      InkWell(
        child: Container(
          margin: EdgeInsets.only(
            top: mediaQuery(context, 'height', 30.47),
            left: mediaQuery(context, 'width', 75),
            right: mediaQuery(context, 'width', 75),
            bottom: mediaQuery(context, 'height', 30.47),
          ),
          height: mediaQuery(context, 'height', 44.47),
          width: mediaQuery(context, 'height', 44.47),
          child: Image.asset(
            LikeWalletImage.icon_back_button,
            height: mediaQuery(context, "height", 44.47),
            color: color,
          ),
        ),
        onTap: () => {Navigator.of(context).pop()},
      ),
    ],
  );
}

backButton2(BuildContext context, Color color) {
  return Row(
    children: [
      InkWell(
        child: Container(
          padding: EdgeInsets.all(
            mediaQuery(context, 'height', 0),
          ),
          height: mediaQuery(context, 'height', 36.33),
          width: mediaQuery(context, 'height', 24.5),
          child: SvgPicture.string(
            '<svg viewBox="-281.5 3213.3 24.5 36.3" ><path  d="M -259.5249938964844 3249.675048828125 C -260.1029968261719 3249.675048828125 -260.6839904785156 3249.47607421875 -261.156005859375 3249.068115234375 L -281.5329895019531 3231.508056640625 L -261.156005859375 3213.947021484375 C -260.1109924316406 3213.0458984375 -258.531005859375 3213.162109375 -257.6300048828125 3214.208984375 C -256.72900390625 3215.2548828125 -256.8460083007812 3216.8330078125 -257.8919982910156 3217.73388671875 L -273.8739929199219 3231.508056640625 L -257.8919982910156 3245.281005859375 C -256.8460083007812 3246.18310546875 -256.72900390625 3247.760986328125 -257.6300048828125 3248.806884765625 C -258.125 3249.381103515625 -258.822998046875 3249.675048828125 -259.5249938964844 3249.675048828125 Z" fill="#231f20" stroke="none" stroke-width="1" stroke-miterlimit="10" stroke-linecap="butt" /></svg>',
            allowDrawingOutsideViewBox: true,
            fit: BoxFit.fill,
          ),
        ),
        onTap: () => {Navigator.of(context).pop()},
      ),
    ],
  );
}

textFieldProfile(context, value, String hint, String label,
    {bool active = true}) {
  return Padding(
    padding: EdgeInsets.only(
      bottom: mediaQuery(context, 'height', 63),
    ),
    child: new Container(
      decoration: BoxDecoration(
        color: LikeWalletAppTheme.bule2,
//        border: Border.all(
//          color: LikeWalletAppTheme.bule1,
//          width: mediaQuery(context, 'height', 0.3),
//        ),
        borderRadius: BorderRadius.all(Radius.circular(5.0)),
      ),
      alignment: Alignment.center,
      height: mediaQuery(context, 'height', 156),
      width: mediaQuery(context, 'width', 930),
//                color: Colors.blue,
      child: TextField(
        enabled: active,
        controller: value,
        style: TextStyle(
            fontSize: mediaQuery(context, 'height', 47),
            fontFamily: AppLocalizations.of(context)!.translate('font1'),
            fontWeight: FontWeight.w100,
            letterSpacing: 0.3,
            color: LikeWalletAppTheme.white),
        decoration: InputDecoration(
          contentPadding: EdgeInsets.only(
              left: MediaQuery.of(context).size.width * 0.03,
              top: MediaQuery.of(context).size.height * 0.03),
          focusedBorder: OutlineInputBorder(
              borderSide: BorderSide(
                  color: LikeWalletAppTheme.bule1,
                  width: mediaQuery(context, 'width', 1))),
//          contentPadding: EdgeInsets.fromLTRB(20.0, 10.0, 20.0, 10.0),
          labelText: AppLocalizations.of(context)!.translate(hint),
          labelStyle: TextStyle(
              letterSpacing: 0.3,
              fontSize: mediaQuery(context, 'height', 50),
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              color: LikeWalletAppTheme.white.withOpacity(0.3)),
          border: InputBorder.none,
          enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(
                  width: mediaQuery(context, 'width', 0.3),
                  color: LikeWalletAppTheme.bule1)),
          hoverColor: Colors.white,
          disabledBorder: InputBorder.none,
          focusColor: Colors.white,
          alignLabelWithHint: true,
          fillColor: Colors.white,
        ),
        keyboardType: TextInputType.text,
      ),
    ),
  );
}

popupGetLike(context, showfavorite, point) {
  return Stack(
    children: <Widget>[
      AnimatedPositioned(
          duration: Duration(milliseconds: 300),
          bottom: showfavorite
              ? MediaQuery.of(context).size.height * Screen_util("height", 2340)
              : MediaQuery.of(context).size.height *
                  Screen_util("height", 1950),
          child: Stack(children: <Widget>[
            Container(
                alignment: Alignment.bottomCenter,
                width: MediaQuery.of(context).size.width,
                height: MediaQuery.of(context).size.height *
                    Screen_util("height", 600),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                      bottomRight: Radius.circular(10.0),
                      bottomLeft: Radius.circular(10.0)),
                  boxShadow: [
                    new BoxShadow(
                        color: Colors.black.withOpacity(0.16),
                        offset: new Offset(0, 3),
                        blurRadius: 5.0,
                        spreadRadius: 1.0),
                  ],
                  color: LikeWalletAppTheme.white,
                ),
                child: Container(
                  margin: EdgeInsets.only(
                      bottom: mediaQuery(context, 'hieght', 100)),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: <Widget>[
                      Text(
                        AppLocalizations.of(context)!.translate('popup_get'),
                        style: LikeWalletAppTheme.textStyle(
                            context,
                            47,
                            LikeWalletAppTheme.black,
                            FontWeight.normal,
                            'font1'),
                      ),
                      Text(
                        " " + point + " ",
                        style: LikeWalletAppTheme.textStyle(
                            context,
                            85,
                            LikeWalletAppTheme.black,
                            FontWeight.normal,
                            'font2'),
                      ),
                      Text(
                        AppLocalizations.of(context)!.translate('popup_like'),
                        style: LikeWalletAppTheme.textStyle(
                            context,
                            47,
                            LikeWalletAppTheme.black,
                            FontWeight.normal,
                            'font1'),
                      ),
                    ],
                  ),
                ))
          ])),
      //--------Button add favorites--------//
//
    ],
  );
}

sendNotification({String? point}) async {
  var androidPlatformChannelSpecifics = AndroidNotificationDetails('10000',
      'FLUTTER_NOTIFICATION_CHANNEL FLUTTER_NOTIFICATION_CHANNEL_DETAIL',
      importance: Importance.max, priority: Priority.high);
  // var iOSPlatformChannelSpecifics = IOSNotificationDetails();

  var platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      // iOS: iOSPlatformChannelSpecifics
  );

  await flutterLocalNotificationsPlugin.show(
      1, 'แจ้งเตือน', 'ได้รับคะแนนสะสม  $point LIKE', platformChannelSpecifics,
      payload: '');
}

sendNotifyGroup({String? message}) async {
  SharedPreferences pref = await SharedPreferences.getInstance();
  if (pref.getString('token_Line') != null) {
    try {
      var url = Uri.https(env.apiUrl, '/notifyServeGroup');
      var response = await http.post(url, body: {
        "token_line": pref.getString('token_Line'),
        "message": message
      });
      var body = json.decode(response.body);
      print(body);
    } catch (e) {
      print(e);
    }
  }
}

FadeNotification(BuildContext context, point, vsync) {
  return showGeneralDialog(
    context: context,
    pageBuilder: (BuildContext buildContext, Animation<double> animation,
        Animation<double> secondaryAnimation) {
      Future.delayed(Duration(milliseconds: 2000), () {
        Navigator.of(context, rootNavigator: true).pop(true);
      });
      return Material(
          type: MaterialType.transparency,
          child: Stack(children: <Widget>[
            Container(
                alignment: Alignment.bottomCenter,
                width: MediaQuery.of(context).size.width,
                height: MediaQuery.of(context).size.height *
                    Screen_util("height", 350),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                      bottomRight: Radius.circular(10.0),
                      bottomLeft: Radius.circular(10.0)),
                  boxShadow: [
                    new BoxShadow(
                        color: Colors.black.withOpacity(0.16),
                        offset: new Offset(0, 3),
                        blurRadius: 5.0,
                        spreadRadius: 1.0),
                  ],
                  color: LikeWalletAppTheme.white,
                ),
                child: Container(
                  margin: EdgeInsets.only(
                      bottom: mediaQuery(context, 'hieght', 100)),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: <Widget>[
                      Text(
                        AppLocalizations.of(context)!.translate('popup_get'),
                        style: LikeWalletAppTheme.textStyle(
                            context,
                            47,
                            LikeWalletAppTheme.black,
                            FontWeight.normal,
                            'font1'),
                      ),
                      Text(
                        " " + point + " ",
                        style: LikeWalletAppTheme.textStyle(
                            context,
                            50,
                            LikeWalletAppTheme.black,
                            FontWeight.normal,
                            'font2'),
                      ),
                      Text(
                        AppLocalizations.of(context)!.translate('popup_like'),
                        style: LikeWalletAppTheme.textStyle(
                            context,
                            47,
                            LikeWalletAppTheme.black,
                            FontWeight.normal,
                            'font1'),
                      ),
                    ],
                  ),
                ))
          ]));
    },
    barrierDismissible: true,
    barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
    barrierColor: Colors.black54,
    transitionDuration: const Duration(milliseconds: 600),
    transitionBuilder: (BuildContext context, Animation<double> animation,
        Animation<double> secondaryAnimation, Widget child) {
      return AnimatedSize(
        vsync: vsync,
        duration: Duration(milliseconds: 600),
        curve: Curves.elasticInOut,
        child: Container(
          child: Container(
            child: FadeTransition(
              child: child,
              opacity: animation,
            ),
          ),
        ),
      );
    },
    useRootNavigator: true,
  );
}

alertUnlock(BuildContext context, balance) {
  return Stack(
    alignment: Alignment.center,
    children: <Widget>[
      Positioned(
        top: mediaQuery(context, 'height', 430),
        child: new ClipRRect(
          borderRadius: BorderRadius.circular(30.0),
          child: new BackdropFilter(
            filter: new ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0),
            child: Container(
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: Colors.transparent,
                image: DecorationImage(
                    colorFilter: new ColorFilter.mode(
                        LikeWalletAppTheme.white.withOpacity(0.7),
                        BlendMode.dstIn),
                    image:
                        AssetImage("assets/image/locklike/button_unlock.png"),
                    fit: BoxFit.fill),
                borderRadius: BorderRadius.circular(30.0),
              ),
              height: mediaQuery(context, 'height', 682),
              width: mediaQuery(context, 'width', 850),
              child: Material(
                color: Colors.transparent,
                child: Stack(
                  children: <Widget>[
                    Padding(
                      padding: EdgeInsets.only(
                        top: mediaQuery(context, 'height', 0),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: <Widget>[
                          Padding(
                            padding: EdgeInsets.only(
                                top: mediaQuery(context, 'height', 60),
                                right: mediaQuery(context, 'width', 70),
                                bottom: mediaQuery(context, 'height', 30)),
                            child: Align(
                              alignment: Alignment.centerRight,
                              child: GestureDetector(
                                onTap: () {
                                  Navigator.of(context).pop();
                                },
                                child: Icon(
                                  Icons.clear,
                                  size: mediaQuery(context, 'height', 54),
                                ),
                              ),
                            ),
                          ),
                          Text(
                            AppLocalizations.of(context)!
                                    .translate('popup_unlock_your') +
                                AppLocalizations.of(context)!
                                    .translate('lock_symbol') +
                                " \n" +
                                AppLocalizations.of(context)!
                                    .translate('popup_unlock_title'),
                            textAlign: TextAlign.center,
                            style: LikeWalletAppTheme.textStyle(
                                context,
                                52,
                                LikeWalletAppTheme.gray,
                                FontWeight.normal,
                                'font2'),
                          ),
                          Expanded(child: Container()),
                          GestureDetector(
                              onTap: () {
                                Navigator.of(context).pop();
                              },
                              child: Container(
                                  width: mediaQuery(context, 'width', 700),
                                  height: mediaQuery(context, 'height', 200),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(20.0),
                                    color: const Color(0xff29374a),
                                    boxShadow: [
                                      BoxShadow(
                                        color: const Color(0x29000000),
                                        offset: Offset(0, 3),
                                        blurRadius: 6,
                                      ),
                                    ],
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: <Widget>[
                                      Text(
                                        AppLocalizations.of(context)!
                                            .translate('popup_unlock_button1'),
                                        textAlign: TextAlign.center,
                                        style: LikeWalletAppTheme.textStyle(
                                            context,
                                            35,
                                            LikeWalletAppTheme.bule1,
                                            FontWeight.normal,
                                            'font2'),
                                      ),
                                      Text(
                                        AppLocalizations.of(context)!
                                            .translate('popup_unlock_button2'),
                                        textAlign: TextAlign.center,
                                        style: LikeWalletAppTheme.textStyle(
                                            context,
                                            40,
                                            LikeWalletAppTheme.bule1,
                                            FontWeight.bold,
                                            'font2'),
                                      ),
                                    ],
                                  ))),
                          SizedBox(
                            height: mediaQuery(context, 'height', 80),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
//      Positioned(
//        top: mediaQuery(context, 'height', 450),
//        right: ,
//        child:
//      ),
    ],
  );
}

FadeSaveReceipt(BuildContext context, text, vsync) {
  return showGeneralDialog(
    context: context,
    pageBuilder: (BuildContext buildContext, Animation<double> animation,
        Animation<double> secondaryAnimation) {
      Future.delayed(Duration(milliseconds: 2000), () {
        Navigator.of(context, rootNavigator: true).pop(true);
      });
      return WillPopScope(
          onWillPop: () {
            return Future.value();
          },
          child: Material(
              type: MaterialType.transparency,
              child: Stack(children: <Widget>[
                Container(
                    alignment: Alignment.bottomCenter,
                    width: MediaQuery.of(context).size.width,
                    height: MediaQuery.of(context).size.height *
                        Screen_util("height", 327.51),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.only(
                          bottomRight: Radius.circular(10.0),
                          bottomLeft: Radius.circular(10.0)),
                      boxShadow: [
                        new BoxShadow(
                            color: Colors.black.withOpacity(0.16),
                            offset: new Offset(0, 3),
                            blurRadius: 5.0,
                            spreadRadius: 1.0),
                      ],
                      color: LikeWalletAppTheme.white,
                    ),
                    child: Container(
                      margin: EdgeInsets.only(
                          bottom: mediaQuery(context, 'hieght', 80)),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: <Widget>[
                          Text(
                            text,
                            style: LikeWalletAppTheme.textStyle(
                                context,
                                38,
                                LikeWalletAppTheme.black,
                                FontWeight.normal,
                                'font1'),
                          ),
                        ],
                      ),
                    ))
              ])));
    },
    barrierDismissible: true,
    barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
    barrierColor: Colors.black54,
    transitionDuration: const Duration(milliseconds: 600),
    transitionBuilder: (BuildContext context, Animation<double> animation,
        Animation<double> secondaryAnimation, Widget child) {
      return AnimatedSize(
        vsync: vsync,
        duration: Duration(milliseconds: 600),
        curve: Curves.elasticInOut,
        child: Container(
          child: Container(
            child: FadeTransition(
              child: child,
              opacity: animation,
            ),
          ),
        ),
      );
    },
    useRootNavigator: true,
  );
}

FadeHowtoPin(BuildContext context, text1, text2, text3, vsync) {
  return showGeneralDialog(
    context: context,
    pageBuilder: (BuildContext buildContext, Animation<double> animation,
        Animation<double> secondaryAnimation) {
////      Future.delayed(Duration(milliseconds: 2000), () {
//        Navigator.of(context, rootNavigator: true).pop(true);
//      });
      return Material(
          type: MaterialType.transparency,
          child: Stack(children: <Widget>[
            Container(
              alignment: Alignment.bottomCenter,
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height *
                  Screen_util("height", 747.4),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.only(
                    bottomRight: Radius.circular(40.0),
                    bottomLeft: Radius.circular(40.0)),
                boxShadow: [
                  new BoxShadow(
                      color: Colors.black.withOpacity(0.16),
                      offset: new Offset(0, 3),
                      blurRadius: 5.0,
                      spreadRadius: 1.0),
                ],
                color: LikeWalletAppTheme.white,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: <Widget>[
                  Container(
                    width: mediaQuery(context, 'height', 852),
                    margin:
                        EdgeInsets.only(top: mediaQuery(context, 'height', 80)),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Text(
                          text1,
                          style: LikeWalletAppTheme.textStyle(
                              context,
                              38,
                              LikeWalletAppTheme.gray4,
                              FontWeight.bold,
                              'font1'),
                        ),
                        SizedBox(
                          height: mediaQuery(context, 'height', 35),
                        ),
                        Text(
                          text2,
                          style: LikeWalletAppTheme.textStyle(
                              context,
                              38,
                              LikeWalletAppTheme.black,
                              FontWeight.normal,
                              'font1'),
                        ),
                        SizedBox(
                          height: mediaQuery(context, 'height', 35),
                        ),
                        Text(
                          text3,
                          style: LikeWalletAppTheme.textStyle(
                              context,
                              38,
                              LikeWalletAppTheme.black,
                              FontWeight.normal,
                              'font1'),
                        ),
                        SizedBox(
                          height: mediaQuery(context, 'height', 35),
                        ),
                      ],
                    ),
                  ),
                  GestureDetector(
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    child: Align(
                      alignment: Alignment.centerRight,
                      child: Container(
                        margin: EdgeInsets.only(
                          right: mediaQuery(context, 'hieght', 63),
                        ),
                        width: mediaQuery(context, 'height', 103),
                        height: mediaQuery(context, 'height', 103),
                        decoration: BoxDecoration(
                            color: LikeWalletAppTheme.bule2_8,
                            shape: BoxShape.circle),
                        child: Icon(
                          Icons.clear,
                          size: mediaQuery(context, 'height', 40),
                          color: LikeWalletAppTheme.white,
                        ),
                      ),
                    ),
                  )
                ],
              ),
            ),
          ]));
    },
    barrierDismissible: true,
    barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
    barrierColor: Colors.black54,
    transitionDuration: const Duration(milliseconds: 600),
    transitionBuilder: (BuildContext context, Animation<double> animation,
        Animation<double> secondaryAnimation, Widget child) {
      return AnimatedSize(
        vsync: vsync,
        duration: Duration(milliseconds: 600),
        curve: Curves.elasticInOut,
        child: Container(
          child: Container(
            child: FadeTransition(
              child: child,
              opacity: animation,
            ),
          ),
        ),
      );
    },
    useRootNavigator: true,
  );
}

void showColoredToast(msg) {
  Fluttertoast.showToast(
      msg: msg,
      toastLength: Toast.LENGTH_LONG,
      backgroundColor: Colors.red,
      textColor: Colors.white);
}

String validateOTP(String value) {
  String pattern = r'(^[0-9]{6}$)';
  RegExp regExp = new RegExp(pattern);
  if (value.length == 0) {
    print(value);
    return 'no otp';
  } else if (!regExp.hasMatch(value)) {
    print(value + ' not match');
    return 'Please enter valid mobile number';
  }
  print(value + ' match');
  return 'match';
}

Widget backButtonLemon(context) {
  return GestureDetector(
    onTap: () => {Navigator.of(context).pop()},
    child: Container(
        decoration: BoxDecoration(
          color: Color(0xffB4E60D),
          borderRadius: new BorderRadius.only(
              bottomRight: Radius.circular(40.0),
              topRight: Radius.circular(40.0)),
          boxShadow: [
            BoxShadow(
              spreadRadius: 0,
              blurRadius: 9,
              color: Color(0xff707071).withOpacity(0.1),
              offset: Offset(
                0.0,
                3.0,
              ),
            ),
          ],
        ),
        alignment: Alignment.centerLeft,
        height: MediaQuery.of(context).size.height * 0.04797863247,
        width: MediaQuery.of(context).size.width * 0.18317592592,
        // margin: EdgeInsets.only(left: ),
        padding: EdgeInsets.symmetric(
            vertical: mediaQuery(context, 'height', 32.2),
            horizontal: mediaQuery(context, 'width', 71.3)),
        child: Image.asset(
          LikeWalletImage.icon_back_button,
        )),
  );
}

Widget alertWhite(context, page, textHead, textDetail) {
  return Dialog(
    elevation: 0,
    backgroundColor: Colors.transparent,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(30.0),
    ),
    child: Container(
      height: mediaQuery(context, 'height', 554.63),
      width: mediaQuery(context, 'width', 929.64),
      color: Colors.transparent,
      margin: EdgeInsets.only(bottom: mediaQuery(context, 'height', 600)),
      child: new ClipRect(
        child: new BackdropFilter(
          filter: new ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
          child: Container(
            decoration: BoxDecoration(
              color: LikeWalletAppTheme.white.withOpacity(0.6),
              borderRadius: BorderRadius.all(Radius.circular(20.0)),
            ),
            height: mediaQuery(context, 'height', 554.63),
            width: mediaQuery(context, 'width', 929.64),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Text(
                  textHead,
                  style: TextStyle(
                    letterSpacing: 0.3,
                    fontFamily:
                        AppLocalizations.of(context)!.translate('font1'),
                    color: LikeWalletAppTheme.black.withOpacity(1),
                    fontSize: mediaQuery(context, "height", 56),
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(
                      bottom: mediaQuery(context, 'height', 80)),
                  width: mediaQuery(context, 'width', 777.62),
                  child: Text(
                    textDetail,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      letterSpacing: 0.3,
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      color: LikeWalletAppTheme.black.withOpacity(1),
                      fontSize: mediaQuery(context, "height", 42),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Container(
                    width: mediaQuery(context, 'width', 777.62),
                    decoration: BoxDecoration(
                      border: Border(
                        top: BorderSide(
                          //                   <--- left side
                          color: LikeWalletAppTheme.black.withOpacity(0.4),
                          width: mediaQuery(context, 'width', 1),
                        ),
                      ),
                    ),
                    child: Row(
                      children: <Widget>[
                        GestureDetector(
                          onTap: () async {
                            Navigator.pop(context);
                            await Navigator.pushReplacement(
                              context,
                              MaterialPageRoute(
                                builder: (context) => page,
                              ),
                            );
                          },
                          child: Container(
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              border: Border(
                                right: BorderSide(
                                  //                   <--- left side
                                  color:
                                      LikeWalletAppTheme.black.withOpacity(0.4),
                                  width: mediaQuery(context, 'width', 1),
                                ),
                              ),
                            ),
                            height: mediaQuery(context, 'height', 127.66),
                            width: mediaQuery(context, 'width', 777.62) / 2,
                            child: Text(
                              AppLocalizations.of(context)!
                                  .translate('network_error_button'),
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                letterSpacing: 0.3,
                                fontFamily: AppLocalizations.of(context)!
                                    .translate('font1'),
                                color:
                                    LikeWalletAppTheme.bule1_7.withOpacity(1),
                                fontSize: mediaQuery(context, "height", 52),
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                        GestureDetector(
                          onTap: () {
                            AppRoutes.makeFirst(context, HomeLikewallet());
                          },
                          child: Container(
                            alignment: Alignment.center,
                            height: mediaQuery(context, 'height', 127.66),
                            width: mediaQuery(context, 'width', 777.62) / 2,
                            child: Text(
                              AppLocalizations.of(context)!
                                  .translate('logout_no'),
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                letterSpacing: 0.3,
                                fontFamily: AppLocalizations.of(context)!
                                    .translate('font1'),
                                color:
                                    LikeWalletAppTheme.bule1_7.withOpacity(1),
                                fontSize: mediaQuery(context, "height", 52),
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                      ],
                    )),
              ],
            ),
          ),
        ),
      ),
    ),
  );
}
