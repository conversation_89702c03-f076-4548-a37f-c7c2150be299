import 'package:flutter/material.dart';
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/libraryman/app_local.dart';

typedef KeyboardTapCallback = void Function(String text);

class KeyboardUIConfig {
  final double digitSize;
  final double digitBorderWidth;
  final TextStyle digitTextStyle;
  final TextStyle deleteButtonTextStyle;
  final Color primaryColor;
  final Color digitFillColor;
  final EdgeInsetsGeometry keyboardRowMargin;
  final EdgeInsetsGeometry deleteButtonMargin;

  String? type;

  KeyboardUIConfig(
      {this.digitSize = 80,
        this.digitBorderWidth = 1,
        this.keyboardRowMargin = const EdgeInsets.only(top: 15),
        this.primaryColor = Colors.white,
        this.digitFillColor = Colors.transparent,
        this.digitTextStyle = const TextStyle(
            fontSize: 30,
            color: LikeWalletAppTheme.bule1,
            fontWeight: FontWeight.w100),
        this.deleteButtonMargin =
        const EdgeInsets.only(right: 0, left: 0, top: 15),
        this.deleteButtonTextStyle =
        const TextStyle(fontSize: 16, color: Colors.white),
        this.type});
}

class Keyboard extends StatelessWidget {
  final KeyboardUIConfig keyboardUIConfig;
  final GestureTapCallback onDeleteCancelTap;
  final KeyboardTapCallback onKeyboardTap;
  final bool shouldShowCancel;
  final String cancelLocalizedText;
  final String deleteLocalizedText;
  final GestureTapCallback fingerPrint;
  final String type;


  Keyboard(
      {
        required this.keyboardUIConfig,
        required this.onDeleteCancelTap,
        required this.onKeyboardTap,
        this.shouldShowCancel = true,
        required this.fingerPrint,
        required this.cancelLocalizedText,
        required this.deleteLocalizedText,
        required this.type});

  @override
  Widget build(BuildContext context) => _buildKeyboard(context);

  Widget _buildKeyboard(context) {
    return Column(
//      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: <Widget>[
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: <Widget>[
            _buildKeyboardDigit(context, '1'),
            _buildKeyboardDigit(context, '2'),
            _buildKeyboardDigit(context, '3'),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: <Widget>[
            _buildKeyboardDigit(context, '4'),
            _buildKeyboardDigit(context, '5'),
            _buildKeyboardDigit(context, '6'),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: <Widget>[
            _buildKeyboardDigit(context, '7'),
            _buildKeyboardDigit(context, '8'),
            _buildKeyboardDigit(context, '9'),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: <Widget>[
            Container(
              width: mediaQuery(context, 'height', 216),
              height: mediaQuery(context, 'height', 216),
            ),
            Center(child: _buildKeyboardDigit(context, '0')),
            Align(
                alignment: Alignment.topRight,
                child: _buildDeleteButton(context))
          ],
        ),
        SizedBox(height: mediaQuery(context, 'height', 112)),
        type == "pinProtect"
            ? Align(alignment: Alignment.center, child: _buildFinger(context))
            : Container(),
      ],
    );
  }

  Widget _buildKeyboardDigit(BuildContext context, String text) {
    return Container(
      margin: keyboardUIConfig.keyboardRowMargin,
//      width: keyboardUIConfig.digitSize,
//      height: keyboardUIConfig.digitSize,
      child: GestureDetector(
//            highlightColor: keyboardUIConfig.primaryColor,
//            splashColor: keyboardUIConfig.primaryColor.withOpacity(0.4),
        onTap: () {
          onKeyboardTap(text);
        },
        child: Container(
          width: mediaQuery(context, 'height', 216),
          height: mediaQuery(context, 'height', 216),
          decoration: new BoxDecoration(
            color: LikeWalletAppTheme.bule2_6,
            shape: BoxShape.circle,
          ),
          alignment: Alignment.center,
          child: Center(
            child: Text(
              text,
              style: const TextStyle(
                  fontSize: 30,
                  color: LikeWalletAppTheme.bule1,
                  fontWeight: FontWeight.w100),
            ),
          ),
        ),
      ),
      decoration: BoxDecoration(
        shape: BoxShape.circle,
//        border: Border.all(
//            color: keyboardUIConfig.primaryColor,
//            width: keyboardUIConfig.digitBorderWidth),
      ),
    );
  }

  Widget _buildDeleteButton(context) {
    return Container(
      margin: keyboardUIConfig.deleteButtonMargin,
      height: mediaQuery(context, 'height', 216),
      width: mediaQuery(context, 'height', 216),
      child: ClipOval(
        child: TextButton(
          onPressed: onDeleteCancelTap,
          child: Image.asset(LikeWalletImage.icon_clear,
              color: LikeWalletAppTheme.bule1,
              height: mediaQuery(context, 'height', 51.84)),
        ),
      ),
//      ClipOval(
//        child: Material(
//          color: keyboardUIConfig.digitFillColor,
//          child: InkWell(
//            highlightColor: keyboardUIConfig.primaryColor,
//            splashColor: keyboardUIConfig.primaryColor.withOpacity(0.4),
//            onTap: onDeleteCancelTap,
//            child: Center(
//              child: Text(
//                shouldShowCancel ? cancelLocalizedText : deleteLocalizedText,
//                style: keyboardUIConfig.deleteButtonTextStyle,
//              ),
//            ),
//          ),
//        ),
//      ),
    );
  }

  Widget _buildFinger(context) {
    return Column(
      children: [
        Container(
          margin: keyboardUIConfig.deleteButtonMargin,
          height: mediaQuery(context, 'height', 176.57),
          width: mediaQuery(context, 'height', 176.57),
          child: ClipOval(
            child: Material(
              color: keyboardUIConfig.digitFillColor,
              child: InkWell(
                highlightColor: keyboardUIConfig.primaryColor,
                splashColor: keyboardUIConfig.primaryColor.withOpacity(0.4),
                onTap: fingerPrint,
                child: Image.asset(
                  LikeWalletImage.icon_fingerprint,
                ),
                // child: Center(
                //     child: Icon(
                //   Icons.fingerprint,
                //   color: Colors.white,
                //   size: MediaQuery.of(context).size.height * 0.08,
                // )),
              ),
            ),
          ),
        ),
        SizedBox(height: mediaQuery(context, 'height', 42.4)),
        Text(
          AppLocalizations.of(context)!.translate('pin_code_finger_print'),
          textAlign: TextAlign.center,
          style: TextStyle(
              letterSpacing: 0.3,
              fontSize: mediaQuery(context, 'height', 40),
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              color: LikeWalletAppTheme.gray7.withOpacity(0.3),
              fontWeight: FontWeight.w300),
        ),
      ],
    );
  }
}