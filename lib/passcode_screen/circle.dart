// import 'package:flutter/material.dart';
// import 'package:likewallet/screen_util.dart';
// import 'package:likewallet/Theme.dart';
//
// class CircleUIConfig {
//   final Color borderColor;
//   final Color fillColor;
//   final double borderWidth;
//   final double circleSize;
//   double extraSize;
//
//   CircleUIConfig(
//       {this.extraSize = 0,
//       this.borderColor = Colors.white,
//       this.borderWidth = 1,
//       this.fillColor = Colors.white,
//       this.circleSize = 20});
// }
//
// class Circle extends StatelessWidget {
//   final bool filled;
//   final CircleUIConfig circleUIConfig;
//
//   Circle({required Key key, this.filled = false, required this.circleUIConfig})
//       : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       margin: EdgeInsets.only(bottom: circleUIConfig.extraSize),
//       width: mediaQuery(context, 'height', 31),
//       height: mediaQuery(context, 'height', 31),
//       decoration: BoxDecoration(
//         color: filled ? LikeWalletAppTheme.white : LikeWalletAppTheme.gray,
//         shape: BoxShape.circle,
//       ),
//     );
//   }
// }