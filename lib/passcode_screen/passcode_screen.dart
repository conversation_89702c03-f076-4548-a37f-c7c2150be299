library passcode_screen;

import 'dart:async';
import 'dart:ui';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:likewallet/passcode_screen/shake_curve.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../Theme.dart';
import '../libraryman/app_local.dart';
import '../libraryman/applang.dart';
import '../screen/index.dart';
import '../screen_util.dart';
import 'circle.dart';
import 'keyboard.dart';


typedef PasswordEnteredCallback = void Function(String text);
typedef IsValidCallback = void Function();
typedef CancelCallback = void Function();
typedef FingerCallback = void Function();

class PasscodeScreen extends StatefulWidget {
  String title;
  String? explain;
  int passwordDigits;
  Color? titleColor;
  Color? explainColor;
  Color? backgroundColor;
  PasswordEnteredCallback? passwordEnteredCallback;

  //isValidCallback will be invoked after passcode screen will pop.
  IsValidCallback? isValidCallback;
  CancelCallback? cancelCallback;
  FingerCallback? fingerPrint;
  String cancelLocalizedText;
  String deleteLocalizedText;
  Stream<bool> shouldTriggerVerification;
  Widget? bottomWidget;
  CircleUIConfig? circleUIConfig;
  KeyboardUIConfig? keyboardUIConfig;
  String? type;

  PasscodeScreen({
    required this.title,
    this.explain,
    this.passwordDigits = 6,
    this.passwordEnteredCallback,
    required this.cancelLocalizedText,
    required this.deleteLocalizedText,
    required this.shouldTriggerVerification,
    this.isValidCallback,
    this.fingerPrint,
    this.circleUIConfig,
    this.keyboardUIConfig,
    this.bottomWidget,
    this.titleColor = Colors.white,
    this.explainColor = Colors.grey,
    this.backgroundColor,
    this.cancelCallback,
    this.type,
  });

  @override
  State<StatefulWidget> createState() => _PasscodeScreenState();
}

class _PasscodeScreenState extends State<PasscodeScreen>
    with WidgetsBindingObserver, TickerProviderStateMixin {
  late StreamSubscription<bool> streamSubscription;
  String enteredPasscode = '';
  late AnimationController controller;
  late Animation<double> animation;

  @override
  initState() {
    super.initState();
    streamSubscription = widget.shouldTriggerVerification
        .listen((isValid) => _showValidation(isValid));
    controller = AnimationController(
        duration: const Duration(milliseconds: 500), vsync: this);
    final Animation curve =
    CurvedAnimation(parent: controller, curve: ShakeCurve());
    animation = Tween(begin: 0.0, end: 10.0).animate(controller)
      ..addStatusListener((status) {
        if (status == AnimationStatus.completed) {
          setState(() {
            enteredPasscode = '';
            controller.value = 0;
          });
        }
      })
      ..addListener(() {
        setState(() {
          // the animation object’s value is the changed state
        });
      });
  }

  Future<bool> destroyApp() async {
    final storage = new FlutterSecureStorage();
    SharedPreferences pref = await SharedPreferences.getInstance();
    //save old lang
    final lang = pref.getString('language_code');
    bool check = await pref.clear();

    await storage.deleteAll();

    pref.setString('language_code', lang!);

    return true;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: LikeWalletAppTheme.bule2_7,
      body: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: <Widget>[
            widget.type == 'setPin'
                ? Container(
              padding: EdgeInsets.only(
                top: mediaQuery(context, 'height', 139.33),
                left: 0,
              ),
              child: backButton(context, Color(0xff707071)),
            )
                : widget.type == 'confirmPin'
                ? Padding(
              padding: EdgeInsets.only(
                top: mediaQuery(context, 'height', 139.33),
                left: 0,
              ),
              child: backButton(context, Color(0xff707071)),
            )
                : Container(
              padding: EdgeInsets.only(
                top: mediaQuery(context, 'height', 139.33),
                left: 0,
              ),
            ),
            widget.type == 'pinProtect'
                ? Container(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Expanded(
                    child: Container(),
                  ),
                  Expanded(
                    child: InkWell(
                      onTap: () {
                        _showDialog(context);
                      },
                      child: Text(
                        AppLocalizations.of(context)!
                            .translate('pin_code_forgot_pin'),
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            letterSpacing: 0.5,
                            fontFamily: AppLocalizations.of(context)!
                                .translate('font1'),
                            fontSize: ScreenUtil().setSp(39),
                            color: Color(0xff707071),
                            fontWeight: FontWeight.w100),
                      ),
                    ),
                  ),
                  Expanded(child: Container()),
                ],
              ),
            )
                : widget.type == 'setPin'
                ? Container(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Expanded(
                    child: InkWell(
                      onTap: () {
                        FadeHowtoPin(
                            context,
                            AppLocalizations.of(context)!
                                .translate('setpin_explain_title1'),
                            AppLocalizations.of(context)!
                                .translate('setpin_explain_title2'),
                            AppLocalizations.of(context)!
                                .translate('setpin_explain_title3'),
                            this);
                      },
                      child: Text(
                        AppLocalizations.of(context)!
                            .translate('setpin_explain'),
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            letterSpacing: 0.5,
                            fontFamily: AppLocalizations.of(context)!
                                .translate('font1'),
                            fontSize: ScreenUtil().setSp(36),
                            color: Color(0xff707071),
                            fontWeight: FontWeight.w300),
                      ),
                    ),
                  ),
                ],
              ),
            )
                : Container(),
            SizedBox(
              height: mediaQuery(context, 'height', 100),
            ),
            // FlatButton(
            //   onPressed: () {
            //
            //   },
            //   child: Padding(
            //     padding: EdgeInsets.only(
            //       bottom: mediaQuery(context, 'height', 198),
            //     ),
            //     child: Text(
            //       widget.explain,
            //       textAlign: TextAlign.center,
            //       style: TextStyle(
            //           fontSize: mediaQuery(context, 'height', 39),
            //           color: widget.explainColor,
            //           fontWeight: FontWeight.w300),
            //     ),
            //   ),
            // ),
            Text(
              widget.title,
              textAlign: TextAlign.center,
              style: TextStyle(
                  fontSize: ScreenUtil().setSp(65),
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  color: widget.titleColor,
                  fontWeight: FontWeight.w300),
            ),
            Container(
              margin: EdgeInsets.only(
                top: mediaQuery(context, 'height', 76),
                left: mediaQuery(context, 'width', 290),
                right: mediaQuery(context, 'width', 290),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: _buildCircles(),
              ),
            ),
            Container(
              margin: EdgeInsets.only(
                top: mediaQuery(context, 'height', 171),
                left: mediaQuery(context, 'width', 100),
                right: mediaQuery(context, 'width', 100),
              ),
              child: Keyboard(
                onDeleteCancelTap: _onDeleteCancelButtonPressed,
                onKeyboardTap: _onKeyboardButtonPressed,
                shouldShowCancel: enteredPasscode.length == 0,
                cancelLocalizedText: widget.cancelLocalizedText,
                fingerPrint: widget.fingerPrint ?? () {},
                deleteLocalizedText: widget.deleteLocalizedText,
                keyboardUIConfig: widget.keyboardUIConfig ?? KeyboardUIConfig(),
                type: widget.type ?? 'pinProtect',
              ),
            ),
            widget.bottomWidget ?? Container()
          ],
        ),
      ),
    );
  }

  void _showDialog(BuildContext context) {
    showDialog(
      builder: (BuildContext context) => WillPopScope(
        onWillPop: () {
          return Future.value();
        },
        child: Dialog(
            elevation: 0.0,
            backgroundColor: Colors.transparent,
            insetPadding:
            EdgeInsets.only(bottom: mediaQuery(context, 'height', 1200)),
            child: Stack(
              alignment: Alignment.topCenter,
              children: [
                Container(
                  height: mediaQuery(context, 'height', 554.63),
                  width: mediaQuery(context, 'width', 929.64),
                  color: Colors.transparent,
                  child: new ClipRect(
                    child: new BackdropFilter(
                      filter: new ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.5),
                          borderRadius: BorderRadius.all(Radius.circular(20.0)),
                        ),
                        height: mediaQuery(context, 'height', 554.63),
                        width: mediaQuery(context, 'width', 929.64),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: <Widget>[
                            Text(
                              AppLocalizations.of(context)!
                                  .translate('pin_code_forgot_pin'),
                              style: TextStyle(
                                letterSpacing: 0.3,
                                fontFamily: AppLocalizations.of(context)!
                                    .translate('font1'),
                                color: LikeWalletAppTheme.black.withOpacity(1),
                                fontSize: mediaQuery(context, "height", 56),
                                fontWeight: FontWeight.w300,
                              ),
                            ),
                            Container(
                              margin: EdgeInsets.only(
                                  bottom: mediaQuery(context, 'height', 80)),
                              // padding: EdgeInsets.symmetric(horizontal: mediaQuery(context, 'height', 10)),
                              width: mediaQuery(context, 'width', 777.62),
                              child: Text(
                                AppLocalizations.of(context)!
                                    .translate('pin_code_forgot_detail'),
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  letterSpacing: 0.3,
                                  fontFamily: AppLocalizations.of(context)!
                                      .translate('font1'),
                                  color:
                                  LikeWalletAppTheme.black.withOpacity(1),
                                  fontSize: mediaQuery(context, "height", 42),
                                  fontWeight: FontWeight.normal,
                                ),
                              ),
                            ),
                            Container(
                                width: mediaQuery(context, 'width', 777.62),
                                decoration: BoxDecoration(
                                  border: Border(
                                    top: BorderSide(
                                      //                   <--- left side
                                      color: LikeWalletAppTheme.black
                                          .withOpacity(0.4),
                                      width: mediaQuery(context, 'width', 1),
                                    ),
                                  ),
                                ),
                                child: Row(
                                  children: <Widget>[
                                    GestureDetector(
                                      onTap: () async {
                                        Navigator.of(context).pop(context);
                                        destroyApp().then((result) async {
                                          AppLanguage appLanguage =
                                          AppLanguage();
                                          await appLanguage.fetchLocale();
                                          Navigator.pushAndRemoveUntil(
                                            context,
                                            MaterialPageRoute(
                                                builder:
                                                    (BuildContext context) =>
                                                    IndexLike()),
                                            ModalRoute.withName('/'),
                                          );
                                        });
                                      },
                                      child: Container(
                                        alignment: Alignment.center,
                                        decoration: BoxDecoration(
                                          border: Border(
                                            right: BorderSide(
                                              //                   <--- left side
                                              color: LikeWalletAppTheme.black
                                                  .withOpacity(0.4),
                                              width: mediaQuery(
                                                  context, 'width', 1),
                                            ),
                                          ),
                                        ),
                                        height: mediaQuery(
                                            context, 'height', 127.66),
                                        width: mediaQuery(
                                            context, 'width', 777.62) /
                                            2,
                                        child: Text(
                                          AppLocalizations.of(context)!
                                              .translate(
                                              'pin_code_forgot_sign_out'),
                                          textAlign: TextAlign.center,
                                          style: TextStyle(
                                            letterSpacing: 0.3,
                                            fontFamily:
                                            AppLocalizations.of(context)!
                                                .translate('font1'),
                                            color: LikeWalletAppTheme.bule1_7
                                                .withOpacity(1),
                                            fontSize: mediaQuery(
                                                context, "height", 48),
                                            fontWeight: FontWeight.w300,
                                          ),
                                        ),
                                      ),
                                    ),
                                    GestureDetector(
                                      onTap: () {
                                        Navigator.of(context).pop();
                                      },
                                      child: Container(
                                        alignment: Alignment.center,
                                        height: mediaQuery(
                                            context, 'height', 127.66),
                                        width: mediaQuery(
                                            context, 'width', 777.62) /
                                            2,
                                        child: Text(
                                          AppLocalizations.of(context)!
                                              .translate(
                                              'pin_code_forgot_cancel'),
                                          textAlign: TextAlign.center,
                                          style: TextStyle(
                                            letterSpacing: 0.3,
                                            fontFamily:
                                            AppLocalizations.of(context)!
                                                .translate('font1'),
                                            color: LikeWalletAppTheme.bule1_7
                                                .withOpacity(1),
                                            fontSize: mediaQuery(
                                                context, "height", 48),
                                            fontWeight: FontWeight.w300,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                )),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            )),
      ), context: context,
    );
  }

  List<Widget> _buildCircles() {
    var list = <Widget>[];
    var config = widget.circleUIConfig != null
        ? widget.circleUIConfig
        : CircleUIConfig();
    config!.extraSize = animation.value;
    for (int i = 0; i < widget.passwordDigits; i++) {
      list.add(Circle(
        i < enteredPasscode.length,
        config,
      ));
    }
    return list;
  }

  Widget Circle(filled, circleUIConfig){
    return Container(
      margin: EdgeInsets.only(bottom: circleUIConfig.extraSize),
      width: mediaQuery(context, 'height', 31),
      height: mediaQuery(context, 'height', 31),
      decoration: BoxDecoration(
        color: filled ? LikeWalletAppTheme.white : LikeWalletAppTheme.gray,
        shape: BoxShape.circle,
      ),
    );
  }

  _onDeleteCancelButtonPressed() {
    if (enteredPasscode.length > 0) {
      setState(() {
        enteredPasscode =
            enteredPasscode.substring(0, enteredPasscode.length - 1);
      });
    } else {
      Navigator.maybePop(context);

      if (widget.cancelCallback != null) {
        widget.cancelCallback!();
      }
    }
  }

  _onKeyboardButtonPressed(String text) {
    setState(() {
      if (enteredPasscode.length < widget.passwordDigits) {
        enteredPasscode += text;
        if (enteredPasscode.length == widget.passwordDigits) {
          widget.passwordEnteredCallback!(enteredPasscode);
        }
      }
    });
  }

  @override
  didUpdateWidget(PasscodeScreen old) {
    super.didUpdateWidget(old);
    // in case the stream instance changed, subscribe to the new one
    if (widget.shouldTriggerVerification != old.shouldTriggerVerification) {
      streamSubscription.cancel();
      streamSubscription = widget.shouldTriggerVerification
          .listen((isValid) => _showValidation(isValid));
    }
  }

  @override
  dispose() {
    super.dispose();
    controller.dispose();
    streamSubscription.cancel();
  }

  _showValidation(bool isValid) {
    if (isValid) {
      Navigator.maybePop(context).then((pop) => _validationCallback());
    } else {
      controller.forward();
    }
  }

  _validationCallback() {
    if (widget.isValidCallback != null) {
      widget.isValidCallback!();
    } else {
      print(
          "You didn't implement validation callback. Please handle a state by yourself then.");
    }
  }
}

class CircleUIConfig {
  final Color borderColor;
  final Color fillColor;
  final double borderWidth;
  final double circleSize;
  double extraSize;

  CircleUIConfig(
      {this.extraSize = 0,
        this.borderColor = Colors.white,
        this.borderWidth = 1,
        this.fillColor = Colors.white,
        this.circleSize = 20});
}