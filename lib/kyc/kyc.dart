import 'dart:io';

import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as riverpod;
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/animationPage.dart';
import 'package:likewallet/close_maintenance.dart';
import 'package:likewallet/device_utils.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/main.dart';
import 'package:likewallet/middleware/check_maintenance/check_maintenance.dart';
import 'package:likewallet/middleware/getLanguage.dart';
import 'package:likewallet/model/pageStatus.dart';
import 'package:likewallet/routes.dart';

import 'package:likewallet/animationPage.dart';
import 'package:likewallet/libraryman/app_local.dart';

import 'package:likewallet/screen_util.dart';

import 'package:likewallet/screen/takephoto.dart';

import 'package:likewallet/tabslide/logout.dart';

import 'package:shared_preferences/shared_preferences.dart';
import 'package:camera/camera.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:likewallet/screen/home.dart';
import 'package:likewallet/bank/completeTX.dart';
import 'package:permission_handler/permission_handler.dart';

class KYC extends StatefulWidget {
  KYC({this.frontPhoto, this.id, this.backPhoto, this.selfiePhoto, this.title});
  final String? frontPhoto;
  final String? id;
  final String? backPhoto;
  final String? selfiePhoto;

  final String? title;
  _KYC createState() => new _KYC(
      frontPhoto: frontPhoto,
      id: id,
      backPhoto: backPhoto,
      selfiePhoto: selfiePhoto,
      title: title);
}

enum photo { front, end, selfie }

class _KYC extends State<KYC> {
  _KYC(
      {this.frontPhoto, this.id, this.backPhoto, this.selfiePhoto, this.title});

  final String? frontPhoto;
  final String? id;
  final String? backPhoto;
  final String? selfiePhoto;
  final String? title;

  TextEditingController Nametext = new TextEditingController();
  TextEditingController LastNametext = new TextEditingController();
  TextEditingController IDCardtext = new TextEditingController();
  TextEditingController Fronttext = new TextEditingController();
  TextEditingController Backtext = new TextEditingController();
  TextEditingController Selfietext = new TextEditingController();
  late String Name;
  late String LastName;
  late String IDCard;
  late String Front;
  late String Back;
  late String Selfie;

  String frontPhotoUp = 'none';
  String backPhotoUp = 'none';
  String selfiePhotoUp = 'none';
  String kycStatus = 'nokyc';
  bool _saving = false;
  bool checkAutoFocus = false;
  late BaseAuth auth;
  late CameraDescription firstCamera;
  late SharedPreferences sharedPreferences;
  late CheckAbout checkAbout;
  late OnLanguage language;

  void showColoredToast(msg) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_LONG,
        backgroundColor: Colors.red,
        textColor: Colors.white);
  }

  Future<bool?> saveKYC() async {
    sharedPreferences = await SharedPreferences.getInstance();
    auth.getCurrentUser().then((decodedToken) {
      String _year = new DateTime.now().year.toString();
      String _month = new DateTime.now().month.toString();
      String _day = new DateTime.now().day.toString();
      String _date = _day + '-' + _month + '-' + _year;

      FirebaseFirestore.instance.collection('kyc').doc(decodedToken!.uid).set({
        'firstName': Name,
        'lastName': LastName,
        'fullname': Name + " " + LastName,
        'idcard': IDCard,
        'imgIdCardFront': frontPhotoUp,
        'imgIdCardBack': backPhotoUp,
        'selfie': selfiePhotoUp,
        'phone_number': decodedToken.phoneNumber,
        'uid': decodedToken.uid,
        'active': 1,
        'datetime': new DateTime.now().millisecondsSinceEpoch,
        'date': _date
      }).then((data) {
        sharedPreferences.setString('kyc_status', 'pending');
        setState(() {
          _saving = false;
        });
        Navigator.of(context).pushReplacementNamed('/home');
      });
    });
  }

  @override
  dispose() {
    super.dispose();
    Nametext.clear();
    LastNametext.clear();
    IDCardtext.clear();
    Fronttext.clear();
    Backtext.clear();
    Selfietext.clear();
  }

  Future<bool> _onWillPop() async {
    return ExitKYC(context);
  }

  setName() {
    sharedPreferences.setString('firstname', Nametext.text);
    if (!mounted) return;
    setState(() {
      Name = Nametext.text;
    });

    print(Name);
  }

  setLastName() {
    sharedPreferences.setString('lastname', LastNametext.text);
    if (!mounted) return;
    setState(() {
      LastName = LastNametext.text;
    });

    print(LastName);
  }

  setIDCard() {
    sharedPreferences.setString('idcard', IDCardtext.text);
    if (!mounted) return;
    setState(() {
      IDCard = IDCardtext.text;
    });

    print(IDCard);
  }

  setFront() {
    if (!mounted) return;
    setState(() {
      Front = Fronttext.text;
    });

    print(Front);
  }

  setBack() {
    if (!mounted) return;
    setState(() {
      Back = Backtext.text;
    });
    print(Back);
  }

  setSelfie() {
    if (!mounted) return;
    setState(() {
      Selfie = Selfietext.text;
    });

    print(Selfie);
  }

  void checkFill() async {
    sharedPreferences = await SharedPreferences.getInstance();
    String checkFirst = sharedPreferences.getString('firstname') ?? "_";
    String checkLast = sharedPreferences.getString('lastname') ?? "_";
    String checkIdCard = sharedPreferences.getString('idcard') ?? "_";
    if (checkFirst != '_') {
      if (!mounted) return;
      setState(() {
        checkAutoFocus = true;
        Name = checkFirst;
        Nametext.text = checkFirst;
      });
    }
    if (checkLast != '_') {
      setState(() {
        LastName = checkLast;
        LastNametext.text = checkLast;
      });
    }
    if (checkIdCard != '_') {
      setState(() {
        IDCard = checkIdCard;
        IDCardtext.text = checkIdCard;
      });
    }
  }

  void checkStatus() async {
    sharedPreferences = await SharedPreferences.getInstance();
//    kycStatus = sharedPreferences.getString('kyc_status') ?? 'nokyc';

    auth.getCurrentUser().then((decodedToken) {
      FirebaseFirestore.instance
          .collection('kyc')
          .doc(decodedToken!.uid)
          .get()
          .then((userSnap) {
        if (userSnap.exists) {
          print(userSnap.data()!["active"]);
          if (userSnap.data()!["active"] == 2) {
            setState(() {
              print('success kyc');

              kycStatus = 'success';
            });
          } else if (userSnap.data()!["active"] == 1) {
            setState(() {
              kycStatus = 'pending';
            });
          } else if (userSnap.data()!["active"] == 0) {
            setState(() {
              kycStatus = 'nokyc';
            });
          }

          if (!mounted) return;
          setState(() {
            _saving = false;
          });
        } else {
          if (!mounted) return;
          setState(() {
            _saving = false;
          });
          checkFirst();
        }
      });
    });
    print(kycStatus);
  }

  checkFirst() async {
    PageMaintenance statusPage = await checkAbout.checkTierPermission(
        tierLevel: context.read(tierLevel).state, page: 'kyc');
    if (statusPage.status == 'active') {
      //เริ่มทำงาน
      print('active');
      // setInitState();
    } else {
      //ปิด maintenance
      print('inactive');
      final lang = await language.getLanguage();
      final title = await checkAbout.selectLanguage(
          language: lang, detail: statusPage.title);
      final detail = await checkAbout.selectLanguage(
          language: lang, detail: statusPage.detail);
      final detailTime = await checkAbout.selectLanguage(
          language: lang, detail: statusPage.detail_time);
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
            builder: (context) => CloseMaintenance(
                  title: title,
                  detail: detail,
                  detailTime: detailTime,
                  url: statusPage.url,
                )),
      );
    }
  }

  @override
  void initState() {
    super.initState();
    if (!mounted) return;
    setState(() {
      _saving = true;
    });
    checkAbout = OnCheckAbout();
    language = CallLanguage();
    setInitState();
  }

  setInitState() async {
    print('ok continue');
    checkStatus();
    checkFill();
    // checkPhoto();

    Nametext.addListener(setName);
    LastNametext.addListener(setLastName);
    IDCardtext.addListener(setIDCard);
    Fronttext.addListener(setFront);
    Backtext.addListener(setBack);
    Selfietext.addListener(setSelfie);

    auth = Auth();
    WidgetsFlutterBinding.ensureInitialized();
    setCamera();
    print('ok set Camera');
    setState(() {
      if (IDCardtext.text.length <= 0) {
        IDCardtext.text = id!;
      }
    });
  }

  void setCamera() async {
    // Obtain a list of the available cameras on the device.
    final cameras = await availableCameras();
    // Get a specific camera from the list of available cameras.
    firstCamera = cameras.first;
  }



  Widget build(BuildContext context) {
    return new WillPopScope(
      onWillPop: _onWillPop,
      child: ModalProgressHUD(
        opacity: 0.1,
        inAsyncCall: _saving,
        progressIndicator: CustomLoading(),
        child: GestureDetector(
          onTap: () {
            DeviceUtils.hideKeyboard(context);
          },
          child: Scaffold(
            backgroundColor: LikeWalletAppTheme.bule2,
            body: SingleChildScrollView(
              child: Container(
                width: MediaQuery.of(context).size.width,
                height: MediaQuery.of(context).size.height,
                child: kycStatus == 'nokyc'
                    ? KYCForm()
                    : CompleteTX(
                        title: kycStatus == 'pending'
                            ? AppLocalizations.of(context)!
                            .translate('kyc_status_title_pending')
                            : AppLocalizations.of(context)!
                            .translate('kyc_status_title_completed'),
                        detail: kycStatus == 'pending'
                            ? AppLocalizations.of(context)!
                            .translate('kyc_status_detail_pending')
                            : AppLocalizations.of(context)!
                            .translate('kyc_status_detail_completed'),
                        buttonText: AppLocalizations.of(context)!
                            .translate('backtohome'),
                        back: HomeLikewallet(),
                      ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget KYCForm() {
    return new Stack(
      alignment: Alignment.center,
      children: <Widget>[
        //ปุ่ม back
        Positioned(
            top: MediaQuery.of(context).size.height *
                Screen_util("height", 139.33),
            left: MediaQuery.of(context).size.width * Screen_util("width", 75),
            child: _backButton()),
        //หัวเรื่อง KYC
        Positioned(
            top:
                MediaQuery.of(context).size.height * Screen_util("height", 319),
            child: _titel()),
        Positioned(
            top:
                MediaQuery.of(context).size.height * Screen_util("height", 476),
            child: textFieldKYC(context, checkAutoFocus, Nametext, 'kyc_name')),
        Positioned(
            top:
                MediaQuery.of(context).size.height * Screen_util("height", 692),
            child: textFieldKYC(
                context, checkAutoFocus, LastNametext, 'kyc_lastname')),
        Positioned(
            top:
                MediaQuery.of(context).size.height * Screen_util("height", 908),
            child: textFieldKYC(
                context, checkAutoFocus, IDCardtext, 'kyc_idcard')),
        Positioned(
            top: MediaQuery.of(context).size.height *
                Screen_util("height", 1124),
            child: textFieldKYC2(context, Fronttext, frontPhotoUp, frontPhoto,
                'kyc_pic_front', firstCamera, 1, photo.front)),
        Positioned(
            top: MediaQuery.of(context).size.height *
                Screen_util("height", 1340),
            child: textFieldKYC2(context, Backtext, backPhotoUp, backPhoto,
                'kyc_pic_back', firstCamera, 2, photo.end)),
        Positioned(
            top: MediaQuery.of(context).size.height *
                Screen_util("height", 1558),
            child: textFieldKYC2(context, Selfietext, selfiePhotoUp,
                selfiePhoto, 'kyc_details', firstCamera, 3, photo.selfie)),
        //เช็คกรอกครบเเสดงปุ่ม
        if (Name != '' &&
            LastName != '' &&
            IDCard != '' &&
            frontPhotoUp != 'none' &&
            backPhotoUp != 'none' &&
            selfiePhotoUp != 'none')
          Positioned(
              top: MediaQuery.of(context).size.height *
                  Screen_util("height", 1874),
              child: _sendButton()),
      ],
    );
  }

  Widget _backButton() {
    return InkWell(
      child: Container(
        padding: EdgeInsets.all(
          mediaQuery(context, 'height', 20.47),
        ),
        height: mediaQuery(context, 'height', 84.47),
        width: mediaQuery(context, 'height', 84.47),
        child: Image.asset(
          LikeWalletImage.icon_back_button,
          height: mediaQuery(context, "height", 44.47),
          color: LikeWalletAppTheme.gray,
        ),
      ),
      onTap: () => {
        setState(() {
          Navigator.pop(context);
        })
      },
    );
  }

  Widget _titel() {
    return Container(
        alignment: Alignment.centerLeft,
        width: mediaQuery(context, 'width', 930),
        child: Text(
          AppLocalizations.of(context)!.translate('kyc_title'),
          style: TextStyle(
              color: Color(0xff707071),
              fontSize: MediaQuery.of(context).size.height *
                  Screen_util("height", 89),
              fontFamily: 'Proxima Nova'),
        ));
  }

  Widget _sendButton() {
    return ButtonTheme(
      minWidth: MediaQuery.of(context).size.width * Screen_util("width", 930),
      height: MediaQuery.of(context).size.height * Screen_util("height", 132),
      child: TextButton(
        onPressed: () {
          setState(() {
            _saving = true;
          });
          saveKYC();
        },
        style: ButtonStyle(
          shape: MaterialStateProperty.all<RoundedRectangleBorder>(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(5.0),
            ),
          ),
          backgroundColor: MaterialStateProperty.resolveWith<Color>(
                (Set<MaterialState> states) {
              if (states.contains(MaterialState.disabled)) {
                return LikeWalletAppTheme.bule1;
              }
              return LikeWalletAppTheme.bule1;
            },
          ),
        ),
        child: Text(
          AppLocalizations.of(context)!.translate('kyc_button'),
          style: TextStyle(
            color: LikeWalletAppTheme.black,
            fontSize: MediaQuery.of(context).size.height * Screen_util("height", 51),
            fontFamily: AppLocalizations.of(context)!.translate('font1'),
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  textFieldKYC(context, checkAutoFocus, controller, label) {
    return new Container(
      decoration: BoxDecoration(
        color: LikeWalletAppTheme.bule2,
        border: Border.all(
          color: LikeWalletAppTheme.bule1,
          width: mediaQuery(context, 'height', 0.3),
        ),
        borderRadius: BorderRadius.all(Radius.circular(5.0)),
      ),
      alignment: Alignment.center,
      height: mediaQuery(context, 'height', 156),
      width: mediaQuery(context, 'width', 930),
      child: TextField(
        autofocus: checkAutoFocus == true ? true : false,
        controller: controller,
        style: TextStyle(
            color: Colors.white,
            fontSize: MediaQuery.of(context).size.height * 0.02051282051),
        decoration: InputDecoration(
          isDense: true,
          contentPadding: EdgeInsets.only(left: 10),
          hintText: AppLocalizations.of(context)!.translate(label),
          hintStyle: TextStyle(
            fontSize: MediaQuery.of(context).size.height * 0.02051282051,
            color: Colors.white70,
            fontFamily: AppLocalizations.of(context)!.translate('font1'),
            fontWeight: FontWeight.normal,
          ),
          border: InputBorder.none,
        ),
        keyboardType: TextInputType.text,
      ),
    );
  }

  textFieldKYC2(
      context, controller, String PhotoUp, Photo, value, Camera, num, type) {
    return new Container(
      alignment: Alignment.centerLeft,
      width: mediaQuery(context, 'width', 930),
      child: Container(
        alignment: Alignment.center,
        height: mediaQuery(context, 'height', 156),
        width: mediaQuery(context, 'width', 930),
        decoration: BoxDecoration(
          color: Color(0xff141322),
          border: Border.all(
            color: Color(0xff0FE8D8),
            width: mediaQuery(context, 'height', 0.3),
          ),
          borderRadius: BorderRadius.all(Radius.circular(5.0)),
        ),
        child: Row(
          children: <Widget>[
            new Container(
              alignment: Alignment.center,
              height: MediaQuery.of(context).size.height * 0.05,
              width: MediaQuery.of(context).size.width * 0.65,
              child: TextField(
                enabled: false,
                controller: controller,
                style: TextStyle(
                    color: Colors.white,
                    fontSize:
                        MediaQuery.of(context).size.height * 0.02051282051),
                decoration: InputDecoration(
                  contentPadding: EdgeInsets.only(left: 10),
                  labelText: PhotoUp == 'none'
                      ? Photo == 'none'
                          ? AppLocalizations.of(context)!.translate(value)
                          : Photo?.substring(0, 20)
                      : PhotoUp.substring(0, 20),
                  labelStyle: TextStyle(
                      color: Colors.white70,
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      fontSize:
                          MediaQuery.of(context).size.height * 0.02051282051),
                  hintStyle: TextStyle(
                    fontSize:
                        MediaQuery.of(context).size.height * 0.02051282051,
                    color: Colors.white,
                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
                    fontWeight: FontWeight.normal,
                  ),
                  border: InputBorder.none,
                ),
                keyboardType: TextInputType.text,
              ),
            ),
            GestureDetector(
              onTap: () {
                if (PhotoUp == 'none') {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => takePhoto(
                              pageFront: num,
                              firstCamera: Camera,
                            )),
                  ).then((value) {
                    setState(() {
                      if (photo.front == type) {
                        print(value);
                        frontPhotoUp = value;
                      }
                      if (photo.end == type) {
                        backPhotoUp = value;
                      }
                      if (photo.selfie == type) {
                        selfiePhotoUp = value;
                      }
                    });
                  });
                }
              },
              child: Container(
                  alignment: Alignment.center,
                  height: MediaQuery.of(context).size.height * 0.05,
                  width: MediaQuery.of(context).size.width * 0.2,
                  child: PhotoUp != 'none'
                      ? Container(
                          alignment: Alignment.centerRight,
                          height:
                              MediaQuery.of(context).size.width * 0.07429629629,
                          width:
                              MediaQuery.of(context).size.width * 0.07429629629,
                          child: Icon(Icons.check,
                              color: LikeWalletAppTheme.bule1),
                        )
                      : Container(
                          alignment: Alignment.centerRight,
                          height:
                              MediaQuery.of(context).size.width * 0.07429629629,
                          width:
                              MediaQuery.of(context).size.width * 0.07429629629,
                          decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              image: DecorationImage(
                                  fit: BoxFit.cover,
                                  image:
                                      AssetImage(LikeWalletImage.edit_photo)),
                              border: Border.all(
                                  color: Colors.white,
                                  width: MediaQuery.of(context).size.width *
                                      0.00277777777)),
                        )),
            ),
          ],
        ),
      ),
    );
  }
}
