import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

Screen_util(String type, double value) {
  double widthScreen = 1080;

  double heightScreen = 2340;

  if (type == "height") {
    return (value / heightScreen);
  } else {
    return (value / widthScreen);
  }
}

showShortToast(msg, Color color) {
  Fluttertoast.showToast(
      msg: msg,
      toastLength: Toast.LENGTH_SHORT,
      backgroundColor: color,
      textColor: Colors.white);
}

mediaQuery(context, String type, double value) {
//  BuildContext context;
  double _height = MediaQuery.of(context).size.height;
  double _width = MediaQuery.of(context).size.width;

  double widthScreen = 1080;
  double heightScreen = 2340;

  if (type == "height") {
    return (_height * (value / heightScreen));
  } else {
    return (_width * (value / widthScreen));
  }
}

screenUtil(context) {
  // return ScreenUtil.init(context, designSize: Size(1080, 2340), allowFontScaling: false);
  // return ScreenUtil.init(context, designSize: Size(1080, 2340));
  print(context);
  print('screen_util.dart');
  print('screen width ' + MediaQuery.of(context).size.width.toString());
  print('screen height ' + MediaQuery.of(context).size.width.toString());

  return ScreenUtil.init(context, designSize: Size(1080, 2340));
}
