import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:image_picker/image_picker.dart';
import 'package:likewallet/app_config.dart';

abstract class OnCallCamera {
  Future<PickedFile> getImage();
  Future<String> uploadImage({File file});
}

class CallCamera implements OnCallCamera {
  final ImagePicker _picker = ImagePicker();

  @override
  Future<PickedFile> getImage() async {
    PickedFile? image = await _picker.getImage(source: ImageSource.camera, maxHeight: 600, maxWidth: 600) as PickedFile;
    return image;
  }

  Future<String> uploadImage({File? file}) async {
    if (file == null) return 'false';
    String base64Image = base64Encode(file.readAsBytesSync());
    String fileName = file.path.split("/").last;
    var url = Uri.https(env.apiCheck, '/uploadBackFromBase');
    final response = await http.post(url, body: {
      "image": base64Image,
      "name": fileName,
    });
    print(response.statusCode);
    final body = json.decode(response.body);

    print(body["result"]);
    print(body["result"]["id"]);
    print(body["result"]["url"]);
    return body["result"]["url"];
  }
}
