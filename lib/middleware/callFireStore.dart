import 'dart:convert';
import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:likewallet/screen_util.dart';

abstract class OnCallFireStore {
  Future getDataFireStoreCol2(
      {String collection1, String doc1, String collection2, String doc2});
  Future AddPolicyFireStore(
      {String collection1, String doc1, String collection2, String doc2});
  Future getPermissionPage({String tier, String page});
}

class CallFireStore implements OnCallFireStore {
  @override
  Future getDataFireStoreCol2({
    String? collection1,
    String? doc1,
    String? collection2,
    String? doc2,
  }) async {
    return FirebaseFirestore.instance
        .collection(collection1.toString())
        .doc(doc1)
        .collection(collection2.toString())
        .doc(doc2)
        .get()
        .then((value) {
      if (value.data()!.isNotEmpty) {
        print(value.data()!['status']);
        return value.data();
      } else {
        return null;
      }
    });
    // if (ds.data().lqueryength == 0) {
    //   print('error');
    // }
    // if (ds.data().isNotEmpty) {
    //   return ds.data();
    // } else {
    //   return ds.data();
    // }
  }

  @override
  Future AddPolicyFireStore(
      {String? collection1,
      String? doc1,
      String? collection2,
      String? doc2}) async {
    await FirebaseFirestore.instance
        .collection(collection1.toString())
        .doc(doc1)
        .collection(collection2.toString())
        .doc(doc2)
        .set({"status": true}).then((value) {
      return true;
    }).catchError((onError) {
      print(onError);
    });
    return true;
  }

  @override
  Future<bool> getPermissionPage({String? tier, String? page}) async {
    return FirebaseFirestore.instance
        .collection('tierController')
        .doc('controller')
        .collection(tier.toString())
        .doc(page)
        .get()
        .then((value) {
      if (value.data()!.isNotEmpty) {
        return value.data()!['permission'];
      } else {
        return false;
      }
    });
  }
}
