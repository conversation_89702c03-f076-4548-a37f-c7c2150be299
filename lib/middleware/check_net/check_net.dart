import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/screen_util.dart';

abstract class IConnectivity {
  void callConnectivityListen();
}

class CallConnectivity implements IConnectivity {
  late StreamSubscription<ConnectivityResult> subscription;

  void callConnectivityListen() {
    subscription = Connectivity()
        .onConnectivityChanged
        .listen((ConnectivityResult result) {
      print(result);
      if (result == ConnectivityResult.none) {
        showShortToast('Disconnected', Colors.grey);
      }
      if (result == ConnectivityResult.wifi) {
        showShortToast('Connected', LikeWalletAppTheme.bule1);
      }
      if (result == ConnectivityResult.mobile) {
        showShortToast('Connected', LikeWalletAppTheme.bule1);
      }
    });
  }
}
