import 'dart:io';
import 'package:flutter/services.dart';
import 'package:launch_review/launch_review.dart';
import 'package:open_store/open_store.dart';
import 'package:url_launcher/url_launcher.dart';

abstract class StoreDownload {
  _launchURL();
}

class CallStoreDownload implements StoreDownload {
  _openURLAPP(url) async {
    print(url);
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      LaunchReview.launch(androidAppId: url, iOSAppId: url);
      throw 'Could not launch $url';
    }
  }

  _launchURL() async {
    String url;
    //ตรวจสอบ platform
    if (Platform.isAndroid) {
      url = 'likewallet.likewallet';
      //ตรวจสอบว่ามีการติดตั้งไว้ในเครื่องหรือยัง
      // bool isInstalled = await DeviceApps.isAppInstalled(url);
      // //ถ้าติดตั้งแอพแล้วให้เปิดเลย
      // if (isInstalled) {
      //   print('open app');
      //   await DeviceApps.openApp(url);
      // } else {
      //   //ถ้ายังไม่มีให้ไปเปิดใน play store เพื่อติดตั้งแทน
      //   print('ถ้ายังไม่มีให้ไปเปิดใน');
      //   _openURLAPP(url);
      // }
      //ถ้าเป็น iOS
    } else if (Platform.isIOS) {
      String os = Platform.operatingSystem;

      if (os == "ios") {
        print("Container clicked");
        try {
          await launch("likewallet://");
        } on PlatformException catch (e) {
          OpenStore.instance.open(
            appStoreId: '1492241404', // AppStore id of your app
            androidAppBundleId: 'likewallet.likewallet', // Android app bundle package name
          );
        } finally {
          await launch("likewallet://");
          OpenStore.instance.open(
            appStoreId: '1492241404', // AppStore id of your app
            androidAppBundleId: 'likewallet.likewallet', // Android app bundle package name
          );
        }
      } else if (os == "android") {
        try {
          // await AppCheck.launchApp("likewallet.likewallet");
        } catch (e) {
          const url =
              'https://play.google.com/store/apps/details?id=likewallet.likewallet&hl=en';
          if (await canLaunch(url)) {
            await launch(url);
          } else {
            throw 'Could not launch $url';
          }
        }
      } else {}
    }
  }
}
