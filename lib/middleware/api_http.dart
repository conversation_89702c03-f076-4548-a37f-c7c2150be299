import 'dart:convert';
import 'dart:io';

import 'package:crypto/crypto.dart';
import 'package:dart_jsonwebtoken/dart_jsonwebtoken.dart';
import 'package:intl/intl.dart';
import 'package:ags_authrest2/ags_authrest.dart';
import 'package:http/http.dart' as http;

// class AppHttps {
//   static apiPost(List<Map<String, Object>> data) async {
//     Uri url = Uri.parse('https://dockerapi-ci.prachakij.com/jwtauth');
//     DateTime now = DateTime.now().toUtc();
//
//     String formattedDate = DateFormat('MM/dd').format(now);
//
//     // print(now);
//     //
//     var keyP =
//         "A^Amps9@_Um_=^-9tfwJ&d&!pFqgppnK9=JWtFrJqxq=m=5H*3U@%&f%R@+Nsymz&@aC_8tbq6gYjM*R#6mqJ!7A^ZPYwAG3P!8C*dR2zuc33";
//     var key = utf8.encode(keyP);
//     var bytes = utf8.encode(formattedDate);
//     var hmacSha256 = new Hmac(sha256, key);
//     var digest = hmacSha256.convert(bytes);
//
//     final jwt = JWT({
//       "sub": "dockerapi-22022203889234",
//       "iat": DateTime.now().toUtc().millisecondsSinceEpoch,
//     });
//
//     var token = jwt.sign(SecretKey('$digest'));
//
//     HttpClient httpClient = new HttpClient();
//
//     HttpClientRequest request = await httpClient.postUrl(url);
//     request.headers.set('content-type', 'application/json');
//     request.headers.set('path', 'send_telegram_app');
//     request.headers.set('port', '5015');
//     request.headers.set('Authorization', token);
//     request.add(utf8.encode(json.encode(data)));
//     HttpClientResponse response = await request.close();
//     // todo - you should check the response.statusCode
//     String reply = await response.transform(utf8.decoder).join();
//
//     var xx = await json.decode(reply);
//
//     return xx;
//   }
// }
class AppHttps {
  static String sendTelegramPKG = 'https://dockerapi-ci.prachakij.com/jwtauth';

  static postTG_PKG(String url, List<dynamic> jsonMap) async {
    var auth = Ags_restauth();
    auth.SECERT_JWT = 'A^Amps9@_Um_=^-9tfwJ&d&!pFqgppnK9=JWtFrJqxq=m=5H*3U@%&f%R@+Nsymz&@aC_8tbq6gYjM*R#6mqJ!7A^ZPYwAG3P!8C*dR2zuc33';
    auth.R_USER = 'dockerapi-22022203889234';
    var bodyData = jsonMap;
    var headers = {
      'path': 'send_telegram_pkg',
      'port': '5015',
      'Authorization': auth.genTokenEncryp(), // gentoken and encryp
      'Content-Type': 'application/json'
    };
    var body = json.encode(bodyData);
    var request = http.Request('POST',Uri.parse(url));
    request.body = json.encode(auth.encrypbody(body)); //encrypbody
    request.headers.addAll(headers);
    http.StreamedResponse response = await request.send();
    String reply = await response.stream.bytesToString();
    var xx = await json.decode(reply);
    return xx;
  }

  static postTG_BCT(String url, List<dynamic> jsonMap) async {
    var auth = Ags_restauth();
    auth.SECERT_JWT = 'A^Amps9@_Um_=^-9tfwJ&d&!pFqgppnK9=JWtFrJqxq=m=5H*3U@%&f%R@+Nsymz&@aC_8tbq6gYjM*R#6mqJ!7A^ZPYwAG3P!8C*dR2zuc33';
    auth.R_USER = 'dockerapi-22022203889234';
    var bodyData = jsonMap;
    var headers = {
      'path': 'send_telegram_app',
      'port': '5015',
      'Authorization': auth.genTokenEncryp(), // gentoken and encryp
      'Content-Type': 'application/json'
    };
    var body = json.encode(bodyData);
    var request = http.Request('POST',Uri.parse(url));
    request.body = json.encode(auth.encrypbody(body)); //encrypbody
    request.headers.addAll(headers);
    http.StreamedResponse response = await request.send();
    String reply = await response.stream.bytesToString();
    var xx = await json.decode(reply);
    return xx;
  }

  static postTG_PGH(String url, List<dynamic> jsonMap) async {
    var auth = Ags_restauth();
    auth.SECERT_JWT = 'A^Amps9@_Um_=^-9tfwJ&d&!pFqgppnK9=JWtFrJqxq=m=5H*3U@%&f%R@+Nsymz&@aC_8tbq6gYjM*R#6mqJ!7A^ZPYwAG3P!8C*dR2zuc33';
    auth.R_USER = 'dockerapi-22022203889234';
    var bodyData = jsonMap;
    var headers = {
      'path': 'send_telegram_pgh',
      'port': '5015',
      'Authorization': auth.genTokenEncryp(), // gentoken and encryp
      'Content-Type': 'application/json'
    };
    var body = json.encode(bodyData);
    var request = http.Request('POST',Uri.parse(url));
    request.body = json.encode(auth.encrypbody(body)); //encrypbody
    request.headers.addAll(headers);
    http.StreamedResponse response = await request.send();
    String reply = await response.stream.bytesToString();
    var xx = await json.decode(reply);
    return xx;
  }

  static Old_postTG_PKG(String url, List<dynamic> jsonMap) async {
    HttpClient httpClient = HttpClient();
    HttpClientRequest request = await httpClient.postUrl(Uri.parse(url));
    request.headers.set('content-type', 'application/json');
    request.headers.set('port', '5015');
    request.headers.set('path', 'send_telegram_pkg');
    request.headers.set('Authorization',
        'Basic YWdzLWNpOmNkV21XLG81SGxOUGR9Z3pFWmxrYnpDSih6RHRQKQ==');
    request.add(utf8.encode(json.encode(jsonMap)));
    HttpClientResponse response = await request.close();
    // todo - you should check the response.statusCode
    String reply = await response.transform(utf8.decoder).join();
    httpClient.close();
    return json.decode(reply);
  }

  static Old_postTG_BCT(String url, List<dynamic> jsonMap) async {
    HttpClient httpClient = HttpClient();
    HttpClientRequest request = await httpClient.postUrl(Uri.parse(url));
    request.headers.set('content-type', 'application/json');
    request.headers.set('port', '5015');
    request.headers.set('path', 'send_telegram_app');
    request.headers.set('Authorization',
        'Basic YWdzLWNpOmNkV21XLG81SGxOUGR9Z3pFWmxrYnpDSih6RHRQKQ==');
    request.add(utf8.encode(json.encode(jsonMap)));
    HttpClientResponse response = await request.close();
    // todo - you should check the response.statusCode
    String reply = await response.transform(utf8.decoder).join();
    httpClient.close();
    return json.decode(reply);
  }
}
