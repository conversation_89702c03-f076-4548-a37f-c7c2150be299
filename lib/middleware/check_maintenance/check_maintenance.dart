import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:likewallet/alert_update_version/alert_update_version.dart';
import 'package:likewallet/app_config.dart';
import 'package:likewallet/close_maintenance.dart';
import 'package:likewallet/libraryman/configuration_service.dart';
import 'package:likewallet/main.dart';
import 'package:likewallet/model/pageStatus.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as riverpod;
import 'package:http/http.dart' as http;
import 'package:likewallet/model/white_list_ppp7.dart';
import 'package:shared_preferences/shared_preferences.dart';

bool status = false;
bool statusUpdateVersion = false;
String role = '';
String detailMaintenance = '';
bool permissionMenu = false;

abstract class CheckAbout {
  Future<bool> contractUpdate(
      {String docName, String contract, BuildContext context});
  Future<bool> firstStep(
      {String docNameMain, String docNameCon, contract, BuildContext context});
  Future<bool> checkMaintenance({String docName});
  Future<String> checkTier(
      {String? phone, String? email, String list, BuildContext context});
  Future<String> checkBlackList({String type, String phone});
  Future<PageMaintenance> checkTierPermission({String tierLevel, String page});
  Future<String> selectLanguage({String language, List detail});
  Future<bool> checkPermissionMenu({String tierLevel, String page});
}

class OnCheckAbout implements CheckAbout {
  late IConfigurationService configETH;
  @override
  Future<bool> firstStep(
      {String? docNameMain,
      String? docNameCon,
      contract,
      BuildContext? context}) async {
    bool statusFirstStep = false;
    print("status");
    bool status = await checkMaintenance(docName: docNameMain.toString());
    print("status : $status");
    if (status) {
      if (docNameCon == '') {
        statusFirstStep = true;
      } else {
        bool result = await contractUpdate(
            docName: docNameCon.toString(),
            contract: contract,
            context: context as BuildContext);
        print("result $result");
        if (result) {
          statusFirstStep = true;
        } else {
          print('contract old');
          // showDialog(
          //     context: context,
          //     barrierColor: Colors.transparent,
          //     builder: (BuildContext context) {
          //       return WillPopScope(
          //           child: Scaffold(
          //             backgroundColor: Colors.transparent,
          //             body: dialogContent(context),
          //           ),
          //           onWillPop: () async => true);
          //     });
        }
      }
    } else {
      if (docNameMain == 'borrow') {
        // Navigator.push(
        //   context,
        //   MaterialPageRoute(
        //       builder: (context) => CloseMaintenance(docName: docNameMain)),
        // );
      } else {
        // Navigator.pushReplacement(
        //   context,
        //   MaterialPageRoute(
        //       builder: (context) => CloseMaintenance(docName: docNameMain)),
        // );
      }
    }
    print("statusFirstStep $statusFirstStep");
    return statusFirstStep;
  }

  @override
  Future<bool> contractUpdate(
      {String? docName, String? contract, BuildContext? context}) async {
    final res = await FirebaseFirestore.instance
        .collection('smartcontract')
        .doc(docName)
        .get();
    if (res.data()!['address'].toString().toLowerCase() !=
        contract.toString().toLowerCase()) {
      statusUpdateVersion = false;
      print("contract old Version");
    } else {
      statusUpdateVersion = true;
    }
    return statusUpdateVersion;
  }

  @override
  Future<bool> checkMaintenance({String? docName}) async {
    DocumentSnapshot<Map<String, dynamic>> ds = await FirebaseFirestore.instance
        .collection('maintenance')
        .doc(docName)
        .get();
    if (ds.data()!["status"] == 'active') {
      status = true;
    } else {
      status = false;
    }
    return status;
  }

  @override
  Future<String> checkTier(
      {String? phone,
      String? email,
      String? list,
      BuildContext? context}) async {
    //หาจำนวน tier
    if (phone == null || phone == '') {
      // role = 'normal';
      //นำจำนวน tier ค้นว่าอยู่ tier ไหน?
      // final tier = await FirebaseFirestore.instance
      //     .collection('sandbox')
      //     .doc('tierBU')
      //     .collection(list.toString())
      //     .where("email", isEqualTo: email)
      //     .limit(1)
      //     .get();
      // if (tier.docs.length > 0) {
      //   role = 'tierBU';
      // } else {
      //   role = 'normal';
      // }
      SharedPreferences pref = await SharedPreferences.getInstance();
      configETH = new ConfigurationService(pref);
      String address = configETH.getAddress();
      var url = Uri.https(env.apiUrl, '/getTierBUfromRDS');
      final response = await http.post(url, body: {
        "address": address,
      });
      print("response:");
      print(response.statusCode);
      print("++++++++++++++++++++");

      final body = json.decode(response.body);
      // print(body['status']);
      if (response.statusCode == 200) {
        if (body['status'] == 200) {
          role = 'tier1';
        } else {
          role = 'normal';
        }
      }
    } else {
      print('ไม่พบข้อมูลเบอร์นี้');
      //นำจำนวน tier ค้นว่าอยู่ tier ไหน?
      final data =
          await checkWhiteListPPP7(phoneNumber: phone, context: context!);
      print("data $data");
      //set tier1
      if (data != null) {
        role = data.tier;
        context.read(userLevel).state = data.level;
        print('พบเบอร์ PPP7');
      } else {
        print('ไม่พบเบอร์ PPP7');
        final data =
            await FirebaseFirestore.instance.collection('sandbox').get();
        for (int i = 0; i < data.docs.length; i++) {
          print("id" +" " + data.docs[i].id);
          final tier = await FirebaseFirestore.instance
              .collection('sandbox')
              .doc(data.docs[i].id)
              .collection(list.toString())
              .where("phone", isEqualTo: phone)
              .limit(1)
              .get();
          tier.docs.forEach((element) {
            print('พบเบอร์ ใน sandbox');
            print(element.id);
            context.read(userLevel).state = element['level'];
          });
          print(tier.docs.length);
          print(tier.docs.length);

          if (tier.docs.length > 0) {
            role = data.docs[i].id;
            print("test:"+role);
            break;
          } else {
            role = 'normal';
            context.read(userLevel).state = "1";
          }
        }
      }
    }
    print("role $role");
    return role;
  }

  late WhiteListPPP7 result;
  Future<WhiteListPPP7?> checkWhiteListPPP7({
    String? phoneNumber,
    BuildContext? context,
  }) async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    try {
      var url = Uri.parse('https://new.likepoint.io' + '/getWhitelistPKG');
      final response = await http.post(url, body: {"phoneNumber": phoneNumber});
      print(response.statusCode);
      if (response.statusCode == 200) {
        var body = json.decode(response.body);
        print(body);
        if (body['status'] == 200) {
          result = WhiteListPPP7.fromJson(body);
          role = result.tier;
          print('พบเบอร์นี้ $role');
          print(result.token_Line);
          pref.setString('token_Line', result.token_Line);
          return result;
        }
        if (body['statusCode'] == 204) {
          return null;
        }
      } else {
        return null;
      }
    } catch (e) {
      print(e);
      return null;
    }
  }

  @override
  Future<String> checkBlackList({String? type, String? phone}) async {
    //หาจำนวน tier
    if (phone == null) {
      role = 'no';
    } else {
      final tier = await FirebaseFirestore.instance
          .collection('sandbox')
          .doc(type)
          .collection('blacklist')
          .where("phone", isEqualTo: phone)
          .limit(1)
          .get();
      if (tier.docs.length > 0) {
        print(tier.docs.length == 0);
        role = type.toString();
      } else {
        role = 'no';
      }
    }

    print("role : $role");
    return role;
  }

  @override
  Future<PageMaintenance> checkTierPermission(
      {String? tierLevel, String? page}) async {
    //หาจำนวน tier
    print("page ===> $page tierLevel ====>");
    print(tierLevel);
    final data = await FirebaseFirestore.instance
        .collection('tierController')
        .doc('controller')
        .collection(tierLevel.toString())
        .doc(page)
        .get();
    return PageMaintenance.fromJson(data.data() as Map<String, dynamic>);
  }

  @override
  Future<String> selectLanguage({String? language, List? detail}) async {
    //หาจำนวน tier
    if (language == 'th') {
      detailMaintenance = detail![0];
    } else if (language == 'en') {
      detailMaintenance = detail![1];
    } else if (language == 'lo') {
      detailMaintenance = detail![2];
    } else if (language == 'km') {
      detailMaintenance = detail![3];
    } else if (language == 'vi') {
      detailMaintenance = detail![4];
    }
    return detailMaintenance;
  }

  @override
  Future<bool> checkPermissionMenu({String? tierLevel, String? page}) async {
    print('checkPermissionMenu ' + tierLevel.toString());
    DocumentSnapshot<Map<String, dynamic>> ds = await FirebaseFirestore.instance
        .collection('tierController')
        .doc('controller')
        .collection(tierLevel.toString())
        .doc(page)
        .get();
    permissionMenu = ds.data()!['permission'];
    return permissionMenu;
  }
}
