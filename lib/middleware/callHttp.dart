import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:likewallet/app_config.dart';
import 'package:http/http.dart' as http;
import 'package:likewallet/model/sumSubToken.dart';

abstract class CallHttp {
  Future<SumSubToken> getSumSubAccessToken({String url, Map jsonMap});
  Future<StatusSumSub> callStatusSumSub({String url, Map jsonMap});
}

class OnCallHttp implements CallHttp {
  Future<SumSubToken> getSumSubAccessToken({String? url, Map? jsonMap}) async {
    HttpClient httpClient = new HttpClient();
    HttpClientRequest request =
        await httpClient.postUrl(Uri.parse(url.toString()));
    request.headers.set('content-type', 'application/json');
    request.headers
        .set('x-api-key', 'SzjUCCwNTAazIW1E7brZu8F1Dv36fF7L4mYTzzV9');
    request.add(utf8.encode(json.encode(jsonMap)));
    HttpClientResponse response = await request.close();
    // todo - you should check the response.statusCode
    String reply = await response.transform(utf8.decoder).join();
    var valueMap = SumSubToken.fromJson(jsonDecode(reply));
    httpClient.close();
    return valueMap;
  }

  Future<StatusSumSub> callStatusSumSub({String? url, Map? jsonMap}) async {
    HttpClient httpClient = new HttpClient();
    // print(url.toString());
    // print(json.encode(jsonMap));
    HttpClientRequest request =
        await httpClient.postUrl(Uri.parse(url.toString()));
    // print(request);
    request.headers.set('content-type', 'application/json');
    request.headers
        .set('x-api-key', 'SzjUCCwNTAazIW1E7brZu8F1Dv36fF7L4mYTzzV9');
    request.add(utf8.encode(json.encode(jsonMap)));
    HttpClientResponse response = await request.close();
    // todo - you should check the response.statusCode
    String reply = await response.transform(utf8.decoder).join();
    var valueMap = StatusSumSub.fromJson(jsonDecode(reply));
    httpClient.close();
    return valueMap;
  }
}
