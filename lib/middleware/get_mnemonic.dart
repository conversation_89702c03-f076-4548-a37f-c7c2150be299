import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:likewallet/libraryman/configuration_service.dart';
abstract class GetMnemonic {
  Future<String> getMnemonic();
  Future<String> getETHPK();
}
class MnemonicRetrieve implements GetMnemonic {
  late IConfigurationService configETH;

  late String mnemonic;
  late String pketh;
  @override
  Future<String> getMnemonic() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    configETH = new ConfigurationService(pref);
//    mnemonic = pref.getString('seed');
//    pketh = pref.getString('pk');
    mnemonic = await configETH.getMnemonic();
    pketh = await configETH.getPrivateKey();
//    print(mnemonic);
//    print(pketh);
    return mnemonic+':'+pketh;
  }
  Future<String> getETHPK() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    configETH = new ConfigurationService(pref);
//    pketh = pref.getString('pk');
    pketh = await configETH.getPrivateKey();
    return pketh;
  }



}