//Check contacts permission
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:likewallet/model/contact.dart';

import 'package:permission_handler/permission_handler.dart';

abstract class OnContact {
  Future<PermissionStatus> permissionContact();
  Future<List<Contacts>> getContacts({BuildContext context});
}

class CallContact implements OnContact {
  late Iterable<Contact> contacts;

  Future<PermissionStatus> permissionContact() async {
    final PermissionStatus permission = await Permission.contacts.status;
    if (permission != PermissionStatus.granted) {
      final Map<Permission, PermissionStatus> permissionStatus =
          await [Permission.contacts].request();
      return permissionStatus[Permission.contacts] ??
          PermissionStatus.denied;
    } else {
      return permission;
    }
  }

  Future<List<Contacts>> getContacts({BuildContext? context}) async {
    List phoneContact = [];
    // List phoneContactLAO = [];
    // List phoneContactKM = [];
    List<Contacts> getContactMobile = [];
    final PermissionStatus permissionStatus = await permissionContact();
    if (permissionStatus == PermissionStatus.granted) {
      contacts = await FlutterContacts.getContacts();

      contacts.forEach((element) async {
        element.phones.forEach((phone) async {
          if (phone.number.toString().substring(0, 1) == '0') {
            final p = phone.number.toString();
            phoneContact.add(p
                .replaceFirst('0', '+66')
                .replaceAll(' ', '')
                .replaceAll('-', ''));
            phoneContact.add(p
                .replaceFirst('0', '+885')
                .replaceAll(' ', '')
                .replaceAll('-', ''));
            phoneContact.add(p
                .replaceFirst('0', '+888')
                .replaceAll(' ', '')
                .replaceAll('-', ''));
          } else {}
        });
      });

      phoneContact.forEach((element) async {
        print(element);
        QuerySnapshot<Map<String, dynamic>> phone = await FirebaseFirestore.instance
            .collection('addressDNS')
            .where('phoneNumber', isEqualTo: element)
            .get();
        phone.docs.forEach((phone) async {
          print(phone.data()['phoneNumber']);
        });
        if (phone.docs.isNotEmpty) {
          phone.docs.forEach((phone) async {
            getContactMobile.add(Contacts.fromJson(phone.data()));
          });
          // print('getContactMobile $getContactMobile');
        } else {}
      });
      print('getContactMobile $getContactMobile');
    } else {
      //If permissions have been denied show standard cupertino alert dialog
      showDialog(
          context: context as BuildContext,
          builder: (BuildContext context) => CupertinoAlertDialog(
                title: Text('Permissions error'),
                content: Text('Please enable contacts access '
                    'permission in system settings'),
                actions: <Widget>[
                  CupertinoDialogAction(
                    child: Text('OK'),
                    onPressed: () => Navigator.of(context).pop(),
                  )
                ],
              ));
      // return '';
    }
    print(getContactMobile);
    //
    return Future.value();
  }
}
