// void _verifyOTPNoFirebase(String smsCode) async {
//   print(smsCode);
//   String url = env.apiUrl + '/verifyOTPNoFirebaseNew';
//   var response = await http
//       .post(url, body: {'phone_number': phoneNumber, 'codeVerify': smsCode});
//
//   print('Response status: ${response.statusCode}');
//   print('Response body: ${response.body}');
//   var body = json.decode(response.body);
//   if (body['statusCode'] == 200) {
//     final customToken = body['token'];
//
//     UserCredential user = await FirebaseAuth.instance
//         .signInWithCustomToken(customToken.toString())
//         .catchError((error) {
//       setState(() {
//         _saving = false;
//       });
//       showShortToast(
//           AppLocalizations.of(context)!.translate('otp_incorrect'), Colors.red);
//     });
//     //sign in firebase
//     setState(() {
//       _saving = false;
//     });
//
//     print('phoneNumber : ' + user.user.phoneNumber);
//
//     user.user.getIdToken().then((value) {
//       if (checkIf == 'register') {
//         Navigator.push(
//             context,
//             EnterExitRoute(
//                 exitPage: OTP_PAGE(),
//                 enterPage: SecretPassFirst(
//                     checkIf: checkIf,
//                     roundSMS: smsRound,
//                     codeVerify: passCode,
//                     phoneNumber: phoneNumber,
//                     firstName: firstName,
//                     lastName: lastName,
//                     refCode: refCode)));
//       } else {
//         Navigator.push(
//           context,
//           EnterExitRoute(
//               exitPage: OTP_PAGE(),
//               enterPage: SetPin(
//                   refCode: refCode,
//                   firstName: firstName,
//                   lastName: lastName,
//                   checkIf: checkIf,
//                   secret: "LikeWallet",
//                   roundSMS: smsRound,
//                   codeVerify: passCode,
//                   phoneNumber: phoneNumber,
//                   pinAgain: false)),
//         );
//       }
//     });
//   } else {
//     setState(() {
//       _saving = false;
//     });
//     showShortToast(
//         AppLocalizations.of(context)!.translate('otp_incorrect'), Colors.red);
//   }
// }

import 'dart:async';
import 'dart:convert';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'package:likewallet/app_config.dart';

abstract class CallLogin {
  Future verifyOTPNoFirebase({String smsCode});
}

class OnCallLogin implements CallLogin {
  bool login = false;

  @override
  Future<String> verifyOTPNoFirebase(
      {String? smsCode, String? phoneNumber}) async {
    print(smsCode);
    var url = Uri.https(env.apiUrl, '/verifyOTPNoFirebaseNew');
    var response = await http.post(url, body: {'phone_number': phoneNumber, 'codeVerify': smsCode});

    print('Response status: ${response.statusCode}');
    print('Response body: ${response.body}');
    var body = json.decode(response.body);
    if (body['statusCode'] == 200) {
      final customToken = body['token'];
      return customToken;
    } else {
      return 'null';
    }
  }
}
