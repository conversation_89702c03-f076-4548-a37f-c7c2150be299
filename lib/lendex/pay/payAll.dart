// import 'dart:async';
// import 'dart:async';
// import 'dart:convert';
// import 'dart:io';
//
// import 'package:adobe_xd/adobe_xd.dart';
// import 'package:currency_text_input_formatter/currency_text_input_formatter.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:intl/intl.dart';
// import 'package:likewallet/ImageTheme.dart';
// import 'package:likewallet/Theme.dart';
// import 'package:likewallet/device_utils.dart';
// import 'package:likewallet/lendex/account/account_lendex.dart';
// import 'package:likewallet/lendex/modal_confirm_lendex.dart';
// import 'package:likewallet/libraryman/app_local.dart';
// import 'package:likewallet/libraryman/custom_loading.dart';
// import 'package:likewallet/libraryman/ethcontractv2.dart';
// import 'package:likewallet/routes.dart';
// import 'package:likewallet/screen/home.dart';
// import 'package:likewallet/screen_util.dart';
// import 'package:intl/intl.dart' as formatIntl;
// import 'package:http/http.dart' as http;
// import 'package:likewallet/app_config.dart';
// import 'package:firebase_auth/firebase_auth.dart';
// import 'package:likewallet/main.dart';
// import 'package:likewallet/libraryman/address_service.dart';
// import 'package:likewallet/libraryman/configuration_service.dart';
// import 'package:likewallet/libraryman/ethcontract.dart';
// import 'package:likewallet/libraryman/auth.dart';
// import 'package:likewallet/libraryman/crypto.dart';
// import 'package:likewallet/libraryman/serviceHTTP.dart';
// import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:toast/toast.dart';
// import 'dart:ui';
// import 'package:flutter/painting.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:web3dart/web3dart.dart';
//
// class PayAllScreen extends StatefulWidget {
//   @override
//   _PayAllScreenState createState() => _PayAllScreenState();
// }
//
// class _PayAllScreenState extends State<PayAllScreen>
//     with TickerProviderStateMixin {
//   int boxNumber = 0;
//   var month = 0.0;
//   late AnimationController _animationController;
//   late AnimationController _controller;
//   late FocusNode amountFocusNode;
//   int statusBorrowStep = 0;
//   double rate = 100.00;
//   final f = new formatIntl.NumberFormat("###,###.##");
//   late String mnemonic;
//   String fiat = 'none';
//   final fireStore = FirebaseFirestore.instance;
//   late String addr;
//   bool _saving = false;
//   String amountValue = '0';
//   String amountPay = '0';
//   bool statusBorrow = false;
//   late String pketh;
//   late String ethAddr;
//   String amountUnlock = '0';
//   late String getPK;
//   late String addressETH;
//   String dept = "0";
//   String currentDept = "0";
//   String interestAmount = "0";
//   String borrowSum = "0";
//   /////////////////////////////////////////////////////////////
//   late AbstractServiceHTTP getfee;
//   late AbstractServiceHTTP APIHttp;
//   late IAddressService addressService;
//   late IConfigurationService configETH;
//   late CryptoEncryptInterface encrypt;
//   late BaseAuth auth;
//   late BaseETHV2 eth;
//   ///////////////////////////////////////////////////////////////
//
//   ///////////////////////////////////////////////////////////////
//   @override
//   void initState() {
//     // TODO: implement initState
//     super.initState();
//
//     _animationController =
//         new AnimationController(vsync: this, duration: Duration(seconds: 1));
//     _animationController.repeat();
//     amountFocusNode = FocusNode();
//     setInitState();
//   }
//
//   @override
//   void dispose() {
//     super.dispose();
//     _animationController.dispose();
//
//     amountFocusNode.dispose();
//   }
//
//   changeAmount(borrowSum) {
//     var valid = validateNumber(borrowSum.replaceAll(',', ''));
//     if (valid == 'match') {
//       setState(() {
//         amountValue = borrowSum.replaceAll(',', '');
//       });
//
//       if (borrowSum.length > 0 && borrowSum.isNotEmpty) {
//         setState(() {
//           // showbutton = true;
//         });
//       } else {
//         setState(() {
//           // showsymbol = false;
//           // showbutton = false;
//         });
//       }
//     } else {}
//   }
//
//   String validateNumber(String value) {
//     String pattern = r'(^[0-9.]{1,}$)';
//     RegExp regExp = new RegExp(pattern);
//     if (value.length == 0) {
//       print(value);
//       setState(() {
//         amountValue = '0';
//       });
//       return 'Please enter mobile number';
//     } else if (!regExp.hasMatch(value)) {
//       print(value + ' not match');
//       return 'Please enter valid mobile number';
//     }
//     print(value + ' match');
//     return 'match';
//   }
//
//   bool validateFirstNumber(String value) {
//     String pattern = r'(^[0-9]?\d)';
//     RegExp regExp = new RegExp(pattern);
//     if (value.length == 0) {
//       print(value);
//       return false;
//     } else if (!regExp.hasMatch(value)) {
//       print(value + ' not match2');
//
//       return false;
//     }
//     print(value + ' match2');
//     return true;
//   }
//
//   setInitState() async {
//     new Future.delayed(Duration.zero, () {
//       if (!mounted) return;
//       setState(() {
//         _saving = true;
//       });
//       new Future.delayed(new Duration(milliseconds: 500), () {
//         eth = new EthContractV2();
//
//         setInit();
//       });
//     });
//   }
//
//   Future setInit({source}) async {
//     SharedPreferences pref = await SharedPreferences.getInstance();
//     configETH = new ConfigurationService(pref);
//     addressService = new AddressService(configETH);
//
//     mnemonic = await configETH.getMnemonic();
//     if (!mounted) return;
//     setState(() {
//       addressETH = configETH.getAddress();
//     });
//
//     eth.getLoanV2(address: addressETH).then((loan) {
//       //callback //array 10 ตำแหน่ง ["0address คนยืม", "1.วลายืม", "2.อัตราดอกเบี้ย", "3.เวลาสิ้นสุดสัญญา", "4หนี้คงเหลือ", "5. ดอกเบี้ยที่จ่ายแล้ว", "6. จำนวนเงินต้น", "7. หลักประกัน", "8. จ่ายเงินต้นมาแล้วทั้งหมด", "9. จ่ายดอกเบี้ยมาแล้วทั้งหมด", "10. สถานะการกู้ยืม 1 ยังกู้อยู่ 0 หมดสัญญาแล้ว"]
//       print(loan);
//       if (loan[11].toString() == "1") {
//         eth.getCurrentDeptV2(address: addressETH).then((deptInterest) {
//           var totalDept =
//               EtherAmount.inWei(deptInterest[0][6] + deptInterest[1])
//                   .getInEther;
//           if (!mounted) return;
//           setState(() {
//             currentDept = totalDept.toString();
//             interestAmount = (BigInt.parse(deptInterest[1].toString()) /
//                     BigInt.parse("1000000000000000000"))
//                 .toString();
//             borrowSum =
//                 (double.parse(currentDept) + double.parse(interestAmount))
//                     .toString();
//             changeAmount(borrowSum);
//           });
//           print('currentDept is ' + currentDept);
//           print('interestAmount is ' + interestAmount);
//           print('borrowSum is ' + borrowSum);
//         });
//         print(EtherAmount.inWei(loan[6] as BigInt).getInEther.toString());
//         setState(() {
//           statusBorrow = true;
//           dept = EtherAmount.inWei(loan[6] as BigInt).getInEther.toString();
//           //รวมดอก
//           amountPay = f
//               .format(double.parse(interestAmount) + double.parse(dept))
//               .toString();
//         });
//       }
//     });
//     getPK = addressService.getPrivateKey(mnemonic);
//     print(addressETH);
//     //เช็คยอด
//     eth.getBalance(address: addressETH).then((balance) {
//       print(balance);
//       amountUnlock = balance.toString();
//     });
//     //เช็คยอดการล็อค
//     eth.getBalanceLock(address: addressETH).then((balanceLock) {
//       print(balanceLock);
//       Future.delayed(Duration(seconds: 1)).then((value) {});
//       if (!mounted) return;
//       setState(() {
//         if (source != 'loop') {
//           _saving = false;
//         }
//       });
//     });
//   }
//
//   Future<String> _RePayAll() async {
//     var tx = await eth.repayAllV2(pk: getPK);
//     return tx;
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return ModalProgressHUD(
//       opacity: 0.1,
//       inAsyncCall: _saving,
//       progressIndicator: CustomLoading(),
//       child: Scaffold(
//         body: SingleChildScrollView(
//           child: GestureDetector(
//             onTap: () {
//               DeviceUtils.hideKeyboard(context);
//             },
//             child: Container(
//               width: MediaQuery.of(context).size.width,
//               height: MediaQuery.of(context).size.height,
//               decoration: BoxDecoration(
//                 image: DecorationImage(
//                     image: AssetImage(
//                       LikeWalletImage.bg_lendex,
//                     ),
//                     fit: BoxFit.fitWidth,
//                     alignment: Alignment.topCenter),
//               ),
//               child: Stack(
//                 alignment: Alignment.topCenter,
//                 children: [
//                   head(),
//                   Positioned(
//                     bottom: 0,
//                     child: Container(
//                       width: 1080.w,
//                       height: 1742.h,
//                       decoration: BoxDecoration(
//                         gradient: LinearGradient(
//                           begin: Alignment(0.0, -1.0),
//                           end: Alignment(0.0, 1.0),
//                           colors: [
//                             const Color(0xff192330),
//                             const Color(0xff0f141f)
//                           ],
//                           stops: [0.0, 1.0],
//                         ),
//                       ),
//                       child: Column(
//                         children: [
//                           SizedBox(height: 188.0.h),
//                           Row(
//                             mainAxisAlignment: MainAxisAlignment.center,
//                             crossAxisAlignment: CrossAxisAlignment.center,
//                             children: [
//                               Text(
//                                 amountValue == ''
//                                     ? '0'
//                                     : validateFirstNumber(amountValue) == true
//                                         ? f
//                                             .format((double.parse(amountValue) /
//                                                 rate))
//                                             .toString()
//                                         : amountValue.substring(0, 1) == '.'
//                                             ? f.format((double.parse(
//                                                     '0' + amountValue) *
//                                                 rate))
//                                             : '0',
//                                 style: TextStyle(
//                                   fontFamily: 'Noto Sans',
//                                   fontSize: 36.h,
//                                   height: 3.h,
//                                   color: const Color(0x4dffffff),
//                                   letterSpacing: 3.6.w,
//                                 ),
//                                 textAlign: TextAlign.center,
//                               ),
//                               SizedBox(width: 10.0.w),
//                               Text(
//                                 AppLocalizations.of(context)!
//                             .translate('lendex_pay_pay_THB'),
//                                 style: TextStyle(
//                                   height: 3.h,
//                                   fontFamily: 'Noto Sans',
//                                   fontSize: 36.h,
//                                   color: const Color(0x4dffffff),
//                                   letterSpacing: 3.6.w,
//                                 ),
//                                 textAlign: TextAlign.center,
//                               ),
//                             ],
//                           ),
//                           SizedBox(height: 403.0.h),
//                           button(),
//                           SizedBox(height: 150.0.h),
//                           statusBorrowStep == 2
//                               ? Column(
//                                   children: [
//                                     Text(
//                                       AppLocalizations.of(context)!.translate(
//                                           'lendex_pay_pay_successed'),
//                                       style: TextStyle(
//                                         fontFamily: AppLocalizations.of(context)!
//                             .translate('font1'),
//                                         fontSize: 50.h,
//                                         color: Colors.white,
//                                         letterSpacing: 3.6.w,
//                                       ),
//                                       textAlign: TextAlign.center,
//                                     ),
//                                     SizedBox(height: 20.h),
//                                     Padding(
//                                       padding: EdgeInsets.symmetric(
//                                           horizontal: 75.w),
//                                       child: Text(
//                                         AppLocalizations.of(context)!
//                             .translate('borrow_success_detail'),
//                                         style: TextStyle(
//                                           fontFamily:
//                                               AppLocalizations.of(context)!
//                             .translate('font1'),
//                                           fontSize: 45.h,
//                                           color: Colors.white,
//                                           letterSpacing: 3.6.w,
//                                         ),
//                                         textAlign: TextAlign.center,
//                                       ),
//                                     )
//                                   ],
//                                 )
//                               : Container(),
//                         ],
//                       ),
//                     ),
//                   ),
//                   Positioned(
//                     top: 437.h,
//                     child: Container(
//                       width: 990.0.w,
//                       height: 306.0.h,
//                       decoration: BoxDecoration(
//                         borderRadius: BorderRadius.circular(153.0.h),
//                         color: const Color(0xff2a323b),
//                         boxShadow: [
//                           BoxShadow(
//                             color: const Color(0x40000000),
//                             offset: Offset(0, 35.h),
//                             blurRadius: 65.w,
//                           ),
//                         ],
//                       ),
//                       child: Row(
//                         children: [
//                           Container(
//                             margin: EdgeInsets.only(left: 82.w),
//                             child: Container(
//                               width: 95.85.h,
//                               height: 95.85.h,
//                               child: SvgPicture.string(
//                                 '<svg viewBox="127.1 552.0 95.9 95.9" ><path transform="translate(127.15, 552.0)" d="M 47.92578125 0 C 74.39439392089844 0 95.8515625 21.4571704864502 95.8515625 47.92578125 C 95.8515625 74.39439392089844 74.39439392089844 95.8515625 47.92578125 95.8515625 C 21.4571704864502 95.8515625 0 74.39439392089844 0 47.92578125 C 0 21.4571704864502 21.4571704864502 0 47.92578125 0 Z M 44.35530853271484 63.8041877746582 L 44.35530853271484 52.92523574829102 L 51.86148452758789 52.92523574829102 L 51.86148452758789 47.69253921508789 L 44.35530853271484 47.69253921508789 L 44.35530853271484 43.83451080322266 L 51.86148452758789 43.83451080322266 L 51.86148452758789 38.4428596496582 L 44.35530853271484 38.4428596496582 L 44.35530853271484 33.39248275756836 L 55.70153427124023 33.39248275756836 L 55.70153427124023 23.95410346984863 L 34.39553070068359 23.95410346984863 L 34.39553070068359 38.4428596496582 L 25.91586303710938 38.4428596496582 L 25.91586303710938 43.83451080322266 L 34.38075637817383 43.83451080322266 L 34.38075637817383 47.69253921508789 L 25.91586303710938 47.69253921508789 L 25.91586303710938 52.92523574829102 L 34.38075637817383 52.92523574829102 L 34.38075637817383 73.23976898193359 L 70.46586608886719 73.23976898193359 L 70.46586608886719 63.8013916015625 L 44.35530853271484 63.8041877746582 Z" fill="#0fe8d8" fill-opacity="0.9" stroke="none" stroke-width="1" stroke-opacity="0.9" stroke-miterlimit="10" stroke-linecap="butt" /></svg>',
//                                 allowDrawingOutsideViewBox: true,
//                               ),
//                             ),
//                           ),
//                           Expanded(
//                             child: Column(
//                               children: [
//                                 SizedBox(height: 46.h),
//                                 Container(
//                                   child: Text(
//                                     AppLocalizations.of(context)!
//                             .translate('lendex_pay_pay_likepoint'),
//                                     style: TextStyle(
//                                       fontFamily: 'Noto Sans',
//                                       fontSize: 36.h,
//                                       color: const Color(0x4dffffff),
//                                       letterSpacing: 1.08.w,
//                                     ),
//                                     textAlign: TextAlign.center,
//                                   ),
//                                 ),
//                                 SizedBox(height: 18.h),
//                                 SizedBox(
//                                   child: Text(
//                                     f
//                                         .format(double.parse(borrowSum))
//                                         .toString(),
//                                     style: TextStyle(
//                                       fontFamily: 'Noto Sans',
//                                       fontSize: 65.h,
//                                       color: const Color(0xffc9b890),
//                                       letterSpacing: 1.95.w,
//                                       fontWeight: FontWeight.w600,
//                                     ),
//                                   ),
//                                 ),
//                               ],
//                             ),
//                           ),
//                           Container(
//                             margin: EdgeInsets.only(left: 82.w),
//                             child: Container(
//                               width: 95.85.h,
//                               height: 95.85.h,
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           ),
//         ),
//       ),
//     );
//   }
//
//   Widget head() {
//     return Container(
//       child: Column(
//         children: [
//           SizedBox(height: 128.h),
//           rowHead(context),
//           SizedBox(height: 56.h),
//           SizedBox(
//             width: 738.0.w,
//             child: Text(
//               AppLocalizations.of(context)!.translate('lendex_pay_pay_loan') +
//                   ' #1 ',
//               style: TextStyle(
//                 fontFamily: AppLocalizations.of(context)!.translate('font1'),
//                 fontSize: 39.h,
//                 color: const Color(0xccffffff),
//                 letterSpacing: 1.17.w,
//               ),
//               textAlign: TextAlign.center,
//             ),
//           ),
//           SizedBox(
//             height: 81.h,
//           ),
//           SizedBox(
//             // width: 437.0.w,
//             child: Text(
//               AppLocalizations.of(context)!
//                             .translate('lendex_pay_total_pay_loan'),
//               style: TextStyle(
//                 fontFamily: 'Prompt',
//                 fontSize: 36.h,
//                 color: const Color(0xff2a323b),
//                 letterSpacing: 1.17.w,
//               ),
//               textAlign: TextAlign.center,
//             ),
//           ),
//         ],
//       ),
//     );
//   }
//
//   Widget selectSymbol() {
//     return SizedBox(
//       width: 871.w,
//       child: Row(
//         mainAxisAlignment: MainAxisAlignment.start,
//         children: [
//           point(),
//           SizedBox(
//             width: 34.w,
//           ),
//           Text(
//             'สกุลเงิน',
//             style: TextStyle(
//               fontFamily: AppLocalizations.of(context)!.translate('font1'),
//               fontSize: 39.h,
//               color: const Color(0xff908468),
//               letterSpacing: 1.9500000000000002.w,
//             ),
//             textAlign: TextAlign.left,
//           ),
//           Expanded(
//             child: Container(),
//           ),
//           point2()
//         ],
//       ),
//     );
//   }
//
//   Widget inputSymbol() {
//     return SizedBox(
//       width: 871.w,
//       child: Row(
//         children: [
//           SizedBox(
//             height: 142.h,
//             width: 549.w,
//             child: Stack(
//               alignment: Alignment.centerLeft,
//               children: [
//                 SvgPicture.string(
//                   '<svg viewBox="91.0 822.0 549.0 142.0" ><path transform="translate(91.0, 822.0)" d="M 71 0 L 478 0 C 517.2122192382812 0 549 31.78778076171875 549 71 C 549 110.2122192382812 517.2122192382812 142 478 142 L 71 142 C 31.78778076171875 142 0 110.2122192382812 0 71 C 0 31.78778076171875 31.78778076171875 0 71 0 Z" fill="#0e141c" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
//                   allowDrawingOutsideViewBox: true,
//                 ),
//                 Padding(
//                   padding: EdgeInsets.only(left: 92.w),
//                   child: Text(
//                     'LIKE',
//                     style: TextStyle(
//                       fontFamily: 'Proxima Nova',
//                       fontSize: 54.h,
//                       color: const Color(0xe5ffffff),
//                       letterSpacing: 5.4.w,
//                       fontWeight: FontWeight.w600,
//                     ),
//                     textAlign: TextAlign.left,
//                   ),
//                 ),
//               ],
//             ),
//           ),
//         ],
//       ),
//     );
//   }
//
//   Widget selectNumber() {
//     return SizedBox(
//       width: 871.w,
//       child: Row(
//         mainAxisAlignment: MainAxisAlignment.start,
//         children: [
//           point(),
//           SizedBox(
//             width: 34.w,
//           ),
//           Text(
//             'จำนวนที่ต้องการกู้ยืม?',
//             style: TextStyle(
//               fontFamily: AppLocalizations.of(context)!.translate('font1'),
//               fontSize: 39.h,
//               color: const Color(0xff908468),
//               letterSpacing: 1.9500000000000002.w,
//             ),
//             textAlign: TextAlign.left,
//           ),
//           Expanded(
//             child: Container(),
//           ),
//           point2()
//         ],
//       ),
//     );
//   }
//
//   Widget rowBox(data) {
//     return InkWell(
//       onTap: () {
//         setState(
//           () {
//             boxNumber = data;
//           },
//         );
//       },
//       child: Container(
//         margin: EdgeInsets.symmetric(horizontal: 7.w),
//         alignment: Alignment.center,
//         width: 146.0.h,
//         height: 146.0.h,
//         decoration: BoxDecoration(
//           borderRadius: BorderRadius.circular(47.0.h),
//           color: boxNumber == data ? Color(0xff00b5d0) : Color(0x66323c4a),
//         ),
//         child: Text(
//           data.toString() + "%",
//           style: TextStyle(
//             fontFamily: AppLocalizations.of(context)!.translate('font2'),
//             fontSize: 39.h,
//             color: boxNumber == data
//                 ? const Color(0xff000000)
//                 : const Color(0xff00b5d0),
//             fontWeight: boxNumber == data ? FontWeight.bold : FontWeight.normal,
//           ),
//           textAlign: TextAlign.center,
//         ),
//       ),
//     );
//   }
//
//   Widget time() {
//     return SizedBox(
//       width: 871.w,
//       child: Row(
//         mainAxisAlignment: MainAxisAlignment.start,
//         children: [
//           point(),
//           SizedBox(
//             width: 34.w,
//           ),
//           Text(
//             'ระยะเวลา? ',
//             style: TextStyle(
//               fontFamily: AppLocalizations.of(context)!.translate('font1'),
//               fontSize: 39.h,
//               color: const Color(0xff908468),
//               letterSpacing: 1.9500000000000002.w,
//             ),
//             textAlign: TextAlign.left,
//           ),
//           Text(
//             month.toInt().toString() + ' เดือน',
//             style: TextStyle(
//               fontFamily: AppLocalizations.of(context)!.translate('font1'),
//               fontSize: 39.h,
//               color: const Color(0xff908468),
//               letterSpacing: 1.9500000000000002.w,
//             ),
//             textAlign: TextAlign.left,
//           ),
//           Expanded(
//             child: Container(),
//           ),
//           point2()
//         ],
//       ),
//     );
//   }
//
//   Widget slider() {
//     return SizedBox(
//       width: 1000.0.w,
//       child: SliderTheme(
//         data: SliderTheme.of(context).copyWith(
//           //已拖动的颜色
//           activeTrackColor: Color(0xff00B5D0),
//           //未拖动的颜色
//           inactiveTrackColor: Color(0xff00B5D0).withOpacity(0.4),
//
//           //提示进度的气泡的背景色
//           valueIndicatorColor: Color(0xff00B5D0),
//           //提示进度的气泡文本的颜色
//           valueIndicatorTextStyle: TextStyle(
//             color: Colors.white,
//           ),
//
//           //滑块中心的颜色
//           thumbColor: Color(0xff00B5D0),
//           //滑块边缘的颜色
//           // overlayColor: Color(0xff00B5D0),
//
//           //divisions对进度线分割后，断续线中间间隔的颜色
//           inactiveTickMarkColor: Colors.white,
//         ),
//         child: Slider(
//           value: month,
//           label: month.toInt().toString(),
//           min: 0.0,
//           max: 12.0,
//           divisions: 10,
//           onChanged: (val) {
//             setState(() {
//               month = val.floorToDouble(); //转化成double
//             });
//           },
//         ),
//       ),
//     );
//   }
//
//   Widget button() {
//     return InkWell(
//       onTap: () {
//         if (statusBorrowStep == 2) {
//           Navigator.pushReplacement(
//             context,
//             MaterialPageRoute(
//               builder: (context) => AccountLenDexScreen(),
//             ),
//           );
//         } else {
//           checkAmount();
//         }
//       },
//       child: Container(
//         width: 445.0.w,
//         height: 124.0.h,
//         alignment: Alignment.center,
//         decoration: BoxDecoration(
//           borderRadius: BorderRadius.circular(71.0.h),
//           color: const Color(0xff908468),
//         ),
//         child: statusBorrowStep == 1
//             ? loading()
//             : statusBorrowStep == 2
//                 ? Padding(
//                     padding: EdgeInsets.only(left: 16.w),
//                     child: Row(
//                       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                       children: [
//                         Container(
//                             width: 99.0.h,
//                             height: 99.0.h,
//                             decoration: BoxDecoration(
//                               borderRadius: BorderRadius.all(
//                                   Radius.elliptical(9999.0, 9999.0)),
//                               color: const Color(0xff2a323b),
//                               boxShadow: [
//                                 BoxShadow(
//                                   color: const Color(0x59000000),
//                                   offset: Offset(0, 6.h),
//                                   blurRadius: 12.h,
//                                 ),
//                               ],
//                             ),
//                             child: Stack(
//                               alignment: Alignment.center,
//                               children: [
//                                 SizedBox(
//                                   height: 24.41.h,
//                                   child: Image.asset(
//                                     LikeWalletImage.icon_success,
//                                     fit: BoxFit.fitHeight,
//                                     color: Colors.white,
//                                   ),
//                                 ),
//                               ],
//                             )),
//                         Text(
//                           AppLocalizations.of(context)!
//                             .translate('borrow_back_to'),
//                           style: TextStyle(
//                             fontFamily: 'Prompt',
//                             fontSize: 37.h,
//                             color: const Color(0xff000000),
//                             letterSpacing: 1.1099999999999999.w,
//                             shadows: [
//                               Shadow(
//                                 color: const Color(0x29000000),
//                                 offset: Offset(0, 3.h),
//                                 blurRadius: 4.h,
//                               )
//                             ],
//                           ),
//                           textAlign: TextAlign.left,
//                         ),
//                         SizedBox(width: 50.w)
//                       ],
//                     ),
//                   )
//                 : Text(
//                     AppLocalizations.of(context)!.translate('lendex_pay_pay'),
//                     style: TextStyle(
//                       fontFamily: 'Prompt',
//                       fontSize: 50.h,
//                       color: const Color(0xff000000),
//                       letterSpacing: 1.5.w,
//                       shadows: [
//                         Shadow(
//                           color: const Color(0x29000000),
//                           offset: Offset(0, 3.h),
//                           blurRadius: 4.h,
//                         )
//                       ],
//                     ),
//                   ),
//       ),
//     );
//   }
//
//   Widget loading() {
//     return AnimatedBuilder(
//         animation: _animationController,
//         builder: (_, child) {
//           return Transform.rotate(
//             angle: _animationController.value * 6,
//             child: child,
//           );
//         },
//         child: Icon(Icons.refresh));
//   }
//
//   Widget point() {
//     return Container(
//       width: 12.0.h,
//       height: 12.0.h,
//       decoration: BoxDecoration(
//         borderRadius: BorderRadius.all(Radius.elliptical(9999.0, 9999.0)),
//         color: const Color(0xff908468),
//       ),
//     );
//   }
//
//   Widget point2() {
//     return Row(
//       children: [
//         Container(
//           width: 12.0.h,
//           height: 12.0.h,
//           decoration: BoxDecoration(
//             borderRadius: BorderRadius.all(Radius.elliptical(9999.0, 9999.0)),
//             color: const Color(0xff908468),
//           ),
//         ),
//         SizedBox(
//           width: 34.w,
//         ),
//         Container(
//           width: 12.0.h,
//           height: 12.0.h,
//           decoration: BoxDecoration(
//             borderRadius: BorderRadius.all(Radius.elliptical(9999.0, 9999.0)),
//             color: const Color(0xff908468),
//           ),
//         ),
//       ],
//     );
//   }
//
//   Future checkAmount() async {
//     print(dept);
//
//     if (double.parse(amountUnlock) < double.parse(dept)) {
//       showColoredToast(
//           AppLocalizations.of(context)!.translate('alert_like_limit_destitute'));
//     } else if (statusBorrowStep == 2) {
//       Navigator.of(context).pop();
//     } else {
//       if (statusBorrowStep == 0) {
//         setState(() => statusBorrowStep = 1);
//         final tx = await _RePayAll();
//         //   Future.delayed(Duration(seconds: 1)).then((value) => setState(() {
//         //         statusBorrowStep = 2;
//         //         _animationController.stop();
//         //       }));
//         //   print('success borrow');
//         //   setState(() {
//         //     borrowSum = "0";
//         //     amountValue = '0';
//         //   });
//         // });
//         var url = Uri.https(env.apiUrl, '/checkPending');
//         final response = await http.post(url, body: {"tx": tx});
//         if (response.statusCode == 200) {
//           var body = json.decode(response.body);
//           if (body['statusCode'] == 200) {
//             print('success borrow');
//             setState(() {
//               statusBorrowStep = 2;
//               borrowSum = "0";
//               amountValue = '0';
//               _animationController.stop();
//             });
//           }
//           //หรือ 202 ==false
//           else {
//             setState(() => statusBorrowStep = 0);
//             showShortToast(
//                 AppLocalizations.of(context)!.translate('save_err'), Colors.red);
//           }
//         } else {
//           setState(() => statusBorrowStep = 0);
//           showShortToast(
//               AppLocalizations.of(context)!.translate('save_err'), Colors.red);
//         }
//       }
//     }
//   }
//
//   Widget rowHead(context) {
//     return Row(
//       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//       children: [
//         statusBorrowStep == 2
//             ? Container(
//                 height: mediaQuery(context, 'height', 36.47),
//               )
//             : FlatButton(
//                 child: Container(
//                   padding: EdgeInsets.all(
//                     mediaQuery(context, 'height', 0),
//                   ),
//                   height: mediaQuery(context, 'height', 36.33),
//                   width: mediaQuery(context, 'height', 24.5),
//                   child: SvgPicture.string(
//                     '<svg viewBox="-281.5 3213.3 24.5 36.3" ><path  d="M -259.5249938964844 3249.675048828125 C -260.1029968261719 3249.675048828125 -260.6839904785156 3249.47607421875 -261.156005859375 3249.068115234375 L -281.5329895019531 3231.508056640625 L -261.156005859375 3213.947021484375 C -260.1109924316406 3213.0458984375 -258.531005859375 3213.162109375 -257.6300048828125 3214.208984375 C -256.72900390625 3215.2548828125 -256.8460083007812 3216.8330078125 -257.8919982910156 3217.73388671875 L -273.8739929199219 3231.508056640625 L -257.8919982910156 3245.281005859375 C -256.8460083007812 3246.18310546875 -256.72900390625 3247.760986328125 -257.6300048828125 3248.806884765625 C -258.125 3249.381103515625 -258.822998046875 3249.675048828125 -259.5249938964844 3249.675048828125 Z" fill="#231f20" stroke="none" stroke-width="1" stroke-miterlimit="10" stroke-linecap="butt" /></svg>',
//                     allowDrawingOutsideViewBox: true,
//                     fit: BoxFit.fill,
//                   ),
//                 ),
//                 onPressed: () {
//                   Navigator.of(context).pop();
//                   _animationController.stop();
//                 },
//               ),
//         FlatButton(
//           onPressed: () {
//             _animationController.stop();
//             AppRoutes.makeFirst(context, HomeLikewallet());
//           },
//           child: Image.asset(
//             LikeWalletImage.bg_home,
//             height: mediaQuery(context, "height", 44.47),
//           ),
//         ),
//       ],
//     );
//   }
// }
