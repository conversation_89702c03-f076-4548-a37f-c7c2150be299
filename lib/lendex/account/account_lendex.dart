// import 'dart:async';
// import 'package:adobe_xd/adobe_xd.dart';
// import 'package:delayed_display/delayed_display.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:intl/intl.dart';
// import 'package:likewallet/ImageTheme.dart';
// import 'package:likewallet/close_maintenance.dart';
// import 'package:likewallet/lendex/modal_confirm_lendex.dart';
// import 'package:likewallet/lendex/pay/payAll.dart';
// import 'package:likewallet/lendex/pay/payLimit.dart';
// import 'package:likewallet/libraryman/app_local.dart';
// import 'package:likewallet/lendex/lendex.dart';
// import 'dart:ui';
// import 'package:flutter/painting.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/rendering.dart';
// import 'package:intl/intl.dart' as formatIntl;
// import 'package:likewallet/libraryman/address_service.dart';
// import 'package:likewallet/libraryman/configuration_service.dart';
// import 'package:likewallet/libraryman/custom_loading.dart';
// import 'package:likewallet/libraryman/ethcontractv2.dart';
// import 'package:likewallet/libraryman/sharmir.dart';
// import 'package:likewallet/main.dart';
// import 'package:likewallet/middleware/check_maintenance/check_maintenance.dart';
// import 'package:likewallet/middleware/getLanguage.dart';
// import 'package:likewallet/model/pageStatus.dart';
// import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'dart:io' show Platform;
// import 'package:flutter_riverpod/flutter_riverpod.dart' as riverpod;
// import 'package:web3dart/web3dart.dart';
//
// class AccountLenDexScreen extends StatefulWidget {
//   @override
//   _AccountLenDexScreenState createState() => _AccountLenDexScreenState();
// }
//
// class _AccountLenDexScreenState extends State<AccountLenDexScreen>
//     with TickerProviderStateMixin {
//   bool status = false;
//   late TabController _tabController;
//   bool buttonLENDEX = false;
//   final f = new formatIntl.NumberFormat("###,###.##");
//   late BaseETHV2 eth;
//
//   TextEditingController loanAmount = TextEditingController();
//   TextEditingController amountBorrow = TextEditingController();
//   StreamController<String> streamAmountUnlock =
//       StreamController<String>.broadcast();
//   StreamController<String> streamAmountLock =
//       StreamController<String>.broadcast();
//   StreamController<String> streamTotalLike =
//       StreamController<String>.broadcast();
//   StreamController<String> streamInterestRate =
//       StreamController<String>.broadcast();
//   StreamController<String> streamDept = StreamController<String>.broadcast();
//   StreamController<String> streamInterest =
//       StreamController<String>.broadcast();
//   StreamController<String> streamRepayPrincipal =
//       StreamController<String>.broadcast();
//   StreamController<String> streamRepayInterest =
//       StreamController<String>.broadcast();
//   StreamController<String> streamAPY = StreamController<String>.broadcast();
//   FocusNode lockFocusNode = new FocusNode();
//   late OverlayEntry? overlayEntry;
//   late CheckAbout checkAbout;
//   late OnLanguage language;
//   bool _lock = true;
//   bool _all = false;
//   late IAddressService addressService;
//   late IConfigurationService configETH;
//   late SharmirInterface callSharmir;
//   bool _saving = false;
//   bool statusBorrow = false;
//   late String pketh;
//   late String mnemonic;
//   late String ethAddr;
//   String amountUnlock = '0';
//   late String getPK;
//   String totalLock = "";
//   late String addressETH;
//   String totalLike = "0";
//   String dept = "0";
//   String currentDept = "0";
//   String principal = "0";
//   String interestAmount = "0";
//   String apyTotal = "0";
//   DateTime? timeBorrow;
//   DateTime? timeFinal;
//   String? language_code = 'en';
//   String totalPay = '0';
//   String borrowSum = "0";
//
//   setInitState() async {
//     new Future.delayed(Duration.zero, () {
//       if (!mounted) return;
//       setState(() {
//         _saving = true;
//       });
//       new Future.delayed(new Duration(milliseconds: 500), () {
//         eth = new EthContractV2();
//         setInit();
//       });
//     });
//   }
//
//   checkFirst() async {
//     PageMaintenance statusPage = await checkAbout.checkTierPermission(
//         tierLevel: context.read(tierLevel).state, page: 'accountBorrow');
//     if (statusPage.status == 'active') {
//       //เริ่มทำงาน
//       print('active');
//       setInitState();
//     } else {
//       //ปิด maintenance
//       print('inactive');
//       final lang = await language.getLanguage();
//       final title = await checkAbout.selectLanguage(
//           language: lang, detail: statusPage.title);
//       final detail = await checkAbout.selectLanguage(
//           language: lang, detail: statusPage.detail);
//       final detailTime = await checkAbout.selectLanguage(
//           language: lang, detail: statusPage.detail_time);
//       Navigator.pushReplacement(
//         context,
//         MaterialPageRoute(
//             builder: (context) => CloseMaintenance(
//                   title: title,
//                   detail: detail,
//                   detailTime: detailTime,
//                   url: statusPage.url,
//                 )),
//       );
//     }
//   }
//
//   getLanguage() async {
//     var prefs = await SharedPreferences.getInstance();
//     language_code = await prefs.getString('language_code');
//     print(language_code);
//   }
//
//   Future setInit({source}) async {
//     SharedPreferences pref = await SharedPreferences.getInstance();
//     configETH = new ConfigurationService(pref);
//     addressService = new AddressService(configETH);
//     mnemonic = await configETH.getMnemonic();
//     if (!mounted) return;
//     setState(() {
//       addressETH = configETH.getAddress();
//     });
//
//     eth.getLoanV2(address: addressETH).then((loan) {
//       //callback //array 11 ตำแหน่ง ["0address คนยืม", "1.วลายืม", "2.อัตราดอกเบี้ย", "3.เวลาสิ้นสุดสัญญา", "4หนี้คงเหลือ", "5. ดอกเบี้ยที่จ่ายแล้ว", "6. จำนวนเงินต้น", "7. หลักประกัน", "8. จ่ายเงินต้นมาแล้วทั้งหมด", "9. จ่ายดอกเบี้ยมาแล้วทั้งหมด", "10. สถานะการกู้ยืม 1 ยังกู้อยู่ 0 หมดสัญญาแล้ว"]
//       setState(() {
//         // set ค่าจาก cllback //array 11loan ตำแหน่ง
//         String timeBor = BigInt.parse(loan[1].toString()).toString();
//         String timeFin = BigInt.parse(loan[3].toString()).toString();
//         // แปลงเวลายืม
//         timeBorrow =
//             DateTime.fromMillisecondsSinceEpoch(int.parse(timeBor) * 1000);
//         print("เวลา" + timeBorrow.toString());
//         // //เวลาสิ้นสุดสัญญา
//         timeFinal =
//             new DateTime.fromMillisecondsSinceEpoch(int.parse(timeFin) * 1000);
//         print("เวลา" + timeFinal.toString());
//       });
//
//       if (loan[11].toString() == "1") {
//         totalPay = (BigInt.parse(loan[8].toString()) /
//                 BigInt.parse("1000000000000000000"))
//             .toString();
//         eth.getCurrentDeptV2(address: addressETH).then((deptInterest) {
//           var totalDept =
//               EtherAmount.inWei(deptInterest[0][6] + deptInterest[1])
//                   .getInEther;
//           setState(() {
//             currentDept = totalDept.toString();
//             principal =
//                 EtherAmount.inWei(deptInterest[0][6]).getInEther.toString();
//             interestAmount = (BigInt.parse(deptInterest[1].toString()) /
//                     BigInt.parse("1000000000000000000"))
//                 .toString();
//             borrowSum = (double.parse(principal) + double.parse(interestAmount))
//                 .toString();
//           });
//           print('currentDept is ' + currentDept);
//           print('principal is ' + principal);
//           print('interestAmount is ' + interestAmount.toString());
//           print('interestAmount is ' + borrowSum.toString());
//         });
//         print(EtherAmount.inWei(loan[6] as BigInt).getInEther.toString());
//         setState(() {
//           statusBorrow = true;
//           dept = EtherAmount.inWei(loan[6] as BigInt).getInEther.toString();
//         });
//       }
//     });
//     eth.getInterestV2(address: addressETH).then((interestRate) {
//       streamInterestRate.sink.add(interestRate);
//     });
//     getPK = addressService.getPrivateKey(mnemonic);
//     print(addressETH);
//
//     //เช็คยอด
//     eth.getBalance(address: addressETH).then((balance) {
//       print(balance);
//
//       amountUnlock = balance.toString();
//       streamAmountUnlock.sink.add(amountUnlock);
//     });
//     //เช็คยอดการล็อค
//     eth.getBalanceLock(address: addressETH).then((balanceLock) {
//       print(balanceLock);
//       totalLock = balanceLock.toString();
//       streamAmountLock.sink.add(totalLock);
//       Future.delayed(Duration(seconds: 1)).then((value) {
//         totalLike =
//             (double.parse(amountUnlock) + double.parse(totalLock)).toString();
//         streamTotalLike.sink.add(totalLike);
//       });
//       if (!mounted) return;
//       setState(() {
//         if (source != 'loop') {
//           _saving = false;
//         }
//       });
//     });
//   }
//
//   @override
//   void dispose() {
//     super.dispose();
//   }
//
//   @override
//   void initState() {
//     // TODO: implement initState
//     super.initState();
//     checkAbout = OnCheckAbout();
//     language = CallLanguage();
//     _tabController = new TabController(vsync: this, length: 1);
//     // setInitState();
//     checkFirst();
//     getLanguage();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return DefaultTabController(
//       length: 1,
//       child: ModalProgressHUD(
//         opacity: 0.1,
//         inAsyncCall: _saving,
//         progressIndicator: CustomLoading(),
//         child: GestureDetector(
//           onTap: () {
//             setState(() {
//               buttonLENDEX = false;
//             });
//           },
//           child: Scaffold(
//             body: Container(
//               width: MediaQuery.of(context).size.width,
//               height: MediaQuery.of(context).size.height,
//               decoration: BoxDecoration(
//                 image: DecorationImage(
//                     image: AssetImage(
//                       LikeWalletImage.bg_lendex,
//                     ),
//                     fit: BoxFit.fitWidth,
//                     alignment: Alignment.topCenter),
//               ),
//               child: Stack(
//                 children: [
//                   head(),
//                   Positioned(
//                     bottom: -70.h,
//                     child: Container(
//                       width: 1080.0.w,
//                       height: 1807.0.h,
//                       decoration: BoxDecoration(
//                         borderRadius: BorderRadius.only(
//                             topLeft: Radius.circular(100.w),
//                             topRight: Radius.circular(100.w)),
//                         gradient: LinearGradient(
//                           begin: Alignment(0.0, -1.0),
//                           end: Alignment(0.0, 1.0),
//                           colors: [
//                             const Color(0xff192330),
//                             const Color(0xff0f141f)
//                           ],
//                           stops: [0.0, 1.0],
//                         ),
//                       ),
//                       child: TabBarView(controller: _tabController, children: [
//                         card(),
//                       ]),
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           ),
//         ),
//       ),
//     );
//   }
//
//   card() {
//     return Container(
//         padding: EdgeInsets.symmetric(horizontal: 75.w),
//         width: 870.w,
//         child: Stack(
//           children: [
//             Column(
//               children: [
//                 SizedBox(height: 141.h),
//                 lineStyle1(
//                     AppLocalizations.of(context)!
//                         .translate('lendex_account_currency'),
//                     'LIKE'),
//                 SizedBox(height: 14.h),
//                 lineStyle1(
//                     AppLocalizations.of(context)!
//                         .translate('lendex_account_contract'),
//                     timeBorrow == null
//                         ? '-'
//                         : DateFormat('dd MMM yyyy', language_code)
//                             .format(timeBorrow!)
//                             .toString()),
//                 SizedBox(height: 14.h),
//                 // lineStyle1(
//                 //   'ยอดเงินกู้',
//                 //   f.format(double.parse(principal)).toString(),
//                 // ),
//                 // SizedBox(height: 14.h),
//                 // lineStyle3(
//                 //   'ยอดชำระคืนเมื่อครบกำหนด',
//                 //   timeBorrow == null
//                 //       ? ''
//                 //       : f
//                 //           .format(double.parse(principal) +
//                 //               double.parse(interestAmount))
//                 //           .toString(),
//                 // ),
//                 // SizedBox(height: 14.h),
//                 // subText(),
//                 SizedBox(height: 14.h),
//                 lineStyle2(
//                     AppLocalizations.of(context)!
//                         .translate('lendex_account_current'),
//                     timeFinal == null
//                         ? '-'
//                         : DateFormat('dd MMM yyyy', language_code)
//                             .format(timeFinal!)
//                             .toString()),
//                 SizedBox(height: 66.h),
//                 LENDEX().border(),
//                 SizedBox(height: 66.h),
//                 lineStyle1(
//                   AppLocalizations.of(context)!
//                       .translate('lendex_account_loan_amount'),
//                   f.format(double.parse(borrowSum.toString())).toString(),
//                 ),
//                 SizedBox(height: 14.h),
//                 subText(),
//                 SizedBox(height: 32.h),
//                 InkWell(
//                   onTap: () {
//                     showDialog(
//                         context: context,
//                         barrierColor: Colors.transparent,
//                         builder: (BuildContext context) => WillPopScope(
//                             onWillPop: () {
//                               return Future.value();
//                             },
//                             child: LENDEX().popupQuestion661(
//                                 context,
//                                 AppLocalizations.of(context)!
//                                     .translate('lendex_account_popup_3'))));
//                   },
//                   child: Align(
//                       alignment: Alignment.centerLeft,
//                       child: LENDEX().point3()),
//                 ),
//                 SizedBox(height: 106.h),
//                 LENDEX().border(),
//                 SizedBox(height: 89.h),
//                 statusText(),
//                 SizedBox(height: 31.h),
//                 // lineStyle1(
//                 //   'ยอดรวมที่ชำระแล้ว',
//                 //   f.format(double.parse(totalPay.toString())).toString(),
//                 // ),
//                 SizedBox(height: 14.h),
//                 lineStyle1(
//                   AppLocalizations.of(context)!
//                       .translate('lendex_account_unpaid'),
//                   f.format(double.parse(borrowSum)).toString(),
//                 ),
//
//                 SizedBox(height: 49.5.h),
//                 // button(),
//                 Expanded(child: Container()),
//                 statusBorrow ? pay() : Container(),
//                 SizedBox(height: 115.5.h),
//               ],
//             ),
//             buttonLENDEX ? popupPay() : Container(),
//           ],
//         ));
//   }
//
//   Widget statusText() {
//     return Align(
//       alignment: Alignment.centerLeft,
//       child: Text(
//         AppLocalizations.of(context)!.translate('lendex_account_loan_status'),
//         style: TextStyle(
//           fontFamily: AppLocalizations.of(context)!.translate('font1'),
//           fontSize: 51.h,
//           color: const Color(0xe5908468),
//           letterSpacing: 0.*****************.w,
//           fontWeight: FontWeight.w600,
//           shadows: [
//             Shadow(
//               color: const Color(0x29000000),
//               offset: Offset(0, 3),
//               blurRadius: 6,
//             )
//           ],
//         ),
//         textAlign: TextAlign.left,
//       ),
//     );
//   }
//
//   Widget subText() {
//     return Row(
//       mainAxisAlignment: MainAxisAlignment.start,
//       children: [
//         Text(
//           AppLocalizations.of(context)!.translate('lendex_account_principal'),
//           style: TextStyle(
//             fontFamily: AppLocalizations.of(context)!.translate('font1'),
//             fontSize: 39.h,
//             color: const Color(0xff908468),
//             letterSpacing: 1.26.w,
//           ),
//           textAlign: TextAlign.left,
//         ),
//       ],
//     );
//   }
//
//   Widget head() {
//     return Container(
//       child: Column(
//         children: [
//           SizedBox(height: 130.h),
//           rowHead(context),
//           SizedBox(height: 20.h),
//           SizedBox(
//             width: 365.0.w,
//             child: Text(
//               AppLocalizations.of(context)!
//                   .translate('lendex_account_my_account'),
//               style: TextStyle(
//                 fontFamily: AppLocalizations.of(context)!.translate('font1'),
//                 fontSize: 42.h,
//                 color: const Color(0xccffffff),
//                 letterSpacing: 1.26.w,
//               ),
//               textAlign: TextAlign.center,
//             ),
//           ),
//           SizedBox(height: 93.h),
//           Align(
//             alignment: Alignment.centerLeft,
//             child: Container(
//               margin: EdgeInsets.only(left: 75.w),
//               width: 285.w,
//               height: 131.h,
//               decoration: BoxDecoration(
//                   boxShadow: [
//                     BoxShadow(
//                       color: const Color(0x59000000),
//                       offset: Offset(0, 12.h),
//                       blurRadius: 24.h,
//                     ),
//                   ],
//                   borderRadius: BorderRadius.circular(100.h),
//                   color: Color(0xff18222F)),
//               child: Align(
//                 alignment: Alignment.center,
//                 child: Text(
//                   AppLocalizations.of(context)!
//                           .translate('lendex_account_loan') +
//                       "#1",
//                   style: TextStyle(
//                     fontFamily:
//                         AppLocalizations.of(context)!.translate('font1'),
//                     fontSize: 36.h,
//                     color: Color(0xffFF9D00),
//                     letterSpacing: 1.08.w,
//                     fontWeight: FontWeight.w500,
//                   ),
//                 ),
//               ),
//             ),
//
//             // padding: EdgeInsets.only(left: 75.w),
//             // child: TabBar(
//             //     isScrollable: true,
//             //     indicatorSize: TabBarIndicatorSize.label,
//             //     unselectedLabelColor: Colors.white,
//             //     labelColor: Color(0xffFF9D00),
//             //     labelStyle: TextStyle(
//             //       fontFamily: AppLocalizations.of(context)!.translate('font1'),
//             //       fontSize: 36.h,
//             //       letterSpacing: 1.08.w,
//             //       fontWeight: FontWeight.w500,
//             //     ),
//             //     labelPadding: EdgeInsets.symmetric(horizontal: 10.w),
//             //     indicator: BoxDecoration(
//             //         boxShadow: [
//             //           BoxShadow(
//             //             color: const Color(0x59000000),
//             //             offset: Offset(0, 12.h),
//             //             blurRadius: 24.h,
//             //           ),
//             //         ],
//             //         borderRadius: BorderRadius.circular(100.h),
//             //         color: Color(0xff18222F)),
//             //     controller: _tabController,
//             //     onTap: (value) {
//             //       setState(() {
//             //         print(_tabController.index);
//             //       });
//             //     },
//             //     tabs: [
//             //       Tab(
//             //         child: Container(
//             //           width: 285.w,
//             //           height: 131.h,
//             //           decoration: BoxDecoration(
//             //               color: _tabController.index == 0
//             //                   ? Colors.transparent
//             //                   : Colors.white.withOpacity(0.25),
//             //               borderRadius: BorderRadius.circular(100.h)),
//             //           child: Align(
//             //             alignment: Alignment.center,
//             //             child: Text(AppLocalizations.of(context)
//             //                     .translate('lendex_account_loan') +
//             //                 "#1"),
//             //           ),
//             //         ),
//             //       ),
//             //     ]),
//           ),
//         ],
//       ),
//     );
//   }
//
//   Widget lineStyle1(String text1, String text2) {
//     return Row(
//       mainAxisAlignment: MainAxisAlignment.start,
//       children: [
//         textStyleTitle(text1),
//         Expanded(child: Container()),
//         Text(
//           text2 == null
//               ? ''
//               : statusBorrow
//                   ? text2
//                   : '-',
//           style: TextStyle(
//             fontFamily: AppLocalizations.of(context)!.translate('font1'),
//             fontSize: 39.h,
//             color: const Color(0xff908468),
//             letterSpacing: 1.26.w,
//           ),
//           textAlign: TextAlign.left,
//         ),
//       ],
//     );
//   }
//
//   Widget lineStyle2(String text1, String text2) {
//     return Row(
//       mainAxisAlignment: MainAxisAlignment.start,
//       children: [
//         textStyleTitle(text1),
//         Expanded(child: Container()),
//         Text(
//           text2 == null
//               ? ''
//               : statusBorrow
//                   ? text2
//                   : '-',
//           style: TextStyle(
//             fontFamily: AppLocalizations.of(context)!.translate('font1'),
//             fontSize: 39.h,
//             color: Color(0xffFF9D00),
//             letterSpacing: 1.26.w,
//           ),
//           textAlign: TextAlign.left,
//         ),
//       ],
//     );
//   }
//
//   Widget lineStyle3(String text1, String text2) {
//     return Row(
//       mainAxisAlignment: MainAxisAlignment.start,
//       children: [
//         textStyleTitle(text1),
//         Expanded(child: Container()),
//         Text(
//           text2,
//           style: TextStyle(
//             fontFamily: AppLocalizations.of(context)!.translate('font1'),
//             fontSize: 39.h,
//             color: Color(0xff00B5D0),
//             letterSpacing: 1.26.w,
//           ),
//           textAlign: TextAlign.left,
//         ),
//       ],
//     );
//   }
//
//   Widget lineStyle4(String text1, String text2) {
//     return Row(
//       mainAxisAlignment: MainAxisAlignment.start,
//       children: [
//         textStyleTitle(text1),
//         Expanded(child: Container()),
//         Text(
//           text2 == null ? '' : text2,
//           style: TextStyle(
//             fontFamily: AppLocalizations.of(context)!.translate('font1'),
//             fontSize: 39.h,
//             color: Color(0xffFFFFFF),
//             letterSpacing: 1.26.w,
//           ),
//           textAlign: TextAlign.left,
//         ),
//       ],
//     );
//   }
//
//   textStyleTitle(String text) {
//     return Text(
//       text,
//       style: TextStyle(
//         fontFamily: AppLocalizations.of(context)!.translate('font1'),
//         fontSize: 39.h,
//         color: const Color(0xff908468),
//         letterSpacing: 1.26.w,
//       ),
//       textAlign: TextAlign.left,
//     );
//   }
//
//   Widget button() {
//     return Align(
//       alignment: Alignment.centerRight,
//       child: InkWell(
//         onTap: () {
//           setState(() {
//             status = true;
//           });
//         },
//         child: Container(
//           alignment: Alignment.center,
//           decoration: BoxDecoration(
//             borderRadius: BorderRadius.circular(41.0.h),
//             border: Border.all(width: 2.0.w, color: const Color(0xb2ac9359)),
//           ),
//           width: 219.0.w,
//           height: 81.0.h,
//           child: Text(
//             'รายการ',
//             style: TextStyle(
//               fontFamily: AppLocalizations.of(context)!.translate('font1'),
//               fontSize: 36.h,
//               color: const Color(0xb2908468),
//               letterSpacing: 3.6.w,
//             ),
//             textAlign: TextAlign.center,
//           ),
//         ),
//       ),
//     );
//   }
//
//   Widget pay() {
//     return InkWell(
//       onTap: () {
//         if (statusBorrow) {
//           setState(() {
//             buttonLENDEX = !buttonLENDEX;
//           });
//         }
//       },
//       child: Align(
//         alignment: Alignment.centerRight,
//         child: SizedBox(
//           width: 155.0.h,
//           height: 155.0.h,
//           child: Stack(
//             children: <Widget>[
//               Pinned.fromSize(
//                 bounds: Rect.fromLTWH(0.0, 0.0, 155.0.h, 155.0.h),
//                 size: Size(155.0.h, 155.0.h),
//                 pinLeft: true,
//                 pinRight: true,
//                 pinTop: true,
//                 pinBottom: true,
//                 child: Container(
//                   decoration: BoxDecoration(
//                     borderRadius:
//                         BorderRadius.all(Radius.elliptical(9999.0.h, 9999.0.h)),
//                     color: const Color(0xff2a323b),
//                     boxShadow: [
//                       BoxShadow(
//                         color: const Color(0x59000000),
//                         offset: Offset(0, 12.h),
//                         blurRadius: 24.h,
//                       ),
//                     ],
//                   ),
//                 ),
//               ),
//               Pinned.fromSize(
//                 bounds: Rect.fromLTWH(47.0.w, 67.0.h, 61.0.w, 23.3.h),
//                 size: Size(155.0.w, 155.0.h),
//                 fixedWidth: true,
//                 fixedHeight: true,
//                 child:
//                     // Adobe XD layer: 'PAY' (group)
//                     BlendMask(
//                   blendMode: BlendMode.srcOver,
//                   child: Stack(
//                     children: <Widget>[
//                       Pinned.fromSize(
//                         bounds: Rect.fromLTWH(0.0, 0.0, 61.0.w, 23.3.h),
//                         size: Size(61.0.w, 23.3.h),
//                         pinLeft: true,
//                         pinRight: true,
//                         pinTop: true,
//                         pinBottom: true,
//                         child: BlendMask(
//                           blendMode: BlendMode.srcOver,
//                           child: Stack(
//                             children: <Widget>[
//                               Pinned.fromSize(
//                                 bounds: Rect.fromLTWH(0.0, 0.0, 16.7.w, 23.3.h),
//                                 size: Size(61.0.w, 23.3.h),
//                                 pinLeft: true,
//                                 pinTop: true,
//                                 pinBottom: true,
//                                 fixedWidth: true,
//                                 child: SvgPicture.string(
//                                   '<svg viewBox="1063.1 612.3 16.7 23.3" ><path  d="M 1065.990966796875 635.60498046875 L 1063.087036132812 635.60498046875 L 1063.087036132812 612.260986328125 L 1072.467041015625 612.260986328125 C 1074.72900390625 612.260986328125 1076.515014648438 612.93798828125 1077.821044921875 614.2899780273438 C 1079.128051757812 615.6439819335938 1079.781005859375 617.31298828125 1079.781005859375 619.2960205078125 C 1079.781005859375 621.2789916992188 1079.121948242188 622.947021484375 1077.803955078125 624.301025390625 C 1076.484985351562 625.6539916992188 1074.70703125 626.3300170898438 1072.467041015625 626.3300170898438 L 1065.990966796875 626.3300170898438 L 1065.990966796875 635.60498046875 Z M 1072.115966796875 623.739990234375 C 1073.493041992188 623.739990234375 1074.613037109375 623.3259887695312 1075.47705078125 622.4979858398438 C 1076.339965820312 621.6699829101562 1076.77099609375 620.60302734375 1076.77099609375 619.2960205078125 C 1076.77099609375 617.989013671875 1076.339965820312 616.9219970703125 1075.47705078125 616.093017578125 C 1074.613037109375 615.2650146484375 1073.493041992188 614.8510131835938 1072.115966796875 614.8510131835938 L 1065.990966796875 614.8510131835938 L 1065.990966796875 623.739990234375 L 1072.115966796875 623.739990234375 Z" fill="#02dbfc" stroke="none" stroke-width="1" stroke-miterlimit="10" stroke-linecap="butt" /></svg>',
//                                   allowDrawingOutsideViewBox: true,
//                                   fit: BoxFit.fill,
//                                 ),
//                               ),
//                               Pinned.fromSize(
//                                 bounds:
//                                     Rect.fromLTWH(17.2.w, 0.0, 22.4.w, 23.3.h),
//                                 size: Size(61.0.w, 23.3.h),
//                                 pinTop: true,
//                                 pinBottom: true,
//                                 fixedWidth: true,
//                                 child: SvgPicture.string(
//                                   '<svg viewBox="1080.3 612.3 22.4 23.3" ><path  d="M 1102.671020507812 635.60498046875 L 1099.347045898438 635.60498046875 L 1097.281005859375 630.426025390625 L 1085.661010742188 630.426025390625 L 1083.597045898438 635.60498046875 L 1080.27099609375 635.60498046875 L 1089.651000976562 612.260986328125 L 1093.255981445312 612.260986328125 L 1102.671020507812 635.60498046875 Z M 1096.406005859375 627.8359985351562 L 1091.472045898438 615.27099609375 L 1086.5009765625 627.8359985351562 L 1096.406005859375 627.8359985351562 Z" fill="#02dbfc" stroke="none" stroke-width="1" stroke-miterlimit="10" stroke-linecap="butt" /></svg>',
//                                   allowDrawingOutsideViewBox: true,
//                                   fit: BoxFit.fill,
//                                 ),
//                               ),
//                               Pinned.fromSize(
//                                 bounds:
//                                     Rect.fromLTWH(39.8.w, 0.0, 21.3.w, 23.3.h),
//                                 size: Size(61.0.w, 23.3.h),
//                                 pinRight: true,
//                                 pinTop: true,
//                                 pinBottom: true,
//                                 fixedWidth: true,
//                                 child: SvgPicture.string(
//                                   '<svg viewBox="1102.8 612.3 21.3 23.3" ><path  d="M 1114.9560546875 635.60498046875 L 1112.052001953125 635.60498046875 L 1112.052001953125 625.7349853515625 L 1102.847045898438 612.260986328125 L 1106.240966796875 612.260986328125 L 1113.485961914062 623.1099853515625 L 1120.73095703125 612.260986328125 L 1124.126953125 612.260986328125 L 1114.9560546875 625.7349853515625 L 1114.9560546875 635.60498046875 Z" fill="#02dbfc" stroke="none" stroke-width="1" stroke-miterlimit="10" stroke-linecap="butt" /></svg>',
//                                   allowDrawingOutsideViewBox: true,
//                                   fit: BoxFit.fill,
//                                 ),
//                               ),
//                             ],
//                           ),
//                         ),
//                       ),
//                     ],
//                   ),
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
//
//   Widget point() {
//     return Container(
//       width: 12.0.h,
//       height: 12.0.h,
//       decoration: BoxDecoration(
//         borderRadius: BorderRadius.all(Radius.elliptical(9999.0, 9999.0)),
//         color: const Color(0xff908468),
//       ),
//     );
//   }
//
//   Widget point2() {
//     return Row(
//       children: [
//         Container(
//           width: 12.0.h,
//           height: 12.0.h,
//           decoration: BoxDecoration(
//             borderRadius: BorderRadius.all(Radius.elliptical(9999.0, 9999.0)),
//             color: const Color(0xff908468),
//           ),
//         ),
//         SizedBox(
//           width: 34.w,
//         ),
//         Container(
//           width: 12.0.h,
//           height: 12.0.h,
//           decoration: BoxDecoration(
//             borderRadius: BorderRadius.all(Radius.elliptical(9999.0, 9999.0)),
//             color: const Color(0xff908468),
//           ),
//         ),
//       ],
//     );
//   }
//
//   popupPay() {
//     return AnimatedPositioned(
//       duration: Duration(milliseconds: 0),
//       bottom: buttonLENDEX ? 195.h : 0.h,
//       right: buttonLENDEX ? 102.w : 0,
//       child: InkWell(
//         onTap: () {
//           setState(() => buttonLENDEX = !buttonLENDEX);
//         },
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.end,
//           children: [
//             DelayedDisplay(
//               delay: Duration(milliseconds: 0),
//               slidingCurve: Curves.easeInOut,
//               child: InkWell(
//                 onTap: () {
//                   setState(() => buttonLENDEX = false);
//                   Navigator.push(
//                           context,
//                           MaterialPageRoute(
//                               builder: (context) => PayLimitScreen()))
//                       .then((value) {
//                     setState(() => buttonLENDEX = false);
//                     setInitState();
//                   });
//                 },
//                 child: new LENDEX().payLimit(context),
//               ),
//             ),
//             SizedBox(height: 30.h),
//             DelayedDisplay(
//               delay: Duration(milliseconds: 50),
//               slidingCurve: Curves.easeInOut,
//               child: InkWell(
//                 onTap: () {
//                   setState(() => buttonLENDEX = false);
//                   Navigator.push(
//                       context,
//                       MaterialPageRoute(
//                           builder: (context) => PayAllScreen())).then((value) {
//                     setState(() => buttonLENDEX = false);
//                     setInitState();
//                   });
//                 },
//                 child: LENDEX().payAll(context),
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
