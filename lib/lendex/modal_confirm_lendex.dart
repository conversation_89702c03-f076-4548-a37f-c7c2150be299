// import 'package:adobe_xd/adobe_xd.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:likewallet/ImageTheme.dart';
// import 'package:likewallet/Theme.dart';
// import 'package:likewallet/animationPage.dart';
// import 'package:likewallet/lendex/lendex_step2.dart';
// import 'package:likewallet/libraryman/app_local.dart';
// import 'package:likewallet/routes.dart';
// import 'package:likewallet/screen/home.dart';
// import 'package:likewallet/screen/navigationbar/home/<USER>';
// import 'package:likewallet/screen_util.dart';
// import 'package:intl/intl.dart' as formatIntl;
// import 'lendex_step1.dart';
//
// class CustomDialog extends StatefulWidget {
//   final String? borrowDay, borrowAPY, borrowInterest;
//   final String? borrowAmount, borrowSum;
//   final String? borrowGuarantee;
//   CustomDialog({
//     @required this.borrowDay,
//     @required this.borrowAPY,
//     @required this.borrowInterest,
//     @required this.borrowAmount,
//     @required this.borrowSum,
//     @required this.borrowGuarantee,
//   });
//
//   _CustomDialog createState() => new _CustomDialog();
// }
//
// class _CustomDialog extends State<CustomDialog> with TickerProviderStateMixin {
//   final f = new formatIntl.NumberFormat("###,###.##");
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: Colors.transparent,
//       body: dialogContent(context),
//     );
//   }
//
//   dialogContent(BuildContext context) {
//     return Align(
//       alignment: Alignment.center,
//       child: Container(
//         width: 1000.0.w,
//         height: 1711.0.h,
//         decoration: BoxDecoration(
//           borderRadius: BorderRadius.circular(100.0.h),
//           gradient: LinearGradient(
//             begin: Alignment(0.0, -1.0),
//             end: Alignment(0.0, 1.0),
//             colors: [const Color(0xff192330), const Color(0xff202b3a)],
//             stops: [0.0, 1.0],
//           ),
//           boxShadow: [
//             BoxShadow(
//               color: const Color(0x99000000),
//               offset: Offset(0, 80.h),
//               blurRadius: 99.h,
//             ),
//           ],
//         ),
//         child: SingleChildScrollView(
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.center,
//             children: [
//               SizedBox(height: 137.h),
//               text(
//                   context,
//                   AppLocalizations.of(context)!
//                             .translate('lendex_borrow_modal_line1') +
//                       widget.borrowDay.toString(),
//                   widget.borrowSum),
//               SizedBox(height: 52.h),
//               text(
//                   context,
//                   AppLocalizations.of(context)!
//                             .translate('lendex_borrow_modal_line2'),
//                   widget.borrowAPY.toString() + " %"),
//               SizedBox(height: 52.h),
//               text(
//                   context,
//                   AppLocalizations.of(context)!
//                             .translate('lendex_borrow_modal_line3'),
//                   widget.borrowInterest),
//               SizedBox(height: 67.h),
//               SizedBox(
//                 width: 653.w,
//                 child: SvgPicture.string(
//                   '<svg viewBox="214.0 930.0 653.0 4.0" ><defs><linearGradient id="gradient" x1="0.920288" y1="0.0" x2="0.039928" y2="0.0"><stop offset="0.0" stop-color="#008b98c2" stop-opacity="0.0" /><stop offset="0.246305" stop-color="#b38b98c2" stop-opacity="0.7" /><stop offset="0.522168" stop-color="#ff8b98c2"  /><stop offset="0.768473" stop-color="#b38b98c2" stop-opacity="0.7" /><stop offset="1.0" stop-color="#1a8b98c2" stop-opacity="0.1" /></linearGradient></defs><path transform="translate(214.0, 930.0)" d="M 0 0 L 653 0 L 653 4 L 0 4 L 0 0 Z" fill="url(#gradient)" fill-opacity="0.3" stroke="none" stroke-width="1" stroke-opacity="0.3" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
//                   allowDrawingOutsideViewBox: true,
//                 ),
//               ),
//               SizedBox(height: 71.h),
//               text(
//                   context,
//                   AppLocalizations.of(context)!
//                             .translate('lendex_borrow_modal_line4'),
//                   widget.borrowAmount),
//               SizedBox(height: 52.h),
//               text(
//                   context,
//                   AppLocalizations.of(context)!
//                             .translate('lendex_borrow_modal_line5'),
//                   widget.borrowSum),
//               SizedBox(height: 67.h),
//               SizedBox(
//                 width: 653.w,
//                 child: SvgPicture.string(
//                   '<svg viewBox="214.0 930.0 653.0 4.0" ><defs><linearGradient id="gradient" x1="0.920288" y1="0.0" x2="0.039928" y2="0.0"><stop offset="0.0" stop-color="#008b98c2" stop-opacity="0.0" /><stop offset="0.246305" stop-color="#b38b98c2" stop-opacity="0.7" /><stop offset="0.522168" stop-color="#ff8b98c2"  /><stop offset="0.768473" stop-color="#b38b98c2" stop-opacity="0.7" /><stop offset="1.0" stop-color="#1a8b98c2" stop-opacity="0.1" /></linearGradient></defs><path transform="translate(214.0, 930.0)" d="M 0 0 L 653 0 L 653 4 L 0 4 L 0 0 Z" fill="url(#gradient)" fill-opacity="0.3" stroke="none" stroke-width="1" stroke-opacity="0.3" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
//                   allowDrawingOutsideViewBox: true,
//                 ),
//               ),
//               SizedBox(height: 67.h),
//               text2(
//                   context,
//                   AppLocalizations.of(context)!
//                             .translate('lendex_borrow_modal_line6'),
//                   widget.borrowGuarantee),
//               SizedBox(height: 115.h),
//               Row(
//                 mainAxisAlignment: MainAxisAlignment.center,
//                 children: [
//                   closeButton(context),
//                   SizedBox(width: 29.w),
//                   nextButton(context),
//                 ],
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
//
//   text(context, text1, text2) {
//     return Column(
//       children: [
//         SizedBox(
//           // width: 730.0.w,
//           child: Text(
//             text1,
//             style: TextStyle(
//               fontFamily: AppLocalizations.of(context)!.translate('font1'),
//               fontSize: 42.h,
//               color: const Color(0xff908468),
//               letterSpacing: 1.26.w,
//             ),
//             textAlign: TextAlign.center,
//           ),
//         ),
//         SizedBox(height: 16.h),
//         SizedBox(
//           width: 372.0.w,
//           child: Text(
//             text2,
//             style: TextStyle(
//               fontFamily: 'Noto Sans',
//               fontSize: 42.h,
//               color: const Color(0xff00b5d0),
//               letterSpacing: 0.4,
//               fontWeight: FontWeight.w700,
//             ),
//             textAlign: TextAlign.center,
//           ),
//         ),
//       ],
//     );
//   }
//
//   text2(context, text1, text2) {
//     return Column(
//       children: [
//         SizedBox(
//           width: 730.0.w,
//           child: Text(
//             text1,
//             style: TextStyle(
//               fontFamily: AppLocalizations.of(context)!.translate('font1'),
//               fontSize: 42.h,
//               color: const Color(0xff908468),
//               letterSpacing: 1.26.w,
//             ),
//             textAlign: TextAlign.center,
//           ),
//         ),
//         SizedBox(height: 16.h),
//         SizedBox(
//           width: 372.0.w,
//           child: Text(
//             text2,
//             style: TextStyle(
//               fontFamily: 'Noto Sans',
//               fontSize: 42.h,
//               color: const Color(0xffFFFFFF),
//               letterSpacing: 0.4,
//               fontWeight: FontWeight.w700,
//             ),
//             textAlign: TextAlign.center,
//           ),
//         ),
//       ],
//     );
//   }
//
//   closeButton(context) {
//     return InkWell(
//       onTap: () => Navigator.of(context).pop(),
//       child: Container(
//         width: 142.0.h,
//         height: 142.0.h,
//         decoration: BoxDecoration(
//           borderRadius: BorderRadius.circular(47.0.h),
//           color: const Color(0xff323c4a),
//         ),
//         child: Stack(
//           alignment: Alignment.center,
//           children: <Widget>[
//             Image.asset(
//               LikeWalletImage.icon_lendex_close,
//               fit: BoxFit.contain,
//               height: 23.5.h,
//             ),
//           ],
//         ),
//       ),
//     );
//   }
//
//   nextButton(context) {
//     return InkWell(
//       onTap: () async {
//         Navigator.of(context).pop();
//         await Navigator.push(
//             context,
//             MaterialPageRoute(
//                 builder: (context) => LenDexStep2Screen(
//                       borrowGuarantee:
//                           widget.borrowGuarantee.toString().replaceAll(',', ''),
//                       borrowAmount: widget.borrowAmount.toString().replaceAll(',', ''),
//                     ))).then((callback) {});
//       },
//       child: Container(
//         width: 597.0.w,
//         height: 142.0.h,
//         decoration: BoxDecoration(
//           borderRadius: BorderRadius.circular(47.0.h),
//           color: const Color(0xff323c4a),
//         ),
//         alignment: Alignment.center,
//         child: SizedBox(
//           width: 133.0.w,
//           child: Text(
//             AppLocalizations.of(context)!
//                             .translate('lendex_borrow_modal_button'),
//             style: TextStyle(
//               fontFamily: AppLocalizations.of(context)!.translate('font1'),
//               fontSize: 46.h,
//               color: const Color(0xff00b5d0),
//               letterSpacing: 1.38.w,
//               height: 0.7608695652173914,
//             ),
//             textAlign: TextAlign.center,
//           ),
//         ),
//       ),
//     );
//   }
// }
//
// Widget rowHead(context) {
//   return Row(
//     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//     children: [
//       // FlatButton(
//       //   child: Container(
//       //     // margin: EdgeInsets.only(
//       //     //   top: mediaQuery(context, 'height', 30.47),
//       //     //   left: mediaQuery(context, 'width', 75),
//       //     // ),
//       //     height: mediaQuery(context, 'height', 44.47),
//       //     width: mediaQuery(context, 'height', 44.47),
//       //     child: Image.asset(
//       //       LikeWalletImage.icon_back_button,
//       //       height: mediaQuery(context, "height", 44.47),
//       //       color: Colors.black,
//       //     ),
//       //   ),
//       //   onPressed: () {
//       //     AppRoutes.makeFirst(context, HomeLikewallet());
//       //   },
//       // ),
//       FlatButton(
//         child: Container(
//           padding: EdgeInsets.all(
//             mediaQuery(context, 'height', 0),
//           ),
//           height: mediaQuery(context, 'height', 36.33),
//           width: mediaQuery(context, 'height', 24.5),
//           child: SvgPicture.string(
//             '<svg viewBox="-281.5 3213.3 24.5 36.3" ><path  d="M -259.5249938964844 3249.675048828125 C -260.1029968261719 3249.675048828125 -260.6839904785156 3249.47607421875 -261.156005859375 3249.068115234375 L -281.5329895019531 3231.508056640625 L -261.156005859375 3213.947021484375 C -260.1109924316406 3213.0458984375 -258.531005859375 3213.162109375 -257.6300048828125 3214.208984375 C -256.72900390625 3215.2548828125 -256.8460083007812 3216.8330078125 -257.8919982910156 3217.73388671875 L -273.8739929199219 3231.508056640625 L -257.8919982910156 3245.281005859375 C -256.8460083007812 3246.18310546875 -256.72900390625 3247.760986328125 -257.6300048828125 3248.806884765625 C -258.125 3249.381103515625 -258.822998046875 3249.675048828125 -259.5249938964844 3249.675048828125 Z" fill="#231f20" stroke="none" stroke-width="1" stroke-miterlimit="10" stroke-linecap="butt" /></svg>',
//             allowDrawingOutsideViewBox: true,
//             fit: BoxFit.fill,
//           ),
//         ),
//         onPressed: () {
//           AppRoutes.makeFirst(context, HomeLikewallet());
//         },
//       ),
//       FlatButton(
//         onPressed: () {
//           AppRoutes.makeFirst(context, HomeLikewallet());
//         },
//         child: Image.asset(
//           LikeWalletImage.bg_home,
//           height: mediaQuery(context, "height", 44.47),
//         ),
//       ),
//     ],
//   );
// }
