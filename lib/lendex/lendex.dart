// import 'package:flutter/material.dart';
// import 'package:likewallet/ImageTheme.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:likewallet/lendex/pay/payAll.dart';
// import 'package:likewallet/lendex/pay/payLimit.dart';
// import 'package:likewallet/libraryman/app_local.dart';
// import 'package:likewallet/animationPage.dart';
// import 'package:likewallet/lendex/account/account_lendex.dart';
// import 'package:likewallet/lendex/lendex_step1.dart';
// import 'package:adobe_xd/pinned.dart';
//
// class LENDEX {
//   Widget iconLENDEX() {
//     return Image.asset(
//       LikeWalletImage.icon_lendex,
//       fit: BoxFit.contain,
//       height: 23.97.h,
//     );
//   }
//
//   Widget myAccountButton(context) {
//     return InkWell(
//       onTap: () => Navigator.push(
//         context,
//         MaterialPageRoute(builder: (context) => AccountLenDexScreen()),
//       ),
//       child: SizedBox(
//         height: 115.h,
//         width: 370.w,
//         child: Stack(
//           alignment: Alignment.center,
//           children: [
//             SvgPicture.string(
//               '<svg viewBox="126.0 1572.0 370.0 115.0" ><defs><filter id="shadow"><feDropShadow dx="12" dy="24" stdDeviation="35"/></filter></defs><path transform="translate(126.0, 1572.0)" d="M 312.************* 0 L 57.************** 0 C 25.************** 0 0 25.************** 0 57 L 0 58 C 0 89.************** 25.************** 115 57.************** 115 L 312.************* 115 C 344.************* 115 370 89.************** 370 58 L 370 57 C 370 25.************** 344.************* 0 312.************* 0 Z" fill="#00b5d0" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" filter="url(#shadow)"/></svg>',
//               allowDrawingOutsideViewBox: true,
//               fit: BoxFit.fill,
//             ),
//             Text(
//               'My Account ',
//               style: TextStyle(
//                 fontFamily: AppLocalizations.of(context)!.translate('font1'),
//                 fontSize: 39.h,
//                 color: const Color(0xff000000),
//                 letterSpacing: 3.****************.w,
//                 fontWeight: FontWeight.w600,
//                 shadows: [
//                   Shadow(
//                     color: const Color(0x29000000),
//                     offset: Offset(0, 3.h),
//                     blurRadius: 6.h,
//                   )
//                 ],
//               ),
//               textAlign: TextAlign.left,
//             ),
//           ],
//         ),
//       ),
//     );
//   }
//
//   Widget borowButton(context) {
//     return InkWell(
//       onTap: () => Navigator.push(
//         context,
//         MaterialPageRoute(builder: (context) => LenDexStep1Screen()),
//       ),
//       child: SizedBox(
//         height: 115.h,
//         width: 253.w,
//         child: Stack(
//           alignment: Alignment.center,
//           children: [
//             SvgPicture.string(
//               '<svg viewBox="126.0 1717.0 253.0 115.0" ><defs><filter id="shadow"><feDropShadow dx="12" dy="24" stdDeviation="35"/></filter></defs><path transform="translate(126.0, 1717.0)" d="M 196 0 L 57 0 C 25.519775390625 0 0 25.************** 0 57 L 0 58 C 0 89.************** 25.519775390625 115 57 115 L 196 115 C 227.480224609375 115 253 89.************** 253 58 L 253 57 C 253 25.************** 227.480224609375 0 196 0 Z" fill="#00b5d0" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" filter="url(#shadow)"/></svg>',
//               allowDrawingOutsideViewBox: true,
//               fit: BoxFit.fill,
//             ),
//             Text(
//               'Borow',
//               style: TextStyle(
//                 fontFamily: AppLocalizations.of(context)!.translate('font1'),
//                 fontSize: 39.h,
//                 color: const Color(0xff000000),
//                 letterSpacing: 3.****************.w,
//                 fontWeight: FontWeight.w600,
//                 shadows: [
//                   Shadow(
//                     color: const Color(0x29000000),
//                     offset: Offset(0, 3.h),
//                     blurRadius: 6.h,
//                   )
//                 ],
//               ),
//               textAlign: TextAlign.left,
//             ),
//           ],
//         ),
//       ),
//     );
//   }
//
//   Widget payLimit(context) {
//     return SizedBox(
//       height: 115.h,
//       width: 370.w,
//       child: Stack(
//         alignment: Alignment.center,
//         children: [
//           Container(
//             decoration: BoxDecoration(
//               borderRadius: BorderRadius.circular(79.0.h),
//               color: const Color(0xff2a323b),
//               boxShadow: [
//                 BoxShadow(
//                   color: const Color(0x59000000),
//                   offset: Offset(9.w, 24.h),
//                   blurRadius: 35.h,
//                 ),
//               ],
//             ),
//           ),
//           Text(
//             AppLocalizations.of(context)!.translate('lendex_button_pay_1'),
//             style: TextStyle(
//               fontFamily: AppLocalizations.of(context)!.translate('font1'),
//               fontSize: 39.h,
//               color: const Color(0xff03C8E5),
//               letterSpacing: 3.****************.w,
//               fontWeight: FontWeight.w600,
//               shadows: [
//                 Shadow(
//                   color: const Color(0x29000000),
//                   offset: Offset(0, 3.h),
//                   blurRadius: 6.h,
//                 )
//               ],
//             ),
//             textAlign: TextAlign.left,
//           ),
//         ],
//       ),
//     );
//   }
//
//   Widget payAll(context) {
//     return SizedBox(
//       height: 115.h,
//       width: 636.w,
//       child: Stack(
//         alignment: Alignment.center,
//         children: [
//           Container(
//             decoration: BoxDecoration(
//               borderRadius: BorderRadius.circular(79.0.h),
//               color: const Color(0xff2a323b),
//               boxShadow: [
//                 BoxShadow(
//                   color: const Color(0x59000000),
//                   offset: Offset(9.w, 24.h),
//                   blurRadius: 35.h,
//                 ),
//               ],
//             ),
//           ),
//           Row(
//             mainAxisAlignment: MainAxisAlignment.center,
//             children: [
//               Text(
//                 AppLocalizations.of(context)!.translate('lendex_button_pay_2_1'),
//                 style: TextStyle(
//                   fontFamily: AppLocalizations.of(context)!.translate('font1'),
//                   fontSize: 39.h,
//                   color: const Color(0xff03C8E5),
//                   letterSpacing: 3.****************.w,
//                   fontWeight: FontWeight.w600,
//                   shadows: [
//                     Shadow(
//                       color: const Color(0x29000000),
//                       offset: Offset(0, 3.h),
//                       blurRadius: 6.h,
//                     )
//                   ],
//                 ),
//                 textAlign: TextAlign.left,
//               ),
//               Text(
//                 " " +
//                     AppLocalizations.of(context)!
//                             .translate('lendex_button_pay_2_2'),
//                 style: TextStyle(
//                   fontFamily: AppLocalizations.of(context)!.translate('font1'),
//                   fontSize: 39.h,
//                   color: const Color(0xff03C8E5).withOpacity(0.5),
//                   letterSpacing: 3.****************.w,
//                   fontWeight: FontWeight.w500,
//                   shadows: [
//                     Shadow(
//                       color: const Color(0x29000000),
//                       offset: Offset(0, 3.h),
//                       blurRadius: 6.h,
//                     )
//                   ],
//                 ),
//                 textAlign: TextAlign.left,
//               ),
//             ],
//           )
//         ],
//       ),
//     );
//   }
//
//   Widget point3() {
//     return SizedBox(
//         width: 100.0.w,
//         height: 61.0.h,
//         child: Image.asset(
//           LikeWalletImage.button_point3,
//         ));
//   }
//
//   Widget border() {
//     return Container(
//       alignment: Alignment.center,
//       height: 4.96.h,
//       width: 947.69.h,
//       padding: EdgeInsets.only(left: 25.w),
//       child: SvgPicture.string(
//         '<svg viewBox="64.0 1209.0 947.7 5.0" ><defs><linearGradient id="gradient" x1="0.920288" y1="0.0" x2="0.039928" y2="0.0"><stop offset="0.0" stop-color="#00ffffff" stop-opacity="0.0" /><stop offset="0.246305" stop-color="#59ffffff" stop-opacity="0.35" /><stop offset="0.477405" stop-color="#80ffffff" stop-opacity="0.5" /><stop offset="0.723415" stop-color="#59ffffff" stop-opacity="0.35" /><stop offset="1.0" stop-color="#00ffffff" stop-opacity="0.0" /></linearGradient></defs><path transform="translate(64.0, 1209.0)" d="M 0 0 L 947.69140625 0 L 947.69140625 4.962890625 L 498.0333251953125 4.962890625 L 0 4.962890625 L 0 0 Z" fill="url(#gradient)" fill-opacity="0.3" stroke="none" stroke-width="1" stroke-opacity="0.3" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
//         allowDrawingOutsideViewBox: true,
//         fit: BoxFit.fill,
//       ),
//     );
//   }
//
//   Widget popupQuestion(context, text) {
//     return Scaffold(
//       backgroundColor: Colors.transparent,
//       body: Stack(
//         children: [
//           Positioned(
//             right: 62.w,
//             top: 813.h,
//             child: Stack(
//               alignment: Alignment.center,
//               children: [
//                 SizedBox(
//                   height: 278.h,
//                   width: 824.w,
//                   child: SvgPicture.string(
//                     '<svg viewBox="194.0 874.0 824.0 278.0" ><path transform="translate(194.0, 874.0)" d="M 32 0 L 792 0 C 809.673095703125 0 824 14.3268871307373 824 32 L 824 246 C 824 263.6731262207031 809.673095703125 278 792 278 L 32 278 C 14.3268871307373 278 0 263.6731262207031 0 246 L 0 32 C 0 14.3268871307373 14.3268871307373 0 32 0 Z" fill="#000000" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
//                     allowDrawingOutsideViewBox: true,
//                     fit: BoxFit.fill,
//                   ),
//                 ),
//                 Container(
//                   padding: EdgeInsets.symmetric(horizontal: 94.w),
//                   width: 824.w,
//                   child: Text(
//                     text,
//                     style: TextStyle(
//                       fontFamily: 'Noto Sans',
//                       fontSize: 36.h,
//                       color: const Color(0x80ffffff).withOpacity(0.5),
//                       letterSpacing: 1.8.w,
//                     ),
//                     textAlign: TextAlign.left,
//                   ),
//                 ),
//                 Positioned(
//                   top: 30.h,
//                   right: 30.w,
//                   child: InkWell(
//                     onTap: () {
//                       Navigator.of(context).pop();
//                     },
//                     child: closePopup(),
//                   ),
//                 ),
//               ],
//             ),
//           ),
//         ],
//       ),
//     );
//   }
//
//   Widget popupQuestion661(context, text) {
//     return Scaffold(
//       backgroundColor: Colors.transparent,
//       body: Stack(
//         alignment: Alignment.center,
//         children: [
//           Positioned(
//             // right: 62.w,
//             bottom: 175.h,
//             child: Stack(
//               alignment: Alignment.center,
//               children: [
//                 Container(
//                   width: 991.0.w,
//                   height: 662.0.h,
//                   decoration: BoxDecoration(
//                       // borderRadius: BorderRadius.circular(100.0),
//                       ),
//                   child: SvgPicture.string(
//                     '<svg viewBox="210.0 520.0 991.1 661.6" ><path transform="translate(210.0, 520.0)" d="M 38.49052429199219 0 L 952.6405029296875 0 C 973.898193359375 0 991.1309814453125 12.50582790374756 991.1309814453125 27.93255043029785 L 991.1309814453125 633.6924438476562 C 991.1309814453125 649.119140625 973.898193359375 661.625 952.6405029296875 661.625 L 38.49052429199219 661.625 C 17.23279190063477 661.625 -9.5367431640625e-07 649.119140625 -9.5367431640625e-07 633.6924438476562 L 0 27.93255043029785 C 0 12.50582790374756 17.2327938079834 0 38.49052429199219 0 Z" fill="#000000" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
//                     allowDrawingOutsideViewBox: true,
//                     fit: BoxFit.fill,
//                   ),
//                 ),
//                 Container(
//                   padding: EdgeInsets.symmetric(horizontal: 94.w),
//                   width: 991.13.w,
//                   child: Text(
//                     text,
//                     style: TextStyle(
//                       fontFamily: 'Noto Sans',
//                       fontSize: 36.h,
//                       color: const Color(0x80ffffff).withOpacity(0.5),
//                       letterSpacing: 1.8.w,
//                     ),
//                     textAlign: TextAlign.left,
//                   ),
//                 ),
//                 Positioned(
//                   top: 30.h,
//                   right: 30.w,
//                   child: InkWell(
//                     onTap: () {
//                       Navigator.of(context).pop();
//                     },
//                     child: closePopup(),
//                   ),
//                 ),
//               ],
//             ),
//           ),
//         ],
//       ),
//     );
//   }
//
//   Widget closePopup() {
//     return Container(
//       height: 41.7.h,
//       width: 41.7.w,
//       padding: EdgeInsets.all(10.h),
//       child: Stack(
//         children: <Widget>[
//           Pinned.fromSize(
//             bounds: Rect.fromLTWH(0.0, 0.0, 21.7.w, 21.7.h),
//             size: Size(21.7.w, 21.7.h),
//             pinLeft: true,
//             pinRight: true,
//             pinTop: true,
//             pinBottom: true,
//             child: SvgPicture.string(
//               '<svg viewBox="580.5 1819.5 21.7 21.7" ><path transform="translate(580.5, 1819.5)" d="M 0 0 L 21.6875 21.6875" fill="none" fill-opacity="0.5" stroke="#ffffff" stroke-width="3" stroke-opacity="0.5" stroke-miterlimit="4" stroke-linecap="round" /></svg>',
//               allowDrawingOutsideViewBox: true,
//               fit: BoxFit.fill,
//             ),
//           ),
//           Pinned.fromSize(
//             bounds: Rect.fromLTWH(0.0, 0.0, 21.7.w, 21.7.h),
//             size: Size(21.7.w, 21.7.h),
//             pinLeft: true,
//             pinRight: true,
//             pinTop: true,
//             pinBottom: true,
//             child: SvgPicture.string(
//               '<svg viewBox="580.5 1819.5 21.7 21.7" ><path transform="matrix(0.0, 1.0, -1.0, 0.0, 602.19, 1819.5)" d="M 0 0 L 21.6875 21.6875" fill="none" fill-opacity="0.5" stroke="#ffffff" stroke-width="3" stroke-opacity="0.5" stroke-miterlimit="4" stroke-linecap="round" /></svg>',
//               allowDrawingOutsideViewBox: true,
//               fit: BoxFit.fill,
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
