// import 'dart:ui';
// import 'package:flutter/painting.dart';
// import 'package:likewallet/ImageTheme.dart';
// import 'package:likewallet/Theme.dart';
// import 'package:flutter/services.dart';
// import 'package:likewallet/animationPage.dart';
// import 'package:likewallet/libraryman/app_local.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'dart:async';
// import 'package:flutter/services.dart';
// import 'package:likewallet/menu/hourlyRewards.dart';
// import 'package:likewallet/screen_util.dart';
// import 'package:flutter/rendering.dart';
// import 'package:likewallet/tabslide/logout.dart';
// import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
// import 'package:intl/intl.dart' as formatIntl;
// import 'package:adobe_xd/gradient_xd_transform.dart';
// import 'package:carousel_slider/carousel_slider.dart';
// import 'package:likewallet/libraryman/custom_loading.dart';
// import 'package:pattern_formatter/numeric_formatter.dart';
// import 'package:likewallet/libraryman/address_service.dart';
// import 'package:likewallet/libraryman/configuration_service.dart';
// import 'package:likewallet/libraryman/ethcontractv2.dart';
// import 'package:likewallet/libraryman/sharmir.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'dart:io' show Platform;
// import 'package:likewallet/libraryman/keyboard_done_widget.dart';
// import 'package:keyboard_visibility/keyboard_visibility.dart';
// import 'package:rxdart/rxdart.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:web3dart/web3dart.dart';
//
// class loanlike extends StatefulWidget {
//   _loanlike createState() => new _loanlike();
// }
//
// enum statusWithdraw { INACTIVE, ACTIVE }
//
// class _loanlike extends State<loanlike> with TickerProviderStateMixin {
//   @override
//   final f = new formatIntl.NumberFormat("###,###.##");
//
//   TextEditingController loanAmount = TextEditingController();
//   TextEditingController amountBorrow = TextEditingController();
//   StreamController<String> streamAmountUnlock =
//       StreamController<String>.broadcast();
//   StreamController<String> streamAmountLock =
//       StreamController<String>.broadcast();
//   StreamController<String> streamTotalLike =
//       StreamController<String>.broadcast();
//   StreamController<String> streamInterestRate =
//       StreamController<String>.broadcast();
//   StreamController<String> streamDept = StreamController<String>.broadcast();
//   StreamController<String> streamInterest =
//       StreamController<String>.broadcast();
//   StreamController<String> streamRepayPrincipal =
//       StreamController<String>.broadcast();
//   StreamController<String> streamRepayInterest =
//       StreamController<String>.broadcast();
//
//   FocusNode lockFocusNode = new FocusNode();
//   late OverlayEntry overlayEntry;
//
//   bool _lock = true;
//   bool _all = false;
//   late IAddressService addressService;
//   late IConfigurationService configETH;
//   late SharmirInterface callSharmir;
//   late BaseETHV2 eth;
//   bool _saving = false;
//   bool statusBorrow = false;
//   late String pketh;
//   late String mnemonic;
//   late String ethAddr;
//   String amountUnlock = '0';
//   late String getPK;
//   String totalLock = "";
//   late String addressETH;
//   String totalLike = "0";
//   String dept = "0";
//   String currentDept = "0";
//   String principal = "0";
//   String interestAmount = "0";
//   @override
//   void initState() {
//     super.initState();
//     setInitState();
//   }
//
//   setInitState() async {
//     new Future.delayed(Duration.zero, () {
//       if (!mounted) return;
//       setState(() {
//         _saving = true;
//       });
//       // new Future.delayed(new Duration(milliseconds: 10000), () {
//       //   _timer = new Timer.periodic(new Duration(seconds: 60),
//       //       (Timer timer) => setInit(source: 'loop'));
//       // });
//
//       new Future.delayed(new Duration(milliseconds: 500), () {
//         eth = new EthContractV2();
//
//         setInit();
//       });
//     });
//   }
//
//   Future setInit({source}) async {
//     SharedPreferences pref = await SharedPreferences.getInstance();
//     configETH = new ConfigurationService(pref);
//     addressService = new AddressService(configETH);
//
//     mnemonic = await configETH.getMnemonic();
//     if (!mounted) return;
//     setState(() {
//       addressETH = configETH.getAddress();
//     });
//
//     eth.getLoanV2(address: addressETH).then((loan) {
//       //callback //array 10 ตำแหน่ง ["0address คนยืม", "1.วลายืม", "2.อัตราดอกเบี้ย", "3.เวลาสิ้นสุดสัญญา", "4หนี้คงเหลือ", "5. ดอกเบี้ยที่จ่ายแล้ว", "6. จำนวนเงินต้น", "7. หลักประกัน", "8. จ่ายเงินต้นมาแล้วทั้งหมด", "9. จ่ายดอกเบี้ยมาแล้วทั้งหมด", "10. สถานะการกู้ยืม 1 ยังกู้อยู่ 0 หมดสัญญาแล้ว"]
//       print(loan);
//       if (loan[10].toString() == "1") {
//         eth.getCurrentDeptV2(address: addressETH).then((deptInterest) {
//           var totalDept =
//               EtherAmount.inWei(deptInterest[0][6] + deptInterest[1])
//                   .getInEther;
//           setState(() {
//             currentDept = totalDept.toString();
//             principal =
//                 EtherAmount.inWei(deptInterest[0][6]).getInEther.toString();
//             interestAmount = (BigInt.parse(deptInterest[1].toString()) /
//                     BigInt.parse("1000000000000000000"))
//                 .toString();
//           });
//           print('currentDept is ' + currentDept);
//           print('principal is ' + principal);
//           print('interestAmount is ' + interestAmount.toString());
//         });
//         print(EtherAmount.inWei(loan[6] as BigInt).getInEther.toString());
//         setState(() {
//           statusBorrow = true;
//           dept = EtherAmount.inWei(loan[6] as BigInt).getInEther.toString();
//         });
//       }
//     });
//
//     eth.getInterestV2(address: addressETH).then((interestRate) {
//       streamInterestRate.sink.add(interestRate);
//     });
//     getPK = addressService.getPrivateKey(mnemonic);
//     print(addressETH);
//
//     //เช็คยอด
//     eth.getBalance(address: addressETH).then((balance) {
//       print(balance);
//
//       amountUnlock = balance.toString();
//       streamAmountUnlock.sink.add(amountUnlock);
//     });
//     //เช็คยอดการล็อค
//     eth.getBalanceLock(address: addressETH).then((balanceLock) {
//       print(balanceLock);
//       totalLock = balanceLock.toString();
//       streamAmountLock.sink.add(totalLock);
//       Future.delayed(Duration(seconds: 1)).then((value) {
//         totalLike =
//             (double.parse(amountUnlock) + double.parse(totalLock)).toString();
//         streamTotalLike.sink.add(totalLike);
//       });
//       if (!mounted) return;
//       setState(() {
//         if (source != 'loop') {
//           _saving = false;
//         }
//       });
//     });
//   }
//
//   Future<String> _initLoan() async {
//     var tx = await eth.borrowV2(pk: getPK, value: amountBorrow.text);
//     return tx;
//   }
//
//   Future<String> _RePayAll() async {
//     var tx = await eth.repayAllV2(pk: getPK);
//     return tx;
//   }
//
//   Future<String> _RePay() async {
//     var tx = await eth.repayV2(pk: getPK, value: amountBorrow.text);
//     return tx;
//   }
//
//   @override
//   void dispose() {
//     super.dispose();
//   }
//
//   ///เรียกตัวทำสไลด์ภาพ
//   late CarouselSlider carouselSlider;
//   List imgList = [
//     'assets/image/locklike/2.png',
//     'assets/image/locklike/3.png',
//     'assets/image/locklike/1.png',
//   ];
//
// //  List<T> map<T>(List list, Function handler) {
// //    List<T> result = [];
// //    for (var i = 0; i < list.length; i++) {
// //      result.add(handler(i, list[i]));
// //    }
// //    return result;
// //  }
//
//   @override
//   Widget build(BuildContext context) {
//     // TODO: implement build
//     return GestureDetector(
//         onTap: () {
//           FocusScopeNode currentFocus = FocusScope.of(context);
//           if (!currentFocus.hasPrimaryFocus) {
//             currentFocus.unfocus();
//           }
//         },
//         child: Scaffold(
//             body: ModalProgressHUD(
//                 opacity: 0.1,
//                 inAsyncCall: _saving,
//                 progressIndicator: CustomLoading(),
//                 child: Center(
//                   child: Container(
//                     margin: const EdgeInsets.all(10.0),
//                     width: 300,
//                     height: 300,
//                     child: Column(
//                       mainAxisAlignment: MainAxisAlignment.center,
// //                      crossAxisAlignment: MainAxisAlignment.center,
//                       children: [
//                         Row(
//                           children: [
//                             Text(
//                                 'Your Dept is ' +
//                                     f
//                                         .format(double.parse(currentDept))
//                                         .toString(),
//                                 textAlign: TextAlign.right,
//                                 style: TextStyle(
//                                     color: LikeWalletAppTheme.bule1_2,
//                                     letterSpacing: 1,
//                                     fontFamily: AppLocalizations.of(context)!
//                             .translate('font2'),
//                                     fontWeight: FontWeight.w300,
//                                     fontSize:
//                                         mediaQuery(context, "height", 45))),
//
//                             //            Text('Your current interest ')
//                           ],
//                         ),
//                         Row(
//                           children: [
//                             Text(
//                                 'Principal is ' +
//                                     f
//                                         .format(double.parse(principal))
//                                         .toString(),
//                                 textAlign: TextAlign.right,
//                                 style: TextStyle(
//                                     color: LikeWalletAppTheme.bule1_2,
//                                     letterSpacing: 1,
//                                     fontFamily: AppLocalizations.of(context)!
//                             .translate('font2'),
//                                     fontWeight: FontWeight.w300,
//                                     fontSize:
//                                         mediaQuery(context, "height", 45))),
//                           ],
//                         ),
//                         Row(
//                           children: [
//                             Text(
//                                 'Your InterestDept is ' +
//                                     f
//                                         .format(double.parse(interestAmount))
//                                         .toString(),
//                                 textAlign: TextAlign.right,
//                                 style: TextStyle(
//                                     color: LikeWalletAppTheme.bule1_2,
//                                     letterSpacing: 1,
//                                     fontFamily: AppLocalizations.of(context)!
//                             .translate('font2'),
//                                     fontWeight: FontWeight.w300,
//                                     fontSize:
//                                         mediaQuery(context, "height", 45)))
//                           ],
//                         ),
//                         Row(
//                           children: [
//                             StreamBuilder(
//                               stream: streamInterestRate.stream,
//                               initialData: '0',
//                               builder: (context, snapshot) {
//                                 return new Text(
//                                   'Your Maximum Borrow is ' +
//                                       f
//                                           .format(double.parse(snapshot.data.toString()))
//                                           .toString(),
//                                   textAlign: TextAlign.right,
//                                   style: TextStyle(
//                                       color: LikeWalletAppTheme.bule1_2,
//                                       letterSpacing: 1,
//                                       fontFamily: AppLocalizations.of(context)!
//                             .translate('font2'),
//                                       fontWeight: FontWeight.w300,
//                                       fontSize:
//                                           mediaQuery(context, "height", 45)),
//                                 );
//                               },
//                             ),
//                           ],
//                         ),
//                         Row(
//                           children: [
//                             StreamBuilder(
//                               stream: streamTotalLike.stream,
//                               initialData: '0',
//                               builder: (context, snapshot) {
//                                 return new Text(
//                                   'Your Total Like is ' +
//                                       f
//                                           .format(double.parse(snapshot.data.toString()))
//                                           .toString(),
//                                   textAlign: TextAlign.right,
//                                   style: TextStyle(
//                                       color: LikeWalletAppTheme.bule1_2,
//                                       letterSpacing: 1,
//                                       fontFamily: 'Proxima Nova',
//                                       fontWeight: FontWeight.w100,
//                                       fontSize:
//                                           mediaQuery(context, "height", 45)),
//                                 );
//                               },
//                             ),
//                           ],
//                         ),
//                         Row(
//                           children: [
//                             StreamBuilder(
//                               stream: streamAmountLock.stream,
//                               initialData: '0',
//                               builder: (context, snapshot) {
//                                 return new Text(
//                                   'Your Lock Like is ' +
//                                       f
//                                           .format(double.parse(snapshot.data.toString()))
//                                           .toString(),
//                                   textAlign: TextAlign.right,
//                                   style: TextStyle(
//                                       letterSpacing: 0.1,
//                                       color: LikeWalletAppTheme.bule1_2,
//                                       fontFamily: AppLocalizations.of(context)!
//                             .translate('font2'),
//                                       fontWeight: FontWeight.w300,
//                                       fontSize:
//                                           mediaQuery(context, "height", 45)),
//                                 );
//                               },
//                             )
//                           ],
//                         ),
//                         Row(
//                           children: [
//                             StreamBuilder(
//                               stream: streamAmountUnlock.stream,
//                               initialData: '0',
//                               builder: (context, snapshot) {
//                                 return new Text(
//                                   'Your Available Like is ' +
//                                       f
//                                           .format(double.parse(snapshot.data.toString()))
//                                           .toString(),
//                                   textAlign: TextAlign.right,
//                                   style: TextStyle(
//                                       color: LikeWalletAppTheme.bule1_2,
//                                       letterSpacing: 1,
//                                       fontFamily: AppLocalizations.of(context)!
//                             .translate('font2'),
//                                       fontWeight: FontWeight.w300,
//                                       fontSize:
//                                           mediaQuery(context, "height", 45)),
//                                 );
//                               },
//                             ),
//                           ],
//                         ),
//                         Row(
//                           children: [
//                             Container(
//                               width: 150,
//                               child: TextField(
//                                 controller: amountBorrow,
//                                 decoration: InputDecoration(
//                                   border: OutlineInputBorder(),
//                                   labelText: 'amount',
//                                 ),
//                               ),
//                             )
//                           ],
//                         ),
//                         Row(
//                           children: [
//                             statusBorrow == true
//                                 ? repayWidget()
//                                 : FlatButton(
//                                     onPressed: () {
//                                       _initLoan().then((hash) {
//                                         setState(() {
//                                           statusBorrow = true;
//                                           print('success borrow');
//                                         });
//                                       });
//                                     },
//                                     color: Colors.green,
//                                     child: Text('Borrow'))
//                           ],
//                         )
//                       ],
//                     ),
//                   ),
//                 ))));
//   }
// //
//
//   Widget repayWidget() {
//     return Row(
//       children: [
//         Container(
//           width: 100,
//           child: Column(
//             children: [
//               FlatButton(
//                   onPressed: () {
//                     _RePay().then((hash) {
//                       setState(() {
//                         print('success borrow');
//                       });
//                     });
//                   },
//                   color: Colors.blueAccent,
//                   child: Text('Repay')),
//               FlatButton(
//                   onPressed: () {
//                     _RePayAll().then((hash) {
//                       setState(() {
//                         statusBorrow = false;
//                         print('success borrow');
//                       });
//                     });
//                   },
//                   color: Colors.green,
//                   child: Text('RepayAll'))
//             ],
//           ),
//         )
//       ],
//     );
//   }
// }
//
// const double _kRadius = 5;
// const double _kBorderWidth = 0.8;
//
// class MyPainter extends CustomPainter {
//   MyPainter();
//
//   @override
//   void paint(Canvas canvas, Size size) {
//     final rrectBorder =
//         RRect.fromRectAndRadius(Offset.zero & size, Radius.circular(_kRadius));
//     final rrectShadow =
//         RRect.fromRectAndRadius(Offset(0, 0) & size, Radius.circular(_kRadius));
//
//     final shadowPaint = Paint()
//       ..strokeWidth = _kBorderWidth
//       ..color = Colors.black.withOpacity(0.4)
//       ..style = PaintingStyle.stroke
//       ..maskFilter = MaskFilter.blur(BlurStyle.normal, 3);
//     final borderPaint = Paint()
//       ..strokeWidth = _kBorderWidth
//       ..color = LikeWalletAppTheme.bule1_4
//       ..style = PaintingStyle.stroke;
//
//     canvas.drawRRect(rrectShadow, shadowPaint);
//     canvas.drawRRect(rrectBorder, borderPaint);
//   }
//
//   @override
//   bool shouldRepaint(CustomPainter oldDelegate) => true;
// }
