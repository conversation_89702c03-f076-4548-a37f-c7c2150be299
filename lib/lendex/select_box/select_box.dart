import 'package:flutter/material.dart';
import 'package:flutter/painting.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:likewallet/libraryman/app_local.dart';

class SelectBox {
  Widget boxGrey(context, data) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 7.w),
      alignment: Alignment.center,
      width: 146.0.h,
      height: 146.0.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(47.0.h),
        color: data != 25 ? const Color(0x66323c4a) : const Color(0x66323c4a),
      ),
      child: Text(
        data.toString() + "%",
        style: TextStyle(
          fontFamily: AppLocalizations.of(context)!.translate('font2'),
          fontSize: 39.h,
          color: const Color(0xff00b5d0),
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget boxBlue(context, data) {
    return Container(
      width: 146.0.h,
      height: 146.0.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(47.0.h),
        color: const Color(0xff00b5d0),
      ),
      child: Text(
        data.toString() + "%",
        style: TextStyle(
          fontFamily: AppLocalizations.of(context)!.translate('font2'),
          fontSize: 39.h,
          color: const Color(0xff000000),
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}
