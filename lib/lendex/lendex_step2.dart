// import 'dart:async';
// import 'dart:convert';
//
// import 'package:adobe_xd/adobe_xd.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:likewallet/ImageTheme.dart';
// import 'package:likewallet/Theme.dart';
// import 'package:intl/intl.dart' as formatIntl;
// import 'package:http/http.dart' as http;
// import 'package:likewallet/app_config.dart';
// import 'package:likewallet/lendex/account/account_lendex.dart';
// import 'package:likewallet/lendex/lendex.dart';
// import 'package:likewallet/lendex/modal_confirm_lendex.dart';
// import 'package:likewallet/lendex/select_box/select_box.dart';
// import 'package:likewallet/libraryman/address_service.dart';
// import 'package:likewallet/libraryman/app_local.dart';
// import 'package:likewallet/libraryman/configuration_service.dart';
// import 'package:likewallet/libraryman/custom_loading.dart';
// import 'package:likewallet/libraryman/ethcontractv2.dart';
// import 'package:likewallet/libraryman/sharmir.dart';
// import 'package:likewallet/routes.dart';
// import 'package:likewallet/screen/home.dart';
// import 'package:likewallet/screen_util.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:web3dart/web3dart.dart';
//
// class LenDexStep2Screen extends StatefulWidget {
//   final String? borrowAmount, borrowGuarantee;
//   LenDexStep2Screen({this.borrowAmount, this.borrowGuarantee});
//
//   @override
//   _LenDexStep2ScreeState createState() => _LenDexStep2ScreeState();
// }
//
// // enum statusWithdraw { INACTIVE, ACTIVE }
//
// class _LenDexStep2ScreeState extends State<LenDexStep2Screen>
//     with SingleTickerProviderStateMixin {
//   bool status = false;
//   FocusNode lockFocusNode = new FocusNode();
//   late OverlayEntry overlayEntry;
//   late AnimationController _controller;
//   bool _lock = true;
//   bool _all = false;
//   late IAddressService addressService;
//   late IConfigurationService configETH;
//   late SharmirInterface callSharmir;
//   late BaseETHV2 eth;
//   bool _saving = false;
//   bool statusBorrow = false;
//   int statusBorrowStep = 0;
//   late String pketh;
//   late String mnemonic;
//   late String ethAddr;
//   String amountUnlock = '0';
//   late String getPK;
//   String totalLock = "";
//   late String addressETH;
//   String totalLike = "0";
//   String dept = "0";
//   String currentDept = "0";
//   String principal = "0";
//   String interestAmount = "0";
//   final f = new formatIntl.NumberFormat("###,###.##");
//   TextEditingController loanAmount = TextEditingController();
//   // TextEditingController amountBorrow = TextEditingController();
//   StreamController<String> streamAmountUnlock =
//       StreamController<String>.broadcast();
//   StreamController<String> streamAmountLock =
//       StreamController<String>.broadcast();
//   StreamController<String> streamTotalLike =
//       StreamController<String>.broadcast();
//   StreamController<String> streamInterestRate =
//       StreamController<String>.broadcast();
//   StreamController<String> streamDept = StreamController<String>.broadcast();
//   StreamController<String> streamInterest =
//       StreamController<String>.broadcast();
//   StreamController<String> streamRepayPrincipal =
//       StreamController<String>.broadcast();
//   StreamController<String> streamRepayInterest =
//       StreamController<String>.broadcast();
//
//   @override
//   void initState() {
//     super.initState();
//     setInitState();
//   }
//
//   setInitState() async {
//     _controller =
//         AnimationController(vsync: this, duration: Duration(seconds: 2))
//           ..repeat();
//     new Future.delayed(Duration.zero, () {
//       if (!mounted) return;
//       setState(() {
//         _saving = true;
//       });
//       // new Future.delayed(new Duration(milliseconds: 10000), () {
//       //   _timer = new Timer.periodic(new Duration(seconds: 60),
//       //       (Timer timer) => setInit(source: 'loop'));
//       // });
//       new Future.delayed(new Duration(milliseconds: 500), () {
//         eth = new EthContractV2();
//         setInit();
//       });
//     });
//   }
//
//   Future setInit({source}) async {
//     SharedPreferences pref = await SharedPreferences.getInstance();
//     configETH = new ConfigurationService(pref);
//     addressService = new AddressService(configETH);
//
//     mnemonic = await configETH.getMnemonic();
//     if (!mounted) return;
//     setState(() {
//       addressETH = configETH.getAddress();
//     });
//
//     eth.getLoanV2(address: addressETH).then((loan) {
//       //callback //array 10 ตำแหน่ง ["0address คนยืม", "1.วลายืม", "2.อัตราดอกเบี้ย", "3.เวลาสิ้นสุดสัญญา", "4หนี้คงเหลือ", "5. ดอกเบี้ยที่จ่ายแล้ว", "6. จำนวนเงินต้น", "7. หลักประกัน", "8. จ่ายเงินต้นมาแล้วทั้งหมด", "9. จ่ายดอกเบี้ยมาแล้วทั้งหมด", "10. สถานะการกู้ยืม 1 ยังกู้อยู่ 0 หมดสัญญาแล้ว"]
//       print(loan);
//       if (loan[10].toString() == "1") {
//         eth.getCurrentDeptV2(address: addressETH).then((deptInterest) {
//           var totalDept =
//               EtherAmount.inWei(deptInterest[0][6] + deptInterest[1])
//                   .getInEther;
//           setState(() {
//             currentDept = totalDept.toString();
//             principal =
//                 EtherAmount.inWei(deptInterest[0][6]).getInEther.toString();
//             interestAmount = (BigInt.parse(deptInterest[1].toString()) /
//                     BigInt.parse("1000000000000000000"))
//                 .toString();
//           });
//           print('currentDept is ' + currentDept);
//           print('principal is ' + principal);
//           print('interestAmount is ' + interestAmount.toString());
//         });
//         print(EtherAmount.inWei(loan[6] as BigInt).getInEther.toString());
//         setState(() {
//           statusBorrow = true;
//           dept = EtherAmount.inWei(loan[6] as BigInt).getInEther.toString();
//         });
//       }
//     });
//
//     eth.getInterestV2(address: addressETH).then((interestRate) {
//       streamInterestRate.sink.add(interestRate);
//     });
//     getPK = addressService.getPrivateKey(mnemonic);
//     print(addressETH);
//
//     //เช็คยอด
//     eth.getBalance(address: addressETH).then((balance) {
//       print(balance);
//
//       amountUnlock = balance.toString();
//       streamAmountUnlock.sink.add(amountUnlock);
//     });
//     //เช็คยอดการล็อค
//     eth.getBalanceLock(address: addressETH).then((balanceLock) {
//       print(balanceLock);
//       totalLock = balanceLock.toString();
//       streamAmountLock.sink.add(totalLock);
//       Future.delayed(Duration(seconds: 1)).then((value) {
//         totalLike =
//             (double.parse(amountUnlock) + double.parse(totalLock)).toString();
//         streamTotalLike.sink.add(totalLike);
//       });
//       // if (!mounted) return;
//       // setState(() {
//       //   if (source != 'loop') {
//       //     _saving = false;
//       //   }
//       // });
//     });
//   }
//
//   Future<String> _initLoan() async {
//     setState(() => statusBorrowStep = 1);
//     var tx = await eth.borrowV2(pk: getPK, value: widget.borrowAmount.toString());
//     return tx;
//   }
//
//   Future<String> _RePayAll() async {
//     var tx = await eth.repayAllV2(pk: getPK);
//     return tx;
//   }
//
//   Future<String> _RePay() async {
//     var tx = await eth.repayV2(pk: getPK, value: widget.borrowAmount.toString());
//     return tx;
//   }
//
//   @override
//   void dispose() {
//     super.dispose();
//     _controller.dispose();
//     _controller.stop();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       body: Container(
//         width: MediaQuery.of(context).size.width,
//         height: MediaQuery.of(context).size.height,
//         decoration: BoxDecoration(
//           image: DecorationImage(
//               image: AssetImage(
//                 LikeWalletImage.bg_lendex,
//               ),
//               fit: BoxFit.fitWidth,
//               alignment: Alignment.topCenter),
//         ),
//         child: Stack(
//           children: [
//             head(),
//             Positioned(
//               bottom: -70.h,
//               child: Container(
//                 width: 1080.0.w,
//                 height: 1807.0.h,
//                 decoration: BoxDecoration(
//                   borderRadius: BorderRadius.only(
//                       topLeft: Radius.circular(100.w),
//                       topRight: Radius.circular(100.w)),
//                   gradient: LinearGradient(
//                     begin: Alignment(0.0, -1.0),
//                     end: Alignment(0.0, 1.0),
//                     colors: [const Color(0xff192330), const Color(0xff0f141f)],
//                     stops: [0.0, 1.0],
//                   ),
//                 ),
//                 child: Column(
//                   children: [
//                     SizedBox(height: 141.h),
//                     selectSymbol(),
//                     SizedBox(height: 28.h),
//                     inputSymbol(),
//                     SizedBox(height: 150.h),
//                     button(),
//                     // statusBorrowStep == 2 ? repayWidget() : Container(),
//                   ],
//                 ),
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
//
//   Widget loading() {
//     return AnimatedBuilder(
//         animation: _controller,
//         builder: (_, child) {
//           return Transform.rotate(
//             angle: _controller.value * 6,
//             child: child,
//           );
//         },
//         child: Icon(Icons.refresh));
//   }
//
//   Widget repayWidget() {
//     return Row(
//       children: [
//         Container(
//           width: 100,
//           child: Column(
//             children: [
//               FlatButton(
//                   onPressed: () {
//                     _RePay().then((hash) {
//                       setState(() {
//                         print('success borrow');
//                       });
//                     });
//                   },
//                   color: Colors.blueAccent,
//                   child: Text('Repay')),
//               FlatButton(
//                   onPressed: () {
//                     _RePayAll().then((hash) {
//                       setState(() {
//                         statusBorrow = false;
//                         print('success borrow');
//                       });
//                     });
//                   },
//                   color: Colors.green,
//                   child: Text('RepayAll'))
//             ],
//           ),
//         )
//       ],
//     );
//   }
//
//   Widget head() {
//     return Container(
//       child: Column(
//         children: [
//           SizedBox(height: 90.h),
//           rowHead(context),
//           SizedBox(height: 40.h),
//           SizedBox(
//             width: 800.0.w,
//             child: Text(
//               AppLocalizations.of(context)!.translate('lendex_borrow_confirm'),
//               style: TextStyle(
//                 fontFamily: AppLocalizations.of(context)!.translate('font1'),
//                 fontSize: 39.h,
//                 color: const Color(0xccffffff),
//                 letterSpacing: 1.17.w,
//               ),
//               textAlign: TextAlign.left,
//             ),
//           ),
//           SizedBox(height: 39.h),
//           SizedBox(
//             width: 800.0.w,
//             child: Text.rich(
//               TextSpan(
//                 children: [
//                   TextSpan(
//                     text: AppLocalizations.of(context)!
//                             .translate('lendex_borrow_confirm_text1'),
//                     style: TextStyle(
//                       fontFamily:
//                           AppLocalizations.of(context)!.translate('font1'),
//                       fontSize: 39.h,
//                       color: const Color(0xccffffff),
//                       letterSpacing: 1.17.w,
//                     ),
//                   ),
//                   TextSpan(
//                     text: AppLocalizations.of(context)!
//                             .translate('lendex_borrow_confirm_text2'),
//                     style: TextStyle(
//                       fontFamily:
//                           AppLocalizations.of(context)!.translate('font1'),
//                       fontSize: 39.h,
//                       color: const Color(0xffff9d00),
//                       letterSpacing: 3.9000000000000004.w,
//                       fontWeight: FontWeight.w600,
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           ),
//           SizedBox(height: 123.h),
//         ],
//       ),
//     );
//   }
//
//   Widget selectSymbol() {
//     return SizedBox(
//       width: 871.w,
//       child: Row(
//         mainAxisAlignment: MainAxisAlignment.start,
//         children: [
//           point(),
//           SizedBox(width: 29.w),
//           Text(
//             AppLocalizations.of(context)!
//                             .translate('lendex_borrow_confirm_collaterral'),
//             style: TextStyle(
//               fontFamily: AppLocalizations.of(context)!.translate('font1'),
//               fontSize: 42.h,
//               color: const Color(0xff908468),
//               letterSpacing: 1.26.w,
//             ),
//             textAlign: TextAlign.left,
//           ),
//           SizedBox(width: 34.w),
//           Expanded(child: Container()),
//           InkWell(
//               onTap: () {
//                 showDialog(
//                     context: context,
//                     barrierColor: Colors.transparent,
//                     builder: (BuildContext context) => WillPopScope(
//                       onWillPop: () { return Future.value(); },
//                         child: LENDEX().popupQuestion(
//                             context,
//                             AppLocalizations.of(context)!
//                             .translate('lendex_account_popup_2'))));
//               },
//               child: point2()),
//         ],
//       ),
//     );
//   }
//
//   Widget inputSymbol() {
//     return SizedBox(
//       width: 880.44.w,
//       child: Row(
//         children: [
//           SizedBox(
//             height: 142.h,
//             width: 880.44.w,
//             child: Stack(
//               alignment: Alignment.centerLeft,
//               children: [
//                 SvgPicture.string(
//                   '<svg viewBox="100.0 822.0 880.4 142.0" ><path transform="translate(100.0, 822.0)" d="M 71 0 L 809.4375 0 C 848.************* 0 880.4375 31.************** 880.4375 71 C 880.4375 110.************* 848.************* 142 809.4375 142 L 71 142 C 31.************** 142 0 110.************* 0 71 C 0 31.************** 31.************** 0 71 0 Z" fill="#0e141c" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
//                   allowDrawingOutsideViewBox: true,
//                   fit: BoxFit.fill,
//                 ),
//                 Padding(
//                   padding: EdgeInsets.only(left: 92.w),
//                   child: Text(
//                     statusBorrowStep == 2
//                         ? '0'
//                         : f
//                             .format(double.parse(widget.borrowGuarantee.toString()))
//                             .toString(),
//                     style: TextStyle(
//                       fontFamily:
//                           AppLocalizations.of(context)!.translate('font1'),
//                       fontSize: 54.h,
//                       color: const Color(0xe5ffffff),
//                       letterSpacing: 5.4.w,
//                       fontWeight: FontWeight.w600,
//                     ),
//                     textAlign: TextAlign.left,
//                   ),
//                 ),
//               ],
//             ),
//           ),
//         ],
//       ),
//     );
//   }
//
//   Widget button() {
//     return InkWell(
//       onTap: () async {
//         if (statusBorrowStep == 2) {
//           Navigator.pushReplacement(
//             context,
//             MaterialPageRoute(
//               builder: (context) => AccountLenDexScreen(),
//             ),
//           );
//         } else {
//           if (statusBorrowStep == 0) {
//             final tx = await _initLoan();
//             var url = Uri.https(env.apiUrl, '/checkPending');
//             final response = await http.post(url, body: {"tx": tx});
//             if (response.statusCode == 200) {
//               var body = json.decode(response.body);
//               if (body['statusCode'] == 200) {
//                 print('success borrow');
//                 setState(() => statusBorrowStep = 2);
//                 _controller.stop();
//                 Future.delayed(Duration(seconds: 4)).then((value) {
//                   Navigator.pushReplacement(
//                     context,
//                     MaterialPageRoute(
//                       builder: (context) => AccountLenDexScreen(),
//                     ),
//                   );
//                 });
//               }
//               //หรือ 202 ==false
//               else {
//                 setState(() => statusBorrowStep = 0);
//                 showShortToast(
//                     AppLocalizations.of(context)!.translate('save_err'),
//                     Colors.red);
//               }
//             } else {
//               setState(() => statusBorrowStep = 0);
//               showShortToast(AppLocalizations.of(context)!.translate('save_err'),
//                   Colors.red);
//             }
//             // _initLoan().then((hash) {
//             //   Future.delayed(Duration(seconds: 1))
//             //       .then((value) => setState(() => statusBorrowStep = 2));
//             //   print('success borrow');
//             // });
//           }
//         }
//       },
//       child: SizedBox(
//         width: 445.0.w,
//         height: 124.0.h,
//         child: Stack(
//           alignment: Alignment.center,
//           children: <Widget>[
//             Pinned.fromSize(
//               bounds: Rect.fromLTWH(0.0, 0.0, 768.0.w, 124.0.h),
//               size: Size(768.0.w, 124.0.h),
//               pinLeft: true,
//               pinRight: true,
//               pinTop: true,
//               pinBottom: true,
//               child: Container(
//                 decoration: BoxDecoration(
//                   borderRadius: BorderRadius.circular(71.0.h),
//                   gradient: LinearGradient(
//                     begin: Alignment(0.0, -1.0),
//                     end: Alignment(0.0, 1.0),
//                     colors: [const Color(0xff908468), const Color(0xff908468)],
//                     stops: [0.0, 1.0],
//                   ),
//                 ),
//               ),
//             ),
//             statusBorrowStep == 1
//                 ? loading()
//                 : statusBorrowStep == 2
//                     ? Image.asset(
//                         LikeWalletImage.icon_success,
//                         fit: BoxFit.contain,
//                         height: 39.42.h,
//                         color: Colors.white,
//                       )
//                     : Text(
//                         AppLocalizations.of(context)!
//                             .translate('lendex_borrow_confirm_button'),
//                         style: TextStyle(
//                           fontFamily:
//                               AppLocalizations.of(context)!.translate('font1'),
//                           fontSize: 46.h,
//                           color: const Color(0xff000000),
//                           letterSpacing: 1.38.w,
//                         ),
//                         textAlign: TextAlign.left,
//                       ),
//           ],
//         ),
//       ),
//     );
//   }
//
//   Widget point() {
//     return Container(
//       width: 12.0.h,
//       height: 12.0.h,
//       decoration: BoxDecoration(
//         borderRadius: BorderRadius.all(Radius.elliptical(9999.0, 9999.0)),
//         color: const Color(0xff908468),
//       ),
//     );
//   }
//
//   Widget point2() {
//     return Row(
//       children: [
//         Container(
//           width: 12.0.h,
//           height: 12.0.h,
//           decoration: BoxDecoration(
//             borderRadius: BorderRadius.all(Radius.elliptical(9999.0, 9999.0)),
//             color: const Color(0xff908468),
//           ),
//         ),
//         SizedBox(
//           width: 34.w,
//         ),
//         Container(
//           width: 12.0.h,
//           height: 12.0.h,
//           decoration: BoxDecoration(
//             borderRadius: BorderRadius.all(Radius.elliptical(9999.0, 9999.0)),
//             color: const Color(0xff908468),
//           ),
//         ),
//       ],
//     );
//   }
//
//   Widget rowHead(context) {
//     return Row(
//       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//       children: [
//         statusBorrowStep == 2
//             ? Container(
//                 height: mediaQuery(context, 'height', 84.47),
//               )
//             : Padding(
//                 padding: EdgeInsets.only(left: 25.w),
//                 child: FlatButton(
//                   child: Container(
//                     padding: EdgeInsets.all(
//                       mediaQuery(context, 'height', 0),
//                     ),
//                     height: mediaQuery(context, 'height', 36.33),
//                     width: mediaQuery(context, 'height', 24.5),
//                     child: SvgPicture.string(
//                       '<svg viewBox="-281.5 3213.3 24.5 36.3" ><path  d="M -259.5249938964844 3249.675048828125 C -260.1029968261719 3249.675048828125 -260.6839904785156 3249.47607421875 -261.156005859375 3249.068115234375 L -281.5329895019531 3231.508056640625 L -261.156005859375 3213.947021484375 C -260.1109924316406 3213.0458984375 -258.531005859375 3213.162109375 -257.6300048828125 3214.208984375 C -256.72900390625 3215.2548828125 -256.8460083007812 3216.8330078125 -257.8919982910156 3217.73388671875 L -273.8739929199219 3231.508056640625 L -257.8919982910156 3245.281005859375 C -256.8460083007812 3246.18310546875 -256.72900390625 3247.760986328125 -257.6300048828125 3248.806884765625 C -258.125 3249.381103515625 -258.822998046875 3249.675048828125 -259.5249938964844 3249.675048828125 Z" fill="#231f20" stroke="none" stroke-width="1" stroke-miterlimit="10" stroke-linecap="butt" /></svg>',
//                       allowDrawingOutsideViewBox: true,
//                       fit: BoxFit.fill,
//                     ),
//                   ),
//                   onPressed: () {
//                     Navigator.of(context).pop();
//                   },
//                 ),
//               ),
//         FlatButton(
//           onPressed: () {
//             _controller.stop();
//             AppRoutes.makeFirst(context, HomeLikewallet());
//           },
//           child: Image.asset(
//             LikeWalletImage.bg_home,
//             height: mediaQuery(context, "height", 44.47),
//           ),
//         ),
//       ],
//     );
//   }
// }
