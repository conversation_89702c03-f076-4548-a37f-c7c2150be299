// import 'dart:async';
// import 'dart:io';
// import 'dart:ui';
//
// import 'package:adobe_xd/adobe_xd.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:intl/intl.dart';
// import 'package:likewallet/ImageTheme.dart';
// import 'package:likewallet/Theme.dart';
// import 'package:likewallet/close_maintenance.dart';
// import 'package:likewallet/device_utils.dart';
// import 'package:likewallet/lendex/account/account_lendex.dart';
// import 'package:likewallet/lendex/lendex.dart';
// import 'package:likewallet/lendex/modal_confirm_lendex.dart';
// import 'package:likewallet/libraryman/app_local.dart';
// import 'package:likewallet/libraryman/custom_loading.dart';
// import 'package:likewallet/libraryman/ethcontractv2.dart';
// import 'package:likewallet/menu/LockLIKE_New.dart';
// import 'package:likewallet/middleware/check_maintenance/check_maintenance.dart';
// import 'package:likewallet/middleware/getLanguage.dart';
// import 'package:likewallet/model/pageStatus.dart';
// import 'package:likewallet/routes.dart';
// import 'package:likewallet/screen/home.dart';
// import 'package:likewallet/screen_util.dart';
// import 'package:intl/intl.dart' as formatIntl;
// import 'package:firebase_auth/firebase_auth.dart';
// import 'package:likewallet/main.dart';
// import 'package:likewallet/libraryman/address_service.dart';
// import 'package:likewallet/libraryman/configuration_service.dart';
// import 'package:likewallet/libraryman/ethcontract.dart';
// import 'package:likewallet/libraryman/auth.dart';
// import 'package:likewallet/libraryman/crypto.dart';
// import 'package:likewallet/libraryman/serviceHTTP.dart';
// import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
// import 'package:pattern_formatter/numeric_formatter.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:web3dart/web3dart.dart';
//
// class LenDexStep1Screen extends StatefulWidget {
//   @override
//   _LenDexScreeState createState() => _LenDexScreeState();
// }
//
// class _LenDexScreeState extends State<LenDexStep1Screen> {
//   List select = [20, 25, 30, 35, 40];
//   int boxNumber = 0;
//   var month = 0.0;
//   TextEditingController amountInput = TextEditingController();
//   FocusNode? amountFocusNode;
//   StreamController<String> streamBalanceLocked =
//       StreamController<String>.broadcast();
//   StreamController<String> streamLocked50Percent =
//       StreamController<String>.broadcast();
//   bool status = false;
//
//   bool buttonLENDEX = false;
//   final f = new formatIntl.NumberFormat("###,###.##");
//   final f2 = new NumberFormat("###.##", "en_US");
//
//   late BaseETHV2 eth;
//   TextEditingController loanAmount = TextEditingController();
//   TextEditingController amountBorrow = TextEditingController();
//   StreamController<String> streamAmountUnlock =
//       StreamController<String>.broadcast();
//   StreamController<String> streamAmountLock =
//       StreamController<String>.broadcast();
//   StreamController<String> streamTotalLike =
//       StreamController<String>.broadcast();
//   StreamController<String> streamInterestRate =
//       StreamController<String>.broadcast();
//   StreamController<String> streamDept = StreamController<String>.broadcast();
//   StreamController<String> streamInterest =
//       StreamController<String>.broadcast();
//   StreamController<String> streamRepayPrincipal =
//       StreamController<String>.broadcast();
//   StreamController<String> streamRepayInterest =
//       StreamController<String>.broadcast();
//   StreamController<String> streamAPY = StreamController<String>.broadcast();
//   FocusNode lockFocusNode = new FocusNode();
//   late OverlayEntry? overlayEntry;
//   late IAddressService addressService;
//   late IConfigurationService configETH;
//   bool _saving = false;
//   bool statusBorrow = false;
//   late String pketh;
//   late String mnemonic;
//   late String ethAddr;
//   String amountUnlock = '0';
//   late String getPK;
//   String totalLock = "";
//   late String addressETH;
//   String totalLike = "0";
//   String dept = "0";
//   String currentDept = "0";
//   String principal = "0";
//   String interestAmount = "0";
//   String symbol = 'LIKE ';
//   String BHT = '0';
//   String balanceTHB = '0';
//   String fiat = 'none';
//   String balanceShow = 'Loading..';
//   double balanceLIKELock = 0.00;
//   String locked_balance = 'Loading..';
//   double APY = 5.5;
//   final fireStore = FirebaseFirestore.instance;
//   late String addr;
//   double apyTotal = 0;
//   late String maxBorrow;
//   /////////////////////////////////////////////////////////////
//   late AbstractServiceHTTP getfee;
//   late AbstractServiceHTTP APIHttp;
//   late CryptoEncryptInterface encrypt;
//   late BaseAuth auth;
//   late CheckAbout checkAbout;
//   late OnLanguage language;
//   ///////////////////////////////////////////////////////////////
//
//   @override
//   void initState() {
//     // TODO: implement initState
//     super.initState();
//     Future.delayed(Duration.zero, () {
//       APIHttp = ServiceHTTP();
//       auth = new Auth();
//       encrypt = new CryptoEncrypt();
//       getfee = new ServiceHTTP();
//       eth = new EthContractV2();
//       amountFocusNode = FocusNode();
//       checkAbout = OnCheckAbout();
//       language = CallLanguage();
//       checkFirst();
//       setState(() => _saving = true);
//     });
//   }
//
//   @override
//   void dispose() {
//     super.dispose();
//     streamBalanceLocked.close();
//     amountFocusNode!.dispose();
//   }
//
//   Future setInitState({source}) async {
//     SharedPreferences pref = await SharedPreferences.getInstance();
//     configETH = new ConfigurationService(pref);
//     addressService = new AddressService(configETH);
//
//     mnemonic = await configETH.getMnemonic();
//     if (!mounted) return;
//     setState(() {
//       addressETH = configETH.getAddress();
//     });
//     eth.getUnlockDate(address: addressETH).then((data) {
//       print('unlock' + data.toString());
//       print('unlock' + data[3]);
//       print(data[3]);
//       if (data[3] == '1') {
//         showDialog(
//             context: context,
//             builder: (BuildContext context) => WillPopScope(
//                 onWillPop: () {
//                   return Future.value();
//                 },
//                 child: alertWhite(
//                   context,
//                   lockLike(),
//                   AppLocalizations.of(context)!
//                       .translate('notify_title_notification'),
//                   AppLocalizations.of(context)!
//                       .translate('alert_lendex_await_unlock'),
//                 )));
//       }
//     });
//     eth.getLoanV2(address: addressETH).then((loan) {
//       //callback //array 10 ตำแหน่ง ["0address คนยืม", "1.วลายืม", "2.อัตราดอกเบี้ย", "3.เวลาสิ้นสุดสัญญา", "4หนี้คงเหลือ", "5. ดอกเบี้ยที่จ่ายแล้ว", "6. จำนวนเงินต้น", "7. หลักประกัน", "8. จ่ายเงินต้นมาแล้วทั้งหมด", "9. จ่ายดอกเบี้ยมาแล้วทั้งหมด", "10. สถานะการกู้ยืม 1 ยังกู้อยู่ 0 หมดสัญญาแล้ว"]
//       print(loan);
//       if (loan[11].toString() == "1") {
//         eth.getCurrentDeptV2(address: addressETH).then((deptInterest) {
//           var totalDept =
//               EtherAmount.inWei(deptInterest[0][6] + deptInterest[1])
//                   .getInEther;
//           setState(() {
//             currentDept = totalDept.toString();
//             principal =
//                 EtherAmount.inWei(deptInterest[0][6]).getInEther.toString();
//             interestAmount = (BigInt.parse(deptInterest[1].toString()) /
//                     BigInt.parse("1000000000000000000"))
//                 .toString();
//           });
//           print('currentDept is ' + currentDept);
//           print('principal is ' + principal);
//           print('interestAmount is ' + interestAmount.toString());
//         });
//         print(EtherAmount.inWei(loan[6] as BigInt).getInEther.toString());
//         setState(() {
//           statusBorrow = true;
//           dept = EtherAmount.inWei(loan[6] as BigInt).getInEther.toString();
//         });
//
//         showDialog(
//             context: context,
//             builder: (BuildContext context) => WillPopScope(
//                 onWillPop: () {
//                   return Future.value();
//                 },
//                 child: alertWhite(
//                   context,
//                   AccountLenDexScreen(),
//                   AppLocalizations.of(context)!
//                       .translate('notify_title_notification'),
//                   AppLocalizations.of(context)!
//                       .translate('alert_lendex_borrow'),
//                 )));
//       }
//     });
//     print("address :" + addressETH);
//     eth.getInterestV2(address: addressETH).then((interestRate) {
//       streamInterestRate.sink.add(interestRate);
//       setState(() {
//         maxBorrow = interestRate;
//       });
//     });
//     getPK = addressService.getPrivateKey(mnemonic);
//     print(addressETH);
//
//     //เช็คยอด
//     eth.getBalance(address: addressETH).then((balance) {
//       print(balance);
//
//       amountUnlock = balance.toString();
//       streamAmountUnlock.sink.add(amountUnlock);
//     });
//     //เช็คยอดการล็อค
//     eth.getBalanceLock(address: addressETH).then((balanceLock) {
//       print(balanceLock);
//       totalLock = balanceLock.toString();
//       streamAmountLock.sink.add(totalLock);
//       Future.delayed(Duration(seconds: 1)).then((value) {
//         totalLike =
//             (double.parse(amountUnlock) + double.parse(totalLock)).toString();
//         streamTotalLike.sink.add(totalLike);
//       });
//       if (!mounted) return;
//       setState(() {
//         if (source != 'loop') {
//           _saving = false;
//         }
//       });
//     });
//
//     eth.getAPY().then((apy) {
//       setState(() => apyTotal =
//           double.parse(apy.replaceAll('[', '').replaceAll(']', '')) / 10e15);
//       print("apy : " + apyTotal.toString());
//       if (!mounted) return;
//       setState(() {
//         if (source != 'loop') {
//           _saving = false;
//         }
//       });
//     });
//   }
//
//   checkFirst() async {
//     PageMaintenance statusPage = await checkAbout.checkTierPermission(
//         tierLevel: context.read(tierLevel).state, page: 'borrow');
//     if (statusPage.status == 'active') {
//       //เริ่มทำงาน
//       print('active');
//       setInitState();
//     } else {
//       //ปิด maintenance
//       print('inactive');
//       final lang = await language.getLanguage();
//       final title = await checkAbout.selectLanguage(
//           language: lang, detail: statusPage.title);
//       final detail = await checkAbout.selectLanguage(
//           language: lang, detail: statusPage.detail);
//       final detailTime = await checkAbout.selectLanguage(
//           language: lang, detail: statusPage.detail_time);
//       Navigator.pushReplacement(
//         context,
//         MaterialPageRoute(
//             builder: (context) => CloseMaintenance(
//                   title: title,
//                   detail: detail,
//                   detailTime: detailTime,
//                   url: statusPage.url,
//                 )),
//       );
//     }
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return ModalProgressHUD(
//       opacity: 0.1,
//       inAsyncCall: _saving,
//       progressIndicator: CustomLoading(),
//       child: statusBorrow
//           ? Container()
//           : Scaffold(
//               body: SingleChildScrollView(
//                 child: GestureDetector(
//                   onTap: () {
//                     DeviceUtils.hideKeyboard(context);
//                   },
//                   child: Container(
//                     width: MediaQuery.of(context).size.width,
//                     height: MediaQuery.of(context).size.height,
//                     decoration: BoxDecoration(
//                       image: DecorationImage(
//                           image: AssetImage(
//                             LikeWalletImage.bg_lendex,
//                           ),
//                           fit: BoxFit.fitWidth,
//                           alignment: Alignment.topCenter),
//                     ),
//                     child: Stack(
//                       children: [
//                         head(),
//                         Positioned(
//                           bottom: -100.h,
//                           child: Container(
//                             width: 1080.0.w,
//                             height: 1807.0.h,
//                             decoration: BoxDecoration(
//                               borderRadius: BorderRadius.only(
//                                   topLeft: Radius.circular(100.w),
//                                   topRight: Radius.circular(100.w)),
//                               gradient: LinearGradient(
//                                 begin: Alignment(0.0, -1.0),
//                                 end: Alignment(0.0, 1.0),
//                                 colors: [
//                                   const Color(0xff192330),
//                                   const Color(0xff0f141f)
//                                 ],
//                                 stops: [0.0, 1.0],
//                               ),
//                             ),
//                             child: Column(
//                               children: [
//                                 SizedBox(
//                                   height: 141.h,
//                                 ),
//                                 selectSymbol(),
//                                 SizedBox(
//                                   height: 28.h,
//                                 ),
//                                 inputSymbol(),
//                                 SizedBox(
//                                   height: 150.h,
//                                 ),
//                                 selectNumber(),
//                                 SizedBox(
//                                   height: 28.h,
//                                 ),
//                                 inputNumber(),
//                                 SizedBox(
//                                   height: 42.h,
//                                 ),
//                                 // Row(
//                                 //   mainAxisAlignment: MainAxisAlignment.center,
//                                 //   children: [
//                                 //     Container(
//                                 //       alignment: Alignment.center,
//                                 //       height: 150.h,
//                                 //       width: 840.0.w,
//                                 //       child: ListView.builder(
//                                 //         itemCount: select.length,
//                                 //         scrollDirection: Axis.horizontal,
//                                 //         itemBuilder: (context, i) {
//                                 //           return rowBox(select[i]);
//                                 //         },
//                                 //       ),
//                                 //     ),
//                                 //   ],
//                                 // ),
//                                 SizedBox(
//                                   height: 175.h,
//                                 ),
//                                 // time(),
//                                 // SizedBox(
//                                 //   height: 101.h,
//                                 // ),
//                                 // slider(),
//                                 // Expanded(child: Container()),
//                                 button(),
//                                 SizedBox(
//                                   height: 175.h,
//                                 ),
//                               ],
//                             ),
//                           ),
//                         ),
//                       ],
//                     ),
//                   ),
//                 ),
//               ),
//             ),
//     );
//   }
//
//   Widget head() {
//     return Container(
//       child: Column(
//         children: [
//           SizedBox(height: 90.h),
//           rowHead(context),
//           SizedBox(height: 25.h),
//           SizedBox(
//             width: 738.0.w,
//             child: Text(
//               AppLocalizations.of(context)!.translate('lendex_borrow_title'),
//               style: TextStyle(
//                 fontFamily: AppLocalizations.of(context)!.translate('font1'),
//                 fontSize: 39.h,
//                 color: const Color(0xccffffff),
//                 letterSpacing: 1.17.w,
//               ),
//               textAlign: TextAlign.center,
//             ),
//           ),
//           SizedBox(height: 18.h),
//           SizedBox(
//             width: 386.0.w,
//             child: StreamBuilder(
//                 stream: streamAmountLock.stream,
//                 initialData: '0',
//                 builder: (context, snapshot) {
//                   return Text(
//                     f.format(double.parse(snapshot.data.toString())).toString(),
//                     style: TextStyle(
//                       fontFamily:
//                           AppLocalizations.of(context)!.translate('font1'),
//                       fontSize: 39.h,
//                       color: const Color(0xccffffff),
//                       letterSpacing: 3.9000000000000004.w,
//                       fontWeight: FontWeight.w600,
//                     ),
//                     textAlign: TextAlign.center,
//                   );
//                 }),
//           ),
//           SizedBox(
//             height: 39.h,
//           ),
//           SizedBox(
//             // width: 7.0.w,
//             child: Text(
//               AppLocalizations.of(context)!.translate('lendex_borrow_50'),
//               style: TextStyle(
//                 fontFamily: AppLocalizations.of(context)!.translate('font1'),
//                 fontSize: 39.h,
//                 color: const Color(0xccffffff),
//                 letterSpacing: 1.17.w,
//               ),
//               textAlign: TextAlign.center,
//             ),
//           ),
//           SizedBox(height: 18.h),
//           SizedBox(
//             width: 337.0.w,
//             child: StreamBuilder(
//                 stream: streamInterestRate.stream,
//                 initialData: '0',
//                 builder: (context, snapshot) {
//                   return Text(
//                     f.format(double.parse(snapshot.data.toString())).toString(),
//                     style: TextStyle(
//                       fontFamily:
//                           AppLocalizations.of(context)!.translate('font1'),
//                       fontSize: 39.w,
//                       color: const Color(0xffff9d00),
//                       letterSpacing: 3.9000000000000004.w,
//                       fontWeight: FontWeight.w700,
//                     ),
//                     textAlign: TextAlign.center,
//                   );
//                 }),
//           ),
//           SizedBox(
//             height: 29.h,
//           ),
//           // Padding(
//           //   padding: EdgeInsets.only(left: 100.w),
//           //   child: Row(
//           //     mainAxisAlignment: MainAxisAlignment.start,
//           //     children: [
//           //       Text(
//           //         '1. เลือกการกู้ยืมที่ต้องการ',
//           //         style: TextStyle(
//           //           fontFamily: AppLocalizations.of(context)!.translate('font1'),
//           //           fontSize: 45.w,
//           //           color: const Color(0xff000000),
//           //           letterSpacing: 1.3499999999999999.w,
//           //           fontWeight: FontWeight.w600,
//           //         ),
//           //         textAlign: TextAlign.left,
//           //       ),
//           //     ],
//           //   ),
//           // )
//         ],
//       ),
//     );
//   }
//
//   Widget selectSymbol() {
//     return SizedBox(
//       width: 871.w,
//       child: Row(
//         mainAxisAlignment: MainAxisAlignment.start,
//         children: [
//           point(),
//           SizedBox(
//             width: 34.w,
//           ),
//           Text(
//             AppLocalizations.of(context)!.translate('lendex_borrow_currency'),
//             style: TextStyle(
//               fontFamily: AppLocalizations.of(context)!.translate('font1'),
//               fontSize: 39.h,
//               color: const Color(0xff908468),
//               letterSpacing: 1.9500000000000002.w,
//             ),
//             textAlign: TextAlign.left,
//           ),
//           Expanded(
//             child: Container(),
//           ),
//           InkWell(
//               onTap: () {
//                 showDialog(
//                     context: context,
//                     barrierColor: Colors.transparent,
//                     builder: (BuildContext context) => WillPopScope(
//                         onWillPop: () {
//                           return Future.value();
//                         },
//                         child: LENDEX().popupQuestion(
//                             context,
//                             AppLocalizations.of(context)!
//                                 .translate('lendex_borrow_popup'))));
//               },
//               child: point2()),
//         ],
//       ),
//     );
//   }
//
//   Widget inputSymbol() {
//     return SizedBox(
//       width: 871.w,
//       child: Row(
//         mainAxisAlignment: MainAxisAlignment.start,
//         children: [
//           Stack(
//             alignment: Alignment.centerLeft,
//             children: [
//               Container(
//                 height: 142.h,
//                 width: 549.w,
//                 child: SvgPicture.string(
//                   '<svg viewBox="91.0 822.0 549.0 142.0" ><path transform="translate(91.0, 822.0)" d="M 71 0 L 478 0 C 517.2122192382812 0 549 31.78778076171875 549 71 C 549 110.2122192382812 517.2122192382812 142 478 142 L 71 142 C 31.78778076171875 142 0 110.2122192382812 0 71 C 0 31.78778076171875 31.78778076171875 0 71 0 Z" fill="#0e141c" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
//                   allowDrawingOutsideViewBox: true,
//                   fit: BoxFit.fill,
//                 ),
//               ),
//               Container(
//                 padding: EdgeInsets.only(left: 92.w),
//                 alignment: Alignment.centerLeft,
//                 child: Text(
//                   'LIKE',
//                   style: TextStyle(
//                     fontFamily: 'Proxima Nova',
//                     fontSize: 54.h,
//                     color: const Color(0xe5ffffff),
//                     letterSpacing: 5.4.w,
//                     fontWeight: FontWeight.w600,
//                   ),
//                   textAlign: TextAlign.left,
//                 ),
//               ),
//             ],
//           ),
//         ],
//       ),
//     );
//   }
//
//   Widget selectNumber() {
//     return SizedBox(
//       width: 871.w,
//       child: Row(
//         mainAxisAlignment: MainAxisAlignment.start,
//         children: [
//           point(),
//           SizedBox(
//             width: 34.w,
//           ),
//           Text(
//             AppLocalizations.of(context)!.translate('lendex_borrow_count'),
//             style: TextStyle(
//               fontFamily: AppLocalizations.of(context)!.translate('font1'),
//               fontSize: 39.h,
//               color: const Color(0xff908468),
//               letterSpacing: 1.9500000000000002.w,
//             ),
//             textAlign: TextAlign.left,
//           ),
//           Expanded(
//             child: Container(),
//           ),
//         ],
//       ),
//     );
//   }
//
//   Widget inputNumber() {
//     return SizedBox(
//       width: 880.4.w,
//       child: Row(
//         children: [
//           SizedBox(
//             height: 142.h,
//             width: 880.4.w,
//             child: Stack(
//               alignment: Alignment.centerLeft,
//               children: [
//                 SvgPicture.string(
//                   '<svg viewBox="91.0 1196.0 880.4 142.0" ><path transform="translate(91.0, 1196.0)" d="M 71 0 L 809.4375 0 C 848.6497192382812 0 880.4375 31.78778076171875 880.4375 71 C 880.4375 110.2122192382812 848.6497192382812 142 809.4375 142 L 71 142 C 31.78778076171875 142 0 110.2122192382812 0 71 C 0 31.78778076171875 31.78778076171875 0 71 0 Z" fill="#0e141c" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
//                   allowDrawingOutsideViewBox: true,
//                   fit: BoxFit.fill,
//                 ),
//                 Padding(
//                   padding: EdgeInsets.only(left: 92.w, right: 75.w),
//                   child: Row(
//                       mainAxisAlignment: MainAxisAlignment.center,
//                       children: [
//                         Expanded(
//                           // height: 142.h,
//                           // width: 650.4.w,
//                           // alignment: Alignment.centerLeft,
//                           child: TextFormField(
//                             // textAlignVertical: TextAlignVertical.center,
//                             textAlign: TextAlign.start,
//                             inputFormatters: [
//                               ThousandsFormatter(allowFraction: true)
//                             ],
//                             style: TextStyle(
//                                 fontSize: mediaQuery(context, 'height', 54),
//                                 fontFamily: AppLocalizations.of(context)!
//                                     .translate('font2'),
//                                 color: LikeWalletAppTheme.gray1),
//                             keyboardType: TextInputType.number,
//                             controller: amountInput,
//                             focusNode: amountFocusNode,
//                             decoration: InputDecoration(
//                               focusedBorder: InputBorder.none,
//                               enabledBorder: InputBorder.none,
//                               hintText: '0',
//                               hintStyle: TextStyle(
//                                   fontSize: mediaQuery(context, 'height', 54),
//                                   fontFamily: AppLocalizations.of(context)!
//                                       .translate('font2'),
//                                   color: LikeWalletAppTheme.gray1),
// //                      ),
//                             ),
//                           ),
//                         ),
//                         InkWell(
//                           onTap: () {
//                             amountFocusNode!.requestFocus();
//                           },
//                           child: Image.asset(
//                             LikeWalletImage.button_key,
//                             height: 37.h,
//                           ),
//                         ),
//                       ]),
//                 ),
//               ],
//             ),
//           ),
//         ],
//       ),
//     );
//   }
//
//   Widget rowBox(data) {
//     return InkWell(
//       onTap: () {
//         setState(
//           () {
//             boxNumber = data;
//           },
//         );
//       },
//       child: Container(
//         margin: EdgeInsets.symmetric(horizontal: 7.w),
//         alignment: Alignment.center,
//         width: 146.0.h,
//         height: 146.0.h,
//         decoration: BoxDecoration(
//           borderRadius: BorderRadius.circular(47.0.h),
//           color: boxNumber == data ? Color(0xff00b5d0) : Color(0x66323c4a),
//         ),
//         child: Text(
//           data.toString() + "%",
//           style: TextStyle(
//             fontFamily: AppLocalizations.of(context)!.translate('font2'),
//             fontSize: 39.h,
//             color: boxNumber == data
//                 ? const Color(0xff000000)
//                 : const Color(0xff00b5d0),
//             fontWeight: boxNumber == data ? FontWeight.bold : FontWeight.normal,
//           ),
//           textAlign: TextAlign.center,
//         ),
//       ),
//     );
//   }
//
//   Widget time() {
//     return SizedBox(
//       width: 871.w,
//       child: Row(
//         mainAxisAlignment: MainAxisAlignment.start,
//         children: [
//           point(),
//           SizedBox(
//             width: 34.w,
//           ),
//           Text(
//             'ระยะเวลา? ',
//             style: TextStyle(
//               fontFamily: AppLocalizations.of(context)!.translate('font1'),
//               fontSize: 39.h,
//               color: const Color(0xff908468),
//               letterSpacing: 1.9500000000000002.w,
//             ),
//             textAlign: TextAlign.left,
//           ),
//           Text(
//             month.toInt().toString() + ' เดือน',
//             style: TextStyle(
//               fontFamily: AppLocalizations.of(context)!.translate('font1'),
//               fontSize: 39.h,
//               color: const Color(0xff908468),
//               letterSpacing: 1.9500000000000002.w,
//             ),
//             textAlign: TextAlign.left,
//           ),
//           Expanded(
//             child: Container(),
//           ),
//           point2()
//         ],
//       ),
//     );
//   }
//
//   Widget slider() {
//     return SizedBox(
//       width: 1000.0.w,
//       child: SliderTheme(
//         data: SliderTheme.of(context).copyWith(
//           //已拖动的颜色
//           activeTrackColor: Color(0xff00B5D0),
//           //未拖动的颜色
//           inactiveTrackColor: Color(0xff00B5D0).withOpacity(0.4),
//
//           //提示进度的气泡的背景色
//           valueIndicatorColor: Color(0xff00B5D0),
//           //提示进度的气泡文本的颜色
//           valueIndicatorTextStyle: TextStyle(
//             color: Colors.white,
//           ),
//
//           //滑块中心的颜色
//           thumbColor: Color(0xff00B5D0),
//           //滑块边缘的颜色
//           // overlayColor: Color(0xff00B5D0),
//
//           //divisions对进度线分割后，断续线中间间隔的颜色
//           inactiveTickMarkColor: Colors.white,
//         ),
//         child: Slider(
//           value: month,
//           label: month.toInt().toString(),
//           min: 0.0,
//           max: 12.0,
//           divisions: 10,
//           onChanged: (val) {
//             setState(() {
//               month = val.floorToDouble(); //转化成double
//             });
//           },
//         ),
//       ),
//     );
//   }
//
//   Widget button() {
//     return InkWell(
//       onTap: () => checkAmount(),
//       child: SizedBox(
//         width: 768.0.w,
//         height: 124.0.h,
//         child: Stack(
//           alignment: Alignment.center,
//           children: <Widget>[
//             Pinned.fromSize(
//               bounds: Rect.fromLTWH(0.0, 0.0, 768.0.w, 124.0.h),
//               size: Size(768.0.w, 124.0.h),
//               pinLeft: true,
//               pinRight: true,
//               pinTop: true,
//               pinBottom: true,
//               child: Container(
//                 decoration: BoxDecoration(
//                   borderRadius: BorderRadius.circular(71.0.h),
//                   gradient: LinearGradient(
//                     begin: Alignment(0.0, -1.0),
//                     end: Alignment(0.0, 1.0),
//                     colors: [const Color(0xff908468), const Color(0xff908468)],
//                     stops: [0.0, 1.0],
//                   ),
//                 ),
//               ),
//             ),
//             Text(
//               AppLocalizations.of(context)!
//                   .translate('lendex_borrow_calculate'),
//               style: TextStyle(
//                 fontFamily: AppLocalizations.of(context)!.translate('font1'),
//                 fontSize: 46.h,
//                 color: const Color(0xff000000),
//                 letterSpacing: 1.38.w,
//               ),
//               textAlign: TextAlign.left,
//             ),
//           ],
//         ),
//       ),
//     );
//   }
//
//   Widget point() {
//     return Container(
//       width: 12.0.h,
//       height: 12.0.h,
//       decoration: BoxDecoration(
//         borderRadius: BorderRadius.all(Radius.elliptical(9999.0, 9999.0)),
//         color: const Color(0xff908468),
//       ),
//     );
//   }
//
//   Widget point2() {
//     return Container(
//       height: 30.h,
//       width: 100.w,
//       child: Row(
//         children: [
//           Container(
//             width: 12.0.h,
//             height: 12.0.h,
//             decoration: BoxDecoration(
//               borderRadius: BorderRadius.all(Radius.elliptical(9999.0, 9999.0)),
//               color: const Color(0xff908468),
//             ),
//           ),
//           SizedBox(
//             width: 34.w,
//           ),
//           Container(
//             width: 12.0.h,
//             height: 12.0.h,
//             decoration: BoxDecoration(
//               borderRadius: BorderRadius.all(Radius.elliptical(9999.0, 9999.0)),
//               color: const Color(0xff908468),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
//
//   Future checkAmount() async {
//     SharedPreferences pref = await SharedPreferences.getInstance();
//     final lang = pref.getString('language_code');
//     if (amountInput.text.isEmpty) {
//       print('กรุณากรอกจำนวน');
//       showColoredToast(
//         AppLocalizations.of(context)!.translate('alert_enter_number'),
//       );
//     } else {
//       if (double.parse(amountInput.text.replaceAll(',', '')) >
//           double.parse(maxBorrow)) {
//         showColoredToast(
//             AppLocalizations.of(context)!.translate('alert_overdue'));
//       } else if (double.parse(amountInput.text.replaceAll(',', '')) == 0) {
//         showColoredToast(
//             AppLocalizations.of(context)!.translate('alert_enter_number_0'));
//       } else {
//         var borrowInterest =
//             double.parse(amountInput.text.replaceAll(',', '')) * apyTotal / 100;
//         await DeviceUtils.hideKeyboard(context);
//         await showDialog(
//           context: context,
//           builder: (BuildContext context) => CustomDialog(
//             borrowAmount: f
//                 .format(double.parse(amountInput.text.replaceAll(',', '')))
//                 .toString(),
//             borrowAPY: apyTotal.toStringAsFixed(3),
//             borrowDay: DateFormat('dd MMM yyyy', lang).format(DateTime(
//                 DateTime.now().year + 1,
//                 DateTime.now().month,
//                 DateTime.now().day)),
//             borrowGuarantee: f.format(double.parse(totalLock)).toString(),
//             borrowInterest: f2.format(borrowInterest).toString(),
//             borrowSum: f
//                 .format((double.parse(amountInput.text.replaceAll(',', '')) +
//                     borrowInterest))
//                 .toString(),
//           ),
//         );
//       }
//     }
//   }
//
//   Widget rowHead(context) {
//     return Row(
//       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//       children: [
//         FlatButton(
//           child: Container(
//             padding: EdgeInsets.all(
//               mediaQuery(context, 'height', 0),
//             ),
//             height: mediaQuery(context, 'height', 36.33),
//             width: mediaQuery(context, 'height', 24.5),
//             child: SvgPicture.string(
//               '<svg viewBox="-281.5 3213.3 24.5 36.3" ><path  d="M -259.5249938964844 3249.675048828125 C -260.1029968261719 3249.675048828125 -260.6839904785156 3249.47607421875 -261.156005859375 3249.068115234375 L -281.5329895019531 3231.508056640625 L -261.156005859375 3213.947021484375 C -260.1109924316406 3213.0458984375 -258.531005859375 3213.162109375 -257.6300048828125 3214.208984375 C -256.72900390625 3215.2548828125 -256.8460083007812 3216.8330078125 -257.8919982910156 3217.73388671875 L -273.8739929199219 3231.508056640625 L -257.8919982910156 3245.281005859375 C -256.8460083007812 3246.18310546875 -256.72900390625 3247.760986328125 -257.6300048828125 3248.806884765625 C -258.125 3249.381103515625 -258.822998046875 3249.675048828125 -259.5249938964844 3249.675048828125 Z" fill="#231f20" stroke="none" stroke-width="1" stroke-miterlimit="10" stroke-linecap="butt" /></svg>',
//               allowDrawingOutsideViewBox: true,
//               fit: BoxFit.fill,
//             ),
//           ),
//           onPressed: () {
//             Navigator.of(context).pop();
//           },
//         ),
//         FlatButton(
//           onPressed: () {
//             AppRoutes.makeFirst(context, HomeLikewallet());
//           },
//           child: Image.asset(
//             LikeWalletImage.bg_home,
//             height: mediaQuery(context, "height", 44.47),
//           ),
//         ),
//       ],
//     );
//   }
// }
