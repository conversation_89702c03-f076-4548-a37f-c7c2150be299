import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/screen_util.dart';
import 'dart:ui';
import 'package:flutter/rendering.dart';
import 'package:likewallet/bank/banking.dart';
import 'package:image_picker/image_picker.dart';
import 'package:scan/scan.dart';

class scanAddress extends StatefulWidget {
  _scanAddress createState() => new _scanAddress();
}

class _scanAddress extends State<scanAddress> {
  List<String> data = [];
  // GlobalKey<CameraMlVisionState> qr_scan = GlobalKey();
  // BarcodeDetector detector = FirebaseVision.instance.barcodeDetector();

  final ImagePicker _picker = ImagePicker();

  late String barcode;
  int scanCount = 0;
  int number = 0;
  late File _image;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  Future scanFromImage(path) async {
    barcode = (await Scan.parse(path).catchError((onError) {
      throw onError;
    }))!;
    print(barcode);
    if (barcode.substring(0, 2) == '0x') {
      Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(
          builder: (context) =>
              Banking(scanActive: 'active', address: barcode.toString()),
        ),
        ModalRoute.withName('/bank'),
      );
    }
  }

  Future getImage() async {
    var image = await _picker.getImage(source: ImageSource.gallery);

    setState(() {
      _image = File(image!.path);
      number = 1;
    });
    print(_image.path);
    scanFromImage(_image.path);
  }

  changeContent(value) {
//    if(value == 0){
//      setState(() {
//        curImage == 'none';
//      });
//    }
    setState(() {
      number = value;
    });
  }

  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xff141322),
      body: Stack(
        alignment: Alignment.center,
        children: <Widget>[
          backPage(),
          title(),
          number == 0
              ? scan()
              : Positioned(
                  top: MediaQuery.of(context).size.height *
                      Screen_util("height", 390),
                  child: Container(
                    color: Colors.grey.withOpacity(0.3),
                    height: MediaQuery.of(context).size.height *
                        Screen_util("height", 780),
                    width: MediaQuery.of(context).size.width *
                        Screen_util("width", 1080),
                  ),
                ),
          // BorderCamera(),
          if (number == 0) detail()
        ],
      ),
    );
  }

  Widget backPage() {
    return Positioned(
        top: MediaQuery.of(context).size.height * Screen_util("height", 139.33),
        child: GestureDetector(
          child: new Container(
            alignment: Alignment.centerLeft,
            width: MediaQuery.of(context).size.width,
            child: new IconButton(
              icon: new Icon(
                Icons.arrow_back_ios,
                size: MediaQuery.of(context).size.height *
                    Screen_util("height", 44.47),
              ),
              color: Color(0xff707071),
              onPressed: () => {Navigator.of(context).pop()},
            ),
          ),
        ));
  }

  Widget title() {
    return Positioned(
        top: MediaQuery.of(context).size.height * Screen_util("height", 167),
        child: Container(
            width: MediaQuery.of(context).size.width,
            alignment: Alignment.center,
            child: Column(
              children: <Widget>[
                Container(
                    height: MediaQuery.of(context).size.height *
                        Screen_util("height", 110),
                    width: MediaQuery.of(context).size.width *
                        Screen_util("width", 633.92),
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: Colors.white.withOpacity(0.2),
                          width: MediaQuery.of(context).size.width *
                              Screen_util("width", 2),
                        ),
                      ),
                    ),
                    child: Text(
                      AppLocalizations.of(context)!.translate('scanpay_title'),
                      style: TextStyle(
                        color: Color(0xffB4E60D),
                        fontSize: MediaQuery.of(context).size.height *
                            Screen_util("height", 50),
                        fontFamily: AppLocalizations.of(context)!
                            .translate('font1Light'),
                        letterSpacing: 0.1,
                      ),
                    )),
                GestureDetector(
                    onTap: () {
                      getImage();
                    },
                    child: Container(
                      height: MediaQuery.of(context).size.height *
                          Screen_util("width", 50),
                      width: MediaQuery.of(context).size.width *
                          Screen_util("width", 300),
                      alignment: Alignment.center,
                      child: Text(
                        AppLocalizations.of(context)!
                            .translate('scanpay_select'),
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: MediaQuery.of(context).size.height *
                              Screen_util("height", 30),
                          fontFamily: AppLocalizations.of(context)!
                              .translate('font1Light'),
                          letterSpacing: 0.5,
                        ),
                      ),
                    )),
              ],
            )));
  }

  Widget BorderCamera() {
    return Positioned(
      top: MediaQuery.of(context).size.height * Screen_util("height", 470),
      child: Image.asset('assets/image/border_camera.png'),
      height: mediaQuery(context, 'height', 608.3),
      width: mediaQuery(context, 'width', 924.53),
    );
  }

  Widget scan() {
    return Positioned(
      top: MediaQuery.of(context).size.height * Screen_util("height", 390),
      child: Container(
        color: Colors.grey.withOpacity(0.3),
        height: MediaQuery.of(context).size.height * Screen_util("height", 780),
        width: MediaQuery.of(context).size.width * Screen_util("width", 1080),
        // child: CameraMlVision<List<Barcode>>(
        //   key: qr_scan,
        //   detector: detector.detectInImage,
        //   resolution: ResolutionPreset.high,
        //   onResult: (barcodes) {
        //     if (barcodes == null ||
        //         barcodes.isEmpty ||
        //         data.contains(barcodes.first.displayValue) ||
        //         !mounted) {
        //       return;
        //     }
        //     setState(() {
        //       data.add(barcodes.first.displayValue);
        //       print(barcodes.first.displayValue);
        //       Navigator.pop(context, barcodes.first.displayValue.toString());
        //       if (barcodes.first.displayValue.substring(0, 2) == '0x') {
        //         scanCount = 1;
        //         qr_scan.currentState.deactivate();
        //       }
        //     });
        //   },
        //   onDispose: () {
        //     detector.close();
        //   },
        // ),
//        child: new QrCamera(
//          key: qr_scan,
//          onError: (context, error) => Text(
//            error.toString(),
//            style: TextStyle(color: Colors.red),
//          ),
//          qrCodeCallback: (code) {
//            print(qr_scan);
//            if (scanCount == 0) {
//              //check format address ethereum
//              if (code.substring(0, 2) == '0x') {
//                scanCount = 1;
//                qr_scan.currentState.deactivate();
//
//                Navigator.pushAndRemoveUntil(
//                  context,
//                  MaterialPageRoute(
//                    builder: (context) => Banking(
//                      scanActive: 'active',
//                      address: code.toString(),
//                    ),
//                  ),
//                  ModalRoute.withName('/bank'),
//                );
//              }
//            }
//          },
//        ),
      ),
    );
  }

  Widget detail() {
    return Positioned(
      top: MediaQuery.of(context).size.height * Screen_util("height", 1325),
      child: new Container(
          alignment: Alignment.center,
          width: MediaQuery.of(context).size.width * Screen_util("width", 700),
          child: Text(
            AppLocalizations.of(context)!.translate('scanpay_details'),
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Color(0xff707071),
              fontSize: MediaQuery.of(context).size.height *
                  Screen_util("height", 39),
              fontFamily: AppLocalizations.of(context)!.translate('font1Light'),
              letterSpacing: 0.5,
            ),
          )),
    );
  }

  //เส้นเเนวตั้งเมนู
  Widget border() {
    return Container(
      height: MediaQuery.of(context).size.height * Screen_util("height", 125),
      decoration: BoxDecoration(
          border: Border(
        right: BorderSide(
          color: Colors.white,
          width: MediaQuery.of(context).size.width * 0.00185185185,
        ),
      )),
      alignment: Alignment.topCenter,
    );
  }
}
