import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:likewallet/ImageTheme.dart';

import 'package:likewallet/Theme.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/quickpay/favorites/list.dart';
import 'package:likewallet/screen_util.dart';
import 'package:http/http.dart' as http;
import 'package:likewallet/app_config.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:async';
import 'dart:convert';

import 'package:likewallet/bank/banking.dart';
import 'package:likewallet/libraryman/serviceHTTP.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'favorites/search_list.dart';

class stores extends StatefulWidget {
  _stores createState() => new _stores();
}

class _stores extends State<stores> {
  late AbstractServiceHTTP APIHttp;
  List<list> _list = [];
  // List group = List();
  List group = [];
  // List<list> restaurant = List();
  // List<list> search = List();
  // List<String> favorite = List<String>();
  List<list> restaurant = [];
  List<list> search = [];
  List<String> favorite = [];
  late BaseAuth auth;
  List<String> _groupList = [
    "RESTAURANT \nCOFFEE SHOP\nBAKERY",
    "AUTO DEALER\nGARAGE\nCARCARE",
    "LEASING",
    "UTILITIES BILL",
    "RETAIL\nMINIMART"
  ];
  late SharedPreferences pref;
  bool delete = false;

  getShop() async {
    //เรียก API ร้านค้า
    APIHttp.getQuickpayShop().then((data) {
      setState(() {
        _list = data[0];
        search = data[0];
        group = data[1];
      });
    });
  }

  void getPref() async {
    pref = await SharedPreferences.getInstance();
    setState(() {
      favorite = pref.getStringList('shopFavorite') ?? [];
      print(favorite);
    });
  }

  deleteFavorites(bool value) {
    setState(() {
      delete = value;
      print(delete);
    });
  }

  bool saveFavorite(save) {
    pref.setStringList('shopFavorite', save);
    print(save);
    auth.getCurrentUser().then((decodeToken) {
      // print(decodeToken);
      FirebaseFirestore.instance
          .collection('favoriteQuickpay')
          .doc(decodeToken!.uid)
          .set({'favorite': save});
    });
    return true;
  }

  @override
  void initState() {
    super.initState();
    auth = Auth();
    APIHttp = ServiceHTTP();
    getShop();
    setInit();
  }

  void setInit() {
    getPref();
  }

//  final List<int> numbers = [1, 2, 3, 5, 8, 13, 21, 34, 55];
  Widget build(BuildContext context) {
    final _debouncer = Debouncer(milliseconds: 500);
    return GestureDetector(
      onTap: () {
        FocusScopeNode currentFocus = FocusScope.of(context);

        if (!currentFocus.hasPrimaryFocus) {
          currentFocus.unfocus();
        }
      },
      child: Scaffold(
        backgroundColor: Color(0xffFFFFFF),
        body: Stack(
          alignment: Alignment.center,
          children: <Widget>[
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Container(
                  height: MediaQuery.of(context).size.height,
                  width: mediaQuery(context, 'width', 334),
                  color: LikeWalletAppTheme.bule2_10,
                ),
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                        borderRadius: new BorderRadius.circular(3.0),
                        color: LikeWalletAppTheme.bule2_9,
                        gradient: new LinearGradient(
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                          colors: [
                            LikeWalletAppTheme.bule2_9,
                            LikeWalletAppTheme.bule2_9.withOpacity(0.95),
                          ],
                        )),
                    height: MediaQuery.of(context).size.height,
                    width: MediaQuery.of(context).size.width,
                  ),
                ),
              ],
            ),
            Positioned(
                top: MediaQuery.of(context).size.height *
                    Screen_util("height", 139.33),
                left: MediaQuery.of(context).size.height *
                    Screen_util("width", 5),
                child: GestureDetector(
                  child: new Container(
                    alignment: Alignment.centerLeft,
                    width: MediaQuery.of(context).size.width,
                    child: new IconButton(
                      icon: new Icon(
                        Icons.arrow_back_ios,
                        size: MediaQuery.of(context).size.height *
                            Screen_util("height", 44.47),
                      ),
                      color: Color(0xff707071),
                      onPressed: () => {Navigator.of(context).pop()},
                    ),
                  ),
                )),
            Positioned(
                top: MediaQuery.of(context).size.height *
                    Screen_util("height", 175),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    Image.asset(
                      LikeWalletImage.icon_search,
                      height: MediaQuery.of(context).size.height *
                          Screen_util("height", 48),
                    ),
                    Container(
                      padding: EdgeInsets.only(
                        left: MediaQuery.of(context).size.width *
                            Screen_util("width", 20),
                      ),
                      width: MediaQuery.of(context).size.width *
                          Screen_util("width", 250),
                      child: TextField(
                        style: TextStyle(
                          color: LikeWalletAppTheme.white,
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font1'),
                          fontStyle: FontStyle.normal,
                          fontWeight: FontWeight.w500,
                          fontSize: MediaQuery.of(context).size.height *
                              Screen_util("height", 36),
                        ),
                        decoration: InputDecoration(
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.all(0.0),
                          hintText: AppLocalizations.of(context)!
                              .translate('stores_title'),
                          hintStyle: TextStyle(
                            color: LikeWalletAppTheme.white.withOpacity(0.5),
                            fontFamily: AppLocalizations.of(context)!
                                .translate('font1'),
                            fontStyle: FontStyle.normal,
                            fontWeight: FontWeight.w500,
                            fontSize: MediaQuery.of(context).size.height *
                                Screen_util("height", 36),
                          ),
                        ),
                        onChanged: (string) {
                          _debouncer.run(() {
                            setState(() {
                              _list = search
                                  .where((u) => (u.title
                                          .toString()
                                          .toLowerCase()
                                          .contains(string.toLowerCase()) ||
                                      u.details
                                          .toString()
                                          .toLowerCase()
                                          .contains(string.toLowerCase())))
                                  .toList();
                              print(search);
                            });
                          });
                        },
                      ),
                    )
                  ],
                )),
            Positioned(
                width: MediaQuery.of(context).size.width,
                top: MediaQuery.of(context).size.height *
                    Screen_util("height", 280),
                child: Row(
                  children: <Widget>[
                    Container(
                        width: MediaQuery.of(context).size.width *
                            Screen_util("width", 1080),
                        height: MediaQuery.of(context).size.height *
                            Screen_util("height", 2340),
                        child: new ListView.builder(
                            scrollDirection: Axis.vertical,
                            shrinkWrap: true,
//                          padding: EdgeInsets.all(10.0),
                            itemCount: group.length,
//                          physics: const ClampingScrollPhysics(),
                            // itemExtent: 10.0,
                            // reverse: true, //makes the list appear in descending order
                            itemBuilder: (BuildContext context, int index) {
                              return _buildItems1(index);
                            })),
                  ],
                )),
            Positioned(
                width: MediaQuery.of(context).size.width,
                top: MediaQuery.of(context).size.height *
                    Screen_util("height", 1810),
                child: Row(
                  children: <Widget>[
                    Container(
                      // color: Colors.white,
                      child: Container(
                          padding: EdgeInsets.only(
                            bottom: MediaQuery.of(context).size.height *
                                Screen_util("height", 30),
                          ),
                          alignment: Alignment.center,
                          width: MediaQuery.of(context).size.width,
                          height: MediaQuery.of(context).size.height *
                              Screen_util("height", 636),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment(0.0, 1.0),
                              end: Alignment(0.0, -1.0),
                              colors: [
                                const Color(0xff141322),
                                const Color(0xff2b2a38)
                              ],
                              stops: [0.0, 1.0],
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: const Color(0x73000000),
                                offset: Offset(0, -5),
                                blurRadius: 30,
                              ),
                            ],
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: <Widget>[
                              Container(
                                  width: MediaQuery.of(context).size.width *
                                      Screen_util("width", 1080),
                                  height: MediaQuery.of(context).size.height *
                                      Screen_util("height", 310),
                                  padding: EdgeInsets.only(
                                    left: mediaQuery(context, 'width', 55),
                                  ),
                                  child: new ListView.builder(
                                      scrollDirection: Axis.horizontal,
                                      shrinkWrap: true,
//                          padding: EdgeInsets.all(10.0),
                                      itemCount: favorite.length,
//                          physics: const ClampingScrollPhysics(),
                                      // itemExtent: 10.0,
                                      // reverse: true, //makes the list appear in descending order
                                      itemBuilder:
                                          (BuildContext context, int int) {
                                        return _buildItems2(int);
                                      }))
                            ],
                          )),
                    ),
                  ],
                )),
            Positioned(
                top: MediaQuery.of(context).size.height *
                    Screen_util("height", 1850),
                left: MediaQuery.of(context).size.width *
                    Screen_util("width", 75),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[
                    Container(
                      width: MediaQuery.of(context).size.width *
                          Screen_util("width", 20),
                      height: MediaQuery.of(context).size.height *
                          Screen_util("height", 20),
                      decoration: BoxDecoration(
                          color: Color(0xffB4E60D), shape: BoxShape.circle),
                    ),
                    Text(
                      '  ' +
                          AppLocalizations.of(context)!
                              .translate('stores_favorites'),
                      style: TextStyle(
                        letterSpacing: 1,
                        color: LikeWalletAppTheme.white.withOpacity(0.5),
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                        fontStyle: FontStyle.normal,
                        fontWeight: FontWeight.w100,
                        fontSize: MediaQuery.of(context).size.height *
                            Screen_util("height", 40),
                        shadows: [
                          Shadow(
                            blurRadius: 5.0,
                            color: LikeWalletAppTheme.black,
                            offset: Offset(0.0, 0.0),
                          ),
                        ],
                      ),
                    ),
                  ],
                )),
            Positioned(
                top: MediaQuery.of(context).size.height *
                    Screen_util("height", 1750),
                right: MediaQuery.of(context).size.width *
                    Screen_util("width", 200),
                child: GestureDetector(
                  onTap: () {
                    if (delete == false)
                      deleteFavorites(true);
                    else
                      deleteFavorites(false);
                  },
                  child: Image.asset(
                    'assets/image/delete_favorites.png',
                    height: MediaQuery.of(context).size.height *
                        Screen_util("height", 125),
                    width: MediaQuery.of(context).size.height *
                        Screen_util("height", 125),
                  ),
                )),
            Positioned(
                top: MediaQuery.of(context).size.height *
                    Screen_util("height", 1750),
                right: MediaQuery.of(context).size.width *
                    Screen_util("width", 50),
                child: GestureDetector(
                  onTap: () {
                    Navigator.pushNamed(context, '/listFavorites')
                        .whenComplete(() {
                      setInit();
                    });
                    setState(() {
                      deleteFavorites(false);
                    });
                  },
                  child: Image.asset(
                    'assets/image/add_favorites.png',
                    height: MediaQuery.of(context).size.height *
                        Screen_util("height", 125),
                    width: MediaQuery.of(context).size.height *
                        Screen_util("height", 125),
                  ),
                )),
          ],
        ),
      ),
    );
  }

  Widget _buildItems1(int index) {
    if(index == 0) {
      return Container(
        margin: EdgeInsets.only(bottom: mediaQuery(context, 'height', 25)),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Container(
              padding: EdgeInsets.only(
                left:
                MediaQuery.of(context).size.width * Screen_util("width", 75),
              ),
              width:
              MediaQuery.of(context).size.width * Screen_util("width", 300),
              // height:
              //     MediaQuery.of(context).size.height * Screen_util("height", 247),
              alignment: Alignment.centerLeft,
              child: Text(
                AppLocalizations.of(context)!.translate(group[index]),
                style: TextStyle(
                    color: LikeWalletAppTheme.white.withOpacity(0.3),
                    letterSpacing: 1,
                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
                    fontWeight: FontWeight.w100,
                    fontSize: MediaQuery.of(context).size.height *
                        Screen_util("height", 28)),
              ),
            ),
            Expanded(
                child: Container(
                    padding: EdgeInsets.only(
                      left: MediaQuery.of(context).size.width *
                          Screen_util("width", 64),
                    ),
                    height: MediaQuery.of(context).size.height *
                        Screen_util("height", 247.5),
                    child: new ListView.builder(
                        scrollDirection: Axis.horizontal,
                        shrinkWrap: true,
                        itemCount: _list.length,
//                          physics: const ClampingScrollPhysics(),
                        // itemExtent: 10.0,
                        // reverse: true, //makes the list appear in descending order
                        itemBuilder: (BuildContext context, int i) {
//                  print(group[index]);
//                  print(_list[i].shopGroup);
                          return group[index].toString() ==
                              _list[i].shopGroup.toString()
                              ? detail(i)
                              : Container();
                        })))
          ],
        ),
      );
    }else {
      return Container();
    }
  }

  Widget _buildItems2(int index) {
    return new Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        new ListView.builder(
            scrollDirection: Axis.horizontal,
            shrinkWrap: true,
//                          padding: EdgeInsets.all(10.0),
            itemCount: _list.length,
//                          physics: const ClampingScrollPhysics(),
            // itemExtent: 10.0,
            // reverse: true, //makes the list appear in descending order
            itemBuilder: (BuildContext context, int i) {
              return favorite[index].toString() == _list[i].running.toString()
                  ? favorites(i)
                  : Container();
            })
      ],
    );
  }

  Widget detail(int index) {
    if(index == 7 ) {
      return Container();
    }else {
      return GestureDetector(
          onTap: () {
            print(index);
            Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) => Banking(
                      selectedAddr: 'address',
                      shopID: _list[index].running,
                      source: 'favorite',
                      contract: _list[index].contract != null
                          ? _list[index].contract
                          : 'no',
                      abi:
                      _list[index].contract != null ? _list[index].abi : 'no',
                      callFunction: _list[index].contract != null
                          ? _list[index].callFunction
                          : 'no')),
            );
          },
          child: _list[index].logo!.isNotEmpty
              ? Container(
            // padding: EdgeหInsets.only(left: mediaQuery(context, 'width', 58)),
            margin: EdgeInsets.only(
              left: mediaQuery(context, 'width', 58),
            ),
            height: MediaQuery.of(context).size.height *
                Screen_util("height", 215),
            width: MediaQuery.of(context).size.height *
                Screen_util("height", 215),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: [
                new BoxShadow(
                    color: Colors.black.withOpacity(0.3),
                    offset: new Offset(1, 2),
                    blurRadius: 3.0,
                    spreadRadius: 1.0),
              ],
            ),
            child: CachedNetworkImage(
              placeholder: (context, url) => SizedBox(
                height: mediaQuery(context, 'height', 200),
                width: mediaQuery(context, 'height', 200),
                child: CustomLoading(),
              ),
              imageUrl: _list[index].logo ?? '',
              fit: BoxFit.contain,
              // height: MediaQuery.of(context).size.height * Screen_util("height", 4),
            ),
          )
              : Container());
    }

  }

  Widget favorites(int index) {
    return Container(
      padding: EdgeInsets.only(right: mediaQuery(context, 'width', 43)),
      child: Stack(
        children: <Widget>[
          GestureDetector(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (context) => Banking(
                        selectedAddr: 'address',
                        shopID: _list[index].running,
                        source: 'favorite',
                        contract: _list[index].contract != null
                            ? _list[index].contract
                            : 'no',
                        abi: _list[index].contract != null
                            ? _list[index].abi
                            : 'no',
                        callFunction: _list[index].contract != null
                            ? _list[index].callFunction
                            : 'no')),
              );
            },
            child: CachedNetworkImage(
              placeholder: (context, url) => SizedBox(
                  height: mediaQuery(context, 'height', 200),
                  width: mediaQuery(context, 'height', 200),
                  child: CustomLoading()),
              imageUrl: _list[index].logo ?? '',
              height: mediaQuery(context, 'height', 247),
            ),
          ),
          if (delete == true)
            Positioned(
                bottom: MediaQuery.of(context).size.height *
                    Screen_util("height", 30),
                right: MediaQuery.of(context).size.height *
                    Screen_util("height", 30),
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      favorite.remove(_list[index].running.toString());
                    });
                    saveFavorite(favorite);
                  },
                  child: Image.asset(
                    'assets/image/delete_favorites.png',
                    height: MediaQuery.of(context).size.height *
                        Screen_util("height", 86),
                    width: MediaQuery.of(context).size.height *
                        Screen_util("height", 86),
                  ),
                ))
        ],
      ),
    );
  }
}

class Debouncer {
  final int? milliseconds;
  late VoidCallback action;
  late Timer _timer;

  Debouncer({this.milliseconds});

  run(VoidCallback action) {
    if (null != _timer) {
      _timer.cancel();
    }
    _timer = Timer(Duration(milliseconds: milliseconds!), action);
  }
}
