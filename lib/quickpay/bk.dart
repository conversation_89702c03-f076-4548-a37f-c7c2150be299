//
//import 'dart:io';
//import 'dart:typed_data';
//
//import 'package:flutter/cupertino.dart';
//import 'package:flutter/material.dart';
//import 'package:likewallet/libraryman/app_local.dart';
//import 'package:likewallet/libraryman/custom_loading.dart';
//import 'package:likewallet/screen_util.dart';
//import 'dart:ui';
//import 'package:flutter/rendering.dart';
//import 'package:qr_mobile_vision/qr_camera.dart';
//import 'package:likewallet/bank/banking.dart';
//import 'package:image_picker/image_picker.dart';
//import 'package:qrscan/qrscan.dart' as scanner;
//
//class scanPay extends StatefulWidget {
//  _scanPay createState() => new _scanPay();
//}
//
//class _scanPay extends State<scanPay> {
//
//  GlobalKey<QrCameraState> qr_scan = GlobalKey();
//  String barcode;
//  int scanCount = 0;
//  int number = 0;
//  File _image;
//
//  @override
//  void initState() {
//    // TODO: implement initState
//    super.initState();
//  }
//
//  Future scanFromImage(path) async {
//    barcode = await scanner.scanPath(path).catchError((onError) {
//      //show error
//    });
//    print(barcode);
//    if (barcode.substring(0, 2) == '0x') {
//      Navigator.pushAndRemoveUntil(
//        context,
//        MaterialPageRoute(
//          builder: (context) => Banking(
//            scanActive: 'active',
//            address: barcode.toString(),
//          ),
//        ),
//        ModalRoute.withName('/bank'),
//      );
//    }
//  }
//
//  Future getImage() async {
//    var image = await ImagePicker.pickImage(source: ImageSource.gallery);
//
//    setState(() {
//      _image = image;
//      number = 1;
//    });
//    print(_image.path);
//    scanFromImage(_image.path);
//  }
//
//  changeContent(value) {
////    if(value == 0){
////      setState(() {
////        curImage == 'none';
////      });
////    }
//    setState(() {
//      number = value;
//    });
//  }
//
//  Widget build(BuildContext context) {
//    return Scaffold(
//      backgroundColor: Color(0xff141322),
//      body: Stack(
//        alignment: Alignment.center,
//        children: <Widget>[
//          backPage(),
//          title(),
//          number == 0
//              ? scan()
//              : Positioned(
//            top: MediaQuery.of(context).size.height *
//                Screen_util("height", 390),
//            child: Container(
//              color: Colors.grey.withOpacity(0.3),
//              height: MediaQuery.of(context).size.height *
//                  Screen_util("height", 780),
//              width: MediaQuery.of(context).size.width *
//                  Screen_util("width", 1080),
//            ),
//          ),
//          if (number == 0) detail()
//        ],
//      ),
//    );
//  }
//
//  Widget backPage() {
//    return Positioned(
//        top: MediaQuery.of(context).size.height * Screen_util("height", 139.33),
//        child: GestureDetector(
//          child: new Container(
//            alignment: Alignment.centerLeft,
//            width: MediaQuery.of(context).size.width,
//            child: new IconButton(
//              icon: new Icon(
//                Icons.arrow_back_ios,
//                size: MediaQuery.of(context).size.height *
//                    Screen_util("height", 44.47),
//              ),
//              color: Color(0xff707071),
//              onPressed: () => {Navigator.of(context).pop()},
//            ),
//          ),
//        ));
//  }
//
//  Widget title() {
//    return Positioned(
//        top: MediaQuery.of(context).size.height * Screen_util("height", 167),
//        child: Container(
//            width: MediaQuery.of(context).size.width,
//            alignment: Alignment.center,
//            child: Column(
//              children: <Widget>[
//                Container(
//                    height: MediaQuery.of(context).size.height *
//                        Screen_util("height", 79),
//                    width: MediaQuery.of(context).size.width *
//                        Screen_util("width", 633.92),
//                    alignment: Alignment.center,
//                    decoration: BoxDecoration(
//                      border: Border(
//                        bottom: BorderSide(
//                          color: Colors.white,
//                          width: MediaQuery.of(context).size.width *
//                              Screen_util("width", 1),
//                        ),
//                      ),
//                    ),
//                    child: Text(
//                      AppLocalizations.of(context)!.translate('scanpay_title'),
//                      style: TextStyle(
//                        color: Color(0xffB4E60D),
//                        fontSize: MediaQuery.of(context).size.height *
//                            Screen_util("height", 50),
//                        fontFamily:
//                        AppLocalizations.of(context)!.translate('font1'),
//                      ),
//                    )),
//                Row(
//                  mainAxisAlignment: MainAxisAlignment.center,
//                  crossAxisAlignment: CrossAxisAlignment.start,
//                  children: <Widget>[
//                    FlatButton(
//                      onPressed: () {
//                        setState(() {
//                          number = 0;
//                        });
//                      },
//                      child: Container(
//                          height: MediaQuery.of(context).size.height *
//                              Screen_util("height", 68),
//                          width: MediaQuery.of(context).size.width *
//                              Screen_util("width", 250),
//                          alignment: Alignment.center,
//                          child: Text(
//                            AppLocalizations.of(context)
//                                .translate('scanpay_scan'),
//                            style: TextStyle(
//                              color: Colors.white,
//                              fontSize: MediaQuery.of(context).size.height *
//                                  Screen_util("height", 35),
//                              fontFamily: AppLocalizations.of(context)
//                                  .translate('font1'),
//                            ),
//                          )),
//                    ),
//                    border(),
//                    FlatButton(
//                        onPressed: () {
//                          getImage();
//                        },
//                        child: Container(
//                          height: MediaQuery.of(context).size.height *
//                              Screen_util("height", 68),
//                          width: MediaQuery.of(context).size.width *
//                              Screen_util("width", 250),
//                          alignment: Alignment.center,
//                          child: Text(
//                            AppLocalizations.of(context)
//                                .translate('scanpay_select'),
//                            style: TextStyle(
//                              color: Colors.white,
//                              fontSize: MediaQuery.of(context).size.height *
//                                  Screen_util("height", 35),
//                              fontFamily: AppLocalizations.of(context)
//                                  .translate('font1'),
//                            ),
//                          ),
//                        )),
//                  ],
//                )
//              ],
//            )));
//  }
//
//  Widget scan() {
//    return Positioned(
//      top: MediaQuery.of(context).size.height * Screen_util("height", 390),
//      child: Container(
//        color: Colors.grey.withOpacity(0.3),
//        height: MediaQuery.of(context).size.height * Screen_util("height", 780),
//        width: MediaQuery.of(context).size.width * Screen_util("width", 1080),
//        child: new QrCamera(
//          key: qr_scan,
//          onError: (context, error) => Text(
//            error.toString(),
//            style: TextStyle(color: Colors.red),
//          ),
//          qrCodeCallback: (code) {
//            print(qr_scan);
//            if (scanCount == 0) {
//              //check format address ethereum
//              if (code.substring(0, 2) == '0x') {
//                scanCount = 1;
//                qr_scan.currentState.deactivate();
//
//                Navigator.pushAndRemoveUntil(
//                  context,
//                  MaterialPageRoute(
//                    builder: (context) => Banking(
//                      scanActive: 'active',
//                      address: code.toString(),
//                    ),
//                  ),
//                  ModalRoute.withName('/bank'),
//                );
//              }
//            }
//          },
//        ),
//      ),
//    );
//  }
//
//  Widget detail() {
//    return Positioned(
//      top: MediaQuery.of(context).size.height * Screen_util("height", 1320),
//      child: new Container(
//          alignment: Alignment.center,
//          width: MediaQuery.of(context).size.width * 0.9,
//          child: Text(
//            AppLocalizations.of(context)!.translate('scanpay_details'),
//            style: TextStyle(
//              color: Color(0xff707071),
//              fontSize: MediaQuery.of(context).size.height *
//                  Screen_util("height", 45),
//              fontFamily: AppLocalizations.of(context)!.translate('font1'),
//            ),
//          )),
//    );
//  }
//
//  //เส้นเเนวตั้งเมนู
//  Widget border() {
//    return Container(
//      height: MediaQuery.of(context).size.height * Screen_util("height", 125),
//      decoration: BoxDecoration(
//          border: Border(
//            right: BorderSide(
//              color: Colors.white,
//              width: MediaQuery.of(context).size.width * 0.00185185185,
//            ),
//          )),
//      alignment: Alignment.topCenter,
//    );
//  }
//}
