import 'dart:convert';

import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:likewallet/Theme.dart';

import 'package:likewallet/bank/confirm_transection.dart';
import 'package:likewallet/jsonDecode/vending.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:likewallet/libraryman/serviceHTTP.dart';
import 'package:likewallet/quickpay/favorites/list.dart';
import 'package:likewallet/screen_util.dart';
import 'dart:ui';
import 'package:flutter/rendering.dart';
import 'package:likewallet/bank/banking.dart';
import 'package:image_picker/image_picker.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';
import 'package:qrcode_flutter/qrcode_flutter.dart';
import 'package:qrscan/qrscan.dart' as scanner;
// import 'package:flutter_camera_ml_vision/flutter_camera_ml_vision.dart';
// import 'package:firebase_ml_vision/firebase_ml_vision.dart';
// import 'package:qrcode_flutter/qrcode_flutter.dart';

class scanPay extends StatefulWidget {
  const scanPay({
    Key? key,
  }) : super(key: key);
  _scanPay createState() => new _scanPay();
}

class _scanPay extends State<scanPay> with TickerProviderStateMixin {
  late String qr;
  GlobalKey key = new GlobalKey();
  bool _isLoad = false;
  late QRViewController controller;
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
  List<String> data = [];
  QRCaptureController _controller = QRCaptureController();
  // GlobalKey<CameraMlVisionState> qr_scan = GlobalKey();
  // BarcodeDetector detector = FirebaseVision.instance.barcodeDetector();
  late AbstractServiceHTTP APIHttp;
  late List<list> _list = [];
  late String barcode;
  int scanCount = 0;
  // int number = 0;
  late File _image;
  final ImagePicker _picker = ImagePicker();
  bool _loading = false;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    APIHttp = ServiceHTTP();
    getShop();
    // _controller.onCapture((data) {
    //   print('$data');
    //   if (data.substring(0, 2) == '0x') {
    //     _controller.pause();
    //     Navigator.pushAndRemoveUntil(
    //       context,
    //       MaterialPageRoute(
    //         builder: (context) => Banking(
    //           selectedAddr: 'address',
    //           scanActive: 'active',
    //           address: data,
    //         ),
    //       ),
    //       ModalRoute.withName('/bank'),
    //     );
    //   } else {
    //     print('else vending');
    //     Vending vending = Vending.fromJson(jsonDecode(data));
    //     print(vending);
    //     if (vending.function == 'transfer_vending') {
    //       Navigator.pushAndRemoveUntil(
    //         context,
    //         MaterialPageRoute(
    //             builder: (context) => confirmTransection(
    //                 titleName: vending.addr,
    //                 addressText: vending.addr,
    //                 amountSend: vending.amount.toString(),
    //                 rateCurrency: 1,
    //                 vending: vending,
    //                 isVending: true,
    //                 contract: 'no',
    //                 abi: 'no',
    //                 callFunction: 'no')),
    //         ModalRoute.withName('/confirmTransection'),
    //       );
    //     }
    //   }
    // });
  }

  getShop() async {
    setState(() => _loading = true);
    //เรียก API ร้านค้า
    APIHttp.listSpendkpayShop().then((data) {
      setState(() {
        _list = data[0];
        _loading = false;
      });
    });
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  Future scanFromImage(path) async {
    barcode = await scanner.scanPath(path).catchError((onError) {
      //show error
      throw onError;
      print('onError $onError');
    });

    var qrCodeResult = await QRCaptureController.getQrCodeByImagePath(path)
        .catchError((onError) {
      print(onError);
    });
    barcode = qrCodeResult.join('\n');
    if (barcode.substring(0, 2) == '0x') {
      Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(
          builder: (context) => Banking(
            selectedAddr: 'address',
            scanActive: 'active',
            address: barcode.toString(),
          ),
        ),
        ModalRoute.withName('/bank'),
      );
    } else {
      showShortToast('onError', Colors.red);
      // Vending vending = Vending.fromJson(jsonDecode(barcode));
//       if (vending.function == 'transfer_vending') {
//         print(vending);
//
// //        "ref_code": "123DDD",
// //    "machine_id": "123",
// //    "amount": 10000,
// //    "addr": "0x8BF4b6631e199b9c6A4a007e88CAcBa2e43D6716",
// //    "function": "transfer_vending",
// //    "payment_id": "VEN2020PKG00001"
//
//         Navigator.pushReplacement(
//           context,
//           MaterialPageRoute(
//               builder: (context) => confirmTransection(
//                   titleName: vending.addr,
//                   addressText: vending.addr,
//                   amountSend: vending.amount.toString(),
//                   rateCurrency: 1,
//                   vending: vending,
//                   isVending: true,
//                   contract: 'no',
//                   abi: 'no',
//                   callFunction: 'no')),
//           // ModalRoute.withName('/confirmTransection'),
//         );
// //        Navigator.pushAndRemoveUntil(
// //          context,
// //          MaterialPageRoute(
// //            builder: (context) => Banking(
// //              selectedAddr: 'address',
// //              scanActive: 'active',
// //              address: vending.addr,
// //              vending: vending,
// //              isVending: true,
// //              contract: vending.addr
// //            ),
// //          ),
// //          ModalRoute.withName('/bank'),
// //        );
//       }
    }
  }

  Future getImage() async {
    PickedFile? image = await _picker.getImage(source: ImageSource.gallery);
    setState(() {
      _image = File(image!.path);
      // number = 1;
    });
    print(_image.path);
    scanFromImage(_image.path);
  }

  changeContent(value) {
//    if(value == 0){
//      setState(() {
//        curImage == 'none';
//      });
//    }
    setState(() {
      // number = value;
    });
  }

  Widget build(BuildContext context) {
    return Scaffold(
      key: key,
      backgroundColor: Color(0xff141322),
      body: Stack(
        alignment: Alignment.center,
        children: <Widget>[backPage(), title(), scan(), detail()],
      ),
    );
  }

  Widget backPage() {
    return ModalProgressHUD(
      inAsyncCall: _loading,
      opacity: 0.1,
      progressIndicator: CustomLoading(),
      child: Positioned(
          top: MediaQuery.of(context).size.height *
              Screen_util("height", 139.33),
          child: GestureDetector(
            child: new Container(
              alignment: Alignment.centerLeft,
              width: MediaQuery.of(context).size.width,
              child: new IconButton(
                icon: new Icon(
                  Icons.arrow_back_ios,
                  size: MediaQuery.of(context).size.height *
                      Screen_util("height", 44.47),
                ),
                color: Color(0xff707071),
                onPressed: () => {Navigator.of(context).pop()},
              ),
            ),
          )),
    );
  }

  Widget title() {
    return Positioned(
        top: MediaQuery.of(context).size.height * Screen_util("height", 167),
        child: Container(
            width: MediaQuery.of(context).size.width,
            alignment: Alignment.center,
            child: Column(
              children: <Widget>[
                Container(
                    height: MediaQuery.of(context).size.height *
                        Screen_util("height", 110),
                    width: MediaQuery.of(context).size.width *
                        Screen_util("width", 633.92),
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: Colors.white.withOpacity(0.2),
                          width: MediaQuery.of(context).size.width *
                              Screen_util("width", 2),
                        ),
                      ),
                    ),
                    child: Text(
                      AppLocalizations.of(context)!.translate('scanpay_title'),
                      style: TextStyle(
                        color: LikeWalletAppTheme.bule1_2,
                        fontSize: MediaQuery.of(context).size.height *
                            Screen_util("height", 50),
                        fontFamily: AppLocalizations.of(context)!
                            .translate('font1Light'),
                        letterSpacing: 0.1,
                      ),
                    )),
                GestureDetector(
                    onTap: () {
                      getImage();
                    },
                    child: Container(
                      height: MediaQuery.of(context).size.height *
                          Screen_util("width", 50),
                      width: MediaQuery.of(context).size.width *
                          Screen_util("width", 300),
                      alignment: Alignment.center,
                      child: Text(
                        AppLocalizations.of(context)!
                            .translate('scanpay_select'),
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: MediaQuery.of(context).size.height *
                              Screen_util("height", 30),
                          fontFamily: AppLocalizations.of(context)!
                              .translate('font1Light'),
                          letterSpacing: 0.5,
                        ),
                      ),
                    )),
              ],
            )));
  }

  void _onQRViewCreated(QRViewController controller) async {
    this.controller = controller;
    controller.scannedDataStream.listen((scanData) async {
      try {
        if (!mounted) return;
        if (scanData.code.toString().substring(0, 5) == 'store') {
          for (var i = 0; i < _list.length; i++) {
            if (_list[i].address.toString() ==
                scanData.code.toString().replaceAll('store', '')) {
              print('ทำงาน');
              controller.pauseCamera();
              Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (context) => Banking(
                        selectedAddr: 'address',
                        shopID: _list[i].running,
                        source: 'favorite',
                        contract: _list[i].contract != null
                            ? _list[i].contract
                            : 'no',
                        abi: _list[i].contract != null ? _list[i].abi : 'no',
                        callFunction: _list[i].contract != null
                            ? _list[i].callFunction
                            : 'no')),
              ).then((value) {
                controller.resumeCamera();
              });
              break;
            }
          }
        } else if (scanData.code.toString().substring(0, 2) == '0x') {
          controller.pauseCamera();
          Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(
              builder: (context) => Banking(
                selectedAddr: 'address',
                scanActive: 'active',
                address: scanData.code,
              ),
            ),
            ModalRoute.withName('/bank'),
          );
        } else {
          controller.pauseCamera();
          print('else vending');
          Vending vending = Vending.fromJson(jsonDecode(scanData.code!));
          print(vending);
          if (vending.function == 'transfer_vending') {
            controller.pauseCamera();
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(
                  builder: (context) => confirmTransection(
                      titleName: vending.addr,
                      addressText: vending.addr,
                      amountSend: vending.amount.toString(),
                      rateCurrency: 1,
                      vending: vending,
                      isVending: true,
                      contract: 'no',
                      abi: 'no',
                      callFunction: 'no')),
              // ModalRoute.withName('/confirmTransection'),
            );
          }
        }
      } catch (e) {
        print(e);
        setState(() {
          // _isLoad = false;
        });
      }
    });
  }

  Widget scan() {
    return Positioned(
      top: MediaQuery.of(context).size.height * Screen_util("height", 390),
      child: Container(
        width: MediaQuery.of(context).size.width,
        height: 780.h,
        child: QRView(
          key: qrKey,
          onQRViewCreated: _onQRViewCreated,
        ),
      ),

      // child: Container(
      //   color: Colors.grey.withOpacity(0.3),
      //   height: MediaQuery.of(context).size.height * Screen_util("height", 780),
      //   width: MediaQuery.of(context).size.width * Screen_util("width", 1080),
      // child: QRCaptureView(
      //   key: qr_scan,
      //   controller: _controller,
      // ),
      // child: CameraMlVision<List<Barcode>>(
      //   key: qr_scan,
      //   detector: detector.detectInImage,
      //   resolution: ResolutionPreset.high,
      //   onResult: (barcodes) {
      //     if (barcodes == null ||
      //         barcodes.isEmpty ||
      //         data.contains(barcodes.first.displayValue) ||
      //         !mounted) {
      //       return;
      //     }
      //     setState(() {
      //       data.add(barcodes.first.displayValue);
      //       print(barcodes.first.displayValue);
      //       if (barcodes.first.displayValue.substring(0, 2) == '0x') {
      //         scanCount = 1;
      //         qr_scan.currentState.deactivate();
      //
      //         Navigator.pushAndRemoveUntil(
      //           context,
      //           MaterialPageRoute(
      //             builder: (context) => Banking(
      //               selectedAddr: 'address',
      //               scanActive: 'active',
      //               address: barcodes.first.displayValue.toString(),
      //             ),
      //           ),
      //           ModalRoute.withName('/bank'),
      //         );
      //       }
      //     });
      //   },
      //   onDispose: () {
      //     detector.close();
      //   },
      // ),
//        child: new QrCamera(
//          key: qr_scan,
//          onError: (context, error) => Text(
//            error.toString(),
//            style: TextStyle(color: Colors.red),
//          ),
//          qrCodeCallback: (code) {
//            print(qr_scan);
//            if (scanCount == 0) {
//              //check format address ethereum
//              if (code.substring(0, 2) == '0x') {
//                scanCount = 1;
//                qr_scan.currentState.deactivate();
//
//                Navigator.pushAndRemoveUntil(
//                  context,
//                  MaterialPageRoute(
//                    builder: (context) => Banking(
//                      scanActive: 'active',
//                      address: code.toString(),
//                    ),
//                  ),
//                  ModalRoute.withName('/bank'),
//                );
//              }
//            }
//          },
//        ),
    );
//                     child: SizedBox(
  }

  Widget detail() {
    return Positioned(
      top: MediaQuery.of(context).size.height * Screen_util("height", 1325),
      child: new Container(
          alignment: Alignment.center,
          width: MediaQuery.of(context).size.width * Screen_util("width", 700),
          child: Text(
            AppLocalizations.of(context)!.translate('scanpay_details'),
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Color(0xff707071),
              fontSize: MediaQuery.of(context).size.height *
                  Screen_util("height", 39),
              fontFamily: AppLocalizations.of(context)!.translate('font1Light'),
              letterSpacing: 0.5,
            ),
          )),
    );
  }

  //เส้นเเนวตั้งเมนู
  Widget border() {
    return Container(
      height: MediaQuery.of(context).size.height * Screen_util("height", 125),
      decoration: BoxDecoration(
          border: Border(
        right: BorderSide(
          color: Colors.white,
          width: MediaQuery.of(context).size.width * 0.00185185185,
        ),
      )),
      alignment: Alignment.topCenter,
    );
  }
}
// import 'package:flutter/material.dart';
// import 'package:qrcode_flutter/qrcode_flutter.dart';
// import 'package:image_picker/image_picker.dart';
//
// class scanPay extends StatefulWidget {
//   @override
//   _scanPay createState() => _scanPay();
// }
//
// class _scanPay extends State<scanPay> {
//   @override
//   void initState() {
//     super.initState();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return MaterialApp(
//         home: Scaffold(
//       appBar: AppBar(
//         title: Text("1"),
//       ),
//       body: Builder(
//         builder: (context) => RaisedButton(
//           onPressed: () {
//             Navigator.of(context)
//                 .push(MaterialPageRoute(builder: (_) => MyApp()));
//           },
//         ),
//       ),
//     ));
//   }
// }
//
// class MyApp extends StatefulWidget {
//   @override
//   _MyAppState createState() => _MyAppState();
// }
//
// class _MyAppState extends State<MyApp> with TickerProviderStateMixin {
//   QRCaptureController _controller = QRCaptureController();
//
//   bool _isTorchOn = false;
//
//   String _captureText = '';
//
//   @override
//   void initState() {
//     super.initState();
//
//     _controller.onCapture((data) {
//       print('$data');
//       setState(() {
//         _captureText = data;
//       });
//     });
//   }
//
//   @override
//   void dispose() {
//     super.dispose();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text('scan'),
//         actions: <Widget>[
//           FlatButton(
//             onPressed: () async {
//               PickedFile image =
//                   await ImagePicker().getImage(source: ImageSource.gallery);
//               var qrCodeResult =
//                   await QRCaptureController.getQrCodeByImagePath(image.path);
//               setState(() {
//                 _captureText = qrCodeResult.join('\n');
//               });
//             },
//             child: Text('photoAlbum', style: TextStyle(color: Colors.white)),
//           ),
//         ],
//       ),
//       body: Stack(
//         alignment: Alignment.center,
//         children: <Widget>[
//           Container(
//             width: 300,
//             height: 300,
//             child: QRCaptureView(
//               controller: _controller,
//             ),
//           ),
//           SafeArea(
//             child: Align(
//               alignment: Alignment.bottomCenter,
//               child: _buildToolBar(),
//             ),
//           ),
//           Container(
//             child: Text('$_captureText'),
//           )
//         ],
//       ),
//     );
//   }
//
//   Widget _buildToolBar() {
//     return Row(
//       mainAxisSize: MainAxisSize.max,
//       mainAxisAlignment: MainAxisAlignment.center,
//       children: <Widget>[
//         FlatButton(
//           onPressed: () {
//             _controller.pause();
//           },
//           child: Text('pause'),
//         ),
//         FlatButton(
//           onPressed: () {
//             if (_isTorchOn) {
//               _controller.torchMode = CaptureTorchMode.off;
//             } else {
//               _controller.torchMode = CaptureTorchMode.on;
//             }
//             _isTorchOn = !_isTorchOn;
//           },
//           child: Text('torch'),
//         ),
//         FlatButton(
//           onPressed: () {
//             _controller.resume();
//           },
//           child: Text('resume'),
//         ),
//       ],
//     );
//   }
// }
// import 'dart:io';
//
// import 'package:flutter/foundation.dart';
// import 'package:flutter/material.dart';
// import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
// import 'package:qr_code_scanner/qr_code_scanner.dart';
//
// class scanPay extends StatefulWidget {
//   const scanPay({
//     Key key,
//   }) : super(key: key);
//
//   @override
//   State<StatefulWidget> createState() => _scanPay();
// }
//
// class _scanPay extends State<scanPay> {
//   String qr;
//   GlobalKey key = new GlobalKey();
//   bool _isLoad = false;
//   QRViewController controller;
//   final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
//   int count = 0;
//   String uid;
//   String idCustomer;
//
//   @override
//   initState() {
//     super.initState();
//   }
//
//   @override
//   void dispose() {
//     controller.dispose();
//     super.dispose();
//   }
//
//   DateTime now = new DateTime.now();
//
//   @override
//   Widget build(BuildContext context) {
//     return ModalProgressHUD(
//       inAsyncCall: _isLoad,
//       child: new Scaffold(
//         key: key,
//         body: Container(
//           child: new Center(
//             child: new Column(
//               mainAxisAlignment: MainAxisAlignment.start,
//               children: <Widget>[
//                 new Container(
//                   /// Camera for Scan Barcode
//                   child: Center(
//                     child: SizedBox(
//                       width: MediaQuery.of(context).size.width,
//                       height: 300,
//                       child: QRView(
//                         key: qrKey,
//                         onQRViewCreated: _onQRViewCreated,
//                       ),
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//           ),
//         ),
//       ),
//     );
//   }
//
//   void _onQRViewCreated(QRViewController controller) async {
//     this.controller = controller;
//     controller.scannedDataStream.listen((scanData) async {
//       try {
//         if (!mounted) return;
//         // UniqueCode code = UniqueCode.fromJson(convert
//         //     .jsonDecode(scanData.replaceAll('https://digibaht.com/', '')));
//         print(scanData);
//         print('dfdf');
//         // controller.pauseCamera();
//         // await sendCode(code);
//       } catch (e) {
//         print(e);
//         setState(() {
//           _isLoad = false;
//         });
//       }
//     });
//   }
// }
