import 'dart:ui';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'dart:async';
//import 'User.dart';
//import 'Services.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:likewallet/Theme.dart';
import 'package:likewallet/app_config.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../screen_util.dart';

import 'package:likewallet/libraryman/custom_loading.dart';

import 'package:likewallet/libraryman/auth.dart';

class Services {
//  static const String url = 'https://jsonplaceholder.typicode.com/users';

  static Future<List<User>> getUsers() async {
    try {
//      final response = await http.get(url);
      var url = Uri.https(env.apiUrl, '/listQucikpayShop');
      var response = await http.post(url, body: {
        "apiKey": env.APIKEY,
        "secretKey": env.SECRETKEY,
      });

      print(response);
      if (response.statusCode == 200) {
        List<User> list = parseUsers(response.body);
        return list;
      } else {
        throw Exception("Error");
      }
    } catch (e) {
      print(e.toString());
      throw Exception(e.toString());
    }
  }

  static List<User> parseUsers(String responseBody) {
    final parsed =
        json.decode(responseBody)["result"].cast<Map<String, dynamic>>();
    return parsed.map<User>((json) => User.fromJson(json)).toList();
  }
}

class UserFilterDemo extends StatefulWidget {
  UserFilterDemo() : super();

  @override
  UserFilterDemoState createState() => UserFilterDemoState();
}

class Debouncer {
  final int? milliseconds;
  late VoidCallback action;
  late Timer _timer;

  Debouncer({this.milliseconds});

  run(VoidCallback action) {
    if (null != _timer) {
      _timer.cancel();
    }
    _timer = Timer(Duration(milliseconds: milliseconds!), action);
  }
}

class UserFilterDemoState extends State<UserFilterDemo> {
  // https://jsonplaceholder.typicode.com/users

  final _debouncer = Debouncer(milliseconds: 500);
  // List<User> users = List();
  // List<User> filteredUsers = List();
  // List<String> favorite = List<String>();
  List<User> users = [];
  List<User> filteredUsers = [];
  List<String> favorite = [];
  late SharedPreferences pref;
  late BaseAuth auth;
  late String uid;

  @override
  void initState() {
    super.initState();
    print('lo');
    auth = Auth();
    setPref();
    Services.getUsers().then((usersFromServer) {
      print('something wrong !');
      setState(() {
        users = usersFromServer;
        filteredUsers = users;
      });
    });
  }

  void setPref() async {
    pref = await SharedPreferences.getInstance();
    favorite = pref.getStringList('shopFavorite') ?? [];
    print(favorite);
  }

  var tmpArray = [];

  bool saveFavorite(save) {
    pref.setStringList('shopFavorite', save);
    print(save);
    auth.getCurrentUser().then((decodeToken) {
      // print(decodeToken);
      uid = decodeToken!.uid;
      FirebaseFirestore.instance
          .collection('favoriteQuickpay')
          .doc(decodeToken.uid)
          .set({'favorite': save});
    });
    return true;
  }

  getCheckboxItems() {
//    filteredUsers.forEach((key, value) {
//      if (value == true) {
//        tmpArray.add(key);
//      }
//    });
  }

  bool isCheck = false;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: LikeWalletAppTheme.bule2_9,
      body: Stack(
        alignment: Alignment.center,
        children: <Widget>[
          Positioned(
              top: MediaQuery.of(context).size.height *
                  Screen_util("height", 139.33),
              child: GestureDetector(
                child: new Container(
                  alignment: Alignment.centerLeft,
                  width: MediaQuery.of(context).size.width,
                  child: new IconButton(
                    icon: new Icon(
                      Icons.arrow_back_ios,
                      size: MediaQuery.of(context).size.height *
                          Screen_util("height", 44.47),
                    ),
                    color: Color(0xff707071),
                    onPressed: () => {Navigator.of(context).pop()},
                  ),
                ),
              )),
          Positioned(
              top: MediaQuery.of(context).size.height *
                  Screen_util("height", 175),
              child: Row(
                children: <Widget>[
                  new Icon(
                    Icons.search,
                    color: LikeWalletAppTheme.white.withOpacity(0.5),
                    size: MediaQuery.of(context).size.height *
                        Screen_util("height", 48),
                  ),
                  Container(
                    padding: EdgeInsets.only(
                      left: MediaQuery.of(context).size.width *
                          Screen_util("width", 20),
                    ),
                    width: MediaQuery.of(context).size.width *
                        Screen_util("width", 350),
                    child: TextField(
                      style: TextStyle(
                        color: LikeWalletAppTheme.white.withOpacity(0.5),
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                        fontStyle: FontStyle.normal,
                        fontWeight: FontWeight.w500,
                        fontSize: MediaQuery.of(context).size.height *
                            Screen_util("height", 36),
                      ),
                      decoration: InputDecoration(
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.all(0.0),
                        hintText: 'SEARCH STORES',
                        hintStyle: TextStyle(
                          color: LikeWalletAppTheme.white.withOpacity(0.5),
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font1'),
                          fontStyle: FontStyle.normal,
                          fontWeight: FontWeight.w100,
                          fontSize: MediaQuery.of(context).size.height *
                              Screen_util("height", 36),
                        ),
                      ),
                      onChanged: (string) {
                        _debouncer.run(() {
                          setState(() {
                            filteredUsers = users
                                .where((u) => (u.title
                                        .toString()
                                        .toLowerCase()
                                        .contains(string.toLowerCase()) ||
                                    u.details
                                        .toString()
                                        .toLowerCase()
                                        .contains(string.toLowerCase())))
                                .toList();
                          });
                        });
                      },
                    ),
                  )
                ],
              )),
          Container(
            padding: EdgeInsets.only(
              top: MediaQuery.of(context).size.height *
                  Screen_util("height", 334),
            ),
            child: ListView.builder(
              padding: EdgeInsets.all(10.0),
              itemCount: filteredUsers.length,
              itemBuilder: (BuildContext context, int index) {
                return Container(
                  padding: EdgeInsets.symmetric(
                      vertical: mediaQuery(context, 'height', 20)),
                  child: Row(
                    children: <Widget>[
                      Container(
                        height: MediaQuery.of(context).size.height *
                            Screen_util("height", 247),
                        child: CachedNetworkImage(
                          placeholder: (context, url) => SizedBox(
                            height: mediaQuery(context, 'height', 200),
                            width: mediaQuery(context, 'height', 200),
                            child: CustomLoading(),
                          ),
                          imageUrl: filteredUsers[index].logo ?? '',
                        ),
                      ),
                      SizedBox(
                        width: mediaQuery(context, 'width', 49),
                      ),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            Text(
                              filteredUsers[index].title.toString(),
                              style: TextStyle(
                                color:
                                    LikeWalletAppTheme.white.withOpacity(0.4),
                                fontFamily: AppLocalizations.of(context)!
                                    .translate('font1'),
                                fontStyle: FontStyle.normal,
                                fontWeight: FontWeight.w500,
                                fontSize: MediaQuery.of(context).size.height *
                                    Screen_util("height", 33),
                              ),
                            ),
                            Text(
                              filteredUsers[index].details.toString(),
                              style: TextStyle(
                                color:
                                    LikeWalletAppTheme.white.withOpacity(0.4),
                                fontFamily: AppLocalizations.of(context)!
                                    .translate('font1'),
                                fontStyle: FontStyle.normal,
                                fontWeight: FontWeight.w200,
                                fontSize: MediaQuery.of(context).size.height *
                                    Screen_util("height", 33),
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(
                        width: mediaQuery(context, 'width', 49),
                      ),
                      Container(
                        height: MediaQuery.of(context).size.height *
                            Screen_util("height", 107),
                        width: MediaQuery.of(context).size.width *
                            Screen_util("width", 107),
                        child: favorite.contains(
                                    filteredUsers[index].running.toString()) ==
                                false
                            ? GestureDetector(
                                onTap: () {
                                  print(filteredUsers[index].running);

                                  setState(() {
                                    favorite.add(filteredUsers[index]
                                        .running
                                        .toString());
                                  });

                                  saveFavorite(favorite);
                                },
                                child: Image.asset(
                                    'assets/image/add_favorites.png'))
                            : GestureDetector(
                                onTap: () {
                                  setState(() {
                                    favorite.remove(filteredUsers[index]
                                        .running
                                        .toString());
                                  });
                                  saveFavorite(favorite);
                                },
                                child:
                                    Image.asset('assets/image/favorited.png'),
                              ),
//                      Image.network(
//                        filteredUsers[index].logo,
//                      ),
                      ),

//                    Checkbox(
//                        value: filteredUsers[index].isSelected,
//                        onChanged: (bool value) {
//                          setState(() {
//                            filteredUsers[index].isSelected = value;
//                            print(filteredUsers[index].isSelected);
//                          });
//                        }
//                        )
                    ],
                  ),
                );
//
              },
            ),
          ),
        ],
      ),
    );
  }
}

class User {
//  int id;
  String? logo;
  int? running;
  String? title;
  String? details;
  bool? isCheck;
  User({this.logo, this.running, this.title, this.details, this.isCheck});

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      logo: json["logo"] as String,
      running: json["running"] as int,
      title: json["title"] as String,
      details: json["details"] as String,
      isCheck: json["isCheck"] as bool,
//      email: json["email"] as String,
    );
  }
}
