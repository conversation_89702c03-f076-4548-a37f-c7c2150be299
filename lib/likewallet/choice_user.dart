//import 'package:flutter/material.dart';
//class ChoiceUser extends StatefulWidget {
//  State<StatefulWidget> createState() => new _ChoiceUser();
//}
//
//class _ChoiceUser extends State<ChoiceUser> {
//  @override
//  Widget build(BuildContext context) {
//    // TODO: implement build
//    return Scaffold(
//      backgroundColor: Color(0xff150E23),
//      body: new Stack(
//        alignment: Alignment.center,
//        children: <Widget>[
//          Positioned(
//            top: MediaQuery.of(context).size.height/4,
//            child: new Column(
//              mainAxisAlignment: MainAxisAlignment.center,
//              children: <Widget>[
//                new Text('Anyone can use this', style: TextStyle(color: Color(0xff6C6B6D), fontSize: 16),),
//                new Text('easy version of LIKE Wallet', style: TextStyle(color: Color(0xff6C6B6D), fontSize: 16),),
//                Padding(padding: EdgeInsets.only(top:30),
//                child: ButtonTheme(
//                  minWidth: MediaQuery.of(context).size.width/1.2,
//                  height: 110,
//                  child:new FlatButton(
//                      shape: new RoundedRectangleBorder(
//                        borderRadius: new BorderRadius.circular(8.0),
//                      ),
//                      disabledColor: Color(0xff00F1E0),
//                      color: Color(0xff00F1E0),
//                      onPressed: () => {
//                        Navigator.push(
//                          context,
//                          MaterialPageRoute(builder: (context) => HomeLikewallet()),
//                        ),
//                      },
//                      child: new Text('LIKE Wallet', style: TextStyle(color: Colors.black, fontSize: 20, fontWeight: FontWeight.bold),)
//                  ),
//                ),
//                ),
//                Padding(
//                  padding: EdgeInsets.only(top: MediaQuery.of(context).size.height/10, bottom: 30),
//                  child: ButtonTheme(
//                    minWidth: MediaQuery.of(context).size.width/1.2,
//                    height: 110,
//                    child:new FlatButton(
//                        shape: new RoundedRectangleBorder(
//                          borderRadius: new BorderRadius.circular(8.0),
//                        ),
//                        disabledColor: Color(0xffA4FD00),
//                        color: Color(0xffA4FD00),
//                        onPressed: () => {
//                          Navigator.push(
//                            context,
//                            MaterialPageRoute(builder: (context) => HomeLikewallet()),
//                          ),
//                        },
//                        child: new Text('LIKE Wallet Pro', style: TextStyle(color: Colors.black, fontSize: 20, fontWeight: FontWeight.bold),)
//                    ),
//                  ),
//                ),
//                new Text('LIKE Wallet for professional', style: TextStyle(color: Color(0xff6C6B6D), fontSize: 16),),
//                new Text('crypto users', style: TextStyle(color: Color(0xff6C6B6D), fontSize: 16),),
//              ],
//            )
//          )
//        ],
//      )
//    );
//  }
//
//}