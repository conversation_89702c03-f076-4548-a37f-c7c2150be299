//import 'package:flutter/material.dart';
//import 'package:flutter_likewallet/icon/my_flutter_app_icons.dart' as CustomIcon;
//import 'package:flutter_likewallet/icon/icon_home.dart' as IconsHome;
//import 'package:flutter_likewallet/icon/mining_icons.dart' as IconsMining;
//import 'package:flutter_likewallet/icon/spendlike_icons.dart' as IconsSpend;
//import 'package:flutter_likewallet/likewallet/swap.dart';
//class HomeLikewallet extends StatefulWidget {
//
//  Likewallet createState() => new Likewallet();
//}
//
//class Likewallet extends State<HomeLikewallet> {
//
//
//  int tabMenu = 1;
//
//  @override
//  void initState() {
//    // TODO: implement initState
//    super.initState();
//
//  }
//
//  changeMenu(_number){
//    setState(() {
//      tabMenu = _number;
//    });
//  }
//  @override
//  Widget build(BuildContext context) {
//    // TODO: implement build
//    return Scaffold(
//        drawer: Drawer(
//          // Add a ListView to the drawer. This ensures the user can scroll
//          // through the options in the drawer if there isn't enough vertical
//          // space to fit everything.
//          child: Container(
//            decoration: BoxDecoration(
//              color: Color(0xff150E23)
//            ),
//            child: ListView(
//              // Important: Remove any padding from the ListView.
//              padding: EdgeInsets.zero,
//              children: <Widget>[
//                DrawerHeader(
//                  margin: EdgeInsets.only(bottom: 0),
//                  child: Align(
//                    alignment: Alignment.bottomLeft,
//                    child: Text('BASIC', style: TextStyle(color: Color(0xff6C6B6D), fontSize: 24.0),),
//                  ),
//                  decoration: BoxDecoration(
//                    color: Colors.black,
//                  ),
//                ),
//                ListTile(
//                  title: Text('Subscribe to PREMIUM', style: TextStyle(color: Color(0xff6C6B6D)),),
//                  onTap: () {
//                    // Update the state of the app.
//                    // ...
//                  },
//                ),
//                ListTile(
//                  title: Text('Language', style: TextStyle(color: Color(0xff6C6B6D)),),
//                  onTap: () {
//                    // Update the state of the app.
//                    // ...
//                  },
//                ),
//                ListTile(
//                  title: new Row(
//                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                    children: <Widget>[
//                      Text('Use Touch ID / Face ID', style: TextStyle(color: Color(0xff6C6B6D)),),
//                      Transform.scale(
//                        scale: 1.4,
//                        child: Switch(
//                            value: true,
//                            onChanged: (value) {
//                              setState(() {
//
//                              });
//                            },
//                            activeTrackColor: Color(0xff929194),
//                            activeColor: Colors.white,
//    //                        inactiveThumbColor: Color(0xff929194),
//                            inactiveTrackColor: Color(0xff929194),
//                          ),
//                        )
//                    ],
//                  ),
//                  onTap: () {
//                    // Update the state of the app.
//                    // ...
//                  },
//                ),
//                ListTile(
//                  title: new Row(
//                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                    children: <Widget>[
//                      Text('Profile', style: TextStyle(color: Color(0xff6C6B6D)),),
//                      Transform.scale(
//                        scale: 1.4,
//                        child: Switch(
//                          value: true,
//                          onChanged: (value) {
//                            setState(() {
//
//                            });
//                          },
//                          activeTrackColor: Color(0xff929194),
//                          activeColor: Colors.white,
//                          //                        inactiveThumbColor: Color(0xff929194),
//                          inactiveTrackColor: Color(0xff929194),
//                        ),
//                      )
//                    ],
//                  ),
//                  onTap: () {
//                    // Update the state of the app.
//                    // ...
//                  },
//                ),
//                ListTile(
//                  title: new Row(
//                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                    children: <Widget>[
//                      Text('Notifications', style: TextStyle(color: Color(0xff6C6B6D)),),
//                      Transform.scale(
//                        scale: 1.4,
//                        child: Switch(
//                          value: true,
//                          onChanged: (value) {
//                            setState(() {
//
//                            });
//                          },
//                          activeTrackColor: Color(0xff929194),
//                          activeColor: Colors.white,
//                          //                        inactiveThumbColor: Color(0xff929194),
//                          inactiveTrackColor: Color(0xff929194),
//                        ),
//                      )
//                    ],
//                  ),
//                  onTap: () {
//                    // Update the state of the app.
//                    // ...
//                  },
//                ),
//                ListTile(
//                  title: Text('Contact Us', style: TextStyle(color: Color(0xff6C6B6D)),),
//                  onTap: () {
//                    // Update the state of the app.
//                    // ...
//                  },
//                ),
//                ListTile(
//                  title: Text('Log Out', style: TextStyle(color: Color(0xff6C6B6D)),),
//                  onTap: () {
//                    // Update the state of the app.
//                    // ...
//                  },
//                ),
//                Column(
//                  children: <Widget>[
//                    SizedBox(
//                      width: 250,
//                      child:  Divider(
//                        color: Color(0xff6C6B6D),
//
//                      ),
//                    )
//                  ],
//                ),
//                ListTile(
//                  title: Text('JOIN COMMUNITY', style: TextStyle(color: Color(0xff6C6B6D)),),
//                  onTap: () {
//                    // Update the state of the app.
//                    // ...
//                  },
//                ),
//                ListTile(
//                  title: new Row(
//                    children: <Widget>[
//                      Text('LINE', style: TextStyle(color: Color(0xff6C6B6D)),),
//                      Padding(
//                        padding: EdgeInsets.only(left: 10),
//                        child: Image(image: AssetImage('assets/image/LINE@ Logo64.png'), width: 32,),
//                      )
//                    ],
//                  ),
//                  onTap: () {
//                    // Update the state of the app.
//                    // ...
//                  },
//                ),
//                ListTile(
//                  title: new Row(
//                    children: <Widget>[
//                      Text('Facebook', style: TextStyle(color: Color(0xff6C6B6D)),),
//                      Padding(
//                        padding: EdgeInsets.only(left: 10),
//                        child: Image(image: AssetImage('assets/image/FB Logo64.png'), width: 32,),
//                      )
//                    ],
//                  ),
//                  onTap: () {
//                    // Update the state of the app.
//                    // ...
//                  },
//                ),
//                ListTile(
//                  title: new Row(
//                    children: <Widget>[
//                      Text('WeChat', style: TextStyle(color: Color(0xff6C6B6D)),),
//                      Padding(
//                        padding: EdgeInsets.only(left: 10),
//                        child: Image(image: AssetImage('assets/image/wechat64.png'), width: 32,),
//                      )
//                    ],
//                  ),
//                ),
//                ListTile(
//                  title: new Row(
//                    children: <Widget>[
//                      Text('WhatsApp', style: TextStyle(color: Color(0xff6C6B6D)),),
//                      Padding(
//                        padding: EdgeInsets.only(left: 10),
//                        child: Image(image: AssetImage('assets/image/whatsapp.png'), width: 32,),
//                      )
//                    ],
//                  ),
//                ),
//                ListTile(
//                  title: new Row(
//                    children: <Widget>[
//                      Text('Twitter', style: TextStyle(color: Color(0xff6C6B6D)),),
//                      Padding(
//                        padding: EdgeInsets.only(left: 10),
//                        child: Image(image: AssetImage('assets/image/twitter64.png'), width: 32,),
//                      )
//                    ],
//                  ),
//                ),
//                ListTile(
//                  title: new Row(
//                    children: <Widget>[
//                      Text('Telegram', style: TextStyle(color: Color(0xff6C6B6D)),),
//                      Padding(
//                        padding: EdgeInsets.only(left: 10),
//                        child: Image(image: AssetImage('assets/image/icons8-telegram-app64.png'), width: 32,),
//                      )
//                    ],
//                  ),
//                ),
//                ListTile(
//                  title: new Row(
//                    children: <Widget>[
//                      Text('Reddit', style: TextStyle(color: Color(0xff6C6B6D)),),
//                      Padding(
//                        padding: EdgeInsets.only(left: 10),
//                        child: Image(image: AssetImage('assets/image/reddit-social-logo-character64.png'), color: Colors.red, width: 32,),
//                      )
//                    ],
//                  ),
//                ),
//                ListTile(
//                  title: new Row(
//                    children: <Widget>[
//                      Text('Youtube', style: TextStyle(color: Color(0xff6C6B6D)),),
//                      Padding(
//                        padding: EdgeInsets.only(left: 10),
//                        child: Image(image: AssetImage('assets/image/youtube64.png'), width: 32,),
//                      )
//                    ],
//                  ),
//                ),
//                Column(
//                  children: <Widget>[
//                    SizedBox(
//                      width: 250,
//                      child:  Divider(
//                        color: Color(0xff6C6B6D),
//
//                      ),
//                    )
//                  ],
//                ),
//                ListTile(
//                  title: Text('Help Center', style: TextStyle(color: Color(0xff6C6B6D)),),
//                  onTap: () {
//                    // Update the state of the app.
//                    // ...
//                  },
//                ),
//                ListTile(
//                  title: Text('Share With Friends', style: TextStyle(color: Color(0xff6C6B6D)),),
//                  onTap: () {
//                    // Update the state of the app.
//                    // ...
//                  },
//                ),
//                ListTile(
//                  title: Text('Privacy/Terms of Service', style: TextStyle(color: Color(0xff6C6B6D)),),
//                  onTap: () {
//                    // Update the state of the app.
//                    // ...
//                  },
//                ),
//                ListTile(
//                  title: new Row(
//                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                    children: <Widget>[
//                      Text('Version', style: TextStyle(color: Color(0xff6C6B6D)),),
//                      Text('1.0', style: TextStyle(color: Color(0xff6C6B6D)),)
//                    ],
//                  ),
//                  onTap: () {
//                    // Update the state of the app.
//                    // ...
//                  },
//                ),
//              ],
//            ),
//          )
//        ),
//        bottomNavigationBar: BottomNavigationBar(
//          backgroundColor: Colors.white,
//          type: BottomNavigationBarType.fixed,
//          currentIndex: 0, // this will be set when a new tab is tapped
//          items: [
//            BottomNavigationBarItem(
//              icon: new Icon(CustomIcon.MyFlutterApp.home, size: 22, color: Color(0xff6C6B6D),),
//              title: new Text('Home', style: TextStyle(color: Color(0xff6C6B6D),),),
//            ),
//            BottomNavigationBarItem(
//              icon: new Icon(CustomIcon.MyFlutterApp.comment, size: 22 , color: Color(0xff6C6B6D),),
//              title: new Text('Messages', style: TextStyle(color: Color(0xff6C6B6D),),),
//            ),
//            BottomNavigationBarItem(
//                icon: Icon(CustomIcon.MyFlutterApp.refer, size: 46, color: Color(0xff6C6B6D),),
//                title: Text('')
//            ),
//            BottomNavigationBarItem(
//                icon: Icon(CustomIcon.MyFlutterApp.refresh, size: 22, color: Color(0xff6C6B6D),),
//                title: Text('History' , style: TextStyle(color: Color(0xff6C6B6D),),)
//            ),
//            BottomNavigationBarItem(
//                icon: Padding(
//                  padding: EdgeInsets.only(top: 16.0, right: 76.0),
//                  child: Icon(CustomIcon.MyFlutterApp.lendex, size: 10, color: Color(0xff6C6B6D),),
//                ),
//                title: Text('')
//            )
//          ],
//        ),
//      body: new Container(
//        decoration: BoxDecoration(
//          gradient: LinearGradient(
//            begin: Alignment.topRight,
//            end: Alignment.bottomLeft,
//            stops: [0.2, 0.3, 0.4,0.6,0.7],
//            colors: [
//            // Colors are easy thanks to Flutter's Colors class.
//              Color(0xff111112),
//            Color(0xff111112).withOpacity(0.9),
//            Color(0xff111112).withOpacity(0.85),
//              Color(0xff111112).withOpacity(0.8),
//              Color(0xff111112).withOpacity(0.75)
//            ],
//          )
//        ),
//        child:  new Column(
////          mainAxisSize: MainAxisSize.max,
//          children: <Widget>[
//           new AppBar(
//              title: Align(
//                alignment: Alignment.centerRight,
//                child: new Text('34.0/USD', style: TextStyle(color: Color(0xff6C6B6D), fontSize: 14.0, fontWeight: FontWeight.bold),),
//              ),
//              iconTheme: IconThemeData(
//                color: Color(0xff6C6B6D)
//              ),
//              backgroundColor: Colors.transparent,
//              elevation: 0,
//            ),
////             _title(),
//            _balance(context),
//            _wallet(),
//            _name(context),
//            if(tabMenu==1)
//              _earnMenu(context)
//            else if(tabMenu ==2)
//              _SpendLike(context)
//            else if(tabMenu == 3)
//                _InvestLike(context)
////            _footer(context)
//
//          ],
//        ),
//      )
//    );
//  }
//  Widget _earnMenu(context){
//    return new Stack(
//      alignment: Alignment.topLeft,
//      clipBehavior: Clip.none,
//      fit: StackFit.passthrough,
//      children: <Widget>[
//        Positioned(
//          bottom: -80,
//          child: Container(
//              alignment: Alignment.topLeft,
//              color: Colors.white,
//              height: MediaQuery.of(context).size.height/4,
//              width: MediaQuery.of(context).size.width,
//              child: new Padding(
//                padding: EdgeInsets.only(top:MediaQuery.of(context).size.height/35, left: 30.0),
////                child:  new Text('EARN \nSPEND\nINVEST\nCRYPTO/\nFIAT', style: TextStyle(color: Color(0xff6C6B6D)),),
//                child:  GestureDetector(
//                  onTap: () => {
//                    Navigator.push(
//                      context,
//                      MaterialPageRoute(builder: (context) => SwapCoin()),
//                    ),
//                  },
//                  child: Image.asset('assets/image/Swap LKE PKC.png', scale: 8, color: Color(0xff6C6B6D),),
//                ),
//              )
//          ),
//        ),
//        Align(
//            alignment: Alignment.topLeft,
//            child: _LeftMenu()
//        ),
//
//        Align(
//          alignment: Alignment.topRight,
//          child:  new Container(
//            child: new Padding(
//              padding: EdgeInsets.only(top: MediaQuery.of(context).size.height/50, left: MediaQuery.of(context).size.width/17),
//              child: new Container(
//                height: MediaQuery.of(context).size.height/1.7,
//                width: MediaQuery.of(context).size.width/1.35,
//                decoration:  BoxDecoration(
//                  gradient: LinearGradient(
//                    begin: Alignment.topCenter,
//                    end: Alignment.bottomCenter,
//                    stops: [1],
//                    colors: [
//                      // Colors are easy thanks to Flutter's Colors class.
//                      Colors.cyan
//                    ],
//                  ),
//
//                ),
//                child: new Column(
//                  children: <Widget>[
//                    new Row(
//                      children: <Widget>[
//                        Padding(
//                          padding: EdgeInsets.only(top:30.0, left:60.0),
//                          child: new Column(
//                            children: <Widget>[
//                              new Icon(IconsHome.MyFlutterApp.save_money, size: 52,),
//                              new Text('LIKE recieve'),
//                            ],
//                          ),
//                        ),
//                        Padding(
//                          padding: EdgeInsets.only(top:30.0, left:42.0),
//                          child: new Column(
//                            children: <Widget>[
////                              new Icon(IconsHome.MyFlutterApp.buy_like, size: 52),
//                              new Image.asset('assets/image/Buy LIKE.png', scale: 8.5,),
//                              new Text('Buy LIKE'),
//                            ],
//                          ),
//                        ),
//                      ],
//                    ),
//                    new Row(
//                      children: <Widget>[
//                        Padding(
//                          padding: EdgeInsets.only(top:30.0, left:60.0),
//                          child: new Column(
//                            children: <Widget>[
//                              new Icon(IconsMining.Mining.like_mining, size: 52),
//                              new Text('LIKE Mining'),
//                            ],
//                          ),
//                        ),
//                        Padding(
//                          padding: EdgeInsets.only(top:30.0, left:42.0),
//                          child: new Column(
//                            children: <Widget>[
//                              new Icon(IconsHome.MyFlutterApp.lock_earn, size: 52),
//                              new Text('LIKE Lock \nEarn More')
//                            ],
//                          ),
//                        ),
//
//
//                      ],
//                    ),
//                    new Row(
//                      children: <Widget>[
//                        Padding(
//                          padding: EdgeInsets.only(top:30.0, left:60.0),
//                          child: new Column(
//                            children: <Widget>[
//                              new Icon(IconsHome.MyFlutterApp.joystick, size: 52),
//                              new Text('Play And Earn'),
//                            ],
//                          ),
//                        ),
//                        Padding(
//                          padding: EdgeInsets.only(top:30.0, left:42.0),
//                          child: new Column(
//                            children: <Widget>[
////                              new Icon(IconsHome.MyFlutterApp.loading, size: 52),
//                              new Image.asset('assets/image/Group 1898.png',scale: 0.9,),
//                              new Text('LDX')
//                            ],
//                          ),
//                        ),
//
//                      ],
//                    ),
//                    new Row(
//                      children: <Widget>[
//                        Padding(
//                          padding: EdgeInsets.only(top:30.0, left:64.0),
//                          child: new Column(
//                            children: <Widget>[
////                              new Icon(IconsHome.MyFlutterApp.reward, size: 52),
//                              new Image.asset('assets/image/partners.png', scale: 8.5,),
//                              new Text('Partnership'),
//                            ],
//                          ),
//                        ),
//                        Padding(
//                          padding: EdgeInsets.only(top:30.0, left:52.0),
//                          child: new Column(
//                            children: <Widget>[
//                              new Icon(IconsHome.MyFlutterApp.reward, size: 52),
////                              new Image.asset('assets/image/Group 1898.png',scale: 0.9,),
//                              new Text('Rewards')
//                            ],
//                          ),
//                        ),
//
//                      ],
//                    )
//                  ],
//                ),
//              ),
//            ),
//          ),
//        ),
//        Positioned(
//          left: 85,
//          child: Container(
//            child: new Padding(
//              padding: EdgeInsets.only(top: MediaQuery.of(context).size.height/8),
//              child: new CustomPaint(
//                foregroundPainter: ChatBubbleTriangle(),
//                child: null,
//              ),
//            ),
//          ),
//        ),
//        Positioned(
//          bottom: -35,
//          right: 20,
//          child: Container(
//            decoration: BoxDecoration(
//                gradient: LinearGradient(
//                    begin: Alignment.topRight,
//                    end: Alignment.bottomLeft,
//                    stops: [0.2, 0.3, 0.4,0.6,0.7],
//                    colors: [
//                      Color(0xff150e23),
//                      Color(0xff150e23),
//                      Color(0xff150e23),
//                      Color(0xff150e23),
//                      Color(0xff150e23),
//                    ]
//                ),
//                borderRadius: BorderRadius.all(Radius.circular(8))
//            ),
//            height: 70,
//            width: 120,
//            child: new Padding(
//              padding: EdgeInsets.only(top: 28, left: 26),
//              child: new Text('LIKE PAY', style: TextStyle(color: Color(0xff90c707), fontWeight: FontWeight.bold),),
//            ),
//
//          ),
//        ),
//
//
//
////    ],
////  )
//      ],
//    );
//
//  }
//  Widget _LeftMenu() {
//    return new Container(
//      child: new Column(
//        children: <Widget>[
//          new Padding(
//              padding: EdgeInsets.only(top:MediaQuery.of(context).size.height/10, left: 30.0),
//              child: new Column(
//                crossAxisAlignment: CrossAxisAlignment.start,
//                textDirection: TextDirection.ltr,
//                children: <Widget>[
//                  new GestureDetector(
//                    onTap: () => {
//                      changeMenu(1)
//                    },
//                    child: new Text('EARN\nLIKE', style: TextStyle(color: Color(0xff6C6B6D)),),
//
//                  )
//                ],
//              )
//          ),
//          new Padding(
//              padding: EdgeInsets.only(top:MediaQuery.of(context).size.height/10, left: 30.0),
//              child: new Column(
//                crossAxisAlignment: CrossAxisAlignment.start,
//                textDirection: TextDirection.ltr,
//                children: <Widget>[
//                  new GestureDetector(
//                    onTap: () => {
//                      changeMenu(2)
//                    },
//                    child: new Text('SPEND\nLIKE', style: TextStyle(color: Color(0xff6C6B6D)),),
//                  )
//                ],
//              )
//          ),
//          new Padding(
//              padding: EdgeInsets.only(top:MediaQuery.of(context).size.height/10, left: 30.0),
//              child: new Column(
//                crossAxisAlignment: CrossAxisAlignment.start,
//                textDirection: TextDirection.ltr,
//                children: <Widget>[
//                  new GestureDetector(
//                    onTap: () => {
//                      changeMenu(3)
//                    },
//                    child: new Text('INVEST\nLIKE', style: TextStyle(color: Color(0xff6C6B6D)),),
//                  )
//
//                ],
//              )
//          ),
//
//
//        ],
//      ),
//    );
//  }
//
//  Widget _InvestLike(context){
//    return new Stack(
//      alignment: Alignment.topLeft,
//clipBehavior: Clip.none,
//      fit: StackFit.passthrough,
//      textDirection: TextDirection.ltr,
//      children: <Widget>[
//        Positioned(
//          bottom: -80,
//          child: Container(
//            alignment: Alignment.topLeft,
//              color: Colors.white,
//              height: MediaQuery.of(context).size.height/4,
//              width: MediaQuery.of(context).size.width,
//              child: new Padding(
//                padding: EdgeInsets.only(top:MediaQuery.of(context).size.height/35, left: 30.0),
////                child: new Text('EARN \nSPEND\nINVEST\nCRYPTO/\nFIAT', style: TextStyle(color: Color(0xff6C6B6D)),),
//                  child: Image.asset('assets/image/Swap LKE PKC.png', scale: 8, color: Color(0xff6C6B6D),)
//              )
//          ),
//        ),
//        Align(
//            alignment: Alignment.topLeft,
//            child: _LeftMenu()
//        ),
//
//        Align(
//          alignment: Alignment.topRight,
//          child:  new Container(
//            child: new Padding(
//              padding: EdgeInsets.only(top: MediaQuery.of(context).size.height/50, left: MediaQuery.of(context).size.width/17),
//              child: new Container(
//                height: MediaQuery.of(context).size.height/1.7,
//                width: MediaQuery.of(context).size.width/1.35,
//                decoration:  BoxDecoration(
//                  gradient: LinearGradient(
//                    begin: Alignment.topCenter,
//                    end: Alignment.bottomCenter,
//                    stops: [1],
//                    colors: [
//                      // Colors are easy thanks to Flutter's Colors class.
//                      Colors.cyan
//                    ],
//                  ),
//
//                ),
//                child: new Column(
//
//                  children: <Widget>[
//                    new Row(
//
//                      children: <Widget>[
//                        new Padding(padding: EdgeInsets.only(top: 30, left: 45),
//                          child:   new Text('Super easy invest your LIKE.\nNo need to have experiences in\ninvestment. Just a few clicks\nand we will do the rest. Sit back\nand see your LIKE increase.\n\n Sound good. I\'d like to', style: TextStyle(color: Color(0xff6C6B6D)),),
//                        )
//                      ],
//                    ),
//                    new Row(
//                      children: <Widget>[
//                        new Padding(
//                          padding: EdgeInsets.only(top: 30, left: 45),
//                          child: new Container(
//                            height: 40,
//                            width: 40,
//                            decoration: BoxDecoration(
//                              shape: BoxShape.circle,
//                              color: Colors.white
//                            ),
//                          ),
//                        ),
//                        new Padding(
//                          padding: EdgeInsets.only(top: 30, left: 10),
//                          child: new Column(
//                            mainAxisAlignment: MainAxisAlignment.start,
//                            crossAxisAlignment: CrossAxisAlignment.start,
//                            children: <Widget>[
//                              new Text('invest with', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),),
//                              new Text('LIKEWallet', style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),),
//                            ],
//                          )
//                        ),
//                      ],
//                    ),
//                    new Row(
//                      children: <Widget>[
//                        new Padding(
//                          padding: EdgeInsets.only(top: 30, left: 45),
//                          child: new Container(
//                            height: 40,
//                            width: 40,
//                            decoration: BoxDecoration(
//                                shape: BoxShape.circle,
//                                color: Colors.white
//                            ),
//                          ),
//                        ),
//                        new Padding(
//                            padding: EdgeInsets.only(top: 30, left: 10),
//                            child: new Column(
//                              mainAxisAlignment: MainAxisAlignment.start,
//                              crossAxisAlignment: CrossAxisAlignment.start,
//                              children: <Widget>[
//                                new Text('invest with', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),),
//                                new Text('LENDEX', style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),),
//                              ],
//                            )
//                        ),
//                      ],
//                    ),
//
//                  ],
//                ),
//              ),
//            ),
//          ),
//        ),
//        Positioned(
//          left: 85,
//          child: Container(
//            child: new Padding(
//              padding: EdgeInsets.only(top: MediaQuery.of(context).size.height/2.4),
//              child: new CustomPaint(
//                foregroundPainter: ChatBubbleTriangle(),
//                child: null,
//              ),
//            ),
//          ),
//        ),
//        Positioned(
//          bottom: -35,
//          right: 20,
//          child: Container(
//            decoration: BoxDecoration(
//                gradient: LinearGradient(
//                    begin: Alignment.topRight,
//                    end: Alignment.bottomLeft,
//                    stops: [0.2, 0.3, 0.4,0.6,0.7],
//                    colors: [
//                      Color(0xff150e23),
//                      Color(0xff150e23),
//                      Color(0xff150e23),
//                      Color(0xff150e23),
//                      Color(0xff150e23),
//                    ]
//                ),
//                borderRadius: BorderRadius.all(Radius.circular(8))
//            ),
//            height: 70,
//            width: 120,
//            child: new Padding(
//              padding: EdgeInsets.only(top: 28, left: 26),
//              child: new Text('LIKE PAY', style: TextStyle(color: Color(0xff90c707), fontWeight: FontWeight.bold),),
//            ),
//
//          ),
//        ),
//
//
//
////    ],
////  )
//      ],
//    );
//  }
//  Widget _SpendLike(context){
//    return new Stack(
//      alignment: Alignment.topLeft,
//      clipBehavior: Clip.none,
//      fit: StackFit.passthrough,
//      children: <Widget>[
//        Positioned(
//          bottom: -80,
//          child: Container(
//              alignment: Alignment.topLeft,
//              color: Colors.white,
//              height: MediaQuery.of(context).size.height/4,
//              width: MediaQuery.of(context).size.width,
//              child: new Padding(
//                padding: EdgeInsets.only(top:MediaQuery.of(context).size.height/35, left: 30.0),
////                child: new Text('EARN \nSPEND\nINVEST\nCRYPTO/\nFIAT', style: TextStyle(color: Color(0xff6C6B6D)),),
//                  child: Image.asset('assets/image/Swap LKE PKC.png', scale: 8, color: Color(0xff6C6B6D),)
//              )
//          ),
//        ),
//        Align(
//          alignment: Alignment.topLeft,
//          child: _LeftMenu()
//        ),
//
//        Align(
//          alignment: Alignment.topRight,
//          child:  new Container(
//            child: new Padding(
//              padding: EdgeInsets.only(top: MediaQuery.of(context).size.height/50, left: MediaQuery.of(context).size.width/17),
//              child: new Container(
//                height: MediaQuery.of(context).size.height/1.7,
//                width: MediaQuery.of(context).size.width/1.35,
//                decoration:  BoxDecoration(
//                  gradient: LinearGradient(
//                    begin: Alignment.topCenter,
//                    end: Alignment.bottomCenter,
//                    stops: [1],
//                    colors: [
//                      // Colors are easy thanks to Flutter's Colors class.
//                      Colors.cyan
//                    ],
//                  ),
//
//                ),
//                child: new Column(
//                  children: <Widget>[
//                    new Row(
//                      children: <Widget>[
//                        Padding(
//                          padding: EdgeInsets.only(top:30.0, left:60.0),
//                          child: new Column(
//                            children: <Widget>[
////                              new Icon(IconsSpend.Spendlike.true_top_up, size: 52,),
//                              new Image.asset('assets/image/trueTopUp.png', scale: 9,),
//                              new Text('TRUE MONEY\nTop up' , style: TextStyle(fontSize: 12.0),),
//                            ],
//                          ),
//                        ),
//                        Padding(
//                          padding: EdgeInsets.only(top:26.0, left:42.0),
//                          child: new Column(
//                            children: <Widget>[
//                              new Image.asset('assets/image/mobile-transfer.png', scale: 9,),
//                              new Text('Transfer'),
//                            ],
//                          ),
//                        ),
//                      ],
//                    ),
//                    new Row(
//                      children: <Widget>[
//                        Padding(
//                          padding: EdgeInsets.only(top:30.0, left:60.0),
//                          child: new Column(
//                            children: <Widget>[
//                              new Icon(IconsHome.MyFlutterApp.exchange, size: 52),
//                              new Text('Cash LIKE'),
//                            ],
//                          ),
//                        ),
//                        Padding(
//                          padding: EdgeInsets.only(top:30.0, left:58.0),
//                          child: new Column(
//                            children: <Widget>[
//                              new Icon(IconsHome.MyFlutterApp.reward, size: 52),
//                              new Text('LIKEShop\nRewards')
//                            ],
//                          ),
//                        ),
//
//
//                      ],
//                    ),
//                    new Row(
//                      children: <Widget>[
//                        Padding(
//                          padding: EdgeInsets.only(top:30.0, left:60.0),
//                          child: new Column(
//                            children: <Widget>[
////                              new Icon(IconsHome.MyFlutterApp.bitcoin, size: 52),
//                              new Image.asset('assets/image/Shopping.png', scale: 9,),
//                              new Text('Shopping'),
//                            ],
//                          ),
//                        ),
//
//
//                      ],
//                    ),
//
//                  ],
//                ),
//              ),
//            ),
//          ),
//        ),
//        Positioned(
//          left: 85,
//          child: Container(
//            child: new Padding(
//              padding: EdgeInsets.only(top: MediaQuery.of(context).size.height/3.8),
//              child: new CustomPaint(
//                foregroundPainter: ChatBubbleTriangle(),
//                child: null,
//              ),
//            ),
//          ),
//        ),
//        Positioned(
//          bottom: -35,
//          right: 20,
//          child: Container(
//            decoration: BoxDecoration(
//                gradient: LinearGradient(
//                    begin: Alignment.topRight,
//                    end: Alignment.bottomLeft,
//                    stops: [0.2, 0.3, 0.4,0.6,0.7],
//                    colors: [
//                      Color(0xff150e23),
//                      Color(0xff150e23),
//                      Color(0xff150e23),
//                      Color(0xff150e23),
//                      Color(0xff150e23),
//                    ]
//                ),
//                borderRadius: BorderRadius.all(Radius.circular(8))
//            ),
//            height: 70,
//            width: 120,
//            child: new Padding(
//              padding: EdgeInsets.only(top: 28, left: 26),
//              child: new Text('LIKE PAY', style: TextStyle(color: Color(0xff90c707), fontWeight: FontWeight.bold),),
//            ),
//
//          ),
//        ),
//
//
//
////    ],
////  )
//      ],
//    );
//  }
//
//
//  Widget _title() {
//    return new Row(
//      mainAxisAlignment: MainAxisAlignment.spaceBetween,
//      children: <Widget>[
//        new Align(
//          alignment: Alignment.topLeft,
//          child:  new Padding(
//            padding: EdgeInsets.only(top:36.0, left: 12),
//            child: new IconButton(icon: Icon(Icons.menu),
//                disabledColor: Color(0xff6C6B6D),
//                onPressed: null),
//          ),
//        ),
//        new Padding(
//          padding: EdgeInsets.only(top:36.0, right: 12),
//          child: new Text('34.0/USD', style: TextStyle(color: Color(0xff6C6B6D), fontWeight: FontWeight.bold),),
//        ),
//      ],
//    );
//  }
//
//  Widget _balance(context){
//    return  new Row(
//      crossAxisAlignment: CrossAxisAlignment.center,
//      mainAxisAlignment: MainAxisAlignment.center,
//      children: <Widget>[
//        new Padding(padding: EdgeInsets.only(left: MediaQuery.of(context).size.width/3.8,top:9, right: 10),
//          child: new Text('LIKE', style: TextStyle(color: Colors.white, fontFamily: 'Roboto', fontWeight: FontWeight.bold, fontSize: 16.0),),
//        ),
//        new Padding(padding: EdgeInsets.only(right: 90),
//          child: new Text('310,050', style: TextStyle(color: Colors.white, fontSize:30.0, fontWeight: FontWeight.bold,  fontFamily: 'Roboto'),),
//        ),
//        new Expanded(
//            child: new Column(
//              children: <Widget>[
//                new Icon(Icons.arrow_forward_ios, color: Color(0xff6C6B6D).withOpacity(0.6),)
//              ],
//            ))
//      ],
//
//    );
//  }
//
//  Widget _wallet(){
//    return new Row(
//      mainAxisAlignment: MainAxisAlignment.center,
//      children: <Widget>[
//        new Padding(
//          padding: EdgeInsets.only(top:10),
//          child: new Row(
//            children: <Widget>[
//              new Text('Wallet 1', style: TextStyle(color: Color(0xff6C6B6D), fontSize: 16.0),),
//              new Icon(Icons.arrow_forward_ios, color: Color(0xff6C6B6D).withOpacity(0.6),size: 16,)
//            ],
//          ),
//        )
//      ],
//    );
//  }
//
//  Widget _name(context){
//    return new Row(
//      children: <Widget>[
//        new Padding(
//          padding: EdgeInsets.only(top:10, left: MediaQuery.of(context).size.width/14),
//          child: new Text('Hi, Prapat', style: TextStyle(color: Colors.cyan, fontSize: 20.0),),
//        ),
//        new Padding(
//          padding: EdgeInsets.only(top:10, left: 4),
//          child: new Text('/ BASIC', style: TextStyle(color: Color(0xff6C6B6D).withOpacity(0.6), fontSize: 16.0),),
//        ),
//      ],
//    );
//  }
//
//
//
//  Widget _footer(context) {
//    return new Container(
//      color: Colors.white,
//      height: 80.0,
//      alignment: FractionalOffset.center,
//      child: new Column(
//        crossAxisAlignment: CrossAxisAlignment.start,
//        mainAxisAlignment: MainAxisAlignment.start,
//        children: <Widget>[
//          new Text("Hello", style: TextStyle(color: Colors.black),)
//        ],
//      ),
//    );
//  }
//
//}
//class ArcClipper extends CustomClipper<Path> {
//  @override
//  Path getClip(Size size) {
//    var path = Path();
//    path.lineTo(size.width, size.height);
//
////    var firstControlPoint = Offset(size.width / 4, size.height);
////    var firstPoint = Offset(size.width / 2, size.height);
////    path.quadraticBezierTo(firstControlPoint.dx, firstControlPoint.dy,
////        firstPoint.dx, firstPoint.dy);
////
////    var secondControlPoint = Offset(size.width - (size.width / 4), size.height);
////    var secondPoint = Offset(size.width, size.height - 30 );
////    path.quadraticBezierTo(secondControlPoint.dx, secondControlPoint.dy,
////        secondPoint.dx, secondPoint.dy);
//
//    path.lineTo(size.width, 0.0);
//    path.lineTo(200, 0.0);
//    path.close();
//
//    return path;
//  }
//
//  @override
//  bool shouldReclip(CustomClipper<Path> oldClipper) => true;
//}
//
//class ChatBubbleTriangle extends CustomPainter {
//  @override
//  void paint(Canvas canvas, Size size) {
//    var paint = Paint()..color = Colors.cyan;
//
//    var path = Path();
//
//    path.lineTo(24, -18);
//    path.lineTo(24, 18);
//    canvas.drawPath(path, paint);
//  }
//
//  @override
//  bool shouldRepaint(CustomPainter oldDelegate) {
//    return true;
//  }
//}
////Widget _bottom() {
////  return Stack(
////    children: <Widget>[
////      Align(
////        alignment: Alignment.bottomRight,
////        child: Container(
////          color: Colors.white,
////          height: 200,
////          width: 200,
////        ),
////      ),
////    ],
////  );
////}
//
