import 'package:get/get.dart';
import 'package:likewallet/controller/buyLikepointController/buyLikepointController.dart';
import 'package:likewallet/controller/historyController/historyController.dart';
import 'package:likewallet/controller/newsController/newsController.dart';
import 'package:likewallet/controller/otherController/configurationController.dart';
import 'package:likewallet/controller/otherController/keyMnemoryController.dart';
import 'package:likewallet/controller/otherController/logoStoreController.dart';
import 'package:likewallet/controller/permissionCheck/permissionCheckController.dart';
import 'package:likewallet/controller/profile/loginCotroller.dart';
import 'package:likewallet/controller/profile/profileController.dart';
import 'package:likewallet/controller/rewardController/rewardController.dart';
import 'package:likewallet/controller/scanController/scanController.dart';
import 'package:likewallet/controller/slipController/slipController.dart';
import 'package:likewallet/controller/transferController/onContractController.dart';
import 'package:likewallet/controller/transferController/transferController.dart';
import 'package:likewallet/controller/walletController/walletDataController.dart';


class AppBindings {
  static Future<void> lazyLoadControllers() async {
    // Controllers
    Get.lazyPut<LoginController>(() => LoginController());
    Get.lazyPut<ProfileController>(() => ProfileController());
    Get.lazyPut<CheckAboutController>(() => CheckAboutController());
    Get.lazyPut<ConfigurationController>(() => ConfigurationController());
    Get.lazyPut<KeyMNemoryController>(() => KeyMNemoryController());
    Get.lazyPut<WalletDataController>(() => WalletDataController());
    Get.lazyPut<LogoStoreController>(() => LogoStoreController());
    Get.lazyPut<TransferController>(() => TransferController());
    Get.lazyPut<ContactController>(() => ContactController());
    Get.lazyPut<ScanController>(() => ScanController());
    Get.lazyPut<RewardController>(() => RewardController());
    Get.lazyPut<SlipController>(() => SlipController());
    Get.lazyPut<NewsController>(() => NewsController());
    Get.lazyPut<BuyLikepointController>(() => BuyLikepointController());
    Get.lazyPut<HistoryController>(() => HistoryController());

    final profileCtrl = Get.find<ProfileController>();
    await profileCtrl.getCurrentUser();

    final keyCtrl = Get.find<KeyMNemoryController>();
    await keyCtrl.initializeEncryptionKeys();

    final rewardCtrl = Get.find<RewardController>();
    await rewardCtrl.getNewReward();

    final historyCtrl = Get.find<HistoryController>();
    await historyCtrl.getHistory();



  }
}
