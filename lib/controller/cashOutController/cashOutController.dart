import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dio/dio.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:likewallet/service/components.dart';
import 'package:likewallet/service/getStorage.dart';
import 'package:likewallet/service/globalConfig.dart';
import 'package:likewallet/view/transferPoint/cashOut/choiceTopay.dart';

class CashOutController extends GetxController {
  // Text controllers
  final TextEditingController amountController = TextEditingController();
  final TextEditingController addressController = TextEditingController();
  final TextEditingController bahtController = TextEditingController();
  final TextEditingController amountGoldController = TextEditingController();
  
  // UI state variables
  RxBool assetSelect = false.obs;
  RxBool currencySelect = false.obs;
  RxInt selectCurrency = 0.obs;
  RxInt selectPageCashOut = 0.obs;
  RxInt selectGoldWeight = 0.obs;
  RxBool isLoading = false.obs;
  
  // Transaction variables
  RxString symbol = "THB".obs;
  RxDouble rate = 100.0.obs;
  RxDouble amount = 0.0.obs;
  RxString accountNumber = "".obs;
  RxDouble totalSell = 0.0.obs;
  RxDouble fee = 0.0.obs;
  
  // Currency options
  final List<String> currencies = ['THB', 'USD', 'LAK'];
  final List<String> currencyNames = ['Thai Baht', 'US Dollar', 'Lao Kip'];
  
  // Gold weight options in grams
  final List<String> goldWeights = ['1.00', '3.811', '7.622', '11.433', '15.244'];
  
  // Dio for API calls
  final Dio dio = Dio();
  
  @override
  void onInit() {
    super.onInit();
    
    // Initialize with default values
    amountGoldController.text = "1.00";
    
    // Add listeners to text controllers
    amountController.addListener(updateAmount);
    bahtController.addListener(updateBaht);
    
    // Load initial data
    loadExchangeRates();
    loadFees();
  }
  
  @override
  void onClose() {
    // Dispose controllers
    amountController.dispose();
    addressController.dispose();
    bahtController.dispose();
    amountGoldController.dispose();
    super.onClose();
  }
  
  // Update amount when LIKE amount changes
  void updateAmount() {
    if (amountController.text.isNotEmpty) {
      try {
        double likeAmount = double.parse(amountController.text.replaceAll(',', ''));
        // Convert LIKE to selected currency
        double currencyAmount = likeAmount / rate.value;
        bahtController.text = currencyAmount.toStringAsFixed(2);
        amount.value = currencyAmount;
      } catch (e) {
        print('Error parsing amount: $e');
        bahtController.text = '';
        amount.value = 0.0;
      }
    } else {
      bahtController.text = '';
      amount.value = 0.0;
    }
  }
  
  // Update LIKE amount when currency amount changes
  void updateBaht() {
    // This is intentionally left empty as we handle this in the UI
  }
  
  // Load exchange rates from Firestore
  Future<void> loadExchangeRates() async {
    try {
      isLoading.value = true;
      
      final snapshot = await FirebaseFirestore.instance
          .collection('exchangeFiat')
          .get();
      
      for (var doc in snapshot.docs) {
        Map<String, dynamic> data = doc.data();
        if (data['to'] == currencies[selectCurrency.value]) {
          rate.value = double.parse(data['rate'].toString());
          break;
        }
      }
      
      isLoading.value = false;
    } catch (e) {
      print('Error loading exchange rates: $e');
      isLoading.value = false;
    }
  }
  
  // Load fees from API
  Future<void> loadFees() async {
    try {
      isLoading.value = true;
      
      final response = await dio.get(
        '${AppEnv.apiUrl}/getCurrentFee',
        queryParameters: {
          'currency': currencies[selectCurrency.value]
        }
      );
      
      if (response.data != null && response.statusCode == 200) {
        fee.value = double.parse(response.data.toString());
      }
      
      isLoading.value = false;
    } catch (e) {
      print('Error loading fees: $e');
      isLoading.value = false;
    }
  }
  
  // Change selected currency
  void changeCurrency(int index) {
    selectCurrency.value = index;
    symbol.value = currencies[index];
    loadExchangeRates();
    loadFees();
  }
  
  // Change selected asset type
  void changeAssetType(int index) {
    selectPageCashOut.value = index;
    assetSelect.value = false;
  }
  
  // Change selected gold weight
  void changeGoldWeight(int index) {
    selectGoldWeight.value = index;
    amountGoldController.text = goldWeights[index];
  }
  
  // Toggle asset selection dropdown
  void toggleAssetSelect() {
    assetSelect.value = !assetSelect.value;
  }
  
  // Toggle currency selection dropdown
  void toggleCurrencySelect() {
    currencySelect.value = !currencySelect.value;
  }
  
  // Proceed to next step based on selected asset type
  Future<void> proceedToNextStep(BuildContext context) async {
    if (selectPageCashOut.value == 0) {
      // LIKE to currency
      if (amountController.text.isEmpty) {
        Get.snackbar(
          'Error',
          'Please enter an amount',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return;
      }
      
      double likeAmount = double.parse(amountController.text.replaceAll(',', ''));
      
      // Check minimum withdrawal amount
      if ((symbol.value == 'USD' && amount.value < 1) ||
          (symbol.value == 'THB' && amount.value < 10) ||
          (symbol.value == 'LAK' && amount.value < 10)) {
        Get.snackbar(
          'Error',
          'Minimum withdrawal amount not met',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return;
      }
      
      // Calculate total sell amount
      totalSell.value = likeAmount;
      
      // Navigate to payment method selection
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ChoiceTopay(
            amount: amount.value,
            fee: fee.value,
            rate: rate.value,
            symbol: symbol.value,
            totalSell: totalSell.value,
          ),
        ),
      );
    }
  }
}
