import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dio/dio.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:likewallet/service/components.dart';
import 'package:likewallet/service/getStorage.dart';
import 'package:likewallet/service/globalConfig.dart';
import 'package:likewallet/service/httpRequest.dart';
import 'package:likewallet/view/transferPoint/buyLikepoint/showQRBuyLikepoint.dart';
// import 'package:likewallet/view/transferPoint/buyLikepoint/successBuyLike.dart';

class BuyLikepointController extends GetxController {
  // Text controllers
  final TextEditingController amountSend = TextEditingController();
  final FocusNode amountFocusNode = FocusNode();

  // Formatters
  final NumberFormat f = NumberFormat("#,##0", "en_US");

  // Reactive variables
  RxBool showsymbol = false.obs;
  RxBool showbutton = false.obs;
  RxString amountValue = ''.obs;
  RxString Symbol = 'THB'.obs;
  RxDouble rate = 100.0.obs;
  RxDouble rateCurrency = 1.0.obs;
  RxList<String> symbol = <String>['THB', 'USD', 'LAK', 'VND', 'GOLD', 'LIKE'].obs;
  RxString selectPage = 'QRCode'.obs;

  RxString qrCode = "".obs;
  RxString timerString = "00:00".obs;

  // Variables for QR code generation
  final Dio dio = Dio();
  Timer? timer;
  RxInt countLoop = 0.obs;
  RxInt timerOut = 600.obs; // 10 minutes timeout
  RxString partnerTxnUid = "".obs;
  RxString origPartnerTxnUid = "".obs;
  RxString requestDt = "".obs;

  // Variables for bank slip upload
  final ImagePicker _picker = ImagePicker();
  Rx<File?> imageFile = Rx<File?>(null);
  RxBool isUploading = false.obs;
  RxBool isSlipUploaded = false.obs;
  RxString uploadedImageId = "".obs;
  RxString uploadedImageUrl = "".obs;

  // Bank account details
  RxString nameAcc = "..loading".obs;
  RxString numAcc = "..loading".obs;
  RxString bankAcc = "..loading".obs;

  @override
  void onInit() {
    super.onInit();
    loadSavedCurrency();
    loadBankAccountDetails();

    // Listen for changes in the amount field
    amountSend.addListener(() {
      if (amountSend.text.isNotEmpty) {
        amountValue.value = amountSend.text.replaceAll(',', '');
        showbutton.value = true;
      } else {
        amountValue.value = '';
        showbutton.value = false;
      }
    });
  }

  // Load bank account details from Firestore
  void loadBankAccountDetails() {
    FirebaseFirestore.instance
        .collection('accountBuyLike')
        .doc('bank')
        .get()
        .then((value) {
      if (value.exists) {
        nameAcc.value = value.data()?["name"] ?? "..loading";
        numAcc.value = value.data()?["number"] ?? "..loading";
        bankAcc.value = value.data()?["bank"] ?? "..loading";
      }
    }).catchError((error) {
      print('Error loading bank account details: $error');
    });
  }

  @override
  void onClose() {
    amountSend.dispose();
    amountFocusNode.dispose();
    if (timer != null && timer!.isActive) {
      timer!.cancel();
    }
    super.onClose();
  }

  // Load saved currency from storage
  void loadSavedCurrency() {
    _changeCurrency('THB');
    // set standard currency to THB
    return ;
    final savedCurrency = Storage.get<String>(StorageKeys.currency);
    if (savedCurrency != null && symbol.contains(savedCurrency)) {
      Symbol.value = savedCurrency;
      _changeCurrency(savedCurrency);
    } else {
      // Default to THB if no saved currency
      Symbol.value = 'THB';
      _changeCurrency('THB');
    }
  }

  // Change currency and update rate
  void _changeCurrency(String currency) {
    Symbol.value = currency;

    // Set rate based on currency
    if (currency == 'LIKE') {
      rate.value = 1.0; // 1:1 ratio for LIKE
    } else {
      rate.value = 100.0; // 1:100 ratio for other currencies
    }

    // Save the currency
    Storage.save(StorageKeys.currency, currency);
  }

  // Toggle symbol dropdown
  void toggleSymbolDropdown() {
    showsymbol.value = !showsymbol.value;
  }

  // Hide symbol dropdown
  void hideSymbolDropdown() {
    showsymbol.value = false;
  }

  // Change currency from dropdown
  void changeCurrency(String newCurrency) {
    Symbol.value = newCurrency;
    _changeCurrency(newCurrency);
    showsymbol.value = false;

    if (amountSend.text.isNotEmpty) {
      showbutton.value = true;
    }
  }


  // Change value textEditingController
  void showButton() {
    if(amountSend.text.isNotEmpty && validateFirstNumber(amountSend.text)) {
      amountValue.value = amountSend.text.replaceAll(',', '');
      showbutton.value = true;
    } else {
      amountValue.value = '';
      showbutton.value = false;
    }
    print(showbutton.value);
    update();
  }

  // Reset form
  void resetForm() {
    showbutton.value = false;
    amountSend.clear();
    amountValue.value = '0';
  }

  // Validate if the first character is a number
  bool validateFirstNumber(String value) {
    if (value.isEmpty) return false;

    try {
      double.parse(value);
      return true;
    } catch (e) {
      return false;
    }
  }

  void changeSelectPage(String page) {
    selectPage.value = page;
    update();
  }

  // Navigate to confirmation page
  void navigateToConfirmation(BuildContext context) {
    if (amountSend.text.isEmpty) return;

    double amount = double.parse(amountSend.text.replaceAll(',', ''));

    if (amount > 0 && selectPage.value == 'QRCode' && amount <= 50000) {
      // Generate QR code and navigate to QR code page
      generateQR();
    } else if (amount > 0 && selectPage.value == 'Upload') {
      Get.toNamed('/confirmBuylike', arguments: {
        'amountSend': amountSend.text,
        'selectPage': selectPage.value,
        'titleName': '..Loading',
        'rateCurrency': (amount * rate.value).toString()
      });
    } else if (amount > 50000 && selectPage.value == 'QRCode') {
      Get.snackbar(
        'error'.tr,
        'amount_too_large'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } else if (amount == 0) {
      Get.snackbar(
        'error'.tr,
        'greater_than_0'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  // Update timer display string
  void updateTimerString() {
    int minutes = timerOut.value ~/ 60;
    int seconds = timerOut.value % 60;
    timerString.value = '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  // Check transaction status
  void inquiry() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      // Get Firebase token
      final idToken = await user.getIdToken();

      // Get user data from Firestore
      final userDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .get();

      if (!userDoc.exists) return;

      // Make API request to check transaction status
      final response = await dio.post(
        '${AppEnv.apiUrl}/inquiryBuy',
        data: {
          "_token": idToken,
          "partnerTxnUid": partnerTxnUid.value,
          "origPartnerTxnUid": origPartnerTxnUid.value
        }
      );

      if (response.data != null && response.data["statusCode"] == 200) {
        final txnStatus = response.data["result"]["txnStatus"];

        // If payment is completed
        if (txnStatus == "PAID") {
          // Cancel the timer
          if (timer != null && timer!.isActive) {
            timer!.cancel();
          }

          countLoop.value = 1;

          // Navigate to success page
          // Get.toNamed('/successBuylike', arguments: {
          //   'amount': amountValue.value,
          //   'symbol': Symbol.value
          // });
        }

        // Decrease timer
        timerOut.value -= 5;
      }
    } catch (e) {
      print('Error in inquiry: $e');
    }
  }

  // Pick image from gallery
  Future<void> pickImage() async {
    isUploading.value = true;

    try {
      final XFile? pickedFile = await _picker.pickImage(
        source: ImageSource.gallery,
        maxHeight: 1000,
        maxWidth: 1000,
        imageQuality: 85,
      );

      if (pickedFile != null) {
        imageFile.value = File(pickedFile.path);
        await uploadImage();
      } else {
        isUploading.value = false;
      }
    } catch (e) {
      print('Error picking image: $e');
      isUploading.value = false;
      Get.snackbar(
        'error'.tr,
        'image_pick_failed'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  // Upload image to server
  Future<void> uploadImage() async {
    if (imageFile.value == null) {
      isUploading.value = false;
      return;
    }

    print("uploadImage");
    try {
      // Get user authentication
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        isUploading.value = false;
        print('User not authenticated');
        return;
      }

      // Get Firebase token
      final idToken = await user.getIdToken();

      // Convert image to base64
      String base64Image = base64Encode(imageFile.value!.readAsBytesSync());
      String fileName = imageFile.value!.path.split("/").last;

      Map body = {
        "image": base64Image,
        "name": fileName,
      };

      // Make API request to upload image
      final response = await AppApi.post(
        'https://${AppEnv.apiCheck}/uploadImageBuy',
          body
      );

      print(response);

      if (response["statusCode"] == 200) {
        final result = response["result"];

        // Store upload result
        uploadedImageId.value = result["id"] ?? '';
        uploadedImageUrl.value = result["url"] ?? '';

        // Update UI
        isSlipUploaded.value = true;
        isUploading.value = false;

      } else {
        isUploading.value = false;
        print('Failed to upload image: ${response}');
        Get.snackbar(
          'error'.tr,
          'upload_failed'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      isUploading.value = false;
      print('Error uploading image: $e');
      Get.snackbar(
        'error'.tr,
        'upload_failed'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  // Reset upload state
  void resetUpload() {
    isSlipUploaded.value = false;
    imageFile.value = null;
    uploadedImageId.value = '';
    uploadedImageUrl.value = '';
  }

  // Generate QR code for payment
  void generateQR() async {
    try {
      // Get user authentication
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        print('User not authenticated');
        return;
      }

      // Get stored address
      final addr = Storage.get<String>(StorageKeys.addressETH);
      if (addr == null || addr == "no") {
        print('No wallet address found');
        return;
      }

      // Get Firebase token
      final idToken = await user.getIdToken();

      // Get user data from Firestore
      final userDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .get();

      if (!userDoc.exists) {
        print('User document not found');
        return;
      }

      // Make API request to create order
      final response = await dio.post(
        '${AppEnv.apiUrl}/createOrderBuy',
        data: {
          "_token": idToken,
          "walletNumber": userDoc.data()?['wallet_number'] == "1"
              ? "0"
              : userDoc.data()?['wallet_number'],
          "address": addr,
          "amount": amountValue.value
        }
      );

      if (response.data != null && response.data["statusCode"] == 200) {
        final result = response.data["result"][0];

        // Parse date and calculate timer
        final parsedDate = DateTime.parse(result["requestDt"].toString());
        final startTime = parsedDate.millisecondsSinceEpoch;
        final diffTime = ((startTime / 1000) + 600) - (DateTime.now().millisecondsSinceEpoch / 1000);

        // Set timer value
        timerOut.value = diffTime.toInt() > 0 ? diffTime.toInt() : 600;

        print( result["qrcode"].toString());
        // Store transaction data
        partnerTxnUid.value = result["partnerTxnUid"].toString();
        origPartnerTxnUid.value = result["partnerTxnUid"].toString();
        qrCode.value = result["qrcode"].toString();
        requestDt.value = result["requestDt"].toString();

        // Start timer to check transaction status
        countLoop.value = 0;
        if (timer != null && timer!.isActive) {
          timer!.cancel();
        }

        // Start timer to update countdown display
        Timer.periodic(Duration(seconds: 1), (displayTimer) {
          if (timerOut.value <= 0) {
            displayTimer.cancel();
            timerString.value = "00:00";

            // Auto-regenerate QR code when timer expires
            if (Get.isRegistered<QRCodePromptpay>()) {
              // Show regeneration message
              Get.snackbar(
                'info'.tr,
                'qr_expired_regenerating'.tr,
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: Colors.orange,
                colorText: Colors.white,
                duration: Duration(seconds: 2),
              );

              // Wait a moment before regenerating
              Future.delayed(Duration(seconds: 2), () {
                generateQR();
              });
            }
          } else {
            timerOut.value--;
            updateTimerString();
          }
        });

        // Start timer to check transaction status
        timer = Timer.periodic(Duration(seconds: 5), (timerx) {
          if (countLoop.value == 0) {
            // Only cancel the timer, don't set countLoop to 1
            // This allows the QR code to be regenerated
            if (timerOut.value <= 0) {
              timer?.cancel();
            } else {
              inquiry();
            }
          }
        });

        // Navigate to QR code page
        Get.to(() => QRCodePromptpay());
        update();
      } else {
        print('Failed to create order: ${response.data}');
      }
    } catch (e) {
      print('Error generating QR: $e');
    }
  }
}
