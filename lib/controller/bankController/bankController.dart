import 'package:get/get.dart';

class BankController extends GetxController {
  // final fireStore = FirebaseFirestore.instance;
  //
  // // --- State
  // var showFavorite = false.obs;
  // var price = 0.0.obs;
  // var running = 0.obs;
  // var hideButton = false.obs;
  // var balance = 'Loading..'.obs;
  // var lockedBalance = 'Loading..'.obs;
  // var available = 'Loading..'.obs;
  // var rateCurrency = 0.0.obs;
  // var balanceLIKE = 0.0.obs;
  // var balanceLIKELock = 0.0.obs;
  // var balanceTHB = '0'.obs;
  // var tabMenu = 0.obs;
  // var saving = false.obs;
  // var opacity = 1.0.obs;
  // var delete = false.obs;
  // var showLike = false.obs;
  // var autoValidate = false.obs;
  // var crossFadeView = CrossFadeState.showFirst.obs;
  //
  // // --- Controllers
  // final addressText = TextEditingController();
  // final nameFavorite = TextEditingController();
  // final addressFavorite = TextEditingController();
  //
  // final formatIntl = NumberFormat("###,###.##");
  // final formatCurrency = NumberFormat.decimalPattern();
  // final formatBank = NumberFormat("###-###-####");
  //
  // final streamBalance = StreamController<String>.broadcast();
  // final streamBalanceLocked = StreamController<String>.broadcast();
  // final streamAvailable = StreamController<double>.broadcast();
  // final streamBalanceLock = StreamController<double>.broadcast();
  // final streamCurrencyAmount = StreamController<String>.broadcast();
  //
  // // --- Data Variables
  // late String accountNumber;
  // late String currency;
  // late String symbol1;
  // late String symbol2;
  // late String fiat;
  // late String toAddress;
  // late String photoFavorite;
  // late File? imageFavorite;
  // late String mnemonic;
  // late String uid;
  // late String dataString;
  // int curIndex = 0;
  // double fee = 0.0;
  // double amountInput = 0.0;
  // double rate = 100.0;
  // int keyword = 1;
  // bool login = false;
  // bool kycStatus = false;
  //
  // // --- Services (ต้องเตรียม DI)
  // late dynamic eth;
  // late dynamic configETH;
  // late dynamic addressService;
  // late dynamic seed;
  // late dynamic logon;
  // late dynamic firebaseAuth;
  // late dynamic checkAPI;
  // late dynamic setFormat;
  // late dynamic apiHttp;
  // late dynamic checkAbout;
  // late dynamic auth;
  //
  // // --- UI Data
  // List<Widget> tabBar = [];
  // List<Widget> tabBarBody = [];
  // List permission = [false, false, false, false];
  // List listShop = [];
  // List group = [];
  // List search = [];
  // OverlayEntry? overlayEntry;
  // FocusNode amountFocusNode = FocusNode();
  // FocusNode amountBuyFocusNode = FocusNode();
  // FocusNode amountCashFocusNode = FocusNode();
  //
  //
  //
  // Future<void> setInit() async {
  //   fiat = Storage.get(StorageKeys.currency) ?? 'none';
  //   currency = fiat;
  //
  //   kycStatus = Storage.get(StorageKeys.kycActive) ?? false;
  //   login = Storage.get(StorageKeys.login) ?? false;
  //
  //   if (login) {
  //     mnemonic = await configETH.getMnemonic();
  //     await generateETH();
  //   }
  //
  //   final tier = Storage.get(StorageKeys.tierLevel) ?? '1';
  //   await checkPermission(tier: tier);
  //   _changeText(currency);
  //   await hotReloadBalance();
  // }
  //
  // void showShortToast(String msg) {
  //   Fluttertoast.showToast(
  //     msg: msg,
  //     toastLength: Toast.LENGTH_SHORT,
  //     backgroundColor: Colors.cyan,
  //     textColor: Colors.white,
  //   );
  // }
  //
  // void showColoredToast(String msg) {
  //   Fluttertoast.showToast(
  //     msg: msg,
  //     toastLength: Toast.LENGTH_LONG,
  //     backgroundColor: Colors.red,
  //     textColor: Colors.white,
  //   );
  // }
  //
  // Future<void> hotReloadBalance() async {
  //   await generateETH();
  //   await reloadBalance(configETH.getAddress(), fiat);
  // }
  //
  // Future<void> generateETH() async {
  //   final address = configETH.getAddress();
  //   final avaiBalance = await eth.getBalance(address: address);
  //   balanceLIKE.value = avaiBalance.toDouble();
  //   streamAvailable.sink.add(balanceLIKE.value);
  //
  //   final balanceLock = await eth.getBalanceLock(address: address);
  //   balanceLIKELock.value = balanceLock.toDouble();
  //   balance.value = formatIntl.format(balanceLIKE.value + balanceLIKELock.value);
  //   lockedBalance.value = formatIntl.format(balanceLIKELock.value);
  //
  //   streamBalance.sink.add(balance.value);
  //   streamBalanceLocked.sink.add(lockedBalance.value);
  //   streamBalanceLock.sink.add(balanceLIKELock.value);
  // }
  //
  // Future<void> checkPermission({required String tier}) async {
  //   permission[0] = await checkAbout.checkPermissionMenu(tierLevel: tier, page: 'send');
  //   permission[1] = await checkAbout.checkPermissionMenu(tierLevel: tier, page: 'receive');
  //   permission[2] = await checkAbout.checkPermissionMenu(tierLevel: tier, page: 'buylike');
  //   permission[3] = await checkAbout.checkPermissionMenu(tierLevel: tier, page: 'cash');
  //
  //   // TODO: เพิ่ม tabBar และ tabBarBody ตาม permission ที่ได้
  // }
  //
  // // Future<void> _changeText(String symbol) async {
  // //   final exchangeFiat = fireStore.collection('exchangeFiat').withConverter<ExchangeFiat>(
  // //     fromFirestore: (snapshot, _) => ExchangeFiat.fromJson(snapshot.data()!),
  // //     toFirestore: (model, _) => model.toJson(),
  // //   );
  // //   final ds = await exchangeFiat.doc(symbol == 'none' ? "THB-LIKE" : "THB-$symbol").get();
  // //   final rateData = ds.data();
  // //   if (rateData != null) {
  // //     if (rateData.to == 'LIKE') {
  // //       rateCurrency.value = rateData.rate.toDouble();
  // //     } else {
  // //       rateCurrency.value = 1 / rateData.rate.toDouble();
  // //     }
  // //   }
  // //   symbol1 = symbol;
  // // }
  //
  // Future<void> pasteAddress() async {
  //   final data = await Clipboard.getData('text/plain');
  //   if (data != null) {
  //     addressText.text = data.text ?? '';
  //     toAddress = data.text ?? '';
  //   }
  // }
  //
  // void showOverlay(BuildContext context) {
  //   if (overlayEntry != null) return;
  //   final overlayState = Overlay.of(context);
  //   overlayEntry = OverlayEntry(
  //     builder: (context) => Positioned(
  //       bottom: MediaQuery.of(context).viewInsets.bottom,
  //       right: 0,
  //       left: 0,
  //       child: InputDoneView(),
  //     ),
  //   );
  //   overlayState?.insert(overlayEntry!);
  // }
  //
  // void removeOverlay() {
  //   overlayEntry?.remove();
  //   overlayEntry = null;
  // }
}
