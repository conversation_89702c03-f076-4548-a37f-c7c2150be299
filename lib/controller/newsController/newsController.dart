import 'dart:ui';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:likewallet/service/getStorage.dart';
import 'package:likewallet/service/globalConfig.dart';
import 'package:likewallet/service/httpRequest.dart';
import 'package:likewallet/controller/profile/profileController.dart';

class NewsController extends GetxController {
  // Variables from the original NewsPage
  RxBool isLoading = false.obs;
  RxBool permissionNews = true.obs;
  RxInt tabSelect = 1.obs;
  RxString uid = ''.obs;
  RxBool loadingNews = false.obs;
  
  // Formatters for date display
  final DateFormat f_th = DateFormat('d MMMM kk:mm น.', 'th');
  final DateFormat f_en = DateFormat('MMMM d kk:mm a', 'en');
  
  // Language setting
  RxString lang = 'en'.obs;
  
  // Alignment for the tab selector animation
  Rx<AlignmentGeometry> alignment = Alignment.centerLeft.obs;
  
  // Variables for transaction details
  RxDouble amount = 0.0.obs;
  RxString fee = '0 LIKE'.obs;
  RxDouble rate = 100.0.obs;
  RxString symbol = 'THB'.obs;
  RxDouble totalSell = 0.0.obs;
  
  // Firebase reference
  final fireStore = FirebaseFirestore.instance;
  
  // Profile controller for tier level
  final ProfileController profileCtrl = Get.find<ProfileController>();
  
  @override
  void onInit() {
    super.onInit();
    initializeData();
  }
  
  // Initialize data
  Future<void> initializeData() async {
    loadingNews.value = true;
    
    // Get user ID from storage
    uid.value = profileCtrl.user!.uid;
    
    // Get language setting
    lang.value = Storage.get<String>(StorageKeys.language) ?? 'en';
    
    // Check if news feature is permitted
    await checkNewsPermission();
    
    loadingNews.value = false;
  }
  
  // Check if news feature is permitted
  Future<void> checkNewsPermission() async {
    try {
      final tierLevel = profileCtrl.tierLevel;
      
      final response = await AppApi.post('${AppEnv.apiUrl}/checkPermission', {
        "apiKey": AppEnv.APIKEY,
        "secretKey": AppEnv.SECRETKEY,
        "page": "news",
        "tierLevel": tierLevel
      });
      
      if (response != null && response['status'] == 'success') {
        permissionNews.value = response['permission'] ?? true;
      } else {
        permissionNews.value = false;
      }
    } catch (e) {
      print('Error checking news permission: $e');
      permissionNews.value = false;
    }
  }
  
  // Change tab alignment
  void changeAlignment() {
    alignment.value = alignment.value == Alignment.centerLeft
        ? Alignment.centerRight
        : Alignment.centerLeft;
  }
  
  // Change tab selection
  Future<void> changeTabSelection(int tab) async {
    if (tabSelect.value != tab) {
      changeAlignment();
      await Future.delayed(Duration(milliseconds: 150));
      tabSelect.value = tab;
      
      // If switching to notifications tab, mark all as read
      if (tab == 2 && uid.value.isNotEmpty) {
        await markNotificationsAsRead();
      }
    }
  }
  
  // Mark all notifications as read
  Future<void> markNotificationsAsRead() async {
    try {
      QuerySnapshot ds = await fireStore
          .collection('notificationByUser')
          .doc(uid.value)
          .collection('notify')
          .where("status", isEqualTo: "unread")
          .get();
          
      if (ds.docs.isNotEmpty) {
        for (var chat in ds.docs) {
          await fireStore
              .collection('notificationByUser')
              .doc(uid.value)
              .collection('notify')
              .doc(chat.id)
              .update({"status": "read"});
        }
      }
    } catch (e) {
      print('Error marking notifications as read: $e');
    }
  }
  
  // Delete notification
  Future<void> deleteNotification(int id) async {
    try {
      await fireStore
          .collection('notificationByUser')
          .doc(uid.value)
          .collection('notify')
          .doc(id.toString())
          .delete();
    } catch (e) {
      print('Error deleting notification: $e');
    }
  }
  
  // Show KYC dialog
  void showKycDialog(BuildContext context, Map<String, dynamic> document, int id) {
    Dialog simpleDialog = Dialog(
      elevation: 500,
      backgroundColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(30.0),
      ),
      child: Container(
        width: 0.9.sw,
        color: Colors.transparent,
        margin: EdgeInsets.only(bottom: 0.6.sh),
        child: ClipRect(
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.6),
                borderRadius: BorderRadius.all(Radius.circular(20.0)),
              ),
              height: 0.55.sh,
              width: 0.9.sw,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  SizedBox(height: 30.h),
                  Text(
                    document['detail'],
                    style: TextStyle(
                      letterSpacing: 0.3,
                      fontFamily: 'Proxima Nova',
                      color: Colors.black,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Expanded(child: Container()),
                  Container(
                    margin: EdgeInsets.only(bottom: 30.h),
                    width: 0.8.sw,
                    child: Text(
                      'tabslide_kyc1'.tr,
                      textAlign: TextAlign.center,
                      maxLines: 4,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        letterSpacing: 0.3,
                        fontFamily: 'Proxima Nova',
                        color: Colors.black,
                        height: 1.5,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(bottom: 30.h),
                    width: 0.8.sw,
                    child: Text(
                      'tabslide_kyc2'.tr,
                      textAlign: TextAlign.center,
                      maxLines: 4,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        letterSpacing: 0.3,
                        fontFamily: 'Proxima Nova',
                        color: Colors.black,
                        height: 1.5,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Expanded(child: Container()),
                  _buildDialogButtons(context, id),
                ],
              ),
            ),
          ),
        ),
      ),
    );
    
    Get.dialog(simpleDialog);
    
    // Mark notification as read
    fireStore
        .collection('notificationByUser')
        .doc(uid.value)
        .collection('notify')
        .doc(id.toString())
        .update({"status": "read"});
  }
  
  // Build dialog buttons
  Widget _buildDialogButtons(BuildContext context, int id) {
    return Container(
      width: 0.8.sw,
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: Colors.black.withOpacity(0.4),
            width: 1,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          GestureDetector(
            onTap: () {
              Get.back();
            },
            child: Container(
              alignment: Alignment.center,
              height: 0.13.sh,
              width: 0.4.sw,
              child: Text(
                'messages_no'.tr,
                textAlign: TextAlign.center,
                style: TextStyle(
                  letterSpacing: 0.3,
                  fontFamily: 'Proxima Nova',
                  color: Color(0xff08e8de),
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          GestureDetector(
            onTap: () {
              Get.back();
              Get.toNamed('/choiceTopay', arguments: {
                'amount': amount.value,
                'fee': fee.value,
                'rate': rate.value,
                'symbol': symbol.value,
                'totalSell': totalSell.value
              });
            },
            child: Container(
              alignment: Alignment.center,
              height: 0.13.sh,
              width: 0.4.sw,
              child: Text(
                'tabslide_account'.tr,
                textAlign: TextAlign.center,
                style: TextStyle(
                  letterSpacing: 0.3,
                  fontFamily: 'Proxima Nova',
                  color: Color(0xff08e8de),
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  // Open web view
  void openWebView(String url) {
    print('Opening web view with URL: $url');
    // Get.to(() => WebOpenNoTitle(
    //   title: 'notifications'.tr,
    //   url: url,
    // ));
  }
}
