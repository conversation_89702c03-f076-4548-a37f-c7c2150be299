import 'dart:math';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:likewallet/service/getStorage.dart';
import 'package:likewallet/service/globalConfig.dart';
import 'package:likewallet/service/httpRequest.dart';
import 'dart:typed_data';
import 'package:bip39/bip39.dart' as bip39;
import 'package:bip32/bip32.dart' as bip32;
import 'package:hex/hex.dart';
import 'package:encrypt/encrypt.dart' as Encrypt;

class LoginController extends GetxController {
  RxBool isLoading = false.obs;
  final FirebaseAuth firebaseAuth = FirebaseAuth.instance;
  late String verificationId;
  String usePhone = '';
  String codeVerify = '999999';

  static const chars = "abcdefghijklmnopqrstuvwxyz0123456789";

  Future<bool> checkMigrate()async {
    try {
      final user = firebaseAuth.currentUser;
      if (user == null) {
        debugPrint("No user signed in.");
        return false;
      }

      final doc = await FirebaseFirestore.instance
          .collection('migration')
          .doc(user.uid)
          .get();

      if (!doc.exists) {
        debugPrint("Migration document not found.");
        return false;
      }

      final data = doc.data();
      if (data == null) return false;

      final migrated = data['migrated']?.toString() ?? '';
      return migrated.isNotEmpty;
    } catch (e) {
      debugPrint("Error during migration check: $e");
      return false;
    }
  }

  Future<String?> checkExistUser(String phone) async {
    final url = '${AppEnv.apiUrl}/checkExistUser';
    final data = {
      'apiKey': AppEnv.APIKEY,
      'secretKey': AppEnv.SECRETKEY,
      'phone_number': phone,
    };
    usePhone = phone;
    try {
      final response = await AppApi.post(url, data);
      if (response['statusCode'] == 200) {
        if (response['result'] == true) {
          debugPrint('User exists');

          return null;
        } else {
          debugPrint('User does not exist');
          final oldUserStatus = await checkOldUser(phone);
          if (oldUserStatus[0] == '200') {
            return 'register';
          } else {
            Fluttertoast.showToast(
              msg: 'noUser'.tr,
              toastLength: Toast.LENGTH_LONG,
              backgroundColor: Colors.red,
              textColor: Colors.white,
            );
            return 'no';
          }
        }
      } else {
        debugPrint('Error checking user: ${response['message']}');
        return 'error';
      }
    } catch (e) {
      debugPrint('Error checking user: $e');
      return 'error';
    }
  }

  Future<List<String>> checkOldUser(String phone) async {
    final url = '${AppEnv.OldAPI}/checkOldUserName';
    final data = {'phone_number': phone};

    try {
      final response = await AppApi.post(url, data);
      if (response['status'] == 200) {
        return [
          response['statusCode'].toString(),
          response['firstname'] ?? '',
          response['lastname'] ?? '',
        ];
      } else {
        debugPrint('Error checking old user: ${response['message']}');
        return ['500', '', ''];
      }
    } catch (e) {
      debugPrint('Error checking old user: $e');
      return ['500', '', ''];
    }
  }

  Future<String> sendOTP(String phone) async {
    try {
      // final otpDoc = await FirebaseFirestore.instance.collection('switch').doc('OTP').get();
      // final useFirebase = otpDoc.data()?['status'] == true;

      // return useFirebase ? await sendCodeWithFirebase(phone) : await sendCodeWithApi(phone);
      return await sendCodeWithApi(phone);
    } catch (e) {
      debugPrint('Failed to send OTP: $e');
      Get.snackbar(
        'Error',
        'Failed to send OTP: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return 'no';
    }
  }

  // Future<String> sendCodeWithFirebase(String phone) async {
  //   try {
  //     await firebaseAuth.verifyPhoneNumber(
  //       phoneNumber: phone,
  //       timeout: const Duration(seconds: 5),
  //       verificationCompleted: (AuthCredential credential) async {
  //         final userCredential = await firebaseAuth.signInWithCredential(credential);
  //         debugPrint('Firebase: Verification completed for ${userCredential.user?.uid}');
  //
  //         final migrateStatus = await checkMigrate();
  //         if (migrateStatus) {
  //           // TODO: นำทางไปหน้า AlreadyMigrate
  //           // Get.offNamed('/already_migrated');
  //         } else {
  //           // TODO: นำทางไปหน้า SetPin
  //           // Get.to(() => SetPin(...));
  //         }
  //         isLoading.value = false;
  //       },
  //       verificationFailed: (FirebaseAuthException exception) {
  //         debugPrint('Firebase: Verification failed: ${exception.code} - ${exception.message}');
  //         Get.snackbar(
  //           'Error',
  //           'Phone number verification failed: ${exception.message}',
  //           backgroundColor: Colors.red,
  //           colorText: Colors.white,
  //         );
  //         isLoading.value = false;
  //       },
  //       codeSent: (String verId, int? forceResendingToken) {
  //         verificationId = verId;
  //         debugPrint('Firebase: Code sent to $phone, verificationId: $verId');
  //         // TODO: นำทางไปหน้า OTP
  //         // Get.off(() => OTPPage(...));
  //       },
  //       codeAutoRetrievalTimeout: (String verId) {
  //         verificationId = verId;
  //         debugPrint('Firebase: Verification timeout');
  //         Get.snackbar(
  //           'Error',
  //           'Verification timed out. Please try again.',
  //           backgroundColor: Colors.red,
  //           colorText: Colors.white,
  //         );
  //         isLoading.value = false;
  //       },
  //     );
  //     return 'otp';
  //   } catch (e) {
  //     debugPrint('Firebase: Failed to send OTP: $e');
  //     Get.snackbar(
  //       'Error',
  //       'Failed to send OTP: $e',
  //       backgroundColor: Colors.red,
  //       colorText: Colors.white,
  //     );
  //     isLoading.value = false;
  //     return 'no';
  //   }
  // }

  Future<String> sendCodeWithApi(String phone) async {
    try {
      final data = {'phone_number': phone};
      final response = await AppApi.post('${AppEnv.apiUrl}/authNoFirebase', data);

      print(response);
      if (response['statusCode'] == 200) {
        debugPrint('API: Code sent to $phone');
        // TODO: นำทางไปหน้า OTP
        // Get.off(() => OTPPage(...));
        return 'otp';
      } else {
        debugPrint('API: Failed to send OTP: ${response['message']}');
        Get.snackbar(
          'Error',
          'Failed to send OTP: ${response['message']}',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return 'no';
      }
    } catch (e) {
      debugPrint('API: Failed to send OTP: $e');
      Get.snackbar(
        'Error',
        'Failed to send OTP: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return 'no';
    } finally {
      isLoading.value = false;
    }
  }

  Future<String> requestOTP(String phone) async {
    try {
      isLoading.value = true;
      final existUserResult = await checkExistUser(phone);
      if (existUserResult != null) {
        return existUserResult;
      }
      return await sendOTP(phone);
    } catch (e) {
      debugPrint('Error in requestOTP: $e');
      Get.snackbar(
        'Error',
        'Failed to process OTP request: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return 'error';
    } finally {
      isLoading.value = false;
    }
  }

  Future<String> verifyOTP(String otpCode) async {
    try {
      isLoading.value = true;

      // switch (roundSMS) {
      //   case ProviderSMS.Firebase:
      //     return await verifyWithFirebase(otpCode);
      //   case ProviderSMS.Twilio:
      //     return await verifyWithApi(usePhone, otpCode, '/verifyOTPNoFirebaseNew');
      //   case ProviderSMS.MKT:
      //     return await verifyWithApi(usePhone, otpCode, '/verifyOTPSmsMKT');
      //   default:
      //     debugPrint('Unsupported provider: $roundSMS');
      //     return 'error';
      // }
      codeVerify = otpCode;
      return await verifyWithApi(usePhone, otpCode, '/verifyOTPNoFirebaseNew');
    } catch (e) {
      debugPrint('Error in verifyOTP: $e');
      Get.snackbar(
        'Error',
        'Failed to verify OTP: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return 'error';
    } finally {
      isLoading.value = false;
    }
  }

  Future<String> verifyWithApi(String phone, String otpCode, String endpoint) async {
    try {
      final data = {
        'phone_number': phone,
        'codeVerify': otpCode.trim(),
      };

      final response = await AppApi.post('${AppEnv.apiUrl}$endpoint', data);

      if (response['statusCode'] == 200) {
        debugPrint('API: OTP verified for $phone');
        // TODO: นำทางไปหน้าถัดไป (เช่น SetPin หรือ Dashboard)
        // Get.to(() => SetPin(...));

        final customToken = response['token'];

        print("UserCredential");
        UserCredential user = await FirebaseAuth.instance
            .signInWithCustomToken(customToken.toString())
            .catchError((error) {

              debugPrint('Firebase: Failed to sign in with custom token: $error');
          // showShortToast(AppLocalizations.of(context)!.translate('otp_incorrect'),
          //     Colors.red);
        });
        //sign in firebase

        print('phoneNumber : ${user.user!.phoneNumber}');

        user.user!.getIdToken().then((value) {
          // if (checkIf == 'register') {
          //   Navigator.pushReplacement(
          //       context,
          //       CupertinoPageRoute(
          //           builder: (context) => SecretPassFirst(
          //               checkIf: checkIf,
          //               roundSMS: smsRound,
          //               codeVerify: passCode,
          //               phoneNumber: phoneNumber,
          //               firstName: firstName,
          //               lastName: lastName,
          //               refCode: refCode)));
          //   // Navigator.push(
          //   //     context,
          //   //     EnterExitRoute(
          //   //         exitPage: OTP_PAGE(),
          //   //         enterPage: ));
          // } else {
          //   Navigator.pushReplacement(
          //     context,
          //     CupertinoPageRoute(
          //         builder: (context) => SetPin(
          //             refCode: refCode,
          //             firstName: firstName,
          //             lastName: lastName,
          //             checkIf: checkIf,
          //             secret: "LikeWallet",
          //             roundSMS: smsRound,
          //             codeVerify: passCode,
          //             phoneNumber: phoneNumber,
          //             pinAgain: false)),
          //   );
          // }
        });

        return 'success';
      } else {
        debugPrint('API: Failed to verify OTP: ${response['message']}');
        Get.snackbar(
          'Error',
          'Failed to verify OTP: ${response['message']}',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return 'no';
      }
    } catch (e) {
      debugPrint('API: Failed to verify OTP: $e');
      Get.snackbar(
        'Error',
        'Failed to verify OTP: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return 'no';
    }
  }

  // Future<String> verifyWithFirebase(String otpCode) async {
  //   try {
  //     final credential = PhoneAuthProvider.credential(
  //       verificationId: verificationId,
  //       smsCode: otpCode.trim(),
  //     );
  //     final userCredential = await firebaseAuth.signInWithCredential(credential);
  //     debugPrint('Firebase: OTP verified for ${userCredential.user?.uid}');
  //
  //     final migrateStatus = await checkMigrate();
  //     if (migrateStatus) {
  //       // TODO: นำทางไปหน้า AlreadyMigrate
  //       // Get.offNamed('/already_migrated');
  //     } else {
  //       // TODO: นำทางไปหน้า SetPin
  //       // Get.to(() => SetPin(...));
  //     }
  //     return 'success';
  //   } catch (e) {
  //     debugPrint('Firebase: Failed to verify OTP: $e');
  //     Get.snackbar(
  //       'Error',
  //       'Failed to verify OTP: $e',
  //       backgroundColor: Colors.red,
  //       colorText: Colors.white,
  //     );
  //     return 'no';
  //   }
  // }

  String RandomString(int strlen) {
    Random rnd = new Random( new DateTime.now().millisecondsSinceEpoch);
    String result = "";
    for (var i = 0; i < strlen; i++) {
      result += chars[rnd.nextInt(chars.length)];
    }
    return result;
  }

  Future<void> createWallet(dataUser) async {
    try{
      print("dataUser");
      print(dataUser!.phoneNumber);

      var secret = Storage.get(StorageKeys.secret);

      String keyEncrypt = RandomString(32);
      String ivEncrypt = RandomString(16);

      Storage.save(StorageKeys.keyEncrypt, keyEncrypt);
      Storage.save(StorageKeys.ivEncrypt, ivEncrypt);
      // var keyEncrypt = Storage.get(StorageKeys.keyEncrypt) ?? "";
      // var ivEncrypt = Storage.get(StorageKeys.ivEncrypt) ?? "";

      Map dataUse = {
        'phone_number': dataUser!.phoneNumber,
        'password': secret == "" || secret == null ? "LikeWallet" : secret,
        'codeVerify': codeVerify,
        'register': 'false',
        'keyEncrypt': keyEncrypt,
        'ivEncrypt': ivEncrypt,
        'refCode': 'no', /// => ตัวแปลนี้ ไม่ได้ถูกเซ็ตเลยใน likewallet เก่า แต่มีช่องให้กรองในหน้า register ซึ่ง ไม่ได้ถูกแทนที่ค่านั้นเลย เลยไม่รู้ว่าเอาไปทำไร แต่มีการเซ็ต null เป็น no
        'numberID': 'no', /// => ตัวแปลนี้ ไม่ได้ถูกเซ็ตเลยใน likewallet เก่า เลยไม่รู้ว่าเอาไปทำไร แต่มีการเซ็ต null เป็น no
      };

      print(dataUse);

      var response = await AppApi.post('${AppEnv.apiUrl}/verifyUserAndCreateNoSecret', dataUse);

      print("verifyUserAndCreateNoSecret");
      if (response['statusCode'] == 204 || response['statusCode'] == 200) {

        print('seed');
        print(response);
        print(response['seed']);

        // await getPrivateKey(response['seed']);
        await setMnemonic(response['seed']);
      } else {
        debugPrint('Secret: Failed to create wallet: ${response['message']}');
      }

    }catch (e) {
      debugPrint('Error creating wallet: $e');
    }
  }

  // Future<void> generateETH() async {
  //   try {
  //     // ดึง mnemonic จาก storage
  //     final mnemonic = await Storage.get(StorageKeys.mnemonic);
  //     if (mnemonic == null || mnemonic.isEmpty) {
  //       throw Exception("Mnemonic not found in storage.");
  //     }
  //
  //     // แปลง mnemonic เป็น seed
  //     String seedHex = bip39.mnemonicToSeedHex(mnemonic);
  //     final root = bip32.BIP32.fromSeed(HEX.decode(seedHex) as Uint8List);
  //     final child = root.derivePath("m/44'/60'/0'/0/0");
  //
  //     // สร้าง private key
  //     final privateKey = HEX.encode(child.privateKey as List<int>);
  //
  //
  //     await Storage.save(StorageKeys.privateKey, privateKey);
  //
  //     // สร้าง public address
  //     final credentials = EthPrivateKey.fromHex(privateKey);
  //     final address = await credentials.extractAddress();
  //     print("Ethereum address: $address");
  //
  //     // บันทึก address ลง storage
  //     await Storage.save(StorageKeys.addressETH, address.hex);
  //   } catch (e) {
  //     print("Error in generateETH: $e");
  //   }
  // }

  String getPrivateKey(String mnemonic) {

    String seed =  bip39.mnemonicToSeedHex(mnemonic);
   // KeyData master = ED25519_HD_KEY.derivePath("m/0'/2147483647'/0'/2147483646'/0'", seed);
   // final privateKey = HEX.encode(master.key);
   // print('mnemonic getPK : '+ mnemonic);
    final root = bip32.BIP32.fromSeed(HEX.decode(seed) as Uint8List);
    final string = root.toBase58();
    final restored = bip32.BIP32.fromBase58(string);
    final child1 = restored.derivePath("m/44'/60'/0'/0/0");
    final privateKey = HEX.encode(child1.privateKey as List<int>);

    print('privateKey : '+ privateKey);

    Storage.save(StorageKeys.privateKey, privateKey);
    return privateKey;
  }

  Future<void> setMnemonic(String value) async {
    print("setMnemonic eiei");
    print(value);
    String mnemonic = await decrypt(value);
    print("mnemonic");
    print(mnemonic);

    await Storage.save(StorageKeys.mnemonic, mnemonic);
    getPrivateKey(mnemonic);
    // await ฆtorage.write(key: 'mnemonic', value: mnemonic);
    // await _preferences.setString("mnemonic", mnemonic);
  }

  Future<String> decrypt(String text) async {

    String keyEncrypt = await Storage.get(StorageKeys.keyEncrypt) ?? "";
    String ivEncrypt = await Storage.get(StorageKeys.ivEncrypt) ?? "";

    final key = Encrypt.Key.fromUtf8(keyEncrypt);
    final iv = Encrypt.IV.fromUtf8(ivEncrypt);
    final encrypter =
    Encrypt.Encrypter(Encrypt.AES(key, mode: Encrypt.AESMode.cbc));
    final decrypted =
    encrypter.decrypt(Encrypt.Encrypted.fromBase64(text), iv: iv);
    return decrypted;
//
  }
}