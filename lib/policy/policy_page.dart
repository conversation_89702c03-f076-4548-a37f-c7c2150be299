import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:likewallet/middleware/callFireStore.dart';
import 'package:likewallet/screen_util.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class Policy extends StatefulWidget {
  const Policy();
  @override
  _PolicyState createState() => new _PolicyState();
}

class _PolicyState extends State<Policy> {
  static const String flutterUrl = 'https://help.likepoint.io/borrow';
  static const String githubUrl = 'https://help.likepoint.io/borrow';
  bool _checkbox = false;
  late OnCallFireStore firestore;
  late BaseAuth auth;
  static const TextStyle linkStyle = const TextStyle(
    color: Colors.blue,
    decoration: TextDecoration.underline,
  );

  late TapGestureRecognizer _flutterTapRecognizer;
  late TapGestureRecognizer _githubTapRecognizer;

  @override
  void initState() {
    super.initState();
    firestore = CallFireStore();
    auth = Auth();
    _flutterTapRecognizer = new TapGestureRecognizer()
      ..onTap = () => _openUrl(flutterUrl);
    _githubTapRecognizer = new TapGestureRecognizer()
      ..onTap = () => _openUrl(githubUrl);
  }

  @override
  void dispose() {
    _flutterTapRecognizer.dispose();
    _githubTapRecognizer.dispose();
    super.dispose();
  }

  void _openUrl(String url) async {
    // Close the about dialog.
    Navigator.pop(context);

    if (await canLaunch(url)) {
      await launch(url);
    } else {
      throw 'Could not launch $url';
    }
  }

  Widget _buildAboutDialog(BuildContext context) {
    return new AlertDialog(
      backgroundColor: LikeWalletAppTheme.bule2,
      title: Text(
        AppLocalizations.of(context)!.translate('borrow_title'),
        style: TextStyle(
            color: Colors.white,
            fontFamily: AppLocalizations.of(context)!.translate('font1'),
            fontSize:
                MediaQuery.of(context).size.height * Screen_util("height", 45),
            fontWeight: FontWeight.normal),
      ),
      content: new Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          _buildAboutText(),
          SizedBox(height: 50.h),
          _buttonAgree(),
          SizedBox(height: 30.h),
          _buttonDeLine()
        ],
      ),
    );
  }

  Widget _buttonAgree() {
    return Container(
      alignment: Alignment.center,
      child: ButtonTheme(
        minWidth: 884.w,
        height: 120.h,
        child: TextButton(
          style: ButtonStyle(
            shape: MaterialStateProperty.all<RoundedRectangleBorder>(
              RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(5.0),
              ),
            ),
            backgroundColor: MaterialStateProperty.resolveWith<Color>(
                  (Set<MaterialState> states) {
                if (states.contains(MaterialState.disabled)) {
                  return LikeWalletAppTheme.bule1; // Disabled color
                }
                return LikeWalletAppTheme.bule1; // Regular color
              },
            ),
          ),
          onPressed: () async {
            var snapshot = await auth.getCurrentUser();
            await firestore.AddPolicyFireStore(
                collection1: 'allowPolicy',
                doc1: 'policy',
                collection2: 'borrow',
                doc2: snapshot!.uid
            );
            Navigator.of(context).pop();
          },
          child: Text(
            AppLocalizations.of(context)!.translate('borrow_agree'),
            style: TextStyle(
                color: Colors.white,
                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                fontSize: MediaQuery.of(context).size.height * Screen_util("height", 45),
                fontWeight: FontWeight.normal
            ),
          ),
        ),
        disabledColor: LikeWalletAppTheme.gray.withOpacity(0.64),
      ),
    );
  }

  Widget _buttonDeLine() {
    return Container(
      alignment: Alignment.center,
      child: ButtonTheme(
        minWidth: 884.w,
        height: 120.h,
        child:TextButton(
          style: ButtonStyle(
            shape: MaterialStateProperty.all<RoundedRectangleBorder>(
              RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(5.0),
              ),
            ),
            backgroundColor: MaterialStateProperty.resolveWith<Color>(
                  (Set<MaterialState> states) {
                if (states.contains(MaterialState.disabled)) {
                  return LikeWalletAppTheme.gray; // Disabled color
                }
                return LikeWalletAppTheme.gray; // Regular color
              },
            ),
          ),
          onPressed: () {
            Navigator.of(context).pop();
          },
          child: Text(
            AppLocalizations.of(context)!.translate('borrow_cancel'),
            style: TextStyle(
              color: Colors.black87,
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              fontSize: MediaQuery.of(context).size.height * Screen_util("height", 45),
              fontWeight: FontWeight.normal,
            ),
          ),
        ),
        disabledColor: LikeWalletAppTheme.gray.withOpacity(0.64),
      ),
    );
  }

  Widget _buildAboutText() {
    return new RichText(
      text: new TextSpan(
        text: '',
        style: TextStyle(
            color: Colors.white,
            fontFamily: AppLocalizations.of(context)!.translate('font1'),
            fontSize:
                MediaQuery.of(context).size.height * Screen_util("height", 39),
            fontWeight: FontWeight.normal),
        children: <TextSpan>[
          TextSpan(
              text: AppLocalizations.of(context)!.translate('borrow_detail') +
                  " "),
          new TextSpan(
            text: AppLocalizations.of(context)!.translate('borrow_read'),
            recognizer: _flutterTapRecognizer,
            style: TextStyle(
                color: Colors.blue,
                decoration: TextDecoration.underline,
                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                fontSize: MediaQuery.of(context).size.height *
                    Screen_util("height", 39),
                fontWeight: FontWeight.normal),
          ),
          const TextSpan(
            text: '  '
                '',
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return new Container(
      padding: const EdgeInsets.only(top: 100.0),
      color: Colors.transparent,
      child: new Column(
        children: <Widget>[
          _buildAboutDialog(context),
        ],
      ),
    );
  }
}
