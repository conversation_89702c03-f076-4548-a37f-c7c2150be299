// import 'package:dash_chat/dash_chat.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:likewallet/libraryman/app_local.dart';

class AppService {
  static String dateFormatByLang(String lang, String date) {
    try {
      var parseDate = DateTime.parse(date);
      if (lang.toString() == "th") {
        List<String> monthList = [
          'มกราคม',
          'กุมภาพันธ์',
          'มีนาคม',
          'เมษายน',
          'พฤษภาคม',
          'มิถุนายน',
          'กรกฎาคม',
          'สิงหาคม',
          'กันยายน',
          'ตุลาคม',
          'พฤศจิกายน',
          'ธันวาคม',
        ];
        var month = parseDate.month;
        return '${parseDate.day} ${monthList[month - 1]} ${parseDate.year}';
      } else {
        return DateFormat("dd MMM yyyy").format(parseDate);
      }
    } catch (e) {
      return "-";
    }
  }

  static String dateTimeByLang(String lang, String date) {
    try {
      var parseDate = DateTime.parse(date);
      if (lang.toString() == "th") {
        return '${DateFormat("HH:MM").format(parseDate)} น.';
      } else {
        return DateFormat("hh:mm a").format(parseDate);
      }
    } catch (e) {
      return "-";
    }
  }

  static Widget statusPOI(
      BuildContext context, String lang, String dateNow, String timeLock) {
    try {
      if (DateTime.parse(timeLock).microsecondsSinceEpoch <=
          DateTime.parse(dateNow).microsecondsSinceEpoch) {
        return Text(
          lang == 'th' ? "ครบกำหนด" : "Available for claim",
          style: TextStyle(
            fontFamily: AppLocalizations.of(context)!.translate('font4'),
            fontSize: 40.sp,
            fontWeight: FontWeight.w600,
            color: Color(0xFF23AF69),
          ),
        );
      } else {
        return Text(
          lang == 'th' ? "ล็อคอยู่" : "Locked",
          style: TextStyle(
            fontFamily: AppLocalizations.of(context)!.translate('font4'),
            fontSize: 40.sp,
            fontWeight: FontWeight.w600,
            color: Color(0xFF0078FF),
          ),
        );
      }
    } catch (e) {
      return Text(
        "-",
        style: TextStyle(
          fontFamily: AppLocalizations.of(context)!.translate('font4'),
          fontSize: 40.sp,
          fontWeight: FontWeight.w600,
          color: Color(0xFF0078FF),
        ),
      );
    }
  }

  static bool checkStatusPOI(
      BuildContext context, String lang, String dateNow, String timeLock) {
    try {
      if (DateTime.parse(timeLock).microsecondsSinceEpoch <=
          DateTime.parse(dateNow).microsecondsSinceEpoch) {
          return true;
      } else {
        return false;
      }
    } catch (e) {
      return false;
    }
  }

  static String activityBUByLang(String lang, String bu) {
    switch (bu) {
      case "MappRPLC":
        return lang == 'th' ? "อาพีแอลซี" : "RPLC";
      case "MappAICP":
        return lang == 'th' ? "เอไอซีพี" : "AICP";
      case "MappRAFCO":
        return lang == 'th' ? "ราฟโค" : "RAFCO";
      case "MappPMS":
        return lang == 'th' ? "ประชากิจ" : "Prachakij";
      case "MappAAM":
        return lang == 'th' ? "เอเอเอ็ม" : "AAM";
      default:
        return "-";
    }
  }


}
