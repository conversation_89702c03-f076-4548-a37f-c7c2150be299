import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:likewallet/controller/setting_controller/setting_controller.dart';
import 'package:likewallet/main.dart';

// คลาสสำหรับจัดการ keys ของ GetStorage
class StorageKeys {
  static const String userId = 'user_id';
  static const String phoneNumber = 'phone_number';
  static const String passcode = 'passcode';
  static const String isLoggedIn = 'is_logged_in';
  static const String language = 'language';
  static const String agreementPolicy = 'agreementPolicy';
  static const String agreementTermsAndCondition = 'agreementTermsAndCondition';
  static const String addressETH = 'addressETH';
  static const String didSetupWallet = 'didSetupWallet';
  static const String mnemonic = 'mnemonic';
  static const String tokenLine = 'tokenLine';
  static const String keyEncrypt = 'keyEncrypt';
  static const String ivEncrypt = 'ivEncrypt';
  static const String privateKey = 'privateKey';
  static const String secret = 'secret';
  static const String currency = 'currency';
  static const String currencyRatio = 'currency_ratio';
  static const String kycActive = 'kycActive';
  static const String login = 'login';
  static const String tierLevel = 'tierLevel';
  static const String actionSyncContact = 'actionSyncContact';
  static const String syncContact = 'syncContact';
  static const String saveSlip = 'saveSlip';
  static const String availableSymbols = 'available_symbols';
}

// Static class สำหรับจัดการ GetStorage
class Storage {
  static final GetStorage _storage = GetStorage();

  // บันทึกข้อมูล
  static Future<void> save(String key, dynamic value) => _storage.write(key, value);

  // อ่านข้อมูล
  static T? get<T>(String key) => _storage.read<T>(key);

  // ลบข้อมูล
  static void remove(String key) => _storage.remove(key);

  // ล้างข้อมูลทั้งหมด
  static void clear() => _storage.erase();

  // ฟังก์ชัน logout
  static Future<void> logout() async {
    try {
      // ล้างข้อมูลทั้งหมดใน Storage
      await _storage.erase();

      // รีเซ็ต GetX controllers และ states
      Get.reset();

      // ใส่ SettingController ใหม่หลังจาก reset
      Get.put(SettingController(), permanent: true);

      // นำทางไปยังหน้า Login และล้าง Navigation Stack
      Get.offAll(() => const MyApp());

    } catch (e, stacktrace) {
      // จัดการ error และแสดง Stacktrace เพิ่มเพื่อ debug ง่ายขึ้น
      print('Logout error: $e');
      print('Stacktrace: $stacktrace');
      rethrow;
    }
  }

}