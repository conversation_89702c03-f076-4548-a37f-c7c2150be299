import 'package:global_configuration/global_configuration.dart';

class AppEnv {
  AppEnv._();

  static String chainId = GlobalConfiguration().getValue("chainId");
  static String apiFee = GlobalConfiguration().getValue("apiFee");
  static String apiCheck = GlobalConfiguration().getValue("apiCheck");
  static String gasClaim = GlobalConfiguration().getValue("gasClaim");
  static String apiSumSub = "https://kyc.likewalletapp.com/user/";
  static String likeapi = "https://likeapi-dev-2cvcjlddla-el.a.run.app";

//  tomochain mainnet
  static String apiUrl = "https://${GlobalConfiguration().getValue("apiUrl")}";
  static String OldAPI = GlobalConfiguration().getValue("OldAPI");
  static String apiLikepointBCT =
      'https://jwt5vh9tqb.execute-api.ap-southeast-1.amazonaws.com';
  static String rpcUrl = GlobalConfiguration().getValue("rpcUrl");
  static String PortfolioUrl = GlobalConfiguration().getValue("PortfolioUrl");
  static String wsUrl = GlobalConfiguration().getValue("wsUrl");
  static String contractLock = GlobalConfiguration().getValue("contractLock");
  static String contractLike = GlobalConfiguration().getValue("contractLike");
  //new
  static String contractLotteryLock =
  GlobalConfiguration().getValue("contractLotteryLock");
  static String contractNFT = GlobalConfiguration().getValue("contractNFT");
  static String lottery = GlobalConfiguration().getValue("lottery");
  static String abi_contractLotteryLock =
  GlobalConfiguration().getValue("abi_contractLotteryLock");
  static String abi_contractNFT =
  GlobalConfiguration().getValue("abi_contractNFT");
  static String abi_lottery = GlobalConfiguration().getValue("abi_lottery");

  static String contractAirdrop =
  GlobalConfiguration().getValue("contractAirdrop");
  static String contractSlotMachine =
  GlobalConfiguration().getValue("contractSlotMachine");
  static String contractMessage =
  GlobalConfiguration().getValue("contractMessage");
  static String contractLoan = GlobalConfiguration().getValue("contractLoan");

  static String abiContractLike =
  GlobalConfiguration().getValue("abiContractLike");
  static String abiContractLock =
  GlobalConfiguration().getValue("abiContractLock");
  static String abiContractAirdrop =
  GlobalConfiguration().getValue("abiContractAirdrop");
  static String abiContractSlot =
  GlobalConfiguration().getValue("abiContractSlot");
  static String abiContractLoan =
  GlobalConfiguration().getValue("abiContractLoan");
  static String abiContractMessage =
  GlobalConfiguration().getValue("abiContractMessage");
  static String APIKEY = GlobalConfiguration().getValue("APIKEY");
  static String SECRETKEY = GlobalConfiguration().getValue("SECRETKEY");

}