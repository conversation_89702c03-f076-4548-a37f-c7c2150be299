import 'dart:convert';

import 'package:http/http.dart' as http;

class AppApi {

  static Future<dynamic> post(String url, Map jsonMap, [String? xKey]) async {
    try {
      var urlApi = Uri.parse(url);

      Map<String, String> requestHeaders = {
        'Content-Type': 'application/json',
        if (xKey != null) 'x-api-key': xKey,
      };

      var response = await http.post(
        urlApi,
        headers: requestHeaders,
        body: jsonEncode(jsonMap),
      );

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception('Failed to load data: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error during POST request: $e');
    }
  }

  static Future<dynamic> get(String url) async {
    try {
      var urlApi = Uri.parse(url);
      var response = await http.get(urlApi);
      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception('Failed to load data: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error during GET request: $e');
    }
  }



}