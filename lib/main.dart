
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:global_configuration/global_configuration.dart';
import 'package:likewallet/controller/setting_controller/lazyPutController.dart';
import 'package:likewallet/controller/setting_controller/setting_controller.dart';
import 'package:likewallet/controller/translate/translate_controller.dart';
import 'package:likewallet/view/login/index.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await GetStorage.init();
  // SystemChrome.setPreferredOrientations([
  //   DeviceOrientation.portraitUp,
  //   DeviceOrientation.portraitDown,
  // ]);

  await Firebase.initializeApp();

  FirebaseMessaging.onBackgroundMessage(_messageHandler);

  try {
    await GlobalConfiguration()
        .loadFromUrl("https://new.likepoint.io/configAPInew");
  } catch (e) {
    // something went wrong while fetching the config from the url ... do something
  }
  // lazy put controller
  await AppBindings.lazyLoadControllers();
  runApp(const MyApp());
}

Future<void> _messageHandler(RemoteMessage message) async {
  if (kDebugMode) {
    print('background message ${message.notification?.body.toString()}');
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {

    Get.put(SettingController(), permanent: true);

    return ScreenUtilInit(
      designSize: const Size(402, 874),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context , child) {
        return GetMaterialApp(
          builder: EasyLoading.init(),
          debugShowCheckedModeBanner: false,
          title: 'Likewallet',
          theme: ThemeData(
            fontFamily: 'Proxima Nova',
            scaffoldBackgroundColor: Colors.white70,
            primaryColor: const Color(0xFFFF8500),
            colorScheme:
            ColorScheme.fromSwatch().copyWith(secondary: Colors.amber),
          ),
          supportedLocales: const [
            Locale('en'),
            Locale('th'),
            Locale('lo'),
            Locale('km'),
          ],
          locale: const Locale('en'),
          fallbackLocale: const Locale('en'),
          localizationsDelegates: const [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          translations: TranslationsService(),
          home: const Stack(
            children: <Widget>[
              HomePage(),
              // Notify(),
            ],
          ),
        );
      },
    );
  }
}

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusNode().unfocus();
      },
      child: const IndexLike(),
    );
  }
}

// class Notify extends StatefulWidget {
//   @override
//   _NotifyState createState() => _NotifyState();
// }
//
// class _NotifyState extends State<Notify> {
//   RxString _homeScreenText = "Waiting for token...".obs;
//   final SecureStorage secureStorage = SecureStorage();
//
//   RxDouble heightAnimate = 0.0.obs;
//
//   Future<void> getAPNToken(_fcm) async {
//     if (Platform.isIOS) {
//       String? apnsToken = await _fcm.getAPNSToken();
//       if (apnsToken != null) {
//         await _fcm.subscribeToTopic('K4QBZ5ZDCH');
//       } else {
//         await Future<void>.delayed(
//           const Duration(
//             seconds: 3,
//           ),
//         );
//         apnsToken = await _fcm.getAPNSToken();
//         if (apnsToken != null) {
//           await _fcm.subscribeToTopic('K4QBZ5ZDCH');
//         }
//       }
//     } else {
//       await _fcm.subscribeToTopic('K4QBZ5ZDCH');
//     }
//   }
//
//   Future<void> subscribeToTopicLikePoint(_fcm) async {
//     await _fcm.subscribeToTopic('LIKE_POINT_2_0');
//   }
//
//   @override
//   void initState() {
//     super.initState();
//     final _fcm = FirebaseMessaging.instance;
//     subscribeToTopicLikePoint(_fcm);
//     if(Platform.isIOS){
//       getAPNToken(_fcm);
//     }
//     _fcm.setForegroundNotificationPresentationOptions(
//       sound: true,
//       badge: true,
//       alert: true,
//     );
//     _fcm.getToken().then((token) {
//       if (token != null && token.isNotEmpty) {
//         setTokenNotify(token);
//         setState(() {
//           _homeScreenText.value = "Push Messaging token: $token";
//           print(_homeScreenText.value);
//         });
//       }
//     });
//
//     FirebaseMessaging.onMessage.listen((RemoteMessage remoteMessage) async {
//       final carRepairCtl = Get.put(CarRepairController());
//       carRepairCtl.getCarRepairStatus();
//       Get.snackbar(
//         remoteMessage.notification!.title.toString(),
//         remoteMessage.notification!.body.toString(),
//         snackPosition: SnackPosition.TOP,
//         snackStyle: SnackStyle.FLOATING,
//         duration: const Duration(seconds: 7),
//         padding: const EdgeInsets.only(top: 10, left: 6, right: 6, bottom: 10),
//         icon: Container(
//           width: 50,
//           height: 50,
//           alignment: Alignment.topLeft,
//           margin: const EdgeInsets.only(left: 10,right: 6),
//           child: ClipRRect(
//             borderRadius: BorderRadius.circular(5), // Adj
//             child: Image.asset(
//               "assets/icon/icon.png",
//             ),
//           ),
//         ),
//         mainButton: remoteMessage.data['type'].toString() != '1' ?TextButton(
//           onPressed: () async {
//
//             Get.back();
//             // print( remoteMessage.data['type'].toString());
//
//             await eSignatureCtrl.changeUsePDF(remoteMessage.data['type'].toString());
//             await eSignatureCtrl.changeShowPDF(remoteMessage.data['type'].toString());
//             await eSignatureCtrl.getCurrentNotiByRunning(profileCtl.profile.value.mobile.toString());
//             Get.to(() => SigDetail());
//           },
//           child: Text(
//             'รายละเอียด',
//             style: TextStyle(color: Colors.black, fontSize: 12),
//           ),
//         ) : null,
//       );
//     });
//   }
//
//
//   setTokenNotify(tokenNotify) async {
//     await secureStorage.writeSecureData('tokenNotify', tokenNotify.toString());
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     // TODO: implement build
//     return Container();
//   }
//
// }
