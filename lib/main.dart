import 'dart:async';
import 'dart:io' as io;
import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:firebase_analytics/observer.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_jailbreak_detection/flutter_jailbreak_detection.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/all.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:likewallet/Theme.dart';
import 'package:likewallet/menu/playGame.dart';
import 'package:likewallet/middleware/check_net/check_net.dart';
import 'package:likewallet/quickpay/favorites/search_list.dart';

import 'package:likewallet/quickpay/stores.dart';
import 'package:likewallet/quickpay/scanPay.dart';
import 'package:likewallet/libraryman/app_local.dart';

import 'package:flutter_native_timezone/flutter_native_timezone.dart';
import 'package:likewallet/screen/home.dart';
import 'package:likewallet/screen/index.dart';
import 'package:likewallet/screen/choice_user.dart';
import 'package:likewallet/login/confirmOTP.dart';
import 'package:likewallet/login/email_singin.dart';
import 'package:likewallet/screen/navigationbar/home/<USER>';
import 'package:likewallet/screen/registerForm.dart';
import 'package:likewallet/login/sing_in.dart';
import 'package:likewallet/screen_util.dart';

import 'package:likewallet/splash_screen.dart';
import 'package:likewallet/tabslide/language.dart';
import 'package:likewallet/screen/navigationbar/messages.dart';

import 'package:likewallet/jailbroken_page/jailbroken_page.dart';
import 'package:likewallet/bank/banking.dart';
import 'package:likewallet/bank/favoriteTakePhoto.dart';
import 'package:likewallet/screen/touch_id_singin.dart';
import 'package:likewallet/screen/navigationbar/history.dart';
import 'package:likewallet/screen/navigationbar/refer/refer.dart';
import 'package:likewallet/tabslide/currency.dart';
import 'package:likewallet/bank/trueMoney_OTP.dart';
import 'package:likewallet/bank/creditCard.dart';
import 'package:likewallet/bank/confirm_transection.dart';
import 'package:likewallet/bank/complete.dart';
import 'package:likewallet/login/forgetPassword.dart';
import 'package:likewallet/login/resetUser.dart';
import 'package:likewallet/menu/hourlyRewards.dart';
import 'package:likewallet/menu/LockLIKE_New.dart';

import 'package:firebase_analytics/firebase_analytics.dart';

import 'package:likewallet/libraryman/applang.dart';
import 'package:likewallet/screen/invest.dart';
import 'package:likewallet/kyc/kyc.dart';

import 'dart:ui';
import 'package:flutter/cupertino.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:likewallet/test.dart' as test;
import 'package:permission_handler/permission_handler.dart';
import 'package:rxdart/subjects.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as riverpod;
import 'package:provider/provider.dart' as old_provider hide ReadContext;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:global_configuration/global_configuration.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:timezone/timezone.dart' as tz;
import 'package:uni_links/uni_links.dart';

//
//
final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
    FlutterLocalNotificationsPlugin();
//
// Streams are created so that app can respond to notification-related events since the plugin is initialised in the `main` function
final BehaviorSubject<ReceivedNotification> didReceiveLocalNotificationSubject =
    BehaviorSubject<ReceivedNotification>();

final BehaviorSubject<String> selectNotificationSubject =
    BehaviorSubject<String>();

int _coins = 0;

final amount = riverpod.StateProvider((ref) => 0.00);
final pay = riverpod.StateProvider((ref) => false);
final totalAmount = riverpod.StateProvider((ref) => 0.00);

class ReceivedNotification {
  final int id;
  final String? title;
  final String? body;
  final String? payload;
  ReceivedNotification({required this.id, this.title, this.body, this.payload});
}

//
String? selectedNotificationPayload;

///ประกาศตัวแปรจำนวน notify
final countNotify = StateProvider((ref) => 0);

var tierLevel = StateProvider((ref) => '');
var userLevel = StateProvider((ref) => '');

final checkNet = StateProvider((ref) => false);

bool USE_FIRESTORE_EMULATOR = false;
//
// void main() async {
//   WidgetsFlutterBinding.ensureInitialized();
//   await Firebase.initializeApp();
//   try {
//     await GlobalConfiguration()
//         .loadFromUrl("https://new.likepoint.io/configAPInew");
//   } catch (e) {
//     // something went wrong while fetching the config from the url ... do something
//   }
//   if (USE_FIRESTORE_EMULATOR) {
//     FirebaseFirestore.instance.settings = Settings(
//         host: 'localhost:8080', sslEnabled: false, persistenceEnabled: false);
//   }
//   AppLanguage appLanguage = AppLanguage();
//   await appLanguage.fetchLocale();
//   SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
//
//   WidgetsFlutterBinding.ensureInitialized();
//
//   await _configureLocalTimeZone();
//
//   final NotificationAppLaunchDetails? notificationAppLaunchDetails =
//       await flutterLocalNotificationsPlugin.getNotificationAppLaunchDetails();
//   // String initialRoute = HomePage.routeName;
//   // if (notificationAppLaunchDetails?.didNotificationLaunchApp ?? false) {
//   //   selectedNotificationPayload = notificationAppLaunchDetails!.payload;
//   //   initialRoute = SecondPage.routeName;
//   // }
//
//   const AndroidInitializationSettings initializationSettingsAndroid =
//       AndroidInitializationSettings('app_icon');
//
//   /// Note: permissions aren't requested here just to demonstrate that can be
//   /// done later
//   final IOSInitializationSettings initializationSettingsIOS =
//       IOSInitializationSettings(
//           requestAlertPermission: false,
//           requestBadgePermission: false,
//           requestSoundPermission: false,
//           onDidReceiveLocalNotification:
//               (int id, String? title, String? body, String? payload) async {
//             didReceiveLocalNotificationSubject.add(ReceivedNotification(
//                 id: id, title: title, body: body, payload: payload));
//           });
//   const MacOSInitializationSettings initializationSettingsMacOS =
//       MacOSInitializationSettings(
//           requestAlertPermission: false,
//           requestBadgePermission: false,
//           requestSoundPermission: false);
//   final InitializationSettings initializationSettings = InitializationSettings(
//       android: initializationSettingsAndroid,
//       iOS: initializationSettingsIOS,
//       macOS: initializationSettingsMacOS);
//   await flutterLocalNotificationsPlugin.initialize(initializationSettings,
//       onSelectNotification: (String? payload) async {
//     if (payload != null) {
//       debugPrint('notification payload: $payload');
//     }
//     selectedNotificationPayload = payload;
//     selectNotificationSubject.add(payload!);
//   });
//
//   runApp(riverpod.ProviderScope(
//       child: MyApp(
//     appLanguage: appLanguage,
//   )));
// }
//
// Future<void> _configureLocalTimeZone() async {
//   tz.initializeTimeZones();
//   final String? timeZoneName = await FlutterNativeTimezone.getLocalTimezone();
//   tz.setLocalLocation(tz.getLocation(timeZoneName!));
// }
//
// class MyApp extends StatelessWidget {
//   final AppLanguage appLanguage;
//   late SharedPreferences sharedPreferences;
//   MyApp({required this.appLanguage});
//   final List mnemonic = [];
//   // final List mnemonic = new List();
//   FirebaseAnalytics analytics = FirebaseAnalytics.instance;
//
//   @override
//   Widget build(BuildContext context) {
//     return old_provider.ChangeNotifierProvider<AppLanguage>(
//       create: (_) => appLanguage,
//       child:
//           old_provider.Consumer<AppLanguage>(builder: (context, model, child) {
//         return ScreenUtilInit(
//           designSize: Size(1080, 2340),
//           builder: () => MaterialApp(
//             debugShowCheckedModeBanner: false,
//             // locale: model.appLocal == null ? Locale('en') : model.appLocal,
//             supportedLocales: [
//               Locale('en', 'US'),
//               Locale('th', ''),
//               Locale('vi', ''),
//               Locale('km', ''),
//               Locale('lo'),
//             ],
//             localizationsDelegates: [
//               AppLocalizations.delegate,
//               GlobalMaterialLocalizations.delegate,
//               GlobalWidgetsLocalizations.delegate,
//               GlobalCupertinoLocalizations.delegate
//             ],
//             title: "Likewallet",
//             theme: new ThemeData(
//               primarySwatch: Colors.blue,
//             ),
//             home: MyHomePage(),
//             routes: <String, WidgetBuilder>{
//               '/index': (BuildContext context) => new IndexLike(),
//               '/home': (BuildContext context) => new HomeLikewallet(),
//               '/homeScreen': (BuildContext context) => new HomeScreen(),
//               '/register': (BuildContext context) => new REGISTER_FORM(),
//               '/choice_user': (BuildContext context) => new ChoiceUser(),
//               '/sing_in': (BuildContext context) => new SING_IN(),
//               '/singinForm': (BuildContext context) => new EMAIL_SINGIN(),
//               '/confirmOTP': (BuildContext context) => new OTP_PAGE(),
//               '/touchID': (BuildContext context) => new TOUCN_ID_SINGIN(),
//               '/firstHome': (BuildContext context) => new HomeScreen(),
//               '/refer': (BuildContext context) => new ReferPage(),
//               '/history': (BuildContext context) => new HistoryPage(),
//               '/messages': (BuildContext context) => new MassagesPage(),
//               '/bank': (BuildContext context) => new Banking(),
//               '/confirmTransection': (BuildContext context) =>
//                   new confirmTransection(),
//               // '/billTransection': (BuildContext context) => new billTransection(),
//               '/forgetPassword': (BuildContext context) => new forgetPassword(),
//               // '/test': (BuildContext context) => new MyApp(),
//               '/resetUser': (BuildContext context) => new resetUser(),
//               // '/netBank': (BuildContext context) => new NetBanking(),
//               // '/trueMoney': (BuildContext context) => new trueMoney(),
//               // '/promptpay': (BuildContext context) => new PromptPay(),
//               '/trueMoney_OTP': (BuildContext context) => new trueMoney_OTP(),
//               '/creditCard': (BuildContext context) => new creditCard(),
//               '/completeTransaction': (BuildContext context) =>
//                   new CompleteTransaction(),
//               '/hourlyRewards': (BuildContext context) => new hourlyRewards(),
//               '/lockLike': (BuildContext context) => new lockLike(),
//               // '/choiceTopay': (BuildContext context) => new ChoiceTopay(),
//               // '/confirmCash': (BuildContext context) => new ConfirmCash(),
//               '/currency': (BuildContext context) => new Currency(),
//               '/invest': (BuildContext context) => new Invest(),
//               // '/adsRewards': (BuildContext context) => new adsRewards(),
//               // '/partners': (BuildContext context) => new Partners(),
//               '/language': (BuildContext context) => new Language(),
//               '/kyc': (BuildContext context) => new KYC(),
//               // '/takePhoto': (BuildContext context) => new takePhoto(),
//               '/scanPay': (BuildContext context) => new scanPay(),
//               '/stores': (BuildContext context) => new stores(),
// //            '/text': (BuildContext context) => new TEST(),
//               "crop_page": (context) => FavoriteTakePhoto(),
//               '/listFavorites': (BuildContext context) => new UserFilterDemo(),
//               // '/slotMachine': (BuildContext context) => new SlotMachine(),
//               '/playGame': (BuildContext context) => new PlayGame(),
//               // '/buyTicket': (BuildContext context) => new buyTicket(),
//             },
//             navigatorObservers: [
//               FirebaseAnalyticsObserver(analytics: analytics),
//             ],
//           ),
//         );
//       }),
//     );
//   }
// }
//
// class MyHomePage extends StatefulWidget {
//   @override
//   _MyHomePageState createState() => _MyHomePageState();
// }
//
// class _MyHomePageState extends State<MyHomePage> with TickerProviderStateMixin {
//   late StreamSubscription<ConnectivityResult> subscription;
//   String status = '';
//   bool first = false;
//   late SharedPreferences sharedPreferences;
//
//   final _scaffoldKey = GlobalKey();
//   StreamSubscription? _sub;
//   Uri? _initialUri;
//   Uri? _latestUri;
//   Object? _err;
//
//   @override
//   void initState() {
//     super.initState();
//     _handleIncomingLinks();
//     _handleInitialUri();
//     checkFirst();
//     setLang();
//     initPlatformState();
//   }
//
//   @override
//   void dispose() {
//     _sub?.cancel();
//     super.dispose();
//   }
//
//   void _handleIncomingLinks() {
//     if (!kIsWeb) {
//       // It will handle app links while the app is already started - be it in
//       // the foreground or in the background.
//       _sub = uriLinkStream.listen((Uri? uri) {
//         if (!mounted) return;
//         print('got uri: $uri');
//         Navigator.pushReplacement(
//             context,
//             MaterialPageRoute(
//                 builder: (context) => test.MyApp(
//                       uri: uri,
//                     )));
//         setState(() {
//           _latestUri = uri;
//           _err = null;
//         });
//       }, onError: (Object err) {
//         if (!mounted) return;
//         print('got err: $err');
//         setState(() {
//           _latestUri = null;
//           if (err is FormatException) {
//             _err = err;
//           } else {
//             _err = null;
//           }
//         });
//       });
//     }
//   }
//
//   Future<void> _handleInitialUri() async {
//     // In this example app this is an almost useless guard, but it is here to
//     // show we are not going to call getInitialUri multiple times, even if this
//     // was a weidget that will be disposed of (ex. a navigation route change).
//     if (!_initialUriIsHandled) {
//       _initialUriIsHandled = true;
//
//       print('_handleInitialUri called');
//       try {
//         final uri = await getInitialUri();
//         if (uri == null) {
//           print('no initial uri');
//         } else {
//           Navigator.pushReplacement(
//             context,
//             MaterialPageRoute(
//                 builder: (context) => Container(
//                       color: Colors.red,
//                     )),
//           );
//           print('got initial uri: $uri');
//         }
//         if (!mounted) return;
//         setState(() => _initialUri = uri);
//       } on PlatformException {
//         // Platform messages may fail but we ignore the exception
//         print('falied to get initial uri');
//       } on FormatException catch (err) {
//         if (!mounted) return;
//         print('malformed initial uri');
//         setState(() => _err = err);
//       }
//     }
//   }
//
//   Future<void> initPlatformState() async {
//     bool jailbroken;
//     bool developerMode;
//     // Platform messages may fail, so we use a try/catch PlatformException.
//     try {
//       jailbroken = await FlutterJailbreakDetection.jailbroken;
//       developerMode = await FlutterJailbreakDetection.developerMode;
//     } on PlatformException {
//       jailbroken = true;
//       developerMode = true;
//     }
//
//     // If the widget was removed from the tree while the asynchronous platform
//     // message was in flight, we want to discard the reply rather than calling
//     // setState to update our non-existent appearance.
//     if (!mounted) return;
//
//     if (jailbroken || developerMode) {
//       Navigator.push(
//         context,
//         MaterialPageRoute(builder: (context) => JailBrokenPage()),
//       );
//     }
//   }
//
//   setLang() async {
//     sharedPreferences = await SharedPreferences.getInstance();
//     if (sharedPreferences.getString('language_code') == null) {
//       sharedPreferences.setString('language_code', "en");
//     }
//   }
//
//   Future checkFirst() async {
//     var connectivityResult = await (Connectivity().checkConnectivity());
//     if (connectivityResult == ConnectivityResult.none) {
//       print(connectivityResult);
//       if (!mounted) return;
//       setState(() => context.read(checkNet).state = true);
//     } else {
//       subscription = Connectivity()
//           .onConnectivityChanged
//           .listen((ConnectivityResult result) {
//         print(result);
//         if (result == ConnectivityResult.none) {
//           setState(() => context.read(checkNet).state = true);
//           showShortToast('Disconnected', Colors.grey);
//         }
//         if (result == ConnectivityResult.wifi) {
//           setState(() => context.read(checkNet).state = false);
//           showShortToast('Connected', LikeWalletAppTheme.bule1);
//         }
//         if (result == ConnectivityResult.mobile) {
//           setState(() => context.read(checkNet).state = false);
//           showShortToast('Connected', LikeWalletAppTheme.bule1);
//         }
//       });
//     }
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       // body:
//       // Center(
//       //   child: Column(
//       //     mainAxisAlignment: MainAxisAlignment.center,
//       //     children: <Widget>[
//       //       Text(
//       //           'Jailbroken: ${_jailbroken == null ? "Unknown" : _jailbroken ? "YES" : "NO"}'),
//       //       Text(
//       //           'Developer mode: ${_developerMode == null ? "Unknown" : _developerMode ? "YES" : "NO"}')
//       //     ],
//       //   ),
//       // ),
//       body:
//           context.read(checkNet).state ? CheckNetWorkScreen() : SplashScreen(),
//     );
//   }
//
//   void _showSnackBar(String msg) {
//     WidgetsBinding.instance?.addPostFrameCallback((_) {
//       final context = _scaffoldKey.currentContext;
//       if (context != null) {
//         ScaffoldMessenger.of(context).showSnackBar(SnackBar(
//           content: Text(msg),
//         ));
//       }
//     });
//   }
// }

bool _initialUriIsHandled = false;

// class MyHttpOverrides extends io.HttpOverrides {
//   @override
//   io.HttpClient createHttpClient(SecurityContext context) {
//     return super.createHttpClient(context)
//       ..badCertificateCallback =
//           (X509Certificate cert, String host, int port) => true;
//   }
// }
class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}

void main() async {
  io.HttpOverrides.global = MyHttpOverrides();
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  try {
    await GlobalConfiguration()
        .loadFromUrl("https://new.likepoint.io/configAPInew");
  } catch (e) {
    // something went wrong while fetching the config from the url ... do something
  }
  if (USE_FIRESTORE_EMULATOR) {
    FirebaseFirestore.instance.settings = Settings(
        host: 'localhost:8080', sslEnabled: false, persistenceEnabled: false);
  }
  AppLanguage appLanguage = AppLanguage();
  await appLanguage.fetchLocale();
  SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

  WidgetsFlutterBinding.ensureInitialized();

  await _configureLocalTimeZone();

  final NotificationAppLaunchDetails? notificationAppLaunchDetails =
      await flutterLocalNotificationsPlugin.getNotificationAppLaunchDetails();
  // String initialRoute = HomePage.routeName;
  // if (notificationAppLaunchDetails?.didNotificationLaunchApp ?? false) {
  //   selectedNotificationPayload = notificationAppLaunchDetails!.payload;
  //   initialRoute = SecondPage.routeName;
  // }

  const AndroidInitializationSettings initializationSettingsAndroid =
      AndroidInitializationSettings('app_icon');

  /// Note: permissions aren't requested here just to demonstrate that can be
  /// done later
  final DarwinInitializationSettings initializationSettingsIOS =
  DarwinInitializationSettings(
          requestAlertPermission: false,
          requestBadgePermission: false,
          requestSoundPermission: false,
          onDidReceiveLocalNotification:
              (int id, String? title, String? body, String? payload) async {
            didReceiveLocalNotificationSubject.add(ReceivedNotification(
                id: id, title: title, body: body, payload: payload));
          });
  final InitializationSettings initializationSettings = InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS);
  await flutterLocalNotificationsPlugin.initialize(initializationSettings,
      onDidReceiveNotificationResponse: (payload) async {
    if (payload.payload != null) {
      debugPrint('notification payload: $payload.payload');
    }
    selectedNotificationPayload = payload.payload;
    selectNotificationSubject.add(payload.payload!);
  });

  FirebaseAnalytics analytics = FirebaseAnalytics.instance;
  SharedPreferences pref = await SharedPreferences.getInstance();
  pref.setBool('checkMigrateStopper', false);
  runApp(riverpod.ProviderScope(
    child: old_provider.ChangeNotifierProvider<AppLanguage>(
      create: (_) => appLanguage,
      child: old_provider.Consumer<AppLanguage>(
        builder: (context, model, child) {
          return ScreenUtilInit(
              designSize: Size(1080, 2340),
              builder: (context, child) => MaterialApp(
                    debugShowCheckedModeBanner: false,
                    locale:
                        model.appLocal == null ? Locale('en') : model.appLocal,
                    supportedLocales: [
                      Locale('en', 'US'),
                      Locale('th', ''),
                      Locale('vi', ''),
                      Locale('km', ''),
                      Locale('lo'),
                    ],
                    localizationsDelegates: [
                      AppLocalizations.delegate,
                      GlobalMaterialLocalizations.delegate,
                      GlobalWidgetsLocalizations.delegate,
                      GlobalCupertinoLocalizations.delegate
                    ],
                    title: "Likewallet",
                    theme: new ThemeData(
                      primarySwatch: Colors.blue,
                    ),
                    builder: EasyLoading.init(),
                    routes: <String, WidgetBuilder>{
                      '/index': (BuildContext context) => new IndexLike(),
                      '/home': (BuildContext context) => new HomeLikewallet(),
                      '/homeScreen': (BuildContext context) => new HomeScreen(),
                      '/register': (BuildContext context) =>
                          new REGISTER_FORM(),
                      '/choice_user': (BuildContext context) =>
                          new ChoiceUser(),
                      '/sing_in': (BuildContext context) => new SING_IN(),
                      '/singinForm': (BuildContext context) =>
                          new EMAIL_SINGIN(),
                      '/confirmOTP': (BuildContext context) => new OTP_PAGE(),
                      '/touchID': (BuildContext context) =>
                          new TOUCN_ID_SINGIN(),
                      '/firstHome': (BuildContext context) => new HomeScreen(),
                      '/refer': (BuildContext context) => new ReferPage(),
                      '/history': (BuildContext context) => new HistoryPage(),
                      '/messages': (BuildContext context) => new MassagesPage(),
                      '/bank': (BuildContext context) => new Banking(),
                      '/confirmTransection': (BuildContext context) =>
                          new confirmTransection(),
                      '/forgetPassword': (BuildContext context) =>
                          new forgetPassword(),
                      '/resetUser': (BuildContext context) => new resetUser(),
                      '/trueMoney_OTP': (BuildContext context) =>
                          new trueMoney_OTP(),
                      '/creditCard': (BuildContext context) => new creditCard(),
                      '/completeTransaction': (BuildContext context) =>
                          new CompleteTransaction(),
                      // '/hourlyRewards': (BuildContext context) =>
                      //     new hourlyRewards(),
                      '/lockLike': (BuildContext context) => new lockLike(),
                      '/currency': (BuildContext context) => new Currency(),
                      '/invest': (BuildContext context) => new Invest(),
                      '/language': (BuildContext context) => new Language(),
                      '/kyc': (BuildContext context) => new KYC(),
                      '/scanPay': (BuildContext context) => new scanPay(),
                      '/stores': (BuildContext context) => new stores(),
                      "crop_page": (context) => FavoriteTakePhoto(),
                      '/listFavorites': (BuildContext context) =>
                          new UserFilterDemo(),
                      '/playGame': (BuildContext context) => new PlayGame(),
                    },
                    navigatorObservers: [
                      FirebaseAnalyticsObserver(analytics: analytics),
                    ],
                    home:  MyApp(),
                  ));
        },
      ),
    ),
  ));
  // runApp( MyApp(
  //       appLanguage: appLanguage,
  //     )));
}

Future<void> _configureLocalTimeZone() async {
  tz.initializeTimeZones();
  final String? timeZoneName = await FlutterNativeTimezone.getLocalTimezone();
  tz.setLocalLocation(tz.getLocation(timeZoneName!));
}

class MyApp extends StatefulWidget {
  @override
  _MyAppState createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with SingleTickerProviderStateMixin {
  final List mnemonic = [];
  StreamSubscription? _sub;
  late IConnectivity iConnectivity;

  @override
  void initState() {
    requestNotificationPermissions();
    super.initState();
    initPlatformState();
    iConnectivity = CallConnectivity();
  }

  Future<void> requestNotificationPermissions() async {
    var res = await Permission.notification.request();
  }

  Future<void> initPlatformState() async {
    bool jailbroken;
    bool developerMode;
    // Platform messages may fail, so we use a try/catch PlatformException.
    try {
      jailbroken = await FlutterJailbreakDetection.jailbroken;
      developerMode = await FlutterJailbreakDetection.developerMode;
    } on PlatformException {
      jailbroken = true;
      developerMode = true;
    }

    // If the widget was removed from the tree while the asynchronous platform
    // message was in flight, we want to discard the reply rather than calling
    // setState to update our non-existent appearance.
    if (!mounted) return;

    if (jailbroken || developerMode) {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => JailBrokenPage()),
      );
    } else {
      //Check Net
      checkNetwork();
    }
  }

  Future checkNetwork() async {
    var connectivityResult = await (Connectivity().checkConnectivity());
    if (connectivityResult == ConnectivityResult.none) {
      print(connectivityResult);
      if (!mounted) return;
      setState(() => context.read(checkNet).state = true);
    } else {
      iConnectivity.callConnectivityListen();
    }
  }

  @override
  void dispose() {
    _sub?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () async {
          return true;
        },
        child: SplashScreen());
  }
}
