import 'package:flutter/cupertino.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/animationPage.dart';
import 'package:likewallet/bank/popup/popup_theme.dart';
import 'package:likewallet/change_phone/change_phone.dart';

import 'package:likewallet/libraryman/app_local.dart';
import 'dart:io';
import 'package:flutter/material.dart';
import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/rendering.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:likewallet/screen_util.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:firebase_storage/firebase_storage.dart' as firebase_storage;
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:path/path.dart' as Path;
import 'package:image_picker/image_picker.dart';
import '../screen_util.dart';
import 'dart:io' as io;

class FbCloneProfileStful extends StatefulWidget {
  _FbCloneProfileState createState() => _FbCloneProfileState();
}

class _FbCloneProfileState extends State<FbCloneProfileStful> {
  late String dropdownValue;
  int count = 0;

  late BaseAuth auth;
  String firstName = "..loading";
  String lastName = "";
  String phoneNumber = "";

  final ImagePicker _picker = ImagePicker();
  String currentPhone = '..loading';

  TextEditingController editFirstName = TextEditingController();
  TextEditingController editLastName = TextEditingController();
  TextEditingController editPhoneNumber = TextEditingController();
  TextEditingController editEmail = TextEditingController();
  TextEditingController editPhone = TextEditingController();
  TextEditingController editFacebook = TextEditingController();
  TextEditingController editLine = TextEditingController();
  bool _saving = false;
  late File _image;
  int refers = 0;
  String _uploadedFileURL = "noprofile";
  @override
  void initState() {
    super.initState();
    auth = new Auth();
    setState(() {
      _saving = true;
    });
    setInit();
  }

  Future chooseFile() async {
    var image = await _picker.getImage(
      source: ImageSource.gallery,
      imageQuality: 80,
      maxHeight: 400,
      maxWidth: 400,
    );
    setState(() {
      _image = File(image!.path);
    });

    if (image!.path != null) {
      return true;
    } else {
      return false;
    }
  }

  Future<bool> uploadFile() async {
    setState(() {
      _saving = true;
    });

    firebase_storage.Reference storageReference = firebase_storage
        .FirebaseStorage.instance
        .ref()
        .child('profile/${Path.basename(_image.path)}}');
    final metadata = firebase_storage.SettableMetadata(
        contentType: 'image/jpeg',
        customMetadata: {'picked-file-path': _image.path});
    firebase_storage.UploadTask uploadTask =
        storageReference.putFile(io.File(_image.path), metadata);
    final task = await uploadTask;
    if (task != null) {
      print('File Uploaded');
      storageReference.getDownloadURL().then((fileURL) {
        setState(() {
          _uploadedFileURL = fileURL;

          print(_uploadedFileURL);
          _saving = false;
        });
        auth.getCurrentUser().then((user) {
          FirebaseFirestore.instance
              .collection('users')
              .doc(user!.uid)
              .update({"imageProfile": _uploadedFileURL}).then((data) {});
        });
      });
      return true;
    } else {
      return false;
    }
  }

  void showColoredToast(msg, colors) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_LONG,
        backgroundColor: colors,
        textColor: Colors.white);
  }

  setInit() {
    auth.getCurrentUser().then((user) {
      if (!mounted) return;
      setState(() {
        //currentPhone = user.phoneNumber;
        editPhone.text = user!.phoneNumber.toString();
      });

      FirebaseFirestore.instance
          .collection('users')
          .doc(user!.uid)
          .get()
          .then((DocumentSnapshot<Map<String, dynamic>> ds) {
        setState(() {
          firstName = ds.data()!["firstName"];
          lastName = ds.data()!["lastName"];
          editFirstName.text = ds.data()!["firstName"];
          editLastName.text = ds.data()!["lastName"];
          editEmail.text = ds.data()!["email"] ?? "";
          editFacebook.text = ds.data()!["facebook"] ?? "";
          editLine.text = ds.data()!["line"] ?? "";
          phoneNumber = user.phoneNumber.toString();
          editPhoneNumber.text = user.phoneNumber.toString();
          _uploadedFileURL = ds.data()!["imageProfile"] ?? '';
          _saving = false;
        });
        FirebaseFirestore.instance
            .collection('users')
            .where('refCode', isEqualTo: ds.data()!["selfCode"])
            .snapshots()
            .listen((data) {
          setState(() {
            refers = data.docs.length;
          });
        });
      });
    });
  }

  saveProfile() {
    auth.getCurrentUser().then((user) {
      FirebaseFirestore.instance.collection('users').doc(user!.uid).update({
        'firstName': editFirstName.text,
        'lastName': editLastName.text,
        'email': editEmail.text,
        'facebook': editFacebook.text,
        'line': editLine.text
      }).then((data) {
        setInit();
        showColoredToast("Updated !", Colors.green);
        setState(() {
          _saving = false;
        });
      });
    });
  }

  Widget build(BuildContext cx) {
    return new ModalProgressHUD(
      opacity: 0.1,
      inAsyncCall: _saving,
      progressIndicator: CustomLoading(),
      child: GestureDetector(
        onTap: () {
          FocusScopeNode currentFocus = FocusScope.of(context);

          if (!currentFocus.hasPrimaryFocus) {
            currentFocus.unfocus();
          }
        },
        child: new Scaffold(
          // backgroundColor: Color(0xff141322),
          appBar: null,
          body: SingleChildScrollView(
              child: Stack(
            children: [
              _body(),
              //เเสดงรูปProfile วงกลม
              Positioned(
                top: mediaQuery(context, 'height', 219),
                left: mediaQuery(context, 'width', 63),
                child: Container(
                  height: MediaQuery.of(context).size.width * 0.22777777777,
                  width: MediaQuery.of(context).size.width * 0.22777777777,
                  decoration: BoxDecoration(
                      color: LikeWalletAppTheme.white.withOpacity(0.9),
                      shape: BoxShape.circle,
                      image: DecorationImage(
                        fit: BoxFit.cover,
                        image: _uploadedFileURL == 'noprofile'
                            ? AssetImage(
                                LikeWalletImage.defaultProfile,
                              ) as ImageProvider
                            : NetworkImage(_uploadedFileURL),
                      ),
                      border: Border.all(
                          color: Colors.white,
                          width: MediaQuery.of(context).size.width *
                              0.00277777777)),
                ),
              ),
              //ปุ่มเปลี่ยนรูปภาพ
              Positioned(
                  top: mediaQuery(context, 'height', 390),
                  left: mediaQuery(context, 'width', 240.76),
                  child: GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      print('click');
                      chooseFile().then((data) {
                        if (data) {
                          uploadFile();
                        }
                      });
                    },
                    child: Container(
                      height: mediaQuery(context, 'width', 80.24),
                      width: mediaQuery(context, 'width', 80.24),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        image: DecorationImage(
                            fit: BoxFit.cover,
                            image: AssetImage(LikeWalletImage.edit_photo)),
                        border: Border.all(
                          color: LikeWalletAppTheme.white,
                        ),
                      ),
                    ),
                  )),
            ],
          )),
        ),
      ),
    );
  }

  Widget _body() {
    return Column(
      children: <Widget>[_cover(), _bg()],
    );
  }

  Widget _bg() {
    return Container(
      height: MediaQuery.of(context).size.height -
          mediaQuery(context, 'height', 390),
      decoration: BoxDecoration(
        color: const Color(0xff141322),
        boxShadow: [
          BoxShadow(
            color: const Color(0xcc00887f),
            offset: Offset(0, -mediaQuery(context, 'height', 18)),
            spreadRadius: 1,
            blurRadius: mediaQuery(context, 'height', 35),
          ),
        ],
      ),
      child: Column(
        children: [
          _phone(),
//                textFieldProfile(
//                    context, editPhone, 'profile_phone', 'profile_phone',
//                    active: false),
          _profile(),
          textFieldProfile(
              context, editFirstName, 'profile_name', 'profile_name'),
          textFieldProfile(
              context, editLastName, 'profile_lastname', 'profile_lastname'),
          // textFieldProfile(
          //     context, editEmail, 'profile_email', 'profile_email'),
          textFieldProfile(
              context, editFacebook, 'profile_facebook', 'profile_facebook'),
          textFieldProfile(context, editLine, 'profile_line', 'profile_line'),
          _bottomSave(),
          SizedBox(
            height: 18,
          ),
          _bottomDeletion(),
        ],
      ),
    );
  }

  Widget _phone() {
    return Container(
      alignment: Alignment.centerRight,
      padding: EdgeInsets.only(
          top: mediaQuery(context, 'height', 65),
          right: mediaQuery(context, 'width', 75)),
      width: MediaQuery.of(context).size.width,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: <Widget>[
          Text(
            editPhone.text,
            style: TextStyle(
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              color: LikeWalletAppTheme.white.withOpacity(0.9),
              letterSpacing: 0.5,
              fontSize: mediaQuery(context, "height", 45),
              fontWeight: FontWeight.w100,
            ),
          ),
          Container(
            child: Text(
              AppLocalizations.of(context)!.translate('profile_phone'),
              textAlign: TextAlign.center,
              style: TextStyle(
                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                color: LikeWalletAppTheme.white.withOpacity(0.4),
                letterSpacing: 0.5,
                fontSize: mediaQuery(context, "height", 36),
                fontWeight: FontWeight.w100,
              ),
            ),
          ),
          SizedBox(
            height: mediaQuery(context, 'height', 40),
          ),
          // changePhone()
        ],
      ),
    );
  }

  Widget changePhone() {
    return InkWell(
      // onTap: () => Navigator.push(
      //     context,
      //     EnterExitRoute(
      //         exitPage: FbCloneProfileStful(),
      //         enterPage: ChangePhone(oldPhone: editPhone.text))),
      child: Container(
        alignment: Alignment.center,
        width: mediaQuery(context, 'width', 363),
        height: mediaQuery(context, 'height', 159),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(80.0),
          gradient: LinearGradient(
            begin: Alignment(0.0, -1.0),
            end: Alignment(0.0, 1.0),
            colors: [const Color(0x802f3042), const Color(0x8028293b)],
            stops: [0.0, 1.0],
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0x80000000),
              offset: Offset(0, mediaQuery(context, 'height', 3)),
              spreadRadius: 0,
              blurRadius: mediaQuery(context, 'height', 6),
            ),
          ],
        ),
        child: Text(
          AppLocalizations.of(context)!.translate('profile_change_phone'),
          textAlign: TextAlign.center,
          style: TextStyle(
            fontFamily: AppLocalizations.of(context)!.translate('font1'),
            color: LikeWalletAppTheme.bule1,
            letterSpacing: 0.8,
            fontSize: mediaQuery(context, "height", 33),
            fontWeight: FontWeight.w100,
          ),
        ),
      ),
    );
  }

  Widget _cover() {
    return Container(
      child: Stack(
        clipBehavior: Clip.none,
        children: <Widget>[
          Column(
            children: <Widget>[
              //ชื่อนามสกุลโชว์ในcover
              Container(
                alignment: Alignment.center,
                width: mediaQuery(context, 'width', 1080.0),
                height: mediaQuery(context, 'height', 390),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment(1.0, -1.0),
                    end: Alignment(-0.96, 1.08),
                    colors: [const Color(0xff30f3ca), const Color(0xff24c6e4)],
                    stops: [0.0, 1.0],
                  ),
                ),
                child: Text(
                  firstName + '\n' + lastName,
//                          + '\n'+ currentPhone,
                  textAlign: TextAlign.center,
                  style: LikeWalletAppTheme.textStyle(
                      context,
                      65,
                      LikeWalletAppTheme.white.withOpacity(0.6),
                      FontWeight.bold,
                      'font1'),
                ),
              ),
            ],
          ),
          Positioned(
            top: 100.33.h,
            left: 0,
            child: backButton(context, LikeWalletAppTheme.black),
          ),
        ],
      ),
    );
  }

  Widget _editPhoto() {
    return Container(
        alignment: Alignment.centerLeft,
        padding: EdgeInsets.only(left: mediaQuery(context, 'width', 240)),
        child: GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            print('click');
            chooseFile().then((data) {
              if (data) {
                uploadFile();
              }
            });
          },
          child: Container(
            height: mediaQuery(context, 'width', 80.24),
            width: mediaQuery(context, 'width', 80.24),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              image: DecorationImage(
                  fit: BoxFit.cover,
                  image: AssetImage(LikeWalletImage.edit_photo)),
              border: Border.all(
                color: LikeWalletAppTheme.white,
              ),
            ),
          ),
        ));
  }

  Widget _3list() {
    return Container(
        margin: EdgeInsets.only(top: mediaQuery(context, 'height', 57)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: <Widget>[
                Text(
                  '0',
                  style: LikeWalletAppTheme.textStyle(
                    context,
                    45,
                    LikeWalletAppTheme.bule1,
                    FontWeight.normal,
                    'font1',
                  ),
                ),
                Container(
                  width: mediaQuery(context, 'height', 253),
                  child: Text(
                      AppLocalizations.of(context)!
                          .translate('profile_ranking'),
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                        color: LikeWalletAppTheme.bule1_8.withOpacity(0.4),
                        letterSpacing: 0.3,
                        fontSize: mediaQuery(context, "height", 41),
                        fontWeight: FontWeight.normal,
                      )),
                )
              ],
            ),
            Container(
              margin: EdgeInsets.only(
                left: mediaQuery(context, 'width', 50),
                right: mediaQuery(context, 'width', 70),
              ),
              alignment: Alignment.center,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: <Widget>[
                  Text(
                    refers.toString(),
                    style: LikeWalletAppTheme.textStyle(
                      context,
                      45,
                      LikeWalletAppTheme.bule1,
                      FontWeight.normal,
                      'font1',
                    ),
                  ),
                  Container(
                    width: mediaQuery(context, 'height', 253),
                    child: Text(
                      AppLocalizations.of(context)!.translate('profile_refers'),
                      textAlign: TextAlign.center,
                      style: LikeWalletAppTheme.textStyle(
                        context,
                        36,
                        LikeWalletAppTheme.bule1_8.withOpacity(0.4),
                        FontWeight.normal,
                        'font1',
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ));
  }

  Widget _profile() {
    return Padding(
        padding: EdgeInsets.only(
            top: mediaQuery(context, 'hieght', 65),
            left: mediaQuery(context, 'width', 100),
            bottom: mediaQuery(context, 'hieght', 54.27)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: <Widget>[
            Padding(
              padding: EdgeInsets.only(right: mediaQuery(context, 'width', 10)),
              child: Text(
                AppLocalizations.of(context)!.translate('profile_title'),
                style: LikeWalletAppTheme.textStyle(
                    context,
                    41,
                    LikeWalletAppTheme.bule1_8.withOpacity(0.4),
                    FontWeight.normal,
                    'font1'),
              ),
            ),
          ],
        ));
  }

  Widget _bottomSave() {
    return Container(
      width: 930.w,
      height: 132.h,
      child: new TextButton(
          style: ButtonStyle(
            backgroundColor: MaterialStateProperty.all<Color>(
              Color(0xff00F1E0),
            ),
            shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10.0),
            )),
          ),
          onPressed: () {
            setState(() {
              _saving = true;
            });
            saveProfile();
          },
          child: new Text(
            AppLocalizations.of(context)!.translate('profile_save'),
            style: LikeWalletAppTheme.textStyle(context, 51,
                LikeWalletAppTheme.black, FontWeight.normal, 'font1'),
          )),
    );
  }
  Widget _bottomDeletion() {
    return Container(
      width: 930.w,
      height: 132.h,
      child: new TextButton(
          style: ButtonStyle(
            backgroundColor: MaterialStateProperty.all<Color>(
              LikeWalletAppTheme.red,
            ),
            shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10.0),
                )),
          ),
          onPressed: () {
            CustomPopup.showErrorDialog(context, AppLocalizations.of(context)!.translate('profile_delete_account_title'), AppLocalizations.of(context)!.translate('profile_delete_account_text'));
          },
          child: new Text(
            AppLocalizations.of(context)!.translate('delete_account'),
            style: LikeWalletAppTheme.textStyle(context, 51,
                LikeWalletAppTheme.black, FontWeight.normal, 'font1'),
          )),
    );
  }
}
