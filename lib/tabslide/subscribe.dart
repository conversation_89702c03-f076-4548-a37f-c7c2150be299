import 'package:flutter/material.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/screen/NavigationBar.dart' as nav;
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/screen_util.dart';

class Subscribe extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return new _Subscribe();
  }
}

class _Subscribe extends State<Subscribe> {
  bool _count = false;
  int count = 0;
  @override
  Widget build(BuildContext context) {
    return Material(
        child: Scaffold(
      bottomNavigationBar: nav.NavigationBar(),
      backgroundColor: Colors.white,
      body: Stack(
        children: <Widget>[
          _background(),
          _background2(),
          _backtohome(),
          _premium(),
          _basic()
        ],
      ),
    ));
  }

  Widget _background() {
    return Container(
      decoration: BoxDecoration(
          //การไล่สีพื้นหลัง
          gradient: LinearGradient(
        begin: Alignment.topRight,
        end: Alignment.bottomLeft,
        stops: [0.2, 0.3, 0.4, 0.6, 0.7],
        colors: [
          // Colors are easy thanks to Flutter's Colors class.
          Color(0xff111112),
          Color(0xff111112).withOpacity(0.9),
          Color(0xff111112).withOpacity(0.85),
          Color(0xff111112).withOpacity(0.8),
          Color(0xff111112).withOpacity(0.75)
        ],
      )),
    );
  }

  Widget _background2() {
    return Positioned(
        top: mediaQuery(context, 'height', 1407),
        child: new Container(
            color: Colors.white,
            alignment: Alignment.centerLeft,
            height: mediaQuery(context, 'height', 800),
            width: MediaQuery.of(context).size.width));
  }

  Widget _backtohome() {
    return //ปุ่ม back
        Positioned(
            top: mediaQuery(context, "height", 139.33),
            child: TextButton(
              onPressed: () {},
              child: new Container(
                alignment: Alignment.centerLeft,
                width: MediaQuery.of(context).size.width,
                child: new IconButton(
                  icon: new Icon(
                    Icons.arrow_back_ios,
                    size: mediaQuery(context, "height", 44.47),
                  ),
                  color: LikeWalletAppTheme.gray,
                  onPressed: () =>
                      {Navigator.of(context).popUntil((_) => count++ >= 2)},
                ),
              ),
            ));
  }

  Widget _premium() {
    return Positioned(
        top: mediaQuery(context, 'height', 501),
//                left: MediaQuery.of(context).size.width ,
        left: mediaQuery(context, 'width', -150.99),
        child: new Container(
//                  color: Colors.blue,
          height: mediaQuery(context, 'height', 1028.99),
          width: mediaQuery(context, 'width', 569.35),
          child: GestureDetector(
            onTap: () {
              setState(() {
                if (_count == false) {
                  _count = true;
                  print(_count);
                } else {
                  _count = false;
                  print(_count);
                }
              });
            },
            child: AnimatedSwitcher(
                duration: const Duration(milliseconds: 200),
                transitionBuilder: (Widget child, Animation<double> animation) {
                  return ScaleTransition(child: child, scale: animation);
                },
                child: _count == true
                    ? Image.asset(
                        AppLocalizations.of(context)!
                            .translate('subscribe_prenium'),
                        key: ValueKey<bool>(_count),
                      )
                    : Image.asset(
                        AppLocalizations.of(context)!
                            .translate('subscribe_basic'),
                        key: ValueKey<bool>(_count),
                      )),
          ),
        ));
  }

  Widget _basic() {
    return Positioned(
        top: mediaQuery(context, 'height', 387),
//                left: MediaQuery.of(context).size.width ,
        left: mediaQuery(context, 'width', 294),
        child: new Container(
//                  color: Colors.amber,
          alignment: Alignment.center,
          height: mediaQuery(context, 'height', 1284.41),
          width: mediaQuery(context, 'width', 710.67),
          child: GestureDetector(
            onTap: () {
              setState(() {
//                        _count = true;
//                        print(_count);
              });
            },
            child: AnimatedSwitcher(
                duration: const Duration(milliseconds: 200),
                transitionBuilder: (Widget child, Animation<double> animation) {
                  return ScaleTransition(child: child, scale: animation);
                },
                child: _count == false
                    ? Image.asset(
                        AppLocalizations.of(context)!
                            .translate('subscribe_prenium'),
                        key: ValueKey<bool>(_count),
                      )
                    : Image.asset(
                        AppLocalizations.of(context)!
                            .translate('subscribe_basic'),
                        key: ValueKey<bool>(_count),
                      )),
          ),
        ));
  }
}
