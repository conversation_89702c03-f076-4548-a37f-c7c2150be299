import 'dart:ui';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/animationPage.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/libraryman/applang.dart';
import 'package:likewallet/main.dart';
import 'package:likewallet/menu/LockLIKE_New.dart';
import 'package:likewallet/menu/hourlyRewards.dart';
import 'package:likewallet/menu/reward/rewards_screen.dart';
import 'package:likewallet/routes.dart';
import 'package:likewallet/screen/home.dart';

import 'package:likewallet/screen/index.dart';
import 'package:likewallet/screen_util.dart';

import 'package:shared_preferences/shared_preferences.dart';

final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
Future<bool> destroyApp() async {
  final storage = new FlutterSecureStorage();
  SharedPreferences pref = await SharedPreferences.getInstance();
  //save old lang
  final String? lang = pref.getString('language_code');
  final bool? agreementPolicy = pref.getBool('agreementPolicy');
  final bool? agreementTermsAndCondition = pref.getBool('agreementTermsAndCondition');
  bool check = await pref.clear();
  _firebaseMessaging.unsubscribeFromTopic('notifyTier1');
  _firebaseMessaging.unsubscribeFromTopic('notifyAll');
  _firebaseMessaging.unsubscribeFromTopic('notifyNormal');
  await storage.deleteAll();

  pref.setString('language_code', lang ?? 'en');
  pref.setBool('agreementPolicy', agreementPolicy!);
  pref.setBool('agreementTermsAndCondition', agreementTermsAndCondition!);

  return true;
}

LogoutDialog(context) {
  Dialog simpleDialog = Dialog(
    elevation: 500,
    backgroundColor: Colors.transparent,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(30.0),
    ),
    child: Container(
      height: mediaQuery(context, 'height', 554.63),
      width: mediaQuery(context, 'width', 929.64),
      color: Colors.transparent,
      margin: EdgeInsets.only(bottom: mediaQuery(context, 'height', 600)),
      child: new ClipRect(
        child: new BackdropFilter(
          filter: new ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
          child: Container(
            decoration: BoxDecoration(
              color: LikeWalletAppTheme.white.withOpacity(0.6),
              borderRadius: BorderRadius.all(Radius.circular(20.0)),
            ),
            height: mediaQuery(context, 'height', 554.63),
            width: mediaQuery(context, 'width', 929.64),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Text(
                  AppLocalizations.of(context)!.translate('tabslide_logout'),
                  style: TextStyle(
                    letterSpacing: 0.3,
                    fontFamily:
                        AppLocalizations.of(context)!.translate('font1'),
                    color: LikeWalletAppTheme.black.withOpacity(1),
                    fontSize: mediaQuery(context, "height", 56),
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(
                      bottom: mediaQuery(context, 'height', 80)),
                  width: mediaQuery(context, 'width', 777.62),
                  child: Text(
                    AppLocalizations.of(context)!.translate('logout_detail'),
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      letterSpacing: 0.3,
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      color: LikeWalletAppTheme.black.withOpacity(1),
                      fontSize: mediaQuery(context, "height", 42),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Container(
                    width: mediaQuery(context, 'width', 777.62),
                    decoration: BoxDecoration(
                      border: Border(
                        top: BorderSide(
                          //                   <--- left side
                          color: LikeWalletAppTheme.black.withOpacity(0.4),
                          width: mediaQuery(context, 'width', 1),
                        ),
                      ),
                    ),
                    child: Row(
                      children: <Widget>[
                        GestureDetector(
                          onTap: () async {
                            Navigator.of(context).pop();
                            destroyApp().then((result) async {
                              AppLanguage appLanguage = AppLanguage();
                              await appLanguage.fetchLocale();
                              AppRoutes.makeFirst(context, IndexLike());
                              // Navigator.pushAndRemoveUntil(
                              //   context,
                              //   MaterialPageRoute(
                              //       builder: (BuildContext context) =>
                              //           MyHomePage()),
                              //   ModalRoute.withName('/'),
                              // );
                            });
                          },
                          child: Container(
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              border: Border(
                                right: BorderSide(
                                  //                   <--- left side
                                  color:
                                      LikeWalletAppTheme.black.withOpacity(0.4),
                                  width: mediaQuery(context, 'width', 1),
                                ),
                              ),
                            ),
                            height: mediaQuery(context, 'height', 127.66),
                            width: mediaQuery(context, 'width', 777.62) / 2,
                            child: Text(
                              AppLocalizations.of(context)!
                                  .translate('logout_yes'),
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                letterSpacing: 0.3,
                                fontFamily: AppLocalizations.of(context)!
                                    .translate('font1'),
                                color:
                                    LikeWalletAppTheme.bule1_7.withOpacity(1),
                                fontSize: mediaQuery(context, "height", 52),
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                        GestureDetector(
                          onTap: () {
                            Navigator.of(context).pop();
                          },
                          child: Container(
                            alignment: Alignment.center,
                            height: mediaQuery(context, 'height', 127.66),
                            width: mediaQuery(context, 'width', 777.62) / 2,
                            child: Text(
                              AppLocalizations.of(context)!
                                  .translate('logout_no'),
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                letterSpacing: 0.3,
                                fontFamily: AppLocalizations.of(context)!
                                    .translate('font1'),
                                color:
                                    LikeWalletAppTheme.bule1_7.withOpacity(1),
                                fontSize: mediaQuery(context, "height", 52),
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                      ],
                    )),
              ],
            ),
          ),
        ),
      ),
    ),
  );
  showDialog(context: context, builder: (BuildContext context) => simpleDialog);
}

GetRewardsPopup(context) {
  Dialog simpleDialog = Dialog(
    elevation: 500,
    backgroundColor: Colors.transparent,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(30.0),
    ),
    child: Container(
      height: mediaQuery(context, 'height', 554.63),
      width: mediaQuery(context, 'width', 929.64),
      color: Colors.transparent,
      margin: EdgeInsets.only(bottom: mediaQuery(context, 'height', 600)),
      child: new ClipRect(
        child: new BackdropFilter(
          filter: new ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
          child: Container(
            decoration: BoxDecoration(
              color: LikeWalletAppTheme.white.withOpacity(0.6),
              borderRadius: BorderRadius.all(Radius.circular(20.0)),
            ),
            height: mediaQuery(context, 'height', 554.63),
            width: mediaQuery(context, 'width', 929.64),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Text(
                  AppLocalizations.of(context)!
                      .translate('tabslide_alert_title_rewads'),
                  style: TextStyle(
                    letterSpacing: 0.3,
                    fontFamily:
                        AppLocalizations.of(context)!.translate('font1'),
                    color: LikeWalletAppTheme.black.withOpacity(1),
                    fontSize: mediaQuery(context, "height", 56),
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(
                      bottom: mediaQuery(context, 'height', 80)),
                  width: mediaQuery(context, 'width', 777.62),
                  child: Text(
                    AppLocalizations.of(context)!
                        .translate('tabslide_alert_rewards'),
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      letterSpacing: 0.3,
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      color: LikeWalletAppTheme.black.withOpacity(1),
                      fontSize: mediaQuery(context, "height", 42),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Container(
                    width: mediaQuery(context, 'width', 777.62),
                    decoration: BoxDecoration(
                      border: Border(
                        top: BorderSide(
                          //                   <--- left side
                          color: LikeWalletAppTheme.black.withOpacity(0.4),
                          width: mediaQuery(context, 'width', 1),
                        ),
                      ),
                    ),
                    child: Row(
                      children: <Widget>[
                        GestureDetector(
                          onTap: () async {
                            Navigator.of(context).pop();
                            Navigator.push(
                                context,
                                EnterExitRoute(
                                    exitPage: lockLike(),
                                    enterPage: Rewards()));
                          },
                          child: Container(
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              border: Border(
                                right: BorderSide(
                                  //                   <--- left side
                                  color:
                                      LikeWalletAppTheme.black.withOpacity(0.4),
                                  width: mediaQuery(context, 'width', 1),
                                ),
                              ),
                            ),
                            height: mediaQuery(context, 'height', 127.66),
                            width: mediaQuery(context, 'width', 777.62) / 2,
                            child: Text(
                              AppLocalizations.of(context)!
                                  .translate('logout_yes'),
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                letterSpacing: 0.3,
                                fontFamily: AppLocalizations.of(context)!
                                    .translate('font1'),
                                color:
                                    LikeWalletAppTheme.bule1_7.withOpacity(1),
                                fontSize: mediaQuery(context, "height", 52),
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                        GestureDetector(
                          onTap: () {
                            Navigator.of(context).pop();
                          },
                          child: Container(
                            alignment: Alignment.center,
                            height: mediaQuery(context, 'height', 127.66),
                            width: mediaQuery(context, 'width', 777.62) / 2,
                            child: Text(
                              AppLocalizations.of(context)!
                                  .translate('logout_no'),
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                letterSpacing: 0.3,
                                fontFamily: AppLocalizations.of(context)!
                                    .translate('font1'),
                                color:
                                    LikeWalletAppTheme.bule1_7.withOpacity(1),
                                fontSize: mediaQuery(context, "height", 52),
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                      ],
                    )),
              ],
            ),
          ),
        ),
      ),
    ),
  );
  showDialog(context: context, builder: (BuildContext context) => simpleDialog);
}

ExitDialog(context) {
  Dialog simpleDialog = Dialog(
    elevation: 500,
    backgroundColor: Colors.transparent,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(30.0),
    ),
    child: Container(
      height: mediaQuery(context, 'height', 554.63),
      width: mediaQuery(context, 'width', 929.64),
      color: Colors.transparent,
      margin: EdgeInsets.only(bottom: mediaQuery(context, 'height', 600)),
      child: new ClipRect(
        child: new BackdropFilter(
          filter: new ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
          child: Container(
            decoration: BoxDecoration(
              color: LikeWalletAppTheme.white.withOpacity(0.6),
              borderRadius: BorderRadius.all(Radius.circular(20.0)),
            ),
            height: mediaQuery(context, 'height', 554.63),
            width: mediaQuery(context, 'width', 929.64),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Text(
                  AppLocalizations.of(context)!.translate('exit_app'),
                  style: TextStyle(
                    letterSpacing: 0.3,
                    fontFamily:
                        AppLocalizations.of(context)!.translate('font1'),
                    color: LikeWalletAppTheme.black.withOpacity(1),
                    fontSize: mediaQuery(context, "height", 56),
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(
                      bottom: mediaQuery(context, 'height', 80)),
                  width: mediaQuery(context, 'width', 777.62),
                  child: Text(
                    AppLocalizations.of(context)!.translate('detail_exit_app'),
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      letterSpacing: 0.3,
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      color: LikeWalletAppTheme.black.withOpacity(1),
                      fontSize: mediaQuery(context, "height", 42),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Container(
                    width: mediaQuery(context, 'width', 777.62),
                    decoration: BoxDecoration(
                      border: Border(
                        top: BorderSide(
                          //                   <--- left side
                          color: LikeWalletAppTheme.black.withOpacity(0.4),
                          width: mediaQuery(context, 'width', 1),
                        ),
                      ),
                    ),
                    child: Row(
                      children: <Widget>[
                        GestureDetector(
                          onTap: () async {
                            if (Platform.isAndroid) {
                              SystemNavigator.pop();
                            } else {
                              exit(0);
                            }
                          },
                          child: Container(
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              border: Border(
                                right: BorderSide(
                                  //                   <--- left side
                                  color:
                                      LikeWalletAppTheme.black.withOpacity(0.4),
                                  width: mediaQuery(context, 'width', 1),
                                ),
                              ),
                            ),
                            height: mediaQuery(context, 'height', 127.66),
                            width: mediaQuery(context, 'width', 777.62) / 2,
                            child: Text(
                              AppLocalizations.of(context)!
                                  .translate('logout_yes'),
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                letterSpacing: 0.3,
                                fontFamily: AppLocalizations.of(context)!
                                    .translate('font1'),
                                color:
                                    LikeWalletAppTheme.bule1_7.withOpacity(1),
                                fontSize: mediaQuery(context, "height", 52),
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                        GestureDetector(
                          onTap: () {
                            Navigator.of(context).pop();
                          },
                          child: Container(
                            alignment: Alignment.center,
                            height: mediaQuery(context, 'height', 127.66),
                            width: mediaQuery(context, 'width', 777.62) / 2,
                            child: Text(
                              AppLocalizations.of(context)!
                                  .translate('logout_no'),
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                letterSpacing: 0.3,
                                fontFamily: AppLocalizations.of(context)!
                                    .translate('font1'),
                                color:
                                    LikeWalletAppTheme.bule1_7.withOpacity(1),
                                fontSize: mediaQuery(context, "height", 52),
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                      ],
                    )),
              ],
            ),
          ),
        ),
      ),
    ),
  );
  showDialog(context: context, builder: (BuildContext context) => simpleDialog);
}

ExitKYC(context) {
  Dialog simpleDialog = Dialog(
    elevation: 500,
    backgroundColor: Colors.transparent,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(30.0),
    ),
    child: Container(
      height: mediaQuery(context, 'height', 554.63),
      width: mediaQuery(context, 'width', 929.64),
      color: Colors.transparent,
      margin: EdgeInsets.only(bottom: mediaQuery(context, 'height', 600)),
      child: new ClipRect(
        child: new BackdropFilter(
          filter: new ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
          child: Container(
            decoration: BoxDecoration(
              color: LikeWalletAppTheme.white.withOpacity(0.6),
              borderRadius: BorderRadius.all(Radius.circular(20.0)),
            ),
            height: mediaQuery(context, 'height', 554.63),
            width: mediaQuery(context, 'width', 929.64),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Text(
                  AppLocalizations.of(context)!.translate('check_exitsKYC'),
                  style: TextStyle(
                    letterSpacing: 0.3,
                    fontFamily:
                        AppLocalizations.of(context)!.translate('font1'),
                    color: LikeWalletAppTheme.black.withOpacity(1),
                    fontSize: mediaQuery(context, "height", 50),
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(
                      bottom: mediaQuery(context, 'height', 80)),
                  width: mediaQuery(context, 'width', 777.62),
                  child: Text(
                    AppLocalizations.of(context)!
                        .translate('check_exitsKYC_detail'),
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      letterSpacing: 0.3,
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      color: LikeWalletAppTheme.black.withOpacity(1),
                      fontSize: mediaQuery(context, "height", 42),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Container(
                    width: mediaQuery(context, 'width', 777.62),
                    decoration: BoxDecoration(
                      border: Border(
                        top: BorderSide(
                          //                   <--- left side
                          color: LikeWalletAppTheme.black.withOpacity(0.4),
                          width: mediaQuery(context, 'width', 1),
                        ),
                      ),
                    ),
                    child: Row(
                      children: <Widget>[
                        GestureDetector(
                          onTap: () {
                            AppRoutes.makeFirst(context, HomeLikewallet());
                          },
                          child: Container(
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              border: Border(
                                right: BorderSide(
                                  //                   <--- left side
                                  color:
                                      LikeWalletAppTheme.black.withOpacity(0.4),
                                  width: mediaQuery(context, 'width', 1),
                                ),
                              ),
                            ),
                            height: mediaQuery(context, 'height', 127.66),
                            width: mediaQuery(context, 'width', 777.62) / 2,
                            child: Text(
                              AppLocalizations.of(context)!
                                  .translate('logout_yes'),
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                letterSpacing: 0.3,
                                fontFamily: AppLocalizations.of(context)!
                                    .translate('font1'),
                                color:
                                    LikeWalletAppTheme.bule1_7.withOpacity(1),
                                fontSize: mediaQuery(context, "height", 52),
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                        GestureDetector(
                          onTap: () {
                            Navigator.of(context).pop();
                          },
                          child: Container(
                            alignment: Alignment.center,
                            height: mediaQuery(context, 'height', 127.66),
                            width: mediaQuery(context, 'width', 777.62) / 2,
                            child: Text(
                              AppLocalizations.of(context)!
                                  .translate('logout_no'),
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                letterSpacing: 0.3,
                                fontFamily: AppLocalizations.of(context)!
                                    .translate('font1'),
                                color:
                                    LikeWalletAppTheme.bule1_7.withOpacity(1),
                                fontSize: mediaQuery(context, "height", 52),
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                      ],
                    )),
              ],
            ),
          ),
        ),
      ),
    ),
  );
  showDialog(context: context, builder: (BuildContext context) => simpleDialog);
}

warningLimit(context, amount, sum) {
  Dialog simpleDialog = Dialog(
    elevation: 500,
    backgroundColor: Colors.transparent,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(30.0),
    ),
    child: Card(
      semanticContainer: true,
      clipBehavior: Clip.antiAliasWithSaveLayer,
      color: Colors.transparent,
      child: new ClipRect(
        child: new BackdropFilter(
          filter: new ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
          child: Container(
            decoration: BoxDecoration(
              color: LikeWalletAppTheme.white.withOpacity(0.8),
              borderRadius: BorderRadius.all(Radius.circular(20.0)),
            ),
            height: mediaQuery(context, 'height', 600.63),
            width: mediaQuery(context, 'width', 929.64),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Text(
                  AppLocalizations.of(context)!
                      .translate('tabslide_alert_title_rewads'),
                  style: TextStyle(
                    letterSpacing: 0.3,
                    fontFamily:
                        AppLocalizations.of(context)!.translate('font1'),
                    color: LikeWalletAppTheme.black.withOpacity(1),
                    fontSize: mediaQuery(context, "height", 56),
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(
                      top: mediaQuery(context, 'height', 40),
                      bottom: mediaQuery(context, 'height', 40)),
                  width: mediaQuery(context, 'width', 777.62),
                  child: Text(
                    AppLocalizations.of(context)!
                            .translate('limit_lock_detail1') +
                        " " +
                        amount.toString() +
                        " " +
                        "LIKE"
                            "\n" +
                        AppLocalizations.of(context)!
                            .translate('limit_lock_detail2') +
                        " " +
                        sum.toString() +
                        " " +
                        "LIKE",
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      letterSpacing: 0.3,
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      color: LikeWalletAppTheme.black.withOpacity(1),
                      fontSize: mediaQuery(context, "height", 42),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Container(
                    width: mediaQuery(context, 'width', 777.62),
                    decoration: BoxDecoration(
                      border: Border(
                        top: BorderSide(
                          color: LikeWalletAppTheme.black.withOpacity(0.4),
                          width: mediaQuery(context, 'width', 1),
                        ),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        TextButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          child: Container(
                            alignment: Alignment.center,
                            child: Text(
                              AppLocalizations.of(context)!
                                  .translate('logout_no'),
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                letterSpacing: 0.3,
                                fontFamily: AppLocalizations.of(context)!
                                    .translate('font1'),
                                color:
                                    LikeWalletAppTheme.bule1_7.withOpacity(1),
                                fontSize: mediaQuery(context, "height", 52),
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                      ],
                    )),
              ],
            ),
          ),
        ),
      ),
    ),
  );
  showDialog(context: context, builder: (BuildContext context) => simpleDialog);
}
