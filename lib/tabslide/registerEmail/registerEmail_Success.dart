import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/routes.dart';
import 'package:likewallet/screen/home.dart';
import 'package:likewallet/screen_util.dart';


class RegisterEmailSuccess extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return _RegisterEmailSuccess();
  }
}

class _RegisterEmailSuccess extends State<RegisterEmailSuccess> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: LikeWalletAppTheme.bule2,
      body: Stack(
        alignment: Alignment.center,
        children: <Widget>[
          Positioned(
              top: mediaQuery(context, 'height', 136),
              child: GestureDetector(
                child: new Container(
                  alignment: Alignment.centerLeft,
                  width: MediaQuery.of(context).size.width,
                  child: new IconButton(
                    icon: new Icon(
                      Icons.arrow_back_ios,
                      size: mediaQuery(context, "height", 44.47),
                    ),
                    color: LikeWalletAppTheme.white,
                    onPressed: () =>
                        {AppRoutes.makeFirst(context, HomeLikewallet())},
                  ),
                ),
              )),
          Positioned(
              top: mediaQuery(context, 'height', 500),
              child: Container(
                  child: Image.asset(
                'assets/image/success.png',
                color: LikeWalletAppTheme.bule1,
                height: mediaQuery(context, 'height', 400),
              ))),
          Positioned(
            top: mediaQuery(context, 'height', 900),
            child: Text(
              AppLocalizations.of(context)!.translate('success_registerEmail'),
              style: TextStyle(
                color: LikeWalletAppTheme.white,
                fontSize: mediaQuery(context, 'height', 75),
                fontFamily: AppLocalizations.of(context)!.translate('font1'),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
