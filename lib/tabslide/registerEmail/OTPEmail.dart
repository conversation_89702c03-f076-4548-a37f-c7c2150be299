import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/rendering.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/animationPage.dart';
import 'package:likewallet/libraryman/secret_pass_first.dart';
import 'package:likewallet/login/sing_in.dart';
import 'package:likewallet/tabslide/registerEmail/registerEmail_Success.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:likewallet/screen/choice_user.dart';
import 'package:likewallet/app_config.dart';
import 'package:http/http.dart' as http;
import 'package:likewallet/libraryman/secret_pass.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/libraryman/otp.dart';
import 'package:likewallet/libraryman/app_local.dart';

class OTP_EMAIL extends StatefulWidget {
//  OTP_PAGE({this.checkIf});

  OTP_EMAIL(
      {this.refCode,
      this.checkIf,
      this.auth,
      this.verificationId,
      this.roundSMS,
      this.phoneNumber,
      this.firstName,
      this.lastName,
      this.prefixNumber,
      this.email,
      this.password});

  final FirebaseAuth? auth;
  final String? verificationId;
  final providerSMS? roundSMS;
  final String? phoneNumber;
  final String? firstName;
  final String? lastName;
  final String? refCode;
  final String? prefixNumber;

  final String? email;
  final String? password;

  //  OTP_PAGE({this.auth, this.verificationId});

//  final FirebaseAuth auth;
//  final String verificationId;
//  final VoidCallback onSignedIn;
  final String? checkIf;
//  OTP_PAGE({Key key, @required this.text}) : super(key: key);
  @override
  _OTP_EMAIL createState() => new _OTP_EMAIL(
      checkIf: checkIf,
      auth: auth,
      verificationId: verificationId,
      roundSMS: roundSMS,
      phoneNumber: phoneNumber,
      firstName: firstName,
      lastName: lastName,
      refCode: refCode,
      prefixNumber: prefixNumber,
      email: email,
      password: password);
}

setToken(String _token) async {
  SharedPreferences pref = await SharedPreferences.getInstance();
  pref.setString("_token", _token);
  return true;
}

enum FormType { login, register }

class _OTP_EMAIL extends State<OTP_EMAIL> {
  _OTP_EMAIL(
      {this.checkIf,
      this.auth,
      this.verificationId,
      this.roundSMS,
      this.phoneNumber,
      this.firstName,
      this.lastName,
      this.refCode,
      this.prefixNumber,
      this.email,
      this.password});

  final FirebaseAuth? auth;
  final String? verificationId;
  final providerSMS? roundSMS;
  final String? firstName;
  final String? lastName;
  final String? phoneNumber;
  final String? checkIf;
  final String? refCode;
  final String? prefixNumber;
  final String? email;
  final String? password;

  int checkRound = 0;
  late AbstractOTP OTPAbstract;
  // List<String> otp = List<String>();
  List<String> otp = [];
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    OTPAbstract = OTPHandle();
//    OTP = List<String>();
    print(checkIf);

    print(refCode);
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
  }

  final formKey = new GlobalKey<FormState>();

  late String _email;
  late String _password;
  FormType _formType = FormType.login;
  int numberPhone = 0;
  late String dropdownValue;
//  FirebaseAuth _auth = FirebaseAuth.instance;
  int passCodeColo = 0;
  String passCode = '';
  bool _saving = false;

  bool validateAndSave() {
    final form = formKey.currentState;
    if (form!.validate()) {
      form.save();
      return true;
    } else {
      return false;
    }
  }

  void setCode(pass) {
    if (pass.toString() == 'remove') {
      setState(() {
        if (passCodeColo <= 0) {
          passCodeColo = 0;
          passCode = '';
        } else {
          passCodeColo -= 1;
          passCode = passCode.substring(0, passCode.length - 1);
          setState(() {
            otp.removeLast();
          });
        }
      });
    } else {
      setState(() {
        if (passCodeColo > 6) {
          passCodeColo = 6;
        } else {
          passCodeColo += 1;
        }

        passCode += pass.toString();
        setState(() {
          otp.add(pass.toString());
        });
      });
    }
    print(passCode);
    if (passCodeColo == 6) {
      //login
      setState(() {
        _saving = true;
      });
      if (roundSMS == providerSMS.Firebase) {
//        _signInWithPhoneNumber(passCode.trim());
      } else if (roundSMS == providerSMS.Twilio) {
//        _verifyOTPNoFirebase(passCode.trim());
      }
//      _signInWithPhoneNumber(passCode.trim());
      Navigator.push(
          context,
          EnterExitRoute(
              exitPage: OTP_EMAIL(), enterPage: RegisterEmailSuccess()));
    }
  }

  void _verifyOTPNoFirebase(String smsCode) async {
    print(smsCode);

    var url = Uri.https(env.apiUrl, '/verifyOTPNoFirebase');
    var response = await http.post(url, body: {'phone_number': phoneNumber, 'codeVerify': smsCode});

    print('Response status: ${response.statusCode}');
    print('Response body: ${response.body}');
    var body = json.decode(response.body);
    if (body['statusCode'] == 200) {
      //sign in firebase
      setState(() {
        _saving = false;
      });
      if (checkIf == 'register') {
        Navigator.push(
            context,
            EnterExitRoute(
                exitPage: OTP_EMAIL(),
                enterPage: SecretPassFirst(
                    checkIf: checkIf,
                    roundSMS: roundSMS,
                    codeVerify: passCode,
                    phoneNumber: phoneNumber,
                    firstName: firstName,
                    lastName: lastName,
                    refCode: refCode)));

      } else {
        Navigator.push(
            context,
            EnterExitRoute(
                exitPage: OTP_EMAIL(),
                enterPage: SecretPass(
                    checkIf: checkIf,
                    roundSMS: roundSMS,
                    codeVerify: passCode,
                    phoneNumber: phoneNumber,
                    firstName: firstName,
                    lastName: lastName,
                    refCode: refCode)));
      }
    } else {
      setState(() {
        _saving = false;
      });
    }
  }

  reSend() {
    if (checkRound < 1) {
      checkRound = 1;
      setState(() {
        _saving = true;
      });
      OTPAbstract.sendCodeToPhoneNumber(phoneNumber).then((data) {
        setState(() {
          _saving = false;
        });
      });
    }
  }

  void moveToRegister() {
    formKey.currentState!.reset();
    setState(() {
      _formType = FormType.register;
    });
  }

  void moveToLogin() {
    formKey.currentState!.reset();
    setState(() {
      _formType = FormType.login;
    });
  }

  gridviewForPhone(Orientation orientation) {
    return new Scaffold(
      resizeToAvoidBottomInset: false,
      // resizeToAvoidBottomPadding: false,
      backgroundColor: LikeWalletAppTheme.bule2,
      body: new Stack(
//          width: double.infinity,
        alignment: Alignment.bottomCenter,
        children: <Widget>[
          new Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.max,
            children: <Widget>[
              new Padding(
                padding: EdgeInsets.only(
                    top: MediaQuery.of(context).size.width * 0.2),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    new Text(
                      AppLocalizations.of(context)!.translate('otp_n_code'),
                      style: TextStyle(
                          color: LikeWalletAppTheme.white,
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font1'),
                          fontSize: MediaQuery.of(context).size.height *
                              0.01724137931),
                    ),
                    SizedBox(
                      width: MediaQuery.of(context).size.width * 0.02,
                    ),
                    ButtonTheme(
                      minWidth: MediaQuery.of(context).size.width * 0.1,
                      height: MediaQuery.of(context).size.height * 0.0369458128,
                      child: checkRound == 0
                          ? new TextButton(
                              style: ButtonStyle(
                                  shape: MaterialStateProperty.all(new RoundedRectangleBorder(borderRadius: new BorderRadius.circular(5.0),)),
                                  foregroundColor: MaterialStateProperty.all<Color>(LikeWalletAppTheme.gray),
                                  backgroundColor: MaterialStateProperty.all<Color>(LikeWalletAppTheme.gray)
                              ),
                              // shape: new RoundedRectangleBorder(borderRadius: new BorderRadius.circular(5.0),),
                              // disabledColor: LikeWalletAppTheme.gray,
                              // color: LikeWalletAppTheme.gray,
                              onPressed: () {
                                reSend();
                              },
                              child: new Text(
                                AppLocalizations.of(context)!
                            .translate('otp_resend'),
                                style: TextStyle(
                                    fontFamily: AppLocalizations.of(context)!
                            .translate('font1'),
                                    color: Colors.white,
                                    fontSize:
                                        MediaQuery.of(context).size.height *
                                            0.01600985221,
                                    fontWeight: FontWeight.normal),
                              ))
                          : new TextButton(
                                style: ButtonStyle(
                                    shape: MaterialStateProperty.all(new RoundedRectangleBorder(borderRadius: new BorderRadius.circular(5.0),)),
                                    foregroundColor: MaterialStateProperty.all<Color>(Color(0xff000000).withOpacity(0.2)),
                                    backgroundColor: MaterialStateProperty.all<Color>(Color(0xff000000).withOpacity(0.2))
                                ),
                              // shape: new RoundedRectangleBorder(borderRadius: new BorderRadius.circular(5.0),),
                              // disabledColor: Color(0xff000000).withOpacity(0.2),
                              // color: Color(0xff000000).withOpacity(0.2),
                              onPressed: () {  },
                              child: new Text(
                                AppLocalizations.of(context)!
                            .translate('otp_resend'),
                                style: TextStyle(
                                    fontFamily: AppLocalizations.of(context)!
                            .translate('font1'),
                                    color: Colors.white.withOpacity(0.2),
                                    fontSize:
                                        MediaQuery.of(context).size.height *
                                            0.01600985221,
                                    fontWeight: FontWeight.normal),
                              )),
                    ),
                  ],
                ),
              ),
              new Padding(
                padding: EdgeInsets.only(
                    top: MediaQuery.of(context).size.height * 0.1),
                child: new Text(
                  AppLocalizations.of(context)!.translate('otp_code'),
                  style: TextStyle(
                      color: LikeWalletAppTheme.white.withOpacity(0.7),
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      fontWeight: FontWeight.bold,
                      fontSize:
                          MediaQuery.of(context).size.height * 0.02586206896),
                ),
              ),
              new Container(
                  padding: EdgeInsets.only(
                      top: MediaQuery.of(context).size.width * 0.03),
                  child: new Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      new Padding(
                        padding: EdgeInsets.all(
                            MediaQuery.of(context).size.height * 0.002),
                        child: new Container(
                          width: MediaQuery.of(context).size.height * 0.05,
                          height: MediaQuery.of(context).size.height * 0.05,
                          child: new Align(
                            alignment: Alignment.center,
                            child: passCodeColo <= 0
                                ? Text('')
                                : Text(
                                    otp[0],
                                    style: TextStyle(
                                      fontSize:
                                          MediaQuery.of(context).size.height *
                                              0.01923076923,
                                      fontFamily: AppLocalizations.of(context)!
                            .translate('font2'),
                                      fontWeight: FontWeight.normal,
                                      color: LikeWalletAppTheme.white,
                                    ),
                                  ),
                          ),
                          decoration: new BoxDecoration(
                            color: passCodeColo <= 0
                                ? LikeWalletAppTheme.gray
                                : LikeWalletAppTheme.gray,
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                      new Padding(
                        padding: EdgeInsets.all(
                            MediaQuery.of(context).size.height * 0.002),
                        child: new Container(
                          width: MediaQuery.of(context).size.height * 0.05,
                          height: MediaQuery.of(context).size.height * 0.05,
                          child: new Align(
                            alignment: Alignment.center,
                            child: passCodeColo <= 1
                                ? Text('')
                                : Text(
                                    otp[1],
                                    style: TextStyle(
                                        fontSize:
                                            MediaQuery.of(context).size.height *
                                                0.01923076923,
                                        fontFamily: AppLocalizations.of(context)!
                            .translate('font2'),
                                        fontWeight: FontWeight.normal,
                                        color: LikeWalletAppTheme.white),
                                  ),
                          ),
                          decoration: new BoxDecoration(
                            color: passCodeColo <= 1
                                ? LikeWalletAppTheme.gray
                                : LikeWalletAppTheme.gray,
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                      new Padding(
                        padding: EdgeInsets.all(
                            MediaQuery.of(context).size.height * 0.002),
                        child: new Container(
                          width: MediaQuery.of(context).size.height * 0.05,
                          height: MediaQuery.of(context).size.height * 0.05,
                          child: new Align(
                            alignment: Alignment.center,
                            child: passCodeColo <= 2
                                ? Text('')
                                : Text(
                                    otp[2],
                                    style: TextStyle(
                                        fontSize:
                                            MediaQuery.of(context).size.height *
                                                0.01923076923,
                                        fontFamily: AppLocalizations.of(context)!
                            .translate('font2'),
                                        fontWeight: FontWeight.normal,
                                        color: Colors.white),
                                  ),
                          ),
                          decoration: new BoxDecoration(
                            color: passCodeColo <= 2
                                ? LikeWalletAppTheme.gray
                                : LikeWalletAppTheme.gray,
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                      new Padding(
                        padding: EdgeInsets.all(
                            MediaQuery.of(context).size.height * 0.001),
                        child: new Container(
                          width: MediaQuery.of(context).size.height * 0.05,
                          height: MediaQuery.of(context).size.height * 0.05,
                          child: new Align(
                            alignment: Alignment.center,
                            child: passCodeColo <= 3
                                ? Text('')
                                : Text(
                                    otp[3],
                                    style: TextStyle(
                                        fontSize:
                                            MediaQuery.of(context).size.height *
                                                0.01923076923,
                                        fontFamily: AppLocalizations.of(context)!
                            .translate('font2'),
                                        fontWeight: FontWeight.normal,
                                        color: LikeWalletAppTheme.white),
                                  ),
                          ),
                          decoration: new BoxDecoration(
                            color: passCodeColo <= 2
                                ? LikeWalletAppTheme.gray
                                : LikeWalletAppTheme.gray,
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                      new Padding(
                        padding: EdgeInsets.all(
                            MediaQuery.of(context).size.height * 0.002),
                        child: new Container(
                          width: MediaQuery.of(context).size.height * 0.05,
                          height: MediaQuery.of(context).size.height * 0.05,
                          child: new Align(
                            alignment: Alignment.center,
                            child: passCodeColo <= 4
                                ? Text('')
                                : Text(
                                    otp[4],
                                    style: TextStyle(
                                        fontSize:
                                            MediaQuery.of(context).size.height *
                                                0.01923076923,
                                        fontFamily: AppLocalizations.of(context)!
                            .translate('font2'),
                                        fontWeight: FontWeight.normal,
                                        color: LikeWalletAppTheme.white),
                                  ),
                          ),
                          decoration: new BoxDecoration(
                            color: passCodeColo <= 4
                                ? LikeWalletAppTheme.gray
                                : LikeWalletAppTheme.gray,
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                      new Padding(
                        padding: EdgeInsets.all(
                            MediaQuery.of(context).size.height * 0.002),
                        child: new Container(
                          width: MediaQuery.of(context).size.height * 0.05,
                          height: MediaQuery.of(context).size.height * 0.05,
                          child: new Align(
                            alignment: Alignment.center,
                            child: passCodeColo <= 5
                                ? Text('')
                                : Text(
                                    otp[5],
                                    style: TextStyle(
                                        fontSize:
                                            MediaQuery.of(context).size.height *
                                                0.01923076923,
                                        fontFamily: AppLocalizations.of(context)!
                            .translate('font2'),
                                        fontWeight: FontWeight.normal,
                                        color: LikeWalletAppTheme.white),
                                  ),
                          ),
                          decoration: new BoxDecoration(
                            color: passCodeColo <= 5
                                ? LikeWalletAppTheme.gray
                                : LikeWalletAppTheme.gray,
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                    ],
                  )),
            ],
          ),
          new Container(
            padding:
                EdgeInsets.only(top: MediaQuery.of(context).size.height * 0.1),
            child: new Container(
              width: MediaQuery.of(context).size.width * 0.75,
//                  padding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.05),
              child: new Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  for (var a = 0; a < 3; a++)
                    new Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        for (var i = 1; i <= 3; i++)
                          if (a == 0)
                            new Expanded(
//                                  padding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.02),
                                child: Column(
                              children: <Widget>[
                                TextButton(
                                  onPressed: () => setCode(i),
                                  child: new Text(
                                    i.toString(),
                                    style: TextStyle(
                                        fontSize:
                                            MediaQuery.of(context).size.height *
                                                0.04556650246,
                                        fontFamily: 'Nimbus Sans',
                                        fontWeight: FontWeight.normal,
                                        color: LikeWalletAppTheme.white),
                                  ),
                                ),
                                SizedBox(
                                    height: MediaQuery.of(context).size.height *
                                        0.02)
                              ],
                            ))
                          else if (a == 1)
                            new Expanded(
//                                  padding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.02),
                                child: Column(
                              children: <Widget>[
                                TextButton(
                                  onPressed: () => setCode(i + 3),
                                  child: new Text(
                                    (i + 3).toString(),
                                    style: TextStyle(
                                        fontSize:
                                            MediaQuery.of(context).size.height *
                                                0.04556650246,
                                        fontFamily: 'Nimbus Sans',
                                        fontWeight: FontWeight.normal,
                                        color: LikeWalletAppTheme.white),
                                  ),
                                ),
                                SizedBox(
                                    height: MediaQuery.of(context).size.height *
                                        0.02)
                              ],
                            ))
                          else if (a == 2)
                            new Expanded(
//                                    padding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.02),
                                child: Column(
                              children: <Widget>[
                                TextButton(
                                  onPressed: () => setCode(i + 6),
                                  child: new Text(
                                    (i + 6).toString(),
                                    style: TextStyle(
                                        fontSize:
                                            MediaQuery.of(context).size.height *
                                                0.04556650246,
                                        fontFamily: 'Nimbus Sans',
                                        fontWeight: FontWeight.normal,
                                        color: LikeWalletAppTheme.white),
                                  ),
                                ),
                                SizedBox(
                                    height: MediaQuery.of(context).size.height *
                                        0.02)
                              ],
                            ))
                      ],
                    ),
                  new Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      new Expanded(
//                            padding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.02),
                          child: TextButton(
                        onPressed: () => setCode(0),
                        child: new Text(''),
                      )),
                      new Expanded(
//                            padding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.02),
                          child: TextButton(
                        onPressed: () => setCode(0),
                        child: new Text(
                          '0',
                          style: TextStyle(
                              fontSize: MediaQuery.of(context).size.height *
                                  0.04556650246,
                              fontFamily: 'Nimbus Sans',
                              fontWeight: FontWeight.normal,
                              color: LikeWalletAppTheme.white),
                        ),
                      )),
                      new Expanded(
//                            padding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.01),
                          child: TextButton(
                              onPressed: () => setCode('remove'),
                              child: new Icon(Icons.backspace,
                                  size: MediaQuery.of(context).size.height *
                                      0.04556650246,
                                  color: LikeWalletAppTheme.white)))
                    ],
                  )
                ],
              ),
            ),
          )
        ],
      ),
    );
  }

  gridviewForTablet(Orientation orientation) {
    return new Scaffold(
      resizeToAvoidBottomInset: false,
      // resizeToAvoidBottomPadding: false,
      backgroundColor: Color(0xff13D7C8),
      body: new Container(
          width: double.infinity,
          alignment: Alignment.bottomCenter,
          child: new Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.max,
            children: <Widget>[
              new Padding(
                padding: EdgeInsets.only(
                    top: MediaQuery.of(context).size.width * 0.2),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    new Text(
                      'Didn’t get the code?    ',
                      style:
                          TextStyle(color: Color(0xff000000), fontSize: 28.0),
                    ),
                    ButtonTheme(
                      minWidth: MediaQuery.of(context).size.width * 0.1,
                      height: MediaQuery.of(context).size.width * 0.07,
                      child:TextButton(
                        style: ButtonStyle(
                          shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                            RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(5.0),
                            ),
                          ),
                          backgroundColor: MaterialStateProperty.resolveWith<Color>(
                                (Set<MaterialState> states) {
                              if (states.contains(MaterialState.disabled)) {
                                return Color(0xff000000); // Disabled color
                              }
                              return Color(0xff000000); // Regular color
                            },
                          ),
                        ),
                        onPressed: () {
                          // Your onPressed logic goes here
                        },
                        child: Text(
                          'Resend',
                          style: TextStyle(
                            fontSize: 28,
                            color: Colors.white,
                            fontWeight: FontWeight.normal,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              new Padding(
                padding: EdgeInsets.only(
                    top: MediaQuery.of(context).size.width * 0.15),
                child: new Text(
                  'Enter Your Passcode',
                  style: TextStyle(color: Color(0xff5B5E5C), fontSize: 28.0),
                ),
              ),
              new Padding(
                  padding: EdgeInsets.only(
                      top: MediaQuery.of(context).size.width * 0.03),
                  child: new Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      new Padding(
                        padding: EdgeInsets.all(2.0),
                        child: new Container(
                          width: MediaQuery.of(context).size.width * 0.05,
                          height: MediaQuery.of(context).size.width * 0.05,
                          decoration: new BoxDecoration(
                            color: passCodeColo == 0
                                ? Colors.white
                                : Color(0xff3C3C3C),
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                      new Padding(
                        padding: EdgeInsets.all(2.0),
                        child: new Container(
                          width: MediaQuery.of(context).size.width * 0.05,
                          height: MediaQuery.of(context).size.width * 0.05,
                          decoration: new BoxDecoration(
                            color: passCodeColo <= 1
                                ? Colors.white
                                : Color(0xff3C3C3C),
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                      new Padding(
                        padding: EdgeInsets.all(2.0),
                        child: new Container(
                          width: MediaQuery.of(context).size.width * 0.05,
                          height: MediaQuery.of(context).size.width * 0.05,
                          decoration: new BoxDecoration(
                            color: passCodeColo <= 2
                                ? Colors.white
                                : Color(0xff3C3C3C),
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                      new Padding(
                        padding: EdgeInsets.all(2.0),
                        child: new Container(
                          width: MediaQuery.of(context).size.width * 0.05,
                          height: MediaQuery.of(context).size.width * 0.05,
                          decoration: new BoxDecoration(
                            color: passCodeColo <= 3
                                ? Colors.white
                                : Color(0xff3C3C3C),
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                      new Padding(
                        padding: EdgeInsets.all(2.0),
                        child: new Container(
                          width: MediaQuery.of(context).size.width * 0.05,
                          height: MediaQuery.of(context).size.width * 0.05,
                          decoration: new BoxDecoration(
                            color: passCodeColo <= 4
                                ? Colors.white
                                : Color(0xff3C3C3C),
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                      new Padding(
                        padding: EdgeInsets.all(2.0),
                        child: new Container(
                          width: MediaQuery.of(context).size.width * 0.05,
                          height: MediaQuery.of(context).size.width * 0.05,
                          decoration: new BoxDecoration(
                            color: passCodeColo <= 5
                                ? Colors.white
                                : Color(0xff3C3C3C),
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                    ],
                  )),
              new Padding(
                padding: EdgeInsets.only(
                    top: MediaQuery.of(context).size.width * 0.2),
                child: new Container(
                  child: new Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      for (var a = 0; a < 3; a++)
                        new Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: <Widget>[
                            for (var i = 1; i <= 3; i++)
                              if (a == 0)
                                new Padding(
                                    padding: EdgeInsets.all(
                                        MediaQuery.of(context).size.width *
                                            0.05),
                                    child: TextButton(
                                      onPressed: () => setCode(i),
                                      child: new Text(
                                        i.toString(),
                                        style: TextStyle(fontSize: 50.0),
                                      ),
                                    ))
                              else if (a == 1)
                                new Padding(
                                    padding: EdgeInsets.all(
                                        MediaQuery.of(context).size.width *
                                            0.05),
                                    child: TextButton(
                                      onPressed: () => setCode(i + 3),
                                      child: new Text(
                                        (i + 3).toString(),
                                        style: TextStyle(fontSize: 50.0),
                                      ),
                                    ))
                              else if (a == 2)
                                new Padding(
                                    padding: EdgeInsets.all(
                                        MediaQuery.of(context).size.width *
                                            0.05),
                                    child: TextButton(
                                      onPressed: () => setCode(i + 6),
                                      child: new Text(
                                        (i + 6).toString(),
                                        style: TextStyle(fontSize: 50.0),
                                      ),
                                    ))
                          ],
                        ),
                      new Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: <Widget>[
                          new Padding(
                              padding: EdgeInsets.only(
                                  right: MediaQuery.of(context).size.width *
                                      0.21)),
                          new Padding(
                              padding: EdgeInsets.all(
                                  MediaQuery.of(context).size.width * 0.04),
                              child: TextButton(
                                onPressed: () => setCode(0),
                                child: new Text(
                                  '0',
                                  style: TextStyle(fontSize: 50.0),
                                ),
                              )),
                          new Padding(
                              padding: EdgeInsets.all(
                                  MediaQuery.of(context).size.width * 0.05),
                              child: TextButton(
                                  onPressed: () => setCode('remove'),
                                  child: new Icon(
                                    Icons.backspace,
                                    size: 50,
                                  )))
                        ],
                      )
                    ],
                  ),
                ),
              )
            ],
          )),
    );
  }

  @override
  Widget build(BuildContext context) {
    final double shortestSide = MediaQuery.of(context).size.shortestSide;
    final bool useMobileLayout = shortestSide < 600.0;
    final Orientation orientation = MediaQuery.of(context).orientation;
    return Scaffold(
        body: ModalProgressHUD(
      opacity: 0.1,
      child: useMobileLayout
          ? gridviewForPhone(orientation)
          : gridviewForPhone(orientation),
      inAsyncCall: _saving,
      progressIndicator: CustomLoading(),
    ));
  }

  /**
   *
   * phone_number
   * */
  /// Sign in using an sms code as input.

  void _signInWithPhoneNumber(String smsCode) async {
    final AuthCredential credential = PhoneAuthProvider.credential(
      verificationId: verificationId!,
      smsCode: smsCode,
    );
    final UserCredential user = await auth!.signInWithCredential(credential);
    // print(auth!.currentUser);
    user.user!.getIdTokenResult().then((_token) {
      setToken(_token.token.toString());
      // print(_token.token);
      setState(() {
        _saving = false;
      });

      if (checkIf == 'register') {
        Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) => SecretPassFirst(
                    checkIf: checkIf,
                    roundSMS: roundSMS,
                    firstName: firstName,
                    lastName: lastName,
                    refCode: refCode,
                  )),
        );
      } else {
        Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) => SecretPass(
                    checkIf: checkIf,
                    roundSMS: roundSMS,
                    firstName: firstName,
                    lastName: lastName,
                    refCode: refCode,
                  )),
        );
      }
    });
    // ตั้งค่า setFirstTime เป็น ture
    SharedPreferences pref = await SharedPreferences.getInstance();
    pref.setBool('setFirstTime', true);
  }
}

class CircleButton extends StatelessWidget {
  final GestureTapCallback? onTap;

  const CircleButton({Key? key, this.onTap, color}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    double size = 20.0;

    return new InkResponse(
      onTap: onTap,
      child: new Container(
        width: size,
        height: size,
        decoration: new BoxDecoration(
          color: Colors.white,
          shape: BoxShape.circle,
        ),
      ),
    );
  }
}
