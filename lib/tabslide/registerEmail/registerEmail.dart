import 'package:flutter/material.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/animationPage.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/tabslide/registerEmail/OTPEmail.dart';

import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/animationPage.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/tabslide/registerEmail/OTPEmail.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:likewallet/libraryman/serviceHTTP.dart';
import 'package:likewallet/tabslide/registerEmail/registerEmail_Success.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:likewallet/app_config.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_screenutil/flutter_screenutil.dart';

class RegisterEmail extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return _RegisterEmail();
  }
}

class _RegisterEmail extends State<RegisterEmail> {
  TextEditingController email = new TextEditingController();
  TextEditingController password = new TextEditingController();
  TextEditingController cfpassword = new TextEditingController();
  TextEditingController secret = new TextEditingController();

  bool _emailSync = false;
  bool _autoValidate = false;
  bool _saving = true;
  late AbstractServiceHTTP apiCall;
  late BaseAuth auth;

  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    apiCall = ServiceHTTP();
    auth = Auth();
    auth.getTokenFirebase().then((_token) {
      apiCall.checkSync(token: _token!).then((_email) {
        if (_email != 'no') {
          if (!mounted) return;
          setState(() {
            _saving = false;
            email.text = _email;
            _emailSync = true;
          });
        } else {
          if (!mounted) return;
          setState(() {
            _saving = false;
          });
        }
      });
    });
  }

  void showShortToast(msg, Color color) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_SHORT,
        backgroundColor: color,
        textColor: Colors.white);
  }

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return ModalProgressHUD(
        inAsyncCall: _saving,
        opacity: 0.1,
        progressIndicator: CustomLoading(),
        child: GestureDetector(
          onTap: () {
            FocusScopeNode currentFocus = FocusScope.of(context);
            if (!currentFocus.hasPrimaryFocus) {
              currentFocus.unfocus();
            }
          },
          child: Scaffold(
            backgroundColor: LikeWalletAppTheme.bule2,
            body: SingleChildScrollView(
              child: Form(
                key: _formKey,
                // autovalidate: _autoValidate,
                child: Container(
                  height: MediaQuery.of(context).size.height,
                  decoration: BoxDecoration(
                    image: DecorationImage(
                      image: AssetImage('assets/image/back.png'),
                      fit: BoxFit.cover,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: <Widget>[
                      //พื้นหลัง
                      Container(
                        height: mediaQuery(context, 'height', 499),
                        decoration: BoxDecoration(
                            color: LikeWalletAppTheme.bule2,
                            boxShadow: [
                              BoxShadow(
                                spreadRadius: 1.0,
                                blurRadius: 8.0,
                                color:
                                    LikeWalletAppTheme.black.withOpacity(0.5),
                                offset: Offset(0, 3),
                              )
                            ]),
                        child: Column(
                          children: [
                            //พื้นหลัง
                            Container(
                              padding: EdgeInsets.only(
                                top: 132.h,
                                left: 0,
                              ),
                              child:
                                  backButton(context, LikeWalletAppTheme.gray),
                            ),
                            SizedBox(
                              height: mediaQuery(context, 'height', 50),
                            ),
                            Expanded(
                              child: Container(
                                width: mediaQuery(context, 'width', 927),
                                child: Text(
                                  AppLocalizations.of(context)!
                                      .translate('login_email_head'),
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    color: LikeWalletAppTheme.white
                                        .withOpacity(0.6),
                                    letterSpacing: 0.3,
                                    fontSize: mediaQuery(context, 'height', 39),
                                    fontFamily: AppLocalizations.of(context)!
                                        .translate('font1'),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      _emailSync == true
                          ? Padding(
                              padding: EdgeInsets.only(
                                  top: mediaQuery(context, 'height', 52)),
                              child: Card(
                                  child: Container(
                                width: mediaQuery(context, 'height', 940),
                                height: mediaQuery(context, 'height', 100),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: <Widget>[
                                    Text(
                                      AppLocalizations.of(context)!
                                          .translate('registeredEmail_head'),
                                      style: TextStyle(
                                        color: LikeWalletAppTheme.bule3,
                                        fontSize:
                                            mediaQuery(context, 'height', 50),
                                        fontFamily:
                                            AppLocalizations.of(context)!
                                                .translate('font1'),
                                      ),
                                    ),
                                    Icon(
                                      Icons.check_circle,
                                      color: LikeWalletAppTheme.bule3,
                                    )
                                  ],
                                ),
                              )),
                            )
                          : Container(),
                      //input email
                      Padding(
                        padding: EdgeInsets.only(
                            top: mediaQuery(context, 'height', 52)),
                        child: _input(
                            'registerEmail_email',
                            email,
                            TextInputType.emailAddress,
                            false,
                            'registerEmail_textField_email',
                            validateEmail),
                      ),
                      //input password
                      Padding(
                        padding: EdgeInsets.only(
                            top: mediaQuery(context, 'height', 52)),
                        child: _input(
                            'registerEmail_password',
                            password,
                            TextInputType.text,
                            true,
                            'registerEmail_textField_password',
                            validatePassword),
                      ),
                      Padding(
                        padding: EdgeInsets.only(
                            top: mediaQuery(context, 'height', 52)),
                        child: _input(
                            'registerEmail_password_again',
                            cfpassword,
                            TextInputType.text,
                            true,
                            'registerEmail_textField_password',
                            validatePassword),
                      ),
                      //ปุ่มลงทะเบียน
//                Padding(
//                  padding:
//                  EdgeInsets.only(top: mediaQuery(context, 'height', 50)),
//                  child: _input(
//                      'registerEmail_secret',
//                      secret,
//                      TextInputType.text,
//                      true,
//                      'registerEmail_textField_secret',
//                      validatePassword),
//                ),
                      Padding(
                        padding: EdgeInsets.only(
                            top: mediaQuery(context, 'height', 98)),
                        child: _button(),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ));
  }

  Widget _input(
      hintText, controller, TextInputType, bool, validatorText, validator) {
    return Container(
      margin: EdgeInsets.only(
          top: mediaQuery(context, 'height', 20),
          bottom: mediaQuery(context, 'height', 20)),
      decoration: BoxDecoration(
        color: Color(0xff141322),
        border: Border.all(
          color: Color(0xff6C6B6D),
          width: 1.0,
        ),
        borderRadius: BorderRadius.all(Radius.circular(10.0)),
      ),
      alignment: Alignment.center,
      height: mediaQuery(context, 'height', 150),
      width: mediaQuery(context, 'width', 930),
      child: TextFormField(
          controller: controller,
          style: TextStyle(
              letterSpacing: 0.3,
              fontSize: mediaQuery(context, 'height', 47),
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              color: LikeWalletAppTheme.white),
          decoration: InputDecoration(
//            focusedBorder: OutlineInputBorder(
//                borderSide: BorderSide(color: LikeWalletAppTheme.bule1)),
//            enabledBorder: OutlineInputBorder(
//                borderSide: BorderSide(
//                    width: mediaQuery(context, 'width', 1),
//                    color: LikeWalletAppTheme.bule1)),
            contentPadding:
                EdgeInsets.only(left: mediaQuery(context, 'width', 70)),
            border: InputBorder.none,
            hoverColor: Colors.white,
            disabledBorder: InputBorder.none,
            focusColor: Colors.white,
            alignLabelWithHint: true,
            fillColor: Colors.white,
            hintText: AppLocalizations.of(context)!.translate(hintText),
            hintStyle: TextStyle(
                letterSpacing: 0.3,
                fontSize: mediaQuery(context, 'height', 47),
                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                color: LikeWalletAppTheme.gray),
          ),
          obscureText: bool,
//          validator: validator,
          keyboardType: TextInputType),
    );
  }

  Widget _button() {
    return new Container(
      alignment: Alignment.center,
      width: mediaQuery(context, 'width', 930),
      child: ButtonTheme(
        minWidth: mediaQuery(context, 'width', 930),
        height: mediaQuery(context, 'height', 132),
        child: TextButton(
          style: ButtonStyle(
            shape: MaterialStateProperty.all<RoundedRectangleBorder>(
              RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(5.0),
              ),
            ),
            backgroundColor: MaterialStateProperty.resolveWith<Color>(
                  (Set<MaterialState> states) {
                if (states.contains(MaterialState.disabled)) {
                  return LikeWalletAppTheme.white; // Disabled color
                }
                return LikeWalletAppTheme.bule1; // Regular color
              },
            ),
          ),
          onPressed: _validateInputs,
          child: Text(
            AppLocalizations.of(context)!.translate('registerEmail_send'),
            style: TextStyle(
              letterSpacing: 0.3,
              color: LikeWalletAppTheme.black,
              fontSize: mediaQuery(context, 'height', 45),
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  Future<bool> syncEmailWithPhoneNumber() async {
    var url = Uri.https(env.apiUrl, '/syncEmailWithPhoneNumber');
    String? _token = await auth.getTokenFirebase();
    print(email.text.trim().toString());
    var response = await http.post(url, body: {
      'email': email.text.trim().toString(),
      '_token': _token,
      'password': password.text.trim()
    });
    print('Response status: ${response.statusCode}');
    print('Response body: ${response.body}');
    var body = json.decode(response.body);
    print(body['statusCode']);
    print(body);
    if (body['statusCode'] == 200) {
      return true;
    } else {
      print(body['statusCode']);
      return false;
    }
  }

  Future<bool> verifySecret() async {
    final storage = new FlutterSecureStorage();
    String? _secret = await storage.read(key: 'secretpass');
    if (secret.text.trim() == _secret) {
      return true;
    } else {
      return false;
    }
  }

  void _validateInputs() {
    if (!mounted) return;
    setState(() {
      _saving = true;
    });
    if (password.text.trim() == cfpassword.text.trim()) {
      if (_formKey.currentState!.validate()) {
        auth.getTokenFirebase().then((_token) {
//          verifySecret().then((verify){
          apiCall
              .checkEmailExists(
                  email: email.text.trim(),
                  token: _token!,
                  password: password.text.trim())
              .then((result) {
            if (result == 'true') {
//                if(verify){
              syncEmailWithPhoneNumber().then((syncResult) {
                if (syncResult) {
                  setState(() {
                    _saving = false;
                  });
                  //    If all data are correct then save data to out variables
                  Navigator.push(
                      context,
                      EnterExitRoute(
                          exitPage: RegisterEmail(),
                          enterPage: RegisterEmailSuccess()));
                  _formKey.currentState!.save();
                } else {
                  setState(() {
                    _saving = false;
                  });
                  showShortToast(
                      AppLocalizations.of(context)!.translate('save_err'),
                      Colors.red);
                }
              });
//
//                }else{
//                  setState(() {
//                    _saving = false;
//                  });
//                  showShortToast(AppLocalizations.of(context)!.translate('choice_user_secret_wrong_title'), Colors.red);
//                }

            } else if (result == 'update') {
              setState(() {
                _saving = false;
              });
              Navigator.push(
                  context,
                  EnterExitRoute(
                      exitPage: RegisterEmail(),
                      enterPage: RegisterEmailSuccess()));
              _formKey.currentState!.save();
            } else {
              setState(() {
                _saving = false;
              });
              showShortToast(
                  AppLocalizations.of(context)!.translate('email_exists'),
                  Colors.red);
            }
          });
        });
//        });

      } else {
//    If all data are not valid then start auto validation.
        if (!mounted) return;
        setState(() {
          _saving = false;
          _autoValidate = true;
        });
      }
    } else {
      //password incorrect
      showShortToast(
          AppLocalizations.of(context)!.translate('password_different'),
          Colors.red);
      if (!mounted) return;
      setState(() {
        _saving = false;
      });
    }
  }

  String validateEmail(String value) {
    Pattern pattern =
        r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$';
    RegExp regex = new RegExp(pattern.toString());
    if (!regex.hasMatch(value))
      return AppLocalizations.of(context)!
          .translate('registerEmail_textField_email');
    else
      return 'error';
  }

  String validatePassword(String value) {
    if (value.length < 6)
      return AppLocalizations.of(context)!
          .translate('registerEmail_textField_password');
    else
      return 'error';
  }
}
