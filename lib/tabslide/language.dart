import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:likewallet/main.dart';
import 'package:likewallet/screen/NavigationBar.dart';
import 'package:likewallet/Theme.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:likewallet/screen/home.dart';
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/libraryman/applang.dart';
import 'package:provider/provider.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class Language extends StatefulWidget {
  @override
  State<StatefulWidget> createState() => _Language();
}

class _Language extends State<Language> {
  late SharedPreferences sharedPreferences;
  bool _ENG = false;
  bool _THAI = false;
  bool _LAO = false;
  bool _CAM = false;
  bool _VIE = false;
  int count = 0;

  @override
  void initState() {
    super.initState();
    chackCurrency();
  }

  @override
  Widget build(BuildContext context) {
    final width930 = mediaQuery(context, "height", 930);
    var appLanguage = Provider.of<AppLanguage>(context);
    return Material(
        child: Scaffold(
      backgroundColor: Color(0xff141322),
      body: Stack(
        alignment: Alignment.center,
        children: <Widget>[
          //ปุ่ม back
          Positioned(
              top: MediaQuery.of(context).size.height *
                  Screen_util("height", 136),
              left: 0.w,
              child: backButton(context, LikeWalletAppTheme.gray)),
          //head
          Positioned(
              top: mediaQuery(context, "height", 319),
              child: _titleLanguage(width930)),
          //รายการเลือก
          Positioned(
              top: mediaQuery(context, "height", 489),
              child: _selectBody(width930, appLanguage))
        ],
      ),
    ));
  }

  Widget _titleLanguage(width930) {
    return new Container(
        alignment: Alignment.centerLeft,
        width: width930,
        child: Text(
          AppLocalizations.of(context)!.translate('language'),
          style: TextStyle(
            color: LikeWalletAppTheme.gray,
            fontSize: mediaQuery(context, "height", 89),
            fontFamily: 'Proxima Nova',
          ),
        ));
  }

  Widget _selectBody(width930, appLanguage) {
    return Column(
      children: <Widget>[
        _english(width930, appLanguage),
        _thai(width930, appLanguage),
        _lao(width930, appLanguage),
        _canbodia(width930, appLanguage),
        _vietnam(width930, appLanguage)
      ],
    );
  }

  Widget _english(width930, appLanguage) {
    return Column(
      children: <Widget>[
        new Container(
            alignment: Alignment.centerLeft,
            width: width930,
            child: TextButton(
                style: TextButton.styleFrom(
                  padding: EdgeInsets.all(0),
                ),
                onPressed: () {
                  appLanguage.changeLanguage(Locale("en"));
                  setState(() {
                    _ENG = true;
                    print(_ENG);
                    if (_ENG == true) {
                      _THAI = false;
                      _LAO = false;
                      _CAM = false;
                      _VIE = false;
                      _onPressed(_ENG, _THAI, _LAO, _CAM, _VIE);
                    }
                    Intl.defaultLocale = 'en';
                    count = 0;
                    Navigator.popUntil(context, (route) {
                      return count++ == 2;
                    });
                  });
                },
                child: Row(
                  children: <Widget>[
                    Text(
                      AppLocalizations.of(context)!.translate('language_us'),
                      style: TextStyle(
                        fontFamily: 'Proxima Nova',
                        fontWeight: FontWeight.normal,
                        fontSize: mediaQuery(context, 'height', 45),
                        color: LikeWalletAppTheme.gray1,
                      ),
                    ),
                    if (_ENG == true)
                      Expanded(
                        child: Align(
                          alignment: Alignment.centerRight,
                          child: new Icon(
                            Icons.check,
                            size: mediaQuery(context, "height", 45),
                            color: LikeWalletAppTheme.gray1,
                          ),
                        ),
                      )
                  ],
                ))),
        border(width930)
      ],
    );
  }

  Widget _thai(width930, appLanguage) {
    return Column(
      children: <Widget>[
        new Container(
            alignment: Alignment.centerLeft,
            width: width930,
            child: TextButton(
                style: TextButton.styleFrom(
                  padding: EdgeInsets.all(0),
                ),
                onPressed: () {
                  appLanguage.changeLanguage(Locale("th"));
                  setState(() {
                    _THAI = true;
                    print(_THAI);
                    if (_THAI == true) {
                      _ENG = false;
                      _LAO = false;
                      _CAM = false;
                      _VIE = false;
                      _onPressed(_ENG, _THAI, _LAO, _CAM, _VIE);
                    }
                    Intl.defaultLocale = 'th';
                    count = 0;
                    Navigator.popUntil(context, (route) {
                      return count++ == 2;
                    });
                  });
                },
                child: Row(
                  children: <Widget>[
                    Text(
                      AppLocalizations.of(context)!.translate('language_thai'),
                      style: TextStyle(
                        fontFamily: 'Prompt',
                        fontWeight: FontWeight.normal,
                        fontSize: mediaQuery(context, 'height', 45),
                        color: LikeWalletAppTheme.gray1,
                      ),
                    ),
                    if (_THAI == true)
                      Expanded(
                        child: Align(
                          alignment: Alignment.centerRight,
                          child: new Icon(
                            Icons.check,
                            size: mediaQuery(context, "height", 45),
                            color: LikeWalletAppTheme.gray,
                          ),
                        ),
                      )
                  ],
                ))),
        border(width930)
      ],
    );
  }

  Widget _lao(width930, appLanguage) {
    return Column(
      children: <Widget>[
        new Container(
            alignment: Alignment.centerLeft,
            width: width930,
            child: TextButton(
                style: TextButton.styleFrom(
                  padding: EdgeInsets.all(0),
                ),
                onPressed: () {
                  appLanguage.changeLanguage(Locale("lo"));
                  setState(() {
                    _LAO = true;
                    print(_LAO);
                    if (_LAO == true) {
                      _ENG = false;
                      _THAI = false;
                      _CAM = false;
                      _VIE = false;
                      _onPressed(_ENG, _THAI, _LAO, _CAM, _VIE);
                    }
                    Intl.defaultLocale = 'en';
                    count = 0;
                    Navigator.popUntil(context, (route) {
                      return count++ == 2;
                    });
                  });
                },
                child: Row(
                  children: <Widget>[
                    Text(
                      AppLocalizations.of(context)!.translate('language_lao'),
                      style: TextStyle(
                        fontFamily: 'Proxima Nova',
                        fontWeight: FontWeight.normal,
                        fontSize: mediaQuery(context, 'height', 45),
                        color: LikeWalletAppTheme.gray1,
                      ),
                    ),
                    if (_LAO == true)
                      Expanded(
                        child: Align(
                          alignment: Alignment.centerRight,
                          child: new Icon(Icons.check,
                              size: mediaQuery(context, "height", 45),
                              color: LikeWalletAppTheme.gray),
                        ),
                      ),
                  ],
                ))),
        border(width930)
      ],
    );
  }

  Widget _canbodia(width930, appLanguage) {
    return Column(
      children: <Widget>[
        new Container(
            alignment: Alignment.centerLeft,
            width: width930,
            child: TextButton(
                style: TextButton.styleFrom(
                  padding: EdgeInsets.all(0),
                ),
                onPressed: () {
                  appLanguage.changeLanguage(Locale("km"));
                  setState(() {
                    _CAM = true;
                    print(_CAM);
                    if (_CAM == true) {
                      _ENG = false;
                      _THAI = false;
                      _LAO = false;
                      _VIE = false;
                      _onPressed(_ENG, _THAI, _LAO, _CAM, _VIE);
                    }
                    Intl.defaultLocale = 'en';
                    count = 0;
                    Navigator.popUntil(context, (route) {
                      return count++ == 2;
                    });
                  });
                },
                child: Row(
                  children: <Widget>[
                    Text(
                      AppLocalizations.of(context)!.translate('language_cam'),
                      style: TextStyle(
                        fontFamily: 'Proxima Nova',
                        fontWeight: FontWeight.normal,
                        fontSize: mediaQuery(context, 'height', 45),
                        color: LikeWalletAppTheme.gray1,
                      ),
                    ),
                    if (_CAM == true)
                      Expanded(
                        child: Align(
                          alignment: Alignment.centerRight,
                          child: new Icon(
                            Icons.check,
                            size: mediaQuery(context, "height", 45),
                            color: LikeWalletAppTheme.gray,
                          ),
                        ),
                      )
                  ],
                ))),
        border(width930)
      ],
    );
  }

  Widget _vietnam(width930, appLanguage) {
    return Column(
      children: <Widget>[
        new Container(
            alignment: Alignment.centerLeft,
            width: width930,
            child: TextButton(
                style: TextButton.styleFrom(
                  padding: EdgeInsets.all(0),
                ),
                onPressed: () {
                  appLanguage.changeLanguage(Locale("vi"));

                  setState(() {
                    _VIE = true;
                    print(_VIE);
                    if (_VIE == true) {
                      _ENG = false;
                      _THAI = false;
                      _LAO = false;
                      _CAM = false;
                      _onPressed(_ENG, _THAI, _LAO, _CAM, _VIE);
                    }
                    Intl.defaultLocale = 'en';
                    count = 0;
                    Navigator.popUntil(context, (route) {
                      return count++ == 2;
                    });
                  });
                },
                child: Row(
                  children: <Widget>[
                    Text(
                      AppLocalizations.of(context)!.translate('language_vie'),
                      style: TextStyle(
                        fontFamily: 'Proxima Nova',
                        fontWeight: FontWeight.normal,
                        fontSize: mediaQuery(context, 'height', 45),
                        color: LikeWalletAppTheme.gray1,
                      ),
                    ),
                    if (_VIE == true)
                      Expanded(
                        child: Align(
                          alignment: Alignment.centerRight,
                          child: new Icon(Icons.check,
                              size: mediaQuery(context, "height", 45),
                              color: LikeWalletAppTheme.gray),
                        ),
                      )
                  ],
                ))),
        border(width930)
      ],
    );
  }

  Widget border(width930) {
    return Container(
        margin: EdgeInsets.only(
          top: mediaQuery(context, 'hieght', 0),
          bottom: mediaQuery(context, 'hieght', 0),
        ),
        alignment: Alignment.centerLeft,
        width: width930,
        child: new Container(
          //                  color: Colors.blue,
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                //                   <--- left side
                color: Color(0xff707071),
                width: mediaQuery(context, "width", 1),
              ),
            ),
          ),
        ));
  }

  _onPressed(
      bool value1, bool value2, bool value3, bool value4, bool value5) async {
    sharedPreferences = await SharedPreferences.getInstance();
    setState(() {
      print(_ENG);
      print(_THAI);
      print(_LAO);
      print(_CAM);
      print(_VIE);
      sharedPreferences.setBool("ENG", _ENG);
      sharedPreferences.setBool("THAI", _THAI);
      sharedPreferences.setBool("LAO", _LAO);
      sharedPreferences.setBool("CAM", _CAM);
      sharedPreferences.setBool("VIE", _VIE);
      sharedPreferences.commit();
    });
  }

  chackCurrency() async {
    sharedPreferences = await SharedPreferences.getInstance();
    setState(() {
      _ENG = sharedPreferences.getBool("ENG") ?? false;
      _THAI = sharedPreferences.getBool("THAI") ?? false;
      _LAO = sharedPreferences.getBool("LAO") ?? false;
      _CAM = sharedPreferences.getBool("CAM") ?? false;
      _VIE = sharedPreferences.getBool("VIE") ?? false;
    });
  }
}
