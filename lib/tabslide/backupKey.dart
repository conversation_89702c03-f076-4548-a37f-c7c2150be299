import 'package:flare_flutter/flare_actor.dart';
import 'package:flutter/material.dart';
import 'package:flutter/painting.dart';
import 'package:likewallet/animationPage.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/routes.dart';
import 'package:likewallet/screen/home.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:likewallet/screen_util.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:likewallet/libraryman/auth.dart';

class BackupSecretKey extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return _BackupSecretKey();
  }
}

class _BackupSecretKey extends State<BackupSecretKey> {
  bool keyVisible = true;
  bool _saving = false;
  late SharedPreferences prefs;
  late BaseAuth auth;
  TextEditingController _secret = TextEditingController();
  FirebaseFirestore fireStore = FirebaseFirestore.instance;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    print('BackupSecretKey');
    auth = Auth();
    setInit();
  }
  void setInit() async {
    print('setInit');
    final storage = new FlutterSecureStorage();
    String? secret = await storage.read(key: 'secretpass');

    setState(() {
      _secret.text = secret!;
    });
  }

  Future<bool> _onBackPressed() {
    AppRoutes.makeFirst(context, HomeLikewallet());
    return Future.value();
  }

  @override
  Widget build(BuildContext context) {
    return new WillPopScope(
        onWillPop: _onBackPressed,
        child: Scaffold(
//      backgroundColor: Color(0xff141322),
            body: ModalProgressHUD(
          opacity: 0.1,
          progressIndicator: Success(context),
          inAsyncCall: _saving,
          child: Stack(
            alignment: Alignment.center,
            children: <Widget>[
              new Container(
                decoration: BoxDecoration(
                    gradient: LinearGradient(
                  begin: Alignment.topRight,
                  end: Alignment.bottomLeft,
                  stops: [0.1, 0.3, 0.4, 0.6, 0.7],
                  colors: [
                    // Colors are easy thanks to Flutter's Colors class.
                    Color(0xff141322),
                    Color(0xff141322).withOpacity(0.95),
                    Color(0xff141322).withOpacity(0.9),
                    Color(0xff141322).withOpacity(0.88),
                    Color(0xff141322).withOpacity(0.85)
                  ],
                )),
              ),
              Positioned(
                  top: MediaQuery.of(context).size.height *
                      Screen_util("height", 139.33),
                  child: GestureDetector(
                    child: new Container(
                      alignment: Alignment.centerLeft,
                      width: MediaQuery.of(context).size.width,
                      child: new IconButton(
                        icon: new Icon(
                          Icons.arrow_back_ios,
                          size: MediaQuery.of(context).size.height *
                              Screen_util("height", 44.47),
                        ),
                        color: Color(0xff707071),
                        onPressed: () =>
                            {AppRoutes.makeFirst(context, HomeLikewallet())},
                      ),
                    ),
                  )),
              Positioned(
                top: MediaQuery.of(context).size.height *
                    Screen_util("height", 293),
                child: new Container(
                    alignment: Alignment.center,
                    width: MediaQuery.of(context).size.width * 0.9,
                    child: Text(
                      AppLocalizations.of(context)!.translate('backupkey_title'),
                      style: TextStyle(
                        color: Color(0xff707071),
                        fontSize: MediaQuery.of(context).size.height *
                            Screen_util("height", 89),
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                      ),
                    )),
              ),
              Positioned(
                top: MediaQuery.of(context).size.height *
                    Screen_util("height", 458),
                child: new Container(
                  alignment: Alignment.centerLeft,
                  width: MediaQuery.of(context).size.width *
                      Screen_util('width', 927),
                  child: Container(
                    alignment: Alignment.center,
                    height: MediaQuery.of(context).size.height *
                        Screen_util('height', 158),
                    width: MediaQuery.of(context).size.width *
                        Screen_util('width', 927),
                    decoration: BoxDecoration(
                      color: Color(0xff141322),
                      border: Border.all(
                        color: Color(0xff0FE8D8),
                        width: MediaQuery.of(context).size.width *
                            Screen_util("height", 0.3),
                      ),
                      borderRadius: BorderRadius.all(Radius.circular(5.0)),
                    ),
                    child: Row(
                      children: <Widget>[
                        new Container(
                          alignment: Alignment.center,
                          height: MediaQuery.of(context).size.height *
                              Screen_util('height', 158),
                          width: MediaQuery.of(context).size.width *
                              Screen_util('width', 900),
//                color: Colors.blue,
                          child: TextFormField(
                            controller: _secret,
                            textAlign: TextAlign.left,
//                        initialValue: '...loading',
                            style: TextStyle(
                                color: Colors.white,
                                fontSize: MediaQuery.of(context).size.height *
                                    Screen_util('height', 48)),
                            obscureText: keyVisible,
                            decoration: InputDecoration(
                              suffixIcon: IconButton(
                                color: Colors.white,
                                icon: Icon(keyVisible
                                    ? Icons.visibility
                                    : Icons.visibility_off),
                                onPressed: () {
//                              print(_keyController);
                                  setState(() {
                                    keyVisible = !keyVisible;
                                  });
                                },
                              ),

                              contentPadding:
                                  EdgeInsets.only(top: 15, left: 10),
//                          hintText: AppLocalizations.of(context)
//                              .translate('backupkey_inpu'),
                              hintStyle: TextStyle(
                                fontSize: MediaQuery.of(context).size.height *
                                    Screen_util('height', 48),
                                color: Color(0xff707071),
                                fontFamily: AppLocalizations.of(context)!
                            .translate('font1'),
                                fontWeight: FontWeight.normal,
                              ),
                              border: InputBorder.none,
                            ),
                            keyboardType: TextInputType.text,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Positioned(
                top: MediaQuery.of(context).size.height *
                    Screen_util("height", 776),
                child: ButtonTheme(
                  minWidth: MediaQuery.of(context).size.width *
                      Screen_util("width", 470.34),
                  height: MediaQuery.of(context).size.height *
                      Screen_util("height", 132),
                  child: TextButton(
                    onPressed: () {
                      _submit();
                    },
                    style: ButtonStyle(
                      shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                        RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(5.0),
                        ),
                      ),
                      backgroundColor: MaterialStateProperty.resolveWith<Color>(
                            (Set<MaterialState> states) {
                          if (states.contains(MaterialState.disabled)) {
                            return Color(0xff0FE8D8); // Disabled color
                          }
                          return Color(0xff0FE8D8); // Regular color
                        },
                      ),
                    ),
                    child: Text(
                      AppLocalizations.of(context)!.translate('backupkey_button'),
                      style: TextStyle(
                        color: Color(0xff000000),
                        fontSize: MediaQuery.of(context).size.height * Screen_util("height", 39),
                        fontFamily: AppLocalizations.of(context)!.translate('font1'),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        )));
  }

  Success(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: <Widget>[
        Positioned(
            top:
                MediaQuery.of(context).size.height * Screen_util("height", 458),
            child: Container(
                height: MediaQuery.of(context).size.height *
                    Screen_util("height", 458),
                width: MediaQuery.of(context).size.height *
                    Screen_util("width", 458),
                child: FlareActor(
                  "assets/animation/success.flr",
                  alignment: Alignment.center,
                  fit: BoxFit.contain,
                  animation: "success1",
                )))
      ],
    );
  }

  void _submit() {
    setState(() {
      _saving = true;
    });

    auth.getCurrentUser().then((decodeToken) {
      fireStore
          .collection('logs')
          .doc('backupKey')
          .collection('whobackup')
          .add({
        'uid': decodeToken!.uid,
        'datetime': DateTime.now(),
        'click': 'backupKey'
      }).then((success) {
        print('submitting to backend...');
        new Future.delayed(new Duration(milliseconds: 500), () {
          Navigator.push(
              context,
              EnterExitRoute(
                  exitPage: BackupSecretKey(), enterPage: CompleteBackup()));
          setState(() {
            _saving = false;
          });
        });
      });
    });
    //Simulate a service call
  }
}

class CompleteBackup extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return _CompleteBackup();
  }
}

class _CompleteBackup extends State<CompleteBackup> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFFFFFFF),
      body: Align(
        alignment: Alignment.center,
        child: Column(
          children: <Widget>[
            SizedBox(
              height: MediaQuery.of(context).size.height * 0.15,
            ),
            new Container(
              alignment: Alignment.center,
              height: MediaQuery.of(context).size.height * 0.1,
              width: MediaQuery.of(context).size.width * 0.15,
              child: Image.asset(
                'assets/image/complete.png',
                scale: 1,
              ),
            ),
            new Container(
              alignment: Alignment.center,
              height: MediaQuery.of(context).size.height * 0.05,
              width: MediaQuery.of(context).size.width * 0.8,
              child: new Text(
                AppLocalizations.of(context)!.translate('backupkey_line1'),
                style: TextStyle(
                    color: Color(0xff000000).withOpacity(1),
                    fontFamily: AppLocalizations.of(context)!.translate('font'),
                    fontWeight: FontWeight.normal,
                    fontSize: 15.0),
              ),
            ),
            new Container(
              alignment: Alignment.center,
              height: MediaQuery.of(context).size.height * 0.2,
              width: MediaQuery.of(context).size.width * 0.8,
              child: new Text(
                AppLocalizations.of(context)!.translate('backupkey_line2'),
                style: TextStyle(
                    color: Color(0xff000000).withOpacity(1),
                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
                    fontWeight: FontWeight.normal,
                    fontSize: 17.0),
              ),
            ),
            new Container(
              alignment: Alignment.center,
              height: MediaQuery.of(context).size.height * 0.07,
              width: MediaQuery.of(context).size.width * 0.9,
              child: ButtonTheme(
                minWidth: MediaQuery.of(context).size.width * 0.95,
                height: MediaQuery.of(context).size.height * 0.07,
                child: TextButton(
                  style: ButtonStyle(
                    shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                      RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                    ),
                    backgroundColor: MaterialStateProperty.resolveWith<Color>(
                          (Set<MaterialState> states) {
                        if (states.contains(MaterialState.disabled)) {
                          return Color(0xff141322).withOpacity(0.5); // Disabled color
                        }
                        return Color(0xff141322); // Regular color
                      },
                    ),
                  ),
                  onPressed: () {
                    AppRoutes.makeFirst(context, HomeLikewallet());
                  },
                  child: Text(
                    AppLocalizations.of(context)!.translate('backupkey_button_back'),
                    style: TextStyle(
                      color: Color(0xffB4E60D),
                      fontFamily: 'Proxima Nova',
                      fontSize: 18,
                      fontWeight: FontWeight.normal,
                    ),
                  ),
                )
              ),
            ),
          ],
        ),
      ),
    );
  }
}
