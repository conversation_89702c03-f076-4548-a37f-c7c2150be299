import 'package:flutter/material.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/routes.dart';
import 'package:likewallet/screen/NavigationBar.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:likewallet/screen/home.dart';

import 'package:likewallet/screen_util.dart';
import 'package:likewallet/libraryman/app_local.dart';

class Currency extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return _Currency();
  }
}

class _Currency extends State<Currency> {
  late SharedPreferences sharedPreferences;
  bool _USD = false;
  bool _THB = false;
  bool _LAK = false;
  bool _VND = false;
  bool _GOLD = false;
  bool _LIKE = false;

  @override
  void initState() {
    super.initState();
    chackCurrency();
  }

  void setMainCurrency(String currency) async {
    sharedPreferences = await SharedPreferences.getInstance();
    sharedPreferences.setString('currency', currency);
    Future.delayed(Duration(milliseconds: 0), () {
      AppRoutes.makeFirst(context, HomeLikewallet());
    });
  }

  @override
  Widget build(BuildContext context) {
    final width930 = mediaQuery(context, "height", 930);
    return Material(
        child: Scaffold(
      // bottomNavigationBar: NavigationBar(),
      backgroundColor: LikeWalletAppTheme.bule2,
      body: Stack(
        alignment: Alignment.center,
        children: <Widget>[
          Positioned(
            top: mediaQuery(context, "height", 136),
            left: 0,
            child: backButton(context, LikeWalletAppTheme.gray),
          ),
          Positioned(
            top: mediaQuery(context, "height", 319),
            child: _titleCurrency(width930),
          ),
          Positioned(
            top: mediaQuery(context, "height", 489),
            child: _selectBody(width930),
          ),
        ],
      ),
    ));
  }

  Widget _titleCurrency(width930) {
    return new Container(
      alignment: Alignment.centerLeft,
      width: width930,
      child: Text(
        AppLocalizations.of(context)!.translate('currency_title'),
        style: TextStyle(
          fontFamily: AppLocalizations.of(context)!.translate('font1'),
          fontWeight: FontWeight.w100,
          fontSize: mediaQuery(context, 'height', 89),
          color: LikeWalletAppTheme.gray,
        ),
      ),
    );
  }

  Widget _selectBody(width930) {
    return Column(
      children: <Widget>[
        _usd(width930),
        _thb(width930),
        _lak(width930),
        _vnd(width930),
        _gold(width930),
        _like(width930)
      ],
    );
  }

  Widget _usd(width930) {
    return Column(children: <Widget>[
      new Container(
          alignment: Alignment.centerLeft,
          width: width930,
          child: TextButton(
            onPressed: () {
              setState(() {
                _USD = true;
                print(_USD);
                if (_USD == true) {
                  _THB = false;
                  _LAK = false;
                  _VND = false;
                  _GOLD = false;
                  _LIKE = false;
                  _onPressed(_USD, _THB, _LAK, _VND, _GOLD, _LIKE);
                }
              });
              setMainCurrency('USD');
            },
            child: Row(
              children: <Widget>[
                Text(
                  AppLocalizations.of(context)!.translate('currency_us'),
                  style: LikeWalletAppTheme.currencyStyle(context),
                ),
                if (_USD == true)
                  Expanded(
                    child: Align(
                      alignment: Alignment.centerRight,
                      child: Icon(
                        Icons.check,
                        size: mediaQuery(context, "height", 45),
                        color: LikeWalletAppTheme.gray,
                      ),
                    ),
                  ),
              ],
            ),
          ),
      ),
      border(width930),
    ]);
  }

  Widget _thb(width930) {
    return Column(children: <Widget>[
      new Container(
          alignment: Alignment.centerLeft,
          width: width930,
          child: TextButton(
            onPressed: () {
              setState(() {
                _THB = true;
                print(_USD);
                if (_THB == true) {
                  _USD = false;
                  _LAK = false;
                  _VND = false;
                  _GOLD = false;
                  _LIKE = false;
                  _onPressed(_USD, _THB, _LAK, _VND, _GOLD, _LIKE);
                }
              });
              setMainCurrency('THB');
            },
            style: ButtonStyle(
              padding: MaterialStateProperty.all<EdgeInsetsGeometry>(EdgeInsets.all(0)),
            ),
            child: Row(
              children: <Widget>[
                Text(
                  AppLocalizations.of(context)!.translate('currency_thai'),
                  style: LikeWalletAppTheme.currencyStyle(context),
                ),
                if (_THB == true)
                  Expanded(
                    child: Align(
                      alignment: Alignment.centerRight,
                      child: Icon(
                        Icons.check,
                        size: mediaQuery(context, "height", 45),
                        color: LikeWalletAppTheme.gray,
                      ),
                    ),
                  )
              ],
            ),
          )
      ),
      border(width930),
    ]);
  }

  Widget _lak(width930) {
    return Column(children: <Widget>[
      new Container(
          alignment: Alignment.centerLeft,
          width: width930,
          child: TextButton(
            onPressed: () {
              setState(() {
                _LAK = true;
                print(_LAK);
                if (_LAK == true) {
                  _USD = false;
                  _THB = false;
                  _VND = false;
                  _GOLD = false;
                  _LIKE = false;
                  _onPressed(_USD, _THB, _LAK, _VND, _GOLD, _LIKE);
                }
              });
              setMainCurrency('LAK');
            },
            style: ButtonStyle(
              padding: MaterialStateProperty.all<EdgeInsetsGeometry>(EdgeInsets.zero),
            ),
            child: Row(
              children: <Widget>[
                Text(
                  AppLocalizations.of(context)!.translate('currency_lao'),
                  style: LikeWalletAppTheme.currencyStyle(context),
                ),
                if (_LAK == true)
                  Expanded(
                    child: Align(
                      alignment: Alignment.centerRight,
                      child: Icon(
                        Icons.check,
                        size: mediaQuery(context, "height", 45),
                        color: LikeWalletAppTheme.gray,
                      ),
                    ),
                  ),
              ],
            ),
          )
      ),
      border(width930),
    ]);
  }

  Widget _vnd(width930) {
    return Column(children: <Widget>[
      new Container(
          alignment: Alignment.centerLeft,
          width: width930,
          child: TextButton(
            onPressed: () {
              setState(() {
                _VND = true;
                print(_VND);
                if (_VND == true) {
                  _USD = false;
                  _THB = false;
                  _LAK = false;
                  _GOLD = false;
                  _LIKE = false;
                  _onPressed(_USD, _THB, _LAK, _VND, _GOLD, _LIKE);
                }
              });
              setMainCurrency('VND');
            },
            style: ButtonStyle(
              padding: MaterialStateProperty.all<EdgeInsetsGeometry>(EdgeInsets.all(0)),
              foregroundColor: MaterialStateProperty.all<Color>(
                _VND ? LikeWalletAppTheme.gray : Colors.black,
              ),
            ),
            child: Row(
              children: <Widget>[
                Text(
                  AppLocalizations.of(context)!.translate('currency_vnd'),
                  style: LikeWalletAppTheme.currencyStyle(context),
                ),
                if (_VND)
                  Expanded(
                    child: Align(
                      alignment: Alignment.centerRight,
                      child: Icon(
                        Icons.check,
                        size: mediaQuery(context, "height", 45),
                        color: LikeWalletAppTheme.gray,
                      ),
                    ),
                  ),
              ],
            ),
          ),
      ),
      border(width930),
    ]);
  }

  Widget _gold(width930) {
    return Column(children: <Widget>[
      new Container(
          alignment: Alignment.centerLeft,
          width: width930,
          child: TextButton(
            onPressed: () {
              setState(() {
                _GOLD = true;
                print(_GOLD);
                if (_GOLD == true) {
                  _USD = false;
                  _THB = false;
                  _LAK = false;
                  _VND = false;
                  _LIKE = false;
                  _onPressed(_USD, _THB, _LAK, _VND, _GOLD, _LIKE);
                }
              });

              setMainCurrency('GOLD');
            },
            style: ButtonStyle(
              padding: MaterialStateProperty.all<EdgeInsetsGeometry>(EdgeInsets.all(0)),
            ),
            child: Row(
              children: <Widget>[
                Text(
                  AppLocalizations.of(context)!.translate('currency_gold'),
                  style: LikeWalletAppTheme.currencyStyle(context),
                ),
                if (_GOLD == true)
                  Expanded(
                    child: Align(
                      alignment: Alignment.centerRight,
                      child: Icon(
                        Icons.check,
                        size: mediaQuery(context, "height", 45),
                        color: LikeWalletAppTheme.gray,
                      ),
                    ),
                  )
              ],
            ),
          )
      ),
      border(width930),
    ]);
  }

  Widget _like(width930) {
    return Column(children: <Widget>[
      new Container(
          alignment: Alignment.centerLeft,
          width: width930,
          child: TextButton(
            onPressed: () {
              setState(() {
                _LIKE = true;
                print(_GOLD);
                if (_LIKE == true) {
                  _USD = false;
                  _THB = false;
                  _LAK = false;
                  _VND = false;
                  _GOLD = false;
                  _onPressed(_USD, _THB, _LAK, _VND, _GOLD, _LIKE);
                }
              });
              setMainCurrency('LIKE');
            },
            child: Row(
              children: <Widget>[
                Text(
                  AppLocalizations.of(context)!.translate('currency_like'),
                  style: LikeWalletAppTheme.currencyStyle(context),
                ),
                if (_LIKE == true)
                  Expanded(
                    child: Align(
                      alignment: Alignment.centerRight,
                      child: Icon(
                        Icons.check,
                        size: mediaQuery(context, "height", 45),
                        color: LikeWalletAppTheme.gray,
                      ),
                    ),
                  )
              ],
            ),
          )
      ),
      border(width930),
    ]);
  }

  Widget border(width930) {
    return Container(
        margin: EdgeInsets.only(
          top: mediaQuery(context, 'hieght', 0),
          bottom: mediaQuery(context, 'hieght', 0),
        ),
        alignment: Alignment.centerLeft,
        width: width930,
        child: new Container(
          //                  color: Colors.blue,
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                //                   <--- left side
                color: Color(0xff707071),
                width: mediaQuery(context, "width", 1),
              ),
            ),
          ),
        ));
  }

  _onPressed(bool value1, bool value2, bool value3, bool value4, bool value5,
      bool value6) async {
    sharedPreferences = await SharedPreferences.getInstance();
    setState(() {
      print(_USD);
      print(_THB);
      print(_LAK);
      print(_VND);
      print(_GOLD);
      sharedPreferences.setBool("LIKE", _LIKE);
      sharedPreferences.setBool("USD", _USD);
      sharedPreferences.setBool("THB", _THB);
      sharedPreferences.setBool("LAK", _LAK);
      sharedPreferences.setBool("VND", _VND);
      sharedPreferences.setBool("GOLD", _GOLD);
      // sharedPreferences!.commit();
    });
  }

  chackCurrency() async {
    sharedPreferences = await SharedPreferences.getInstance();
    setState(() {
      _USD = sharedPreferences.getBool("USD") ?? false;
      _THB = sharedPreferences.getBool("THB") ?? false;
      _LAK = sharedPreferences.getBool("LAK") ?? false;
      _VND = sharedPreferences.getBool("VND") ?? false;
      _GOLD = sharedPreferences.getBool("GOLD") ?? false;
      _LIKE = sharedPreferences.getBool("LIKE") ?? false;
    });
  }
}
