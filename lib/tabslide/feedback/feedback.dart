import 'dart:convert';
import 'dart:io';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:http/http.dart' as http;
import 'package:likewallet/device_utils.dart';
import 'package:likewallet/libraryman/configuration_service.dart';
import 'package:likewallet/middleware/api_http.dart';
import 'package:likewallet/tabslide/feedback/alert_feedback_%20success.dart';
import 'package:path/path.dart' as path;
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:likewallet/screen/home.dart';
import 'package:likewallet/app_config.dart';
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/tabslide/feedback/feedback_lang.dart';
import 'package:likewallet/tabslide/feedback/flutter_rating.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:likewallet/libraryman/applang.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class FeedBack extends StatefulWidget {
  State<StatefulWidget> createState() => new _FeedBack();
}

class _FeedBack extends State<FeedBack> with TickerProviderStateMixin {
  double rating = 0;
  var copyStatus = 0;
  int starCount = 5;
  int selected = 0;
  String Issues = "";
  String image = "";
  bool _loading = false;
  final ImagePicker _picker = ImagePicker();
  late BaseAuth auth;
  final fireStore = FirebaseFirestore.instance;
  final FirebaseAuth firebaseAuth = FirebaseAuth.instance;
  List data = [];
  TextEditingController _comment = TextEditingController();
  late SharedPreferences sharedPreferences;
  late IConfigurationService configETH;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    auth = new Auth();
    getListChoice();
  }

  getListChoice() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    configETH = new ConfigurationService(pref);
    dataChoice(pref).then((value) {
      setState(() {
        data = value;
        print(value);
      });
    });
  }

  Future<int> checkRound() async {
    String _year = new DateTime.now().year.toString();
    String _month = new DateTime.now().month.toString();
    String _day = new DateTime.now().day.toString();
    String _date = _day + '-' + _month + '-' + _year;
    print(_date);
    var dataUserRound;
    try {
      dataUserRound = await FirebaseFirestore.instance
          .collection('log')
          .doc('feedbackrewards')
          .collection(_date)
          .where("address", isEqualTo: configETH.getAddress().toLowerCase())
          .get();
      print(dataUserRound.size);

      return dataUserRound.size;
    } catch (e) {
      return 10;
    }
  }

  Future sendFeedBack() async {
    try {
      await auth.getCurrentUser().then((snapshot) async {
        print(snapshot!.uid);
        var loopToday = await checkRound();
        print('loop today' + loopToday.toString());

        if (loopToday > 1) {
          await FirebaseFirestore.instance
              .collection('feedbackComment')
              .doc(DateTime.now().millisecondsSinceEpoch.toString())
              .set({
            "uid": snapshot.uid,
            "phone": snapshot.phoneNumber,
            "rating": rating,
            "IssuesID": selected,
            "Issues": Issues,
            "comment": _comment.text.toString(),
            "image": image,
            "address": configETH.getAddress().toLowerCase(),
            "pay": 4,
            "date": new DateTime.now(),
            "unixTimeStamp": DateTime.now().millisecondsSinceEpoch.toString()
          });
        } else {
          await FirebaseFirestore.instance
              .collection('feedbackComment')
              .doc(DateTime.now().millisecondsSinceEpoch.toString())
              .set({
            "uid": snapshot.uid,
            "phone": snapshot.phoneNumber,
            "rating": rating,
            "IssuesID": selected,
            "Issues": Issues,
            "comment": _comment.text.toString(),
            "image": image,
            "address": configETH.getAddress().toLowerCase(),
            "pay": 0,
            "date": new DateTime.now(),
            "unixTimeStamp": DateTime.now().millisecondsSinceEpoch.toString()
          });
        }
        var room = '-1001421698064';
        List<dynamic> body = [
          {
            "emp": room.toString(),
            "fromdata": {
              "message": "=================================\nมี Feedback เข้ามาใหม่\nจาก UID : ${snapshot.uid}  \nเบอร์โทร : ${snapshot.phoneNumber}\nรายละเอียด : ${_comment.text.toString()}",
            }
          }
        ];
        AppHttps.postTG_PKG(AppHttps.sendTelegramPKG, body);
      });
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: ModalProgressHUD(
        inAsyncCall: _loading,
        opacity: 0.3,
        progressIndicator: CustomLoading(),
        child: SingleChildScrollView(
          child: GestureDetector(
            onTap: () {
              DeviceUtils.hideKeyboard(context);
            },
            child: Stack(
              children: [
                _body(),
                _head(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _head() {
    return Container(
      width: MediaQuery.of(context).size.width,
      height: mediaQuery(context, 'height', 486),
      decoration: BoxDecoration(
        color: const Color(0xf21d2b44),
      ),
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.only(top: 139.h, left: 0),
            child: backButton(context, LikeWalletAppTheme.gray),
          ),
          Text(
            AppLocalizations.of(context)!.translate('feedback_head'),
            style: TextStyle(
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              fontSize: mediaQuery(context, 'height', 51),
              color: LikeWalletAppTheme.bule1,
              letterSpacing: 0.5,
            ),
            textAlign: TextAlign.right,
          ),
          Expanded(child: Container()),
          new Padding(
            padding: EdgeInsets.only(
              bottom: mediaQuery(context, 'height', 57.8),
            ),
            child: new StarRating(
              size: mediaQuery(context, 'height', 66.94),
              rating: rating,
              color: LikeWalletAppTheme.white,
              borderColor: LikeWalletAppTheme.bule1,
              starCount: starCount,
              onRatingChanged: (rating) => setState(
                () {
                  this.rating = rating;
                  print(rating);
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _body() {
    return Container(
      padding: EdgeInsets.only(
        top: mediaQuery(context, 'height', 486),
      ),
      height: mediaQuery(context, 'height', 2340),
      width: mediaQuery(context, 'width', 1080),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment(0.0, -mediaQuery(context, 'height', 1.0)),
          end: Alignment(mediaQuery(context, 'width', 0.04),
              mediaQuery(context, 'height', 1.0)),
          colors: [
            const Color(0xff2be8d8),
            const Color(0xff2be8d8),
            const Color(0xff24cde4)
          ],
          stops: [0.0, 0.493, 1.0],
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          title1(),
          SizedBox(
            height: mediaQuery(context, 'height', 69),
          ),
          _buildChoice(),
          SizedBox(
            height: mediaQuery(context, 'height', 10),
          ),
          title2(),
          SizedBox(
            height: mediaQuery(context, 'height', 16),
          ),
          _textFieldComment(),
          SizedBox(
            height: mediaQuery(context, 'height', 22.5),
          ),
          _textFieldImage(),
          SizedBox(
            height: mediaQuery(context, 'height', 106.7),
          ),
          _buttonSubmit(),
          Expanded(child: Container()),
        ],
      ),
    );
  }

  Widget title1() {
    return Container(
      width: mediaQuery(context, 'width', 884),
      margin: EdgeInsets.only(top: mediaQuery(context, 'height', 85.5)),
      child: Text(
        AppLocalizations.of(context)!.translate('feedback_title1'),
        style: TextStyle(
          fontFamily: AppLocalizations.of(context)!.translate('font1'),
          fontSize: mediaQuery(context, 'height', 41),
          color: LikeWalletAppTheme.bule2_8,
          letterSpacing: 0.3,
        ),
        textAlign: TextAlign.left,
      ),
    );
  }

  Widget title2() {
    return Container(
      width: mediaQuery(context, 'width', 884),
      child: Text(
        AppLocalizations.of(context)!.translate('feedback_title2'),
        style: TextStyle(
          fontFamily: AppLocalizations.of(context)!.translate('font1'),
          fontSize: mediaQuery(context, 'height', 41),
          color: LikeWalletAppTheme.bule2_8,
          letterSpacing: 0.3,
        ),
        textAlign: TextAlign.left,
      ),
    );
  }

  Widget _buildChoice() {
    return MediaQuery.removePadding(
      context: context,
      removeTop: true,
      child: ListView.builder(
        itemCount: data.length,
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        itemBuilder: (BuildContext context, int index) {
          return _listData(data[index]);
        },
      ),
    );
  }

  Widget _listData(index) {
    return Container(
      margin: EdgeInsets.symmetric(
        vertical: mediaQuery(context, 'height', 20.5),
      ),
      padding: EdgeInsets.only(
        left: mediaQuery(context, 'width', 151),
      ),
      child:
          Row(crossAxisAlignment: CrossAxisAlignment.center, children: <Widget>[
        InkWell(
          child: Container(
            height: mediaQuery(context, 'height', 50),
            width: mediaQuery(context, 'height', 50),
            decoration: BoxDecoration(
              border: Border.all(
                color: Colors.white,
              ),
              color:
                  selected == index['id'] ? Colors.white : Colors.transparent,
              shape: BoxShape.circle,
            ),
            alignment: Alignment.topLeft,
          ),
          onTap: () {
            setState(() {
              selected = index['id'];
              Issues = index['detail'];
              print('${index['id']}' + '${index['detail']}');
            });
          },
        ),
        SizedBox(width: mediaQuery(context, 'width', 39)),
        InkWell(
          onTap: () {
            setState(() {
              selected = index['id'];
              Issues = index['detail'];
              print(index['id']);
            });
          },
          child: Container(
            alignment: Alignment.centerLeft,
            child: Text(
              index['detail'],
              style: TextStyle(
                fontSize: mediaQuery(context, 'height', 41),
                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                letterSpacing: 0.3,
                fontWeight: FontWeight.normal,
                fontStyle: FontStyle.normal,
              ),
            ),
          ),
        ),
      ]),
    );
  }

  Widget _textFieldComment() {
    return Container(
      width: mediaQuery(context, 'width', 930),
      height: mediaQuery(context, 'height', 364.77),
      decoration: BoxDecoration(
        color: LikeWalletAppTheme.white.withOpacity(1),
        border: Border.all(
          color: LikeWalletAppTheme.bule1,
          width: mediaQuery(context, 'height', 0.3),
        ),
        gradient: LinearGradient(
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
            colors: [
              LikeWalletAppTheme.white.withOpacity(0.3),
              LikeWalletAppTheme.white.withOpacity(0.2),
            ]),
        boxShadow: [
          new BoxShadow(
              color: Colors.black.withOpacity(0.06),
              offset: new Offset(
                0,
                mediaQuery(context, 'height', 6),
              ),
              blurRadius: mediaQuery(context, 'height', 24),
              spreadRadius: 1.0),
        ],
        borderRadius: BorderRadius.all(Radius.circular(15.0)),
      ),
      child: TextFormField(
        controller: _comment,
        maxLines: 7,
        style: TextStyle(
          fontSize: mediaQuery(context, 'height', 41),
          color: LikeWalletAppTheme.white,
          letterSpacing: 0.3,
          fontFamily: AppLocalizations.of(context)!.translate('font1'),
          fontWeight: FontWeight.normal,
        ),
        decoration: InputDecoration(
          isDense: true,
          contentPadding: EdgeInsets.only(
            top: mediaQuery(context, 'height', 36),
            left: mediaQuery(context, 'width', 55),
            right: mediaQuery(context, 'width', 55),
            bottom: mediaQuery(context, 'height', 36),
          ),
          hintText: AppLocalizations.of(context)!.translate('feedback_comment'),
          hintStyle: TextStyle(
            fontSize: mediaQuery(context, 'height', 41),
            letterSpacing: 0.3,
            color: LikeWalletAppTheme.bule2_8.withOpacity(0.4),
            fontFamily: AppLocalizations.of(context)!.translate('font1'),
            fontWeight: FontWeight.normal,
          ),
          border: InputBorder.none,
        ),
        keyboardType: TextInputType.text,
      ),
    );
  }

  Widget _textFieldImage() {
    return InkWell(
      onTap: () async {
        getImage().then((File _imageFile) async {
          if (_imageFile.path.isNotEmpty) {
            upload(_imageFile).then((thenUpload) {
              setState(() {
                image = thenUpload;
                _loading = false;
              });
            });
          } else {
            setState(() {
              image = '';
              _loading = false;
            });
          }
        });
      },
      child: Container(
        width: mediaQuery(context, 'width', 930),
        height: mediaQuery(context, 'height', 146.51),
        decoration: BoxDecoration(
            color: LikeWalletAppTheme.white.withOpacity(1),
            border: Border.all(
              color: LikeWalletAppTheme.bule1,
              width: mediaQuery(context, 'height', 0.3),
            ),
            borderRadius: BorderRadius.all(Radius.circular(15.0)),
            gradient: LinearGradient(
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
                colors: [
                  LikeWalletAppTheme.white.withOpacity(0.3),
                  LikeWalletAppTheme.white.withOpacity(0.2),
                ]),
            boxShadow: [
              new BoxShadow(
                  color: Colors.black.withOpacity(0.06),
                  offset: new Offset(
                    0,
                    mediaQuery(context, 'height', 6),
                  ),
                  blurRadius: mediaQuery(context, 'height', 24),
                  spreadRadius: 1.0),
            ]),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            Container(
              padding: EdgeInsets.only(
                left: mediaQuery(context, 'width', 55),
              ),
              child: Text(
                image.isNotEmpty
                    ? AppLocalizations.of(context)!
                        .translate('feedback_uploaded')
                    : AppLocalizations.of(context)!
                        .translate('feedback_upload'),
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  fontSize: mediaQuery(context, 'height', 41),
                  color: image.isNotEmpty
                      ? LikeWalletAppTheme.white
                      : LikeWalletAppTheme.gray,
                  letterSpacing: 0.3,
                ),
                textAlign: TextAlign.left,
              ),
            ),
            Expanded(
              child: Container(),
            ),
            Padding(
              padding: EdgeInsets.only(
                right: mediaQuery(context, 'width', 58.2),
              ),
              child: SvgPicture.string(
                '<svg viewBox="899.7 1866.5 47.1 43.0" ><path transform="translate(637.97, -690.16)" d="M 301.4034423828125 2556.64990234375 L 269.2030029296875 2556.64990234375 C 265.1004638671875 2556.655029296875 261.776611328125 2559.924072265625 261.7720336914063 2563.958984375 L 261.7720336914063 2592.385498046875 C 261.776611328125 2596.4208984375 265.1004638671875 2599.690673828125 269.2030029296875 2599.695556640625 L 301.4034423828125 2599.695556640625 C 305.5051879882813 2599.690673828125 308.8291015625 2596.4208984375 308.8336791992188 2592.385498046875 L 308.8336791992188 2563.958984375 C 308.8291015625 2559.924072265625 305.5051879882813 2556.655029296875 301.4034423828125 2556.64990234375 Z M 269.8263549804688 2559.825927734375 L 300.779296875 2559.825927734375 C 303.4080200195313 2559.828857421875 305.5380859375 2561.924072265625 305.5411376953125 2564.510009765625 L 305.5143432617188 2587.493896484375 L 294.1276550292969 2576.995849609375 L 286.5781860351563 2583.080810546875 L 273.2909851074219 2569.9599609375 L 265.0638122558594 2576.539794921875 L 265.0638122558594 2564.513671875 C 265.0645751953125 2561.92626953125 267.1960754394531 2559.828857421875 269.8263549804688 2559.825927734375 Z M 300.779296875 2596.51953125 L 269.8263549804688 2596.51953125 C 267.1976318359375 2596.516845703125 265.0675659179688 2594.42138671875 265.0645751953125 2591.83544921875 L 265.0645751953125 2580.802978515625 L 273.0324096679688 2574.429931640625 L 286.4030151367188 2587.4697265625 L 294.0482177734375 2581.382568359375 L 305.5319213867188 2591.959716796875 C 305.4616088867188 2594.494384765625 303.3567504882813 2596.51416015625 300.779296875 2596.51953125 Z" fill="#ffffff" stroke="none" stroke-width="1" stroke-miterlimit="10" stroke-linecap="butt" /></svg>',
                allowDrawingOutsideViewBox: true,
                width: mediaQuery(context, 'width', 47.06),
                height: mediaQuery(context, 'height', 43.05),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<File> getImage() async {
    var image = await _picker.pickImage(source: ImageSource.gallery);
    if (image == null) {
      print('ยังไม่ได่เลือกรูป');
      return Future.value();
    } else {
      return File(image.path);
    }
  }

  Future upload(File file) async {
    setState(() {
      _loading = true;
    });
    try {
      if (file == null) return ['false'];
      String base64Image = base64Encode(file.readAsBytesSync());
      String fileName = file.path.split("/").last;

      var url = Uri.https(env.apiCheck, '/uploadBackFromBase');
      final response = await http.post(url, body: {
        "image": base64Image,
        "name": fileName,
      });
      print(response.statusCode);
      final body = json.decode(response.body);

      print(body["result"]);
      print(body["result"]["id"]);
      print(body["result"]["url"]);
      return body["result"]["url"];
    } catch (e) {
      setState(() {
        _loading = false;
        showShortToast('upload error');
      });
    }
  }

  void showShortToast(msg) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_SHORT,
        backgroundColor: Colors.red,
        textColor: Colors.white);
  }

  Widget _buttonSubmit() {
    return Padding(
      padding: EdgeInsets.only(bottom: mediaQuery(context, 'height', 40)),
      child: new InkWell(
        onTap: () async {
          if (rating > 0) {
            if (copyStatus == 0) {
              setState(() {
                copyStatus = 1;
                Future.delayed(Duration(milliseconds: 2000), () async {
                  await sendFeedBack().then((value) async {
                    if (value) {
                      setState(() {
                        copyStatus = 2;
                      });
                      Future.delayed(Duration(milliseconds: 500), () async {
                        await showDialog(
                            context: context,
                            barrierColor: Colors.transparent,
                            builder: (BuildContext context) =>
                                alertFeedBack(context));
                      });
                    }
                  });
                });
              });
            }
          } else {
            showShortToast(AppLocalizations.of(context)!
                .translate('feedback_button_alert_rating'
                    ''));
          }
        },
        child: AnimatedContainer(
          width: copyStatus == 1
              ? mediaQuery(context, 'width', 132)
              : copyStatus == 2
                  ? mediaQuery(context, 'width', 720)
                  : mediaQuery(context, 'width', 720),
          duration: Duration(milliseconds: 300),
          height: mediaQuery(context, 'height', 132),
          alignment: FractionalOffset.center,
          decoration: new BoxDecoration(
            boxShadow: [
              new BoxShadow(
                  color: Colors.black.withOpacity(0.16),
                  offset: new Offset(
                    mediaQuery(context, 'width', 3),
                    mediaQuery(context, 'height', 6),
                  ),
                  blurRadius: mediaQuery(context, 'height', 12),
                  spreadRadius: 0.0)
            ],
            gradient: LinearGradient(
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
                colors: copyStatus == 2
                    ? [
                        Color(0xff485DFA).withOpacity(0.8),
                        Color(0xffAC8DFF).withOpacity(0.8)
                      ]
                    : [
                        LikeWalletAppTheme.bule2,
                        LikeWalletAppTheme.bule2_4.withOpacity(0.9),
                      ]),
            borderRadius: copyStatus == 1
                ? new BorderRadius.all(const Radius.circular(30.0))
                : new BorderRadius.all(const Radius.circular(10.0)),
          ),
          child: copyStatus == 1
              ? Container(
                  height: mediaQuery(context, 'height', 50),
                  width: mediaQuery(context, 'height', 50),
                  child: CustomLoading(),
                )
              : copyStatus == 2
                  ? SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            AppLocalizations.of(context)!
                                .translate('feedback_button_success'),
                            style: TextStyle(
                              color: LikeWalletAppTheme.bule1,
                              fontFamily: AppLocalizations.of(context)!
                                  .translate('font1'),
                              fontWeight: FontWeight.w200,
                              fontSize: mediaQuery(context, 'height', 50),
                            ),
                          ),
                          SizedBox(
                            width: mediaQuery(context, 'width', 22.5),
                          ),
                          Container(
                            height: mediaQuery(context, 'height', 48.86),
                            width: mediaQuery(context, 'width', 61.92),
                            child: SvgPicture.string(
                              '<svg viewBox="521.5 2135.7 61.9 48.9" ><path transform="translate(421.75, 2022.69)" d="M 121.3377151489258 161.8282623291016 L 101.6040267944336 142.0951538085938 C 99.13432312011719 139.6254730224609 99.13432312011719 135.6208038330078 101.6040267944336 133.1511077880859 C 104.07373046875 130.681396484375 108.0783996582031 130.681396484375 110.5481033325195 133.1511077880859 L 121.3861846923828 143.9897766113281 L 150.8975524902344 114.7973861694336 C 153.3804168701172 112.3408432006836 157.3851013183594 112.3635864257813 159.8410186767578 114.8458557128906 C 162.2969818115234 117.328727722168 162.2754211425781 121.3333892822266 159.7919616699219 123.7893218994141 L 121.3377151489258 161.8282623291016 Z" fill="#0fe8d8" stroke="none" stroke-width="1" stroke-miterlimit="10" stroke-linecap="butt" /></svg>',
                              allowDrawingOutsideViewBox: true,
                              fit: BoxFit.fill,
                            ),
                          ),
                        ],
                      ),
                    )
                  : new Text(
                      copyStatus == 1
                          ? ''
                          : AppLocalizations.of(context)!
                              .translate('feedback_button_submit'),
                      style: TextStyle(
                        color: LikeWalletAppTheme.bule1,
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                        fontWeight: FontWeight.w200,
                        fontSize: mediaQuery(context, 'height', 50),
                      ),
                    ),
        ),
      ),
    );
  }
}
