library flutter_rating;

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:likewallet/screen_util.dart';

typedef void RatingChangeCallback(double rating);

class StarRating extends StatelessWidget {
  final int starCount;
  final double rating;
  final RatingChangeCallback? onRatingChanged;
  final Color? color;
  final Color? borderColor;
  final double? size;

  StarRating(
      {this.starCount = 5,
      this.rating = .0,
      this.onRatingChanged,
      this.color,
      this.borderColor,
      this.size});

  Widget buildStar(BuildContext context, int index) {
    Container icon;
    double ratingStarSizeRelativeToScreen =
        MediaQuery.of(context).size.width / starCount;

    if (index >= rating) {
      icon = Container(
        padding: EdgeInsets.symmetric(
            horizontal: mediaQuery(context, 'width', 29.8 / 2)),
        child: SvgPicture.string(
          '<svg viewBox="307.0 361.3 70.2 66.9" ><defs><filter id="shadow"><feDropShadow dx="0" dy="2" stdDeviation="5"/></filter></defs><path transform="translate(-309.67, -167.97)" d="M 671.614501953125 596.1973876953125 C 671.2314453125 596.1973876953125 670.85107421875 596.1239013671875 670.4864501953125 595.96923828125 L 651.7135009765625 588.0519409179688 L 632.940673828125 595.96923828125 C 632.0015869140625 596.3654174804688 630.93115234375 596.2421875 630.1102294921875 595.6439208984375 C 629.2890625 595.0484008789063 628.837890625 594.0672607421875 628.92431640625 593.0545654296875 L 630.660888671875 572.7601318359375 L 617.33154296875 557.3532104492188 C 616.66796875 556.5846557617188 616.452880859375 555.5245971679688 616.764892578125 554.5620727539063 C 617.0797119140625 553.596435546875 617.874755859375 552.8671264648438 618.86376953125 552.636474609375 L 638.7015380859375 548.0035400390625 L 649.229248046875 530.5658569335938 C 650.2733154296875 528.8240356445313 653.13818359375 528.8265991210938 654.1873779296875 530.5632934570313 L 664.722900390625 548.0035400390625 L 684.5633544921875 552.636474609375 C 685.5523681640625 552.8671264648438 686.3472900390625 553.596435546875 686.662109375 554.5620727539063 C 686.9742431640625 555.5245971679688 686.75927734375 556.5846557617188 686.095458984375 557.3532104492188 L 672.76611328125 572.7601318359375 L 674.5029296875 593.0545654296875 C 674.5892333984375 594.0672607421875 674.1380615234375 595.0484008789063 673.3170166015625 595.6439208984375 C 672.8133544921875 596.0111694335938 672.2152099609375 596.1973876953125 671.614501953125 596.1973876953125 Z M 651.7135009765625 582.007568359375 C 652.0965576171875 582.007568359375 652.4794921875 582.083740234375 652.841552734375 582.2357788085938 L 668.319580078125 588.765380859375 L 666.8896484375 572.0335693359375 C 666.8240966796875 571.2569580078125 667.0732421875 570.4857177734375 667.5877685546875 569.8930053710938 L 678.576904296875 557.18798828125 L 662.217529296875 553.3682861328125 C 661.4566650390625 553.1924438476563 660.8009033203125 552.7150268554688 660.3968505859375 552.0460205078125 L 651.708251953125 537.66748046875 L 643.0301513671875 552.04345703125 C 642.6287841796875 552.7150268554688 641.9703369140625 553.1924438476563 641.20947265625 553.3682861328125 L 624.8502197265625 557.18798828125 L 635.8394775390625 569.8930053710938 C 636.353759765625 570.4857177734375 636.6029052734375 571.2569580078125 636.5374755859375 572.0335693359375 L 635.1075439453125 588.765380859375 L 650.5855712890625 582.2357788085938 C 650.947509765625 582.083740234375 651.33056640625 582.007568359375 651.7135009765625 582.007568359375 Z" fill="#0fe8d8" stroke="none" stroke-width="1" stroke-miterlimit="10" stroke-linecap="butt" filter="url(#shadow)"/></svg>',
          allowDrawingOutsideViewBox: true,
          color: borderColor ?? Theme.of(context).buttonColor,
          height: size ?? ratingStarSizeRelativeToScreen,
        ),
      );
    } else if (index > rating - 1 && index < rating) {
      icon = Container(
        padding: EdgeInsets.symmetric(
            horizontal: mediaQuery(context, 'width', 29.8 / 2)),
        child: SvgPicture.string(
          '<svg viewBox="307.8 361.3 70.2 66.9" ><defs><filter id="shadow"><feDropShadow dx="0" dy="2" stdDeviation="5"/></filter></defs><path transform="translate(-175.8, -168.0)" d="M 551.5634155273438 552.6693725585938 L 531.722900390625 548.0364379882813 L 521.1873779296875 530.59619140625 C 520.1380615234375 528.859619140625 517.2733154296875 528.8570556640625 516.2293090820313 530.5989379882813 L 505.7016906738281 548.0364379882813 L 485.8636779785156 552.6693725585938 C 484.8746643066406 552.9002075195313 484.0797424316406 553.6294555664063 483.7649230957031 554.5947875976563 C 483.4527282714844 555.560302734375 483.6679382324219 556.617431640625 484.3316955566406 557.38623046875 L 497.6609191894531 572.793212890625 L 495.9243469238281 593.0875854492188 C 495.8376770019531 594.1001586914063 496.2889099121094 595.081298828125 497.1100158691406 595.6795043945313 C 497.9338684082031 596.27490234375 498.9989318847656 596.3983154296875 499.9405822753906 596.0048217773438 L 518.7135620117188 588.084716796875 L 537.4863891601563 596.0048217773438 C 537.85107421875 596.156982421875 538.2313842773438 596.2329711914063 538.6143798828125 596.2329711914063 C 539.2151489257813 596.2329711914063 539.8132934570313 596.0440673828125 540.3169555664063 595.6795043945313 C 541.1380615234375 595.081298828125 541.5894165039063 594.1001586914063 541.5027465820313 593.0875854492188 L 539.7660522460938 572.793212890625 L 553.0953979492188 557.38623046875 C 553.7591552734375 556.617431640625 553.9742431640625 555.560302734375 553.6620483398438 554.5947875976563 C 553.3472290039063 553.6294555664063 552.5523071289063 552.9002075195313 551.5634155273438 552.6693725585938 Z" fill="#ffffff" stroke="none" stroke-width="1" stroke-miterlimit="10" stroke-linecap="butt" filter="url(#shadow)"/></svg>',
          allowDrawingOutsideViewBox: true,
          color: color ?? Theme.of(context).primaryColor,
          height: size ?? ratingStarSizeRelativeToScreen,
        ),
      );
    } else {
      icon = Container(
        padding: EdgeInsets.symmetric(
            horizontal: mediaQuery(context, 'width', 29.8 / 2)),
        child: SvgPicture.string(
          '<svg viewBox="307.8 361.3 70.2 66.9" ><defs><filter id="shadow"><feDropShadow dx="0" dy="2" stdDeviation="5"/></filter></defs><path transform="translate(-175.8, -168.0)" d="M 551.5634155273438 552.6693725585938 L 531.722900390625 548.0364379882813 L 521.1873779296875 530.59619140625 C 520.1380615234375 528.859619140625 517.2733154296875 528.8570556640625 516.2293090820313 530.5989379882813 L 505.7016906738281 548.0364379882813 L 485.8636779785156 552.6693725585938 C 484.8746643066406 552.9002075195313 484.0797424316406 553.6294555664063 483.7649230957031 554.5947875976563 C 483.4527282714844 555.560302734375 483.6679382324219 556.617431640625 484.3316955566406 557.38623046875 L 497.6609191894531 572.793212890625 L 495.9243469238281 593.0875854492188 C 495.8376770019531 594.1001586914063 496.2889099121094 595.081298828125 497.1100158691406 595.6795043945313 C 497.9338684082031 596.27490234375 498.9989318847656 596.3983154296875 499.9405822753906 596.0048217773438 L 518.7135620117188 588.084716796875 L 537.4863891601563 596.0048217773438 C 537.85107421875 596.156982421875 538.2313842773438 596.2329711914063 538.6143798828125 596.2329711914063 C 539.2151489257813 596.2329711914063 539.8132934570313 596.0440673828125 540.3169555664063 595.6795043945313 C 541.1380615234375 595.081298828125 541.5894165039063 594.1001586914063 541.5027465820313 593.0875854492188 L 539.7660522460938 572.793212890625 L 553.0953979492188 557.38623046875 C 553.7591552734375 556.617431640625 553.9742431640625 555.560302734375 553.6620483398438 554.5947875976563 C 553.3472290039063 553.6294555664063 552.5523071289063 552.9002075195313 551.5634155273438 552.6693725585938 Z" fill="#ffffff" stroke="none" stroke-width="1" stroke-miterlimit="10" stroke-linecap="butt" filter="url(#shadow)"/></svg>',
          allowDrawingOutsideViewBox: true,
          color: color ?? Theme.of(context).primaryColor,
          height: size ?? ratingStarSizeRelativeToScreen,
        ),
      );
    }
    return new InkResponse(
      highlightColor: Colors.transparent,
      radius: (size ?? ratingStarSizeRelativeToScreen) / 2,
      onTap:
          onRatingChanged == null ? null : () => onRatingChanged!(index + 1.0),
      child: new Container(
        height: (size ?? ratingStarSizeRelativeToScreen) * 1.5,
        child: icon,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return new Material(
      type: MaterialType.transparency,
      child: new Center(
        child: new Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: new List.generate(
            starCount,
            (index) => buildStar(context, index),
          ),
        ),
      ),
    );
  }
}
