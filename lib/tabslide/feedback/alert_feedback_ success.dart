import 'package:flutter/material.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/libraryman/maintenance.dart';
import 'package:likewallet/routes.dart';
import 'package:likewallet/screen/home.dart';
import 'package:likewallet/screen_util.dart';


int count = 0;
Widget alertFeedBack(context) {
  return WillPopScope(
    onWillPop: () { return Future.value(); },
    child: Dialog(
      elevation: 500,
      // backgroundColor: Colors.red,
      insetPadding: EdgeInsets.only(
        bottom: mediaQuery(context, 'height', 800),
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(30.0),
      ),
      child: Container(
        width: mediaQuery(context, 'width', 884.0),
        height: mediaQuery(context, 'height', 1183),
        decoration: BoxDecoration(
          image: new DecorationImage(
            image: new AssetImage(LikeWalletImage.feedback_animation),
            fit: BoxFit.fitWidth,
            alignment: Alignment.topCenter,
          ),
          borderRadius:
              BorderRadius.circular(mediaQuery(context, 'height', 25.0)),
          color: const Color(0xffffffff),
          // border: Border.all(width: 1.0, color: const Color(0xff707070)),
          boxShadow: [
            BoxShadow(
              color: const Color(0x29000000),
              offset: Offset(0, 45),
              blurRadius: 65,
            ),
          ],
        ),
        child: Container(
            padding: EdgeInsets.only(
              left: mediaQuery(context, 'width', 85),
              right: mediaQuery(context, 'width', 85),
            ),
            child: Column(
              children: [
                Expanded(
                  child: Container(),
                ),
                Text(
                  AppLocalizations.of(context)!
                            .translate('feedback_alert_title'),
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
                    fontSize: mediaQuery(context, 'height', 54),
                    color: LikeWalletAppTheme.bule2_8,
                    letterSpacing: 0.3,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(
                  height: mediaQuery(context, 'height', 7),
                ),
                Text(
                  AppLocalizations.of(context)!
                            .translate('feedback_alert_detail'),
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      fontSize: mediaQuery(context, 'height', 45),
                      color: LikeWalletAppTheme.bule2_8.withOpacity(0.8),
                      letterSpacing: 0.3,
                      fontWeight: FontWeight.normal,
                      height: 1.2),
                ),
                SizedBox(
                  height: mediaQuery(context, 'height', 49),
                ),
                _buttonQRcode(context),
                SizedBox(
                  height: mediaQuery(context, 'height', 75),
                ),
              ],
            )),
      ),
    ),
  );
}

Widget _buttonQRcode(context) {
  return Container(
    width: mediaQuery(context, 'width', 610.88),
    height: mediaQuery(context, 'height', 112),
    decoration: BoxDecoration(
      borderRadius: new BorderRadius.all(
        const Radius.circular(10.0),
      ),
      gradient: LinearGradient(
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
          colors: [
            LikeWalletAppTheme.bule2,
            LikeWalletAppTheme.bule2_4.withOpacity(0.9),
          ]),
    ),
    child: InkWell(
      onTap: () {
        count = 0;
        Navigator.popUntil(context, (route) {
          return count++ == 3;
        });
      },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          new Text(
            AppLocalizations.of(context)!.translate('feedback_alert_button'),
            style: TextStyle(
              letterSpacing: 0.3,
              color: LikeWalletAppTheme.bule1,
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              fontWeight: FontWeight.w100,
              fontSize: mediaQuery(context, 'height', 41),
            ),
          ),
        ],
      ),
    ),
  );
}
