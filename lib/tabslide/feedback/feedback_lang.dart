import 'package:likewallet/libraryman/applang.dart';
import 'package:shared_preferences/shared_preferences.dart';

List data = [];
Future dataChoice(sharedPreferences) async {
  sharedPreferences = await SharedPreferences.getInstance();
  print(sharedPreferences.getString('language_code'));
  if (sharedPreferences.getString('language_code') == 'en') {
    data = [
      {"id": 1, "detail": "The app was confusing to use"},
      {"id": 2, "detail": "The app was visually unappealing"},
      {"id": 3, "detail": "The app was slow"},
      {"id": 4, "detail": "The app crashed"},
      {"id": 5, "detail": "None"},
    ];
  } else if (sharedPreferences.getString('language_code') == 'th') {
    data = [
      {"id": 1, "detail": "แอพสับสน ใช้ยาก"},
      {"id": 2, "detail": "หน้าตาแอพไม่น่าใช้ ไม่สวยงาม"},
      {"id": 3, "detail": "แอพช้า"},
      {"id": 4, "detail": "แอพไม่ทำงาน"},
      {"id": 5, "detail": "อื่นๆ"},
    ];
  } else if (sharedPreferences.getString('language_code') == 'lo') {
    data = [
      {"id": 1, "detail": "The app was confusing to use"},
      {"id": 2, "detail": "The app was visually unappealing"},
      {"id": 3, "detail": "The app was slow"},
      {"id": 4, "detail": "The app crashed"},
      {"id": 5, "detail": "None"},
    ];
  } else if (sharedPreferences.getString('language_code') == 'km') {
    data = [
      {"id": 1, "detail": "The app was confusing to use"},
      {"id": 2, "detail": "The app was visually unappealing"},
      {"id": 3, "detail": "The app was slow"},
      {"id": 4, "detail": "The app crashed"},
      {"id": 5, "detail": "None"},
    ];
  } else if (sharedPreferences.getString('language_code') == 'vi') {
    data = [
      {"id": 1, "detail": "The app was confusing to use"},
      {"id": 2, "detail": "The app was visually unappealing"},
      {"id": 3, "detail": "The app was slow"},
      {"id": 4, "detail": "The app crashed"},
      {"id": 5, "detail": "None"},
    ];
  }
  return data;
}
