import 'dart:io';

import 'package:flutter/material.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/screen_util.dart';

import 'package:likewallet/screen/takephoto.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:camera/camera.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:likewallet/screen/home.dart';
import 'package:likewallet/bank/completeTX.dart';
import 'package:permission_handler/permission_handler.dart';

class KYC extends StatefulWidget {
  KYC({this.frontPhoto, this.id, this.backPhoto, this.selfiePhoto});

  final String? frontPhoto;
  final String? id;
  final String? backPhoto;
  final String? selfiePhoto;

  _KYC createState() => new _KYC(
      frontPhoto: frontPhoto,
      id: id,
      backPhoto: backPhoto,
      selfiePhoto: selfiePhoto);
}

class _KYC extends State<KYC> {
  _KYC({this.frontPhoto, this.id, this.backPhoto, this.selfiePhoto});

  final String? frontPhoto;
  final String? id;
  final String? backPhoto;
  final String? selfiePhoto;

  TextEditingController Nametext = new TextEditingController();
  TextEditingController LastNametext = new TextEditingController();
  TextEditingController IDCardtext = new TextEditingController();
  TextEditingController Fronttext = new TextEditingController();
  TextEditingController Backtext = new TextEditingController();
  TextEditingController Selfietext = new TextEditingController();
  late String Name;
  late String LastName;
  late String IDCard;
  late String Front;
  late String Back;
  late String Selfie;

  String frontPhotoUp = 'none';
  String backPhotoUp = 'none';
  String selfiePhotoUp = 'none';
  bool kycStatus = false;
  bool _saving = false;
  bool checkAutoFocus = false;
  late BaseAuth auth;
  late CameraDescription firstCamera;

 saveKYC() async {
    sharedPreferences = await SharedPreferences.getInstance();
    auth.getCurrentUser().then((decodedToken) {
      String _year = new DateTime.now().year.toString();
      String _month = new DateTime.now().month.toString();
      String _day = new DateTime.now().day.toString();
      String _date = _day + '-' + _month + '-' + _year;

      FirebaseFirestore.instance.collection('kyc').doc(decodedToken!.uid).set({
        'firstName': Name,
        'lastName': LastName,
        'fullname': Name + " " + LastName,
        'idcard': IDCard,
        'imgIdCardFront': frontPhotoUp,
        'imgIdCardBack': backPhotoUp,
        'selfie': selfiePhoto,
        'phone_number': decodedToken!.phoneNumber,
        'uid': decodedToken!.uid,
        'active': 1,
        'datetime': new DateTime.now().millisecondsSinceEpoch,
        'date': _date
      }).then((data) {
        sharedPreferences.setBool('kyc_status', true);
        setState(() {
          _saving = false;
        });
        Navigator.of(context).pushReplacementNamed('/home');
      });
    });
  }

  Future<bool> _onWillPop() async {
    return (await showDialog(
          context: context,
          builder: (context) => new AlertDialog(
            title: new Text(
                AppLocalizations.of(context)!.translate('check_exitsKYC')),
            content: new Text(AppLocalizations.of(context)!
                            .translate('check_exitsKYC_detail')),
            actions: <Widget>[
              new TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child:
                    new Text(AppLocalizations.of(context)!.translate('no_kyc')),
              ),
              new TextButton(
                onPressed: () {
                  print('exit');
                  setState(() {
                    Navigator.of(context).pop(false);
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => HomeLikewallet()),
                    );
                    _front_check = false;
                    _end_check = false;
                    _selfie_check = false;
                    SetBool_front(_front_check);
                    SetBool_end(_end_check);
                    SetBool_selfie(_selfie_check);
                  });
                },
                child:
                    new Text(AppLocalizations.of(context)!.translate('yes_kyc')),
              ),
            ],
          ),
        )) ??
        false;
  }
  setName() {
    sharedPreferences.setString('firstname', Nametext.text);
    if (!mounted) return;
    setState(() {
      Name = Nametext.text;
    });

    print(Name);
  }

  setLastName() {
    sharedPreferences.setString('lastname', LastNametext.text);
    if (!mounted) return;
    setState(() {
      LastName = LastNametext.text;
    });

    print(LastName);
  }

  setIDCard() {
    sharedPreferences.setString('idcard', IDCardtext.text);
    if (!mounted) return;
    setState(() {
      IDCard = IDCardtext.text;
    });

    print(IDCard);
  }

  setFront() {
    if (!mounted) return;
    setState(() {
      Front = Fronttext.text;
    });

    print(Front);
  }

  setBack() {
    if (!mounted) return;
    setState(() {
      Back = Backtext.text;
    });
    print(Back);
  }

  setSelfie() {
    if (!mounted) return;
    setState(() {
      Selfie = Selfietext.text;
    });

    print(Selfie);
  }

  void checkFill() async {
    sharedPreferences = await SharedPreferences.getInstance();
    String checkFirst = sharedPreferences.getString('firstname') ?? "_";
    String checkLast = sharedPreferences.getString('lastname') ?? "_";
    String checkIdCard = sharedPreferences.getString('idcard') ?? "_";
    if (checkFirst != '_') {
      if (!mounted) return;
      setState(() {
        checkAutoFocus = true;
        Name = checkFirst;
        Nametext.text = checkFirst;
      });
    }
    if (checkLast != '_') {
      setState(() {
        LastName = checkLast;
        LastNametext.text = checkLast;
      });
    }
    if (checkIdCard != '_') {
      setState(() {
        IDCard = checkIdCard;
        IDCardtext.text = checkIdCard;
      });
    }
  }

  void checkStatus() async {
    bool statusPermision = await Permission.camera.isGranted;
    if (statusPermision != true) {
      await Permission.camera.request();
    }
    bool statusPermisionMic = await Permission.microphone.isGranted;
    if (statusPermisionMic != true) {
      await Permission.microphone.request();
    }

    sharedPreferences = await SharedPreferences.getInstance();
    kycStatus = sharedPreferences.getBool('kyc_status') ?? false;

    print(kycStatus);
    auth.getCurrentUser().then((decodedToken) {
      FirebaseFirestore.instance
          .collection('kyc')
          .doc(decodedToken!.uid)
          .get()
          .then((userSnap) {
        if (userSnap.exists) {
          if (userSnap.data()!["active"] == 2) {
            setState(() {
              CompleteTX(
                title: 'Your KYC is completed',
                detail: 'pass !',
                buttonText: 'Back to Home',
                back: HomeLikewallet(),
              );
              //completed
            });
          }
          if (userSnap.data()!["active"] == 1) {
            setState(() {
              kycStatus = true;
            });
          }
          if (userSnap.data()!["active"] == 0) {
            setState(() {
              kycStatus = false;
            });
          }

          if (!mounted) return;
          setState(() {
            _saving = false;
          });
        } else {
          if (!mounted) return;
          setState(() {
            _saving = false;
          });
        }
      });
    });
  }

  @override
  void initState() {
    super.initState();
    if (!mounted) return;
    setState(() {
      _saving = true;
    });
    checkStatus();
    checkFill();
    checkPhoto();

    Nametext.addListener(setName);
    LastNametext.addListener(setLastName);
    IDCardtext.addListener(setIDCard);
    Fronttext.addListener(setFront);
    Backtext.addListener(setBack);
    Selfietext.addListener(setSelfie);

    auth = Auth();
    WidgetsFlutterBinding.ensureInitialized();
    setCamera();
    setState(() {
      if (IDCardtext.text.length <= 0) {
        IDCardtext.text = id!;
      }
    });
  }

  void setCamera() async {
    // Obtain a list of the available cameras on the device.
    final cameras = await availableCameras();
    // Get a specific camera from the list of available cameras.
    firstCamera = cameras.first;
  }

  bool _front_check = false;
  bool _end_check = false;
  bool _selfie_check = false;
  late SharedPreferences sharedPreferences;

  Widget build(BuildContext context) {
    return new WillPopScope(
      onWillPop: _onWillPop,
      child: ModalProgressHUD(
        opacity: 0.1,
        inAsyncCall: _saving,
        progressIndicator: CustomLoading(),
        child: Scaffold(
          backgroundColor: Color(0xff141322),
          body: SingleChildScrollView(
            child: Container(
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height,
              child: kycStatus == false
                  ? KYCForm()
                  : CompleteTX(
                      title: 'Your KYC is processing',
                      detail: 'Waiting for review',
                      buttonText: 'Back to Home',
                      back: HomeLikewallet(),
                    ),
            ),
          ),
        ),
      ),
    );
  }

  Widget KYCForm() {
    return new Stack(
      alignment: Alignment.center,
      children: <Widget>[
        //ปุ่ม back
        Positioned(
            top: MediaQuery.of(context).size.height *
                Screen_util("height", 139.33),
            child: GestureDetector(
              child: new Container(
                alignment: Alignment.centerLeft,
                width: MediaQuery.of(context).size.width,
                child: new IconButton(
                  icon: new Icon(
                    Icons.arrow_back_ios,
                    size: MediaQuery.of(context).size.height *
                        Screen_util("height", 44.47),
                  ),
                  color: Color(0xff707071),
                  onPressed: () => {
                    setState(() {
                      Navigator.of(context).pop();
                      _front_check = false;
                      _end_check = false;
                      _selfie_check = false;
                      SetBool_front(_front_check);
                      SetBool_end(_end_check);
                      SetBool_selfie(_selfie_check);
                    })
                  },
                ),
              ),
            )),
        Positioned(
          top: MediaQuery.of(context).size.height * Screen_util("height", 319),
          child: new Container(
              alignment: Alignment.centerLeft,
              width: MediaQuery.of(context).size.width * 0.9,
              child: Text(
                AppLocalizations.of(context)!.translate('kyc_title'),
                style: TextStyle(
                    color: Color(0xff707071),
                    fontSize: MediaQuery.of(context).size.height *
                        Screen_util("height", 89),
                    fontFamily: 'Proxima Nova'),
              )),
        ),
        Positioned(
          top: MediaQuery.of(context).size.height * Screen_util("height", 476),
          child: new Container(
            alignment: Alignment.centerLeft,
            width: MediaQuery.of(context).size.width * 0.9,
            child: Container(
              alignment: Alignment.center,
              height: MediaQuery.of(context).size.height * 0.06666666666,
              width: MediaQuery.of(context).size.width * 0.9,
              decoration: BoxDecoration(
                color: Color(0xff141322),
                border: Border.all(
                  color: Color(0xff0FE8D8),
                  width: MediaQuery.of(context).size.width * 0.00027777777,
                ),
                borderRadius: BorderRadius.all(Radius.circular(5.0)),
              ),
              child: Row(
                children: <Widget>[
                  new Container(
                    alignment: Alignment.center,
                    height: MediaQuery.of(context).size.height * 0.05,
                    width: MediaQuery.of(context).size.width * 0.65,
//                color: Colors.blue,
                    child: TextField(
                      autofocus: checkAutoFocus == true ? true : false,
                      controller: Nametext,
                      style: TextStyle(
                          color: Colors.white,
                          fontSize: MediaQuery.of(context).size.height *
                              0.02051282051),
                      decoration: InputDecoration(
                        contentPadding: EdgeInsets.only(left: 10),
                        labelText:
                            AppLocalizations.of(context)!.translate('kyc_name'),
                        labelStyle: TextStyle(
                            color: Colors.white70,
                            fontFamily:
                                AppLocalizations.of(context)!.translate('font1'),
                            fontSize: MediaQuery.of(context).size.height *
                                0.02051282051),
                        hintStyle: TextStyle(
                          fontSize: MediaQuery.of(context).size.height *
                              0.02051282051,
                          color: Colors.white,
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font1'),
                          fontWeight: FontWeight.normal,
                        ),
                        border: InputBorder.none,
                      ),
                      keyboardType: TextInputType.text,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        Positioned(
          top: MediaQuery.of(context).size.height * Screen_util("height", 692),
          child: new Container(
            alignment: Alignment.centerLeft,
            width: MediaQuery.of(context).size.width * 0.9,
            child: Container(
              alignment: Alignment.center,
              height: MediaQuery.of(context).size.height * 0.06666666666,
              width: MediaQuery.of(context).size.width * 0.9,
              decoration: BoxDecoration(
                color: Color(0xff141322),
                border: Border.all(
                  color: Color(0xff0FE8D8),
                  width: MediaQuery.of(context).size.width * 0.00027777777,
                ),
                borderRadius: BorderRadius.all(Radius.circular(5.0)),
              ),
              child: Row(
                children: <Widget>[
                  new Container(
                    alignment: Alignment.center,
                    height: MediaQuery.of(context).size.height * 0.05,
                    width: MediaQuery.of(context).size.width * 0.65,
//                color: Colors.blue,
                    child: TextField(
                      controller: LastNametext,
                      style: TextStyle(
                          color: Colors.white,
                          fontSize: MediaQuery.of(context).size.height *
                              0.02051282051),
                      decoration: InputDecoration(
                        contentPadding: EdgeInsets.only(left: 10),
                        labelText: AppLocalizations.of(context)!
                            .translate('kyc_lastname'),
                        labelStyle: TextStyle(
                            color: Colors.white70,
                            fontFamily:
                                AppLocalizations.of(context)!.translate('font1'),
                            fontSize: MediaQuery.of(context).size.height *
                                0.02051282051),
                        hintStyle: TextStyle(
                          fontSize: MediaQuery.of(context).size.height *
                              0.02051282051,
                          color: Colors.white,
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font1'),
                          fontWeight: FontWeight.normal,
                        ),
                        border: InputBorder.none,
                      ),
                      keyboardType: TextInputType.text,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        Positioned(
          top: MediaQuery.of(context).size.height * Screen_util("height", 908),
          child: new Container(
            alignment: Alignment.centerLeft,
            width: MediaQuery.of(context).size.width * 0.9,
            child: Container(
              alignment: Alignment.center,
              height: MediaQuery.of(context).size.height * 0.06666666666,
              width: MediaQuery.of(context).size.width * 0.9,
              decoration: BoxDecoration(
                color: Color(0xff141322),
                border: Border.all(
                  color: Color(0xff0FE8D8),
                  width: MediaQuery.of(context).size.width * 0.00027777777,
                ),
                borderRadius: BorderRadius.all(Radius.circular(5.0)),
              ),
              child: Row(
                children: <Widget>[
                  new Container(
                    alignment: Alignment.center,
                    height: MediaQuery.of(context).size.height * 0.05,
                    width: MediaQuery.of(context).size.width * 0.65,
//                color: Colors.blue,
                    child: TextField(
                      controller: IDCardtext,
                      style: TextStyle(
                          color: Colors.white,
                          fontSize: MediaQuery.of(context).size.height *
                              0.02051282051),
                      decoration: InputDecoration(
                        contentPadding: EdgeInsets.only(left: 10),
                        labelText: AppLocalizations.of(context)!
                            .translate('kyc_idcard'),
                        labelStyle: TextStyle(
                            color: Colors.white70,
                            fontFamily:
                                AppLocalizations.of(context)!.translate('font1'),
                            fontSize: MediaQuery.of(context).size.height *
                                0.02051282051),
                        hintStyle: TextStyle(
                          fontSize: MediaQuery.of(context).size.height *
                              0.02051282051,
                          color: Colors.white,
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font1'),
                          fontWeight: FontWeight.normal,
                        ),
                        border: InputBorder.none,
                      ),
                      keyboardType: TextInputType.text,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        Positioned(
          top: MediaQuery.of(context).size.height * Screen_util("height", 1124),
          child: new Container(
            alignment: Alignment.centerLeft,
            width: MediaQuery.of(context).size.width * 0.9,
            child: Container(
              alignment: Alignment.center,
              height: MediaQuery.of(context).size.height * 0.06666666666,
              width: MediaQuery.of(context).size.width * 0.9,
              decoration: BoxDecoration(
                color: Color(0xff141322),
                border: Border.all(
                  color: Color(0xff0FE8D8),
                  width: MediaQuery.of(context).size.width * 0.00027777777,
                ),
                borderRadius: BorderRadius.all(Radius.circular(5.0)),
              ),
              child: Row(
                children: <Widget>[
                  new Container(
                    alignment: Alignment.center,
                    height: MediaQuery.of(context).size.height * 0.05,
                    width: MediaQuery.of(context).size.width * 0.69,
//                color: Colors.blue,
                    child: TextField(
                      enabled: false,
                      controller: Fronttext,
                      style: TextStyle(
                          color: Colors.white,
                          fontSize: MediaQuery.of(context).size.height *
                              0.02051282051),
                      decoration: InputDecoration(
                        contentPadding: EdgeInsets.only(left: 10),
                        labelText: frontPhotoUp == 'none'
                            ? frontPhoto == 'none'
                                ? AppLocalizations.of(context)!
                            .translate('kyc_pic_front')
                                : frontPhoto?.substring(0, 20)!
                            : frontPhotoUp?.substring(0, 20)!,
                        labelStyle: TextStyle(
                            color: Colors.white70,
                            fontFamily:
                                AppLocalizations.of(context)!.translate('font1'),
                            fontSize: MediaQuery.of(context).size.height *
                                0.02051282051),
                        hintStyle: TextStyle(
                          fontSize: MediaQuery.of(context).size.height *
                              0.02051282051,
                          color: Colors.white,
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font1'),
                          fontWeight: FontWeight.normal,
                        ),
                        border: InputBorder.none,
                      ),
                      keyboardType: TextInputType.text,
                    ),
                  ),
                  GestureDetector(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => takePhoto(
                                  pageFront: 1,
                                  firstCamera: firstCamera,
                                )),
                      );
                    },
                    child: Container(
                        alignment: Alignment.center,
                        height: MediaQuery.of(context).size.height * 0.05,
                        width: MediaQuery.of(context).size.width * 0.2,
                        child: _front_check == true
                            ? Container(
                                alignment: Alignment.centerRight,
                                height: MediaQuery.of(context).size.width *
                                    0.07429629629,
                                width: MediaQuery.of(context).size.width *
                                    0.07429629629,
                                child: Icon(
                                  Icons.check,
                                  color: Color(0xff0FE8D8),
                                ),
                              )
                            : Container(
                                alignment: Alignment.centerRight,
                                height: MediaQuery.of(context).size.width *
                                    0.07429629629,
                                width: MediaQuery.of(context).size.width *
                                    0.07429629629,
                                decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    image: DecorationImage(
                                        fit: BoxFit.cover,
                                        image: AssetImage(
                                            'assets/image/edit_photo.png')),
                                    border: Border.all(
                                        color: Colors.white,
                                        width:
                                            MediaQuery.of(context).size.width *
                                                0.00277777777)),
                              )),
                  ),
                ],
              ),
            ),
          ),
        ),
        Positioned(
          top: MediaQuery.of(context).size.height * Screen_util("height", 1340),
          child: new Container(
            alignment: Alignment.centerLeft,
            width: MediaQuery.of(context).size.width * 0.9,
            child: Container(
              alignment: Alignment.center,
              height: MediaQuery.of(context).size.height * 0.06666666666,
              width: MediaQuery.of(context).size.width * 0.9,
              decoration: BoxDecoration(
                color: Color(0xff141322),
                border: Border.all(
                  color: Color(0xff0FE8D8),
                  width: MediaQuery.of(context).size.width * 0.00027777777,
                ),
                borderRadius: BorderRadius.all(Radius.circular(5.0)),
              ),
              child: Row(
                children: <Widget>[
                  new Container(
                    alignment: Alignment.center,
                    height: MediaQuery.of(context).size.height * 0.05,
                    width: MediaQuery.of(context).size.width * 0.69,
//                color: Colors.blue,
                    child: TextField(
                      enabled: false,
                      controller: Backtext,
                      style: TextStyle(
                          color: Colors.white,
                          fontSize: MediaQuery.of(context).size.height *
                              0.02051282051),
                      decoration: InputDecoration(
                        contentPadding: EdgeInsets.only(left: 10),
                        labelText: backPhotoUp == 'none'
                            ? backPhoto == 'none'
                                ? AppLocalizations.of(context)!
                            .translate('kyc_pic_back')
                                : backPhoto?.substring(0, 20)!
                            : backPhotoUp?.substring(0, 20)!,
//                              labelText: AppLocalizations.of(context)
//                                  .translate('kyc_pic_back'),
                        labelStyle: TextStyle(
                            color: Colors.white70,
                            fontFamily:
                                AppLocalizations.of(context)!.translate('font1'),
                            fontSize: MediaQuery.of(context).size.height *
                                0.02051282051),
                        hintStyle: TextStyle(
                          fontSize: MediaQuery.of(context).size.height *
                              0.02051282051,
                          color: Colors.white,
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font1'),
                          fontWeight: FontWeight.normal,
                        ),
                        border: InputBorder.none,
                      ),
                      keyboardType: TextInputType.text,
                    ),
                  ),
                  GestureDetector(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => takePhoto(
                                  pageFront: 2,
                                  firstCamera: firstCamera,
                                )),
                      );
                    },
                    child: Container(
                        alignment: Alignment.center,
                        height: MediaQuery.of(context).size.height * 0.05,
                        width: MediaQuery.of(context).size.width * 0.2,
                        child: _end_check == true
                            ? Container(
                                alignment: Alignment.centerRight,
                                height: MediaQuery.of(context).size.width *
                                    0.07429629629,
                                width: MediaQuery.of(context).size.width *
                                    0.07429629629,
                                child: Icon(
                                  Icons.check,
                                  color: Color(0xff0FE8D8),
                                ),
                              )
                            : Container(
                                alignment: Alignment.centerRight,
                                height: MediaQuery.of(context).size.width *
                                    0.07429629629,
                                width: MediaQuery.of(context).size.width *
                                    0.07429629629,
                                decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    image: DecorationImage(
                                        fit: BoxFit.cover,
                                        image: AssetImage(
                                            'assets/image/edit_photo.png')),
                                    border: Border.all(
                                        color: Colors.white,
                                        width:
                                            MediaQuery.of(context).size.width *
                                                0.00277777777)),
                              )),
                  ),
                ],
              ),
            ),
          ),
        ),
        Positioned(
          top: MediaQuery.of(context).size.height * Screen_util("height", 1558),
          child: new Container(
            alignment: Alignment.centerLeft,
            width: MediaQuery.of(context).size.width * 0.9,
            child: Container(
              alignment: Alignment.center,
              height: MediaQuery.of(context).size.height * 0.06666666666,
              width: MediaQuery.of(context).size.width * 0.9,
              decoration: BoxDecoration(
                color: Color(0xff141322),
                border: Border.all(
                  color: Color(0xff0FE8D8),
                  width: MediaQuery.of(context).size.width * 0.00027777777,
                ),
                borderRadius: BorderRadius.all(Radius.circular(5.0)),
              ),
              child: Row(
                children: <Widget>[
                  new Container(
                    alignment: Alignment.center,
                    height: MediaQuery.of(context).size.height * 0.05,
                    width: MediaQuery.of(context).size.width * 0.69,
//                color: Colors.blue,
                    child: TextField(
                      enabled: false,
                      controller: Selfietext,
                      style: TextStyle(
                          color: Colors.white,
                          fontSize: MediaQuery.of(context).size.height *
                              0.02051282051),
                      decoration: InputDecoration(
                        contentPadding: EdgeInsets.only(left: 10),
//                                labelText: AppLocalizations.of(context)
//                                    .translate('kyc_details'),
                        labelText: selfiePhotoUp == 'none'
                            ? selfiePhoto == 'none'
                                ? AppLocalizations.of(context)!
                            .translate('kyc_details')
                                : selfiePhoto?.substring(0, 20)!
                            : selfiePhotoUp?.substring(0, 20)!,
                        labelStyle: TextStyle(
                            color: Colors.white70,
                            fontFamily:
                                AppLocalizations.of(context)!.translate('font1'),
                            fontSize: MediaQuery.of(context).size.height *
                                0.02051282051),
                        hintStyle: TextStyle(
                          fontSize: MediaQuery.of(context).size.height *
                              0.02051282051,
                          color: Colors.white,
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font1'),
                          fontWeight: FontWeight.normal,
                        ),
                        border: InputBorder.none,
                      ),
                      keyboardType: TextInputType.text,
                    ),
                  ),
                  GestureDetector(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => takePhoto(
                                  pageFront: 3,
                                  firstCamera: firstCamera,
                                )),
                      );
                    },
                    child: Container(
                        alignment: Alignment.center,
                        height: MediaQuery.of(context).size.height * 0.05,
                        width: MediaQuery.of(context).size.width * 0.2,
//                color: Colors.blue,
                        child: _selfie_check == true
                            ? Container(
                                alignment: Alignment.centerRight,
                                height: MediaQuery.of(context).size.width *
                                    0.07429629629,
                                width: MediaQuery.of(context).size.width *
                                    0.07429629629,
                                child: Icon(
                                  Icons.check,
                                  color: Color(0xff0FE8D8),
                                ),
                              )
                            : Container(
                                alignment: Alignment.centerRight,
                                height: MediaQuery.of(context).size.width *
                                    0.07429629629,
                                width: MediaQuery.of(context).size.width *
                                    0.07429629629,
                                decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    image: DecorationImage(
                                        fit: BoxFit.cover,
                                        image: AssetImage(
                                            'assets/image/edit_photo.png')),
                                    border: Border.all(
                                        color: Colors.white,
                                        width:
                                            MediaQuery.of(context).size.width *
                                                0.00277777777)),
                              )),
                  ),
                ],
              ),
            ),
          ),
        ),
        if (Name != '' &&
            LastName != '' &&
            IDCard != '' &&
            frontPhotoUp != 'none' &&
            backPhotoUp != 'none' &&
            selfiePhoto != 'none')
          Positioned(
            top: MediaQuery.of(context).size.height *
                Screen_util("height", 1874),
            child: ButtonTheme(
              minWidth:
                  MediaQuery.of(context).size.width * Screen_util("width", 930),
              height: MediaQuery.of(context).size.height *
                  Screen_util("height", 132),
              child: new TextButton(
                  onPressed: () {
                    setState(() {
                      _saving = true;
                    });
                    saveKYC();
                  },
                  style: ButtonStyle(
                      shape: MaterialStateProperty.all(new RoundedRectangleBorder(borderRadius: new BorderRadius.circular(5.0),)),
                      foregroundColor: MaterialStateProperty.all<Color>( Color(0xff0FE8D8),),
                      backgroundColor: MaterialStateProperty.all<Color>(Color(0xff0FE8D8))
                  ),
                  // shape: new RoundedRectangleBorder(borderRadius: new BorderRadius.circular(5.0),),
                  // disabledColor: Color(0xff0FE8D8),
                  // color: Color(0xff0FE8D8),
//
                  child: new Text(
                    AppLocalizations.of(context)!.translate('kyc_button'),
                    style: TextStyle(
                        color: Color(0xff000000),
                        fontSize: MediaQuery.of(context).size.height *
                            Screen_util("height", 51),
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                        fontWeight: FontWeight.bold),
                  )),
            ),
          ),
      ],
    );
  }

  SetBool_front(bool value) async {
    sharedPreferences = await SharedPreferences.getInstance();
    setState(() {
      _front_check = value;
      sharedPreferences.setBool("front_check", _front_check);
    });
  }

  SetBool_end(bool value) async {
    sharedPreferences = await SharedPreferences.getInstance();
    setState(() {
      _end_check = value;
      sharedPreferences.setBool("end_check", _end_check);
    });
  }

  SetBool_selfie(bool value) async {
    sharedPreferences = await SharedPreferences.getInstance();
    setState(() {
      _selfie_check = value;
      sharedPreferences.setBool("selfie_check", _selfie_check);
    });
  }


  checkPhoto() async {
    sharedPreferences = await SharedPreferences.getInstance();
    setState(() {
      _front_check = sharedPreferences.getBool("front_check") ?? false;
      _end_check = sharedPreferences.getBool("end_check") ?? false;
      _selfie_check = sharedPreferences.getBool("selfie_check") ?? false;
    });

    if (_front_check == true) {
      setState(() {
        frontPhotoUp = sharedPreferences.getString('uploadID') ?? "";
      });
      print(frontPhotoUp);
    }
    if (_end_check == true) {
      setState(() {
        backPhotoUp = sharedPreferences.getString('uploadBackID') ?? "";
      });
      print(backPhotoUp);
    }
    if (_selfie_check == true) {
      setState(() {
        selfiePhotoUp = sharedPreferences.getString('uploadSelfie') ?? "";
      });
      print(selfiePhotoUp);
    }
    print(_front_check);
  }
}
