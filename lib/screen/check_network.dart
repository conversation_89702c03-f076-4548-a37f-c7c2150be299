import 'dart:io';

import 'package:flutter/material.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:likewallet/Theme.dart';
import 'package:flutter_svg/flutter_svg.dart';

class CheckNetWorkScreen extends StatefulWidget {
  @override
  _CheckNetWorkScreen createState() => _CheckNetWorkScreen();
}

class _CheckNetWorkScreen extends State<CheckNetWorkScreen> {
  @override
  Widget build(BuildContext context) {
    screenUtil(context);
    return WillPopScope(
        onWillPop: () { return Future.value(); },
        child: Scaffold(
            body: Stack(
          children: [
            Container(
              width: 1080.0,
              height: 2340.0,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment(0.0, -1.0),
                  end: Alignment(0.0, 1.0),
                  colors: [
                    const Color(0xff1d2b44),
                    const Color(0xff25263c),
                    const Color(0xff141322)
                  ],
                  stops: [0.0, 0.453, 1.0],
                ),
              ),
            ),
            Column(
              children: [
                SizedBox(
                  height: 216.sp,
                ),
                Container(
                    padding: EdgeInsets.symmetric(horizontal: 75.sp),
                    alignment: Alignment.center,
                    // width: 930.sp,
                    child: Text(
                      AppLocalizations.of(context)!
                            .translate('network_error_sorry'),
                      textAlign: TextAlign.center,
                      style: TextStyle(
                          letterSpacing: 1.3.sp,
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font1'),
                          fontWeight: FontWeight.normal,
                          color: LikeWalletAppTheme.white,
                          fontSize: 59.sp),
                    )),
                SizedBox(
                  height: 88.sp,
                ),
                Container(
                  height: 150.83.sp,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    boxShadow: [
                      new BoxShadow(
                          color: Colors.black.withOpacity(0.16),
                          offset: new Offset(1, 2),
                          blurRadius: 2.0,
                          spreadRadius: 1.0),
                    ],
                  ),
                  child: Image.asset(
                    LikeWalletImage.network_error,
                    fit: BoxFit.fill,
                  ),
                ),
                SizedBox(
                  height: 57.5.sp,
                ),
                Container(
                    padding: EdgeInsets.symmetric(horizontal: 75.sp),
                    alignment: Alignment.center,
                    // width: 930.sp,
                    child: Text(
                      AppLocalizations.of(context)!
                            .translate('network_error_title'),
                      textAlign: TextAlign.center,
                      style: TextStyle(
                          letterSpacing: 0.6,
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font1'),
                          fontWeight: FontWeight.normal,
                          color: LikeWalletAppTheme.bule1,
                          fontSize: 42.sp),
                    )),
                SizedBox(
                  height: 280.sp,
                ),
                InkWell(
                  onTap: () => {exit(0)},
                  child: Container(
                    height: 132.91.sp,
                    width: 442.16.sp,
                    decoration: BoxDecoration(
                      boxShadow: [
                        new BoxShadow(
                            color: Colors.black.withOpacity(0.35),
                            offset: new Offset(3.sp, 6.sp),
                            blurRadius: 18.sp,
                            spreadRadius: 1.0),
                      ],
                    ),
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        SvgPicture.string(
                          '<svg viewBox="0.0 0.0 442.2 132.9" ><defs><filter id="shadow"><feDropShadow dx="3" dy="6" stdDeviation="18"/></filter><linearGradient id="gradient" x1="0.131318" y1="0.5" x2="1.164654" y2="0.5"><stop offset="0.0" stop-color="#ff52fcf0"  /><stop offset="0.389134" stop-color="#ff11e7db"  /><stop offset="1.0" stop-color="#ff22c4e6"  /></linearGradient></defs><path transform="translate(-64.3, 0.0)" d="M 82.42448425292969 0 L 488.3377685546875 0 C 498.3472290039062 0 506.4614868164062 8.114260673522949 506.4614868164062 18.12371063232422 L 506.4614868164062 114.7835006713867 C 506.4614868164062 124.7929534912109 498.3472290039062 132.9072113037109 488.3377685546875 132.9072113037109 L 82.42448425292969 132.9072113037109 C 72.4150390625 132.9072113037109 64.30076599121094 124.7929534912109 64.30076599121094 114.7835006713867 L 64.30076599121094 18.12371063232422 C 64.30076599121094 8.114260673522949 72.4150390625 0 82.42448425292969 0 Z" fill="url(#gradient)" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" filter="url(#shadow)"/></svg>',
                          allowDrawingOutsideViewBox: true,
                          fit: BoxFit.fill,
                        ),
                        Text(
                          AppLocalizations.of(context)!
                            .translate('network_error_button'),
                          style: TextStyle(
                              color: Colors.black,
                              fontFamily: AppLocalizations.of(context)!
                            .translate('font1'),
                              fontSize: 54.sp,
                              fontWeight: FontWeight.normal),
                        ),
                      ],
                    ),
                  ),
                ),
                // new ButtonTheme(
                //   minWidth: 450.sp,
                //   height: 132.sp,
                //   child: new FlatButton(
                //       shape: new RoundedRectangleBorder(
                //         borderRadius: new BorderRadius.circular(8.0),
                //       ),
                //       disabledColor: Color(0xff00F1E0),
                //       color: Color(0xff00F1E0),
                //
                //       child
                // ),
              ],
            ),
          ],
        )));
  }
}
