import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/Theme.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:likewallet/libraryman/app_local.dart';

class AutoLockDetail extends StatefulWidget {
  final String point;
  final String dayLock;
  final String rangeLock;
  final String dayUnLock;
  final String amountDayLock;
  final String balanceDayLock;
  AutoLockDetail(
      {required this.point,
      required this.dayLock,
      required this.rangeLock,
      required this.dayUnLock,
      required this.amountDayLock,
      required this.balanceDayLock});
  _AutoLockDetail createState() => new _AutoLockDetail();
}

class _AutoLockDetail extends State<AutoLockDetail> {
  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Column(
          children: [
            SizedBox(height: 130.h),
            backButton(context, Colors.grey),
            SizedBox(height: 50.h),
            Container(
              width: 400.w,
              alignment: Alignment.center,
              child: Text(
                'รายละเอียด',
                style: TextStyle(
                    color: LikeWalletAppTheme.black.withOpacity(0.9),
                    fontFamily:
                        AppLocalizations.of(context)!.translate('font1'),
                    fontSize: 50.h),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            SizedBox(height: 70.h),
            Row(
              children: [
                Image.asset(
                  LikeWalletImage.logo_likepoint,
                  height: 100.h,
                ),
                SizedBox(width: 50.w),
                Text(
                  'LIKE',
                  style: TextStyle(
                      color: LikeWalletAppTheme.black.withOpacity(0.9),
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      fontSize: 60.h),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
            SizedBox(height: 70.h),
            text(
                title: 'จำนวนเงินทั้งหมด',
                detail: widget.point,
                endLine: 'LIKE'),
            text(title: 'วันที่ล็อค', detail: widget.dayLock, endLine: ''),
            text(title: 'วันที่ปลดล็อค', detail: widget.dayUnLock, endLine: ''),
            text(
                title: 'ช่วงเวลาที่ถูกล็อค',
                detail: widget.rangeLock,
                endLine: 'วัน'),
            text(
                title: 'จำนวนวันที่ล็อคไป',
                detail: widget.amountDayLock,
                endLine: 'วัน'),
            text(
                title: 'นับถอยหลังปลดล็อค',
                detail: widget.balanceDayLock,
                endLine: 'วัน'),
            // text(title: 'แจกรางวัล', detail: '1', endLine: 'ครั้ง/วัน'),
            Expanded(child: Container()),
            _buttonClaim(),
            SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  text({String? title, String? detail, String? endLine = ''}) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 40.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '${title!}',
            style: TextStyle(
                color: LikeWalletAppTheme.black.withOpacity(0.9),
                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                fontSize: 48.h),
            overflow: TextOverflow.ellipsis,
          ),
          SizedBox(width: 50.w),
          Text(
            '${detail!}' + '${endLine! != '' ? " " + endLine : ""}',
            style: TextStyle(
                color: LikeWalletAppTheme.black.withOpacity(0.9),
                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                fontSize: 48.h),
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buttonClaim() {
    return Container(
      width: double.infinity,
      // width: 700.w,
      height: 132.h,
      child: TextButton(
          style: ButtonStyle(
            backgroundColor: MaterialStateProperty.all<Color>(
              Color(0xff00F1E0),
            ),
            shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(5.0),
            )),
          ),
          onPressed: () async {
            Navigator.of(context).pop();
          },
          child: new Text(
            'เข้าใจแล้ว',
            style: TextStyle(
                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                color: Colors.black,
                fontSize: 42.h,
                fontWeight: FontWeight.w100),
          )),
    );
  }
}
