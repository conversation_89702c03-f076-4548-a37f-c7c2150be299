import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/libraryman/app_local.dart';

import 'package:likewallet/screen/NavigationBar.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:likewallet/screen_util.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';

class PageViewNotify extends StatefulWidget {
  PageViewNotify({
    this.title,
    this.details,
    this.image,
  });
  final String? title;
  final String? details;
  final String? image;

  _PageViewNotify createState() =>
      new _PageViewNotify(title: title, details: details, image: image);
}

class _PageViewNotify extends State<PageViewNotify> {
  _PageViewNotify({
    this.title,
    this.details,
    this.image,
  });

  final String? title;
  final String? details;
  final String? image;
  bool _saving = false;

  Widget buildForPhone() {
    return Padding(
        padding: EdgeInsets.only(
            left: mediaQuery(context, 'width', 50),
            right: mediaQuery(context, 'width', 50)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(height: mediaQuery(context, "height", 120)),
            backButton(context, LikeWalletAppTheme.gray),
            SizedBox(height: mediaQuery(context, "height", 50)),
            Text(
              title.toString(),
              style: TextStyle(
                  fontSize: mediaQuery(context, "height", 55),
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  fontWeight: FontWeight.normal,
                  color: LikeWalletAppTheme.black),
            ),
            SizedBox(height: mediaQuery(context, "height", 50)),
            Text(
              details.toString(),
              style: TextStyle(
                  fontSize: mediaQuery(context, "height", 45),
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  fontWeight: FontWeight.normal,
                  color: LikeWalletAppTheme.gray),
            ),
          ],
        ));
  }

  @override
  Widget build(BuildContext context) {
    final double shortestSide = MediaQuery.of(context).size.shortestSide;
    final bool useMobileLayout = shortestSide < 600.0;
    final Orientation orientation = MediaQuery.of(context).orientation;
    return ModalProgressHUD(
        opacity: 0.1,
        progressIndicator: CustomLoading(),
        inAsyncCall: _saving,
        child: Scaffold(body: SingleChildScrollView(child: buildForPhone())));
  }
}
