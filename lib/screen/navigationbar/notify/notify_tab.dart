import 'dart:io';
import 'dart:ui';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:intl/intl.dart';
import 'package:likewallet/bank/choiceTopay.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:likewallet/libraryman/open_web_notitle.dart';
import 'package:likewallet/main.dart';
import 'package:likewallet/middleware/check_maintenance/check_maintenance.dart';
import 'package:likewallet/screen/navigationbar/notify/pageview.dart';
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/screen/navigationbar/notify/image_slider.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as riverpod;

class NotifyTab extends StatefulWidget {
  _NotifyTab createState() => new _NotifyTab();
}

class _NotifyTab extends State<NotifyTab> with TickerProviderStateMixin {
  GlobalKey<ScaffoldState> _scaffoldKey = new GlobalKey();
  AlignmentGeometry _alignment = Alignment.centerLeft;
  final List<String> imgList = [];
  int _current = 0;
  late BaseAuth auth;
  String uid = '';
  bool _saving = false;
  final fireStore = FirebaseFirestore.instance;
  late SharedPreferences sharedPreferences;
  final f_en = DateFormat('MMMM d kk:mm a');
  final f_th = DateFormat('d MMMM kk:mm น.');
  var lang = 'en';
  int tabSelect = 1;
  late CheckAbout checkAbout;
  bool permissionNews = false;
  bool loadingNews = false;
  double rate = 100.00;
  double amount = 0.00;
  String accountNumber = '';
  double totalSell = 0.00;
  double fee = 0;
  String Symbol = 'THB';

  @override
  void initState() {
    super.initState();
    auth = Auth();
    checkAbout = OnCheckAbout();
    // getLanguage();\
    checkPermission();
    _loadNotify();
  }

  Future _loadNotify() async {
    final decodeToken = await auth.getCurrentUser();
    setState(() {
      uid = decodeToken!.uid;
    });
    print(uid);
  }

  checkPermission() async {
    setState(() => loadingNews = true);
    final news = await checkAbout.checkPermissionMenu(
        tierLevel: context.read(tierLevel).state, page: 'news');
    if (!mounted) return;
    setState(() {
      loadingNews = false;
      permissionNews = news;
      print("news $permissionNews");
    });
  }

  @override
  Widget build(BuildContext context) {
    screenUtil(context);
    return ModalProgressHUD(
      opacity: 0.1,
      progressIndicator: CustomLoading(),
      inAsyncCall: _saving,
      child: Scaffold(
        key: _scaffoldKey,
        body: Container(
          height: MediaQuery.of(context).size.height,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              _appBar(),
              SizedBox(height: 50.h),
              loadingNews
                  ? Center(
                      child: SpinKitFadingCircle(
                        color: LikeWalletAppTheme.bule1,
                        size: 50,
                      ),
                    )
                  : tabSelect == 1
                      ? permissionNews
                          ? ImageSlider()
                          : notificationPage()
                      : notificationPage()
            ],
          ),
        ),
      ),
    );
  }

  void _changeAlignment() {
    setState(() {
      _alignment = _alignment == Alignment.centerRight
          ? Alignment.centerLeft
          : Alignment.centerRight;
    });
  }

  Widget _appBar() {
    return permissionNews
        ? Align(
            alignment: Alignment.center,
            child: Container(
                margin: EdgeInsets.only(top: 147.h),
                width: 902.0.w,
                height: 80.0.h,
                decoration: BoxDecoration(
                  color: Color(0xff201F2D).withOpacity(0.08),
                  borderRadius: BorderRadius.circular(100.0.h),
                  border:
                      Border.all(width: 2.0.sp, color: const Color(0x4dffffff)),
                ),
                child: Stack(
                  children: [
                    InkWell(
                      onTap: () => _changeAlignment(),
                      child: Container(
                        height: 120.0,
                        width: 902.0.w,
                        child: AnimatedAlign(
                          alignment: _alignment,
                          curve: Curves.ease,
                          duration: Duration(milliseconds: 300),
                          child: Container(
                            decoration: BoxDecoration(
                              color: Color(0xff474652),
                              borderRadius: BorderRadius.only(
                                topLeft: tabSelect == 1
                                    ? Radius.circular(50.0)
                                    : Radius.circular(50.0),
                                bottomLeft: tabSelect == 1
                                    ? Radius.circular(50.0)
                                    : Radius.circular(050.0),
                                bottomRight: tabSelect == 2
                                    ? Radius.circular(50.0)
                                    : Radius.circular(50.0),
                                topRight: tabSelect == 2
                                    ? Radius.circular(50.0)
                                    : Radius.circular(50.0),
                              ),
                            ),
                            width: 900.0.w / 2,
                            height: 80.0.h,
                          ),
                        ),
                      ),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        _tabSelect1(Alignment.centerRight, 'NEWS'),
                        _tabSelect2(Alignment.centerLeft, 'NOTIFICATIONS')
                      ],
                    ),
                  ],
                )),
          )
        : Container();
  }

  Widget _tabSelect1(index, text) {
    return Expanded(
      child: InkWell(
        onTap: () async {
          if (tabSelect == 2) {
            _changeAlignment();
            await Future.delayed(Duration(milliseconds: 150)).then((value) {
              setState(() => tabSelect = 1);
            });
          }
        },
        child: Container(
          alignment: Alignment.center,
          height: double.infinity,
          child: Text(
            text,
            // AppLocalizations.of(context)!.translate(text),
            style: TextStyle(
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              fontSize: 30.h,
              color: _alignment == Alignment.centerLeft
                  ? Color(0xff08e8de)
                  : Colors.black.withOpacity(0.8),
              letterSpacing: 1.5.sp,
              shadows: [
                Shadow(
                  color: const Color(0x29000000),
                  offset: Offset(0, 3.sp),
                  blurRadius: 6.sp,
                )
              ],
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  Widget _tabSelect2(index, text) {
    return Expanded(
      child: InkWell(
        onTap: () async {
          if (tabSelect == 1) {
            _changeAlignment();
            await Future.delayed(Duration(milliseconds: 150))
                .then((value) async {
              setState(() => tabSelect = 2);
              // QuerySnapshot ds = await fireStore
              //     .collection('notificationByUser')
              //     .doc(uid)
              //     .collection('notify')
              //     .where("status", isEqualTo: "unread")
              //     .get();
              // if (ds.docs.isNotEmpty) {
              //   ds.docs.forEach((chat) async {
              //     await fireStore
              //         .collection('notificationByUser')
              //         .doc(uid)
              //         .collection('notify')
              //         .doc(chat.id)
              //         .update({"status": "read"});
              //   });
              // } else {
              //   print('ไม่มีข้อความ');
              // }
            });
          }
        },
        child: Container(
            alignment: Alignment.center,
            height: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(100.0.h),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  text,
                  // AppLocalizations.of(context)!.translate(text),
                  style: TextStyle(
                    fontFamily:
                        AppLocalizations.of(context)!.translate('font1'),
                    fontSize: 30.h,
                    color: _alignment == Alignment.centerRight
                        ? Color(0xff08e8de)
                        : Colors.black.withOpacity(0.8),
                    letterSpacing: 1.5.sp,
                    shadows: [
                      Shadow(
                        color: const Color(0x29000000),
                        offset: Offset(0, 3.sp),
                        blurRadius: 6.sp,
                      )
                    ],
                  ),
                  textAlign: TextAlign.center,
                ),
                uid == ''
                    ? Container()
                    : StreamBuilder<QuerySnapshot>(
                        stream: fireStore
                            .collection('notificationByUser')
                            .doc(uid)
                            .collection('notify')
                            .where("status", isEqualTo: "unread")
                            .snapshots(),
                        builder: (BuildContext context,
                            AsyncSnapshot<QuerySnapshot> snapshot) {
                          if (snapshot.hasError) {
                            print('มีบางอย่างผิกพลาด');
                            return Container();
                          }
                          if (snapshot.connectionState ==
                              ConnectionState.waiting) {
                            return Container();
                          }
                          if (snapshot.data!.docs.isEmpty) {
                            return Container();
                          } else {
                            return Container(
                              margin: EdgeInsets.symmetric(horizontal: 20.w),
                              height: 22.h,
                              width: 22.h,
                              decoration: BoxDecoration(
                                  color: Color(0xffFFC400),
                                  shape: BoxShape.circle),
                            );
                          }
                        }),
              ],
            )),
      ),
    );
  }

//เส้นคั้น
  Widget border() {
    return Container(
      height: 460.sp,
      decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              spreadRadius: 0,
              blurRadius: 3.sp,
              color: Color(0xff707070).withOpacity(0.2),
              offset: Offset(
                1.1.sp,
                0.0,
              ),
            ),
          ],
          border: Border(
            right: BorderSide(
              color: Color(0xff707070).withOpacity(0.12),
              width: 3.sp,
            ),
          )),
    );
  }

  Widget notificationPage() {
    return Expanded(
      child: uid.isEmpty
          ? SpinKitFadingCircle(
              color: LikeWalletAppTheme.bule1,
              size: 200.sp,
            )
          : StreamBuilder<QuerySnapshot>(
              stream: FirebaseFirestore.instance
                  .collection('notificationByUser')
                  .doc(uid)
                  .collection('notify')
                  .orderBy('timeStamp', descending: true)
                  .snapshots(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return Center(child: CircularProgressIndicator());
                }

                if (snapshot.hasError) {
                  return Center(child: Text(snapshot.error.toString()));
                }

                if (!snapshot.hasData) {
                  return Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Theme.of(context).primaryColor,
                      ),
                    ),
                  );
                } else {
                  if (snapshot.data!.docs.length == 0) {
                    return Center(
                        child: Text(AppLocalizations.of(context)!
                            .translate('not_massage')));
                  } else {
                    return new ListView(
                      padding: EdgeInsets.only(bottom: 350.h),
                      children:
                          snapshot.data!.docs.map((DocumentSnapshot document) {
                        return _body(document.data(), int.parse(document.id));
                      }).toList(),
                    );
                  }
                }
              }),
    );
  }

  //ส่วนcard listview
  Widget _body(document, int id) {
    return Padding(
        padding: EdgeInsets.only(left: 40.w, right: 40.w, top: 5.h),
        child: Stack(
          children: <Widget>[
            Card(
              elevation: 5,
              shape: RoundedRectangleBorder(
                side: BorderSide(color: Colors.white70, width: 1.sp),
                borderRadius: BorderRadius.circular(10),
              ),
              child: document['detail']=="คุณผ่านการ KYC เรียบร้อย"
              ?InkWell(
                  onTap: () async {
                    if (document['url'] == "") {
                      Dialog simpleDialog = Dialog(
                        elevation: 500,
                        backgroundColor: Colors.transparent,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(30.0),
                        ),
                        child: Container(
                          // height: mediaQuery(context, 'height', 554.63),
                          width: mediaQuery(context, 'width', 929.64),
                          color: Colors.transparent,
                          margin: EdgeInsets.only(
                              bottom: mediaQuery(context, 'height', 600)),
                          child: new ClipRect(
                            child: new BackdropFilter(
                              filter: new ImageFilter.blur(
                                  sigmaX: 10.0, sigmaY: 10.0),
                              child: Container(
                                decoration: BoxDecoration(
                                  color:
                                      LikeWalletAppTheme.white.withOpacity(0.6),
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(20.0)),
                                ),
                                height: mediaQuery(context, 'height', 554.63),
                                width: mediaQuery(context, 'width', 929.64),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: <Widget>[
                                    SizedBox(height: 30.h),
                                    Text(
                                      document['detail'],
                                      style: TextStyle(
                                        letterSpacing: 0.3,
                                        fontFamily:
                                            AppLocalizations.of(context)!
                                                .translate('font1'),
                                        color: LikeWalletAppTheme.black
                                            .withOpacity(1),
                                        fontSize:
                                            mediaQuery(context, "height", 49),
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    Expanded(child: Container()),
                                    Container(
                                      margin: EdgeInsets.only(
                                          bottom: mediaQuery(
                                              context, 'height', 30)),
                                      width:
                                          mediaQuery(context, 'width', 777.62),
                                      child: Text(
                                          AppLocalizations.of(context)!.translate('tabslide_kyc1'),
                                        textAlign: TextAlign.center,
                                        maxLines: 4,
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                          letterSpacing: 0.3,
                                          fontFamily:
                                              AppLocalizations.of(context)!
                                                  .translate('font1'),
                                          color: LikeWalletAppTheme.black
                                              .withOpacity(1),
                                          height: mediaQuery(
                                              context, "height", 3.5),
                                          fontSize:
                                              mediaQuery(context, "height", 42),
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                    Container(
                                      margin: EdgeInsets.only(
                                          bottom: mediaQuery(
                                              context, 'height', 30)),
                                      width:
                                      mediaQuery(context, 'width', 777.62),
                                      child: Text(
                                        AppLocalizations.of(context)!.translate('tabslide_kyc2'),
                                        textAlign: TextAlign.center,
                                        maxLines: 4,
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                          letterSpacing: 0.3,
                                          fontFamily:
                                          AppLocalizations.of(context)!
                                              .translate('font1'),
                                          color: LikeWalletAppTheme.black
                                              .withOpacity(1),
                                          height: mediaQuery(
                                              context, "height", 3.5),
                                          fontSize:
                                          mediaQuery(context, "height", 42),
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                    Expanded(child: Container()),

                                    Container(
                                        width: mediaQuery(
                                            context, 'width', 777.62),
                                        decoration: BoxDecoration(
                                          border: Border(
                                            top: BorderSide(
                                              //                   <--- left side
                                              color: LikeWalletAppTheme.black
                                                  .withOpacity(0.4),
                                              width: mediaQuery(
                                                  context, 'width', 1),
                                            ),
                                          ),
                                        ),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: <Widget>[
                                            GestureDetector(
                                              onTap: () {
                                                Navigator.of(context).pop();
                                              },
                                              child: Container(
                                                alignment: Alignment.center,
                                                height: mediaQuery(
                                                    context, 'height', 127.66),
                                                width: mediaQuery(context,
                                                        'width', 777.62) /
                                                    2,
                                                child: Text(
                                                  AppLocalizations.of(context)!
                                                      .translate('messages_no'),
                                                  textAlign: TextAlign.center,
                                                  style: TextStyle(
                                                    letterSpacing: 0.3,
                                                    fontFamily:
                                                        AppLocalizations.of(
                                                                context)!
                                                            .translate('font1'),
                                                    color: LikeWalletAppTheme
                                                        .bule1_7
                                                        .withOpacity(1),
                                                    fontSize: mediaQuery(
                                                        context, "height", 40),
                                                    fontWeight: FontWeight.w600,
                                                  ),
                                                ),
                                              ),
                                            ),
                                            GestureDetector(
                                              onTap: () {
                                                Navigator.push(
                                                    context,
                                                    MaterialPageRoute(
                                                        builder: (context) => ChoiceTopay(
                                                            amount: amount,
                                                            fee: fee,
                                                            rate: rate,
                                                            symbol: Symbol,
                                                            totalSell: totalSell)));
                                              },
                                              child: Container(
                                                alignment: Alignment.center,
                                                height: mediaQuery(
                                                    context, 'height', 127.66),
                                                width: mediaQuery(context,
                                                    'width', 777.62) /
                                                    2,
                                                child: Text(
                                                  AppLocalizations.of(context)!
                                                      .translate('tabslide_account'),
                                                  textAlign: TextAlign.center,
                                                  style: TextStyle(
                                                    letterSpacing: 0.3,
                                                    fontFamily:
                                                    AppLocalizations.of(
                                                        context)!
                                                        .translate('font1'),
                                                    color: LikeWalletAppTheme
                                                        .bule1_7
                                                        .withOpacity(1),
                                                    fontSize: mediaQuery(
                                                        context, "height", 40),
                                                    fontWeight: FontWeight.w600,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        )),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      );
                      showDialog(
                          context: context,
                          builder: (BuildContext context) => simpleDialog);

                      await fireStore
                          .collection('notificationByUser')
                          .doc(uid)
                          .collection('notify')
                          .doc(id.toString())
                          .update({"status": "read"});
                    } else {
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) => WebOpenNoTitle(
                                    title: AppLocalizations.of(context)!
                                        .translate('notifications'),
                                    url: document['url'],
                                  )));
                    }
                  },
                  child: Stack(
                    children: <Widget>[
                      Row(
                        children: <Widget>[
                          Padding(
                            padding: EdgeInsets.all(25.w),
                            child: Container(
                              // height: mediaQuery(context, "height", 310),
                              width: 232.5.sp,
                              child: CachedNetworkImage(
                                imageUrl: document['icon'],
                                placeholder: (context, url) =>
                                    SpinKitFadingCircle(
                                  color: LikeWalletAppTheme.bule1,
                                  size: 100.sp,
                                ),
                                errorWidget: (context, url, error) =>
                                    Icon(Icons.error),
                              ),
                            ),
                          ),
                          Expanded(
                            child: Container(),
                          ),
                          Container(
                            alignment: Alignment.center,
                            child: Container(
                              padding: EdgeInsets.only(right: 10.w),
                              alignment: Alignment.center,
                              height: 310.h,
                              width: 570.w,
                              child: Column(
                                children: <Widget>[
                                  Align(
                                    alignment: Alignment.topLeft,
                                    child: Container(
                                      margin: EdgeInsets.only(
                                        top: 40.sp,
                                      ),
                                      child: Text(
                                        document['title'],
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                            fontSize: 40.sp,
                                            fontFamily:
                                                AppLocalizations.of(context)!
                                                    .translate('font1'),
                                            fontWeight: FontWeight.w600,
                                            color: Colors.black),
                                      ),
                                    ),
                                  ),
                                  Align(
                                    alignment: Alignment.topLeft,
                                    child: Container(
                                      margin: EdgeInsets.only(
                                        top: 10.sp,
                                      ),
                                      child: Text(
                                        document['detail'],
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                            fontSize: 36.sp,
                                            fontFamily:
                                                AppLocalizations.of(context)!
                                                    .translate('font1'),
                                            fontWeight: FontWeight.normal,
                                            color:
                                                Colors.black.withOpacity(0.6)),
                                      ),
                                    ),
                                  ),
                                  Expanded(
                                    child: Container(),
                                  ),
                                  Align(
                                    alignment: Alignment.topLeft,
                                    child: Container(
                                      // margin: EdgeInsets.only(
                                      //     bottom: 58.sp,
                                      //     ),
                                      child: Text(
                                        lang == 'th'
                                            ? f_th.format(DateTime
                                                .fromMillisecondsSinceEpoch(
                                                    id * 1000))
                                            : f_en.format(DateTime
                                                .fromMillisecondsSinceEpoch(
                                                    id * 1000)),
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                            fontSize: 30.sp,
                                            fontFamily:
                                                AppLocalizations.of(context)!
                                                    .translate('font1'),
                                            fontWeight: FontWeight.normal,
                                            color:
                                                Colors.black.withOpacity(0.6)),
                                      ),
                                    ),
                                  ),
                                  SizedBox(height: 30.h),
                                ],
                              ),
                            ),
                          ),
                          document['status'] == 'unread'
                              ? Container(
                                  margin:
                                      EdgeInsets.symmetric(horizontal: 20.w),
                                  height: 30.h,
                                  width: 30.h,
                                  decoration: BoxDecoration(
                                      color: Color(0xffFFC400),
                                      shape: BoxShape.circle),
                                )
                              : Container(
                                  margin:
                                      EdgeInsets.symmetric(horizontal: 20.w),
                                ),
                          SizedBox(width: 25.w),
                        ],
                      ),
                      Positioned(
                        top: 38.5.h,
                        right: 42.5.w,
                        child: Container(
                          height: 35.h,
                          width: 35.w,
                          child: InkWell(
                            onTap: () => deleteNotify(id),
                            child: Image.asset(
                              LikeWalletImage.notify_close,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ))
              : document['detail']=="Your KYC is verify!"
                ?InkWell(
                  onTap: () async {
                    if (document['url'] == "") {
                      Dialog simpleDialog = Dialog(
                        elevation: 500,
                        backgroundColor: Colors.transparent,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(30.0),
                        ),
                        child: Container(
                          // height: mediaQuery(context, 'height', 554.63),
                          width: mediaQuery(context, 'width', 929.64),
                          color: Colors.transparent,
                          margin: EdgeInsets.only(
                              bottom: mediaQuery(context, 'height', 600)),
                          child: new ClipRect(
                            child: new BackdropFilter(
                              filter: new ImageFilter.blur(
                                  sigmaX: 10.0, sigmaY: 10.0),
                              child: Container(
                                decoration: BoxDecoration(
                                  color:
                                  LikeWalletAppTheme.white.withOpacity(0.6),
                                  borderRadius:
                                  BorderRadius.all(Radius.circular(20.0)),
                                ),
                                height: mediaQuery(context, 'height', 554.63),
                                width: mediaQuery(context, 'width', 929.64),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: <Widget>[
                                    SizedBox(height: 30.h),
                                    Text(
                                      document['detail'],
                                      style: TextStyle(
                                        letterSpacing: 0.3,
                                        fontFamily:
                                        AppLocalizations.of(context)!
                                            .translate('font1'),
                                        color: LikeWalletAppTheme.black
                                            .withOpacity(1),
                                        fontSize:
                                        mediaQuery(context, "height", 49),
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    Expanded(child: Container()),
                                    Container(
                                      margin: EdgeInsets.only(
                                          bottom: mediaQuery(
                                              context, 'height', 30)),
                                      width:
                                      mediaQuery(context, 'width', 777.62),
                                      child: Text(
                                        AppLocalizations.of(context)!.translate('tabslide_kyc1'),
                                        textAlign: TextAlign.center,
                                        maxLines: 4,
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                          letterSpacing: 0.3,
                                          fontFamily:
                                          AppLocalizations.of(context)!
                                              .translate('font1'),
                                          color: LikeWalletAppTheme.black
                                              .withOpacity(1),
                                          height: mediaQuery(
                                              context, "height", 3.5),
                                          fontSize:
                                          mediaQuery(context, "height", 42),
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                    Container(
                                      margin: EdgeInsets.only(
                                          bottom: mediaQuery(
                                              context, 'height', 30)),
                                      width:
                                      mediaQuery(context, 'width', 777.62),
                                      child: Text(
                                        AppLocalizations.of(context)!.translate('tabslide_kyc2'),
                                        textAlign: TextAlign.center,
                                        maxLines: 4,
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                          letterSpacing: 0.3,
                                          fontFamily:
                                          AppLocalizations.of(context)!
                                              .translate('font1'),
                                          color: LikeWalletAppTheme.black
                                              .withOpacity(1),
                                          height: mediaQuery(
                                              context, "height", 3.5),
                                          fontSize:
                                          mediaQuery(context, "height", 42),
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                    Expanded(child: Container()),

                                    Container(
                                        width: mediaQuery(
                                            context, 'width', 777.62),
                                        decoration: BoxDecoration(
                                          border: Border(
                                            top: BorderSide(
                                              //                   <--- left side
                                              color: LikeWalletAppTheme.black
                                                  .withOpacity(0.4),
                                              width: mediaQuery(
                                                  context, 'width', 1),
                                            ),
                                          ),
                                        ),
                                        child: Row(
                                          mainAxisAlignment:
                                          MainAxisAlignment.center,
                                          children: <Widget>[
                                            GestureDetector(
                                              onTap: () {
                                                Navigator.of(context).pop();
                                              },
                                              child: Container(
                                                alignment: Alignment.center,
                                                height: mediaQuery(
                                                    context, 'height', 127.66),
                                                width: mediaQuery(context,
                                                    'width', 777.62) /
                                                    2,
                                                child: Text(
                                                  AppLocalizations.of(context)!
                                                      .translate('messages_no'),
                                                  textAlign: TextAlign.center,
                                                  style: TextStyle(
                                                    letterSpacing: 0.3,
                                                    fontFamily:
                                                    AppLocalizations.of(
                                                        context)!
                                                        .translate('font1'),
                                                    color: LikeWalletAppTheme
                                                        .bule1_7
                                                        .withOpacity(1),
                                                    fontSize: mediaQuery(
                                                        context, "height", 40),
                                                    fontWeight: FontWeight.w600,
                                                  ),
                                                ),
                                              ),
                                            ),
                                            GestureDetector(
                                              onTap: () {
                                                Navigator.push(
                                                    context,
                                                    MaterialPageRoute(
                                                        builder: (context) => ChoiceTopay(
                                                            amount: amount,
                                                            fee: fee,
                                                            rate: rate,
                                                            symbol: Symbol,
                                                            totalSell: totalSell)));
                                              },
                                              child: Container(
                                                alignment: Alignment.center,
                                                height: mediaQuery(
                                                    context, 'height', 127.66),
                                                width: mediaQuery(context,
                                                    'width', 777.62) /
                                                    2,
                                                child: Text(
                                                  AppLocalizations.of(context)!
                                                      .translate('tabslide_account'),
                                                  textAlign: TextAlign.center,
                                                  style: TextStyle(
                                                    letterSpacing: 0.3,
                                                    fontFamily:
                                                    AppLocalizations.of(
                                                        context)!
                                                        .translate('font1'),
                                                    color: LikeWalletAppTheme
                                                        .bule1_7
                                                        .withOpacity(1),
                                                    fontSize: mediaQuery(
                                                        context, "height", 40),
                                                    fontWeight: FontWeight.w600,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        )),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      );
                      showDialog(
                          context: context,
                          builder: (BuildContext context) => simpleDialog);

                      await fireStore
                          .collection('notificationByUser')
                          .doc(uid)
                          .collection('notify')
                          .doc(id.toString())
                          .update({"status": "read"});
                    } else {
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) => WebOpenNoTitle(
                                title: AppLocalizations.of(context)!
                                    .translate('notifications'),
                                url: document['url'],
                              )));
                    }
                  },
                  child: Stack(
                    children: <Widget>[
                      Row(
                        children: <Widget>[
                          Padding(
                            padding: EdgeInsets.all(25.w),
                            child: Container(
                              // height: mediaQuery(context, "height", 310),
                              width: 232.5.sp,
                              child: CachedNetworkImage(
                                imageUrl: document['icon'],
                                placeholder: (context, url) =>
                                    SpinKitFadingCircle(
                                      color: LikeWalletAppTheme.bule1,
                                      size: 100.sp,
                                    ),
                                errorWidget: (context, url, error) =>
                                    Icon(Icons.error),
                              ),
                            ),
                          ),
                          Expanded(
                            child: Container(),
                          ),
                          Container(
                            alignment: Alignment.center,
                            child: Container(
                              padding: EdgeInsets.only(right: 10.w),
                              alignment: Alignment.center,
                              height: 310.h,
                              width: 570.w,
                              child: Column(
                                children: <Widget>[
                                  Align(
                                    alignment: Alignment.topLeft,
                                    child: Container(
                                      margin: EdgeInsets.only(
                                        top: 40.sp,
                                      ),
                                      child: Text(
                                        document['title'],
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                            fontSize: 40.sp,
                                            fontFamily:
                                            AppLocalizations.of(context)!
                                                .translate('font1'),
                                            fontWeight: FontWeight.w600,
                                            color: Colors.black),
                                      ),
                                    ),
                                  ),
                                  Align(
                                    alignment: Alignment.topLeft,
                                    child: Container(
                                      margin: EdgeInsets.only(
                                        top: 10.sp,
                                      ),
                                      child: Text(
                                        document['detail'],
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                            fontSize: 36.sp,
                                            fontFamily:
                                            AppLocalizations.of(context)!
                                                .translate('font1'),
                                            fontWeight: FontWeight.normal,
                                            color:
                                            Colors.black.withOpacity(0.6)),
                                      ),
                                    ),
                                  ),
                                  Expanded(
                                    child: Container(),
                                  ),
                                  Align(
                                    alignment: Alignment.topLeft,
                                    child: Container(
                                      // margin: EdgeInsets.only(
                                      //     bottom: 58.sp,
                                      //     ),
                                      child: Text(
                                        lang == 'th'
                                            ? f_th.format(DateTime
                                            .fromMillisecondsSinceEpoch(
                                            id * 1000))
                                            : f_en.format(DateTime
                                            .fromMillisecondsSinceEpoch(
                                            id * 1000)),
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                            fontSize: 30.sp,
                                            fontFamily:
                                            AppLocalizations.of(context)!
                                                .translate('font1'),
                                            fontWeight: FontWeight.normal,
                                            color:
                                            Colors.black.withOpacity(0.6)),
                                      ),
                                    ),
                                  ),
                                  SizedBox(height: 30.h),
                                ],
                              ),
                            ),
                          ),
                          document['status'] == 'unread'
                              ? Container(
                            margin:
                            EdgeInsets.symmetric(horizontal: 20.w),
                            height: 30.h,
                            width: 30.h,
                            decoration: BoxDecoration(
                                color: Color(0xffFFC400),
                                shape: BoxShape.circle),
                          )
                              : Container(
                            margin:
                            EdgeInsets.symmetric(horizontal: 20.w),
                          ),
                          SizedBox(width: 25.w),
                        ],
                      ),
                      Positioned(
                        top: 38.5.h,
                        right: 42.5.w,
                        child: Container(
                          height: 35.h,
                          width: 35.w,
                          child: InkWell(
                            onTap: () => deleteNotify(id),
                            child: Image.asset(
                              LikeWalletImage.notify_close,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ))
                :InkWell(
                  onTap: () async {
                    if (document['url'] == "") {
                      Dialog simpleDialog = Dialog(
                        elevation: 500,
                        backgroundColor: Colors.transparent,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(30.0),
                        ),
                        child: Container(
                          // height: mediaQuery(context, 'height', 554.63),
                          width: mediaQuery(context, 'width', 929.64),
                          color: Colors.transparent,
                          margin: EdgeInsets.only(
                              bottom: mediaQuery(context, 'height', 600)),
                          child: new ClipRect(
                            child: new BackdropFilter(
                              filter: new ImageFilter.blur(
                                  sigmaX: 10.0, sigmaY: 10.0),
                              child: Container(
                                decoration: BoxDecoration(
                                  color:
                                  LikeWalletAppTheme.white.withOpacity(0.6),
                                  borderRadius:
                                  BorderRadius.all(Radius.circular(20.0)),
                                ),
                                height: mediaQuery(context, 'height', 554.63),
                                width: mediaQuery(context, 'width', 929.64),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: <Widget>[
                                    SizedBox(height: 30.h),
                                    Text(
                                        document['title'],
                                      style: TextStyle(
                                        letterSpacing: 0.3,
                                        fontFamily:
                                        AppLocalizations.of(context)!
                                            .translate('font1'),
                                        color: LikeWalletAppTheme.black
                                            .withOpacity(1),
                                        fontSize:
                                        mediaQuery(context, "height", 49),
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    Expanded(child: Container()),
                                    Container(
                                      margin: EdgeInsets.only(
                                          bottom: mediaQuery(
                                              context, 'height', 30)),
                                      width:
                                      mediaQuery(context, 'width', 777.62),
                                      child: Text(
                                        document['detail'],
                                        textAlign: TextAlign.center,
                                        maxLines: 4,
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                          letterSpacing: 0.3,
                                          fontFamily:
                                          AppLocalizations.of(context)!
                                              .translate('font1'),
                                          color: LikeWalletAppTheme.black
                                              .withOpacity(1),
                                          height: mediaQuery(
                                              context, "height", 3.5),
                                          fontSize:
                                          mediaQuery(context, "height", 42),
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                    Expanded(child: Container()),

                                    Container(
                                        width: mediaQuery(
                                            context, 'width', 777.62),
                                        decoration: BoxDecoration(
                                          border: Border(
                                            top: BorderSide(
                                              //                   <--- left side
                                              color: LikeWalletAppTheme.black
                                                  .withOpacity(0.4),
                                              width: mediaQuery(
                                                  context, 'width', 1),
                                            ),
                                          ),
                                        ),
                                        child: Row(
                                          mainAxisAlignment:
                                          MainAxisAlignment.center,
                                          children: <Widget>[
                                            GestureDetector(
                                              onTap: () {
                                                Navigator.of(context).pop();
                                              },
                                              child: Container(
                                                alignment: Alignment.center,
                                                height: mediaQuery(
                                                    context, 'height', 127.66),
                                                width: mediaQuery(context,
                                                    'width', 777.62) /
                                                    2,
                                                child: Text(
                                                  AppLocalizations.of(context)!
                                                      .translate('messages_ok'),
                                                  textAlign: TextAlign.center,
                                                  style: TextStyle(
                                                    letterSpacing: 0.3,
                                                    fontFamily:
                                                    AppLocalizations.of(
                                                        context)!
                                                        .translate('font1'),
                                                    color: LikeWalletAppTheme
                                                        .bule1_7
                                                        .withOpacity(1),
                                                    fontSize: mediaQuery(
                                                        context, "height", 40),
                                                    fontWeight: FontWeight.w600,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        )),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      );
                      showDialog(
                          context: context,
                          builder: (BuildContext context) => simpleDialog);

                      await fireStore
                          .collection('notificationByUser')
                          .doc(uid)
                          .collection('notify')
                          .doc(id.toString())
                          .update({"status": "read"});
                    } else {
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) => WebOpenNoTitle(
                                title: AppLocalizations.of(context)!
                                    .translate('notifications'),
                                url: document['url'],
                              )));
                    }
                  },
                  child: Stack(
                    children: <Widget>[
                      Row(
                        children: <Widget>[
                          Padding(
                            padding: EdgeInsets.all(25.w),
                            child: Container(
                              // height: mediaQuery(context, "height", 310),
                              width: 232.5.sp,
                              child: CachedNetworkImage(
                                imageUrl: document['icon'],
                                placeholder: (context, url) =>
                                    SpinKitFadingCircle(
                                      color: LikeWalletAppTheme.bule1,
                                      size: 100.sp,
                                    ),
                                errorWidget: (context, url, error) =>
                                    Icon(Icons.error),
                              ),
                            ),
                          ),
                          Expanded(
                            child: Container(),
                          ),
                          Container(
                            alignment: Alignment.center,
                            child: Container(
                              padding: EdgeInsets.only(right: 10.w),
                              alignment: Alignment.center,
                              height: 310.h,
                              width: 570.w,
                              child: Column(
                                children: <Widget>[
                                  Align(
                                    alignment: Alignment.topLeft,
                                    child: Container(
                                      margin: EdgeInsets.only(
                                        top: 40.sp,
                                      ),
                                      child: Text(
                                        document['title'],
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                            fontSize: 40.sp,
                                            fontFamily:
                                            AppLocalizations.of(context)!
                                                .translate('font1'),
                                            fontWeight: FontWeight.w600,
                                            color: Colors.black),
                                      ),
                                    ),
                                  ),
                                  Align(
                                    alignment: Alignment.topLeft,
                                    child: Container(
                                      margin: EdgeInsets.only(
                                        top: 10.sp,
                                      ),
                                      child: Text(
                                        document['detail'],
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                            fontSize: 36.sp,
                                            fontFamily:
                                            AppLocalizations.of(context)!
                                                .translate('font1'),
                                            fontWeight: FontWeight.normal,
                                            color:
                                            Colors.black.withOpacity(0.6)),
                                      ),
                                    ),
                                  ),
                                  Expanded(
                                    child: Container(),
                                  ),
                                  Align(
                                    alignment: Alignment.topLeft,
                                    child: Container(
                                      // margin: EdgeInsets.only(
                                      //     bottom: 58.sp,
                                      //     ),
                                      child: Text(
                                        lang == 'th'
                                            ? f_th.format(DateTime
                                            .fromMillisecondsSinceEpoch(
                                            id * 1000))
                                            : f_en.format(DateTime
                                            .fromMillisecondsSinceEpoch(
                                            id * 1000)),
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                            fontSize: 30.sp,
                                            fontFamily:
                                            AppLocalizations.of(context)!
                                                .translate('font1'),
                                            fontWeight: FontWeight.normal,
                                            color:
                                            Colors.black.withOpacity(0.6)),
                                      ),
                                    ),
                                  ),
                                  SizedBox(height: 30.h),
                                ],
                              ),
                            ),
                          ),
                          document['status'] == 'unread'
                              ? Container(
                            margin:
                            EdgeInsets.symmetric(horizontal: 20.w),
                            height: 30.h,
                            width: 30.h,
                            decoration: BoxDecoration(
                                color: Color(0xffFFC400),
                                shape: BoxShape.circle),
                          )
                              : Container(
                            margin:
                            EdgeInsets.symmetric(horizontal: 20.w),
                          ),
                          SizedBox(width: 25.w),
                        ],
                      ),
                      Positioned(
                        top: 38.5.h,
                        right: 42.5.w,
                        child: Container(
                          height: 35.h,
                          width: 35.w,
                          child: InkWell(
                            onTap: () => deleteNotify(id),
                            child: Image.asset(
                              LikeWalletImage.notify_close,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ))
            ),
          ],
        ));
  }

  deleteNotify(id) {
    FirebaseFirestore.instance
        .collection('notificationByUser')
        .doc(uid)
        .collection('notify')
        .doc(id.toString())
        .delete()
        .then((value) {
      return Fluttertoast.showToast(
        msg: "ลบสำเร็จ",
        toastLength: Toast.LENGTH_SHORT,
        backgroundColor: LikeWalletAppTheme.bule1,
        gravity: ToastGravity.BOTTOM,
        textColor: LikeWalletAppTheme.bule2,
      );
    });
  }
}
