import 'dart:convert';
import 'dart:ui';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:likewallet/main.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as riverpod;
import 'package:likewallet/Theme.dart';
import 'package:likewallet/middleware/getLanguage.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:likewallet/model/news.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:likewallet/libraryman/app_local.dart';

class ImageSlider extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return _ImageSlider();
  }
}

class _ImageSlider extends State<ImageSlider> {
  final List<News> dataNews = [];

  int _current = 0;
  late OnLanguage language;
  String localDateFormat = '';

  @override
  void initState() {
    super.initState();
    language = CallLanguage();
    firstStep();
  }

  firstStep() async {
    //เคลียข้อมูล List
    setState(() => dataNews.clear());
    var lang = await language.getLanguage();
    setState(() => localDateFormat = lang);
    // print("เช็คภาษา  = " + lang);
    _getNews();
  }

  _getNews() async {
    if (context.read(tierLevel).state == 'tier1' ||
        context.read(tierLevel).state == 'tierBU') {
      final res = await FirebaseFirestore.instance
          .collection('notificationNews')
          .doc('tier1')
          .collection('news')
          .where('status', isEqualTo: 'active')
          .orderBy('timestamp', descending: true)
          .get();
      res.docs.forEach((value) {
        setState(() => dataNews.add(News.fromJson(value.data())));
      });
    } else if (context.read(tierLevel).state == 'normal') {
      final res = await FirebaseFirestore.instance
          .collection('notificationNews')
          .doc('normal')
          .collection('news')
          .where('status', isEqualTo: 'active')
          .orderBy('timestamp', descending: true)
          .get();
      res.docs.forEach((value) {
        setState(() => dataNews.add(News.fromJson(value.data())));
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return dataNews.length == 0
        ? Expanded(
            child: Center(
                // child: SpinKitFadingCircle(
                //   color: LikeWalletAppTheme.bule1,
                //   size: 200.h,
                // ),
                ),
          )
        : Expanded(
            child: Column(children: [
              SizedBox(height: 78.h),
              CarouselSlider(
                items: dataNews
                    .map((item) => Container(
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius:
                                  BorderRadius.all(Radius.circular(10.0)),
                              boxShadow: [
                                BoxShadow(
                                  spreadRadius: 0,
                                  blurRadius: 6,
                                  color: Colors.grey.withOpacity(0.2),
                                  offset: Offset(
                                    -2.0,
                                    0.5,
                                  ),
                                ),
                              ],
                            ),
                            margin: EdgeInsets.only(top: 0.0.h, bottom: 20.0.h),
                            child: ClipRRect(
                                borderRadius:
                                    BorderRadius.all(Radius.circular(5.0)),
                                child: Stack(
                                  children: <Widget>[
                                    CachedNetworkImage(
                                      height: 759.h,
                                      width: 759.h,
                                      imageUrl: item.image,
                                      fit: BoxFit.fill,
                                      placeholder: (context, url) => Center(
                                        child: SpinKitFadingCircle(
                                          color: LikeWalletAppTheme.bule1,
                                          size: 100.sp,
                                        ),
                                      ),
                                      errorWidget: (context, url, error) =>
                                          Icon(Icons.error),
                                    ),
                                  ],
                                )),
                          ),
                        ))
                    .toList(),
                options: CarouselOptions(
                    height: 759.h,
                    viewportFraction: 0.7,
                    enlargeCenterPage: true,
                    disableCenter: false,
                    reverse: false,
                    enableInfiniteScroll: false,
                    initialPage: 0,
                    aspectRatio: 2.0,
                    onPageChanged: (index, reason) {
                      setState(() {
                        _current = index;
                        print(_current);
                      });
                    }),
              ),
              SizedBox(height: 78.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: map<Widget>(dataNews, (index, url) {
                  return Container(
                    alignment: Alignment.topCenter,
                    height: 17.sp,
                    width: 17.sp,
                    margin: EdgeInsets.symmetric(horizontal: 12.sp),
                    decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: _current == index
                            ? LikeWalletAppTheme.bule1
                            : LikeWalletAppTheme.gray.withOpacity(0.5)),
                  );
                }),
              ),
              SizedBox(height: 129.h),
              Expanded(
                // height: 200,
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 150.w),
                        child: Text(
                          dataNews[_current].title,
                          style: TextStyle(
                            fontFamily: AppLocalizations.of(context)!
                                .translate('font1'),
                            fontSize: 43.h,
                            color: const Color(0xff000000),
                            letterSpacing: 1.29.w,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      SizedBox(height: 97.h),
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 150.w),
                        child: Text(
                          dataNews[_current].detail,
                          style: TextStyle(
                            fontFamily: AppLocalizations.of(context)!
                                .translate('font1'),
                            fontSize: 43.h,
                            color: const Color(0xb2000000),
                            letterSpacing: 1.29.w,
                            fontWeight: FontWeight.normal,
                          ),
                        ),
                      ),
                      SizedBox(height: 87.h),
                      Container(
                        alignment: Alignment.centerLeft,
                        padding: EdgeInsets.only(left: 150.w),
                        child: Text(
                          dataNews[_current].timestamp == "" ? " " :DateFormat('dd MMM yyyy', localDateFormat)
                              .format(DateTime.fromMicrosecondsSinceEpoch(
                                  int.parse(dataNews[_current].timestamp) * 1000))
                              .toString(),
                          style: TextStyle(
                            fontFamily: AppLocalizations.of(context)!
                                .translate('font1'),
                            fontSize: 43.h,
                            color: const Color(0xb2000000),
                            letterSpacing: 1.29.w,
                            fontWeight: FontWeight.normal,
                          ),
                        ),
                      ),
                      SizedBox(height: 320.h),
                    ],
                  ),
                ),
              ),
            ]),
          );
  }

  List<T> map<T>(List list, Function handler) {
    List<T> result = [];
    for (var i = 0; i < list.length; i++) {
      result.add(handler(i, list[i]));
    }
    return result;
  }
}
