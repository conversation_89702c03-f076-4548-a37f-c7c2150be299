import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/main.dart';
import 'package:likewallet/model/pageStatus.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:share/share.dart';
import 'package:likewallet/middleware/check_maintenance/check_maintenance.dart';
import 'package:likewallet/app_config.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as riverpod;
import 'package:likewallet/close_maintenance.dart';
import 'package:likewallet/middleware/getLanguage.dart';

class ReferPage extends StatefulWidget {
  State<StatefulWidget> createState() => new _ReferPage();
}

class _ReferPage extends State<ReferPage> {
  late BaseAuth auth;
  String refCode = '..loading';
  late CheckAbout checkAbout;
  late OnLanguage language;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    checkAbout = OnCheckAbout();
    language = CallLanguage();
    auth = Auth();
    checkFirst();
  }

  checkFirst() async {
    PageMaintenance statusPage = await checkAbout.checkTierPermission(
        tierLevel: context.read(tierLevel).state, page: 'refer');
    if (statusPage.status == 'active') {
      //เริ่มทำงาน
      print('active');
      setInitState();
    } else {
      //ปิด maintenance
      print('inactive');
      final lang = await language.getLanguage();
      final title = await checkAbout.selectLanguage(
          language: lang, detail: statusPage.title);
      final detail = await checkAbout.selectLanguage(
          language: lang, detail: statusPage.detail);
      final detailTime = await checkAbout.selectLanguage(
          language: lang, detail: statusPage.detail_time);
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
            builder: (context) => CloseMaintenance(
                  title: title,
                  detail: detail,
                  detailTime: detailTime,
                  url: statusPage.url,
                )),
      );
    }
  }

  setInitState() async {
    auth.getCurrentUser().then((snapshot) {
      FirebaseFirestore.instance
          .collection('users')
          .doc(snapshot!.uid)
          .get()
          .then((DocumentSnapshot<Map<String, dynamic>> ds) {
        if (!mounted) return;
        setState(() {
          refCode = ds.data()!['selfCode'];
        });
      });
    });
  }

  buildForPhone() {
    return Scaffold(
      backgroundColor: LikeWalletAppTheme.white,
      body: new Stack(
        alignment: Alignment.topCenter,
        children: <Widget>[
          Container(
            height: mediaQuery(context, 'height', 2340),
            color: LikeWalletAppTheme.white,
          ),
          Positioned(
            child: Container(
              height: mediaQuery(context, 'height', 780),
              color: LikeWalletAppTheme.bule1,
            ),
          ),
          Positioned(
            top: mediaQuery(context, 'height', 139.33),
            left: mediaQuery(context, 'width', 157.2),
            child: _logo(),
          ),
          Positioned(
            top: mediaQuery(context, 'height', 390),
            child: _cradBody(),
          ),
          Positioned(
            top: mediaQuery(context, 'height', 1579),
            child: _buttonCopy(),
          ),
          Positioned(
            top: mediaQuery(context, 'height', 1750),
            child: _buttonQRcode(),
          ),
          Positioned(
            top: mediaQuery(context, 'height', 130),
            left: mediaQuery(context, 'width', 75),
            child: backButton(context, Colors.black),
          )
        ],
      ),
    );
  }

  Widget _logo() {
    return Row(
      children: <Widget>[
        Image.asset(
          LikeWalletImage.icon_refer,
          height: mediaQuery(context, 'height', 233.41),
        ),
//        Text(
//          '1 refer = 100 Likepoint',
//          style: TextStyle(
//            color: LikeWalletAppTheme.black,
//            fontSize: mediaQuery(context, 'height', 51),
//            fontFamily: AppLocalizations.of(context)!.translate('font1'),
//            fontWeight: FontWeight.w100,
//          ),
//        )
      ],
    );
  }

  void showShortToast(msg, Color color) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_SHORT,
        backgroundColor: color,
        textColor: Colors.white);
  }

  Widget _cradBody() {
    return Container(
      height: mediaQuery(context, 'height', 1103.83),
      width: mediaQuery(context, 'width', 936),
      decoration: BoxDecoration(
        color: LikeWalletAppTheme.white,
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 4.0, // has the effect of softening the shadow
            spreadRadius: 0.0, // has the effect of extending the shadow
            offset: Offset(
              0.0, // horizontal, move right 10
              0.0, // vertical, move down 10
            ),
          )
        ],
      ),
      child: Padding(
        padding: EdgeInsets.only(
          top: mediaQuery(context, 'height', 50),
//          left: mediaQuery(context, 'width', 50),
          bottom: mediaQuery(context, 'height', 30),
        ),
        child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Padding(
                padding: EdgeInsets.only(
                  left: mediaQuery(context, 'width', 50),
                ),
                child: Text(
                  AppLocalizations.of(context)!.translate('refer_title'),
                  style: TextStyle(
                    color: LikeWalletAppTheme.black,
                    fontSize: mediaQuery(context, 'height', 44),
                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(
                  left: mediaQuery(context, 'width', 50),
                ),
                child: Text(
                  refCode,
                  style: TextStyle(
                    color: LikeWalletAppTheme.black,
                    fontSize: mediaQuery(context, 'height', 90),
                    fontFamily: 'font1',
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Expanded(
                child: Container(),
              ),
              Align(
                  alignment: Alignment.center,
                  child: Container(
                    decoration: BoxDecoration(
                      color: LikeWalletAppTheme.white,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black12,
                          blurRadius:
                              3.0, // has the effect of softening the shadow
                          spreadRadius:
                              2.0, // has the effect of extending the shadow
                          offset: Offset(
                            0.0, // horizontal, move right 10
                            0.0, // vertical, move down 10
                          ),
                        )
                      ],
                    ),
                    child: RepaintBoundary(
//                  key: globalKey,
                      child: QrImageView(
                        backgroundColor: Colors.white,
                        data: refCode,
                        size: mediaQuery(context, 'height', 491.24),
                      ),
                    ),
                  )),
              Expanded(
                child: Container(),
              ),
              Container(
                padding:
                    EdgeInsets.only(bottom: mediaQuery(context, 'height', 40)),
                alignment: Alignment.topCenter,
                child: Text(
                  AppLocalizations.of(context)!.translate('refer_detail'),
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: LikeWalletAppTheme.Purple.withOpacity(0.9),
                    fontSize: mediaQuery(context, 'height', 45),
                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
            ]),
      ),
    );
  }

  Widget _buttonCopy() {
    return Container(
      width: mediaQuery(context, 'width', 930),
      height: mediaQuery(context, 'height', 132),
      child: ElevatedButton(
        style: ButtonStyle(
          shape: MaterialStateProperty.all<RoundedRectangleBorder>(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8.0),
            ),
          ),
          backgroundColor: MaterialStateProperty.resolveWith<Color>(
                (Set<MaterialState> states) {
              if (states.contains(MaterialState.disabled)) {
                return LikeWalletAppTheme.bule1;
              }
              return LikeWalletAppTheme.bule2;
            },
          ),
        ),
        onPressed: () {
          print(refCode);
          Clipboard.setData(ClipboardData(text: refCode));
          showShortToast("copy ref successfully", Colors.grey);
        },
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 10.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Text(
                AppLocalizations.of(context)!.translate('refer_copy'),
                style: TextStyle(
                  color: LikeWalletAppTheme.bule1,
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  fontWeight: FontWeight.w200,
                  fontSize: mediaQuery(context, 'height', 50),
                ),
              ),
            ],
          ),
        ),
      )
    );
  }

  Widget _buttonQRcode() {
    return Container(
      width: mediaQuery(context, 'width', 930),
      height: mediaQuery(context, 'height', 132),
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8.0),
          ),
          primary: LikeWalletAppTheme.bule2,
          onPrimary: LikeWalletAppTheme.bule1,
          textStyle: TextStyle(
            fontFamily: AppLocalizations.of(context)!.translate('font1'),
            fontWeight: FontWeight.w200,
            fontSize: mediaQuery(context, 'height', 50),
          ),
        ),
        onPressed: () {
          Share.share(refCode, subject: 'Ref');
        },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Text(
              AppLocalizations.of(context)!.translate('refer_share'),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(body: buildForPhone());
  }
}

class ShowQRFriend extends StatefulWidget {
  ShowQRFriend({this.referCode});
  final String? referCode;
  @override
  _ShowQRFriend createState() => _ShowQRFriend(referCode: referCode);
}

class _ShowQRFriend extends State<ShowQRFriend> {
  _ShowQRFriend({this.referCode});
  final String? referCode;
  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Scaffold(
      body: new Container(
        child: new Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Expanded(
              child: Center(
                child: RepaintBoundary(
//                  key: globalKey,
                  child: QrImageView(
                    backgroundColor: Colors.white,
                    data: referCode ?? '',
                    size: MediaQuery.of(context).size.height * 0.35308547008,
                  ),
                ),
              ),
            )
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: Color(0xff00F1E0),
        onPressed: () {
          Navigator.pop(context);
        },
        child: Icon(Icons.arrow_back_ios),
      ),
    );
  }
}
