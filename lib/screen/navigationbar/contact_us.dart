import 'package:flutter/material.dart';
import 'package:likewallet/chat_in_app/index_chat.dart';
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:likewallet/support/chat.dart';
import 'package:likewallet/animationPage.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../ContractUS.dart';

class ContactUS extends StatefulWidget {
  _ContactUS createState() => new _ContactUS();
}

class _ContactUS extends State<ContactUS> {
  final TextEditingController name = new TextEditingController();
  final TextEditingController email = new TextEditingController();
  final TextEditingController message = new TextEditingController();

  String url_telegram = 'https://t.me/likewallet';
  String url_line = 'https://line.me/ti/g2/SjIseZ6bt7R7PLAz_jPazQ?utm_source=invitation&utm_medium=link_copy&utm_campaign=default';

  bool show = false;
  bool showThx = false;
  bool closeShowThx = false;
  late BaseAuth auth;
  late String uid;

  final fireStore = FirebaseFirestore.instance;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    auth = new Auth();
  }

  void insertData() async {
    auth.getCurrentUser().then((decodeToken) {
      // print(decodeToken);
      uid = decodeToken!.uid;
      print(uid);

      fireStore.collection('feedback').doc().set({
        'name': name.text,
        'email': email.text,
        'message': message.text,
        'uid': decodeToken.uid,
        'phoneNumber': decodeToken.phoneNumber
      }).then((data) {
        setState(() {
          showThx = true;
          name.text = '';
          email.text = '';
          message.text = '';
          Future.delayed(const Duration(seconds: 2), () {
            setState(() {
              closeShowThx = true;
            });
          });
        });
      });
    });
  }

  void _openURL(url) async {
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      // iOS
      if (await canLaunch(url)) {
        await launch(url);
      } else {
        throw 'Could not launch $url';
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Scaffold(
      body: GestureDetector(
        onTap: () {
          FocusScopeNode currentFocus = FocusScope.of(context);
          if (!currentFocus.hasPrimaryFocus) {
            currentFocus.unfocus();
          }
          setState(() {
            show = false;
          });
        },
        child: Stack(
          children: <Widget>[
            ///สร้างเพื่อกำหนดพื้นที่ทั้งหน้า
            Container(
              height: double.infinity,
              width: double.infinity,
            ),

            ///พื้นหลัง
            bg(),

            /// ภาพพนักงาน
            logo(),

            ///สทนากับเรา

            contact(),

            ///ฟอร์มกรอกข้อมูล feed back  || ปุ่ม Send จะอยู่ใน fromContactUs()
            fromContactUs(),

            ///closeShowThx ใช้การปิดตัว buttonSuccess() เมื่อกดส่งเเล้ว
            if (closeShowThx != true) buttonSuccess(),

            ///ปุ่มกลับหน้าหลัก
            backpage(),
          ],
        ),
      ),
    );
  }

  Widget backpage() {
    return Positioned(
        top: mediaQuery(context, "height", 95),
        left: mediaQuery(context, "width", 35),
        child: backButton(context, LikeWalletAppTheme.gray));
  }

  Widget logo() {
    return Positioned(
      top: mediaQuery(context, "height", 1518),
      left: mediaQuery(context, "width", -321.81),
      child: Image.asset(
        LikeWalletImage.contact_us_logo,
        height: mediaQuery(context, "height", 1002),
        width: mediaQuery(context, "width", 1077.81),
      ),
    );
  }

  Widget bg() {
    return Positioned(
      top: mediaQuery(context, "height", 0),
      child: Image.asset(
        LikeWalletImage.contact_us_bg2,
        fit: BoxFit.cover,
        height: mediaQuery(context, "height", 1002),
        width: mediaQuery(context, "width", 1080),
      ),
    );
  }

  Widget contact() {
    return Positioned(
      top: mediaQuery(context, "height", 184),
      left: mediaQuery(context, "width", 198),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          ///chat support
          Padding(
            padding: EdgeInsets.only(
              bottom: mediaQuery(context, "height", 5),
            ),
            child: GestureDetector(
                onTap: () {
                  /// ย้ายไปไลน์ โอ๋เอ๋
                  // Navigator.push(context, FadeRoute(page: IndexChatPage()));
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) =>
                              ContactUSPage()));
                },
                child: Row(
                  children: <Widget>[
                    _Icon_active(),
                    Container(
                      margin: EdgeInsets.only(
                        right: mediaQuery(context, "width", 30),
                      ),
                      child: Text(
                        AppLocalizations.of(context)!
                            .translate('contact_us_chat'),
                        style: TextStyle(
                          letterSpacing: 0.5,
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font1'),
                          fontWeight: FontWeight.w100,
                          fontSize: mediaQuery(context, 'height', 30),
                          color: LikeWalletAppTheme.white.withOpacity(0.5),
                        ),
                      ),
                    ),
                    Container(
                      child: Image.asset(LikeWalletImage.contact_us_chat),
                      height: mediaQuery(context, "height", 100),
                      width: mediaQuery(context, "height", 100),
                    ),
                  ],
                )),
          ),

          ///telegram
          GestureDetector(
              onTap: () async {
                _openURL(url_telegram);
              },
              child: Row(
                children: <Widget>[
                  _Icon_active(),
                  Container(
                    margin: EdgeInsets.only(
                      right: mediaQuery(context, "width", 30),
                    ),
                    child: Text(
                      AppLocalizations.of(context)!
                            .translate('contact_us_telegram'),
                      style: TextStyle(
                        letterSpacing: 0.5,
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                        fontWeight: FontWeight.w100,
                        fontSize: mediaQuery(context, 'height', 30),
                        color: LikeWalletAppTheme.white.withOpacity(0.5),
                      ),
                    ),
                  ),
                  Container(
                    child: Image.asset(LikeWalletImage.contact_us_telegram),
                    height: mediaQuery(context, "height", 120),
                    width: mediaQuery(context, "height", 120),
                  ),
                ],
              )),

          ///line
          GestureDetector(
            onTap: () async {
              _openURL(url_line);
            },
            child: Row(
              children: <Widget>[
                _Icon_active(),
                Container(
                  margin: EdgeInsets.only(
                    right: mediaQuery(context, "width", 30),
                  ),
                  child: Text(
                    AppLocalizations.of(context)!.translate('contact_us_line'),
                    style: TextStyle(
                      letterSpacing: 0.5,
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      fontWeight: FontWeight.w100,
                      fontSize: mediaQuery(context, 'height', 30),
                      color: LikeWalletAppTheme.white.withOpacity(0.5),
                    ),
                  ),
                ),
                Container(
                  child: Image.asset(LikeWalletImage.contact_us_line),
                  height: mediaQuery(context, "height", 100),
                  width: mediaQuery(context, "height", 100),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// ฟอร์มกรอก
  Widget fromContactUs() {
    return AnimatedPositioned(
      top: show
          ? mediaQuery(context, "height", 0)
          : mediaQuery(context, "height", 560),
      right: mediaQuery(context, "height", 0),
//      color: Colors.red,
      duration: Duration(milliseconds: 200),
      child: Container(
          height: mediaQuery(context, "height", 1574),
          width: mediaQuery(context, "width", 816.37),
          child: Stack(
            children: <Widget>[
              Container(
                height: mediaQuery(context, "height", 1422),
                width: mediaQuery(context, "width", 816.37),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(50.0),
                        bottomLeft: Radius.circular(50.0)),
                    color: LikeWalletAppTheme.white,
                    boxShadow: [
                      BoxShadow(
                        color: LikeWalletAppTheme.black.withOpacity(0.1),
                        spreadRadius: 3,
                        blurRadius: 6,
                        offset: Offset(0, 0), // changes position of shadow
                      ),
                    ]),
                child: Column(children: <Widget>[
                  ///----------| Name |--------
                  Container(
                      padding: EdgeInsets.only(
                          top: mediaQuery(context, "height", 150),
                          left: mediaQuery(context, "width", 150)),
                      child: _TextFormField(
                        context,
                        name,
                        AppLocalizations.of(context)!
                            .translate('contact_us_name'),
                      )),

                  ///----------| Email |--------
                  Container(
                      padding: EdgeInsets.only(
                          top: mediaQuery(context, "height", 80),
                          left: mediaQuery(context, "width", 150)),
                      child: _TextFormField(
                          context,
                          email,
                          AppLocalizations.of(context)!
                            .translate('contact_us_email'))),

                  ///----------| Message |--------
                  Container(
//                    color: Colors.lightBlue,
                    padding: EdgeInsets.only(
                        top: mediaQuery(context, "height", 100),
                        left: mediaQuery(context, "width", 100)),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Container(
                          margin: EdgeInsets.only(
                            top: mediaQuery(context, "height", 10),
                          ),
                          child: _Icon_active(),
                        ),
                        Text(
                          AppLocalizations.of(context)!
                            .translate('contact_us_message'),
                          style: TextStyle(
                              letterSpacing: 1,
                              color: LikeWalletAppTheme.gray.withOpacity(0.8),
                              fontFamily: AppLocalizations.of(context)!
                            .translate('font1'),
                              fontSize: MediaQuery.of(context).size.height *
                                  Screen_util('height', 36),
                              fontWeight: FontWeight.normal),
                        )
                      ],
                    ),
                  ),

                  ///เปลี่ยน  ถ้า showThx = true  จะเเสดงข้อความ Thanks เเทน ช่องกรอกข้อมูล
                  showThx
                      ? Container(
                          alignment: Alignment.topCenter,
                          height: mediaQuery(context, "height", 600),
                          padding: EdgeInsets.only(
                              top: mediaQuery(context, "height", 100),
                              left: mediaQuery(context, "width", 150),
                              right: mediaQuery(context, "width", 100)),
                          child: Text(
                            'Thanks for contacting us. We will get back to you soon!',
                            style: TextStyle(
                                letterSpacing: 0.0,
                                color: LikeWalletAppTheme.gray.withOpacity(1),
                                fontFamily: AppLocalizations.of(context)!
                            .translate('font1'),
                                fontSize: MediaQuery.of(context).size.height *
                                    Screen_util('height', 54),
                                fontWeight: FontWeight.normal),
                          ),
                        )
                      : Container(
                          alignment: Alignment.topCenter,
                          height: mediaQuery(context, "height", 600),
                          padding: EdgeInsets.only(
                            top: mediaQuery(context, "height", 0),
                            left: mediaQuery(context, "width", 150),
                            right: mediaQuery(context, "width", 100),
                          ),
                          child: TextFormField(
                            onTap: () {
                              setState(() {
                                show = true;
                              });
                            },
                            controller: message,
                            validator: (val) =>
                                val!.length < 1 ? 'กรุณาใส่อีเมล' : null,
                            maxLines: 10,
                            style: TextStyle(
                                letterSpacing: 0.5,
                                color: LikeWalletAppTheme.gray.withOpacity(1),
                                fontFamily: AppLocalizations.of(context)!
                            .translate('font1'),
                                fontSize: MediaQuery.of(context).size.height *
                                    Screen_util('height', 45),
                                fontWeight: FontWeight.normal),
                            decoration: InputDecoration(
                              labelText: '',
                              labelStyle: TextStyle(
                                  letterSpacing: 1,
                                  color: LikeWalletAppTheme.gray,
                                  fontFamily: AppLocalizations.of(context)!
                            .translate('font1'),
                                  fontSize: MediaQuery.of(context).size.height *
                                      Screen_util('height', 36),
                                  fontWeight: FontWeight.normal),
                              border: InputBorder.none,
                              focusedBorder: InputBorder.none,
                            ),
                          ),
                        ),
                ]),
              ),

              ///ถ้า showThx = true  ปุ่มเ Send จะถูกปิด
              showThx ? Container() : ButtonSend()
            ],
          )),
    );
  }

  Widget buttonSuccess() {
    return AnimatedPositioned(
        top: showThx
            ? mediaQuery(context, "height", 1650)
            : mediaQuery(context, "height", 1800),
        right: mediaQuery(context, "width", 40),
        height: showThx
            ? mediaQuery(context, "height", 223)
            : mediaQuery(context, "height", 0),
        duration: Duration(milliseconds: 200),
        child: Image.asset(
          LikeWalletImage.contact_us_success,
          height: mediaQuery(context, "height", 223),
          width: mediaQuery(context, "width", 223),
        ));
  }

  Widget ButtonSend() {
    return GestureDetector(
        onTap: () {
          if (name.text != '' && email.text != '' && message.text != '') {
            insertData();
          } else {
            showColoredToast(
                AppLocalizations.of(context)!.translate('contact_us_Toast'));
          }
        },
        child: Align(
            alignment: Alignment.bottomRight,
            child: Container(
                alignment: Alignment.center,
                margin: EdgeInsets.only(
                  right: mediaQuery(context, "width", 30),
                  bottom: mediaQuery(context, "height", 25),
                ),
                height: mediaQuery(context, "height", 254),
                width: mediaQuery(context, "width", 254),
                decoration: BoxDecoration(
                    color: LikeWalletAppTheme.bule2_2,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.5),
                        spreadRadius: 3,
                        blurRadius: 5,
                        offset: Offset(0, 0), // changes position of shadow
                      ),
                    ]),
                child: Text(
                  AppLocalizations.of(context)!
                            .translate('contact_us_button_send'),
                  style: TextStyle(
                      letterSpacing: 1,
                      color: LikeWalletAppTheme.bule1.withOpacity(1),
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      fontSize: MediaQuery.of(context).size.height *
                          Screen_util('height', 40),
                      fontWeight: FontWeight.w200),
                ))));
  }

  _TextFormField(context, controller, labelText) {
    return TextFormField(
      onTap: () {
        setState(() {
          if (showThx != true) show = true;
        });
      },
      onFieldSubmitted: (va) {
        setState(() {
          show = false;
        });
      },
      controller: controller,
      style: TextStyle(
          letterSpacing: 0.5,
          color: LikeWalletAppTheme.gray.withOpacity(1),
          fontFamily: AppLocalizations.of(context)!.translate('font1'),
          fontSize: mediaQuery(context, "height", 45),
          fontWeight: FontWeight.normal),
      readOnly: showThx ? true : false,
      decoration: InputDecoration(
        contentPadding: EdgeInsets.only(
          top: mediaQuery(context, "height", 50),
        ),
        labelText: labelText,
        labelStyle: TextStyle(
            color: LikeWalletAppTheme.gray.withOpacity(0.8),
            fontFamily: AppLocalizations.of(context)!.translate('font1'),
            fontSize: mediaQuery(context, "height", 36),
            fontWeight: FontWeight.normal),
      ),
    );
  }

  _Icon_active() {
    return Container(
      margin: EdgeInsets.only(
        right: mediaQuery(context, "width", 30),
      ),
      height: mediaQuery(context, "height", 30),
      width: mediaQuery(context, "width", 30),
      decoration: BoxDecoration(
        color: LikeWalletAppTheme.bule1,
        shape: BoxShape.circle,
      ),
    );
  }

  void showColoredToast(msg) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_LONG,
        backgroundColor: Colors.red,
        textColor: Colors.white);
  }
}
