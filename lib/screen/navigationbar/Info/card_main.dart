import 'package:flutter/material.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/screen/navigationbar/Info/get_data_info.dart';
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:flutter/foundation.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
// import 'package:pie_chart/pie_chart.dart';
import 'package:likewallet/model/info.dart';
import 'package:intl/intl.dart' as formatIntl;
import 'package:likewallet/libraryman/custom_loading.dart';

class CardMain extends StatefulWidget {
  String? type;
  String? image;
  double? heightImage;
  String? title;
  String? subtitle;
  double? paddingTitle;
  String? phone;
  String? address;

  CardMain({
    this.type,
    this.image,
    this.heightImage,
    this.title,
    this.subtitle,
    this.paddingTitle,
    this.phone,
    this.address,
  });

  @override
  _CardMainState createState() => _CardMainState();
}

enum LegendShape { Circle, Rectangle }

class _CardMainState extends State<CardMain> {
  late Info data;
  final f = new formatIntl.NumberFormat("###,###.##");
  late bool _loading = false;
  double totalData = 0;
  double likewalletData = 0;
  double ldxData = 0;
  double otherData = 0;
  double pkgData = 0;

  Map<String, double> dataMap = {
    "LikeWallet": 0,
    "LDX": 0,
    "Others": 0,
    "4C - PKG": 0,
  };

  @override
  void initState() {
    // TODO: implement initState
    if (widget.phone != '' || widget.address != "") {
      if (widget.type == 'total') {
        print('total');
        _loading = true;
        getTotal(widget.address, widget.phone).then((value) {
          setState(() {
            _loading = false;
            data = value;
            totalData = value.total.toDouble();
            likewalletData = value.likewallet.toDouble();
            ldxData = value.ldx.toDouble();
            otherData = value.other.toDouble();
            pkgData = value.pkg.toDouble();
            dataMap = {
              "LikeWallet": likewalletData.toDouble(),
              "LDX": ldxData.toDouble(),
              "Others": otherData.toDouble(),
              "4C - PKG": pkgData.toDouble()
            };
          });
        });
      }
      if (widget.type == 'today') {
        _loading = true;
        getToday(widget.address, widget.phone).then((value) {
          setState(() {
            _loading = false;
            this.data = value;
            this.totalData = value.total.toDouble();
            this.likewalletData = value.likewallet.toDouble();
            this.ldxData = value.ldx.toDouble();
            this.otherData = value.other.toDouble();
            this.pkgData = value.pkg.toDouble();
            this.dataMap = {
              "LikeWallet": likewalletData.toDouble(),
              "LDX": ldxData.toDouble(),
              "Others": otherData.toDouble(),
              "4C - PKG": pkgData.toDouble()
            };
          });
        });
      }
      if (widget.type == 'week') {
        _loading = true;
        getWeek(widget.address, widget.phone).then((value) {
          setState(() {
            _loading = false;
            data = value;
            totalData = value.total.toDouble();
            likewalletData = value.likewallet.toDouble();
            ldxData = value.ldx.toDouble();
            otherData = value.other.toDouble();
            pkgData = value.pkg.toDouble();
            dataMap = {
              "LikeWallet": likewalletData.toDouble(),
              "LDX": ldxData.toDouble(),
              "Others": otherData.toDouble(),
              "4C - PKG": pkgData.toDouble()
            };
          });
        });
      }
      if (widget.type == 'month') {
        _loading = true;
        getMonth(widget.address, widget.phone).then((value) {
          setState(() {
            _loading = false;
            data = value;
            totalData = value.total.toDouble();
            likewalletData = value.likewallet.toDouble();
            ldxData = value.ldx.toDouble();
            otherData = value.other.toDouble();
            pkgData = value.pkg.toDouble();
            dataMap = {
              "LikeWallet": likewalletData.toDouble(),
              "LDX": ldxData.toDouble(),
              "Others": otherData.toDouble(),
              "4C - PKG": pkgData.toDouble()
            };
          });
        });
      }
    } else {
      print('ค่าไม่มา');
    }
  }

  List<Color> colorList = [
    Color(0xFF9443FF),
    Color(0xFF633EFD),
    Color(0xFFBC51FD),
    Color(0xFFC873FC),
  ];

  // ChartType _chartType = ChartType.disc;
  double _ringStrokeWidth = 32;

  int key = 0;
  @override
  Widget build(BuildContext context) {
    return ModalProgressHUD(
      inAsyncCall: _loading,
      progressIndicator: CustomLoading(),
      opacity: 0.1,
      child: Container(
        color: Color(0xff1E2037),
        child: Stack(
          children: [
            Positioned(
                left: 0,
                child: Image.asset(
                  widget.image.toString(),
                  height: mediaQuery(context, 'height', widget.heightImage!.toDouble()),
                )),
            Positioned(
                top: mediaQuery(context, 'height', 379) -
                    mediaQuery(context, 'height', 314.0),
                left: mediaQuery(context, 'width', widget.paddingTitle!.toDouble()),
                child: Column(
                  children: [
                    Text.rich(
                      TextSpan(
                        style: TextStyle(
                          fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                          fontSize: mediaQuery(context, 'height', 36),
                          color: const Color(0xff52ffff),
                          letterSpacing: mediaQuery(context, 'width', 3.6),
                          height: 0,
                        ),
                        children: [
                          TextSpan(
                            text: widget.title.toString() + '\n',
                            style: TextStyle(
                              fontWeight: FontWeight.w300,
                            ),
                          ),
                          TextSpan(
                            text: widget.subtitle,
                            style: TextStyle(
                              color: const Color(0xff5355f7),
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                      textAlign: TextAlign.left,
                    )
                  ],
                )),
            Positioned(
              top: mediaQuery(context, 'height', 903.0) -
                  mediaQuery(context, 'height', 314.0),
              right: 0,
              child: Column(
                children: [
                  total(),
                  height10(),
                  likeWallet(),
                  height10(),
                  ldx(),
                  height10(),
                  others(),
                  height10(),
                  pkgData == 0 ? Container() : four_c_pkg()
                ],
              ),
            ),
            Positioned(
              top: mediaQuery(context, 'height', 627) -
                  mediaQuery(context, 'height', 314),
              right: mediaQuery(context, 'width', 209),
              child: pieChart(),
            )
          ],
        ),
      ),
    );
  }

  Widget height10() {
    return SizedBox(
      height: mediaQuery(context, 'height', 10),
    );
  }

  Widget pieChart() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(50.0),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.16),
            offset: Offset(0, 20),
            blurRadius: 35,
          ),
        ],
      ),
      // child: PieChart(
      //   key: ValueKey(key),
      //   dataMap: dataMap,
      //   animationDuration: Duration(milliseconds: 800),
      //   // chartLegendSpacing: _chartLegendSpacing,
      //   chartRadius: MediaQuery.of(context).size.width / 3.2 > 300
      //       ? 300
      //       : MediaQuery.of(context).size.width / 3.2,
      //   colorList: colorList,
      //   initialAngleInDegree: 0,
      //   chartType: _chartType,
      //   centerText: false ? "HYBRID" : "",
      //   legendOptions: LegendOptions(
      //     // showLegendsInRow: false,
      //     // legendPosition: _legendPosition,
      //     showLegends: false,
      //     // legendShape: _legendShape == LegendShape.Circle
      //     //     ? BoxShape.circle
      //     //     : BoxShape.rectangle,
      //     // legendTextStyle: TextStyle(
      //     //   fontWeight: FontWeight.bold,
      //     // ),
      //   ),
      //   chartValuesOptions: ChartValuesOptions(
      //       showChartValueBackground: false,
      //       showChartValues: true,
      //       chartValueStyle: TextStyle(
      //         fontSize: mediaQuery(context, 'height', 31),
      //         fontFamily: AppLocalizations.of(context)!.translate('font1'),
      //         fontWeight: FontWeight.bold,
      //         color: Colors.white,
      //       ),
      //       showChartValuesInPercentage: true,
      //       showChartValuesOutside: true,
      //       decimalPlaces: 2),
      //   ringStrokeWidth: _ringStrokeWidth,
      //   // emptyColor: Colors.grey,
      // ),
    );
  }

  Widget body(title, detail, number) {
    print(number);
    return Column(
      children: [
        Expanded(child: Container()),
        Padding(
          padding: EdgeInsets.only(
            left: mediaQuery(context, 'width', 52),
            right: mediaQuery(context, 'width', 83),
            bottom: mediaQuery(context, 'height', 42),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      color: const Color(0xff52FFFF),
                      fontWeight: FontWeight.w600,
                      letterSpacing: 1,
                      fontFamily:
                      AppLocalizations.of(context)!.translate('font1'),
                      fontSize: mediaQuery(context, 'height', 45),
                    ),
                  ),
                  SizedBox(
                    height: mediaQuery(context, 'height', 10),
                  ),
                  Text(
                    detail,
                    style: TextStyle(
                      color: const Color(0xff52FFFF).withOpacity(0.6),
                      fontWeight: FontWeight.normal,
                      fontFamily:
                      AppLocalizations.of(context)!.translate('font1'),
                      fontSize: mediaQuery(context, 'height', 32),
                      height: 1,
                    ),
                  ),
                ],
              ),
              Text(
                number == null ? 0.toString() : f.format(number).toString(),
                style: TextStyle(
                  color: const Color(0xff52FFFF),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget total() {
    return Container(
        alignment: Alignment.bottomCenter,
        padding: EdgeInsets.only(bottom: 0),
        width: mediaQuery(context, 'width', 825.0),
        height: mediaQuery(context, 'height', 454.0),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment(-1.0, -0.15),
            end: Alignment(1.0, -0.14),
            colors: [
              const Color(0xff1e2037),
              const Color(0xff273352),
              const Color(0xff000000)
            ],
            stops: [0.0, 1.0, 1.0],
          ),
        ),
        child: body(
            'Total',
            AppLocalizations.of(context)!.translate('info_total_subtitle'),
            totalData));
  }

  Widget likeWallet() {
    return Container(
        width: mediaQuery(context, 'width', 825.0),
        height: mediaQuery(context, 'height', 214.0),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment(-1.0, -0.15),
            end: Alignment(1.0, -0.14),
            colors: [
              const Color(0xff1e2037),
              const Color(0xff273352),
              const Color(0xff000000)
            ],
            stops: [0.0, 1.0, 1.0],
          ),
        ),
        child: body(
            'LikeWallet',
            AppLocalizations.of(context)!.translate('info_likewallet_subtitle'),
            likewalletData));
  }

  Widget ldx() {
    return Container(
      width: mediaQuery(context, 'width', 825.0),
      height: mediaQuery(context, 'height', 214.0),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment(-1.0, -0.15),
          end: Alignment(1.0, -0.14),
          colors: [
            const Color(0xff1e2037),
            const Color(0xff273352),
            const Color(0xff000000)
          ],
          stops: [0.0, 1.0, 1.0],
        ),
      ),
      child: body('LDX',
          AppLocalizations.of(context)!.translate('info_ldx_subtitle'), ldxData),
    );
  }

  Widget others() {
    return Container(
      width: mediaQuery(context, 'width', 825.0),
      height: mediaQuery(context, 'height', 214.0),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment(-1.0, -0.15),
          end: Alignment(1.0, -0.14),
          colors: [
            const Color(0xff1e2037),
            const Color(0xff273352),
            const Color(0xff000000)
          ],
          stops: [0.0, 1.0, 1.0],
        ),
      ),
      child: body(
          'Others',
          AppLocalizations.of(context)!.translate('info_others_subtitle'),
          otherData),
    );
  }

  Widget four_c_pkg() {
    return Container(
      width: mediaQuery(context, 'width', 825.0),
      height: mediaQuery(context, 'height', 214.0),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment(-1.0, -0.15),
          end: Alignment(1.0, -0.14),
          colors: [
            const Color(0xff1e2037),
            const Color(0xff273352),
            const Color(0xff000000)
          ],
          stops: [0.0, 1.0, 1.0],
        ),
      ),
      child: body(
          '4C - PKG',
          AppLocalizations.of(context)!.translate('info_four_c_pkg_subtitle'),
          pkgData),
    );
  }
}