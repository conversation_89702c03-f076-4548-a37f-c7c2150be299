import 'dart:convert';

import 'package:http/http.dart' as http;
import 'package:likewallet/app_config.dart';
import 'package:likewallet/model/info.dart';

Future<Info> getTotal(String? address, String? phoneNumber) async {
  try {
    var url = Uri.https(env.PortfolioUrl, '/getPortfolio');
    final response = await http.post(url, body: {
      "time": "TOTAL",
      "address": address,
      "phoneNumber": phoneNumber
    });
    if (response.statusCode == 200) {
      final body = json.decode(response.body);
      var total = Info.fromJson(body['result']);
      return Future.value(total);
    }
  } catch (e) {
    print('เกิดข้อผิดพลาด');
    return Future.value();
  }

  return Future.value();
}

Future<Info> getToday(address, phoneNumber) async {
  try {

    var url = Uri.https(env.PortfolioUrl, '/getPortfolio');
    final response = await http.post(url,
        body: {"time": "D", "address": address, "phoneNumber": phoneNumber});
    if (response.statusCode == 200) {
      final body = json.decode(response.body);
      var today = Info.fromJson(body['result']);
      print(body);
      return today;
    }
  } catch (e) {
    print('เกิดข้อผิดพลาด');
    return Future.value();
  }
  return Future.value();
}

Future<Info> getWeek(address, phoneNumber) async {
  try {
    var url = Uri.https(env.PortfolioUrl, '/getPortfolio');
    final response = await http.post(url,
        body: {"time": "W", "address": address, "phoneNumber": phoneNumber});
    if (response.statusCode == 200) {
      final body = json.decode(response.body);
      var week = Info.fromJson(body['result']);
      print(body);
      return week;
    }
  } catch (e) {
    print('เกิดข้อผิดพลาด');
    return Future.value();
  }
  return Future.value();
}

Future<Info> getMonth(address, phoneNumber) async {
  try {
    var url = Uri.https(env.PortfolioUrl, '/getPortfolio');
    final response = await http.post(url,
        body: {"time": "M", "address": address, "phoneNumber": phoneNumber});
    if (response.statusCode == 200) {
      final body = json.decode(response.body);
      var month = Info.fromJson(body['result']);
      print(body);
      return month;
    }
  } catch (e) {
    print('เกิดข้อผิดพลาด');
    return Future.value();
  }
  return Future.value();
}
