import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:likewallet/screen/navigationbar/Info/card_main.dart';
import 'package:likewallet/screen/home.dart';
import 'package:likewallet/screen/navigationbar/home/<USER>';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/screen/tabslide.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/main.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/libraryman/configuration_service.dart';
import 'package:likewallet/libraryman/auth.dart';

class InfoScreen extends StatefulWidget {
  InfoScreen({this.phone, this.address});
  final String? address;
  final String? phone;
  @override
  _InfoScreenState createState() => _InfoScreenState();
}

class _InfoScreenState extends State<InfoScreen> {
  GlobalKey<ScaffoldState> _scaffoldKey = new GlobalKey();

  int pageNumber = 0;
  late BaseAuth auth;
  late IConfigurationService configETH;
  // String phone;
  // String address;

  late PageController _controller;
  late double _pageValue;

  @override
  void initState() {
    // TODO: implement initState
    auth = new Auth();

    _controller =
        PageController(viewportFraction: 1.0, initialPage: pageNumber);
    _controller.addListener(() {
      setState(() => _pageValue = _controller.page!);
    });
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
    _controller.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // ScreenUtil.init(context, designSize: Size(1080, 2340), allowFontScaling: false);

    return Scaffold(
      key: _scaffoldKey,
      drawer: tabslide(),
      body: Consumer(builder: (context, watch, _) {
        return DefaultTabController(
          length: 4,
          child: Stack(
            children: [
              groupBody(),
              Column(
                children: [
                  headInfo(),
                  infoBody(),
                ],
              )
            ],
          ),
        );
      }),
    );
  }

  Widget head() {
    return Container(
      height: mediaQuery(context, 'height', 521),
      child: Column(
        children: [
          SizedBox(
            height: mediaQuery(context, 'height', 121.5),
          ),
          Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: <Widget>[
                Expanded(
                  child: Container(
                      width: mediaQuery(context, "width", 1080) / 3,
                      padding: EdgeInsets.only(
                        top: mediaQuery(context, 'height', 15),
                        left: mediaQuery(context, "width", 75),
                      ),
                      height: mediaQuery(context, "height", 100),
                      alignment: Alignment.topLeft,
                      child: InkWell(
                          child: Container(
                            height: mediaQuery(context, "height", 100),
                            alignment: Alignment.topLeft,
                            child: Image.asset(
                              LikeWalletImage.icon_menu,
                              height: mediaQuery(context, "height", 34.07),
                              width: mediaQuery(context, "width", 55.74),
                            ),
                          ),
                          onTap: () {
                            _scaffoldKey.currentState!.openDrawer();
                          })),
                ),
                InkWell(
                  onTap: () {
                    Navigator.of(context).pop();
                  },
                  child: Container(
                    padding:
                        EdgeInsets.only(top: mediaQuery(context, 'height', 15)),
                    width: mediaQuery(context, "width", 1080) / 3,
                    alignment: Alignment.topCenter,
                    height: mediaQuery(context, "height", 100),
                    child: SvgPicture.asset(
                      LikeWalletImage.icon_more,
                      fit: BoxFit.contain,
                      color: LikeWalletAppTheme.black.withOpacity(0.6),
                      height: mediaQuery(context, 'height', 11),
                    ),
                  ),
                ),
                Expanded(child: Container()),
              ]),
          Expanded(child: Container()),
          // Container(
          //   alignment: Alignment.centerLeft,
          //   height: mediaQuery(context, 'height', 250),
          //   child: HomeHeadScreen(seed: seed),
          // ),
        ],
      ),
    );
  }

  Widget headInfo() {
    return Consumer(builder: (context, watch, _) {
      return Container(
        height: mediaQuery(context, 'height', 314),
        child: Column(
          children: [
            SizedBox(
              height: mediaQuery(context, 'height', 121.5),
            ),
            Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: <Widget>[
                  Expanded(
                    child: Container(
                      width: mediaQuery(context, "width", 1080) / 3,
                    ),
                  ),
                  SizedBox(
                    height: mediaQuery(context, 'height', 60.4),
                    child: TextButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        child: Image.asset(
                          LikeWalletImage.button_back,
                          height: mediaQuery(context, 'height', 40.4),
                        )),
                  ),
                  Expanded(child: Container()),
                ]),
            Expanded(child: Container()),
            Container(
              alignment: Alignment.centerLeft,
              // height: mediaQuery(context, 'height', 90),
              child: Column(
                children: [
                  Container(
                      padding: EdgeInsets.only(
                          left: mediaQuery(context, 'width', 75),
                          right: mediaQuery(context, 'width', 75)),
                      alignment: Alignment.centerLeft,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Container(
                              // height: mediaQuery(context, 'height', 80),
                              child: textHead('info_subtitle_total', 0)),
                          Container(
                              // height: mediaQuery(context, 'height', 80),
                              child: textHead('info_subtitle_today', 1)),
                          Container(
                              // height: mediaQuery(context, 'height', 80),
                              child: textHead('info_subtitle_week', 2)),
                          Container(
                              // height: mediaQuery(context, 'height', 80),
                              child: textHead('info_subtitle_month', 3)),
                        ],
                      )),
                  SizedBox(
                    height: 20.h,
                  )
                ],
              ),
            ),
          ],
        ),
      );
    });
  }

  Widget infoBody() {
    return Expanded(
      child: PageView(
        scrollDirection: Axis.vertical,
        controller: _controller,
        onPageChanged: (index) {
          setState(() {
            pageNumber = index;
          });
        },
        children: <Widget>[
          CardMain(
            type: 'total',
            phone: widget.phone,
            address: widget.address,
            image: LikeWalletImage.info_total,
            heightImage: 1825,
            title: AppLocalizations.of(context)!.translate('info_title'),
            subtitle:
                AppLocalizations.of(context)!.translate('info_subtitle_total'),
            paddingTitle: 296,
          ),
          CardMain(
            type: 'today',
            phone: widget.phone,
            address: widget.address,
            image: LikeWalletImage.info_today,
            heightImage: 1641,
            title: AppLocalizations.of(context)!.translate('info_title'),
            subtitle:
                AppLocalizations.of(context)!.translate('info_subtitle_today'),
            paddingTitle: 410,
          ),
          CardMain(
            type: 'week',
            phone: widget.phone,
            address: widget.address,
            image: LikeWalletImage.info_week,
            heightImage: 1668,
            title: AppLocalizations.of(context)!.translate('info_title'),
            subtitle:
                AppLocalizations.of(context)!.translate('info_subtitle_week'),
            paddingTitle: 437,
          ),
          CardMain(
            type: 'month',
            phone: widget.phone,
            address: widget.address,
            image: LikeWalletImage.info_month,
            heightImage: 1970,
            title: AppLocalizations.of(context)!.translate('info_title'),
            subtitle:
                AppLocalizations.of(context)!.translate('info_subtitle_month'),
            paddingTitle: 439,
          ),
        ],
      ),
    );
  }

  Widget bg() {
    return Stack(
      children: [
        Container(
          height: mediaQuery(context, 'height', 1429.0),
          width: mediaQuery(context, 'width', 1080.0),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment(0.0, -1.0),
                end: Alignment(0.0, 1.0),
                colors: [const Color(0xff1f2840), const Color(0xff1e1d34)],
                stops: [0.0, 1.0],
              ),
              boxShadow: [
                BoxShadow(
                  color: const Color(0x33000000),
                  offset: Offset(-9, 0),
                  blurRadius: 25,
                ),
              ],
            ),
          ),
        ),
        Container(
          width: mediaQuery(context, 'width', 1080.0),
          height: mediaQuery(context, 'height', 1429.0),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment(0.0, -1.0),
                end: Alignment(0.0, 1.0),
                colors: [const Color(0xff212b44), const Color(0xff1e1d34)],
                stops: [0.0, 1.0],
              ),
              boxShadow: [
                BoxShadow(
                  color: const Color(0x40000000),
                  offset: Offset(-9, 0),
                  blurRadius: 25,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget groupBody() {
    return Consumer(builder: (context, watch, _) {
      return Stack(
        children: [
          Column(
            children: [
              AnimatedContainer(
                height: mediaQuery(context, 'height', 314),
                duration: Duration(milliseconds: 300),
                width: MediaQuery.of(context).size.width,
                padding: EdgeInsets.only(
                  top: mediaQuery(context, 'height', 121.5),
                ),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment(-1.0, -0.94),
                    end: Alignment(1.0, 1.0),
                    colors: [
                      const Color(0xff52fcf0),
                      const Color(0xff33faec),
                      const Color(0xff22c4e6)
                    ],
                    stops: [0.0, 0.335, 1.0],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0x29000000),
                      offset: Offset(0, 12),
                      blurRadius: 25,
                    ),
                  ],
                ),
              ),
              Expanded(child: Container(color: Color(0xff1E2037))),
            ],
          ),
        ],
      );
    });
  }

  Widget textHead(text, page) {
    return InkWell(
      onTap: () {
        setState(() => pageNumber = page);
        _controller.animateToPage(
          page,
          duration: Duration(milliseconds: 400),
          curve: Curves.easeIn,
        );
      },
      child: Text(
        AppLocalizations.of(context)!.translate(text),
        textAlign: TextAlign.center,
        style: pageNumber == page ? selected(context) : unselected(context),
      ),
    );
  }

  static unselected(context) {
    return TextStyle(
        letterSpacing: 0.3,
        fontFamily: AppLocalizations.of(context)!.translate('font1'),
        fontWeight: FontWeight.normal,
        shadows: [
          Shadow(
            blurRadius: 6.h,
            color: LikeWalletAppTheme.black.withOpacity(0.16),
            offset: Offset(0.0, 3.h),
          ),
        ],
        color: Colors.black.withOpacity(0.4),
        fontSize: 39.sp);
  }

  static selected(context) {
    return TextStyle(
        letterSpacing: 0.3,
        fontFamily: AppLocalizations.of(context)!.translate('font1'),
        fontWeight: FontWeight.w700,
        shadows: [
          Shadow(
            blurRadius: 6.h,
            color: LikeWalletAppTheme.black.withOpacity(0.16),
            offset: Offset(0.0, 3.h),
          ),
        ],
        color: Colors.black,
        fontSize: 47.sp);
  }
}
