// import 'package:flutter/material.dart';
// import 'package:adobe_xd/pinned.dart';
//
// Widget bg() {
//   return SizedBox(
//     width: 749.0,
//     height: 1197.0,
//     child: Stack(
//       children: <Widget>[
//         Pinned.fromSize(
//           bounds: Rect.fromLTWH(0.0, 0.0, 749.2, 1197.1),
//           size: Size(749.2, 1197.1),
//           pinLeft: true,
//           pinRight: true,
//           pinTop: true,
//           pinBottom: true,
//           child: Stack(
//             children: <Widget>[
//               Pinned.fromSize(
//                 bounds: Rect.fromLTWH(0.0, 0.0, 749.2, 749.2),
//                 size: Size(749.2, 1197.1),
//                 pinLeft: true,
//                 pinRight: true,
//                 pinTop: true,
//                 fixedHeight: true,
//                 child: Stack(
//                   children: <Widget>[
//                     Pinned.fromSize(
//                       bounds: Rect.fromLTWH(109.7, 109.7, 529.7, 529.7),
//                       size: Size(749.2, 749.2),
//                       pinLeft: true,
//                       pinRight: true,
//                       pinTop: true,
//                       pinBottom: true,
//                       child: Transform.rotate(
//                         angle: -0.7854,
//                         child: Container(
//                           decoration: BoxDecoration(
//                             borderRadius: BorderRadius.all(
//                                 Radius.elliptical(9999.0, 9999.0)),
//                             border: Border.all(
//                                 width: 0.81, color: const Color(0xb22ffaed)),
//                           ),
//                         ),
//                       ),
//                     ),
//                     Pinned.fromSize(
//                       bounds: Rect.fromLTWH(129.0, 129.0, 491.1, 491.1),
//                       size: Size(749.2, 749.2),
//                       pinLeft: true,
//                       pinRight: true,
//                       pinTop: true,
//                       pinBottom: true,
//                       child: Transform.rotate(
//                         angle: -0.7854,
//                         child: Container(
//                           decoration: BoxDecoration(
//                             borderRadius: BorderRadius.all(
//                                 Radius.elliptical(9999.0, 9999.0)),
//                             border: Border.all(
//                                 width: 0.95, color: const Color(0xb22ffaed)),
//                           ),
//                         ),
//                       ),
//                     ),
//                     Pinned.fromSize(
//                       bounds: Rect.fromLTWH(148.3, 148.3, 452.5, 452.5),
//                       size: Size(749.2, 749.2),
//                       pinLeft: true,
//                       pinRight: true,
//                       pinTop: true,
//                       pinBottom: true,
//                       child: Transform.rotate(
//                         angle: -0.7854,
//                         child: Container(
//                           decoration: BoxDecoration(
//                             borderRadius: BorderRadius.all(
//                                 Radius.elliptical(9999.0, 9999.0)),
//                             border: Border.all(
//                                 width: 1.09, color: const Color(0xb22ffaed)),
//                           ),
//                         ),
//                       ),
//                     ),
//                     Pinned.fromSize(
//                       bounds: Rect.fromLTWH(167.7, 167.7, 413.9, 413.9),
//                       size: Size(749.2, 749.2),
//                       pinLeft: true,
//                       pinRight: true,
//                       pinTop: true,
//                       pinBottom: true,
//                       child: Transform.rotate(
//                         angle: -0.7854,
//                         child: Container(
//                           decoration: BoxDecoration(
//                             borderRadius: BorderRadius.all(
//                                 Radius.elliptical(9999.0, 9999.0)),
//                             border: Border.all(
//                                 width: 1.22, color: const Color(0xb22ffaed)),
//                           ),
//                         ),
//                       ),
//                     ),
//                     Pinned.fromSize(
//                       bounds: Rect.fromLTWH(187.0, 187.0, 375.2, 375.2),
//                       size: Size(749.2, 749.2),
//                       pinLeft: true,
//                       pinRight: true,
//                       pinTop: true,
//                       pinBottom: true,
//                       child: Transform.rotate(
//                         angle: -0.7854,
//                         child: Container(
//                           decoration: BoxDecoration(
//                             borderRadius: BorderRadius.all(
//                                 Radius.elliptical(9999.0, 9999.0)),
//                             border: Border.all(
//                                 width: 1.36, color: const Color(0xb22ffaed)),
//                           ),
//                         ),
//                       ),
//                     ),
//                     Pinned.fromSize(
//                       bounds: Rect.fromLTWH(206.3, 206.3, 336.6, 336.6),
//                       size: Size(749.2, 749.2),
//                       pinLeft: true,
//                       pinRight: true,
//                       pinTop: true,
//                       pinBottom: true,
//                       child: Transform.rotate(
//                         angle: -0.7854,
//                         child: Container(
//                           decoration: BoxDecoration(
//                             borderRadius: BorderRadius.all(
//                                 Radius.elliptical(9999.0, 9999.0)),
//                             border: Border.all(
//                                 width: 1.5, color: const Color(0xb22ffaed)),
//                           ),
//                         ),
//                       ),
//                     ),
//                     Pinned.fromSize(
//                       bounds: Rect.fromLTWH(225.6, 225.6, 298.0, 298.0),
//                       size: Size(749.2, 749.2),
//                       pinLeft: true,
//                       pinRight: true,
//                       pinTop: true,
//                       pinBottom: true,
//                       child: Transform.rotate(
//                         angle: -0.7854,
//                         child: Container(
//                           decoration: BoxDecoration(
//                             borderRadius: BorderRadius.all(
//                                 Radius.elliptical(9999.0, 9999.0)),
//                             border: Border.all(
//                                 width: 1.63, color: const Color(0xb22ffaed)),
//                           ),
//                         ),
//                       ),
//                     ),
//                     Pinned.fromSize(
//                       bounds: Rect.fromLTWH(244.9, 244.9, 259.3, 259.3),
//                       size: Size(749.2, 749.2),
//                       fixedWidth: true,
//                       fixedHeight: true,
//                       child: Transform.rotate(
//                         angle: -0.7854,
//                         child: Container(
//                           decoration: BoxDecoration(
//                             borderRadius: BorderRadius.all(
//                                 Radius.elliptical(9999.0, 9999.0)),
//                             border: Border.all(
//                                 width: 1.77, color: const Color(0xb22ffaed)),
//                           ),
//                         ),
//                       ),
//                     ),
//                     Pinned.fromSize(
//                       bounds: Rect.fromLTWH(264.2, 264.2, 220.7, 220.7),
//                       size: Size(749.2, 749.2),
//                       fixedWidth: true,
//                       fixedHeight: true,
//                       child: Container(
//                         decoration: BoxDecoration(
//                           borderRadius: BorderRadius.all(
//                               Radius.elliptical(9999.0, 9999.0)),
//                           border: Border.all(
//                               width: 1.91, color: const Color(0xb22ffaed)),
//                         ),
//                       ),
//                     ),
//                     Pinned.fromSize(
//                       bounds: Rect.fromLTWH(283.5, 283.5, 182.1, 182.1),
//                       size: Size(749.2, 749.2),
//                       fixedWidth: true,
//                       fixedHeight: true,
//                       child: Container(
//                         decoration: BoxDecoration(
//                           borderRadius: BorderRadius.all(
//                               Radius.elliptical(9999.0, 9999.0)),
//                           border: Border.all(
//                               width: 2.04, color: const Color(0xb22ffaed)),
//                         ),
//                       ),
//                     ),
//                     Pinned.fromSize(
//                       bounds: Rect.fromLTWH(302.9, 302.9, 143.5, 143.5),
//                       size: Size(749.2, 749.2),
//                       fixedWidth: true,
//                       fixedHeight: true,
//                       child: Container(
//                         decoration: BoxDecoration(
//                           borderRadius: BorderRadius.all(
//                               Radius.elliptical(9999.0, 9999.0)),
//                           border: Border.all(
//                               width: 2.18, color: const Color(0xb22ffaed)),
//                         ),
//                       ),
//                     ),
//                     Pinned.fromSize(
//                       bounds: Rect.fromLTWH(322.2, 322.2, 104.8, 104.8),
//                       size: Size(749.2, 749.2),
//                       fixedWidth: true,
//                       fixedHeight: true,
//                       child: Container(
//                         decoration: BoxDecoration(
//                           borderRadius: BorderRadius.all(
//                               Radius.elliptical(9999.0, 9999.0)),
//                           border: Border.all(
//                               width: 2.32, color: const Color(0xb22ffaed)),
//                         ),
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//               Pinned.fromSize(
//                 bounds: Rect.fromLTWH(0.0, 161.6, 749.2, 749.2),
//                 size: Size(749.2, 1197.1),
//                 pinLeft: true,
//                 pinRight: true,
//                 pinTop: true,
//                 fixedHeight: true,
//                 child: Stack(
//                   children: <Widget>[
//                     Pinned.fromSize(
//                       bounds: Rect.fromLTWH(109.7, 109.7, 529.7, 529.7),
//                       size: Size(749.2, 749.2),
//                       pinLeft: true,
//                       pinRight: true,
//                       pinTop: true,
//                       pinBottom: true,
//                       child: Transform.rotate(
//                         angle: -0.7854,
//                         child: Container(
//                           decoration: BoxDecoration(
//                             borderRadius: BorderRadius.all(
//                                 Radius.elliptical(9999.0, 9999.0)),
//                             border: Border.all(
//                                 width: 0.81, color: const Color(0x692ffaed)),
//                           ),
//                         ),
//                       ),
//                     ),
//                     Pinned.fromSize(
//                       bounds: Rect.fromLTWH(129.0, 129.0, 491.1, 491.1),
//                       size: Size(749.2, 749.2),
//                       pinLeft: true,
//                       pinRight: true,
//                       pinTop: true,
//                       pinBottom: true,
//                       child: Transform.rotate(
//                         angle: -0.7854,
//                         child: Container(
//                           decoration: BoxDecoration(
//                             borderRadius: BorderRadius.all(
//                                 Radius.elliptical(9999.0, 9999.0)),
//                             border: Border.all(
//                                 width: 0.95, color: const Color(0x692ffaed)),
//                           ),
//                         ),
//                       ),
//                     ),
//                     Pinned.fromSize(
//                       bounds: Rect.fromLTWH(148.3, 148.3, 452.5, 452.5),
//                       size: Size(749.2, 749.2),
//                       pinLeft: true,
//                       pinRight: true,
//                       pinTop: true,
//                       pinBottom: true,
//                       child: Transform.rotate(
//                         angle: -0.7854,
//                         child: Container(
//                           decoration: BoxDecoration(
//                             borderRadius: BorderRadius.all(
//                                 Radius.elliptical(9999.0, 9999.0)),
//                             border: Border.all(
//                                 width: 1.09, color: const Color(0x692ffaed)),
//                           ),
//                         ),
//                       ),
//                     ),
//                     Pinned.fromSize(
//                       bounds: Rect.fromLTWH(167.7, 167.7, 413.9, 413.9),
//                       size: Size(749.2, 749.2),
//                       pinLeft: true,
//                       pinRight: true,
//                       pinTop: true,
//                       pinBottom: true,
//                       child: Transform.rotate(
//                         angle: -0.7854,
//                         child: Container(
//                           decoration: BoxDecoration(
//                             borderRadius: BorderRadius.all(
//                                 Radius.elliptical(9999.0, 9999.0)),
//                             border: Border.all(
//                                 width: 1.22, color: const Color(0x692ffaed)),
//                           ),
//                         ),
//                       ),
//                     ),
//                     Pinned.fromSize(
//                       bounds: Rect.fromLTWH(187.0, 187.0, 375.2, 375.2),
//                       size: Size(749.2, 749.2),
//                       pinLeft: true,
//                       pinRight: true,
//                       pinTop: true,
//                       pinBottom: true,
//                       child: Transform.rotate(
//                         angle: -0.7854,
//                         child: Container(
//                           decoration: BoxDecoration(
//                             borderRadius: BorderRadius.all(
//                                 Radius.elliptical(9999.0, 9999.0)),
//                             border: Border.all(
//                                 width: 1.36, color: const Color(0x692ffaed)),
//                           ),
//                         ),
//                       ),
//                     ),
//                     Pinned.fromSize(
//                       bounds: Rect.fromLTWH(206.3, 206.3, 336.6, 336.6),
//                       size: Size(749.2, 749.2),
//                       pinLeft: true,
//                       pinRight: true,
//                       pinTop: true,
//                       pinBottom: true,
//                       child: Transform.rotate(
//                         angle: -0.7854,
//                         child: Container(
//                           decoration: BoxDecoration(
//                             borderRadius: BorderRadius.all(
//                                 Radius.elliptical(9999.0, 9999.0)),
//                             border: Border.all(
//                                 width: 1.5, color: const Color(0x692ffaed)),
//                           ),
//                         ),
//                       ),
//                     ),
//                     Pinned.fromSize(
//                       bounds: Rect.fromLTWH(225.6, 225.6, 298.0, 298.0),
//                       size: Size(749.2, 749.2),
//                       pinLeft: true,
//                       pinRight: true,
//                       pinTop: true,
//                       pinBottom: true,
//                       child: Transform.rotate(
//                         angle: -0.7854,
//                         child: Container(
//                           decoration: BoxDecoration(
//                             borderRadius: BorderRadius.all(
//                                 Radius.elliptical(9999.0, 9999.0)),
//                             border: Border.all(
//                                 width: 1.63, color: const Color(0x692ffaed)),
//                           ),
//                         ),
//                       ),
//                     ),
//                     Pinned.fromSize(
//                       bounds: Rect.fromLTWH(244.9, 244.9, 259.3, 259.3),
//                       size: Size(749.2, 749.2),
//                       fixedWidth: true,
//                       fixedHeight: true,
//                       child: Transform.rotate(
//                         angle: -0.7854,
//                         child: Container(
//                           decoration: BoxDecoration(
//                             borderRadius: BorderRadius.all(
//                                 Radius.elliptical(9999.0, 9999.0)),
//                             border: Border.all(
//                                 width: 1.77, color: const Color(0x692ffaed)),
//                           ),
//                         ),
//                       ),
//                     ),
//                     Pinned.fromSize(
//                       bounds: Rect.fromLTWH(264.2, 264.2, 220.7, 220.7),
//                       size: Size(749.2, 749.2),
//                       fixedWidth: true,
//                       fixedHeight: true,
//                       child: Container(
//                         decoration: BoxDecoration(
//                           borderRadius: BorderRadius.all(
//                               Radius.elliptical(9999.0, 9999.0)),
//                           border: Border.all(
//                               width: 1.91, color: const Color(0x692ffaed)),
//                         ),
//                       ),
//                     ),
//                     Pinned.fromSize(
//                       bounds: Rect.fromLTWH(283.5, 283.5, 182.1, 182.1),
//                       size: Size(749.2, 749.2),
//                       fixedWidth: true,
//                       fixedHeight: true,
//                       child: Container(
//                         decoration: BoxDecoration(
//                           borderRadius: BorderRadius.all(
//                               Radius.elliptical(9999.0, 9999.0)),
//                           border: Border.all(
//                               width: 2.04, color: const Color(0x692ffaed)),
//                         ),
//                       ),
//                     ),
//                     Pinned.fromSize(
//                       bounds: Rect.fromLTWH(302.9, 302.9, 143.5, 143.5),
//                       size: Size(749.2, 749.2),
//                       fixedWidth: true,
//                       fixedHeight: true,
//                       child: Container(
//                         decoration: BoxDecoration(
//                           borderRadius: BorderRadius.all(
//                               Radius.elliptical(9999.0, 9999.0)),
//                           border: Border.all(
//                               width: 2.18, color: const Color(0x692ffaed)),
//                         ),
//                       ),
//                     ),
//                     Pinned.fromSize(
//                       bounds: Rect.fromLTWH(322.2, 322.2, 104.8, 104.8),
//                       size: Size(749.2, 749.2),
//                       fixedWidth: true,
//                       fixedHeight: true,
//                       child: Container(
//                         decoration: BoxDecoration(
//                           borderRadius: BorderRadius.all(
//                               Radius.elliptical(9999.0, 9999.0)),
//                           border: Border.all(
//                               width: 2.32, color: const Color(0x692ffaed)),
//                         ),
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//               Pinned.fromSize(
//                 bounds: Rect.fromLTWH(0.0, 448.0, 749.2, 749.2),
//                 size: Size(749.2, 1197.1),
//                 pinLeft: true,
//                 pinRight: true,
//                 pinBottom: true,
//                 fixedHeight: true,
//                 child: Stack(
//                   children: <Widget>[
//                     Pinned.fromSize(
//                       bounds: Rect.fromLTWH(109.7, 109.7, 529.7, 529.7),
//                       size: Size(749.2, 749.2),
//                       pinLeft: true,
//                       pinRight: true,
//                       pinTop: true,
//                       pinBottom: true,
//                       child: Transform.rotate(
//                         angle: -0.7854,
//                         child: Container(
//                           decoration: BoxDecoration(
//                             borderRadius: BorderRadius.all(
//                                 Radius.elliptical(9999.0, 9999.0)),
//                             border: Border.all(
//                                 width: 0.81, color: const Color(0x4d2ffaed)),
//                           ),
//                         ),
//                       ),
//                     ),
//                     Pinned.fromSize(
//                       bounds: Rect.fromLTWH(129.0, 129.0, 491.1, 491.1),
//                       size: Size(749.2, 749.2),
//                       pinLeft: true,
//                       pinRight: true,
//                       pinTop: true,
//                       pinBottom: true,
//                       child: Transform.rotate(
//                         angle: -0.7854,
//                         child: Container(
//                           decoration: BoxDecoration(
//                             borderRadius: BorderRadius.all(
//                                 Radius.elliptical(9999.0, 9999.0)),
//                             border: Border.all(
//                                 width: 0.95, color: const Color(0x4d2ffaed)),
//                           ),
//                         ),
//                       ),
//                     ),
//                     Pinned.fromSize(
//                       bounds: Rect.fromLTWH(148.3, 148.3, 452.5, 452.5),
//                       size: Size(749.2, 749.2),
//                       pinLeft: true,
//                       pinRight: true,
//                       pinTop: true,
//                       pinBottom: true,
//                       child: Transform.rotate(
//                         angle: -0.7854,
//                         child: Container(
//                           decoration: BoxDecoration(
//                             borderRadius: BorderRadius.all(
//                                 Radius.elliptical(9999.0, 9999.0)),
//                             border: Border.all(
//                                 width: 1.09, color: const Color(0x4d2ffaed)),
//                           ),
//                         ),
//                       ),
//                     ),
//                     Pinned.fromSize(
//                       bounds: Rect.fromLTWH(167.7, 167.7, 413.9, 413.9),
//                       size: Size(749.2, 749.2),
//                       pinLeft: true,
//                       pinRight: true,
//                       pinTop: true,
//                       pinBottom: true,
//                       child: Transform.rotate(
//                         angle: -0.7854,
//                         child: Container(
//                           decoration: BoxDecoration(
//                             borderRadius: BorderRadius.all(
//                                 Radius.elliptical(9999.0, 9999.0)),
//                             border: Border.all(
//                                 width: 1.22, color: const Color(0x4d2ffaed)),
//                           ),
//                         ),
//                       ),
//                     ),
//                     Pinned.fromSize(
//                       bounds: Rect.fromLTWH(187.0, 187.0, 375.2, 375.2),
//                       size: Size(749.2, 749.2),
//                       pinLeft: true,
//                       pinRight: true,
//                       pinTop: true,
//                       pinBottom: true,
//                       child: Transform.rotate(
//                         angle: -0.7854,
//                         child: Container(
//                           decoration: BoxDecoration(
//                             borderRadius: BorderRadius.all(
//                                 Radius.elliptical(9999.0, 9999.0)),
//                             border: Border.all(
//                                 width: 1.36, color: const Color(0x4d2ffaed)),
//                           ),
//                         ),
//                       ),
//                     ),
//                     Pinned.fromSize(
//                       bounds: Rect.fromLTWH(206.3, 206.3, 336.6, 336.6),
//                       size: Size(749.2, 749.2),
//                       pinLeft: true,
//                       pinRight: true,
//                       pinTop: true,
//                       pinBottom: true,
//                       child: Transform.rotate(
//                         angle: -0.7854,
//                         child: Container(
//                           decoration: BoxDecoration(
//                             borderRadius: BorderRadius.all(
//                                 Radius.elliptical(9999.0, 9999.0)),
//                             border: Border.all(
//                                 width: 1.5, color: const Color(0x4d2ffaed)),
//                           ),
//                         ),
//                       ),
//                     ),
//                     Pinned.fromSize(
//                       bounds: Rect.fromLTWH(225.6, 225.6, 298.0, 298.0),
//                       size: Size(749.2, 749.2),
//                       pinLeft: true,
//                       pinRight: true,
//                       pinTop: true,
//                       pinBottom: true,
//                       child: Transform.rotate(
//                         angle: -0.7854,
//                         child: Container(
//                           decoration: BoxDecoration(
//                             borderRadius: BorderRadius.all(
//                                 Radius.elliptical(9999.0, 9999.0)),
//                             border: Border.all(
//                                 width: 1.63, color: const Color(0x4d2ffaed)),
//                           ),
//                         ),
//                       ),
//                     ),
//                     Pinned.fromSize(
//                       bounds: Rect.fromLTWH(244.9, 244.9, 259.3, 259.3),
//                       size: Size(749.2, 749.2),
//                       fixedWidth: true,
//                       fixedHeight: true,
//                       child: Transform.rotate(
//                         angle: -0.7854,
//                         child: Container(
//                           decoration: BoxDecoration(
//                             borderRadius: BorderRadius.all(
//                                 Radius.elliptical(9999.0, 9999.0)),
//                             border: Border.all(
//                                 width: 1.77, color: const Color(0x4d2ffaed)),
//                           ),
//                         ),
//                       ),
//                     ),
//                     Pinned.fromSize(
//                       bounds: Rect.fromLTWH(264.2, 264.2, 220.7, 220.7),
//                       size: Size(749.2, 749.2),
//                       fixedWidth: true,
//                       fixedHeight: true,
//                       child: Container(
//                         decoration: BoxDecoration(
//                           borderRadius: BorderRadius.all(
//                               Radius.elliptical(9999.0, 9999.0)),
//                           border: Border.all(
//                               width: 1.91, color: const Color(0x4d2ffaed)),
//                         ),
//                       ),
//                     ),
//                     Pinned.fromSize(
//                       bounds: Rect.fromLTWH(283.5, 283.5, 182.1, 182.1),
//                       size: Size(749.2, 749.2),
//                       fixedWidth: true,
//                       fixedHeight: true,
//                       child: Container(
//                         decoration: BoxDecoration(
//                           borderRadius: BorderRadius.all(
//                               Radius.elliptical(9999.0, 9999.0)),
//                           border: Border.all(
//                               width: 2.04, color: const Color(0x4d2ffaed)),
//                         ),
//                       ),
//                     ),
//                     Pinned.fromSize(
//                       bounds: Rect.fromLTWH(302.9, 302.9, 143.5, 143.5),
//                       size: Size(749.2, 749.2),
//                       fixedWidth: true,
//                       fixedHeight: true,
//                       child: Container(
//                         decoration: BoxDecoration(
//                           borderRadius: BorderRadius.all(
//                               Radius.elliptical(9999.0, 9999.0)),
//                           border: Border.all(
//                               width: 2.18, color: const Color(0x4d2ffaed)),
//                         ),
//                       ),
//                     ),
//                     Pinned.fromSize(
//                       bounds: Rect.fromLTWH(322.2, 322.2, 104.8, 104.8),
//                       size: Size(749.2, 749.2),
//                       fixedWidth: true,
//                       fixedHeight: true,
//                       child: Container(
//                         decoration: BoxDecoration(
//                           borderRadius: BorderRadius.all(
//                               Radius.elliptical(9999.0, 9999.0)),
//                           border: Border.all(
//                               width: 2.32, color: const Color(0x4d2ffaed)),
//                         ),
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ],
//     ),
//   );
// }
