import 'dart:async';
import 'dart:io';
import 'dart:ui';
import 'package:dio/dio.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:likewallet/alert_migrate/alert_migrate_bu.dart';
import 'package:likewallet/alert_migrate/already_migrate.dart';
import 'package:likewallet/app_config.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:likewallet/animationPage.dart';
import 'package:likewallet/screen_util.dart';
import 'package:intl/intl.dart' as formatIntl;
import 'package:likewallet/screen/tabslide.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/screen/index.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/libraryman/applang.dart';
import 'package:likewallet/main.dart';
import 'package:likewallet/libraryman/destroy_app.dart';
import 'package:likewallet/alert_update_version/alert_update_version.dart';
import 'package:likewallet/libraryman/address_service.dart';
import 'package:likewallet/libraryman/configuration_service.dart';
import 'package:likewallet/libraryman/ethcontract.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:likewallet/libraryman/crypto.dart';
import 'package:likewallet/libraryman/serviceHTTP.dart';
import 'package:likewallet/libraryman/open_web.dart' as WebOpenInApp;
import 'package:likewallet/ImageTheme.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:likewallet/libraryman/ethcontractv2.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import 'package:package_info/package_info.dart';
import 'package:launch_review/launch_review.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:web3dart/web3dart.dart';

import '../../../alert_migrate/alert_migrate.dart';

class HomeHeadScreen extends StatefulWidget {
  HomeHeadScreen({this.seed});
  final String? seed;
  @override
  _HomeHeadState createState() => _HomeHeadState(seed: seed);
}

class _HomeHeadState extends State<HomeHeadScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  GlobalKey<ScaffoldState> _scaffoldKey = new GlobalKey();
  _HomeHeadState({this.seed});
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final String? seed;
  bool show = false;
  StreamController<double> streamController =
  StreamController<double>.broadcast();
  StreamController<String> streamTotalBalance =
  StreamController<String>.broadcast();
  StreamController<String> streamCurrencyBalance =
  StreamController<String>.broadcast();
  StreamController<String> streamAddr = StreamController<String>.broadcast();
  StreamController<String> streamFullname =
  StreamController<String>.broadcast();
  StreamController<String> streamCurrencyAmount =
  StreamController<String>.broadcast();

  StreamController<String> streamCurrencyLocked =
  StreamController<String>.broadcast();
  StreamController<String> streamBalanceLocked =
  StreamController<String>.broadcast();
  StreamController<double> streamBalanceLock =
  StreamController<double>.broadcast();

  void dispose() {
    streamTotalBalance.close();
    streamController.close();
    streamAddr.close();
    streamCurrencyBalance.close();
    streamFullname.close();
    streamCurrencyAmount.close();
    streamBalanceLocked.close();
    streamCurrencyLocked.close();
    streamBalanceLock.close();
    super.dispose();
  }

  List group = [];
  // List group = List();
  final f = new formatIntl.NumberFormat("###,###.##");
  String msg = 'hello,this is my github:https://github.com/lizhuoyuan';
  bool login = false;
  late String mnemonic;
  String symbol = 'LIKE ';
//  double amount = 0.00;
  String BHT = '0';
  late String uid;
  String balanceTHB = '0';
  bool showlike = false;
  late AbstractServiceHTTP APIHttp;

  late BaseAuth auth;
  late IAddressService addressService;
  late IConfigurationService configETH;

  late CryptoEncryptInterface encrypt;

//  SharmirInterface callSharmir;
  late BaseETH eth;
  late BaseETHV2 eth2;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  late String pketh;
  int tabMenu = 1;
  bool firstOpen = true;
  double balanceOfLIKE = 0;
  late bool firstLogin;
  String fiat = 'none';
  String balanceShow = 'Loading..';
  double totalBalance = 0.00;
  double balanceLIKELock = 0.00;
  String locked_balance = 'Loading..';
  int numberReload = 0;
  RefreshController _refreshController =
  RefreshController(initialRefresh: false);

  final fireStore = FirebaseFirestore.instance;

  late String addr;
  late String phone;
  String firstName = '..';
  String lastName = 'loading';
  bool _saving = false;
  Dio dio = new Dio();
  /////////////////////////////////////////////////////////////

  late AbstractServiceHTTP getfee;
  ///////////////////////////////////////////////////////////////

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _tabController = new TabController(vsync: this, length: 3);
    Future.delayed(Duration.zero, () {
      APIHttp = ServiceHTTP();
      eth = new EthContract();
      eth2 = new EthContractV2();
      auth = new Auth();
      encrypt = new CryptoEncrypt();
      getfee = new ServiceHTTP();
      setInit();
      checkKYC();
      getVersion();
    });
  }

  getVersion() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    String version = packageInfo.version;
    print("Version of device : " + version);
    if (Platform.isAndroid) {
      await FirebaseFirestore.instance
          .collection('version')
          .doc('android')
          .get()
          .then((value) {
        print("Version of store: " + value.data()!['version']);
        if (version != value.data()!['version']) {
          print("go update version");
          // showDialog(
          //     context: context,
          //     barrierColor: Colors.transparent,
          //     builder: (BuildContext context) {
          //       return AlertUpdateVersion();
          //     });
        }
      });
    } else {
      await FirebaseFirestore.instance
          .collection('version')
          .doc('ios')
          .get()
          .then((value) {
        print("Version of store: " + value.data()!['version']);
        if (version != value.data()!['version']) {
          print("go update version");
          // showDialog(
          //     context: context,
          //     barrierColor: Colors.transparent,
          //     builder: (BuildContext context) {
          //       return AlertUpdateVersion();
          //     });
        }
      });
    }
  }

  void checkKYC() async {
    print('checKYC');
    SharedPreferences prefs = await SharedPreferences.getInstance();
    auth.getCurrentUser().then((decodedToken) {
      FirebaseFirestore.instance
          .collection('kyc')
          .doc(decodedToken!.uid)
          .get()
          .then((userSnap) {
        if (userSnap.exists) {
          print('kyc status ${userSnap.data()!["active"]}');
          if (userSnap.data()!["active"] == 2) {
            prefs.setBool('kycActive', true);
          } else {
            prefs.setBool('kycActive', false);
          }
        } else {
          prefs.setBool('kycActive', false);
        }
      });
    });
  }

  Future setInit() async {
    //check login
    SharedPreferences pref = await SharedPreferences.getInstance();
    configETH = new ConfigurationService(pref);
    User? statusSignin = FirebaseAuth.instance.currentUser;
    if (statusSignin == null) {
      // wrong call in wrong place!
      destroyApp().then((clear) async {
        print('destroyApp on setInit');
        AppLanguage appLanguage = AppLanguage();
        await appLanguage.fetchLocale();
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(
              builder: (BuildContext context) => IndexLike(
//                  appLanguage: appLanguage,
              )),
          ModalRoute.withName('/'),
        );
      });
    }
    //เช็คสกุลเงินตั้งต้น
    fiat = pref.getString('currency') ?? 'none';
    print('fiat : ' + fiat);
    //config address ethereum
    addressService = new AddressService(configETH);

    //เช็คสถานะการ login
// <<<<<<< HEAD
//     login = pref.getBool('login');
//     //เช็คการล็อคอินครั้งแรก
//     firstLogin = pref.getBool('firstLogin') ?? false;
//     if (firstLogin) {
//       mnemonic = await configETH.getMnemonic();
//       addr = configETH.getAddress();
//       reloadBalance(configETH.getAddress(), fiat);
// =======
    login = pref.getBool('login') ?? false;
    //เช็คการล็อคอินครั้งแรก
    firstLogin = pref.getBool('firstLogin') ?? false;

    if (login) {
      print('firstLogin: ' + firstLogin.toString());
      //เช็คการเข้าใช้งานครั้งแรก
      if (!firstLogin) {
        pref.setBool('login', true);
        //ไปดึงค่า favorite มา
        setFavorite();
        //เซ็ตให้สถานะเป็นล็อคอินแล้ว
        firstLogin = await pref.setBool('firstLogin', true);
        //ดึงค่า seed 12 คำ
        mnemonic = await configETH.getMnemonic();
        // print('mnemonic setInit 275' + mnemonic);
        if (mnemonic == 'no') {
          destroyApp().then((clear) async {
            AppLanguage appLanguage = AppLanguage();
            await appLanguage.fetchLocale();
            Navigator.pushAndRemoveUntil(
              context,
              MaterialPageRoute(
                  builder: (BuildContext context) => IndexLike(
//                    appLanguage: appLanguage,
                  )),
              ModalRoute.withName('/'),
            );
          });
        }

        //สร้าง address ethereum
        generateETH(mnemonic, pref);
        // addr = configETH.getAddress();
        // Future.delayed(Duration(milliseconds: 0)).then((later) {
        //   reloadBalance(addr, fiat);
        // });
      } else {
        pref.setBool('login', true);
        setFavorite();
        mnemonic = await configETH.getMnemonic();
        addr = configETH.getAddress();
        // print('mnemonic 302 ' + mnemonic);
        checkUnlock();
        if (mnemonic == 'no') {
          destroyApp().then((clear) async {
            AppLanguage appLanguage = AppLanguage();
            await appLanguage.fetchLocale();
            Navigator.pushAndRemoveUntil(
              context,
              MaterialPageRoute(
                  builder: (BuildContext context) => IndexLike(
//                    appLanguage: appLanguage,
                  )),
              ModalRoute.withName('/'),
            );
          });
        }
        addr = configETH.getAddress();
        streamAddr.sink.add(addr);
        print(addr);
        Future.delayed(Duration(milliseconds: 0)).then((later) {
          print('starting reload balance');
          reloadBalance(addr, fiat);
        });
        getFee(pref);
      }
    } else {
      pref.setBool('login', true);
      var xseed = await configETH.getMnemonic();
      // print(xseed);
      if (xseed == 'no') {
        print('xseed == no');

        // configETH.setMnemonic(seed!);
        generateETH(seed, pref);
      } else {
        generateETH(xseed, pref);
      }
    }

    getfee.getVersionApp().then((versionFire) async {
      print(versionFire);
      PackageInfo packageInfo = await PackageInfo.fromPlatform();

      String appName = packageInfo.appName;
      String packageName = packageInfo.packageName;
      String version = packageInfo.version;
      List<String> oldver = version.split('.');
      List<String> newver = versionFire.split('.');

      int oldv = int.parse(oldver[0] + oldver[1] + oldver[2]);
      int newv = int.parse(newver[0] + newver[1] + newver[2]);

      //เช็ค เวอร์ชั่นว่ามีครบ 5 หลักไหมถ้าไม่มีให้เติม 0
      if (oldv.toString().length < 5) {
        int addzero = 5 - oldv.toString().length;
        for (var k = 0; k < addzero; k++) {
          oldv = int.parse(oldv.toString() + '0');
        }
      }
      //CHECK VERSION ใหม่
      if (oldv < newv) {
        _updateVersion(versionFire);
      } else {
        print(oldv);
        print(newv);
      }
    });
    //set ชื่อคนใช้ถ้ามี
    auth.getCurrentUser().then((decodeToken) {
      // print(decodeToken);
      uid = decodeToken!.uid;

      fireStore
          .collection('users')
          .doc(decodeToken.uid)
          .get()
          .then((DocumentSnapshot<Map<String, dynamic>> ds) async {
        if (!mounted) return;
        setState(() {
          firstName = ds.data()!['firstName'];
          lastName = ds.data()!['lastName'];
        });
        print("comming here");
        await checkMigrate();
        await setPopupMigrate();
        //checkIn/ZOVqTtc8uwPRD0qjrPhU6wzlgoj2
        //แสดงผลชื่อนามสกุล
        streamFullname.sink.add(firstName + ' ' + lastName);
        String langCode = pref.getString('language_code') ?? 'en';
        //ลบคนที่ sync address เบอร์ซ้ำ
        //อัพเดทชื่อคนใช้เบอร์โทรกับ firestore ในฐานข้อมูล
        if (configETH.getAddress().substring(0, 2) == "0x") {
          print('update FCMtoken');
          String? FCMtoken = await _firebaseMessaging.getToken();
          pref.setString('FCMtoken', FCMtoken!);
          fireStore.collection('addressDNS').doc(uid).set({
            'address': configETH.getAddress(),
            'phoneNumber': decodeToken.phoneNumber,
            'name': ds.data()!['firstName'] + ' ' + ds.data()!['lastName'],
            'FCMtoken': pref.getString('FCMtoken'),
            'locale': langCode
          });
        }

        fireStore
            .collection('users')
            .doc(decodeToken.uid)
            .update({"locale": langCode});

        // use ds as a snapshot
      });
    });
  }




  Future checkMigrate() async {
    try {
      final user = await auth.getCurrentUser();
      var id = user!.uid;
      final ds =
      await FirebaseFirestore.instance.collection('migration').doc(id).get();
      ds.data()!;
      var migrated = ds.data()!['migrated'].toString();
      if (migrated.isNotEmpty) {

        Navigator.pushReplacement(
            context, MaterialPageRoute(builder: (context) => AlreadyMigrate()));
        return false;
      }
    }catch (e) {
      print(e);
    }
  }

  Future setPopupMigrate() async {
    print("setPopupMigrate");
    SharedPreferences pref = await SharedPreferences.getInstance();

    var tierHere = context.read(tierLevel).state ?? '';

    bool tierLevelPermission = false;

    bool checkMigrateStopper = pref.getBool('checkMigrateStopper') ?? false;

    if(tierHere == 'normal' || tierHere == '') {
      tierLevelPermission = true;
    }
    print(context.read(tierLevel).state);
    print(checkMigrateStopper);

    final dataMigrate = await FirebaseFirestore.instance
        .collection('Migrate')
        .doc('PXXUr8mUznaCvJztE6Yb')
        .get();

    if (tierLevelPermission && checkMigrateStopper == false) {
      // var alert = await showDialog(
      //     context: context,
      //     barrierDismissible: false,
      //     barrierColor: Colors.transparent,
      //     builder: (BuildContext context) {
      //       return AlertMigrate();
      //     });

      pref.setBool('checkMigrateStopper', true);

      var migrateDate = DateTime.parse(dataMigrate.data()!['dateMigrate']);
      var nowDate = DateTime.now();

      if (nowDate.isAfter(migrateDate)) {
        // if (alert) {
          showDialog(
              context: context,
              barrierDismissible: false,
              barrierColor: Colors.transparent,
              builder: (BuildContext context) {
                return AlertMigrateBU();
              });
        // }
      }
    }
  }


  void checkUnlock() async {
    eth.getBalanceLock(address: addr).then((balanceLock) {
      print("here is getBalanceLock");
      print(balanceLock);
      String totalLock = balanceLock.toString();
      print(balanceLock);

      eth2.getUnlockDate(address: addr).then((data) async {
        // print(data);

        if (int.parse(data[0]) >= 0 || int.parse(data[1]) >= 0) {
          String unlockHour;
          String unlockMin;
          // if (!mounted) return;
          // setState(() async {
          unlockHour = data[0];
          unlockMin = data[1];

          //ถ้าหมดเวลาและถอนได้แล้ว
          if (int.parse(unlockHour) == 0 &&
              int.parse(unlockMin) == 0 &&
              data[3] == '1') {
            //คำสั่งถอนเมื่อถึงเวลาถอนได้
            // showUnlocked(double.parse(balanceLock.toString()) / 10e17);
            final String pk = await configETH.getPrivateKey();
            _unlockLikePoint(totalLock, pk);
          }
          print(unlockHour);
          print('manzer');
          print(double.parse(data[2]));
          // });
        }
      });
    });
  }

//กดเคลมรับจากที่ล็อคกลับมา
  Future<bool> _unlockLikePoint(balance, String pk) async {
    String lock = balance;
    var transaction = await eth2.unlockLikePointV2(pk: pk);
    print('withdraw tx : $transaction');

    return true;
  }

  showUnlocked(balance) {
    showDialog(
        context: context,
        builder: (BuildContext context) =>
            alertUnlock(context, f.format(balance).toString()));
  }

  //ตรวจสอบค่าธรรมเนียม
  Future getFee(SharedPreferences pref) async {
    var checkAccount = pref.getBool('transferAccount');
//    print('checkAccount: '+ checkAccount.toString());
//    if (checkAccount == null) {
    new Future.delayed(new Duration(milliseconds: 3000), () {
      auth.getTokenFirebase().then((token) {
        print('getFee allow');
        getfee
            .checkTransferAccount(address: addr, token: token!)
            .then((transfer) {
          print('transfer + ' + transfer.toString());
          print('transfer: ' + transfer.toString());
          pref.setBool('transferAccount', transfer);
        });
      });
    });
    eth.getNativeBalance(address: addr).then((nativeBalance) {
      print('nativeBalance : ' + nativeBalance.toString());
      if (nativeBalance < 0.01) {
        getfee.getFee(addr).then((data) {
          print(data);
        });
      } else {
        print('have ${nativeBalance} coin native');
      }
    });
  }

  //ฟังก์ชั่นสร้าง address ethereum
  Future generateETH(seed, pref) async {
    String getPK = addressService.getPrivateKey(seed);
    configETH.setPrivateKey(getPK);
//      pref.setString('pk', getPK);
    addressService.getPublicAddress(getPK).then((address) {
      print(address.toString());
      configETH.setAddress(address.toString());
      eth.getBalance(address: address.hex).then((balance) {
        print(balance);
//        amount.read(context).state = balance;

        context.read(amount).state = balance.toDouble();
//        amount = balance;
        addr = address.toString();

        streamAddr.sink.add(addr);
//        streamController.sink.add(amount.read(context).state);
//         streamController.sink.add(context.read(amount).state);
        getFee(pref);
        fiat = pref.getString('currency') ?? 'none';
        reloadBalance(address.toString(), fiat);
      });
    });
  }

//ตรวจสอบเวอร์ชั่นของแอพกับฐานใน firebase
  void _updateVersion(versionFire) {
    // flutter defined function
    showDialog(
        context: context,
        builder: (BuildContext context) {
          // return object of type Dialog
          return AlertDialog(
            title: new Text(
              AppLocalizations.of(context)!.translate('main_update_title'),
              style: LikeWalletAppTheme.headline,
            ),
            content: new Text(
                AppLocalizations.of(context)!.translate('main_update_detail') +
                    " Ver." +
                    versionFire,
                style: LikeWalletAppTheme.headline),
            actions: <Widget>[
              // usually buttons at the bottom of the dialog
              new TextButton(
                child: new Text(
                    AppLocalizations.of(context)!.translate('main_update_yes'),
                    style: LikeWalletAppTheme.headline),
                onPressed: () async {
                  if (Platform.isAndroid) {
                    PackageInfo packageInfo = await PackageInfo.fromPlatform();

                    String packageName = packageInfo.packageName;
                    LaunchReview.launch(
                        androidAppId: packageName, iOSAppId: "0000000");
                  } else {
                    getfee.getUpdateiOS().then((updateLink) {
                      _openURL(updateLink);
                    });
                  }
                },
              ),
              new TextButton(
                child: new Text(
                    AppLocalizations.of(context)!
                        .translate('main_update_cancel'),
                    style: LikeWalletAppTheme.headline),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ],
          );
        });
  }

//ฟังก์ชั่น ดึงค่า favorite จากเมนู quickpay
  void setFavorite() async {
    SharedPreferences pref = await SharedPreferences.getInstance();

    auth.getCurrentUser().then((decodeToken) {
      // print(decodeToken);
      uid = decodeToken?.uid ?? '';
      fireStore
          .collection('favoriteQuickpay')
          .doc(decodeToken?.uid ?? '')
          .get()
          .then((DocumentSnapshot<Map<String, dynamic>> ds) {
        if (ds.exists) {
          // List<String> favorite = List<String>();
          List<String> favorite = [];
          for (int i = 0; i < ds.data()!['favorite'].length; i++) {
            favorite.add(ds.data()!['favorite'][i]);
          }
          pref.setStringList('shopFavorite', favorite);
        }
        // use ds as a snapshot
      });
    });
  }

  double dept = 0.0;
  double lockNew = 0.0;
  double lpcu = 0.0;
  double lockAuto = 0.0;
  double lockAll = 0.0;

  Future reloadBalance(address, fiat) async {
    print(address);
    eth.getBalance(address: address).then((balance) async {
      print("balance" + balance.toString());
      if (!mounted) return;
      context.read(amount).state = balance.toDouble();
      eth.getBalanceLockAuto(address: address).then((balanceLockAuto) async {
        eth.getBalanceLock(address: address).then((balanceLock) async {
          print('await');
          await eth2.getLoanV2(address: address).then((loan) {
            if (loan[11].toString() == "1") {
              dept = double.parse(
                  EtherAmount.inWei(loan[6] as BigInt).getInEther.toString());
              print("เงินต้น" +
                  EtherAmount.inWei(loan[6] as BigInt).getInEther.toString());
            }
          });
          Response response = await dio.post(
              "https://" + env.apiUrl + "/getAutocompound",
              data: {"address": address});
          if (response.data["statusCode"] == 200) {
            print("lockNew:" + response.data["result"].toString());
            lockNew = double.parse(response.data["result"].toString());
          }
          Response resultLPCU = await dio.post(
              "https://" + env.apiUrl + "/getLPCU",
              data: {"address": address});
          if (resultLPCU.data["statusCode"] == 200) {
            print("lpcu:" + resultLPCU.data["result"].toString());
            lpcu = double.parse(resultLPCU.data["result"].toString());
          }
          print("balanceLock" + balanceLock.toString());

          totalBalance = balance.toDouble() + balanceLock.toDouble();
          balanceLIKELock = balanceLock.toDouble();
          print(balanceShow);

          locked_balance = f.format(balanceLock + dept).toString();
          lockAuto = balanceLockAuto.toDouble() - (balanceLock + dept);

          if (!mounted) return;
          context.read(totalAmount).state = context.read(amount).state +
              lockNew +
              lockAuto +
              lpcu +
              double.parse(locked_balance.replaceAll(',', ''));
          lockAll = lockNew +
              lockAuto +
              lpcu +
              double.parse(locked_balance.replaceAll(',', ''));

          streamTotalBalance.sink
              .add(f.format(context.read(totalAmount).state));
          streamBalanceLock.sink.add(balanceLIKELock);
          streamBalanceLocked.sink.add(f.format(lockAll));
          streamController.sink.add(context.read(amount).state);
          setState(() {
            _saving = false;
          });
          if (numberReload == 0) {
            print(numberReload);
            streamCurrency(context.read(totalAmount).state.toDouble());
          } else if (numberReload == 1) {
            print(numberReload);
            streamCurrency(lockAll);
          } else if (numberReload == 2) {
            print(numberReload);
            streamCurrency(balance.toDouble());
          }
        });
      });
    });
  }

  refresh(){
    streamCurrency(0);
    streamTotalBalance.sink.add(f.format(0));
    streamBalanceLock.sink.add(0);
    streamBalanceLocked.sink.add(f.format(0));
    streamController.sink.add(0);
  }

  streamCurrency(double balance) {
    print("balance :" + '${balance}');
    fireStore
        .collection('exchangeFiat')
        .doc(fiat == 'none' ? "THB-THB" : "THB-" + fiat)
        .get()
        .then((DocumentSnapshot<Map<String, dynamic>> ds) {
      print(ds.data()!["main"]);
      print("THB-" + fiat);
      if (fiat == 'THB') {
        setState(() {
          balanceTHB = f.format((balance / 100).floorToDouble()).toString();
        });
        streamCurrencyAmount.sink.add(balanceTHB);
      } else if (fiat == 'LIKE') {
        setState(() {
          balanceTHB = f.format((balance).floorToDouble()).toString();
        });
        streamCurrencyAmount.sink.add(balanceTHB);
      } else if (fiat == 'GOLD') {
        setState(() {
          balanceTHB = f
              .format((balance / 100 / ds.data()!["rate"]).floorToDouble())
              .toString();
        });

        streamCurrencyAmount.sink.add(balanceTHB);
      } else {
        setState(() {
          balanceTHB = f
              .format((balance / 100 * ds.data()!["rate"]).floorToDouble())
              .toString();
        });

        streamCurrencyAmount.sink.add(balanceTHB);
      }
    });
  }

  void _openURL(url) async {
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      // iOS
      if (await canLaunch(url)) {
        await launch(url);
      } else {
        throw 'Could not launch $url';
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // ScreenUtil.init(context, designSize: Size(1080, 2340), allowFontScaling: false);
    return Consumer(builder: (context, watch, _) {
      return DefaultTabController(
          length: 3,
          child: Column(
            children: [
              Container(
                padding:
                EdgeInsets.only(left: mediaQuery(context, 'width', 75)),
                alignment: Alignment.centerLeft,
                child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      TabBar(
                        labelColor: Colors.black,
                        labelStyle: TextStyle(
                            letterSpacing: 0.3,
                            fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                            fontWeight: FontWeight.w700,
                            fontSize: 47.sp),
                        unselectedLabelColor: Colors.black.withOpacity(0.4),
                        unselectedLabelStyle: TextStyle(
                            letterSpacing: 0.3,
                            fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                            fontWeight: FontWeight.normal,
                            fontSize: 39.sp),
                        indicatorColor: Colors.transparent,
                        isScrollable: true,
                        controller: _tabController,
                        onTap: (index) {
                          setState(() {
                            numberReload = index;
                          });
                          reloadBalance(configETH.getAddress(), fiat);
                        },
                        tabs: [
                          Container(
                            alignment: Alignment.bottomCenter,
                            padding: EdgeInsets.only(
                                bottom: mediaQuery(context, 'height', 10)),
                            height: mediaQuery(context, 'height', 80),
                            child: Text(
                              AppLocalizations.of(context)!.translate('home_Total'),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          Container(
                            alignment: Alignment.bottomCenter,
                            padding: EdgeInsets.only(
                                bottom: mediaQuery(context, 'height', 10)),
                            height: mediaQuery(context, 'height', 80),
                            child: Text(
                              AppLocalizations.of(context)!.translate('home_Locked'),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          Container(
                            alignment: Alignment.bottomCenter,
                            padding: EdgeInsets.only(
                                bottom: mediaQuery(context, 'height', 10)),
                            height: mediaQuery(context, 'height', 80),
                            child: Text(
                              AppLocalizations.of(context)!
                                  .translate('home_Available'),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ],
                      ),
                      Container(
                        height: mediaQuery(context, 'height', 80),
                        margin: EdgeInsets.only(right: mediaQuery(context, 'width', 15)),
                        child: IconButton(
                            icon: SvgPicture.asset("assets/svgs/refresh.svg",
                              color: Color(0xFF000000),
                              height: mediaQuery(context, 'height', 40),
                            ),
                            onPressed: () {
                              refresh();
                              reloadBalance(configETH.getAddress(), fiat);
                            }
                        ),
                      ),
                    ]),
              ),
              Expanded(
                child: Stack(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        // color: Colors.white,
                        boxShadow: [
                          BoxShadow(
                            color: Color(0xff00C4D5).withOpacity(0.3),
                            offset:
                            Offset(0, -mediaQuery(context, 'height', 9)),
                            spreadRadius: 0,
                            blurRadius: mediaQuery(context, 'height', 45),
                          ),
                        ],
                      ),
                      child: SvgPicture.string(
                        '<svg viewBox="0.0 289.0 1084.0 232.0" ><defs><filter id="shadow"><feDropShadow dx="0" dy="-9" stdDeviation="45"/></filter><linearGradient id="gradient" x1="0.0" y1="0.5" x2="1.0" y2="0.5"><stop offset="0.0" stop-color="#ffffffff"  /><stop offset="1.0" stop-color="#1affffff" stop-opacity="0.1" /></linearGradient></defs><path transform="translate(0.0, 288.97)" d="M 51.07308197021484 0 L 1032.927001953125 0 C 1061.134033203125 0 1084 0 1084 0 L 1084 232.0312347412109 C 1084 232.0312347412109 1061.134033203125 232.0312347412109 1032.927001953125 232.0312347412109 L 51.07308197021484 232.0312347412109 C 22.86619567871094 232.0312347412109 -1.000192195732552e-08 232.0312347412109 -1.000192195732552e-08 232.0312347412109 L -1.000262361827708e-08 0 C -1.000262361827708e-08 0 22.86619567871094 0 51.07308197021484 0 Z" fill="url(#gradient)" fill-opacity="0.37" stroke="none" stroke-width="1" stroke-opacity="0.37" stroke-miterlimit="4" stroke-linecap="butt" filter="url(#shadow)"/></svg>',
                        allowDrawingOutsideViewBox: true,
                        fit: BoxFit.fill,
                      ),
                    ),
                    TabBarView(
                      physics: NeverScrollableScrollPhysics(),
                      controller: _tabController,
                      children: [
                        Container(
                          padding: EdgeInsets.only(
                            left: mediaQuery(context, 'height', 75),
                          ),
                          child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SizedBox(
                                  height: mediaQuery(context, 'height', 18),
                                ),
                                StreamBuilder(
                                    stream: streamCurrencyAmount.stream,
                                    initialData: '0',
                                    builder: (context, snapshot) {
                                      return new Text(
                                        fiat == 'none'
                                            ? '= THB ' +
                                            snapshot.data.toString()
                                            : fiat == "GOLD"
                                            ? " = " +
                                            fiat +
                                            " " +
                                            snapshot.data.toString() +
                                            " g "
                                            : " = " +
                                            fiat +
                                            " " +
                                            snapshot.data.toString(),
                                        textAlign: TextAlign.left,
                                        style: TextStyle(
                                          fontFamily:
                                          AppLocalizations.of(context)!
                                              .translate('font2'),
                                          height:
                                          mediaQuery(context, 'height', 3),
                                          color: Colors.black.withOpacity(0.4),
                                          fontSize:
                                          mediaQuery(context, 'height', 41),
                                        ),
                                      );
                                    }),
                                SizedBox(
                                  height: mediaQuery(context, 'height', 18),
                                ),
                                Row(
                                    crossAxisAlignment: CrossAxisAlignment.end,
                                    children: [
                                      StreamBuilder(
                                          stream: streamTotalBalance.stream,
                                          initialData: '0',
                                          builder: (context, snapshot) {
                                            return Text(
                                              snapshot.data.toString(),
                                              textAlign: TextAlign.left,
                                              style: TextStyle(
                                                fontFamily: AppLocalizations.of(
                                                    context)!
                                                    .translate('font2'),
                                                color: Colors.black,
                                                fontWeight: FontWeight.w500,
                                                height: mediaQuery(
                                                    context, 'height', 2.7),
                                                fontSize: mediaQuery(
                                                    context, 'height', 77),
                                              ),
                                            );
                                          }),
                                      SizedBox(
                                        width: mediaQuery(context, 'width', 38),
                                      ),
                                      InkWell(
                                        onTap: () {
                                          // showUnlocked(0.0);
                                        },
                                        child: Text(
                                          "LIKE",
                                          textAlign: TextAlign.left,
                                          style: TextStyle(
                                            letterSpacing: 0.5,
                                            fontFamily:
                                            AppLocalizations.of(context)!
                                                .translate('font1'),
                                            color:
                                            Colors.black.withOpacity(0.4),
                                            fontSize: mediaQuery(
                                                context, 'height', 36),
                                          ),
                                        ),
                                      ),
                                    ]),
                              ]),
                        ),
                        Padding(
                          padding: EdgeInsets.only(
                            left: mediaQuery(context, 'height', 75),
                          ),
                          child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SizedBox(
                                  height: mediaQuery(context, 'height', 18),
                                ),
                                StreamBuilder(
                                    stream: streamCurrencyAmount.stream,
                                    initialData: '0',
                                    builder: (context, snapshot) {
                                      return new Text(
                                        fiat == 'none'
                                            ? '= THB ' +
                                            snapshot.data.toString()
                                            : fiat == "GOLD"
                                            ? " = " +
                                            fiat +
                                            " " +
                                            snapshot.data.toString() +
                                            " g "
                                            : " = " +
                                            fiat +
                                            " " +
                                            snapshot.data.toString(),
                                        textAlign: TextAlign.left,
                                        style: TextStyle(
                                          fontFamily:
                                          AppLocalizations.of(context)!
                                              .translate('font2'),
                                          color: Colors.black.withOpacity(0.4),
                                          fontSize:
                                          mediaQuery(context, 'height', 41),
                                        ),
                                      );
                                    }),
                                SizedBox(
                                  height: 18.h,
                                ),
                                Row(
                                    crossAxisAlignment: CrossAxisAlignment.end,
                                    children: [
                                      StreamBuilder(
                                          stream: streamBalanceLocked.stream,
                                          initialData: '0',
                                          builder: (context, snapshot) {
                                            return Text(
                                              snapshot.data.toString(),
                                              textAlign: TextAlign.left,
                                              style: TextStyle(
                                                fontFamily: AppLocalizations.of(
                                                    context)!
                                                    .translate('font2'),
                                                color: Colors.black,
                                                height: mediaQuery(
                                                    context, 'height', 2.7),
                                                fontSize: mediaQuery(
                                                    context, 'height', 80),
                                              ),
                                            );
                                          }),
                                      SizedBox(
                                        width: mediaQuery(context, 'width', 59),
                                      ),
                                      Container(
                                        child: Text(
                                          "LIKE",
                                          textAlign: TextAlign.left,
                                          style: TextStyle(
                                            letterSpacing: 0.5,
                                            fontFamily:
                                            AppLocalizations.of(context)!
                                                .translate('font1'),
                                            color:
                                            Colors.black.withOpacity(0.4),
                                            fontSize: mediaQuery(
                                                context, 'height', 36),
                                          ),
                                        ),
                                      ),
                                      Expanded(child: Container()),
                                      Container(
                                        height: 100.h,
                                        child: TextButton(
                                          onPressed: () {
                                            if (lockAll != 0) {
                                              openShowLockDetail(context);
                                            }
                                          },
                                          child: Icon(
                                            Icons.read_more,
                                            color: Colors.black,
                                          ),
                                        ),
                                      ),
                                    ]),
                              ]),
                        ),
                        Padding(
                          padding: EdgeInsets.only(
                            left: mediaQuery(context, 'height', 75),
                          ),
                          child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SizedBox(
                                  height: mediaQuery(context, 'height', 18),
                                ),
                                StreamBuilder(
                                    stream: streamCurrencyAmount.stream,
                                    initialData: '0',
                                    builder: (context, snapshot) {
                                      return new Text(
                                        fiat == 'none'
                                            ? '= THB ' +
                                            snapshot.data.toString()
                                            : fiat == "GOLD"
                                            ? " = " +
                                            fiat +
                                            " " +
                                            snapshot.data.toString() +
                                            " g "
                                            : " = " +
                                            fiat +
                                            " " +
                                            snapshot.data.toString(),
                                        textAlign: TextAlign.left,
                                        style: TextStyle(
                                          fontFamily:
                                          AppLocalizations.of(context)!
                                              .translate('font2'),
                                          color: Colors.black.withOpacity(0.4),
                                          fontSize:
                                          mediaQuery(context, 'height', 41),
                                        ),
                                      );
                                    }),
                                SizedBox(
                                  height: mediaQuery(context, 'height', 18),
                                ),
                                Row(
                                    crossAxisAlignment: CrossAxisAlignment.end,
                                    children: [
                                      StreamBuilder(
                                          stream: streamController.stream,
                                          initialData: 0,
                                          builder: (context, snapshot) {
                                            return Text(
                                              f
                                                  .format(snapshot.data)
                                                  .toString(),
                                              textAlign: TextAlign.left,
                                              style: TextStyle(
                                                fontFamily: AppLocalizations.of(
                                                    context)!
                                                    .translate('font2'),
                                                color: Colors.black,
                                                height: mediaQuery(
                                                    context, 'height', 2.7),
                                                fontSize: mediaQuery(
                                                    context, 'height', 80),
                                              ),
                                            );
                                          }),
                                      SizedBox(
                                        width: mediaQuery(context, 'width', 59),
                                      ),
                                      Text(
                                        "LIKE",
                                        textAlign: TextAlign.left,
                                        style: TextStyle(
                                          letterSpacing: 0.5,
                                          fontFamily:
                                          AppLocalizations.of(context)!
                                              .translate('font1'),
                                          color: Colors.black.withOpacity(0.4),
                                          fontSize:
                                          mediaQuery(context, 'height', 36),
                                        ),
                                      ),
                                    ]),
                              ]),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ));
    });
  }

  openShowLockDetail(BuildContext context) {
    Dialog simpleDialog = Dialog(
      elevation: 500,
      backgroundColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(30.0),
      ),
      child: Container(
        height: mediaQuery(context, 'height', 800.63),
        width: mediaQuery(context, 'width', 929.64),
        color: Colors.transparent,
        // margin: EdgeInsets.only(bottom: mediaQuery(context, 'height', 600)),
        child: new ClipRect(
          child: new BackdropFilter(
            filter: new ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
            child: Container(
              decoration: BoxDecoration(
                color: LikeWalletAppTheme.white.withOpacity(0.6),
                borderRadius: BorderRadius.all(Radius.circular(20.0)),
              ),
              // height: mediaQuery(context, 'height', 554.63),
              width: mediaQuery(context, 'width', 929.64),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    SizedBox(height: 50.h),
                    Text(
                      AppLocalizations.of(context)!.translate('lock_total'),
                      style: TextStyle(
                        letterSpacing: 0.3,
                        fontFamily:
                        AppLocalizations.of(context)!.translate('font1'),
                        color: LikeWalletAppTheme.black.withOpacity(0.8),
                        fontSize: mediaQuery(context, "height", 50),
                        fontWeight: FontWeight.normal,
                      ),
                    ),
                    SizedBox(height: 20.h),
                    Text(
                      f.format(lockAll) + " " + "LIKE",
                      style: TextStyle(
                        letterSpacing: 0.3,
                        fontFamily:
                        AppLocalizations.of(context)!.translate('font1'),
                        color: LikeWalletAppTheme.black.withOpacity(1),
                        fontSize: mediaQuery(context, "height", 56),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 50.h),
                    lockedDetail(text: 'main_lock', amount: locked_balance),
                    lockedDetail(
                        text: 'locked_reward', amount: f.format(lockAuto)),
                    if(lockNew>0)
                      lockedDetail(text: 'lock_x_like', amount: f.format(lockNew)),
                    if(lpcu>0)
                      lockedDetail(text: 'lock_lpcu', amount: f.format(lpcu)),
                    Expanded(child: Container()),
                    Container(
                        width: mediaQuery(context, 'width', 777.62),
                        decoration: BoxDecoration(
                          border: Border(
                            top: BorderSide(
                              //                   <--- left side
                              color: LikeWalletAppTheme.black.withOpacity(0.4),
                              width: mediaQuery(context, 'width', 1),
                            ),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: <Widget>[
                            GestureDetector(
                              onTap: () {
                                Navigator.of(context).pop();
                              },
                              child: Container(
                                alignment: Alignment.center,
                                height: mediaQuery(context, 'height', 127.66),
                                width: mediaQuery(context, 'width', 777.62) / 2,
                                child: Text(
                                  AppLocalizations.of(context)!
                                      .translate('ldx_yes'),
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    letterSpacing: 0.3,
                                    fontFamily: AppLocalizations.of(context)!
                                        .translate('font1'),
                                    color: LikeWalletAppTheme.bule1_7
                                        .withOpacity(1),
                                    fontSize: mediaQuery(context, "height", 52),
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        )),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
    showDialog(
        context: context, builder: (BuildContext context) => simpleDialog);
  }

  lockedDetail({String? text, String? amount}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            child: Text(
              AppLocalizations.of(context)!.translate(text!),
              textAlign: TextAlign.center,
              style: TextStyle(
                letterSpacing: 0.3,
                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                color: LikeWalletAppTheme.black.withOpacity(1),
                fontSize: mediaQuery(context, "height", 36),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Container(
            child: Text(
              amount! + " " + "LIKE",
              textAlign: TextAlign.center,
              style: TextStyle(
                letterSpacing: 0.3,
                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                color: LikeWalletAppTheme.black.withOpacity(1),
                fontSize: mediaQuery(context, "height", 36),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class CircleTabIndicator extends Decoration {
  final BoxPainter? _painter;

  CircleTabIndicator({required Color color, required double radius})
      : _painter = _CirclePainter(color, radius);

  // @override
  // BoxPainter createBoxPainter([VoidCallback? onChanged]) {
  //   // TODO: implement createBoxPainter
  //   throw UnimplementedError();
  // }

  @override
  BoxPainter createBoxPainter([VoidCallback? onChanged]) =>
      _painter as BoxPainter;
//
}

class _CirclePainter extends BoxPainter {
  final Paint _paint;
  final double radius;

  _CirclePainter(Color color, this.radius)
      : _paint = Paint()
    ..color = color
    ..isAntiAlias = true;

  @override
  void paint(Canvas canvas, Offset offset, ImageConfiguration cfg) {
    final Offset circleOffset =
        offset + Offset(cfg.size!.width / 2, cfg.size!.height - radius);
    canvas.drawCircle(circleOffset, radius, _paint);
  }
}
