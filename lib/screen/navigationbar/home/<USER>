import 'dart:async';
import 'dart:io';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:likewallet/middleware/getLanguage.dart';
import 'package:likewallet/screen/navigationbar/Info/info_screen.dart';
import 'package:likewallet/screen/navigationbar/home/<USER>';
import 'package:likewallet/screen/check_network.dart';
import 'package:likewallet/screen/navigationbar/home/<USER>';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/screen/tabslide.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/alert_lendex/alert_lendex.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:likewallet/libraryman/configuration_service.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:likewallet/alert_lendex/check_alert_lendex.dart';
import 'package:likewallet/alert_update_version/alert_update_version.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:package_info/package_info.dart';
import 'package:likewallet/middleware/check_maintenance/check_maintenance.dart';
import 'package:likewallet/main.dart';
import 'package:likewallet/test.dart' as test;
import 'package:uni_links/uni_links.dart';

class HomeScreen extends StatefulWidget {
  HomeScreen({this.seed});
  final String? seed;
  @override
  _HomeScreenState createState() => _HomeScreenState(seed: seed);
}

bool _initialUriIsHandled = false;

class _HomeScreenState extends State<HomeScreen> {
  GlobalKey<ScaffoldState> _scaffoldKey = new GlobalKey();
  _HomeScreenState({this.seed});

  final String? seed;
  late String phone;
  late String address;
  late BaseAuth auth;
  late IConfigurationService configETH;
  late ICheckAlertLENDEX alertLENDEX;
  late CheckAbout checkAbout;
  late OnLanguage language;
  bool permissionIncome = false;
  bool permissionNews = false;
  bool agreementStatus = true;
  Uri? _initialUri;
  Uri? _latestUri;
  Object? _err;
  StreamSubscription? _sub;

  @override
  void initState() {
    auth = new Auth();
    alertLENDEX = CheckAlertLENDEX();
    checkAbout = OnCheckAbout();
    // TODO: implement initState
    super.initState();
    getCurrentUser();
    getImageReceiptTheme();
    getVersion();
    checkPermission();
    _handleIncomingLinks();
    _handleInitialUri();
  }

  void _handleIncomingLinks() {
    if (!kIsWeb) {
      // It will handle app links while the app is already started - be it in
      // the foreground or in the background.
      _sub = uriLinkStream.listen((Uri? uri) {
        if (!mounted) return;
        // print('got uri: $uri');
        if (uri.toString() == 'https://likewallet.io/?openExternalBrowser=1') {
          print('เปิดเเเค่แอพ');
        } else {
          Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) => test.DeepLink(
                        uri: uri,
                      )));
          setState(() {
            _latestUri = uri;
            _err = null;
          });
        }
      }, onError: (Object err) {
        if (!mounted) return;
        print('got err: $err');
        setState(() {
          _latestUri = null;
          if (err is FormatException) {
            _err = err;
          } else {
            _err = null;
          }
        });
      });
    }
  }

  Future<void> _handleInitialUri() async {
    // In this example app this is an almost useless guard, but it is here to
    // show we are not going to call getInitialUri multiple times, even if this
    // was a weidget that will be disposed of (ex. a navigation route change).
    if (!_initialUriIsHandled) {
      try {
        final uri = await getInitialUri();
        if (uri == null) {
          print('no initial uri');
        } else {
          if (uri.toString() ==
              'https://likewallet.io/?openExternalBrowser=1') {
            print('เปิดเเเค่แอพ');
          } else {
            Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (context) => test.DeepLink(
                          uri: uri,
                        ))).then((value) => _initialUriIsHandled = true);

            print('got initial uri: $uri');
          }
        }
        if (!mounted) return;
        setState(() => _initialUri = uri);
      } on PlatformException {
        // Platform messages may fail but we ignore the exception
        print('falied to get initial uri');
      } on FormatException catch (err) {
        if (!mounted) return;
        print('malformed initial uri');
        setState(() => _err = err);
      }
    }
  }

  checkPermission({String? tier}) async {
    print('checkPermission home_screen :' + tier.toString());
    final income = await checkAbout.checkPermissionMenu(
        tierLevel: context.read(tierLevel).state, page: 'income');
    final news = await checkAbout.checkPermissionMenu(
        tierLevel: context.read(tierLevel).state, page: 'news');
    if (!mounted) return;
    setState(() {
      permissionIncome = income;
    });
  }

  getAlertLoan(String uid) async {
    bool result = await alertLENDEX.callEventLoan(uid: uid);
    // print("เเสดง event" + result.toString());
    // if (result) {
    //   showDialog(
    //       context: context,
    //       barrierColor: Colors.transparent,
    //       builder: (BuildContext context) {
    //         return AlertLendex();
    //       });
    // }
  }

  getImageReceiptTheme() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    await FirebaseFirestore.instance
        .collection('imageURL')
        .doc('page')
        .collection('receipt_page')
        .doc('receipt')
        .get()
        .then((value) async {
      print('receipt');
      await pref.setString("receipt", value.data()!['image']);
    });
  }

  getVersion() async {
    // checkSubmitPolicy();
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    String version = packageInfo.version;
    print("Version of device : " + version);
    if (Platform.isAndroid) {
      await FirebaseFirestore.instance
          .collection('version')
          .doc('android')
          .get()
          .then((value) {
        print("Version of store: " + value.data()!['version']);
        if (version != value.data()!['version']) {
          print("go update version");
          // showDialog(
          //     context: context,
          //     barrierColor: Colors.transparent,
          //     barrierDismissible: true,
          //     builder: (BuildContext context) {
          //       return AlertUpdateVersion();
          //     });
        }
      });
    } else {
      await FirebaseFirestore.instance
          .collection('version')
          .doc('ios')
          .get()
          .then((value) {
        print("Version of store: " + value.data()!['version']);
        if (version != value.data()!['version']) {
          print("go update version");
          // showDialog(
          //     context: context,
          //     barrierColor: Colors.transparent,
          //     builder: (BuildContext context) {
          //       return AlertUpdateVersion();
          //     });
        }
      });
    }
  }

  Future getCurrentUser() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    configETH = ConfigurationService(pref);
    var snapshot = await auth.getCurrentUser();
    if (!mounted) return;

    setState(() {
      phone = snapshot!.phoneNumber.toString();
    });


    // await getAlertLoan(snapshot!.uid);
  }

  @override
  Widget build(BuildContext context) {
    screenUtil(context);
    return Scaffold(
      key: _scaffoldKey,
      drawer: tabslide(),
      body: Consumer(builder: (context, watch, _) {
        return Stack(
          children: [
            groupBody(),
            Column(
              children: [
                head(),
                homeBody(),
              ],
            )
          ],
        );
      }),
    );
  }

  Widget head() {
    return Container(
      height: mediaQuery(context, 'height', 521),
      child: Column(
        children: [
          SizedBox(
            height: mediaQuery(context, 'height', 121.5),
          ),
          Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: <Widget>[
                Expanded(
                  child: Container(
                      width: mediaQuery(context, "width", 1080) / 3,
                      padding: EdgeInsets.only(
                        top: mediaQuery(context, 'height', 15),
                        left: mediaQuery(context, "width", 75),
                      ),
                      height: mediaQuery(context, "height", 100),
                      alignment: Alignment.topLeft,
                      child: InkWell(
                          child: Container(
                            height: mediaQuery(context, "height", 100),
                            alignment: Alignment.topLeft,
                            child: Image.asset(
                              LikeWalletImage.icon_menu,
                              height: mediaQuery(context, "height", 34.07),
                              width: mediaQuery(context, "width", 55.74),
                            ),
                          ),
                          onTap: () {
                            _scaffoldKey.currentState!.openDrawer();
                          })),
                ),
                permissionIncome
                    ? InkWell(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => InfoScreen(
                                  phone: phone,
                                  address:
                                      configETH.getAddress().toLowerCase()),
                            ),
                          );
                        },
                        child: Container(
                          padding: EdgeInsets.only(
                              top: mediaQuery(context, 'height', 15)),
                          width: mediaQuery(context, "width", 1080) / 3,
                          alignment: Alignment.topCenter,
                          height: mediaQuery(context, "height", 100),
                          child: SvgPicture.string(
                            '<svg viewBox="507.0 888.0 65.9 33.4" ><path transform="translate(242.47, 484.07)" d="M 330.4030151367188 405.9169921875 C 330.1220092773438 404.5660095214844 328.7980041503906 403.697998046875 327.4460144042969 403.97900390625 L 317.4020080566406 406.0679931640625 C 316.8819885253906 406.1759948730469 316.4100036621094 406.4469909667969 316.0539855957031 406.8420104980469 C 315.1300048828125 407.8680114746094 315.2130126953125 409.4490051269531 316.239013671875 410.3729858398438 L 319.2720031738281 413.10400390625 C 319.1499938964844 413.1849975585938 319.0320129394531 413.2730102539062 318.9230041503906 413.3770141601562 L 309.3269958496094 422.5969848632812 L 293.0740051269531 409.2040100097656 L 265.406005859375 432.9039916992188 C 264.3569946289062 433.8030090332031 264.2349853515625 435.3810119628906 265.1340026855469 436.4289855957031 C 265.6279907226562 437.0069885253906 266.3290100097656 437.3030090332031 267.0329895019531 437.3030090332031 C 267.6090087890625 437.3030090332031 268.18701171875 437.1050109863281 268.6589965820312 436.7009887695312 L 293.135986328125 415.7340087890625 L 309.5830078125 429.2860107421875 L 322.3880004882812 416.9830017089844 C 322.5880126953125 416.7900085449219 322.7380065917969 416.5679931640625 322.8599853515625 416.3349914550781 L 325.2550048828125 418.4909973144531 C 325.6499938964844 418.8469848632812 326.1489868164062 419.0679931640625 326.6780090332031 419.1210021972656 C 328.052001953125 419.2590026855469 329.2770080566406 418.2569885253906 329.4150085449219 416.8829956054688 L 330.4419860839844 406.677001953125 C 330.4679870605469 406.4230041503906 330.4549865722656 406.1669921875 330.4030151367188 405.9169921875 Z" fill="#0fe8d8" stroke="none" stroke-width="1" stroke-miterlimit="10" stroke-linecap="butt" /></svg>',
                            allowDrawingOutsideViewBox: true,
                            color: Colors.black.withOpacity(0.6),
                            height: 33.85.h,
                          ),
                        ),
                      )
                    : Container(),
                Expanded(child: Container()),
              ]),

          // Expanded(child: Container()),
          Expanded(
            child: HomeHeadScreen(seed: seed),
          ),
        ],
      ),
    );
  }

  Widget homeBody() {
    return Container(
      child: HomeBodyScreen(),
    );
  }

  Widget bg() {
    return Stack(
      children: [
        SizedBox(
          width: 1080.w,
          height: MediaQuery.of(context).size.height,
          child: Stack(
            children: <Widget>[
              Stack(
                children: <Widget>[
                  Container(
                    width: 1080.w,
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment(0.0, -1.0),
                          end: Alignment(0.0, 1.0),
                          colors: [
                            const Color(0xff1f2840),
                            const Color(0xff1e1d34)
                          ],
                          stops: [0.0, 1.0],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0x33000000),
                            offset: Offset(-9, 0),
                            blurRadius: 25,
                          ),
                        ],
                      ),
                    ),
                  ),
                  Container(
                    width: 270.w,
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment(0.0, -1.0),
                          end: Alignment(0.0, 1.0),
                          colors: [
                            const Color(0xff212b44),
                            const Color(0xff1e1d34)
                          ],
                          stops: [0.0, 1.0],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0x40000000),
                            offset: Offset(-9, 0),
                            blurRadius: 25,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              )
            ],
          ),
        )
      ],
    );
  }

  groupBody() {
    return Stack(
      children: [
        Column(
          children: [
            AnimatedContainer(
              height: mediaQuery(context, 'height', 521),
              duration: Duration(milliseconds: 300),
              width: MediaQuery.of(context).size.width,
              padding: EdgeInsets.only(
                top: mediaQuery(context, 'height', 121.5),
              ),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment(-1.0, -0.94),
                  end: Alignment(1.0, 1.0),
                  colors: [
                    const Color(0xff52fcf0),
                    const Color(0xff33faec),
                    const Color(0xff22c4e6)
                  ],
                  stops: [0.0, 0.335, 1.0],
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0x29000000),
                    offset: Offset(0, 12),
                    blurRadius: 25,
                  ),
                ],
              ),
            ),
            Expanded(
              child: bg(),
            ),
          ],
        ),
        Positioned(
          height: mediaQuery(context, 'height', 1197.15),
          width: mediaQuery(context, 'height', 749.16),
          top: mediaQuery(context, 'height', 150),
          right: -100,
          child: Image.asset(LikeWalletImage.home_bg),
        ),
      ],
    );
  }

  checkSubmitPolicy() async {
    User? snapshot = await auth.getCurrentUser();
    final lang = await language.getLanguage();
    setState(() {
      agreementStatus = false;
    });
    print("checkSubmitPolicy");
    print(snapshot);
    print(lang);
  }
}
