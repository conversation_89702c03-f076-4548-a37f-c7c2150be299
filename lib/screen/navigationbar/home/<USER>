import 'dart:io';
import 'dart:ui';

import 'package:dio/dio.dart' as dio;
import 'package:dio/dio.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

// import 'package:http/http.dart';
import 'package:likewallet/LDX/homeLDX.dart';
import 'package:likewallet/developer_test.dart';
import 'package:likewallet/libraryman/configuration_service.dart';
import 'package:likewallet/menu/lotto/lotto_screen.dart';
import 'package:likewallet/poi/poi_histroy.dart';
import 'package:likewallet/screen/navigationbar/home/<USER>';
import 'package:likewallet/menu/reward/rewards_screen.dart';
import 'package:likewallet/screen/navigationbar/home/<USER>';
import 'package:likewallet/screen/navigationbar/refer.dart';
import 'package:likewallet/screen_util.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/animationPage.dart';
import 'package:likewallet/menu/LockLIKE_New.dart';
import 'package:likewallet/quickpay/favorites/list.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/libraryman/serviceHTTP.dart';
import 'package:likewallet/bank/banking.dart';
import 'package:likewallet/main.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:lock_to_win/lock_to_win.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:delayed_display/delayed_display.dart';
import 'package:open_store/open_store.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';

// import 'package:device_apps/device_apps.dart';
import 'package:launch_review/launch_review.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:likewallet/close_maintenance.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:likewallet/check_network/check_network.dart';
import 'package:likewallet/middleware/check_maintenance/check_maintenance.dart';
import 'package:likewallet/middleware/getLanguage.dart';
import 'package:likewallet/app_config.dart';
import 'package:likewallet/policy/policy_page.dart';
import 'package:likewallet/model/pageStatus.dart';
import 'package:likewallet/libraryman/maintenance.dart';
import 'package:likewallet/middleware/callFireStore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as riverpod;

class HomeBodyScreen extends StatefulWidget {
  @override
  _HomeBodyScreenState createState() => _HomeBodyScreenState();
}

class _HomeBodyScreenState extends State<HomeBodyScreen>
    with TickerProviderStateMixin {
  late AbstractServiceHTTP APIHttp;
  late List<list> _list = [];
  bool _saving = false;
  late TabController _tabController;
  late BaseAuth auth;
  bool buttonLENDEX = false;
  bool permissionLockLike = false;
  bool permissionReward = false;
  bool permissionLDX = false;
  bool permissionRefer = false;
  bool permissionTimeLock = false;
  bool permissionLotto = false;
  bool permissionLockToWin = false;
  bool permissionBorrow = false;
  bool permissionAccountBorrow = false;
  bool permissionBanking = false;
  bool permissionStorePay = false;
  bool permissionInvest = false;
  bool loadingMenu = true;
  bool agreementStatus = true;
  bool likeToWinStatus = false;
  late CheckAbout checkAbout;
  late OnLanguage language;
  late OnCallFireStore fireStore;
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  var data;
  Dio dio = new Dio();
  late IConfigurationService configETH;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    APIHttp = ServiceHTTP();

    auth = new Auth();
    checkAbout = OnCheckAbout();
    language = CallLanguage();
    fireStore = CallFireStore();
    _tabController = new TabController(vsync: this, length: 3);
    // checkFirst();
    checkFirst();
    getLikeToWIn();
  }

  getLikeToWIn() async {
    try {
      var data = await FirebaseFirestore.instance
          .collection("lotto")
          .doc("lock_to_win")
          .get()
          .then((value) async {

        if (value.data()!["open"].toString() == 'true') {
          return true;
        }else {
          return false;
        }
      });
      setState(() {
        likeToWinStatus = data ;
      });

      print('getLikeToWIn ${likeToWinStatus}');
    } catch (e) {
      print(e);
    }
  }

  checkSubmitPolicy() async {
    User? snapshot = await auth.getCurrentUser();
    final lang = await language.getLanguage();
    print(lang);
    await FirebaseFirestore.instance
        .collection('users')
        .doc(snapshot!.uid)
        .get()
        .then((value) {
      if (value.exists) {
        var dataUser = value.data();

        if (dataUser!.isNotEmpty) {
          if (dataUser['agreement'] == null || dataUser['agreement'] == false) {
            setState(() {
              agreementStatus = false;
            });
          } else {
            setState(() {
              agreementStatus = true;
            });
          }
        } else {
          showShortToast("Error get Data on checkSubmitPolicy", Colors.red);
        }
      }
    });
  }

  checkPermission({required String tier}) async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    configETH = new ConfigurationService(pref);
    var mnemonic = await configETH.getMnemonic();
    var address = configETH.getAddress().toLowerCase();
    data = {"mnemonic": mnemonic, "address": address, "token": ""};
    checkAbout
        .checkPermissionMenu(tierLevel: tier, page: 'locklike')
        .then((status) {
      permissionLockLike = status;
    });
    checkAbout
        .checkPermissionMenu(tierLevel: tier, page: 'reward')
        .then((status) {
      permissionReward = status;
    });
    checkAbout.checkPermissionMenu(tierLevel: tier, page: 'ldx').then((status) {
      permissionLDX = status;
    });
    checkAbout
        .checkPermissionMenu(tierLevel: tier, page: 'refer')
        .then((status) {
      permissionRefer = status;
    });
    checkAbout
        .checkPermissionMenu(tierLevel: tier, page: 'borrow')
        .then((status) {
      permissionBorrow = status;
    });
    checkAbout
        .checkPermissionMenu(tierLevel: tier, page: 'accountBorrow')
        .then((status) {
      permissionAccountBorrow = status;
    });
    checkAbout
        .checkPermissionMenu(tierLevel: tier, page: 'banking')
        .then((status) {
      permissionBanking = status;
    });
    checkAbout
        .checkPermissionMenu(tierLevel: tier, page: 'storePay')
        .then((status) {
      permissionStorePay = status;
    });
    checkAbout
        .checkPermissionMenu(tierLevel: tier, page: 'invest')
        .then((status) {
      permissionInvest = status;
    });
    checkAbout
        .checkPermissionMenu(tierLevel: tier, page: 'lotto')
        .then((status) {
      permissionLotto = status;
    });
    checkAbout
        .checkPermissionMenu(tierLevel: tier, page: 'lockToWin')
        .then((status) {
      permissionLockToWin = status;
    });
    checkAbout
        .checkPermissionMenu(tierLevel: tier, page: 'timelock')
        .then((status) {
      permissionTimeLock = status;
    });
    setState(() {
      loadingMenu = false;
    });
  }

  String tier = '';

  checkFirst() async {
    setState(() => loadingMenu = true);
    //call auth เอาเบอร์โทร
    User? snapshot = await auth.getCurrentUser();
    //นำเบอร์โทรไปเช็ค

    final blacklist = await checkAbout.checkBlackList(
        type: 'blackList', phone: snapshot?.phoneNumber.toString() ?? '');
    final blacklistLDX = await checkAbout.checkBlackList(
        type: 'blackList_LDX', phone: snapshot?.phoneNumber.toString() ?? '');
    final blacklistFIN = await checkAbout.checkBlackList(
        type: 'blackList_FIN', phone: snapshot?.phoneNumber.toString() ?? '');

    print("check backlist");
    print(blacklist);
    print(blacklistLDX);
    print(blacklistFIN);
    if (blacklist == 'blackList') {
      tier = blacklist;
      context.read(tierLevel).state = tier;
      await checkPermission(tier: tier);
    } else if (blacklistLDX == 'blackList_LDX') {
      tier = blacklistLDX;
      context.read(tierLevel).state = tier;
      await checkPermission(tier: tier);
    } else if (blacklistFIN == 'blackList_FIN') {
      tier = blacklistFIN;
      context.read(tierLevel).state = tier;
      await checkPermission(tier: tier);
    } else {
     try {
       tier = await checkAbout.checkTier(
           email: snapshot?.email,
           phone: snapshot?.phoneNumber,
           list: 'whitelist',
           context: context);
       print("tierLevel $tier");
       //set tierLevel
       if (tier != '') {
         if (!mounted) return;
         context.read(tierLevel).state = tier;
         _firebaseMessaging.subscribeToTopic('notifyAll');
         await checkPermission(tier: context.read(tierLevel).state);
         if (tier == 'tier1') {
           _firebaseMessaging.subscribeToTopic('notifyTier1');
         } else if (tier == 'normal') {
           _firebaseMessaging.subscribeToTopic('notifyNormal');
         }
       }
     }catch (e) {
       print(e.toString());
     }
    }

    //นำ tierLevel ไปเช็ค สถานะ page
    PageMaintenance statusPage =
    await checkAbout.checkTierPermission(tierLevel: tier, page: "home");
    print("home :" + statusPage.status.toString());
    if (statusPage.status == 'active') {
      //เรียกฟังค์ชั่นได้เริ่มต้น
      getShop();
      checkSubmitPolicy();
    } else {
      print('เกิดข้อผิดพลาด');
      final lang = await language.getLanguage();
      final detail = await checkAbout.selectLanguage(
          language: lang, detail: statusPage.detail);
      final title = await checkAbout.selectLanguage(
          language: lang, detail: statusPage.title);
      final detailTime = await checkAbout.selectLanguage(
          language: lang, detail: statusPage.detail_time);
      Navigator.pushReplacement(
          context,
          MaterialPageRoute(
              builder: (context) => Maintenance(
                title: title,
                detail: detail,
                detailTime: detailTime,
                url: statusPage.url,
              )));
    }
  }

  checkLottoPage() async {
    PageMaintenance statusPage = await checkAbout.checkTierPermission(
        tierLevel: context.read(tierLevel).state, page: 'lockToWin');
    if (statusPage.status == 'active') {
      return true;
    } else {
      //ปิด maintenance
      print('inactive');
      final lang = await language.getLanguage();
      final title = await checkAbout.selectLanguage(
          language: lang, detail: statusPage.title);
      final detail = await checkAbout.selectLanguage(
          language: lang, detail: statusPage.detail);
      final detailTime = await checkAbout.selectLanguage(
          language: lang, detail: statusPage.detail_time);
      Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) => CloseMaintenance(
              title: title,
              detail: detail,
              detailTime: detailTime,
              url: statusPage.url,
            )),
      );
      return false;
    }
  }

  getShop() async {
    //เรียก API ร้านค้า
    APIHttp.listSpendkpayShop().then((data) {
      setState(() => _list = data[0]);
    });
  }

  closePay() => context.read(pay).state = false;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: ModalProgressHUD(
        opacity: 0.1,
        inAsyncCall: _saving,
        progressIndicator: CustomLoading(),
        child: GestureDetector(
          onTap: () {
            setState(() {
              buttonLENDEX = false;
              closePay();
            });
          },
          child: Stack(
            children: <Widget>[
              DefaultTabController(
                length: 3,
                child: Row(
                  children: [
                    Container(
                      padding: EdgeInsets.only(
                          top: mediaQuery(context, 'height', 40)),
                      alignment: Alignment.topLeft,
                      child: RotatedBox(
                        quarterTurns: 1,
                        child: TabBar(
                          controller: _tabController,
                          isScrollable: true,
                          indicator: ShapeDecoration(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.only(
                                  topRight: Radius.circular(100),
                                  topLeft: Radius.circular(100)),
                            ),
                            gradient: LinearGradient(
                              begin: Alignment.bottomCenter,
                              end: Alignment.topCenter,
                              colors: <Color>[
                                Color(0xff505DFF),
                                Color(0xff3948FD),
                              ],
                            ),
                          ),

                          indicatorSize: TabBarIndicatorSize.label,
                          labelColor: Color(0xff52FFFF),
                          unselectedLabelColor: Color(0xff2693FF),
                          // indicatorPadding: EdgeInsets.symmetric(
                          //     horizontal: mediaQuery(context, 'height', 75)),
                          labelStyle: TextStyle(
                              fontFamily: AppLocalizations.of(context)!
                                  .translate('font1'),
                              fontSize: mediaQuery(context, 'width', 33),
                              fontWeight: FontWeight.bold,
                              letterSpacing: 0.5,
                              height: 1.2),
                          unselectedLabelStyle: TextStyle(
                              fontFamily: AppLocalizations.of(context)!
                                  .translate('font1'),
                              fontSize: mediaQuery(context, 'width', 33),
                              fontWeight: FontWeight.normal,
                              letterSpacing: 0.5,
                              height: 1.2),
                          onTap: (index) {
                            setState(() {
                              setState(() {
                                buttonLENDEX = false;
                              });
                            });
                          },
                          tabs: [
                            title(
                                1,
                                AppLocalizations.of(context)!
                                    .translate('main_earn'),
                                _tabController.index),
                            permissionStorePay
                                ? title(
                                2,
                                AppLocalizations.of(context)!
                                    .translate('main_spend'),
                                _tabController.index)
                                : Container(),
                            permissionInvest
                                ? title(
                                3,
                                AppLocalizations.of(context)!
                                    .translate('main_invest'),
                                _tabController.index)
                                : Container(),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(
                      width: mediaQuery(context, 'width', 138),
                    ),
                    Expanded(
                      child: TabBarView(
                        controller: _tabController,
                        // physics: NeverScrollableScrollPhysics(),
                        children: [
                          DelayedDisplay(
                            fadingDuration: const Duration(milliseconds: 800),
                            slidingBeginOffset: const Offset(1.0, 0.0),
                            child: loadingMenu
                                ? Center(
                                child: SpinKitFadingCircle(
                                  color: LikeWalletAppTheme.bule1,
                                  size: 200.h,
                                ))
                                : eranLike(),
                          ),
                          DelayedDisplay(
                            fadingDuration: const Duration(milliseconds: 800),
                            slidingBeginOffset: const Offset(1.0, 0.0),
                            child: spendLike(),
                          ),
                          DelayedDisplay(
                            fadingDuration: const Duration(milliseconds: 800),
                            slidingBeginOffset: const Offset(1.0, 0.0),
                            child: investLike(),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              bank(),
              agreementStatus != true ? agreement(500.00, 900.00) : Container(),
            ],
          ),
        ),
      ),
    );
  }

  Widget eranLike() {
    return Container(
      margin: EdgeInsets.only(
        top: mediaQuery(context, 'height', 0),
      ),
      child: ListView(children: [
        permissionLockLike
            ? eranLikeList(
            lockLike(),
            AppLocalizations.of(context)!.translate('main_lock'),
            LikeWalletImage.icon_locklike,
            'locklike')
            : Container(),
        SizedBox(
          width: mediaQuery(context, 'height', 63.8),
        ),
        permissionLockLike ? border() : Container(),
        permissionReward
            ? eranLikeList(
          // hourlyRewards(),
            Rewards(),
            AppLocalizations.of(context)!.translate('main_hourly'),
            LikeWalletImage.icon_reward,
            'reward')
            : Container(),
        permissionReward ? border() : Container(),
        // eranLikeList(
        //     // adsRewards(),
        //     CloseMaintenance(),
        //     AppLocalizations.of(context)!.translate('rewards_ads'),
        //     LikeWalletImage.icon_watch_ads),
        // border(),

        permissionLDX
            ? eranLikeList(
            homeLDX(),
            AppLocalizations.of(context)!.translate('main_ldx'),
            LikeWalletImage.icon_ldx,
            'ldx')
            : Container(),
        permissionLDX ? border() : Container(),
        permissionRefer
            ? eranLikeList(
            ReferPage(),
            AppLocalizations.of(context)!.translate('main_refer'),
            LikeWalletImage.iconRefer,
            'refer_friend')
            : Container(),
        permissionRefer ? border() : Container(),
        permissionTimeLock
            ? eranLikeList(
            POIHistory(),
            AppLocalizations.of(context)!.translate('poi_history'),
            LikeWalletImage.locked_Likepoint_Rewards_home,
            'icon POI')
            : Container(),
        permissionTimeLock ? border() : Container(),
        // permissionLotto
        //     ? eranLikeList(LottoScreen(), 'Lock to win',
        //         LikeWalletImage.icon_lock_to_win, 'Lock to win')
        //     : Container(),
        likeToWinStatus == true
            ? InkWell(
          onTap: () async {
            if (await checkLottoPage()) {
              auth.getTokenFirebase().then((token) async {
                final lang = await language.getLanguage();
                // print(token);
                Response response = await dio.post(
                    "https://" + env.apiUrl + "/createCustomToken",
                    data: {"token": token});
                data['token'] = response.data["token"];
                data['lang'] = lang == 'th' ? 'th' : 'en';
                print(data);
                LockToWin.startLockToWin(context, data: data);
              });
            }
          },

          // LIKE TO WIN
          child: permissionLockToWin
              ? Container(
            child: Row(
              children: [
                Container(
                  height: mediaQuery(context, 'height', 156.98),
                  width: mediaQuery(context, 'width', 152.16),
                  child:
                  Image.asset(LikeWalletImage.icon_lock_to_win),
                ),
                SizedBox(
                  width: mediaQuery(context, 'height', 63.8),
                ),
                Text(
                  AppLocalizations.of(context)!
                      .translate('ltw_title'),
                  style: TextStyle(
                      fontFamily: AppLocalizations.of(context)!
                          .translate('font1'),
                      fontSize: mediaQuery(context, 'height', 33),
                      letterSpacing: 1,
                      color: const Color(0xe5ffffff),
                      height: 1.2),
                  textAlign: TextAlign.left,
                ),
              ],
            ),
          )
              : Container(),
        )
            : Container(),
        permissionLockToWin ?  likeToWinStatus == true ? border()  :Container(): Container(),
        // FlatButton(
        //   child: Text('addData'),
        //   onPressed: () async {
        //     //     Navigator.push(
        //     //         context,
        //     //         MaterialPageRoute(
        //     //             builder: (BuildContext context) => LottoScreen()));
        //     //
        //     QuerySnapshot<Map<String, dynamic>> phone = await FirebaseFirestore
        //         .instance
        //         .collection('addressDNS')
        //         .where('address',
        //             isEqualTo: '0xce3c6204af959b4c7f5be0ec68c2866542790596'
        //                 .toLowerCase())
        //         .get();
        //     if (phone.docs.isNotEmpty) {
        //       phone.docs.forEach((phone) async {
        //         print(phone.id);
        //         print("name:" + phone.data()[' ']);
        //       });
        //     } else {
        //       print('ไม่มีข้อมูล');
        //     }
        //     //     //     }
        //     //     //     //
        //     //     //     // print('add');
        //     //     //     // FirebaseFirestore.instance
        //     //     //     //     .collection('sandbox')
        //     //     //     //     .doc('tier1')
        //     //     //     //     .collection('whitelist')
        //     //     //     //     .where('phone', isEqualTo: '+***********')
        //     //     //     //     .get()
        //     //     //     //     .then((value) {
        //     //     //     //   value.docs.forEach((element) {
        //     //     //     //     print(element.id);
        //     //     //     //   });
        //     //     //     // });
        //     //     //     // List data = [
        //     //     //     //   'accountBorrow',
        //     //     //     //   'banking',
        //     //     //     //   'borrow',
        //     //     //     //   'buylike',
        //     //     //     //   'cash',
        //     //     //     //   'home',
        //     //     //     //   'income',
        //     //     //     //   'kyc',
        //     //     //     //   'kycSumSub',
        //     //     //     //   'ldx',
        //     //     //     //   'likewallet',
        //     //     //     //   'locklike',
        //     //     //     //   'receive',
        //     //     //     //   'refer',
        //     //     //     //   'reward',
        //     //     //     //   'send',
        //     //     //     //   'lotto'
        //     //     //     // ];
        //     //     //     // data.forEach((element) {
        //     //     //     //   FirebaseFirestore.instance
        //     //     //     //       .collection('tierController')
        //     //     //     //       .doc('controller')
        //     //     //     //       .collection('normal')
        //     //     //     //       .doc('invest')
        //     //     //     //       .set({
        //     //     //     //     "detail": [
        //     //     //     //       'หน้านี้ปิดชั่วคราว เพื่อปรับปรุง',
        //     //     //     //       'This section temporarily closed for improvement',
        //     //     //     //       'This section temporarily closed for improvement',
        //     //     //     //       'This section temporarily closed for improvement',
        //     //     //     //       'This section temporarily closed for improvements'
        //     //     //     //     ],
        //     //     //     //     "permission": true,
        //     //     //     //     "status": 'active',
        //     //     //     //     "title": ['ขออภัย!', 'SORRY!', 'SORRY!', 'SORRY!', 'SORRY!'],
        //     //     //     //     "url": "https://bit.ly/3utaMCh",
        //     //     //     //     "detail_time": [
        //     //     //     //       'จะกลับมาในเร็วๆ นี้',
        //     //     //     //       'Will be back soon',
        //     //     //     //       'Will be back soon',
        //     //     //     //       'Will be back soon',
        //     //     //     //       'Will be back soon'
        //     //     //     //     ],
        //     //     //     //   }).then((value) {
        //     //     //     //     // print(valuex);Your KYC is completed.
        //     //     //     //     print('add');
        //     //     //     //   });
        //     //     //     // }
        //     //     //
        //     //     //     // FirebaseFirestore.instance
        //     //     //     //     .collection('kycCashOut')
        //     //     //     //     .doc('bookBank')
        //     //     //     //     .collection('V5tIVP3Vb4OSmKyEzB4oSbWx7sb2')
        //     //     //     //     .doc('**********')
        //     //     //     //     .update({"status": "verify"}).then((value) {
        //     //     //     //   // print(valuex);Your KYC is completed.
        //     //     //     //   print('update');
        //     //     //     // });
        //   },
        // ),
        // FlatButton(
        //   child: Text('lotto'),
        //   onPressed: () async {
        //     auth.getTokenFirebase().then((token) async {
        //       print(token);
        //       Response response = await dio.post(
        //           "https://" + env.apiUrl + "/createCustomToken",
        //           data: {"token": token});
        //       data['token'] = response.data["token"];
        //       print(data);
        //       LockToWin.startLockToWin(context, data: data);
        //     });
        //   },
        // )
      ]),
    );
  }

  Widget eranLikeList(page, text, icon, docName) {
    return InkWell(
      onTap: () async {
        if (page == 'ldx') {
          _openLDX(context);
        } else {
          Navigator.push(context,
              EnterExitRoute(exitPage: HomeScreen(), enterPage: page))
              .then((callback) {});
        }
      },
      child: Container(
        child: Row(
          children: [
            Container(
              height: mediaQuery(context, 'height', 156.98),
              width: mediaQuery(context, 'width', 152.16),
              child: Image.asset(icon),
            ),
            SizedBox(
              width: mediaQuery(context, 'height', 63.8),
            ),
            Text(
              text,
              style: TextStyle(
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  fontSize: mediaQuery(context, 'height', 33),
                  letterSpacing: 1,
                  color: const Color(0xe5ffffff),
                  height: 1.2),
              textAlign: TextAlign.left,
            ),
          ],
        ),
      ),
    );
  }

  Widget spendLike() {
    return Container(
        margin: EdgeInsets.only(
          top: mediaQuery(context, 'height', 100),
        ),
        // width: mediaQuery(context, 'width', 400),
        child: _list.length > 0
            ? Column(
          children: <Widget>[
            Row(
              children: [
                GestureDetector(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => Banking(
                                selectedAddr: 'address',
                                shopID: _list[0].running,
                                source: 'favorite',
                                contract: _list[0].contract != null
                                    ? _list[0].contract
                                    : 'no',
                                abi: _list[0].contract != null
                                    ? _list[0].abi
                                    : 'no',
                                callFunction: _list[0].contract != null
                                    ? _list[0].callFunction
                                    : 'no')),
                      );
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        boxShadow: [
                          new BoxShadow(
                              color: Colors.black.withOpacity(0.16),
                              offset: new Offset(0, 3),
                              blurRadius: 6.0,
                              spreadRadius: 1.0),
                        ],
                      ),
                      height: mediaQuery(context, 'height', 225),
                      child: Image.network(
                        _list[0].logo.toString(),
                      ),
                    )),
                SizedBox(
                  width: mediaQuery(context, 'width', 104),
                ),
                GestureDetector(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => Banking(
                                selectedAddr: 'address',
                                shopID: _list[1].running,
                                source: 'favorite',
                                contract: _list[1].contract != null
                                    ? _list[1].contract
                                    : 'no',
                                abi: _list[1].contract != null
                                    ? _list[1].abi
                                    : 'no',
                                callFunction: _list[1].contract != null
                                    ? _list[1].callFunction
                                    : 'no')),
                      );
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        boxShadow: [
                          new BoxShadow(
                              color: Colors.black.withOpacity(0.16),
                              offset: new Offset(0, 3),
                              blurRadius: 6.0,
                              spreadRadius: 1.0),
                        ],
                      ),
                      height: mediaQuery(context, 'height', 225),
                      child: Image.network(_list[1].logo.toString()),
                    )),
              ],
            ),
            SizedBox(
              height: mediaQuery(context, 'height', 80),
            ),
            // Row(
            //   children: [
            //     GestureDetector(
            //         onTap: () {
            //           Navigator.push(
            //             context,
            //             MaterialPageRoute(
            //                 builder: (context) => Banking(
            //                     selectedAddr: 'address',
            //                     shopID: _list[2].running,
            //                     source: 'favorite',
            //                     contract: _list[2].contract != null
            //                         ? _list[2].contract
            //                         : 'no',
            //                     abi: _list[2].contract != null
            //                         ? _list[2].abi
            //                         : 'no',
            //                     callFunction: _list[2].contract != null
            //                         ? _list[2].callFunction
            //                         : 'no')),
            //           );
            //         },
            //         child: Container(
            //           decoration: BoxDecoration(
            //             shape: BoxShape.circle,
            //             boxShadow: [
            //               new BoxShadow(
            //                   color: Colors.black.withOpacity(0.2),
            //                   offset: new Offset(
            //                       mediaQuery(context, 'height', 6),
            //                       mediaQuery(context, 'height', 12)),
            //                   blurRadius:
            //                   mediaQuery(context, 'height', 25),
            //                   spreadRadius: 1.0),
            //             ],
            //           ),
            //           height: mediaQuery(context, 'height', 225),
            //           child: Image.network(_list[2].logo.toString()),
            //         ))
            //   ],
            // )
          ],
        )
            : Container());
  }

  Widget investLike() {
    return Container(
        margin: EdgeInsets.only(
          top: mediaQuery(context, 'height', 100),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Container(
                width: MediaQuery.of(context).size.width * 0.6051851851,
                child: Text(
                  AppLocalizations.of(context)!.translate('main_detail'),
                  style: TextStyle(
                    fontFamily:
                    AppLocalizations.of(context)!.translate('font1'),
                    color: Color(0xffAFF8FF).withOpacity(0.9),
                    fontSize: mediaQuery(context, 'height', 45),
                  ),
                )),
            SizedBox(
              height: MediaQuery.of(context).size.height * (0.03),
            ),
            // Row(
            //   crossAxisAlignment: CrossAxisAlignment.start,
            //   children: <Widget>[
            //     GestureDetector(
            //       onTap: () {},
            //       child: Image.asset(
            //           AppLocalizations.of(context)!
            //               .translate('main_pic_invest'),
            //           height: mediaQuery(context, 'height', 500)),
            //     ),
            //     SizedBox(width: mediaQuery(context, 'width', 50)),
            //     GestureDetector(
            //       onTap: () {
            //         // Navigator.pushNamed(context, '/invest');
            //       },
            //       child: Image.asset(
            //           AppLocalizations.of(context)!.translate('main_pic_ldx'),
            //           height: mediaQuery(context, 'height', 500)),
            //     ),
            //   ],
            // )
          ],
        ));
  }

  Widget border() {
    return Container(
      margin: EdgeInsets.symmetric(
        vertical: mediaQuery(context, 'height', 58.2),
      ),
      child: SvgPicture.string(
        '<svg viewBox="352.0 846.0 653.0 4.0" ><defs><linearGradient id="gradient" x1="0.920288" y1="0.0" x2="0.039928" y2="0.0"><stop offset="0.0" stop-color="#00ffffff" stop-opacity="0.0" /><stop offset="0.246305" stop-color="#b3ffffff" stop-opacity="0.7" /><stop offset="0.522168" stop-color="#ffffffff"  /><stop offset="0.768473" stop-color="#b3ffffff" stop-opacity="0.7" /><stop offset="1.0" stop-color="#00ffffff" stop-opacity="0.0" /></linearGradient></defs><path transform="translate(352.0, 846.0)" d="M 0 0 L 653 0 L 653 4 L 0 4 L 0 0 Z" fill="url(#gradient)" fill-opacity="0.08" stroke="none" stroke-width="1" stroke-opacity="0.08" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
        allowDrawingOutsideViewBox: true,
      ),
    );
  }

  Widget title(value, text, number) {
    return Container(
      height: 250.39.w,
      width: 164.27.h,
      alignment: Alignment.center,
      child: RotatedBox(
        quarterTurns: -1,
        child: Text(
          text,
          style: TextStyle(
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              fontSize: mediaQuery(context, 'width', 33),
              fontWeight: FontWeight.w500,
              letterSpacing: 1,
              height: 1.2),
        ),
      ),
    );
  }

  late CheckNetWorkService network;

  Widget bank() {
    return Stack(
      children: [
        permissionBorrow || permissionAccountBorrow
            ? Positioned(
          bottom: mediaQuery(context, 'height', 752.8),
          child: InkWell(
              onTap: () async {
                setState(() => _saving = true);
                bool borrowPolicy = false;
                bool accountBorrow = await checkAbout.checkPermissionMenu(
                    page: 'accountBorrow',
                    tierLevel: context.read(tierLevel).state);
                bool borrow = await checkAbout.checkPermissionMenu(
                    page: 'borrow',
                    tierLevel: context.read(tierLevel).state);
                setState(() => _saving = false);
                if (accountBorrow || borrow) {
                  var snapshot = await auth.getCurrentUser();
                  //เช็คสิทธิ์ borrow
                  print(snapshot!.uid);
                  await FirebaseFirestore.instance
                      .collection('allowPolicy')
                      .doc('policy')
                      .collection('borrow')
                      .doc(snapshot.uid)
                      .get()
                      .then((value) {
                    print(value.exists);
                    if (value.exists == false) {
                      borrowPolicy = value.exists;
                    } else {
                      borrowPolicy = value.data()!['status'];
                    }
                  });
                  //เงื่อนไขการพบข้อมูล
                  if (borrowPolicy) {
                    setState(() {
                      buttonLENDEX = !buttonLENDEX;
                    });
                  } else {
                    showDialog(
                      context: context,
                      builder: (BuildContext context) => Policy(),
                    );
                  }
                } else {
                  showShortToast(
                      AppLocalizations.of(context)!.translate('save_err'),
                      Colors.red);
                  buttonLENDEX = false;
                  print('เกิดข้อผิดพลาด');
                }
              },
              child: Column(
                children: [
                  Text(
                      AppLocalizations.of(context)!
                          .translate('title_borrow'),
                      style: TextStyle(
                          fontFamily: AppLocalizations.of(context)!
                              .translate('font1'),
                          fontSize: mediaQuery(context, 'width', 33),
                          fontWeight: FontWeight.w500,
                          color: Color(0xff2693FF),
                          letterSpacing: 1,
                          height: 1.2)),
                  SizedBox(height: 25.h),
                  SizedBox(
                    height: 95.3.h,
                    width: 270.0.w,
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        AnimatedContainer(
                          duration: Duration(milliseconds: 200),
                          child: SvgPicture.string(
                            '<svg viewBox="0.0 1439.0 270.0 95.3" ><defs><linearGradient id="gradient" x1="0.5" y1="1.0" x2="0.5" y2="0.0"><stop offset="0.0" stop-color="#ff1c2034"  /><stop offset="0.310345" stop-color="#ff161821"  /><stop offset="0.46798" stop-color="#ff161821"  /><stop offset="0.600985" stop-color="#ff161821"  /><stop offset="1.0" stop-color="#ff1b1f34"  /></linearGradient></defs><path transform="matrix(0.0, 1.0, -1.0, 0.0, 270.0, 1439.0)" d="M 95.31761932373047 270 L 0 270 L 0 0 L 95.31761932373047 0 L 95.31761932373047 270 Z" fill="url(#gradient)" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                            allowDrawingOutsideViewBox: true,
                            fit: BoxFit.fill,
                          ),
                        ),
                        LENDEX().iconLENDEX(),
                      ],
                    ),
                  ),
                ],
              )),
        )
            : Container(),
        buttonLENDEX ? popupLENDEX() : Container(),
        permissionBanking
            ? Positioned(
          left: mediaQuery(context, 'width', 75),
          bottom: mediaQuery(context, 'height', 500.8),
          child: InkWell(
            onTap: () async {
              Navigator.push(
                  context,
                  EnterExitRoute(
                      exitPage: HomeScreen(), enterPage: Banking()));
              // sendNotification(
              //   point: "message.data[" "]",
              // );
            },
            child: Image.asset(LikeWalletImage.icon_banking,
                height: mediaQuery(context, 'height', 112.18)),
          ),
        )
            : Positioned(
            left: mediaQuery(context, 'width', 75),
            bottom: mediaQuery(context, 'height', 500.8),
            child: Center(
                child: SpinKitFadingCircle(
                  color: LikeWalletAppTheme.bule1,
                  size: 70.h,
                ))),
      ],
    );
  }

  popupLENDEX() {
    return Positioned(
      // duration: Duration(milliseconds: 0),
      top: buttonLENDEX ? 700.w : 0.h,
      left: buttonLENDEX ? 144.w : 0,
      child: InkWell(
        onTap: () {
          setState(() => buttonLENDEX = !buttonLENDEX);
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // InkWell(
            //   onTap: () => Navigator.push(
            //     context,
            //     MaterialPageRoute(builder: (context) => AccountLenDexScreen()),
            //   ).then((value) async {
            //     setState(() => buttonLENDEX = false);
            //     await checkPermission(tier: context.read(tierLevel).state);
            //   }),
            //   child: LENDEX().myAccountButton(context),
            // ),
            SizedBox(height: 30.h),
            // InkWell(
            //   onTap: () => Navigator.push(
            //     context,
            //     MaterialPageRoute(builder: (context) => LenDexStep1Screen()),
            //   ).then((value) async {
            //     setState(() => buttonLENDEX = false);
            //     await checkPermission(tier: context.read(tierLevel).state);
            //   }),
            //   child: LENDEX().borowButton(context),
            // )
          ],
        ),
      ),
    );
  }

  _launchURL() async {
    String url;
    //ตรวจสอบ platform
    if (Platform.isAndroid) {
      url = 'com.prachakij.ldx';
      //ตรวจสอบว่ามีการติดตั้งไว้ในเครื่องหรือยัง
      // bool isInstalled = await DeviceApps.isAppInstalled(url);
      //ถ้าติดตั้งแอพแล้วให้เปิดเลย
      // if (isInstalled) {
      //   print('open app');
      //   // await DeviceApps.openApp(url);
      // } else {
      //   //ถ้ายังไม่มีให้ไปเปิดใน play store เพื่อติดตั้งแทน
      //   print('ถ้ายังไม่มีให้ไปเปิดใน');
      //   _openURLAPP(url);
      // }
      //ถ้าเป็น iOS
    } else if (Platform.isIOS) {
      String os = Platform.operatingSystem;

      if (os == "ios") {
        print("Container clicked");
        try {
          await launch("ldx://");
        } on PlatformException catch (e) {
          OpenStore.instance.open(
            appStoreId: '1504852835', // AppStore id of your app
            androidAppBundleId:
            'com.prachakij.ldx', // Android app bundle package name
          );
        } finally {
          await launch("ldx://");
          OpenStore.instance.open(
            appStoreId: '1504852835', // AppStore id of your app
            androidAppBundleId:
            'com.prachakij.ldx', // Android app bundle package name
          );
        }
      } else if (os == "android") {
        try {
          // await AppCheck.launchApp("com.prachakij.ldx");
        } catch (e) {
          const url =
              'https://play.google.com/store/apps/details?id=com.prachakij.ldx&hl=en';
          if (await canLaunch(url)) {
            await launch(url);
          } else {
            throw 'Could not launch $url';
          }
        }
      } else {}
    }
  }

  _openURLAPP(url) async {
    // print(url);
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      LaunchReview.launch(androidAppId: url, iOSAppId: url);
      throw 'Could not launch $url';
    }
  }

  _openLDX(BuildContext context) {
    Dialog simpleDialog = Dialog(
      elevation: 500,
      backgroundColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(30.0),
      ),
      child: Container(
        height: mediaQuery(context, 'height', 554.63),
        width: mediaQuery(context, 'width', 929.64),
        color: Colors.transparent,
        margin: EdgeInsets.only(bottom: mediaQuery(context, 'height', 600)),
        child: new ClipRect(
          child: new BackdropFilter(
            filter: new ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
            child: Container(
              decoration: BoxDecoration(
                color: LikeWalletAppTheme.white.withOpacity(0.6),
                borderRadius: BorderRadius.all(Radius.circular(20.0)),
              ),
              height: mediaQuery(context, 'height', 554.63),
              width: mediaQuery(context, 'width', 929.64),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  Text(
                    AppLocalizations.of(context)!.translate('ldx_title'),
                    style: TextStyle(
                      letterSpacing: 0.3,
                      fontFamily:
                      AppLocalizations.of(context)!.translate('font1'),
                      color: LikeWalletAppTheme.black.withOpacity(1),
                      fontSize: mediaQuery(context, "height", 56),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(
                        bottom: mediaQuery(context, 'height', 80)),
                    width: mediaQuery(context, 'width', 777.62),
                    child: Text(
                      AppLocalizations.of(context)!.translate('ldx_detail'),
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        letterSpacing: 0.3,
                        fontFamily:
                        AppLocalizations.of(context)!.translate('font1'),
                        color: LikeWalletAppTheme.black.withOpacity(1),
                        fontSize: mediaQuery(context, "height", 42),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Container(
                      width: mediaQuery(context, 'width', 777.62),
                      decoration: BoxDecoration(
                        border: Border(
                          top: BorderSide(
                            //                   <--- left side
                            color: LikeWalletAppTheme.black.withOpacity(0.4),
                            width: mediaQuery(context, 'width', 1),
                          ),
                        ),
                      ),
                      child: Row(
                        children: <Widget>[
                          GestureDetector(
                            onTap: () {
                              _launchURL();
                            },
                            child: Container(
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                border: Border(
                                  right: BorderSide(
                                    //                   <--- left side
                                    color: LikeWalletAppTheme.black
                                        .withOpacity(0.4),
                                    width: mediaQuery(context, 'width', 1),
                                  ),
                                ),
                              ),
                              height: mediaQuery(context, 'height', 127.66),
                              width: mediaQuery(context, 'width', 777.62) / 2,
                              child: Text(
                                AppLocalizations.of(context)!
                                    .translate('ldx_yes'),
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  letterSpacing: 0.3,
                                  fontFamily: AppLocalizations.of(context)!
                                      .translate('font1'),
                                  color:
                                  LikeWalletAppTheme.bule1_7.withOpacity(1),
                                  fontSize: mediaQuery(context, "height", 52),
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                          GestureDetector(
                            onTap: () {
                              Navigator.of(context).pop();
                            },
                            child: Container(
                              alignment: Alignment.center,
                              height: mediaQuery(context, 'height', 127.66),
                              width: mediaQuery(context, 'width', 777.62) / 2,
                              child: Text(
                                AppLocalizations.of(context)!
                                    .translate('ldx_no'),
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  letterSpacing: 0.3,
                                  fontFamily: AppLocalizations.of(context)!
                                      .translate('font1'),
                                  color:
                                  LikeWalletAppTheme.bule1_7.withOpacity(1),
                                  fontSize: mediaQuery(context, "height", 52),
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                        ],
                      )),
                ],
              ),
            ),
          ),
        ),
      ),
    );
    showDialog(
        context: context, builder: (BuildContext context) => simpleDialog);
  }

  agreement(width, height) {
    return Container(
      width: width,
      height: height,
      margin: EdgeInsets.only(top: mediaQuery(context, 'w', 900)),
      padding: EdgeInsets.fromLTRB(
        mediaQuery(context, 'w', 54),
        mediaQuery(context, 'h', 10),
        mediaQuery(context, 'w', 54),
        mediaQuery(context, 'h', 10),
      ),
      child: Container(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Container(
              width: width,
              height: mediaQuery(context, 'h', 330),
              decoration: BoxDecoration(
                borderRadius: new BorderRadius.only(
                  topLeft: Radius.circular(
                    mediaQuery(context, 'h', 50),
                  ),
                  topRight: Radius.circular(
                    mediaQuery(context, 'h', 50),
                  ),
                  bottomRight: Radius.circular(
                    mediaQuery(context, 'h', 50),
                  ),
                  bottomLeft: Radius.circular(
                    mediaQuery(context, 'h', 50),
                  ),
                ),
                gradient: LinearGradient(
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                  stops: [0, 1.0],
                  colors: [
                    Color(0xff505DFF),
                    Color(0xff3948FD),
                  ],
                ),
              ),
              child: Stack(
                children: <Widget>[
                  Container(
                      margin: EdgeInsets.only(
                        top: mediaQuery(context, 'h', 50),
                        left: mediaQuery(context, 'h', 50),
                        // right: mediaQuery(context, 'h', 50),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          InkWell(
                            onTap: () {
                              launch(
                                  "https://likewallet.io/terms-and-conditions");
                            },
                            child: RichText(
                              text: TextSpan(
                                text: 'By clicking, you accept the ',
                                style: TextStyle(
                                  fontFamily: AppLocalizations.of(context)!
                                      .translate('font1'),
                                  fontSize: mediaQuery(context, 'h', 40),
                                  color: Colors.white,
                                ),
                                children: const <TextSpan>[
                                  TextSpan(
                                      text: 'Terms and Conditions',
                                      style: TextStyle(
                                        color: Colors.orange,
                                        fontWeight: FontWeight.bold,
                                        decoration: TextDecoration.underline,
                                      )),
                                ],
                              ),
                            ),
                          ),
                          SizedBox(height: 30.sp),
                          InkWell(
                            onTap: () {
                              launch("https://likewallet.io/privacy-policy");
                            },
                            child: RichText(
                              text: TextSpan(
                                text: 'and acknowledged the ',
                                style: TextStyle(
                                  fontFamily: AppLocalizations.of(context)!
                                      .translate('font1'),
                                  fontSize: mediaQuery(context, 'h', 40),
                                  color: Colors.white,
                                ),
                                children: const <TextSpan>[
                                  TextSpan(
                                      text: 'Privacy Policy.',
                                      style: TextStyle(
                                        color: Colors.orange,
                                        fontWeight: FontWeight.bold,
                                        decoration: TextDecoration.underline,
                                      )),
                                ],
                              ),
                            ),
                          ),
                          SizedBox(height: 30.sp),
                          InkWell(
                            onTap: () {
                              agreementUpdate();
                              setState(() {
                                agreementStatus = true;
                              });
                            },
                            child: Container(
                              alignment: Alignment.center,
                              width: mediaQuery(context, 'height', 164.0),
                              height: mediaQuery(context, 'width', 76.0),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(38.0),
                                color: const Color(0xff17171e),
                                boxShadow: [
                                  BoxShadow(
                                    color: const Color(0x4d000000),
                                    offset: Offset(0, 3),
                                    blurRadius: 6,
                                  ),
                                ],
                              ),
                              child: Text(
                                "Confirm",
                                style: TextStyle(
                                  fontFamily: 'Prompt',
                                  fontSize: mediaQuery(context, 'height', 30),
                                  color: const Color(0xb200ffff),
                                  letterSpacing: 0.5,
                                  fontWeight: FontWeight.w300,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          )
                        ],
                      )),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  agreementUpdate() async {
    try {
      User? snapshot = await auth.getCurrentUser();
      Map<String, dynamic> agreement = {"agreement": true};
      await FirebaseFirestore.instance
          .collection('users')
          .doc(snapshot!.uid)
          .update(agreement);
      setState(() {
        agreementStatus = true;
      });
    } catch (e) {
      showShortToast("Error $e on AgreementUpdate", Colors.red);
    }
  }
}