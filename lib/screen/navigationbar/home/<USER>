import 'package:flutter/material.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/animationPage.dart';
import 'package:likewallet/lendex/account/account_lendex.dart';
import 'package:likewallet/lendex/lendex_step1.dart';
// import 'package:adobe_xd/pinned.dart';

class LENDEX {
  Widget iconLENDEX() {
    return Image.asset(
      LikeWalletImage.icon_lendex,
      fit: BoxFit.contain,
      height: 23.97.h,
    );
  }

  Widget myAccountButton(context) {
    return Container(
      height: 115.h,
      width: 370.w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(100.0),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.4),
            spreadRadius: 2.h,
            blurRadius: 20.h,
            offset: Offset(0.w, 3.h), // changes position of shadow
          ),
        ],
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          new Container(
            height: 132.0.h,
            color: Colors.transparent,
            child: new Container(
                decoration: new BoxDecoration(
                    color: Color(0xFF52FCF0),
                    borderRadius: new BorderRadius.all(
                      Radius.circular(40.0),
                    )),
                child: new Center(
                  child: Text(
                    AppLocalizations.of(context)!.translate('my_account'),
                    style: TextStyle(
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      fontSize: 39.h,
                      color: const Color(0xff000000),
                      letterSpacing: 3.****************.w,
                      fontWeight: FontWeight.w600,
                      shadows: [
                        Shadow(
                          color: const Color(0x29000000),
                          offset: Offset(0, 3.h),
                          blurRadius: 6.h,
                        )
                      ],
                    ),
                    textAlign: TextAlign.left,
                  ),
                )),
          ),
        ],
      ),
    );
  }

  Widget borowButton(context) {
    return Container(
      height: 115.h,
      width: 253.w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(100.0),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.4),
            spreadRadius: 2.h,
            blurRadius: 20.h,
            offset: Offset(0.w, 3.h), // changes position of shadow
          ),
        ],
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          new Container(
            height: 132.0.h,
            color: Colors.transparent,
            child: new Container(
                decoration: new BoxDecoration(
                    color: Color(0xFF52FCF0),
                    borderRadius: new BorderRadius.all(
                      Radius.circular(40.0),
                    )),
                child: new Center(
                  child: Text(
                    AppLocalizations.of(context)!.translate('borrow'),
                    style: TextStyle(
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      fontSize: 39.h,
                      color: const Color(0xff000000),
                      letterSpacing: 3.****************.w,
                      fontWeight: FontWeight.w600,
                      shadows: [
                        Shadow(
                          color: const Color(0x29000000),
                          offset: Offset(0, 3.h),
                          blurRadius: 6.h,
                        )
                      ],
                    ),
                    textAlign: TextAlign.left,
                  ),
                )),
          ),
        ],
      ),
    );
  }

  Widget point3() {
    return SizedBox(
        width: 100.0.w,
        height: 61.0.h,
        child: Image.asset(
          LikeWalletImage.button_point3,
        ));
  }

  Widget border() {
    return SizedBox(
      height: 4.96.h,
      width: 947.69.h,
      child: SvgPicture.string(
        '<svg viewBox="66.0 1522.0 947.7 5.0" ><defs><linearGradient id="gradient" x1="0.920288" y1="0.0" x2="0.039928" y2="0.0"><stop offset="0.0" stop-color="#00ffffff" stop-opacity="0.0" /><stop offset="0.246305" stop-color="#59ffffff" stop-opacity="0.35" /><stop offset="0.522168" stop-color="#80ffffff" stop-opacity="0.5" /><stop offset="0.768473" stop-color="#59ffffff" stop-opacity="0.35" /><stop offset="1.0" stop-color="#1affffff" stop-opacity="0.1" /></linearGradient></defs><path transform="translate(66.0, 1522.0)" d="M 0 0 L 947.69140625 0 L 947.69140625 4.962890625 L 0 4.962890625 L 0 0 Z" fill="url(#gradient)" fill-opacity="0.3" stroke="none" stroke-width="1" stroke-opacity="0.3" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
        allowDrawingOutsideViewBox: true,
      ),
    );
  }
}
