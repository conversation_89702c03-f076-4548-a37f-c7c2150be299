import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:share/share.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:likewallet/screen/navigationbar/refer/refer_animation.dart';

class ReferPage extends StatefulWidget {
  State<StatefulWidget> createState() => new _ReferPage();
}

class _ReferPage extends State<ReferPage> with TickerProviderStateMixin {
  late BaseAuth auth;
  String refCode = '..loading';
  late AnimationController _loginButtonController;
  var copyStatus = 0;
  var shareStatus = 0;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    auth = Auth();
    setInit();
    _loginButtonController = new AnimationController(
        duration: new Duration(milliseconds: 3000), vsync: this);
  }

  setInit() async {
    auth.getCurrentUser().then((snapshot) {
      FirebaseFirestore.instance
          .collection('users')
          .doc(snapshot!.uid)
          .get()
          .then((DocumentSnapshot<Map<String, dynamic>> ds) {
        if (!mounted) return;
        setState(() {
          refCode = ds.data()!['selfCode'];
        });
      });
    });
  }

  buildForPhone() {
    return Scaffold(
      backgroundColor: LikeWalletAppTheme.white,
      body: SingleChildScrollView(
        child: Stack(
          alignment: Alignment.topCenter,
          children: <Widget>[
            Column(
              children: [
                Container(
                    height: mediaQuery(context, 'height', 610),
                    child: Image.asset(
                      LikeWalletImage.refer_head_animation,
                      width: MediaQuery.of(context).size.width,
                      fit: BoxFit.fitWidth,
                    )),
                Container(
                  width: MediaQuery.of(context).size.width,
                  height: mediaQuery(context, 'height', 1730) -
                      mediaQuery(context, 'height', 220),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment(0.0, -1.0),
                      end: Alignment(0.0, 1.0),
                      colors: [
                        const Color(0xffffffff),
                        const Color(0xfff5f5f5)
                      ],
                      stops: [0.0, 1.0],
                    ),
                  ),
                ),
              ],
            ),
            Positioned(
                top: mediaQuery(context, 'height', 479),
                child: Column(
                  children: [
                    _cradBody(),
                    Container(
                      padding: EdgeInsets.only(
                        top: mediaQuery(context, 'height', 65),
                        bottom: mediaQuery(context, 'height', 25),
                      ),
                      child: new Text(
                        '1 Refer = 500 LIKE',
                        style: TextStyle(
                          color: LikeWalletAppTheme.gray4,
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font1'),
                          fontWeight: FontWeight.bold,
                          fontSize: mediaQuery(context, 'height', 35),
                        ),
                      ),
                    ),
                    _buttonCopy(),
                    _buttonQRcode(),
                  ],
                )),
          ],
        ),
      ),
    );
  }

  void showShortToast(msg, Color color) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_SHORT,
        backgroundColor: color,
        textColor: Colors.white);
  }

  Widget _cradBody() {
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        Container(
          height: mediaQuery(context, 'height', 1103.83),
          width: mediaQuery(context, 'width', 936),
          decoration: BoxDecoration(
            boxShadow: [
              new BoxShadow(
                  color: Colors.black.withOpacity(0.08),
                  offset: new Offset(0, mediaQuery(context, 'height', 1)),
                  blurRadius: mediaQuery(context, 'height', 100),
                  spreadRadius: 0.0),
            ],
          ),
          child: SvgPicture.string(
            '<svg viewBox="72.0 479.0 936.0 1103.8" ><defs><filter id="shadow"><feDropShadow dx="0" dy="9" stdDeviation="45"/></filter><linearGradient id="gradient" x1="0.5" y1="0.0" x2="0.5" y2="1.0"><stop offset="0.0" stop-color="#ffffffff"  /><stop offset="0.187192" stop-color="#e6ffffff" stop-opacity="0.9" /><stop offset="1.0" stop-color="#00ffffff" stop-opacity="0.0" /></linearGradient></defs><path transform="translate(72.0, 479.0)" d="M 66 0 L 870 0 C 906.4508056640625 0 936 29.5492057800293 936 66 L 936 1037.8271484375 C 936 1074.277954101563 906.4508056640625 1103.8271484375 870 1103.8271484375 L 66 1103.8271484375 C 29.5492057800293 1103.8271484375 0 1074.277954101563 0 1037.8271484375 L 0 66 C 0 29.5492057800293 29.5492057800293 0 66 0 Z" fill="url(#gradient)" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" filter="url(#shadow)"/></svg>',
            allowDrawingOutsideViewBox: true,
            fit: BoxFit.fill,
            height: mediaQuery(context, 'height', 1103.83),
            width: mediaQuery(context, 'width', 936),
          ),
        ),
        Container(
          height: mediaQuery(context, 'height', 1103.83),
          width: mediaQuery(context, 'width', 936),
          padding: EdgeInsets.only(
            top: mediaQuery(context, 'height', 50),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(
                  left: mediaQuery(context, 'width', 78),
                ),
                child: Text(
                  AppLocalizations.of(context)!.translate('refer_title'),
                  style: TextStyle(
                    color: LikeWalletAppTheme.black,
                    fontSize: mediaQuery(context, 'height', 44),
                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(
                  left: mediaQuery(context, 'width', 78),
                ),
                child: Text(
                  refCode,
                  style: TextStyle(
                    color: LikeWalletAppTheme.black,
                    fontSize: mediaQuery(context, 'height', 90),
                    fontFamily: 'font1',
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Expanded(
                child: Container(),
              ),
              Align(
                  alignment: Alignment.center,
                  child: Container(
                    decoration: BoxDecoration(
                      color: LikeWalletAppTheme.white,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black12,
                          blurRadius:
                              3.0, // has the effect of softening the shadow
                          spreadRadius:
                              2.0, // has the effect of extending the shadow
                          offset: Offset(
                            0.0, // horizontal, move right 10
                            0.0, // vertical, move down 10
                          ),
                        )
                      ],
                    ),
                    child: RepaintBoundary(
//                  key: globalKey,
                      child: QrImageView(
                        backgroundColor: Colors.white,
                        embeddedImage:
                            AssetImage(LikeWalletImage.icon_refer_Qrcode),
                        embeddedImageStyle: QrEmbeddedImageStyle(
                          size: Size(
                            mediaQuery(context, 'height', 95),
                            mediaQuery(context, 'height', 90),
                          ),
                        ),
                        data: refCode,
                        size: mediaQuery(context, 'height', 491.24),
                      ),
                    ),
                  )),
              Expanded(
                child: Container(),
              ),
              Container(
                padding:
                    EdgeInsets.only(bottom: mediaQuery(context, 'height', 40)),
                alignment: Alignment.topCenter,
                child: Text(
                  AppLocalizations.of(context)!.translate('refer_detail'),
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: LikeWalletAppTheme.Purple.withOpacity(1),
                    fontSize: mediaQuery(context, 'height', 45),
                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
                    fontWeight: FontWeight.normal,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
//     return Container(
//       height: mediaQuery(context, 'height', 1103.83),
//       width: mediaQuery(context, 'width', 936),
//       child: Padding(
//         padding: EdgeInsets.only(
//           top: mediaQuery(context, 'height', 50),
// //          left: mediaQuery(context, 'width', 50),
//           bottom: mediaQuery(context, 'height', 30),
//         ),
//         child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: <Widget>[
//               Padding(
//                 padding: EdgeInsets.only(
//                   left: mediaQuery(context, 'width', 50),
//                 ),
//                 child: Text(
//                   AppLocalizations.of(context)!.translate('refer_title'),
//                   style: TextStyle(
//                     color: LikeWalletAppTheme.black,
//                     fontSize: mediaQuery(context, 'height', 44),
//                     fontFamily: AppLocalizations.of(context)!.translate('font1'),
//                     fontWeight: FontWeight.w400,
//                   ),
//                 ),
//               ),
//               Padding(
//                 padding: EdgeInsets.only(
//                   left: mediaQuery(context, 'width', 50),
//                 ),
//                 child: Text(
//                   refCode,
//                   style: TextStyle(
//                     color: LikeWalletAppTheme.black,
//                     fontSize: mediaQuery(context, 'height', 90),
//                     fontFamily: 'font1',
//                     fontWeight: FontWeight.w600,
//                   ),
//                 ),
//               ),
//               Expanded(
//                 child: Container(),
//               ),
//               Align(
//                   alignment: Alignment.center,
//                   child: Container(
//                     decoration: BoxDecoration(
//                       color: LikeWalletAppTheme.white,
//                       boxShadow: [
//                         BoxShadow(
//                           color: Colors.black12,
//                           blurRadius:
//                               3.0, // has the effect of softening the shadow
//                           spreadRadius:
//                               2.0, // has the effect of extending the shadow
//                           offset: Offset(
//                             0.0, // horizontal, move right 10
//                             0.0, // vertical, move down 10
//                           ),
//                         )
//                       ],
//                     ),
//                     child: RepaintBoundary(
// //                  key: globalKey,
//                       child: QrImageView(
//                         backgroundColor: Colors.white,
//                         data: refCode,
//                         size: mediaQuery(context, 'height', 491.24),
//                       ),
//                     ),
//                   )),
//               Expanded(
//                 child: Container(),
//               ),
//               Container(
//                 padding:
//                     EdgeInsets.only(bottom: mediaQuery(context, 'height', 40)),
//                 alignment: Alignment.topCenter,
//                 child: Text(
//                   AppLocalizations.of(context)!.translate('refer_detail'),
//                   textAlign: TextAlign.center,
//                   style: TextStyle(
//                     color: LikeWalletAppTheme.Purple.withOpacity(0.9),
//                     fontSize: mediaQuery(context, 'height', 45),
//                     fontFamily: AppLocalizations.of(context)!.translate('font1'),
//                     fontWeight: FontWeight.w400,
//                   ),
//                 ),
//               ),
//             ]),
//       ),
//     );
  }

  Widget _buttonCopy() {
    return Padding(
      padding: EdgeInsets.only(bottom: mediaQuery(context, 'height', 40)),
      child: new InkWell(
        onTap: () {
          if (copyStatus == 0) {
            setState(() {
              copyStatus = 1;
              Future.delayed(Duration(milliseconds: 500)).then((value) {
                setState(() {
                  copyStatus = 2;
                  print(refCode);
                  Clipboard.setData(ClipboardData(text: refCode));
                  // showShortToast(
                  //     AppLocalizations.of(context)!.translate('refer_copy_show'),
                  //     Colors.grey);
                });
              });
            });
          }
        },
        child: AnimatedContainer(
          width: copyStatus == 1
              ? mediaQuery(context, 'width', 132)
              : copyStatus == 2
                  ? mediaQuery(context, 'width', 720)
                  : mediaQuery(context, 'width', 720),
          duration: Duration(milliseconds: 300),
          height: mediaQuery(context, 'height', 132),
          alignment: FractionalOffset.center,
          decoration: new BoxDecoration(
            gradient: LinearGradient(
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
                colors: copyStatus == 2
                    ? [
                        Color(0xff485DFA).withOpacity(0.8),
                        Color(0xffAC8DFF).withOpacity(0.8)
                      ]
                    : [
                        LikeWalletAppTheme.bule2,
                        LikeWalletAppTheme.bule2_4.withOpacity(0.9),
                      ]),
            borderRadius: copyStatus == 1
                ? new BorderRadius.all(const Radius.circular(30.0))
                : new BorderRadius.all(const Radius.circular(10.0)),
          ),
          child: copyStatus == 2
              ? Text(
                  AppLocalizations.of(context)!.translate('refer_copied'),
                  style: TextStyle(
                    color: LikeWalletAppTheme.bule1,
                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
                    fontWeight: FontWeight.w200,
                    fontSize: mediaQuery(context, 'height', 50),
                  ),
                )
              : new Text(
                  copyStatus == 1
                      ? ''
                      : AppLocalizations.of(context)!.translate('refer_copy'),
                  style: TextStyle(
                    color: LikeWalletAppTheme.bule1,
                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
                    fontWeight: FontWeight.w200,
                    fontSize: mediaQuery(context, 'height', 50),
                  ),
                ),
        ),
      ),
    );
  }

  Widget _buttonQRcode() {
    return Container(
      width: mediaQuery(context, 'width', 720),
      height: mediaQuery(context, 'height', 132),
      decoration: BoxDecoration(
        borderRadius: new BorderRadius.all(
          const Radius.circular(10.0),
        ),
        gradient: LinearGradient(
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
            colors: [
              LikeWalletAppTheme.bule2,
              LikeWalletAppTheme.bule2_4.withOpacity(0.9),
            ]),
      ),
      child: InkWell(
        onTap: () {
//          createWalelt(context);
          Share.share(refCode, subject: 'Ref');
        },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            new Text(
              AppLocalizations.of(context)!.translate('refer_share'),
              style: TextStyle(
                color: LikeWalletAppTheme.bule1,
                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                fontWeight: FontWeight.w200,
                fontSize: mediaQuery(context, 'height', 50),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(body: buildForPhone());
  }
}

class ShowQRFriend extends StatefulWidget {
  ShowQRFriend({this.referCode});
  final String? referCode;
  @override
  _ShowQRFriend createState() => _ShowQRFriend(referCode: referCode);
}

class _ShowQRFriend extends State<ShowQRFriend> {
  _ShowQRFriend({this.referCode});
  final String? referCode;
  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Scaffold(
      body: new Container(
        child: new Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Expanded(
              child: Center(
                child: RepaintBoundary(
//                  key: globalKey,
                  child: QrImageView(
                    backgroundColor: Colors.white,
                    data: referCode ?? '',
                    size: MediaQuery.of(context).size.height * 0.35308547008,
                  ),
                ),
              ),
            )
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: Color(0xff00F1E0),
        onPressed: () {
          Navigator.pop(context);
        },
        child: Icon(Icons.arrow_back_ios),
      ),
    );
  }
}
