import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:likewallet/Theme.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_rounded_date_picker/flutter_rounded_date_picker.dart';
import 'package:likewallet/screen/navigationbar/statement/open_link.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:async';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/libraryman/applang.dart';
import 'package:provider/provider.dart';

import 'package:likewallet/screen_util.dart';

class Statement extends StatefulWidget {
  Statement({this.address});
  final address;
  @override
  _StatementState createState() => _StatementState(address: address);
}

class _StatementState extends State<Statement> {
  _StatementState({this.address});
  final address;
  DateTime selectedDate = DateTime.now();
  var formatterTH = new DateFormat('dd MMM yyyy', 'th');
  var formatterEN = new DateFormat('dd MMM yyyy', 'en');
  var f = new DateFormat('yyyy-MM-dd');

  late Future<void> _launched;
  String _sendDate = '';
  String _startDate = '';
  String _type = 'All';
  List type = ['All','Lock history'];

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    print(address);
  }

  Future<void> _launchInBrowser(String url) async {
    if (await canLaunch(url)) {
      await launch(
        url,
        forceSafariVC: false,
        forceWebView: false,
        headers: <String, String>{'my_header_key': 'my_header_value'},
      );
    } else {
      throw 'Could not launch $url';
    }
  }

  ValueNotifier<DateTime> _dateTimeNotifier =
      ValueNotifier<DateTime>(DateTime.now());
  _selectDate(BuildContext context, type, lang) async {
    DateTime? picked = await showRoundedDatePicker(
      context: context,
      borderRadius: 0,
      background: Colors.transparent,
      height: 1000.sp,
      locale: Locale(lang),
      theme: ThemeData(
        primaryColor: Color(0xff1F1F33),
        accentColor: LikeWalletAppTheme.bule1,
        dialogBackgroundColor: Color(0xff1F1F33),
        textTheme: TextTheme(
          bodyText1: TextStyle(
            color: LikeWalletAppTheme.white,
          ),
          caption: TextStyle(
            color: LikeWalletAppTheme.bule1,
          ),
        ),
        disabledColor: LikeWalletAppTheme.bule1,
        // accentTextTheme: TextTheme(
        //   body2: TextStyle(color: Colors.black),
        // ),
      ),
      lastDate: _dateTimeNotifier.value,
      initialDate: _dateTimeNotifier.value,
      styleDatePicker: MaterialRoundedDatePickerStyle(
        textStyleDayButton: TextStyle(fontSize: 90.sp, color: Colors.black),
        textStyleYearButton: TextStyle(
          fontSize: 50.sp,
          color: LikeWalletAppTheme.bule1,
        ),
        textStyleDayHeader: TextStyle(
          fontSize: 35.sp,
          color: LikeWalletAppTheme.bule1,
        ),
        textStyleCurrentDayOnCalendar: TextStyle(
            fontSize: 35.sp,
            color: LikeWalletAppTheme.white,
            fontWeight: FontWeight.normal),
        textStyleDayOnCalendar: TextStyle(
          fontSize: 35.sp,
          color: LikeWalletAppTheme.white,
        ),
        textStyleDayOnCalendarSelected: TextStyle(
            fontSize: 35.sp,
            color: LikeWalletAppTheme.black,
            fontWeight: FontWeight.normal),
        textStyleDayOnCalendarDisabled: TextStyle(
          fontSize: 45.sp,
          color: Colors.white.withOpacity(0.3),
        ),
        textStyleMonthYearHeader: TextStyle(
            fontSize: 45.sp,
            color: Colors.white,
            fontWeight: FontWeight.normal),
        paddingDatePicker: EdgeInsets.symmetric(horizontal: 76.sp),
        paddingMonthHeader:
            EdgeInsets.symmetric(horizontal: 76.sp, vertical: 90.sp),
        paddingActionBar: EdgeInsets.all(0),
        paddingDateYearHeader: EdgeInsets.all(32.sp),
        sizeArrow: 60.sp,
        colorArrowNext: Colors.white,
        colorArrowPrevious: Colors.white,
        marginLeftArrowPrevious: 50.sp,
        marginTopArrowPrevious: 50.sp,
        marginTopArrowNext: 50.sp,
        marginRightArrowNext: 50.sp,
        textStyleButtonAction: TextStyle(
          fontSize: 45.sp,
          color: LikeWalletAppTheme.bule1,
        ),
        textStyleButtonPositive: TextStyle(
            fontSize: 45.sp,
            color: LikeWalletAppTheme.white,
            fontWeight: FontWeight.normal),
        textStyleButtonNegative: TextStyle(
            fontSize: 45.sp,
            color: LikeWalletAppTheme.white,
            fontWeight: FontWeight.normal),
        decorationDateSelected: BoxDecoration(
            color: LikeWalletAppTheme.bule1, shape: BoxShape.circle),
        backgroundPicker: Color(0xff1F1F33),
        backgroundActionBar: Color(0xff1F1F33),
        backgroundHeaderMonth: Color(0xff1F1F33),
      ),
      styleYearPicker: MaterialRoundedYearPickerStyle(
        textStyleYear: TextStyle(
          fontSize: 45.sp,
          color: LikeWalletAppTheme.black,
        ),
        textStyleYearSelected: TextStyle(
            fontSize: 56.sp, color: Colors.white, fontWeight: FontWeight.bold),
        heightYearRow: 100.sp,
        // backgroundPicker: Colors.deepPurple[400],
      ),
    );

    if (picked != null && picked != selectedDate) {
      if (type == 'start') {
        setState(() {
          _sendDate = f.format(picked);
          print(lang);
          if (lang == 'th') {
            setState(() {
              _startDate = formatterTH.format(picked);
            });
          } else {
            setState(() {
              _startDate = formatterEN.format(picked);
            });
          }
          _launched = _launchInBrowser(
              _type == 'Lock history' ?
              'https://statement.likewallet.io/generateStatementLikeWallet.php?address=$address&start=$_sendDate&type=lock'
          :'https://statement.likewallet.io/generateStatementLikeWallet.php?address=$address&start=$_sendDate');
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    screenUtil(context);
    return Consumer<AppLanguage>(builder: (context, model, child) {
      return Container(
        decoration: BoxDecoration(
          color: Color(0xffF5F5F5),
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            stops: [0.0, 0.2, 0.5],
            colors: [
              // Colors are easy thanks to Flutter's Colors class.
              Colors.white,
              Colors.white,
              LikeWalletAppTheme.white1
            ],
          ),
        ),
        margin: EdgeInsets.only(
          top: 485.h,
        ),
        child: Column(
          children: [
            radioType(),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 75.w),
              child: Row(
                children: [startDate(model.appLocal.toString())],
              ),
            ),
            buttonOR(),
            SizedBox(
              height: 68.h,
            ),
            chooseCard(model.appLocal.toString()),
          ],
        ),
      );
    });
  }

  Widget startDate(lang) {
    return InkWell(
      onTap: () => setState(() {
        _selectDate(context, 'start', lang);
      }),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context)!.translate('statement_specify'),
            style: TextStyle(
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              fontSize: 34.sp,
              color: const Color(0x993c3c43),
              letterSpacing: 1.02.sp,
              height: 1.****************.sp,
            ),
            textAlign: TextAlign.left,
          ),
          SizedBox(
            height: 12.h,
          ),
          Container(
            alignment: Alignment.centerLeft,
            padding: EdgeInsets.only(left: 38.w),
            height: 99.h,
            width: 456.w,
            decoration: BoxDecoration(
              border: Border.all(
                color: LikeWalletAppTheme.gray,
                width: 1.5.sp,
              ),
              borderRadius: BorderRadius.all(Radius.circular(5.0.sp)),
            ),
            child: Text('$_startDate'),
          ),
        ],
      ),
    );
  }

  // Widget endDate() {
  //   return InkWell(
  //     onTap: () {
  //       if (_startDate != '') {
  //         _selectDate(context, 'end');
  //       }
  //     },
  //     child: Column(
  //       crossAxisAlignment: CrossAxisAlignment.start,
  //       children: [
  //         Text(
  //           'End date',
  //           style: TextStyle(
  //             fontFamily: AppLocalizations.of(context)!.translate('font1'),
  //             fontSize: 34.sp,
  //             color: const Color(0x993c3c43),
  //             letterSpacing: 1.02.sp,
  //             height: 1.****************.sp,
  //           ),
  //           textAlign: TextAlign.left,
  //         ),
  //         Container(
  //           alignment: Alignment.centerLeft,
  //           padding: EdgeInsets.only(left: 38.sp),
  //           height: 99.sp,
  //           width: 456.sp,
  //           decoration: BoxDecoration(
  //             border: Border.all(
  //               color: LikeWalletAppTheme.gray,
  //               width: 1.5.sp,
  //             ),
  //             borderRadius: BorderRadius.all(Radius.circular(5.0.sp)),
  //           ),
  //           child: Text('$_endDate'),
  //         ),
  //       ],
  //     ),
  //   );
  // }

  Widget buttonOR() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 534.78.w,
          height: 116.h,
          margin: EdgeInsets.only(
            top: 178.h,
          ),
          child: Stack(
            alignment: Alignment.center,
            children: [
              SvgPicture.string(
                '<svg viewBox="273.0 295.0 534.8 116.0" ><defs><filter id="shadow"><feDropShadow dx="0" dy="-12" stdDeviation="25"/></filter></defs><path transform="translate(462.11, 295.0)" d="M -131.2132568359375 0 L 287.76904296875 0 C 319.7453918457031 0 345.6673278808594 25.96748352050781 345.6673278808594 58 C 345.6673278808594 90.03251647949219 319.7453918457031 116 287.76904296875 116 L -131.2132568359375 116 C -163.1896057128906 116 -189.1115417480469 90.03251647949219 -189.1115417480469 58 C -189.1115417480469 25.96748352050781 -163.1896057128906 0 -131.2132568359375 0 Z" fill="#333443" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" filter="url(#shadow)"/></svg>',
                allowDrawingOutsideViewBox: true,
              ),
              Text(
                AppLocalizations.of(context)!.translate('statement_or'),
                style: TextStyle(
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  fontSize: 34.h,
                  color: Color(0xff08e8de),
                  letterSpacing: 1.5.sp,
                  shadows: [
                    Shadow(
                      color: const Color(0x29000000),
                      offset: Offset(0, 3.sp),
                      blurRadius: 6.sp,
                    )
                  ],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        )
      ],
    );
  }

  Widget chooseCard(lang) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Column(
          children: [
            lang == 'th'
                ? cardTH('30', 'statement_days')
                : card('statement_last', '30', 'statement_days'),
            SizedBox(
              height: 19.h,
            ),
            lang == 'th'
                ? cardTH('3', 'statement_months')
                : card('statement_past', '3', 'statement_months'),
          ],
        ),
        SizedBox(
          width: 19.w,
        ),
        Column(
          children: [
            lang == 'th'
                ? cardTH('6', 'statement_months')
                : card('statement_past', '6', 'statement_months'),
            SizedBox(
              height: 19.h,
            ),
            lang == 'th'
                ? cardTH('1', 'statement_year')
                : card('statement_past', '1', 'statement_year'),
          ],
        ),
      ],
    );
  }

  Widget card(text1, day, text2) {
    return InkWell(
      onTap: () async {
        if (day == '30') {
          _sendDate = f
              .format(DateTime(
                  selectedDate.year, selectedDate.month - 1, selectedDate.day))
              .toString();
        }
        if (day == '3') {
          _sendDate = f
              .format(DateTime(
                  selectedDate.year, selectedDate.month - 3, selectedDate.day))
              .toString();
        }
        if (day == '6') {
          _sendDate = f
              .format(DateTime(
                  selectedDate.year, selectedDate.month - 6, selectedDate.day))
              .toString();
        }
        if (day == '1') {
          _sendDate = f
              .format(DateTime(
                  selectedDate.year - 1, selectedDate.month, selectedDate.day))
              .toString();
        }
        if (_sendDate != '') {
          _launched = _launchInBrowser(
              _type == 'Lock history' ?
              'https://statement.likewallet.io/generateStatementLikeWallet.php?address=$address&start=$_sendDate&type=lock'
                  :'https://statement.likewallet.io/generateStatementLikeWallet.php?address=$address&start=$_sendDate');
        }
      },
      child: Container(
        alignment: Alignment.center,
        width: 456.0.w,
        height: 272.0.h,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(25.0.sp),
          color: const Color(0xffffffff),
          boxShadow: [
            BoxShadow(
              color: const Color(0x1a000000),
              offset: Offset(0, 6.sp),
              blurRadius: 24.sp,
            ),
          ],
        ),
        child: Text.rich(
          TextSpan(
            style: TextStyle(
              fontFamily: AppLocalizations.of(context)!.translate('font'),
              fontSize: 34.h,
              color: const Color(0xff000000),
              letterSpacing: 1.02.sp,
            ),
            children: [
              TextSpan(
                text: AppLocalizations.of(context)!.translate(text1) + " ",
              ),
              TextSpan(
                text: '$day',
                style: TextStyle(
                  fontSize: 65.h,
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  letterSpacing: 1.95.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
              TextSpan(
                text: " " + AppLocalizations.of(context)!.translate(text2),
              ),
            ],
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget radioType(){
    return
    Container(
        padding: EdgeInsets.only(left: 38.w),
     child: Column(
        children: [
          Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                for (int i = 0; i < type.length; i++)
          Expanded(
          child: Row(
          children: [
              Radio(
              value: type[i].toString(), groupValue: _type, onChanged: (value) {
                setState(() {
                  _type = value.toString();
                  print(_type);
                });
              }),
          Expanded(
            child: Text(type[i].toString(),
              style: TextStyle(
                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                fontSize: 45.sp,
              ),
            ),
          )
        ],
      ),
    flex: 1,
    ),
    ],
    ),
    ],
    ),
    );
  }

  Widget cardTH(day, text2) {
    return InkWell(
      onTap: () async {
        if (day == '30') {
          _sendDate = f
              .format(DateTime(
                  selectedDate.year, selectedDate.month - 1, selectedDate.day))
              .toString();
        }
        if (day == '3') {
          _sendDate = DateTime(
                  selectedDate.year, selectedDate.month - 3, selectedDate.day)
              .toString();
        }
        if (day == '6') {
          _sendDate = f
              .format(DateTime(
                  selectedDate.year, selectedDate.month - 6, selectedDate.day))
              .toString();
        }
        if (day == '1') {
          _sendDate = f
              .format(DateTime(
                  selectedDate.year - 1, selectedDate.month, selectedDate.day))
              .toString();
        }
        print(_sendDate);
        if (_sendDate != '') {
          _launched = _launchInBrowser(
              _type == 'Lock history' ?
              'https://statement.likewallet.io/generateStatementLikeWallet.php?address=$address&start=$_sendDate&type=lock'
                  :'https://statement.likewallet.io/generateStatementLikeWallet.php?address=$address&start=$_sendDate');
        }
      },
      child: Container(
        alignment: Alignment.center,
        width: 456.0.w,
        height: 272.0.h,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(25.0.sp),
          color: const Color(0xffffffff),
          boxShadow: [
            BoxShadow(
              color: const Color(0x1a000000),
              offset: Offset(0, 6.sp),
              blurRadius: 24.sp,
            ),
          ],
        ),
        child: Text.rich(
          TextSpan(
            style: TextStyle(
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              fontSize: 65.h,
              color: const Color(0xff000000),
              letterSpacing: 1.95.h,
            ),
            children: [
              TextSpan(
                text: '$day',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                ),
              ),
              TextSpan(
                text: ' ',
                style: TextStyle(
                  fontSize: 34.sp,
                  letterSpacing: 1.02.sp,
                ),
              ),
              TextSpan(
                text: AppLocalizations.of(context)!.translate(text2),
                style: TextStyle(
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  fontSize: 34.sp,
                  letterSpacing: 1.02.sp,
                ),
              ),
            ],
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}
