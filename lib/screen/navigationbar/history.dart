import 'dart:async';
import 'dart:convert';
import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:likewallet/main.dart';
import 'package:likewallet/middleware/check_maintenance/check_maintenance.dart';
import 'package:likewallet/model/activityLock.dart';
import 'package:likewallet/model/activityLockList.dart';
import 'package:likewallet/screen/navigationbar/custom_expansion_panel_list.dart';
import 'package:likewallet/screen/navigationbar/details_auto_locked.dart';
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/libraryman/grouped_list.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:http/http.dart' as http;
import 'package:likewallet/libraryman/auth.dart';
import 'package:likewallet/app_config.dart';
import 'package:likewallet/setmodel/withdraw_model.dart';
import 'package:flutter/foundation.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:likewallet/libraryman/configuration_service.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:intl/intl.dart' as formatIntl;
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:delayed_display/delayed_display.dart';
import 'package:likewallet/screen/navigationbar/statement/statement.dart';

class HistoryPage extends StatefulWidget {
  State<StatefulWidget> createState() => new _HistoryPage();
}

class _HistoryPage extends State<HistoryPage> with TickerProviderStateMixin {
  bool _saving = true;
  bool statusShow = false;
  int tabSelect = 1;
  bool tab = false;
  late BaseAuth FirebaseAuth;
  late List<Withdraw_history> listWithdraw;
  var entries = [];
  var history = [];

  late IConfigurationService configETH;
  late DateFormat dateFormat;
  late DateFormat timeFormat;
  final formatNum = new formatIntl.NumberFormat("###,###.##");
  late TabController _tabController;
  late SharedPreferences sharedPreferences;
  final f = new DateFormat('yyyy-MM-dd hh:mm');
  bool selected = false;
  String addressETH = '';
  int id = -1;
  var today = DateTime.now();
  bool plus = false;
  bool minus = false;
  List searchPlus = [];
  List searchMinus = [];
  List searchRewards = [];
  List searchAutoLocked = [];
  late String formattedDate;
  String ShowDate = '';
  String ShowMonth = '';
  String ShowYear = '';
  List date = [];
  TextEditingController searchController = new TextEditingController();
  late String filter;
  AlignmentGeometry _alignment = Alignment.centerLeft;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    Future.delayed(Duration.zero, () {
      initializeDateFormatting();
      dateFormat = new DateFormat.yMMMMd('en');
      timeFormat = new DateFormat.Hms('en');
      FirebaseAuth = Auth();

      searchController.addListener(() {
        setState(() {
          filter = searchController.text;
        });
      });

      callAddress();
      print('open app');
      setInit();
    });
  }

  @override
  void dispose() {
    searchController.dispose();
    super.dispose();
  }

  callAddress() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    configETH = new ConfigurationService(pref);
    setState(() {
      addressETH = configETH.getAddress();
    });
  }

  setInit() async {
    _tabController = new TabController(vsync: this, length: 4);
    sharedPreferences = await SharedPreferences.getInstance();
    SharedPreferences pref = await SharedPreferences.getInstance();
    configETH = new ConfigurationService(pref);
    addressETH = configETH.getAddress();
    var url = Uri.https(env.apiUrl, '/getHistoryNew');
    var _token = await FirebaseAuth.getTokenFirebase();
    // print("_token" + _token.toString());
    print("addressETH" + addressETH.toString());
    var response =
        await http.post(url, body: {'_token': _token, 'address': addressETH});
//      print(response);
//    if (response.statusCode == 200) {
    var body = json.decode(response.body);
//        print(body);
    final body2 = await body["result"].cast<Map<String, dynamic>>();
    setState(() {
      listWithdraw = body2
          .map<Withdraw_history>((json) => Withdraw_history.fromJson(json))
          .toList();
      listWithdraw.forEach((element) {
        print("วันที่ :" +
                DateTime.fromMillisecondsSinceEpoch(
                        (element.updateTime)! * 1000)
                    .toString()
            // .substring(0, 10),
            );
        // print("ประเภท :" + element.type);
        // print("ถึง :" + element.to);
      });

//      listWithdraw.forEach((element) {
////        print(element.accountNumber);
//      print(element.updateTime);
//        print(element.type);
//      });
    });
    await getLanguage();
    _saving = false;
    return listWithdraw;
  }

  var language_code;
  getLanguage() async {
    var prefs = await SharedPreferences.getInstance();
    language_code = await prefs.getString('language_code');
    print(language_code);
  }

  onSearchTextChanged(String text1, String text2) async {
    String? text;
    String? textSearch;
    if (text1 == 'minus') {
      text = 'transaction';
      searchMinus.clear();
    }

    if (text1 == 'rewards') {
      text = 'Reward';
      searchRewards.clear();
    }
    if (text1 == 'plus') {
      textSearch = 'cash';
      text = 'transaction';
      searchPlus.clear();
    }
    if (text1.isEmpty && text2.isEmpty) {
      setState(() {});
      return;
    }
    if (text1 == 'minus') {
      listWithdraw.forEach((userDetail) {
        if (userDetail.type!.contains(text!) &&
            userDetail.accountNumber!.contains(text2))
          setState(() {
            searchMinus.add(userDetail);
          });
      });
    }
    if (text1 == 'plus') {
      listWithdraw.forEach((userDetail) {
        if (userDetail.type!.contains(text!) &&
                !userDetail.accountNumber!.contains(text2) ||
            userDetail.type!.contains(textSearch!))
          setState(() {
            searchPlus.add(userDetail);
          });
      });
    }
    if (text1 == 'rewards') {
      listWithdraw.forEach((userDetail) {
        if (userDetail.to!.contains(text!) &&
            !userDetail.accountNumber!.contains(text2))
          setState(() {
            searchRewards.add(userDetail);
          });
      });
    }
//    print(searchMinus);
    setState(() {});
  }

  List<ActivityLock?>? activityLock = [];

  getHistoryActivityLock() async {
    ActivityLockList? activityLockList;
    FirebaseAuth.getCurrentUser().then((value) async {
      var headers = {
        'x-api-key': 'z2x4VbpEt57VoXKysG8xl2tP45tvOQuy1bl0qErU',
      };
      var body = {
        "bag_bu": "LDX Digital Ecosystem Co., Ltd",
        "phone": value!.phoneNumber,
        "day": "30",
      };
      var bodyAPP = {
        "bag_bu": "LDX Digital Ecosystem Co., Ltd",
        // "phone": value.phoneNumber,
        "phone": value.phoneNumber,
        "user_transfer": "App",
        "day": "30"
      };
      var url =
          Uri.parse(env.apiLikepointBCT + '/puzzle/getHistoryBCTLockByPhone');
      var urlAPP =
          Uri.parse(env.apiLikepointBCT + '/puzzle/getHistoryClaimLockByPhone');
      try {
        final response = await http.post(url, body: body, headers: headers);
        final responseAPP =
            await http.post(urlAPP, body: bodyAPP, headers: headers);
        activityLock!.clear();
        if (response.statusCode == 200) {
          final body = json.decode(response.body);
          if (body['statusCode'] == 200) {
            print(body);
            activityLockList = ActivityLockList.fromJson(body['result']);
            // if (!mounted) return;
            setState(() {
              for (var i = 0; i < activityLockList!.data!.length; i++) {
                activityLock!.add(activityLockList!.data![i]);
              }
              print(activityLock);
            });
          } else if (body['statusCode'] == 400) {
            print('ไม่พบกิจกรรมจาก BCT');
          }
        } else {
          print('[HISTORY ACTIVITY CALL FAILED]');
        }
        if (responseAPP.statusCode == 200) {
          final bodyAPP = json.decode(responseAPP.body);
          print(bodyAPP);
          if (bodyAPP['statusCode'] == 200) {
            activityLockList = ActivityLockList.fromJson(bodyAPP['result']);
            // if (!mounted) return;
            setState(() {
              for (var i = 0; i < activityLockList!.data!.length; i++) {
                activityLock!.add(activityLockList!.data![i]);
              }
            });
          } else if (bodyAPP['statusCode'] == 400) {
            print('ไม่พบกิจกรรมจาก LDX');
          }
        } else {
          print('[HISTORY ACTIVITY APP CALL FAILED]');
        }
      } catch (e) {
        print(e.toString());
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    screenUtil(context);
    return ModalProgressHUD(
        opacity: 0.1,
        progressIndicator: CustomLoading(),
        inAsyncCall: _saving,
        child: DefaultTabController(
            length: 4,
            child: Scaffold(
              backgroundColor: LikeWalletAppTheme.white,
              body: _saving == true
                  ? Container()
                  : Stack(
                      children: <Widget>[
                        tabSelect == 1
                            ? _bodyHistory()
                            : Statement(
                                address: addressETH,
                              ),
                        _appBar(),
                        tabSelect == 1 ? _tabBarHistory() : _tabBarSatement(),
                      ],
                    ),
            )));
  }

  Widget _bodyHistory() {
    return Container(
      decoration: BoxDecoration(
        color: Color(0xffF5F5F5),
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          stops: [0.0, 0.2, 0.5],
          colors: [
            // Colors are easy thanks to Flutter's Colors class.
            Colors.white,
            Colors.white,
            LikeWalletAppTheme.white1
          ],
        ),
      ),
      margin: EdgeInsets.only(
        top: mediaQuery(context, 'hieght', 270),
      ),
      child: TabBarView(
          physics: NeverScrollableScrollPhysics(),
          controller: _tabController,
          children: [
            DelayedDisplay(
              fadingDuration: const Duration(milliseconds: 1000),
              slidingBeginOffset: const Offset(0.0, 1.0),
              child: _list(listWithdraw),
            ),
            // DelayedDisplay(
            //   fadingDuration: const Duration(milliseconds: 1000),
            //   slidingBeginOffset: const Offset(0.0, 1.0),
            //   child: _listActivityLock(activityLock),
            // ),
            DelayedDisplay(
              fadingDuration: const Duration(milliseconds: 1000),
              slidingBeginOffset: const Offset(0.0, 1.0),
              child: _list(searchRewards),
            ),
            DelayedDisplay(
              fadingDuration: const Duration(milliseconds: 1000),
              slidingBeginOffset: const Offset(0.0, 1.0),
              child: _list(searchPlus),
            ),
            DelayedDisplay(
              fadingDuration: const Duration(milliseconds: 1000),
              slidingBeginOffset: const Offset(0.0, 1.0),
              child: _list(searchMinus),
            ),
          ]),
    );
  }

  Widget _tabBarHistory() {
    return Positioned(
      top: mediaQuery(context, 'height', 295),
      child: Container(
        padding: EdgeInsets.only(
          top: mediaQuery(context, 'height', 9),
          bottom: mediaQuery(context, 'height', 9),
          left: mediaQuery(context, 'width', 36),
          right: mediaQuery(context, 'width', 36),
        ),
        alignment: Alignment.centerLeft,
        width: MediaQuery.of(context).size.width,
        height: mediaQuery(context, 'height', 156),
        child: TabBar(
          isScrollable: true,
          controller: _tabController,
          onTap: (number) {
            setState(() {
              _tabController.index = number;
            });
            if (_tabController.index == 0) {
              setInit();
            }
            // if (_tabController.index == 1) {
            //   getHistoryActivityLock();
            //   // onSearchTextChanged('rewards', addressETH);s
            // }
            if (_tabController.index == 1) {
              onSearchTextChanged('rewards', addressETH);
            }
            if (_tabController.index == 2) {
              onSearchTextChanged('plus', addressETH);
            }
            if (_tabController.index == 3) {
              onSearchTextChanged('minus', addressETH);
            }
          },
          indicatorColor: Colors.transparent,
          labelColor: Colors.transparent,
          unselectedLabelColor: Colors.transparent,
          tabs: [
            Container(
                margin: EdgeInsets.all(mediaQuery(context, 'height', 5)),
                alignment: Alignment.center,
                height: mediaQuery(context, 'height', 136),
                width: mediaQuery(context, 'width', 187),
                decoration: BoxDecoration(
                  // color:
                  //     : LikeWalletAppTheme.bule2_5,
                  borderRadius: BorderRadius.circular(84),
                  gradient: _tabController.index == 0
                      ? new RadialGradient(colors: [
                          LikeWalletAppTheme.white,
                          LikeWalletAppTheme.white,
                        ])
                      : new RadialGradient(
                          colors: [
                            LikeWalletAppTheme.bule2_5,
                            LikeWalletAppTheme.bule2_11,
                          ],
                        ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 2.0,
                      offset: const Offset(0.0, 1.0),
                    ),
                  ],
                ),
                child:
                    Text(AppLocalizations.of(context)!.translate('history_all'),
                        style: TextStyle(
                          color: _tabController.index == 0
                              ? LikeWalletAppTheme.black
                              : LikeWalletAppTheme.bule1_5,
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font1'),
                          fontWeight: FontWeight.normal,
                          fontSize: mediaQuery(context, 'height', 34),
                          letterSpacing: 0.3,
                        ))),
            // Container(
            //   margin: EdgeInsets.all(mediaQuery(context, 'height', 5)),
            //   alignment: Alignment.center,
            //   height: mediaQuery(context, 'height', 136),
            //   width: mediaQuery(context, 'width', 250),
            //   decoration: BoxDecoration(
            //     gradient: _tabController.index == 1
            //         ? new RadialGradient(colors: [
            //             LikeWalletAppTheme.white,
            //             LikeWalletAppTheme.white,
            //           ])
            //         : new RadialGradient(
            //             colors: [
            //               LikeWalletAppTheme.bule2_5,
            //               LikeWalletAppTheme.bule2_11,
            //             ],
            //           ),
            //     borderRadius: BorderRadius.circular(84),
            //     boxShadow: [
            //       BoxShadow(
            //         color: Colors.black.withOpacity(0.3),
            //         blurRadius: 2.0,
            //         offset: const Offset(0.0, 1.0),
            //       ),
            //     ],
            //   ),
            //   child: Text(
            //     AppLocalizations.of(context)!.translate('history_activity'),
            //     style: TextStyle(
            //       color: _tabController.index == 1
            //           ? LikeWalletAppTheme.black
            //           : LikeWalletAppTheme.bule1_5,
            //       fontFamily: AppLocalizations.of(context)!.translate('font1'),
            //       fontWeight: FontWeight.normal,
            //       fontSize: mediaQuery(context, 'height', 34),
            //       letterSpacing: 0.3,
            //     ),
            //   ),
            // ),
            Container(
              margin: EdgeInsets.all(mediaQuery(context, 'height', 5)),
              alignment: Alignment.center,
              height: mediaQuery(context, 'height', 136),
              width: mediaQuery(context, 'width', 187),
              decoration: BoxDecoration(
                gradient: _tabController.index == 1
                    ? new RadialGradient(colors: [
                        LikeWalletAppTheme.white,
                        LikeWalletAppTheme.white,
                      ])
                    : new RadialGradient(
                        colors: [
                          LikeWalletAppTheme.bule2_5,
                          LikeWalletAppTheme.bule2_11,
                        ],
                      ),
                borderRadius: BorderRadius.circular(84),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.3),
                    blurRadius: 2.0,
                    offset: const Offset(0.0, 1.0),
                  ),
                ],
              ),
              child: Text(
                AppLocalizations.of(context)!.translate('history_rewards'),
                style: TextStyle(
                  color: _tabController.index == 1
                      ? LikeWalletAppTheme.black
                      : LikeWalletAppTheme.bule1_5,
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  fontWeight: FontWeight.normal,
                  fontSize: mediaQuery(context, 'height', 34),
                  letterSpacing: 0.3,
                ),
              ),
            ),
            Container(
                padding: EdgeInsets.symmetric(horizontal: 10),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 2.0,
                      offset: const Offset(0.0, 1.0),
                    ),
                  ],
                  gradient: _tabController.index == 2
                      ? new RadialGradient(colors: [
                          LikeWalletAppTheme.white,
                          LikeWalletAppTheme.white,
                        ])
                      : new RadialGradient(
                          colors: [
                            LikeWalletAppTheme.bule2_5,
                            LikeWalletAppTheme.bule2_11,
                          ],
                        ),
                ),
                height: mediaQuery(context, 'height', 116),
                child: SvgPicture.asset(
                  LikeWalletImage.button_plus,
                  fit: BoxFit.contain,
                  height: mediaQuery(context, 'height', 65),
                  color: _tabController.index == 2
                      ? LikeWalletAppTheme.black
                      : LikeWalletAppTheme.bule1_5,
                )),
            Container(
                padding: EdgeInsets.symmetric(horizontal: 10),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 2.0,
                      offset: const Offset(0.0, 1.0),
                    ),
                  ],
                  gradient: _tabController.index == 3
                      ? new RadialGradient(colors: [
                          LikeWalletAppTheme.white,
                          LikeWalletAppTheme.white,
                        ])
                      : new RadialGradient(
                          colors: [
                            LikeWalletAppTheme.bule2_5,
                            LikeWalletAppTheme.bule2_11,
                          ],
                        ),
                ),
                alignment: Alignment.center,
                height: mediaQuery(context, 'height', 116),
                child: SvgPicture.asset(
                  LikeWalletImage.button_minus,
                  fit: BoxFit.contain,
                  height: mediaQuery(context, 'height', 65),
                  color: _tabController.index == 3
                      ? LikeWalletAppTheme.black
                      : LikeWalletAppTheme.bule1_5,
                )),
          ],
        ),
      ),
    );
  }

  Widget _tabBarSatement() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 534.78.w,
          height: 116.h,
          margin: EdgeInsets.only(
            top: 295.h,
          ),
          child: Stack(
            alignment: Alignment.center,
            children: [
              SvgPicture.string(
                '<svg viewBox="273.0 295.0 534.8 116.0" ><defs><filter id="shadow"><feDropShadow dx="0" dy="-12" stdDeviation="25"/></filter></defs><path transform="translate(462.11, 295.0)" d="M -131.2132568359375 0 L 287.76904296875 0 C 319.7453918457031 0 345.6673278808594 25.96748352050781 345.6673278808594 58 C 345.6673278808594 90.03251647949219 319.7453918457031 116 287.76904296875 116 L -131.2132568359375 116 C -163.1896057128906 116 -189.1115417480469 90.03251647949219 -189.1115417480469 58 C -189.1115417480469 25.96748352050781 -163.1896057128906 0 -131.2132568359375 0 Z" fill="#333443" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" filter="url(#shadow)"/></svg>',
                allowDrawingOutsideViewBox: true,
              ),
              Text(
                AppLocalizations.of(context)!.translate('statement_look'),
                style: TextStyle(
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  fontSize: 34.h,
                  color: Color(0xff08e8de),
                  letterSpacing: 1.5.sp,
                  shadows: [
                    Shadow(
                      color: const Color(0x29000000),
                      offset: Offset(0, 3.sp),
                      blurRadius: 6.sp,
                    )
                  ],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        )
      ],
    );
  }

  void _changeAlignment() {
    setState(() {
      _alignment = _alignment == Alignment.centerRight
          ? Alignment.centerLeft
          : Alignment.centerRight;
    });
  }

  Widget _appBar() {
    return Positioned(
      top: 0,
      child: Container(
        color: LikeWalletAppTheme.bule2_4,
        alignment: Alignment.center,
        width: MediaQuery.of(context).size.width,
        height: 360.h,
        child: Container(
            width: 902.0.w,
            height: 80.0.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(9.0.sp),
              border: Border.all(width: 2.0.sp, color: const Color(0x4dffffff)),
            ),
            child: Stack(
              children: [
                InkWell(
                  onTap: () => _changeAlignment(),
                  child: Container(
                    height: 120.0,
                    width: 902.0.w,
                    child: AnimatedAlign(
                      alignment: _alignment,
                      curve: Curves.ease,
                      duration: Duration(milliseconds: 300),
                      child: Container(
                        color: Color(0xff474652),
                        width: 900.0.w / 2,
                        height: 80.0.h,
                      ),
                    ),
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _tabSelect1(Alignment.centerRight, 'recent_transaction'),
                    _tabSelect2(Alignment.centerLeft, 'statement')
                  ],
                ),
              ],
            )),
      ),
    );
  }

  Widget _tabSelect1(index, text) {
    return Expanded(
      child: InkWell(
        onTap: () async {
          _changeAlignment();
          await Future.delayed(Duration(milliseconds: 150)).then((value) {
            setState(() => tabSelect = 1);
          });
        },
        child: Container(
          alignment: Alignment.center,
          height: double.infinity,
          child: Text(
            AppLocalizations.of(context)!.translate(text),
            style: TextStyle(
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              fontSize: 30.h,
              color: _alignment == Alignment.centerLeft
                  ? Color(0xff08e8de)
                  : Colors.white.withOpacity(0.5),
              letterSpacing: 1.5.sp,
              shadows: [
                Shadow(
                  color: const Color(0x29000000),
                  offset: Offset(0, 3.sp),
                  blurRadius: 6.sp,
                )
              ],
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  Widget _tabSelect2(index, text) {
    return Expanded(
      child: InkWell(
        onTap: () async {
          _changeAlignment();
          await Future.delayed(Duration(milliseconds: 150)).then((value) {
            setState(() => tabSelect = 2);
          });
        },
        child: Container(
          alignment: Alignment.center,
          height: double.infinity,
          child: Text(
            AppLocalizations.of(context)!.translate(text),
            style: TextStyle(
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              fontSize: 30.h,
              color: _alignment == Alignment.centerRight
                  ? Color(0xff08e8de)
                  : Colors.white.withOpacity(0.5),
              letterSpacing: 1.5.sp,
              shadows: [
                Shadow(
                  color: const Color(0x29000000),
                  offset: Offset(0, 3.sp),
                  blurRadius: 6.sp,
                )
              ],
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  Widget _list(listDate) {
    return Container(
      height: MediaQuery.of(context).size.height,
      padding: EdgeInsets.only(
        bottom: mediaQuery(context, 'height', 250),
      ),
      child: Stack(
        children: <Widget>[
          GroupedListView<dynamic, String>(
              groupBy: (element) => DateTime.fromMillisecondsSinceEpoch(
                      (element.updateTime) * 1000)
                  .toString()
                  .substring(0, 10),
              elements: listDate,
              order: GroupedListOrder.DESC,
              useStickyGroupSeparators: false,
              sort: false,
              groupSeparatorBuilder: (String value) {
                var diff = DateTime.parse(today.toString().substring(0, 10))
                    .difference(DateTime.parse(value));
                return Padding(
                  padding: EdgeInsets.only(
                    top: mediaQuery(context, 'height', 113),
                  ),
                  child: Text(
                    diff.inDays == 0
                        ? AppLocalizations.of(context)!
                            .translate('history_today')
                        : diff.inDays == 1
                            ? AppLocalizations.of(context)!
                                .translate('history_yesterday')
                            : DateFormat('dd MMMM yyyy', language_code)
                                .format(DateTime.parse(value)),
                    textAlign: TextAlign.center,
                    style: TextStyle(
                        color: LikeWalletAppTheme.gray4.withOpacity(0.6),
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                        fontSize: mediaQuery(
                            context, 'height', diff.inDays == 0 ? 40 : 36),
                        fontWeight: FontWeight.normal),
                  ),
                );
              },
              itemBuilder: (c, element) {
                print(element.to);
                return
                  // _tabController.index == 1
                  //   ? _bodyAutoLock(element) :
                  _body(element);
              }),
        ],
      ),
    );
  }

  Widget _listActivityLock(List<ActivityLock?>? listDate) {
    return Container(
      height: MediaQuery.of(context).size.height,
      padding: EdgeInsets.only(
        bottom: mediaQuery(context, 'height', 250),
      ),
      child: Stack(
        children: <Widget>[
          GroupedListView<dynamic, String>(
              groupBy: (element) {
                // print(element.createTime);
                return element.createTime.toString().substring(0, 10);
              },
              elements: listDate,
              order: GroupedListOrder.DESC,
              useStickyGroupSeparators: false,
              sort: true,
              groupSeparatorBuilder: (String value) {
                var diff = DateTime.parse(today.toString().substring(0, 10))
                    .difference(DateTime.parse(value));
                return Padding(
                  padding: EdgeInsets.only(
                    top: mediaQuery(context, 'height', 113),
                  ),
                  child: Text(
                    diff.inDays == 0
                        ? AppLocalizations.of(context)!
                            .translate('history_today')
                        : diff.inDays == 1
                            ? AppLocalizations.of(context)!
                                .translate('history_yesterday')
                            : DateFormat('dd MMMM yyyy', language_code)
                                .format(DateTime.parse(value)),
                    textAlign: TextAlign.center,
                    style: TextStyle(
                        color: LikeWalletAppTheme.gray4.withOpacity(0.6),
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                        fontSize: mediaQuery(
                            context, 'height', diff.inDays == 0 ? 40 : 36),
                        fontWeight: FontWeight.normal),
                  ),
                );
              },
              itemBuilder: (c, element) {
                // print(element.to);
                return _bodyAutoLock(element);
              }),
        ],
      ),
    );
  }

  Widget _bodyAutoLock(ActivityLock? index) {
    DateFormat dateFormat = DateFormat("yyyy-MM-dd HH:mm:ss");
    DateTime dateTimeActivity = dateFormat.parse(index!.createTime.toString());

    return Padding(
      padding: EdgeInsets.only(
        top: mediaQuery(context, 'height', 20),
      ),
      child: GestureDetector(
        onTap: () {
          DateFormat f = DateFormat("dd MMM yyyy", language_code);
          var rangeDayLock = (int.parse(index.timeConvert) / 86400);
          DateTime dateUnLock =
              dateTimeActivity.add(Duration(days: rangeDayLock.toInt()));
          DateTime now = DateTime.now();
          var amountDay = now.difference(dateTimeActivity);
          var balanceDay = rangeDayLock.toInt() - amountDay.inDays;

          Navigator.push(
              context,
              CupertinoPageRoute(
                  builder: (context) => AutoLockDetail(
                        point: index.point.toString(),
                        dayLock: f.format(dateTimeActivity).toString(),
                        rangeLock: rangeDayLock.toInt().toString(),
                        dayUnLock: f.format(dateUnLock).toString(),
                        amountDayLock: amountDay.inDays < 0
                            ? "0"
                            : amountDay.inDays.toString(),
                        balanceDayLock:
                            balanceDay < 0 ? "0" : balanceDay.toString(),
                      )));
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.0),
            color: LikeWalletAppTheme.white,
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                blurRadius: 5.0,
                offset: const Offset(0.0, 0.0),
              ),
            ],
          ),
          height: mediaQuery(context, 'height', 166),
          width: mediaQuery(context, 'width', 999),
          padding: EdgeInsets.only(
              right: mediaQuery(context, 'width', 57),
              left: mediaQuery(context, 'width', 57)),
          margin: EdgeInsets.only(
            right: mediaQuery(context, 'width', 40),
            left: mediaQuery(context, 'width', 40),
            bottom: mediaQuery(context, 'height', 10),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Row(
                children: <Widget>[
                  Container(
                      margin: EdgeInsets.only(
                        right: mediaQuery(context, 'width', 50),
                      ),
                      child: Image.asset(LikeWalletImage.locklike_icon_unlock,
                          fit: BoxFit.fill,
                          height: mediaQuery(context, 'height', 66))),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Container(
                        child: Text(
                          'จากกิจกรรม',
                          overflow: TextOverflow.clip,
                          style: TextStyle(
                              color: LikeWalletAppTheme.black.withOpacity(0.9),
                              letterSpacing: 0.3,
                              fontFamily: AppLocalizations.of(context)!
                                  .translate('font1'),
                              fontSize: mediaQuery(context, 'height', 39)),
                        ),
                      ),
                      // SizedBox(height: 5),
                      Text(
                        index.point.toString() + "    " + "LIKE",
                        overflow: TextOverflow.clip,
                        style: TextStyle(
                            color: LikeWalletAppTheme.gray3.withOpacity(0.9),
                            letterSpacing: 0.3,
                            fontWeight: FontWeight.w100,
                            fontFamily: AppLocalizations.of(context)!
                                .translate('font1'),
                            fontSize: mediaQuery(context, 'height', 39)),
                      ),
                    ],
                  )
                ],
              ),
              Expanded(
                child: Container(),
              ),
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: <Widget>[
                  Container(
                    width: 400.w,
                    alignment: Alignment.centerRight,
                    child: Text(
                      index.nameActivity.toString(),
                      style: TextStyle(
                          color: LikeWalletAppTheme.black.withOpacity(0.9),
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font1'),
                          fontSize: mediaQuery(context, 'height', 39)),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Container(
                    width: 400.w,
                    alignment: Alignment.centerRight,
                    child: Text(
                      DateFormat("HH:mm a").format(dateTimeActivity).toString(),
                      style: TextStyle(
                          color: LikeWalletAppTheme.gray3.withOpacity(0.9),
                          letterSpacing: 0.3,
                          fontWeight: FontWeight.w100,
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font1'),
                          fontSize: mediaQuery(context, 'height', 39)),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  // Text(
                  //   //+190 is timezone +7
                  //   index.transferTime.substring(10, 19),
                  //   style: TextStyle(
                  //       color: LikeWalletAppTheme.gray3.withOpacity(0.9),
                  //       letterSpacing: 0.3,
                  //       fontWeight: FontWeight.w100,
                  //       fontFamily:
                  //           AppLocalizations.of(context)!.translate('font1'),
                  //       fontSize: mediaQuery(context, 'height', 39)),
                  // ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _body(index) {
    return Padding(
      padding: EdgeInsets.only(
        top: mediaQuery(context, 'height', 20),
      ),
      child: GestureDetector(
        onTap: () {
          setState(() {
            index.isExpanded = !index.isExpanded;
          });
        },
        child: index.isExpanded
            ? Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8.0),
                  color: index.isExpanded
                      ? Colors.transparent
                      : LikeWalletAppTheme.white,
                ),
                // height: mediaQuery(context, 'height', 250),
                // width: mediaQuery(context, 'width', 999),
                margin: EdgeInsets.only(
                    right: mediaQuery(context, 'width', 98),
                    left: mediaQuery(context, 'width', 98)),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
//                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: <Widget>[
                              Container(
                                margin: EdgeInsets.only(
                                  top: mediaQuery(context, 'height', 40),
                                  right: mediaQuery(context, 'width', 25),
                                ),
                                child: index.type == 'cash'
                                    ? SvgPicture.asset(
                                        LikeWalletImage.icon_minus,
                                        fit: BoxFit.fill,
                                        height:
                                            mediaQuery(context, 'height', 66))
                                    : index.type == "transaction"
                                        ? index.accountNumber == addressETH
                                            ? SvgPicture.asset(
                                                LikeWalletImage.icon_minus,
                                                fit: BoxFit.fill,
                                                height: mediaQuery(
                                                    context, 'height', 66))
                                            : SvgPicture.asset(
                                                LikeWalletImage.icon_plus,
                                                height: mediaQuery(
                                                    context, 'height', 66))
                                        : SvgPicture.asset(
                                            LikeWalletImage.icon_minus,
                                            height: mediaQuery(
                                                context, 'height', 66)),
                              ),
                              Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: <Widget>[
                                  Text(
                                    index.accountNumber == addressETH
                                        ? AppLocalizations.of(context)!
                                            .translate('history_send')
                                        : AppLocalizations.of(context)!
                                            .translate('history_received'),
                                    style: TextStyle(
                                        color: LikeWalletAppTheme.black
                                            .withOpacity(0.9),
                                        letterSpacing: 0.3,
                                        fontFamily:
                                            AppLocalizations.of(context)!
                                                .translate('font1'),
                                        fontSize:
                                            mediaQuery(context, 'height', 39)),
                                  ),
                                  Row(
                                    children: <Widget>[
                                      Text(
                                        index.type == 'cash'
                                            ? 'Withdraw ' +
                                                formatNum.format(
                                                    double.parse(index.baht))
                                            : index.type == 'transaction'
                                                ? index.accountNumber ==
                                                        addressETH
                                                    ? formatNum.format(
                                                        double.parse(
                                                            index.baht))
                                                    : formatNum.format(
                                                        double.parse(
                                                            index.baht))
                                                : 'Nothing',
                                        style: TextStyle(
                                            color: LikeWalletAppTheme.gray3
                                                .withOpacity(0.9),
                                            letterSpacing: 0.3,
                                            fontFamily:
                                                AppLocalizations.of(context)!
                                                    .translate('font1'),
                                            fontSize: mediaQuery(
                                                context, 'height', 36)),
                                      ),
                                      SizedBox(
                                        width: mediaQuery(context, 'width', 25),
                                      ),
                                      Text(
                                        index.type == 'cash'
                                            ? ' BAHT'
                                            : index.type == 'transaction'
                                                ? index.accountNumber ==
                                                        addressETH
                                                    ? AppLocalizations.of(
                                                            context)!
                                                        .translate(
                                                            'history_like')
                                                    : AppLocalizations.of(
                                                            context)!
                                                        .translate(
                                                            'history_like')
                                                : 'Nothing',
                                        style: TextStyle(
                                            color: LikeWalletAppTheme.gray3
                                                .withOpacity(0.9),
                                            letterSpacing: 0.3,
                                            fontWeight: FontWeight.w100,
                                            fontFamily:
                                                AppLocalizations.of(context)!
                                                    .translate('font1'),
                                            fontSize: mediaQuery(
                                                context, 'height', 36)),
                                      ),
                                      SizedBox(
                                        width: mediaQuery(context, 'width', 20),
                                      ),
                                    ],
                                  ),
                                  index.bankName == 'sendLike'
                                      ? GestureDetector(
                                          onTap: () {
                                            Navigator.push(
                                                context,
                                                MaterialPageRoute(
                                                  builder: (context) =>
                                                      WebOpen(txid: index.tx),
                                                ));
                                          },
                                          child: Text(
                                            AppLocalizations.of(context)!
                                                .translate(
                                                    'history_transaction_success'),
                                            style: TextStyle(
                                                color: LikeWalletAppTheme.green
                                                    .withOpacity(0.9),
                                                letterSpacing: 0.3,
                                                fontFamily: AppLocalizations.of(
                                                        context)!
                                                    .translate('font1'),
                                                fontSize: mediaQuery(
                                                    context, 'height', 39)),
                                          ))
                                      : index.status == '3'
                                          ? GestureDetector(
                                              onTap: () {
                                                Navigator.push(
                                                    context,
                                                    MaterialPageRoute(
                                                      builder: (context) =>
                                                          WebOpenURL(
                                                        url: index.slip,
                                                      ),
                                                    ));
                                              },
                                              child: Text(
                                                AppLocalizations.of(context)!
                                                    .translate(
                                                        'history_transaction_success'),
                                                style: TextStyle(
                                                    color: LikeWalletAppTheme
                                                        .green
                                                        .withOpacity(0.9),
                                                    letterSpacing: 0.3,
                                                    fontFamily:
                                                        AppLocalizations.of(
                                                                context)!
                                                            .translate('font1'),
                                                    fontSize: mediaQuery(
                                                        context, 'height', 39)),
                                              ))
                                          : Text(
                                              AppLocalizations.of(context)!
                                                  .translate(
                                                      'history_transaction'),
                                              style: TextStyle(
                                                  color: LikeWalletAppTheme
                                                      .bule1_5
                                                      .withOpacity(0.9),
                                                  letterSpacing: 0.3,
                                                  fontFamily:
                                                      AppLocalizations.of(
                                                              context)!
                                                          .translate('font1'),
                                                  fontSize: mediaQuery(
                                                      context, 'height', 39)),
                                            ),
                                ],
                              )
                            ],
                          ),
                        ),
                        Expanded(
                          child: Container(),
                        ),
                        Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: <Widget>[
                            Container(
                              width: 400.w,
                              alignment: Alignment.centerRight,
                              child: Text(
                                index.title == 'recv'
                                    ? index.to.toString() == 'Reward'
                                        ? AppLocalizations.of(context)!
                                                .translate('history_from') +
                                            " " +
                                            AppLocalizations.of(context)!
                                                .translate('history_reward')
                                        : index.to.toString() == 'no'
                                            ? AppLocalizations.of(context)!
                                                    .translate('history_from') +
                                                " " +
                                                AppLocalizations.of(context)!
                                                    .translate('history_no')
                                            : AppLocalizations.of(context)!
                                                    .translate('history_to') +
                                                " " +
                                                index.to.toString()
                                    : index.to.toString() == 'Lock'
                                        ? AppLocalizations.of(context)!
                                                .translate('history_to') +
                                            " " +
                                            AppLocalizations.of(context)!
                                                .translate('history_lock')
                                        : index.to == 'Pay'
                                            ? AppLocalizations.of(context)!
                                                    .translate('history_to') +
                                                " " +
                                                AppLocalizations.of(context)!
                                                    .translate('history_pay')
                                            : AppLocalizations.of(context)!
                                                    .translate('history_to') +
                                                " " +
                                                index.to.toString(),
                                style: TextStyle(
                                    color: LikeWalletAppTheme.black
                                        .withOpacity(0.9),
                                    fontFamily: AppLocalizations.of(context)!
                                        .translate('font1'),
                                    fontSize:
                                        mediaQuery(context, 'height', 39)),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            Text(
                              //+190 is timezone +7
                              " " +
                                  DateFormat("HH:mm a")
                                      .format(
                                          DateTime.fromMillisecondsSinceEpoch(
                                              (index.updateTime) * 1000))
                                      .toString(),
                              style: TextStyle(
                                  color:
                                      LikeWalletAppTheme.gray3.withOpacity(0.9),
                                  letterSpacing: 0.3,
                                  fontWeight: FontWeight.w100,
                                  fontFamily: AppLocalizations.of(context)!
                                      .translate('font1'),
                                  fontSize: mediaQuery(context, 'height', 39)),
                            ),
                            Row(
                              children: [
                                Text(
                                  //+190 is timezone +7
                                  DateFormat("dd MMMM yyyy", language_code)
                                      .format(
                                          DateTime.fromMillisecondsSinceEpoch(
                                              (index.updateTime) * 1000))
                                      .toString(),
                                  style: TextStyle(
                                      color: LikeWalletAppTheme.gray3
                                          .withOpacity(0.9),
                                      letterSpacing: 0.3,
                                      fontWeight: FontWeight.w100,
                                      fontFamily: AppLocalizations.of(context)!
                                          .translate('font1'),
                                      fontSize:
                                          mediaQuery(context, 'height', 39)),
                                ),
                              ],
                            )
                          ],
                        ),
                      ],
                    ), // Tex
                    Container(
                      padding: EdgeInsets.only(
                        left: mediaQuery(context, 'width', 90),
                      ),
                      child: Text(
                        index.bankName == 'sendLike'
                            ? AppLocalizations.of(context)!
                                    .translate('message_note') +
                                ': ' +
                                index.message
                            : AppLocalizations.of(context)!
                                    .translate('message_note') +
                                ': ' +
                                ' ',
                        style: TextStyle(
                            color: LikeWalletAppTheme.gray3.withOpacity(0.9),
                            letterSpacing: 0.3,
                            fontWeight: FontWeight.w100,
                            fontFamily: AppLocalizations.of(context)!
                                .translate('font1'),
                            fontSize: mediaQuery(context, 'height', 39)),
                      ),
                    ),
                  ],
                ))
            : Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8.0),
                  color: index.isExpanded
                      ? Colors.transparent
                      : LikeWalletAppTheme.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.1),
                      blurRadius: 5.0,
                      offset: const Offset(0.0, 0.0),
                    ),
                  ],
                ),
                height: mediaQuery(context, 'height', 166),
                width: mediaQuery(context, 'width', 999),
                padding: EdgeInsets.only(
                    right: mediaQuery(context, 'width', 57),
                    left: mediaQuery(context, 'width', 57)),
                margin: EdgeInsets.only(
                  right: mediaQuery(context, 'width', 40),
                  left: mediaQuery(context, 'width', 40),
                  bottom: mediaQuery(context, 'height', 10),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    Row(
                      children: <Widget>[
                        Container(
                          margin: EdgeInsets.only(
                            right: mediaQuery(context, 'width', 25),
                          ),
                          child: index.type == 'cash'
                              ? SvgPicture.asset(LikeWalletImage.icon_minus,
                                  fit: BoxFit.fill,
                                  height: mediaQuery(context, 'height', 66))
                              : index.type == "transaction"
                                  ? index.accountNumber == addressETH
                                      ? SvgPicture.asset(
                                          LikeWalletImage.icon_minus,
                                          fit: BoxFit.fill,
                                          height:
                                              mediaQuery(context, 'height', 66))
                                      : SvgPicture.asset(
                                          LikeWalletImage.icon_plus,
                                          height:
                                              mediaQuery(context, 'height', 66))
                                  : SvgPicture.asset(LikeWalletImage.icon_minus,
                                      height:
                                          mediaQuery(context, 'height', 66)),
                        ),
                        Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            Text(
                              index.accountNumber == addressETH
                                  ? AppLocalizations.of(context)!
                                      .translate('history_send')
                                  : index.type == 'cash'
                                      ? AppLocalizations.of(context)!
                                          .translate('history_cashout')
                                      : AppLocalizations.of(context)!
                                          .translate('history_received'),
                              style: TextStyle(
                                  color:
                                      LikeWalletAppTheme.black.withOpacity(0.9),
                                  letterSpacing: 0.3,
                                  fontFamily: AppLocalizations.of(context)!
                                      .translate('font1'),
                                  fontSize: mediaQuery(context, 'height', 39)),
                            ),
                            Row(
                              children: <Widget>[
                                Text(
                                  index.type == 'cash'
                                      ? AppLocalizations.of(context)!
                                              .translate('history_withdraw') +
                                          " " +
                                          formatNum
                                              .format(double.parse(index.baht))
                                      : index.type == 'transaction'
                                          ? index.accountNumber == addressETH
                                              ? formatNum.format(
                                                  double.parse(index.baht))
                                              : formatNum.format(
                                                  double.parse(index.baht))
                                          : 'Nothing',
                                  style: TextStyle(
                                      color: LikeWalletAppTheme.gray3
                                          .withOpacity(0.9),
                                      letterSpacing: 0.3,
                                      fontFamily: AppLocalizations.of(context)!
                                          .translate('font1'),
                                      fontSize:
                                          mediaQuery(context, 'height', 36)),
                                ),
                                SizedBox(
                                  width: mediaQuery(context, 'width', 25),
                                ),
                                Text(
                                  index.type == 'cash'
                                      ? AppLocalizations.of(context)!
                                          .translate('bankingbuy_symbol')
                                      : index.type == 'transaction'
                                          ? index.accountNumber == addressETH
                                              ? ' LIKE'
                                              : ' LIKE'
                                          : 'Nothing',
                                  style: TextStyle(
                                      color: LikeWalletAppTheme.gray3
                                          .withOpacity(0.9),
                                      letterSpacing: 0.3,
                                      fontWeight: FontWeight.w100,
                                      fontFamily: AppLocalizations.of(context)!
                                          .translate('font1'),
                                      fontSize:
                                          mediaQuery(context, 'height', 36)),
                                ),
                                SizedBox(
                                  width: mediaQuery(context, 'width', 20),
                                ),
                              ],
                            )
                          ],
                        )
                      ],
                    ),
                    Expanded(
                      child: Container(),
                    ),
                    Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: <Widget>[
                        Container(
                          width: 400.w,
                          alignment: Alignment.centerRight,
                          child: Text(
                            index.title == 'recv'
                                ? index.to == 'Reward'
                                    ? AppLocalizations.of(context)!
                                            .translate('history_from') +
                                        " " +
                                        AppLocalizations.of(context)!
                                            .translate('history_reward')
                                    : index.to.toString() == 'no'
                                        ? AppLocalizations.of(context)!
                                                .translate('history_from') +
                                            " " +
                                            AppLocalizations.of(context)!
                                                .translate('history_no')
                                        : AppLocalizations.of(context)!
                                                .translate('history_from') +
                                            " " +
                                            index.to.toString()
                                : index.to.toString() == 'Lock'
                                    ? AppLocalizations.of(context)!
                                            .translate('history_to') +
                                        " " +
                                        AppLocalizations.of(context)!
                                            .translate('history_lock')
                                    : index.to.toString() == 'Pay'
                                        ? AppLocalizations.of(context)!
                                                .translate('history_to') +
                                            " " +
                                            AppLocalizations.of(context)!
                                                .translate('history_pay')
                                        : AppLocalizations.of(context)!
                                                .translate('history_to') +
                                            " " +
                                            index.to.toString(),
                            style: TextStyle(
                                color:
                                    LikeWalletAppTheme.black.withOpacity(0.9),
                                fontFamily: AppLocalizations.of(context)!
                                    .translate('font1'),
                                fontSize: mediaQuery(context, 'height', 39)),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Text(
                          //+190 is timezone +7
                          DateFormat("HH:mm a","en_US")
                              .format(DateTime.fromMillisecondsSinceEpoch(
                                  (index.updateTime) * 1000))
                              .toString(),
                          style: TextStyle(
                              color: LikeWalletAppTheme.gray3.withOpacity(0.9),
                              letterSpacing: 0.3,
                              fontWeight: FontWeight.w100,
                              fontFamily: AppLocalizations.of(context)!
                                  .translate('font1'),
                              fontSize: mediaQuery(context, 'height', 39)),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
      ),
    );
  }

//  Widget _body(index) {
//    return GestureDetector(
//      onTap: () {
//        setState(() {
//          index.isExpanded = !index.isExpanded;
//        });
//      },
//      child: Card(
//        color: Colors.transparent,
//        shape: RoundedRectangleBorder(
//          borderRadius: const BorderRadius.all(
//            Radius.circular(10.0),
//          ),
//        ),
//        elevation: 5.0,
//        margin: new EdgeInsets.symmetric(horizontal: 10.0, vertical: 6.0),
//        child: CustomExpansionPanelList(
//          animationDuration: Duration(milliseconds: 500),
//          children: [
//            ExpansionPanel(
//              body: Container(
//                color: Colors.transparent,
//                padding: EdgeInsets.only(
//                  right: mediaQuery(context, 'width', 60),
//                  left: mediaQuery(context, 'width', 60),
//                ),
//                child: Row(
//                  children: <Widget>[
//                    Text(
//                      index.type == 'cash'
//                          ? 'send to '
//                          : index.type == "transaction"
//                              ? index.accountNumber == addressETH
//                                  ? 'send to '
//                                  : 'get like'
//                              : 'send to ',
//                      style: TextStyle(
//                          color: LikeWalletAppTheme.black.withOpacity(0.9),
//                          fontFamily:
//                              AppLocalizations.of(context)!.translate('font1'),
//                          fontSize: mediaQuery(context, 'height', 41)),
//                    ),
//                  ],
//                ),
//              ),
//              headerBuilder: (BuildContext context, bool isExpanded) {
//                return Container(
//                  decoration: BoxDecoration(
//                    borderRadius: BorderRadius.circular(8.0),
//                    color: index.isExpanded
//                        ? Colors.transparent
//                        : LikeWalletAppTheme.white,
//                  ),
//                  padding: EdgeInsets.only(
//                      right: mediaQuery(context, 'width', 60),
//                      left: mediaQuery(context, 'width', 60)),
//                  child: Row(
//                    mainAxisAlignment: MainAxisAlignment.center,
//                    children: <Widget>[
//                      Container(
//                        decoration: BoxDecoration(
//                          borderRadius: BorderRadius.circular(8.0),
//                        ),
//                        width: mediaQuery(context, 'width', 400),
//                        child: Row(
////                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                          children: <Widget>[
//                            Container(
//                              margin: EdgeInsets.only(
//                                right: mediaQuery(context, 'width', 25),
//                              ),
//                              child: index.type == 'cash'
//                                  ? SvgPicture.asset(LikeWalletImage.icon_minus,
//                                      fit: BoxFit.fill,
//                                      height: mediaQuery(context, 'height', 66))
//                                  : index.type == "transaction"
//                                      ? index.accountNumber == addressETH
//                                          ? SvgPicture.asset(
//                                              LikeWalletImage.icon_minus,
//                                              fit: BoxFit.fill,
//                                              height: mediaQuery(
//                                                  context, 'height', 66))
//                                          : SvgPicture.asset(
//                                              LikeWalletImage.icon_plus,
//                                              height: mediaQuery(
//                                                  context, 'height', 66))
//                                      : SvgPicture.asset(
//                                          LikeWalletImage.icon_minus,
//                                          height: mediaQuery(
//                                              context, 'height', 66)),
//                            ),
//                            Column(
//                              mainAxisAlignment: MainAxisAlignment.center,
//                              crossAxisAlignment: CrossAxisAlignment.start,
//                              children: <Widget>[
//                                Text(
//                                  index.type == 'cash'
//                                      ? 'Sent'
//                                      : index.type == 'transaction'
//                                          ? 'Received'
//                                          : 'Nothing',
//                                  style: TextStyle(
//                                      color: LikeWalletAppTheme.black
//                                          .withOpacity(0.9),
//                                      letterSpacing: 0.3,
//                                      fontFamily: AppLocalizations.of(context)
//                                          .translate('font1'),
//                                      fontSize:
//                                          mediaQuery(context, 'height', 39)),
//                                ),
//                                Row(
//                                  children: <Widget>[
//                                    Text(
//                                      index.type == 'cash'
//                                          ? 'Withdraw ' +
//                                              formatNum.format(
//                                                  double.parse(index.baht))
//                                          : index.type == 'transaction'
//                                              ? index.accountNumber ==
//                                                      addressETH
//                                                  ? formatNum.format(
//                                                      double.parse(index.baht))
//                                                  : formatNum.format(
//                                                      double.parse(index.baht))
//                                              : 'Nothing',
//                                      style: TextStyle(
//                                          color: LikeWalletAppTheme.gray3
//                                              .withOpacity(0.9),
//                                          letterSpacing: 0.3,
//                                          fontFamily:
//                                              AppLocalizations.of(context)
//                                                  .translate('font1'),
//                                          fontSize: mediaQuery(
//                                              context, 'height', 36)),
//                                    ),
//                                    SizedBox(
//                                      width: mediaQuery(context, 'width', 25),
//                                    ),
//                                    Text(
//                                      index.type == 'cash'
//                                          ? ' BAHT'
//                                          : index.type == 'transaction'
//                                              ? index.accountNumber ==
//                                                      addressETH
//                                                  ? ' LIKE'
//                                                  : ' LIKE'
//                                              : 'Nothing',
//                                      style: TextStyle(
//                                          color: LikeWalletAppTheme.gray3
//                                              .withOpacity(0.9),
//                                          letterSpacing: 0.3,
//                                          fontWeight: FontWeight.w100,
//                                          fontFamily:
//                                              AppLocalizations.of(context)
//                                                  .translate('font1'),
//                                          fontSize: mediaQuery(
//                                              context, 'height', 36)),
//                                    ),
//                                    SizedBox(
//                                      width: mediaQuery(context, 'width', 20),
//                                    ),
//                                  ],
//                                )
//                              ],
//                            )
//                          ],
//                        ),
//                      ),
////                                                        snapshot.data[index]
////                                                                    .type ==
////                                                                'cash'
////                                                            ? snapshot.data[index].status ==
////                                                                        '1' ||
////                                                                    snapshot
////                                                                            .data[
////                                                                                index]
////                                                                            .status ==
////                                                                        '2'
////                                                                ? Icon(
////                                                                    Icons
////                                                                        .history,
////                                                                    color: Colors
////                                                                        .black12,
////                                                                  )
////                                                                : snapshot
////                                                                            .data[
////                                                                                index]
////                                                                            .status ==
////                                                                        '3'
////                                                                    ? Icon(
////                                                                        Icons
////                                                                            .check_circle,
////                                                                        color: Colors
////                                                                            .green,
////                                                                      )
////                                                                    : Icon(
////                                                                        Icons
////                                                                            .cancel,
////                                                                        color: Colors
////                                                                            .red,
////                                                                      )
////                                                            : snapshot
////                                                                        .data[
////                                                                            index]
////                                                                        .type ==
////                                                                    'transaction'
////                                                                ? Icon(
////                                                                    Icons
////                                                                        .check_circle,
////                                                                    color: Colors
////                                                                        .green)
////                                                                : Icon(
////                                                                    Icons
////                                                                        .history,
////                                                                    color: Colors
////                                                                        .black12,
////                                                                  ),
//                      Expanded(
//                        child: Container(),
//                      ),
//                      Column(
//                        mainAxisAlignment: MainAxisAlignment.center,
//                        crossAxisAlignment: CrossAxisAlignment.end,
//                        children: <Widget>[
//                          Text(
//                            'to Kajohn Thovsagul',
//                            style: TextStyle(
//                                color:
//                                    LikeWalletAppTheme.black.withOpacity(0.9),
//                                fontFamily: AppLocalizations.of(context)
//                                    .translate('font1'),
//                                fontSize: mediaQuery(context, 'height', 39)),
//                          ),
//                          Text(
//                            //+190 is timezone +7
//                            DateFormat("HH:mm a")
//                                .format(DateTime.fromMillisecondsSinceEpoch(
//                                    (index.updateTime + 190) * 1000))
//                                .toString(),
//                            style: TextStyle(
//                                color:
//                                    LikeWalletAppTheme.gray3.withOpacity(0.9),
//                                letterSpacing: 0.3,
//                                fontWeight: FontWeight.w100,
//                                fontFamily: AppLocalizations.of(context)
//                                    .translate('font1'),
//                                fontSize: mediaQuery(context, 'height', 39)),
//                          ),
//                        ],
//                      ),
//                    ],
//                  ),
//                );
//              },
//              isExpanded: index.isExpanded,
//            ),
//          ],
//          expansionCallback: (int panelIndex, bool isExpanded) {
//            setState(() {
//              if (id == -1) {
//              } else {
//                index.isExpanded = false;
//              }
//              index.isExpanded = !index.isExpanded;
//              id = index;
//            });
//          },
//        ),
//      ),
//    );
//  }
}

//URL
class WebOpenURL extends StatefulWidget {
  WebOpenURL({this.url});
  final String? url;
  @override
  _WebOpenURL createState() => _WebOpenURL(url: url);
}

class _WebOpenURL extends State<WebOpenURL> {
  _WebOpenURL({this.url});
  final String? url;

  final Completer<WebViewController> _controller =
      Completer<WebViewController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
//      appBar: AppBar(
//        backgroundColor: Colors.transparent,
//        title: const Text('Terms & Policy'),
//        // This drop down menu demonstrates that Flutter widgets can be shown over the web view.
//
//      ),
      // We're using a Builder here so we have a context that is below the Scaffold
      // to allow calling Scaffold.of(context) so we can show a snackbar.
      body: Builder(builder: (BuildContext context) {
        return WebView(
          initialUrl: url,
          javascriptMode: JavascriptMode.unrestricted,
          onWebViewCreated: (WebViewController webViewController) {
            _controller.complete(webViewController);
          },
          // TODO(iskakaushik): Remove this when collection literals makes it to stable.
          // ignore: prefer_collection_literals
          // javascriptChannels: <JavascriptChannel>[
          //   _toasterJavascriptChannel(context),
          // ].toSet(),
          navigationDelegate: (NavigationRequest request) {
            if (request.url.startsWith('https://www.youtube.com/')) {
              print('blocking navigation to $request}');
              return NavigationDecision.prevent;
            }
            print('allowing navigation to $request');
            return NavigationDecision.navigate;
          },
          onPageStarted: (String url) {
            print('Page started loading: $url');
          },
          onPageFinished: (String url) {
            print('Page finished loading: $url');
          },
          gestureNavigationEnabled: true,
        );
      }),
      floatingActionButton: favoriteButton(),
    );
  }

  // JavascriptChannel _toasterJavascriptChannel(BuildContext context) {
  //   return JavascriptChannel(
  //       name: 'Toaster',
  //       onMessageReceived: (JavascriptMessage message) {
  //         Scaffold.of(context).showSnackBar(
  //           SnackBar(content: Text(message.message)),
  //         );
  //       });
  // }

  Widget favoriteButton() {
    return FutureBuilder<WebViewController>(
        future: _controller.future,
        builder: (BuildContext context,
            AsyncSnapshot<WebViewController> controller) {
          if (controller.hasData) {
            return FloatingActionButton(
              onPressed: () async {
                Navigator.pop(context);
              },
              child: const Icon(Icons.arrow_back_ios),
            );
          }
          return Container();
        });
  }
}

//URL
class WebOpen extends StatefulWidget {
  WebOpen({this.txid});
  final String? txid;
  @override
  _WebOpen createState() => _WebOpen(txid: txid);
}

class _WebOpen extends State<WebOpen> {
  _WebOpen({this.txid});
  final String? txid;

  final Completer<WebViewController> _controller =
      Completer<WebViewController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
//      appBar: AppBar(
//        backgroundColor: Colors.transparent,
//        title: const Text('Terms & Policy'),
//        // This drop down menu demonstrates that Flutter widgets can be shown over the web view.
//
//      ),
      // We're using a Builder here so we have a context that is below the Scaffold
      // to allow calling Scaffold.of(context) so we can show a snackbar.
      body: Builder(builder: (BuildContext context) {
        return WebView(
          initialUrl: 'https://scan.tomochain.com/txs/' + txid.toString(),
          javascriptMode: JavascriptMode.unrestricted,
          onWebViewCreated: (WebViewController webViewController) {
            _controller.complete(webViewController);
          },
          // TODO(iskakaushik): Remove this when collection literals makes it to stable.
          // ignore: prefer_collection_literals
          javascriptChannels: <JavascriptChannel>[
            _toasterJavascriptChannel(context),
          ].toSet(),
          navigationDelegate: (NavigationRequest request) {
            if (request.url.startsWith('https://www.youtube.com/')) {
              print('blocking navigation to $request}');
              return NavigationDecision.prevent;
            }
            print('allowing navigation to $request');
            return NavigationDecision.navigate;
          },
          onPageStarted: (String url) {
            print('Page started loading: $url');
          },
          onPageFinished: (String url) {
            print('Page finished loading: $url');
          },
          gestureNavigationEnabled: true,
        );
      }),
      floatingActionButton: favoriteButton(),
    );
  }

  JavascriptChannel _toasterJavascriptChannel(BuildContext context) {
    return JavascriptChannel(
        name: 'Toaster',
        onMessageReceived: (JavascriptMessage message) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(message.message)),
          );
        });
  }

  Widget favoriteButton() {
    return FutureBuilder<WebViewController>(
        future: _controller.future,
        builder: (BuildContext context,
            AsyncSnapshot<WebViewController> controller) {
          if (controller.hasData) {
            return FloatingActionButton(
              onPressed: () async {
                Navigator.pop(context);
              },
              child: const Icon(Icons.arrow_back_ios),
            );
          }
          return Container();
        });
  }
}

class Websites {
  bool _isExpanded;
  List<String> contents = [];
  Websites(this._isExpanded, this.contents);
}

List<Websites> policies = [
  new Websites(false, [
    'Flutter',
  ]),
  new Websites(false, ['Android'])
];
