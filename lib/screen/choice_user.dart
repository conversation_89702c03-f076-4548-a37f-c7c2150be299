import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:likewallet/screen/home.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:likewallet/login/secret_key.dart';
import 'package:likewallet/libraryman/auth.dart' as firebaseEmail;
import 'package:likewallet/login/sing_in.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:likewallet/app_config.dart';
import 'package:http/http.dart' as http;
import 'package:likewallet/libraryman/auth.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:likewallet/libraryman/configuration_service.dart';
import 'package:likewallet/libraryman/address_service.dart';
import 'package:likewallet/libraryman/crypto.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/libraryman/secret_pass.dart';
import 'package:likewallet/Theme.dart';

import 'package:likewallet/screen/index.dart';

import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/screen_util.dart';

class ChoiceUser extends StatefulWidget {
  ChoiceUser(
      {this.auth,
      this.secret,
      this.roundSMS,
      this.phoneNumber,
      this.codeVerify,
      this.checkIf,
      this.firstName,
      this.lastName,
      this.refCode,
      this.numberID});

  final String? checkIf;
  final BaseAuth? auth;
  final String? secret;
  final providerSMS? roundSMS;
  final String? phoneNumber;
  final String? codeVerify;
  final String? firstName;
  final String? lastName;
  final String? refCode;
  final String? numberID;

  State<StatefulWidget> createState() => new _ChoiceUser(
      checkIf: checkIf,
      secret: secret,
      roundSMS: roundSMS,
      phoneNumber: phoneNumber,
      codeVerify: codeVerify,
      firstName: firstName,
      lastName: lastName,
      refCode: refCode,
      numberID: numberID);
}

class _ChoiceUser extends State<ChoiceUser> {
  _ChoiceUser(
      {this.checkIf,
      this.secret,
      this.roundSMS,
      this.phoneNumber,
      this.codeVerify,
      this.firstName,
      this.lastName,
      this.refCode,
      this.numberID});

  final String? firstName;
  final String? lastName;
  final String? secret;
  final providerSMS? roundSMS;
  final String? phoneNumber;
  final String? codeVerify;
  final String? refCode;
  final String? numberID;
  late firebaseEmail.Auth authEmail;
  final String? checkIf;
  late IAddressService addressService;
  late IConfigurationService configETH;
  bool _saving = false;

  late CryptoEncryptInterface Encrypt;
  late String keyEncrypt;
  late String ivEncrypt;
  late BaseAuth auth;

  void secretIsWrong() {
    // flutter defined function
    showDialog(
        context: context,
        builder: (BuildContext context) {
          // return object of type Dialog
          return AlertDialog(
            title: new Text(
              AppLocalizations.of(context)!.translate('save_err'),
              style: TextStyle(
                  color: Color(0xff6C6B6D),
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  fontSize: MediaQuery.of(context).size.height * 0.01581196581,
                  fontWeight: FontWeight.bold),
            ),
            content: new Text(
              AppLocalizations.of(context)!.translate('save_err'),
              style: TextStyle(
                  color: Color(0xff6C6B6D),
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  fontSize: MediaQuery.of(context).size.height * 0.01581196581,
                  fontWeight: FontWeight.bold),
            ),
            actions: <Widget>[
              // usually buttons at the bottom of the dialog
//              new FlatButton(
//                child: new Text(
//                  AppLocalizations.of(context)
//                      .translate('no_ads_body'),
//                  style: TextStyle(
//                      color: Color(0xff6C6B6D),
//                      fontFamily:
//                      AppLocalizations.of(context)!.translate('font1'),
//                      fontSize:
//                      MediaQuery.of(context).size.height * 0.01581196581,
//                      fontWeight: FontWeight.bold),
//                ),
//                onPressed: () async {
//                  Navigator.pushAndRemoveUntil(
//                    context,
//                    MaterialPageRoute(
//                      builder: (context) => IndexLike(),
//                    ),
//                    ModalRoute.withName('/'),
//                  );
//                },
//              ),
              TextButton(
                style: ButtonStyle(
                  shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                    RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(5.0),
                    ),
                  ),
                  backgroundColor: MaterialStateProperty.resolveWith<Color>(
                        (Set<MaterialState> states) {
                      if (states.contains(MaterialState.disabled)) {
                        return Color(0xff00F1E0); // Disabled color
                      }
                      return Color(0xff00F1E0); // Regular color
                    },
                  ),
                ),
                onPressed: () {
                  Navigator.pushAndRemoveUntil(
                    context,
                    MaterialPageRoute(
                      builder: (context) => IndexLike(),
                    ),
                    ModalRoute.withName('/'),
                  );
                },
                child: Text(
                  AppLocalizations.of(context)!.translate('close_name'),
                  style: TextStyle(
                      color: Color(0xff6C6B6D),
                      fontFamily:
                      AppLocalizations.of(context)!.translate('font1'),
                      fontSize:
                      MediaQuery.of(context).size.height * 0.01581196581,
                      fontWeight: FontWeight.bold),
                ),
              ),
            ],
          );
        });
  }

  void setLogin() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    Encrypt.getKeyEncrypt().then((dataKey) {
      keyEncrypt = dataKey[0];
      ivEncrypt = dataKey[1];

      configETH = new ConfigurationService(pref);
      addressService = new AddressService(configETH);
      pref.setBool('login', false);
      pref.setBool('firstLogin', false);
      createWalelt();
    });
  }

  createWalelt() async {
//    let curPhone = req.body.phone_number;
//    let phone_number = curPhone.split('+')[1];
//    const pass = req.body.password;
//    const codeVerify = req.body.codeVerify;
//    const docID = "OTP" + phone_number; `1
//    const _token = req.body._token;
    if (checkIf == 'register') {
      if (roundSMS == providerSMS.Firebase) {
        User? user = FirebaseAuth.instance.currentUser;
        user!.getIdTokenResult().then((userDetail) async {
          var url = Uri.https(env.apiUrl, '/verifyUserAndCreateNoSecret');
          var response = await http.post(url, body: {
            'phone_number': user.phoneNumber,
            'password': secret,
            'codeVerify': '999999',
            '_token': userDetail.token,
            'register': 'true',
            'firstName': firstName,
            'lastName': lastName,
            'keyEncrypt': keyEncrypt,
            'ivEncrypt': ivEncrypt,
            'refCode': refCode,
            'numberID': numberID ?? 'no',
          });
          try {
            var body = json.decode(response.body);

            if (body['statusCode'] == 204 || body['statusCode'] == 200) {
              SharedPreferences pref = await SharedPreferences.getInstance();
              //            pref.setString('seed', body['seed']);
              if (!mounted) return;
              setState(() {
                _saving = false;
              });
              await configETH.setMnemonic(body['seed']);
              pref.setBool('login', true);
              Navigator.push(context,
                  MaterialPageRoute(builder: (context) => HomeLikewallet()));
            } else {
              secretIsWrong();
            }
          } catch (e) {
            secretIsWrong();
          }
        });
      } else if (roundSMS == providerSMS.Twilio) {
//token ได้แล้ว
        print('providerSMS.Twilio 233');
        User? user = FirebaseAuth.instance.currentUser;
        user!.getIdTokenResult().then((userDetail) async {
          var url = Uri.https(env.apiUrl, '/verifyUserAndCreateNoSecret');
          var response = await http.post(url, body: {
            'phone_number': user.phoneNumber,
            'password': secret,
            'codeVerify': codeVerify,
            'register': 'true',
            '_token': userDetail.token,
            'firstName': firstName,
            'lastName': lastName,
            'keyEncrypt': keyEncrypt,
            'ivEncrypt': ivEncrypt,
            'refCode': refCode,
            'numberID': numberID ?? 'no',
          });
          try {
            try {
              var body = json.decode(response.body);
              if (body['statusCode'] == 204 || body['statusCode'] == 200) {
                SharedPreferences pref = await SharedPreferences.getInstance();
                await configETH.setMnemonic(body['seed']);
                if (!mounted) return;
                setState(() {
                  _saving = false;
                });
                pref.setBool('login', true);
                Navigator.push(context,
                    MaterialPageRoute(builder: (context) => HomeLikewallet()));
              } else {
                secretIsWrong();
              }
            } catch (e) {
              secretIsWrong();
            }
          } catch (e) {
            secretIsWrong();
          }
        });
      } else if (roundSMS == providerSMS.Email) {
        //email register

        String? _token = await auth.getTokenFirebase();
        var url = Uri.https(env.apiUrl, '/verifyWithEmailNoSecret');
        var response = await http.post(url, body: {
          'phone_number': phoneNumber,
          'password': secret,
          'register': 'true',
          'firstName': firstName,
          'lastName': lastName,
          'keyEncrypt': keyEncrypt,
          'ivEncrypt': ivEncrypt,
          'refCode': refCode,
          'numberID': numberID ?? 'no',
          '_token': _token
        });

        try {
          var body = json.decode(response.body);
          if (body['statusCode'] == 204 || body['statusCode'] == 200) {
            SharedPreferences pref = await SharedPreferences.getInstance();
            await configETH.setMnemonic(body['seed']);
            if (!mounted) return;
            setState(() {
              _saving = false;
            });
            pref.setBool('login', true);
            Navigator.push(context,
                MaterialPageRoute(builder: (context) => HomeLikewallet()));
          } else {
            secretIsWrong();
          }
        } catch (e) {}
      }
    } else {
      if (roundSMS == providerSMS.Firebase) {
        User? user = FirebaseAuth.instance.currentUser;
        user!.getIdTokenResult().then((userDetail) async {
          var url = Uri.https(env.apiUrl, '/verifyUserAndCreateNoSecret');
          var response = await http.post(url, body: {
            'phone_number': user.phoneNumber,
            'password': secret,
            'codeVerify': '999999',
            '_token': userDetail.token,
            'register': 'false',
            'keyEncrypt': keyEncrypt,
            'ivEncrypt': ivEncrypt,
            'refCode': refCode,
            'numberID': numberID ?? 'no',
          });
          try {
            var body = json.decode(response.body);
            if (body['statusCode'] == 204 || body['statusCode'] == 200) {
              SharedPreferences pref = await SharedPreferences.getInstance();
              await configETH.setMnemonic(body['seed']);
              pref.setBool('login', true);
              if (!mounted) return;
              setState(() {
                _saving = false;
              });
              Navigator.push(
                  context,
                  // MaterialPageRoute(builder: (context) => HomeLikewallet(seed: body['seed'])));
                  MaterialPageRoute(builder: (context) => HomeLikewallet()));
            } else {
              secretIsWrong();
            }
          } catch (e) {
            secretIsWrong();
          }
        });
      } else if (roundSMS == providerSMS.Twilio) {
//token ได้แล้ว
        print('providerSMS.Twilio 233');
        User? user = FirebaseAuth.instance.currentUser;

        print(user!.phoneNumber);

        user.getIdTokenResult().then((userDetail) async {
          var url = Uri.https(env.apiUrl, '/verifyUserAndCreateNoSecret');
          var response = await http.post(url, body: {
            'phone_number': phoneNumber,
            'password': secret,
            'codeVerify': codeVerify,
            'register': 'false',
            'keyEncrypt': keyEncrypt,
            'ivEncrypt': ivEncrypt,
            'refCode': refCode,
            'numberID': numberID ?? 'no',
          });
          try {
            try {
              var body = json.decode(response.body);
              if (body['statusCode'] == 204 || body['statusCode'] == 200) {
                SharedPreferences pref = await SharedPreferences.getInstance();
                await configETH.setMnemonic(body['seed']);
                if (!mounted) return;
                setState(() {
                  _saving = false;
                });
                pref.setBool('login', true);
                Navigator.push(
                    context,
                    // MaterialPageRoute(builder: (context) => HomeLikewallet(seed: body['seed'])));
                    MaterialPageRoute(builder: (context) => HomeLikewallet()));
              } else {
                secretIsWrong();
              }
            } catch (e) {
              secretIsWrong();
            }
          } catch (e) {
            secretIsWrong();
          }
        });
      } else if (roundSMS == providerSMS.MKT) {
//token ได้แล้ว
        print('providerSMS.Twilio 233');
        User? user = FirebaseAuth.instance.currentUser;

        print(user!.phoneNumber);

        user.getIdTokenResult().then((userDetail) async {
          var url = Uri.https(env.apiUrl, '/verifyUserAndCreateNoSecret');
          var response = await http.post(url, body: {
            'phone_number': phoneNumber,
            'password': secret,
            'codeVerify': codeVerify,
            'register': 'false',
            'keyEncrypt': keyEncrypt,
            'ivEncrypt': ivEncrypt,
            'refCode': refCode,
            'numberID': numberID ?? 'no',
          });
          try {
            try {
              var body = json.decode(response.body);
              if (body['statusCode'] == 204 || body['statusCode'] == 200) {
                SharedPreferences pref = await SharedPreferences.getInstance();
                await configETH.setMnemonic(body['seed']);
                if (!mounted) return;
                setState(() {
                  _saving = false;
                });
                pref.setBool('login', true);
                Navigator.push(
                    context,
                    // MaterialPageRoute(builder: (context) => HomeLikewallet(seed: body['seed'])));
                    MaterialPageRoute(builder: (context) => HomeLikewallet()));
              } else {
                secretIsWrong();
              }
            } catch (e) {
              secretIsWrong();
            }
          } catch (e) {
            secretIsWrong();
          }
        });
      } else if (roundSMS == providerSMS.Email) {
        print('Console Email');
        //email signIN
        String? _token = await auth.getTokenFirebase();
        var url = Uri.https(env.apiUrl, '/verifyWithEmailNoSecret');
        var response = await http.post(url, body: {
          'phone_number': phoneNumber ?? 'no',
          'password': secret,
          'register': 'false',
          'keyEncrypt': keyEncrypt,
          'ivEncrypt': ivEncrypt,
          'numberID': numberID ?? 'no',
          '_token': _token
        });

        try {
          var body = json.decode(response.body);
          if (body['statusCode'] == 204 || body['statusCode'] == 200) {
            SharedPreferences pref = await SharedPreferences.getInstance();
            //          pref.setString('seed', body['seed']);
            await configETH.setMnemonic(body['seed']);
            pref.setBool('login', true);
            if (!mounted) return;
            setState(() {
              _saving = false;
            });
            Navigator.push(
                context,
                // MaterialPageRoute(builder: (context) => HomeLikewallet(seed: body['seed'])));
                MaterialPageRoute(builder: (context) => HomeLikewallet()));
          } else {
            secretIsWrong();
          }
        } catch (e) {}
      }
    }
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    setState(() {
      _saving = true;
    });
    auth = Auth();
    Encrypt = CryptoEncrypt();
    Encrypt.generateKey().then((data) {
      //can generate key to encrypt
      if (data) {
        setLogin();
      } else {
        //something is wrong !
      }
    });
  }

  buildForPhone() {
    return Scaffold(
        backgroundColor: Color(0xff150E23),
        body: new Stack(
          alignment: Alignment.center,
          children: <Widget>[
            background_image(context),
            // Positioned(
            //     top: mediaQuery(context, 'height', 904), child: _buttonBasic()),
//            Positioned(
//                top: mediaQuery(context, 'height', 1206), child: _titel1()),
//            Positioned(
//                top: mediaQuery(context, 'height', 1254.41),
//                child: _buttonPro()),
//            Positioned(
//                top: mediaQuery(context, 'height', 1569), child: _titel2()),
          ],
        ));
  }

  Widget _buttonBasic() {
    return Container(
      width: mediaQuery(context, 'width', 928.26),
      height: mediaQuery(context, 'height', 282.96),
      child: Container(),
//      child: RaisedButton(
//          shape: new RoundedRectangleBorder(
//            borderRadius: new BorderRadius.circular(8.0),
//          ),
//          disabledColor: LikeWalletAppTheme.bule1,
//          color: LikeWalletAppTheme.bule1,
//          onPressed: () {
//            createWalelt();
//          },
//          child: Image.asset(
//            LikeWalletImage.likewallet_text,
//            height: mediaQuery(context, 'height', 80),
//          )),
    );
  }

  Widget _titel1() {
    return new Text(
      AppLocalizations.of(context)!.translate('choiceuser_basic'),
      style: TextStyle(
        color: LikeWalletAppTheme.white.withOpacity(0.4),
        fontFamily: AppLocalizations.of(context)!.translate('font1'),
        fontSize: mediaQuery(context, 'height', 48),
      ),
    );
  }

  Widget _buttonPro() {
    return Container(
      width: mediaQuery(context, 'width', 928.26),
      height: mediaQuery(context, 'height', 282.96),
      child: ElevatedButton(
        style: ButtonStyle(
            shape: MaterialStateProperty.all(new RoundedRectangleBorder(
              borderRadius: new BorderRadius.circular(8.0),
            )),
            foregroundColor: MaterialStateProperty.all<Color>(
              Color(0xffFFFFFF),
            ),
            backgroundColor:
                MaterialStateProperty.all<Color>(Color(0xffFFFFFF))),
        // shape: new RoundedRectangleBorder(borderRadius: new BorderRadius.circular(8.0),),
        // disabledColor: Color(0xffFFFFFF),
        // color: Color(0xffFFFFFF),
        onPressed: () {
          createWalelt();
        },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            new Text(
              'like',
              style: TextStyle(
                color: LikeWalletAppTheme.black,
                fontFamily: 'Proxima Nova',
                fontSize: mediaQuery(context, 'height', 70),
              ),
            ),
            new Text(
              'Wallet',
              style: TextStyle(
                color: LikeWalletAppTheme.black,
                fontFamily: 'Proxima Nova',
                fontWeight: FontWeight.w200,
                fontSize: mediaQuery(context, 'height', 70),
              ),
            ),
            new Text(
              ' Pro',
              style: TextStyle(
                color: LikeWalletAppTheme.lemon,
                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                fontWeight: FontWeight.bold,
                fontSize: mediaQuery(context, 'height', 70),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _titel2() {
    return new Text(AppLocalizations.of(context)!.translate('choiceuser_pro'),
        textAlign: TextAlign.center,
        style: TextStyle(
          color: LikeWalletAppTheme.white.withOpacity(0.4),
          fontFamily: AppLocalizations.of(context)!.translate('font1'),
          fontSize: mediaQuery(context, 'height', 48),
        ));
  }

  @override
  Widget build(BuildContext context) {
    return ModalProgressHUD(
      opacity: 0.1,
      child: Scaffold(
        body: buildForPhone(),
      ),
      inAsyncCall: _saving,
      progressIndicator: CustomLoading(),
    );
  }
}
