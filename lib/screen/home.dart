import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:likewallet/chat_in_app/index_chat.dart';
import 'package:likewallet/middleware/check_net/check_net.dart';
import 'package:likewallet/tabslide/logout.dart';
import 'package:flutter/material.dart';

import 'package:likewallet/screen/navigationbar/refer/refer.dart';
import 'package:likewallet/screen/navigationbar/history.dart';
import 'package:likewallet/screen/navigationbar/home/<USER>';
import 'package:likewallet/screen/navigationbar/notify/notify_tab.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/menu/hourlyRewards.dart';
import 'package:likewallet/screen/navigationbar/contact_us.dart';
import 'package:likewallet/libraryman/pinProtect.dart';
import 'package:likewallet/routes.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/animationPage.dart';
import 'package:likewallet/quickpay/stores.dart';
import 'package:likewallet/quickpay/scanPay.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/screen/curved_navigation_bar/curved_navigation_bar.dart';
import 'package:flutter/cupertino.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:rxdart/subjects.dart';
import 'package:likewallet/icon/icon_home_icons.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:likewallet/receive_notify.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:likewallet/main.dart';
import 'package:likewallet/support/chat.dart';
import 'package:likewallet/menu/reward/rewards_screen.dart';

import 'ContractUS.dart';

final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
    FlutterLocalNotificationsPlugin();
//
// // Streams are created so that app can respond to notification-related events since the plugin is initialised in the `main` function
// final BehaviorSubject<ReceivedNotification> didReceiveLocalNotificationSubject =
//     BehaviorSubject<ReceivedNotification>();

final BehaviorSubject<String> selectNotificationSubject =
    BehaviorSubject<String>();

final Map<String, Item> _items = <String, Item>{};

Item _itemForMessage(Map<String, dynamic> message) {
  final dynamic data = message['data'] ?? message;
  final String point = data['point'] ?? '0';
  final String stake = data['stake'] ?? 'no';
  final Item item = _items.putIfAbsent(
      point, () => Item(point: point, stake: stake))
    ..status = data['status'];
  return item;
}

class Item {
  Item({this.point, this.stake});
  final String? point;
  final String? stake;

  StreamController<Item> _controller = StreamController<Item>.broadcast();
  Stream<Item> get onChanged => _controller.stream;

  late String _status;
  String get status => _status;
  set status(String value) {
    _status = value;
    _controller.add(this);
  }

  static final Map<String, Route<void>> routes = <String, Route<void>>{};
  Route<void> get route {
    final String routeName = '/detail/$point';
    return routes.putIfAbsent(
      routeName,
      () => MaterialPageRoute<void>(
        settings: RouteSettings(name: routeName),
        builder: (BuildContext context) => DetailPage(point!),
      ),
    );
  }
}

class DetailPage extends StatefulWidget {
  DetailPage(this.point);
  final String point;

  @override
  _DetailPageState createState() => _DetailPageState();
}

class _DetailPageState extends State<DetailPage> {
  late Item? _item;
  late StreamSubscription<Item>? _subscription;

  @override
  void initState() {
    super.initState();
    _item = _items[widget.point];
    _subscription = _item!.onChanged.listen((Item item) {
      if (!mounted) {
        _subscription!.cancel();
      } else {
        setState(() {
          _item = item;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Item ${_item!.point}"),
      ),
      body: Material(
        child: Center(child: Text("Item status: ${_item!.status}")),
      ),
    );
  }
}

class HomeLikewallet extends StatefulWidget {
  HomeLikewallet({this.selectPage});
  final selectPage;
  // final String? seed;

  Likewallet createState() => new Likewallet(selectPage: selectPage);
}

class Likewallet extends State<HomeLikewallet>
    with WidgetsBindingObserver, TickerProviderStateMixin {
  int _page = 0;
  GlobalKey _bottomNavigationKey = GlobalKey();
  GlobalKey<ScaffoldState> _scaffoldKey = new GlobalKey();
  Likewallet({this.selectPage});

  // final String? seed;
  late BaseAuth auth;
  late int? selectPage;
  double timeCheck = 0;
  double timeResume = 0;
  late AppLifecycleState _notification;
  int _selectPage = 0;
  FirebaseFirestore fireStore = FirebaseFirestore.instance;
  int curIndex = 0;
  late AnimationController rotationController;
  Animation<double>? animation;
  String uid = FirebaseAuth.instance.currentUser!.uid;
  late IConnectivity iConnectivity;
  var _pageOption = [
    HomeScreen(),
    NotifyTab(),
    HistoryPage(),
    HomeScreen(),
  ];

  String message = '';
  String channelId = "1000";
  String channelName = "FLUTTER_NOTIFICATION_CHANNEL";
  String channelDescription = "FLUTTER_NOTIFICATION_CHANNEL_DETAIL";

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  String _homeScreenText = "Waiting for token...";

  void _navigateToItemDetail(Map<String, dynamic> message) {
    final Item item = _itemForMessage(message);
    // Clear away dialogs
    if (item.stake == 'stake') {
      Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(
            builder: (context) => Rewards(),
          ),
          (Route<dynamic> route) => false);
    } else {
      if (Platform.isAndroid) {
        Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(
              builder: (context) => HomeLikewallet(
                selectPage: 3,
              ),
            ),
            (Route<dynamic> route) => false);
      }
    }
  }

  _buildDialogNotify(Map<String, dynamic> message, bool status) {
    if (Platform.isIOS) {
      return FadeNotification(context, message["point"], this);
    } else {
      return FadeNotification(context, message["point"], this);
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    setState(() {
      _notification = state;
    });
    print(_notification);
    switch (state) {
      case AppLifecycleState.resumed:
        //check if application inactive more 60 second
        if (((new DateTime.now().millisecondsSinceEpoch / 1000) - timeCheck) >
            60) {
          //back to pin or bio
          // AppRoutes.makeFirst(
          //     context,
          //     PinProtect(
          //       redirectPage: 'HOME',
          //     ));
        }
        print('still work');
        break;
      case AppLifecycleState.paused:
        setState(() {
          timeCheck = new DateTime.now().millisecondsSinceEpoch / 1000;
        });
        break;
      case AppLifecycleState.inactive:
        setState(() {
          timeCheck = new DateTime.now().millisecondsSinceEpoch / 1000;
        });

        break;
      case AppLifecycleState.detached:
        print('detached');
        AppRoutes.makeFirst(
            context,
            PinProtect(
              redirectPage: 'HOME',
            ));
        break;
    }
  }

  bool _requested = false;
  bool _fetching = false;
  late NotificationSettings _settings;

  Future<void> requestPermissions() async {
    setState(() {
      _fetching = true;
    });

    NotificationSettings settings =
        await FirebaseMessaging.instance.requestPermission(
          alert: true,
          announcement: false,
          badge: true,
          carPlay: false,
          criticalAlert: false,
          provisional: false,
          sound: true,
    );

    setState(() {
      _requested = true;
      _fetching = false;
      _settings = settings;
    });
  }

  setNotifyInitial() {
    message = "No message.";

    var initializationSettingsAndroid =
       AndroidInitializationSettings('app_icon');
    var initializationSettingsIOS = DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
        onDidReceiveLocalNotification: (id, title, body, payload) async {
          // when user tap on notification.
          print("onDidReceiveLocalNotification called.");
          setState(() {
            message = payload!;
          });
        });

    var initializationSettings = InitializationSettings(
        android: initializationSettingsAndroid, iOS: initializationSettingsIOS);

    flutterLocalNotificationsPlugin.initialize(initializationSettings,
        onDidReceiveNotificationResponse: (payload) async {
      // when user tap on notification.
      print("onSelectNotification called.");
      setState(() {
        message = payload.payload!;
      });
    });
  }

  @override
  void initState() {
    setNotifyInitial();
    // TODO: implement initState
    super.initState();
    iConnectivity = CallConnectivity();
    iConnectivity.callConnectivityListen();
    Future.delayed(Duration.zero, () {
      if (selectPage != null) {
        setState(() {
          _page = selectPage!;
        });
      }
      print('hello home');
      // fireStore = FirebaseFirestore.instance;
      auth = Auth();

      _firebaseMessaging.subscribeToTopic('notifyAll');

      requestPermissions();
      FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
        // _navigateToItemDetail(message.data);
        if (message.data["activity"] == 'claimReward') {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => Rewards(),
            ),
          );
        }
        if (message.data["activity"] == 'newsTier1') {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => HomeLikewallet(
                selectPage: 1,
              ),
            ),
          );
        }
        if (message.data["activity"] == 'newsNormal') {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => HomeLikewallet(
                selectPage: 1,
              ),
            ),
          );
        }
      });

      FirebaseMessaging.onMessage.listen((RemoteMessage message) {
        print("stake " + message.data.toString());
        if (Platform.isIOS) {
          if (message.data["stake"] == 'stake') {
            sendNotification(
              point: message.data["point"],
            );
          }
          if (double.parse(message.data["point"]) > 0) {
            sendNotification(
              point: message.data["point"],
            );
          }
        } else if (Platform.isAndroid) {
          if (message.data["stake"] == 'stake') {
            sendNotification(
              point: message.data["point"],
            );
          }
          if (double.parse(message.data["point"]) > 0) {
            print(message.data["point"]);
            sendNotification(
              point: message.data["point"],
            );
          }
        }
      });

      _firebaseMessaging.getToken().then((String? token) {
        assert(token != null);
        if (!mounted) return;
        setState(() {
          _homeScreenText = "Push Messaging token: $token";
        });
        print(_homeScreenText);
      });
      _firebaseMessaging.subscribeToTopic('ads');
      getNotify();
    });
  }

  getNotify() {
    uid = FirebaseAuth.instance.currentUser!.uid;
  }

  @override
  void dispose() {
    WidgetsBinding.instance!.removeObserver(this);
    super.dispose();
  }

  closeButtonPay() => setState(() => context.read(pay).state = false);

  colorNav() {
    return _page == 0 || _page == 3 ? true : false;
  }

  @override
  Widget build(BuildContext context) {
    return new WillPopScope(
      onWillPop: _onBackPressed,
      child: GestureDetector(
        onTap: () {
          closeButtonPay();
        },
        child: Scaffold(
            floatingActionButtonLocation:
                FloatingActionButtonLocation.endDocked,
            floatingActionButton: _page == 0 ? _pay() : Container(),
            body: Stack(
              children: [
                _pageOption[_page],
                Positioned(
                  bottom: 0,
                  child: Container(
                    color: Colors.transparent,
                    height: mediaQuery(context, 'height', 220),
                    width: MediaQuery.of(context).size.width,
                    child: CurvedNavigationBar(
                      key: _bottomNavigationKey,
                      index: _page == 3 ? _page = 0 : _page,
                      // height: mediaQuery(context, 'height', 220),
                      items: <Widget>[
                        SizedBox(
                          height: mediaQuery(context, 'height', 148),
                          width: mediaQuery(context, 'height', 148),
                          child: Icon(IconHome.path_43609,
                              size: mediaQuery(context, 'height', 58),
                              color: _page != 0 ? Colors.black : Colors.white),
                        ),
                        SizedBox(
                            height: mediaQuery(context, 'height', 148),
                            width: mediaQuery(context, 'height', 148),
                            child: StreamBuilder<QuerySnapshot>(
                                stream: fireStore
                                    .collection('notificationByUser')
                                    .doc(FirebaseAuth.instance.currentUser!.uid)
                                    .collection('notify')
                                    .where("status", isEqualTo: "unread")
                                    .snapshots(),
                                builder: (BuildContext context,
                                    AsyncSnapshot<QuerySnapshot> snapshot) {
                                  if (snapshot.hasError) {
                                    print('มีบางอย่างผิดพลาด');
                                    return Icon(IconHome.path_43608,
                                        size: mediaQuery(context, 'height', 70),
                                        color: _page != 0 || _page != 3
                                            ? Colors.white
                                            : Colors.black);
                                  }

                                  if (snapshot.connectionState ==
                                      ConnectionState.waiting) {
                                    return Icon(IconHome.path_43608,
                                        size: mediaQuery(context, 'height', 70),
                                        color: _page != 0
                                            ? Colors.black
                                            : Colors.white);
                                  }
                                  if (snapshot.data!.docs.isEmpty) {
                                    return Icon(IconHome.path_43608,
                                        size: mediaQuery(context, 'height', 70),
                                        color: _page != 0
                                            ? Colors.black
                                            : Colors.white);
                                  } else {
                                    return Stack(
                                      alignment: Alignment.center,
                                      children: [
                                        Icon(IconHome.path_43608,
                                            size: mediaQuery(
                                                context, 'height', 70),
                                            color: _page != 0
                                                ? Colors.black
                                                : Colors.white),
                                        Positioned(
                                          top: 20.h,
                                          right: 20.w,
                                          child: Container(
                                            alignment: Alignment.center,
                                            height: 50.h,
                                            width: 50.h,
                                            padding: EdgeInsets.only(
                                                top: 1.h, left: 1.h),
                                            decoration: BoxDecoration(
                                              shape: BoxShape.circle,
                                              color: Color(0xffFFC400),
                                            ),
                                            child: Text(
                                              snapshot.data!.docs.length
                                                  .toString(),
                                              style: TextStyle(
                                                  fontFamily:
                                                      AppLocalizations.of(
                                                              context)!
                                                          .translate('font1'),
                                                  fontSize: 33.sp,
                                                  color: Colors.white,
                                                  fontWeight: FontWeight.w500,
                                                  letterSpacing: 1,
                                                  height: 1.2),
                                            ),
                                          ),
                                        )
                                      ],
                                    );
                                  }
                                })),
                        SizedBox(
                          height: mediaQuery(context, 'height', 148),
                          width: mediaQuery(context, 'height', 148),
                          child: Icon(IconHome.group_24548,
                              size: mediaQuery(context, 'height', 58),
                              color: _page != 0 ? Colors.black : Colors.white),
                        ),
                        InkWell(
                          onTap: () {
                            /// ย้ายไปไลน์ โอ๋เอ๋
                            // Navigator.push(
                            //     context,
                            //     MaterialPageRoute(
                            //         builder: (context) => IndexChatPage()));
                            Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) =>
                                        ContactUSPage()));
                          },
                          //   Navigator.push(
                          //     context,
                          //     MaterialPageRoute(
                          //         builder: (context) => ChatSupport()),
                          //   ).then((value) async {
                          //     QuerySnapshot ds = await fireStore
                          //         .collection('messages')
                          //         .doc(uid)
                          //         .collection('messages')
                          //         .where("status", isEqualTo: "unread")
                          //         .get();
                          //     if (ds.docs.isNotEmpty) {
                          //       ds.docs.forEach((chat) async {
                          //         await fireStore
                          //             .collection('messages')
                          //             .doc(uid)
                          //             .collection('messages')
                          //             .doc(chat.id)
                          //             .update({"status": "read"});
                          //       });
                          //     } else {
                          //       print('ไม่มีข้อความ');
                          //     }
                          //   });
                          // },
                          // child: SizedBox(
                          //   height: mediaQuery(context, 'height', 148),
                          //   width: mediaQuery(context, 'height', 148),
                          //   child: Icon(IconHome.path_58781,
                          //       size: mediaQuery(context, 'height', 58),
                          //       color:
                          //           _page != 0 ? Colors.black : Colors.white),
                          // ),
                          child: SizedBox(
                              height: mediaQuery(context, 'height', 148),
                              width: mediaQuery(context, 'height', 148),
                              child: StreamBuilder<QuerySnapshot>(
                                  stream: fireStore
                                      .collection('messages')
                                      .doc(uid)
                                      .collection('messages')
                                      .where("status", isEqualTo: "unread")
                                      .snapshots(),
                                  builder: (BuildContext context,
                                      AsyncSnapshot<QuerySnapshot> snapshot) {
                                    if (snapshot.hasError) {
                                      print('มีบางอย่างผิกพลาด');
                                      return Icon(IconHome.path_58781,
                                          size:
                                              mediaQuery(context, 'height', 70),
                                          color: _page != 0
                                              ? Colors.white
                                              : Colors.black);
                                    }

                                    if (snapshot.connectionState ==
                                        ConnectionState.waiting) {
                                      return Icon(IconHome.path_58781,
                                          size:
                                              mediaQuery(context, 'height', 70),
                                          color: _page != 0
                                              ? Colors.black
                                              : Colors.white);
                                    }
                                    return snapshot.data!.docs.length == 0
                                        ? Icon(IconHome.path_58781,
                                            size: mediaQuery(
                                                context, 'height', 70),
                                            color: _page != 0
                                                ? Colors.black
                                                : Colors.white)
                                        : Stack(
                                            alignment: Alignment.center,
                                            children: [
                                              Icon(IconHome.path_58781,
                                                  size: mediaQuery(
                                                      context, 'height', 70),
                                                  color: _page != 0
                                                      ? Colors.black
                                                      : Colors.white),
                                              Positioned(
                                                top: 20.h,
                                                right: 20.w,
                                                child: Container(
                                                  alignment: Alignment.center,
                                                  height: 50.h,
                                                  width: 50.h,
                                                  decoration: BoxDecoration(
                                                    shape: BoxShape.circle,
                                                    color: Color(0xffFFC400),
                                                  ),
                                                  child: Text(
                                                    snapshot.data!.docs.length
                                                        .toString(),
                                                    style: TextStyle(
                                                        fontFamily:
                                                            AppLocalizations.of(
                                                                    context)!
                                                                .translate(
                                                                    'font1'),
                                                        fontSize: 33.sp,
                                                        color: Colors.white,
                                                        fontWeight:
                                                            FontWeight.w500,
                                                        letterSpacing: 1,
                                                        height: 1.2),
                                                  ),
                                                ),
                                              )
                                            ],
                                          );
                                  })),
                        ),
                      ],
                      color: colorNav() ? Color(0xff141322) : Colors.white,
                      buttonBackgroundColor:
                          colorNav() ? Color(0xff141322) : Colors.white,
                      backgroundColor: Colors.transparent,
                      animationCurve: Curves.easeInOut,
                      animationDuration: Duration(milliseconds: 300),
                      onTap: (index) {
                        setState(() {
                          if (index != 3) {
                            _page = index;
                          }
                        });
                      },
                      letIndexChange: (index) => true,
                    ),
                  ),
                )
              ],
            )
            // final CurvedNavigationBarState navBarState =
            //                 _bottomNavigationKey.currentState;
            //             navBarState.setPage(1);
            ),
      ),
    );
  }

//   @override
//   Widget build(BuildContext context) {
//     screenUtil(context);
//     return new WillPopScope(
//       onWillPop: _onBackPressed,
//       child: GestureDetector(
//         onTap: () {
//           setState(() {
//             _count = false;
//           });
//         },
//         child: Scaffold(
//             key: _scaffoldKey,
//             backgroundColor: Color(0xff121424),
//             floatingActionButtonLocation:
//                 FloatingActionButtonLocation.endDocked,
//             floatingActionButton:
//                 _selectPage == 0 || _selectPage == 4 && selectPage == null
//                     ? _pay()
//                     : Container(),
//             bottomNavigationBar: BottomNavigationBar(
//               type: BottomNavigationBarType
//                   .fixed, // this will be set when a new tab is tapped
//
//               backgroundColor: Color(0xff121424),
//               currentIndex: _selectPage,
//               elevation: 100,
//               onTap: (index) {
//                 setState(() {
//                   print(index);
//                   selectPage = null;
//                   _selectPage = index;
//                 });
//               },
//               items: [
//                 BottomNavigationBarItem(
//                   icon: Container(),
//                   title: Container(
//                     height: mediaQuery(context, "height", 170),
//                     width: mediaQuery(context, "width", 170),
//                     alignment: Alignment.bottomCenter,
//                     padding: EdgeInsets.only(
//                         bottom: mediaQuery(context, 'height', 40)),
//                     child: Image.asset(
//                       LikeWalletImage.iconHome,
//                       height: mediaQuery(context, "height", 60),
//                       color: _selectPage == 0
//                           ? Colors.white.withOpacity(1)
//                           : Colors.white.withOpacity(0.4),
//                     ),
//                   ),
//                 ),
//                 BottomNavigationBarItem(
//                   icon: Container(),
//                   title: Stack(
//                     children: [
//                       Container(
//                         height: mediaQuery(context, "height", 170),
//                         width: mediaQuery(context, "width", 170),
//                         alignment: Alignment.bottomCenter,
//                         padding: EdgeInsets.only(
//                             bottom: mediaQuery(context, 'height', 40)),
//                         child: Image.asset(
//                           LikeWalletImage.iconBuzzer,
//                           height: mediaQuery(context, "height", 60),
//                           color: _selectPage == 1
//                               ? Colors.white.withOpacity(1)
//                               : Colors.white.withOpacity(0.4),
//                         ),
//                       ),
//                       Positioned(
//                           top: 40.h,
//                           right: 35.w,
//                           child: StreamBuilder<QuerySnapshot>(
//                               stream: fireStore
//                                   .collection('notificationByUser')
//                                   .doc(uid)
//                                   .collection('notify')
//                                   .where("status", isEqualTo: "not")
//                                   .snapshots(),
//                               builder: (BuildContext context,
//                                   AsyncSnapshot<QuerySnapshot> snapshot) {
//                                 if (snapshot.hasError) {
//                                   print('มีบางอย่างผิกพลาด');
//                                   return Container();
//                                 }
//
//                                 if (snapshot.connectionState ==
//                                     ConnectionState.waiting) {
//                                   return Container();
//                                 }
//                                 return snapshot.data.docs.length == 0
//                                     ? Container()
//                                     : Container(
//                                         alignment: Alignment.center,
//                                         height: 50.sp,
//                                         width: 50.sp,
//                                         decoration: BoxDecoration(
//                                           shape: BoxShape.circle,
//                                           color: Color(0xffFFC400),
//                                         ),
//                                         child: Text(
//                                           snapshot.data.docs.length.toString(),
//                                           style: TextStyle(
//                                               fontFamily:
//                                                   AppLocalizations.of(context)
//                                                       .translate('font1'),
//                                               fontSize: 33.sp,
//                                               color: Colors.white,
//                                               fontWeight: FontWeight.w500,
//                                               letterSpacing: 1,
//                                               height: 1.2),
//                                         ),
//                                       );
//                               })),
//                       // watch(countNotify).state == 0
//                       //     ? Container()
//                       //     : Positioned(
//                       //         top: 40.sp,
//                       //         right: 35.sp,
//                       //         child: Container(
//                       //           alignment: Alignment.center,
//                       //           height: 50.sp,
//                       //           width: 50.sp,
//                       //           decoration: BoxDecoration(
//                       //             shape: BoxShape.circle,
//                       //             color: Colors.red,
//                       //           ),
//                       //           child: Text(
//                       //             watch(countNotify).state.toString(),
//                       //             style: TextStyle(
//                       //                 fontFamily: AppLocalizations.of(context)
//                       //                     .translate('font1'),
//                       //                 fontSize: 33.sp,
//                       //                 color: Colors.white,
//                       //                 fontWeight: FontWeight.w500,
//                       //                 letterSpacing: 1,
//                       //                 height: 1.2),
//                       //           ),
//                       //         ),
//                       //       ),
//                     ],
//                   ),
//                 ),
//                 BottomNavigationBarItem(
//                   icon: Container(),
//                   title: Container(
//                     height: mediaQuery(context, "height", 170),
//                     width: mediaQuery(context, "width", 170),
//                     padding: EdgeInsets.only(
//                         bottom: mediaQuery(context, 'height', 40)),
//                     alignment: Alignment.bottomCenter,
//                     child: Image.asset(
//                       LikeWalletImage.iconRefer,
//                       height: mediaQuery(context, "height", 100),
//                       color: _selectPage == 2
//                           ? Colors.white.withOpacity(1)
//                           : Colors.white.withOpacity(0.4),
//                     ),
//                   ),
//                 ),
//                 BottomNavigationBarItem(
//                   icon: Container(),
//                   title: Container(
//                     height: mediaQuery(context, "height", 170),
//                     width: mediaQuery(context, "width", 170),
//                     padding: EdgeInsets.only(
//                         bottom: mediaQuery(context, 'height', 40)),
//                     alignment: Alignment.bottomCenter,
//                     child: Image.asset(
//                       LikeWalletImage.iconHistory,
//                       height: mediaQuery(context, "height", 60),
//                       color: _selectPage == 3
//                           ? Colors.white.withOpacity(1)
//                           : Colors.white.withOpacity(0.4),
//                     ),
//                   ),
//                 ),
//                 BottomNavigationBarItem(
//                   icon: Container(),
//                   title: InkWell(
//                     onTap: () {
//                       print('firstclick');
//                       Navigator.push(
//                         context,
//                         MaterialPageRoute(builder: (context) => ContactUS()),
//                       );
//                     },
//                     child: Container(
//                       height: mediaQuery(context, "height", 180),
//                       width: mediaQuery(context, "width", 170),
//                       padding: EdgeInsets.only(
//                           bottom: mediaQuery(context, 'height', 35)),
//                       alignment: Alignment.bottomCenter,
//                       child: Image.asset(
//                         LikeWalletImage.iconFeedback,
//                         height: mediaQuery(context, 'height', 90),
//                         color: Colors.white.withOpacity(0.4),
//                       ),
//                     ),
//                   ),
//                 )
//               ],
//             ),
//             body: Stack(
//               children: <Widget>[
//                 selectPage != null
//                     ? _pageOption[selectPage]
//                     : _pageOption[_selectPage],
//               ],
//             )),
//       ),
//     );
//   }
//
  Widget _pay() {
    return Stack(
      children: <Widget>[
        Positioned(
          bottom: mediaQuery(context, "height", 110),
          right: mediaQuery(context, "width", -70),
          child: Container(
//        color: Colors.lightBlue,
              margin: EdgeInsets.only(),
              alignment: Alignment.center,
              height: mediaQuery(context, "height", 450),
              width: mediaQuery(context, "height", 450),
              child: Stack(
                children: <Widget>[
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        if (context.read(pay).state == false) {
                          context.read(pay).state = true;
                          rotationController = AnimationController(
                            vsync: this,
                            duration: Duration(milliseconds: 140),
                          )..addListener(() => setState(() {}));
                          animation = CurvedAnimation(
                            parent: rotationController,
                            curve: Curves.easeInBack,
                          );
                          print(context.read(pay).state);
                        } else {
                          context.read(pay).state = false;
                          rotationController.forward();
                          print(context.read(pay).state);
                        }
                      });
                    },
                    child: AnimatedSwitcher(
                      duration: const Duration(milliseconds: 140),
                      transitionBuilder:
                          (Widget child, Animation<double> animation) {
                        return ScaleTransition(child: child, scale: animation);
                      },
                      child: context.read(pay).state == false
                          ? Container(
                              key: ValueKey<bool>(context.read(pay).state),
                              alignment: Alignment.center,
                              child: Image.asset(
                                LikeWalletImage.icon_quick_pay,
                                height: mediaQuery(context, "height", 210),
                                width: mediaQuery(context, "height", 210),
                              ))
                          : Container(
                              key: ValueKey<bool>(context.read(pay).state),
                              alignment: Alignment.center,
                              child: Stack(
                                children: <Widget>[
                                  RotationTransition(
                                    turns: animation!,
                                    child: Image.asset(
                                      LikeWalletImage.image_quick_pay,
                                      height:
                                          mediaQuery(context, "height", 350),
                                      width: mediaQuery(context, "height", 350),
                                    ),
                                  ),
                                ],
                              )),
                    ),
                  ),
                  Positioned(
                    top: mediaQuery(context, "height", 170),
                    left: mediaQuery(context, "width", 20),
                    child: AnimatedSwitcher(
                        duration: const Duration(milliseconds: 140),
                        transitionBuilder:
                            (Widget child, Animation<double> animation) {
                          return ScaleTransition(
                              child: child, scale: animation);
                        },
                        child: (context.read(pay).state != false)
                            ? GestureDetector(
                                onTap: () {
                                  context.read(pay).state = false;
                                  Navigator.push(
                                      context, ScaleRoute(page: scanPay()));
                                },
                                child: Container(
                                  height: mediaQuery(context, "height", 81.87),
                                  width: mediaQuery(context, "height", 81.87),
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    boxShadow: [
                                      new BoxShadow(
                                          color: Colors.black.withOpacity(0.16),
                                          offset: new Offset(0, 3),
                                          blurRadius: 5.0,
                                          spreadRadius: 1.0),
                                    ],
                                  ),
                                  child: Image.asset(
                                    LikeWalletImage.image_quick_pay_scan,
                                    height:
                                        mediaQuery(context, "height", 81.87),
                                    width: mediaQuery(context, "height", 81.87),
                                    key:
                                        ValueKey<bool>(context.read(pay).state),
                                  ),
                                ),
                              )
                            : Container(
                                padding: EdgeInsets.only(
                                  left: mediaQuery(context, "width", 230),
                                ),
                              )),
                  ),
                  Positioned(
                    top: mediaQuery(context, "height", 15),
                    right: mediaQuery(context, "width", 100),
                    child: AnimatedSwitcher(
                        duration: const Duration(milliseconds: 140),
                        transitionBuilder:
                            (Widget child, Animation<double> animation) {
                          return ScaleTransition(
                              child: child, scale: animation);
                        },
                        child: (context.read(pay).state != false)
                            ? InkWell(
                                onTap: () {
                                  context.read(pay).state = false;
                                  Navigator.push(
                                      context, ScaleRoute(page: stores()));
                                },
                                child: Container(
                                  alignment: Alignment.topLeft,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    boxShadow: [
                                      new BoxShadow(
                                          color: Colors.black.withOpacity(0.16),
                                          offset: new Offset(0, 3),
                                          blurRadius: 5.0,
                                          spreadRadius: 1.0),
                                    ],
                                  ),
                                  child: Image.asset(
                                    LikeWalletImage.image_quick_pay_store,
                                    height:
                                        mediaQuery(context, "height", 81.87),
                                    width: mediaQuery(context, "height", 81.87),
                                    key:
                                        ValueKey<bool>(context.read(pay).state),
                                  ),
                                ),
                              )
                            : Container(
                                alignment: Alignment.topLeft,
                                padding: EdgeInsets.only(
                                  top: mediaQuery(context, "height", 100),
                                ),
                              )),
                  )
                ],
              )),
        )
      ],
    );
  }

//
  Future<bool> _onBackPressed() {
    return ExitDialog(context);
  }
//
//   Widget roundedButton(String buttonLabel, Color bgColor, Color textColor) {
//     var loginBtn = new Container(
//       padding: EdgeInsets.all(5.0),
//       alignment: FractionalOffset.center,
//       decoration: new BoxDecoration(
//         color: bgColor,
//         borderRadius: new BorderRadius.all(const Radius.circular(10.0)),
//         boxShadow: <BoxShadow>[
//           BoxShadow(
//             color: const Color(0xFF696969),
//             offset: Offset(1.0, 6.0),
//             blurRadius: 0.001,
//           ),
//         ],
//       ),
//       child: Text(
//         buttonLabel,
//         style: new TextStyle(
//             color: textColor, fontSize: 20.0, fontWeight: FontWeight.bold),
//       ),
//     );
//     return loginBtn;
//   }
}
