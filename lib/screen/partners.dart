// import 'dart:convert';
// import 'dart:io';
//
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'dart:async';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:likewallet/bank/confirm_transection.dart';
// import 'dart:math' as math;
// import 'package:likewallet/libraryman/ethcontract.dart';
// import 'package:likewallet/libraryman/address_service.dart';
// import 'package:likewallet/libraryman/configuration_service.dart';
// import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
// import 'package:http/http.dart' as http;
// import 'package:likewallet/app_config.dart';
// import 'package:likewallet/setmodel/next_rewards_model.dart';
// import 'package:fluttertoast/fluttertoast.dart';
// import 'package:likewallet/libraryman/custom_loading.dart';
// import 'package:intl/intl.dart' as formatIntl;
// import 'package:ads/ads.dart';
// import 'package:likewallet/libraryman/app_local.dart';
// import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:likewallet/libraryman/auth.dart';
// import 'package:firebase_admob/firebase_admob.dart';
// import 'package:likewallet/libraryman/AppAds.dart';
// import 'package:likewallet/screen/quickpay.dart';
//
// class Partners extends StatefulWidget {
//   _Partners createState() => new _Partners();
// }
//
// enum statusClaim { INACTIVE, ACTIVE }
//
// class _Partners extends State<Partners> with TickerProviderStateMixin {
//   final f = new formatIntl.NumberFormat("###,###");
//   String dropdownValue;
//   double nextRewards = 0;
//   double upcomingRewards = 0;
//   double rewards = 0.0;
//   double totalLocked = 0.0;
//   IAddressService addressService;
//   IConfigurationService configETH;
//   BaseETH eth;
//   statusClaim isChaim = statusClaim.INACTIVE;
//   String addressETH;
//   String mnemonic;
//   String getPK;
//   bool _saving = false;
//   int tabMenu = 0;
//   BaseAuth auth;
//   String messageUpcoming = "Please, wait next round";
//   List<NextRewards> listRewards;
//   bool ads_1 = false;
//   bool ads_2 = false;
//   bool ads_3 = false;
//   bool ads_4 = false;
//   int rewardsToday = 0;
//
//   @override
//   void initState() {
//     super.initState();
//   }
//
//   ///ads admob ////
//   ///
//   ///
//   /// end ads admobb
//   ///
//   @override
//   void dispose() {
// //    ads?.dispose();
//     super.dispose();
//   }
//
//   void showColoredToast(msg, colors) {
//     Fluttertoast.showToast(
//         msg: msg,
//         toastLength: Toast.LENGTH_LONG,
//         backgroundColor: colors,
//         textColor: Colors.white);
//   }
//
//   void showShortToast(msg) {
//     Fluttertoast.showToast(
//         msg: msg,
//         toastLength: Toast.LENGTH_SHORT,
//         backgroundColor: Colors.cyan,
//         textColor: Colors.white);
//   }
//
//   setInit() async {
//     SharedPreferences pref = await SharedPreferences.getInstance();
//
//     configETH = new ConfigurationService(pref);
//     addressService = new AddressService(configETH);
//     mnemonic = pref.getString('seed');
//
// //    getPK = addressService.getPrivateKey(mnemonic);
//     getPK = configETH.getPrivateKey();
//
//     setState(() {
//       addressETH = configETH.getAddress();
//     });
//
//     //get address
//
// //    addressService.getPublicAddress(getPK).then((address) {
//     print(addressETH);
//   }
//
//   buildForPhone(Orientation orientation) {
//     return Scaffold(
//       backgroundColor: Colors.white,
//       body: tabMenu == 1
//           ? confirmTransection()
//           : SingleChildScrollView(
//           child: Stack(
//             children: <Widget>[
//               new Container(
//                 height: MediaQuery.of(context).size.height,
//                 decoration: BoxDecoration(
//                     gradient: LinearGradient(
//                       begin: Alignment.topRight,
//                       end: Alignment.bottomLeft,
//                       stops: [0.4, 0.6, 0.7, 0.9, 0.95],
//                       colors: [
//                         // Colors are easy thanks to Flutter's Colors class.
//                         Color(0xff111112).withOpacity(0.9),
//                         Color(0xff111112).withOpacity(0.8),
//                         Color(0xff111112).withOpacity(0.75),
//                         Color(0xff111112).withOpacity(0.73),
//                         Color(0xff111112).withOpacity(0.69)
//                       ],
//                     )),
//               ),
//               Positioned(
//                 top: MediaQuery.of(context).size.height * 0.1,
//                 child: new Column(
//                   children: <Widget>[
//                     Container(
//                       width: MediaQuery.of(context).size.width * 0.95,
//                       alignment: Alignment.centerRight,
//                       child: new Text(
//                         'Partners',
//                         textAlign: TextAlign.right,
//                         style: TextStyle(
//                             fontFamily: 'Proxima Nova',
//                             color: Color(0xffFFFFFF),
//                             fontSize: 15,
//                             fontWeight: FontWeight.normal),
//                       ),
//                     ),
//                     Container(
//                       width: MediaQuery.of(context).size.width * 0.95,
//                       alignment: Alignment.centerRight,
//                       child: new Text(
//                         'Pay',
//                         textAlign: TextAlign.right,
//                         style: TextStyle(
//                             fontFamily: 'Roboto',
//                             color: Color(0xffFFFFFF),
//                             fontSize: 30,
//                             fontWeight: FontWeight.normal),
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//               Positioned(
//                 top: MediaQuery.of(context).size.height * 0.2,
//                 left: MediaQuery.of(context).size.height * 0.02,
//                 child: Container(
//                     padding: EdgeInsets.only(
//                       left: MediaQuery.of(context).size.width * 0.1,
//                     ),
//                     alignment: Alignment.center,
//                     height: MediaQuery.of(context).size.height * 0.1,
//                     width: MediaQuery.of(context).size.width * 0.9,
//                     child: Column(
//                       children: <Widget>[
//                         new Text(
//                           "Shop",
//                           textAlign: TextAlign.right,
//                           style: TextStyle(
//                               fontFamily: 'Proxima Nova',
//                               color: Color(0xff029A93),
//                               fontSize: 32,
//                               fontWeight: FontWeight.normal),
//                         ),
//                       ],
//                     )),
//               ),
//               Positioned(
//                 top: MediaQuery.of(context).size.height * 0.25,
//                 left: MediaQuery.of(context).size.height * 0.02,
//                 child: Container(
//                   padding: EdgeInsets.only(
//                     left: MediaQuery.of(context).size.width * 0.1,
//                   ),
//                   alignment: Alignment.center,
//                   height: MediaQuery.of(context).size.height * 0.3,
//                   width: MediaQuery.of(context).size.width * 0.9,
//                   child: StreamBuilder<QuerySnapshot>(
//                     stream: FirebaseFirestore.instance
//                         .collection('/partnerShop')
//                         .snapshots(),
//                     builder: (BuildContext context,
//                         AsyncSnapshot<QuerySnapshot> snapshot) {
//                       if (snapshot.hasError)
//                         return new Text('Error: ${snapshot.error}');
//                       switch (snapshot.connectionState) {
//                         case ConnectionState.waiting:
//                           return new Text('Loading...');
//                         default:
//                           return new ListView(
//                             children: snapshot.data.docs
//                                 .map((DocumentSnapshot document) {
//                               return new ListTile(
//                                   onTap: () {
//                                     print(document.data()['address']);
//                                     Navigator.push(
//                                       context,
//                                       MaterialPageRoute(
//                                           builder: (context) => Quickpay(
//                                               address:
//                                               document.data()['address'],
//                                               name: document.data()['name'])),
//                                     );
//                                   },
//                                   contentPadding: EdgeInsets.symmetric(
//                                       horizontal: 20.0, vertical: 10.0),
//                                   leading: Container(
//                                     padding: EdgeInsets.only(right: 12.0),
//                                     decoration: new BoxDecoration(
//                                         border: new Border(
//                                             right: new BorderSide(
//                                                 width: 1.0,
//                                                 color: Colors.white24))),
//                                     child: Image.network(
//                                         document.data()['url'].trim()),
//                                   ),
//                                   title: Text(
//                                     document.data()['name'],
//                                     style: TextStyle(
//                                         color: Colors.white,
//                                         fontWeight: FontWeight.bold),
//                                   ),
//                                   // subtitle: Text("Intermediate", style: TextStyle(color: Colors.white)),
//
//                                   subtitle: Row(
//                                     children: <Widget>[
//                                       Icon(Icons.linear_scale,
//                                           color: Colors.yellowAccent),
//                                       Text(document.data()['title'],
//                                           style:
//                                           TextStyle(color: Colors.white))
//                                     ],
//                                   ),
//                                   trailing: Icon(Icons.keyboard_arrow_right,
//                                       color: Colors.white, size: 30.0));
//                             }).toList(),
//                           );
//                       }
//                     },
//                   ),
//                 ),
//               ),
//             ],
//           )),
//     );
//   }
//
//   buildForTablet(Orientation orientation) {}
//
//   @override
//   Widget build(BuildContext context) {
//     final double shortestSide = MediaQuery.of(context).size.shortestSide;
//     final bool useMobileLayout = shortestSide < 600.0;
//     final Orientation orientation = MediaQuery.of(context).orientation;
//     return Scaffold(
//       body: ModalProgressHUD(
//         opacity: 0.1,
//         child: useMobileLayout
//             ? buildForPhone(orientation)
//             : buildForTablet(orientation),
//         inAsyncCall: _saving,
//         progressIndicator: CustomLoading(),
//       ),
//     );
//   }
// }
