import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:likewallet/libraryman/open_web_notitle.dart';

class MassagesPage extends StatefulWidget {
  _MassagesPage createState() => new _MassagesPage();
}

class _MassagesPage extends State<MassagesPage> {

  @override
  void initState() {
    super.initState();
  }

  // ข้อมูล
  final List urlNotification = [
    {
      "icon":
      "https://cdn.pixabay.com/photo/2016/10/03/15/42/button-1711966_960_720.png",
      "title": 'โอน-ถอน-จ่าย ไม่คิดค่าธรรมเนียม',
      "detail":
      'ฟรี ฟรี ฟรี!! ค่าธรรมเนียมธนาคาร "ฟรีค่าธรรมเนียม" เรารอมาเนิ่นนานเหลือเกิน สุดท้ายมันก็เกิดขึ้นจริงๆสักที (เฉพาะการใช้บริการธนาคารออนไลน์)'
    },
    {
      "icon":
      "https://cdn.pixabay.com/photo/2016/10/03/15/42/button-1711966_960_720.png",
      "title": 'โอน-ถอน-จ่าย ไม่คิดค่าธรรมเนียม',
      "detail":
      'ฟรี ฟรี ฟรี!! ค่าธรรมเนียมธนาคาร "ฟรีค่าธรรมเนียม" เรารอมาเนิ่นนานเหลือเกิน สุดท้ายมันก็เกิดขึ้นจริงๆสักที (เฉพาะการใช้บริการธนาคารออนไลน์)'
    },
    {
      "icon":
      "https://cdn.pixabay.com/photo/2016/10/03/15/42/button-1711966_960_720.png",
      "title": 'โอน-ถอน-จ่าย ไม่คิดค่าธรรมเนียม',
      "detail":
      'ฟรี ฟรี ฟรี!! ค่าธรรมเนียมธนาคาร "ฟรีค่าธรรมเนียม" เรารอมาเนิ่นนานเหลือเกิน สุดท้ายมันก็เกิดขึ้นจริงๆสักที (เฉพาะการใช้บริการธนาคารออนไลน์)'
    },
    {
      "icon":
      "https://cdn.pixabay.com/photo/2016/10/03/15/42/button-1711966_960_720.png",
      "title": 'โอน-ถอน-จ่าย ไม่คิดค่าธรรมเนียม',
      "detail":
      'ฟรี ฟรี ฟรี!! ค่าธรรมเนียมธนาคาร "ฟรีค่าธรรมเนียม" เรารอมาเนิ่นนานเหลือเกิน สุดท้ายมันก็เกิดขึ้นจริงๆสักที (เฉพาะการใช้บริการธนาคารออนไลน์)'
    }
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Stack(
          alignment: Alignment.center,
          children: <Widget>[
//        _ButtonBack(),
            _TitleNotification(),
            Container(
                margin: EdgeInsets.only(
                  top: mediaQuery(context, "height", 300),
                ),
//                child:  ListView.builder(
//                    itemCount: urlNotification.length,
//                    itemBuilder: (BuildContext ctxt, int index) {
//                      return _body(index);
//                    })
            child:StreamBuilder<QuerySnapshot>(
              stream: FirebaseFirestore.instance.collection('/bellnotifications').where('status', isEqualTo: 'active').snapshots(),
              builder: (BuildContext context, AsyncSnapshot<QuerySnapshot> snapshot) {
                if (snapshot.hasError)
                  return new Text('Error: ${snapshot.error}');
                switch (snapshot.connectionState) {
                  case ConnectionState.waiting: return new Text('Loading...');
                  default:
                    return new ListView(
                      children: snapshot.data!.docs.map((DocumentSnapshot document) {
                        return _body(document);
                      }).toList(),
                    );
                }
              },

            ),
            ),
          ],
        ));
  }

//หัวข้อเมนู
  Widget _TitleNotification() {
    return Positioned(
        top: mediaQuery(context, "height", 200),
        child: GestureDetector(
          child: new Container(
//              color: Colors.lightBlue,
              height: mediaQuery(context, "height", 70),
              alignment: Alignment.center,
              width: MediaQuery.of(context).size.width,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: <Widget>[
                  Container(
                    alignment: Alignment.bottomRight,
                    transform: Matrix4.rotationZ(-0.25),
                    child: Image.asset(
                      LikeWalletImage.iconBuzzer,
                      scale: 2.5,
                      color: Colors.black,
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(
                      left: mediaQuery(context, "width", 30),
                    ),
                  ),
                  Text(AppLocalizations.of(context)!.translate('notifications')),
                ],
              )),
        ));
  }

//ส่วนcard listview
  Widget _body(document) {
    return Padding(
        padding: EdgeInsets.only(
            left: mediaQuery(context, "width", 40),
            right: mediaQuery(context, "width", 40),
            top: mediaQuery(context, "height", 5)),
        child: Stack(
          children: <Widget>[
            Card(
                shape: RoundedRectangleBorder(
                  side: BorderSide(color: Colors.white70, width: 1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: GestureDetector(
                onTap: () {

                  Navigator.push(context,
                      MaterialPageRoute(
                        builder: (context) => WebOpenNoTitle(title: AppLocalizations.of(context)!.translate('notifications'), url: document['url'],)
                      )
                  );
                },
                child: Stack(
                  children: <Widget>[
                    Row(
                      children: <Widget>[
                        Padding(
                          padding:
                          EdgeInsets.all(mediaQuery(context, "height", 25)),
                          child: Container(
                            height: mediaQuery(context, "height", 418),
                            width: mediaQuery(context, "width", 232.5),
                            child: Image.network(
                              document['icon'],
                            ),
                          ),
                        ),
                        border(),
                        Container(
                          margin: EdgeInsets.only(
                            left: mediaQuery(context, "width", 50),
                          ),
                          alignment: Alignment.center,
                          child: Container(
//                            color: Colors.black45,
                            alignment: Alignment.center,
                            height: mediaQuery(context, "height", 418),
                            width: mediaQuery(context, "width", 620.5),
                            child: Column(
                              children: <Widget>[
                                Align(
                                  alignment: Alignment.topLeft,
                                  child: Container(
                                    margin: EdgeInsets.only(
                                      top: mediaQuery(context, "height", 40),
                                    ),
                                    child: Text(
                                      document['title'],
                                      style: TextStyle(
                                          fontSize:
                                          mediaQuery(context, "height", 43),
                                          fontFamily:
                                          AppLocalizations.of(context)!
                            .translate('font1'),
                                          fontWeight: FontWeight.bold,
                                          color: Colors.black),
                                    ),
                                  ),
                                ),
                                Align(
                                  alignment: Alignment.topLeft,
                                  child: Container(
                                    margin: EdgeInsets.only(
                                      top: mediaQuery(context, "height", 30),
                                    ),
                                    child: Text(
                                      document['detail'],
                                      style: TextStyle(
                                          fontSize:
                                          mediaQuery(context, "height", 40),
                                          fontFamily:
                                          AppLocalizations.of(context)!
                            .translate('font1'),
                                          fontWeight: FontWeight.normal,
                                          color: Colors.black),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                )),
            ),
            Positioned(
                top: mediaQuery(context, "height", 394),
                right: mediaQuery(context, "width", 40),
                child: GestureDetector(
                  onTap: () {
                    Navigator.push(context,
                        MaterialPageRoute(
                            builder: (context) => WebOpenNoTitle(title: document['title'], url: document['url'],)
                        )
                    );
                  },
                    child: ClipPath(
                    clipper: new CustomHalfCircleClipper(),
                    child: new Container(
                      alignment: Alignment.center,
                      padding: EdgeInsets.only(
                        bottom: mediaQuery(context, "height", 50),
                      ),
                      height: mediaQuery(context, "height", 175.06),
                      width: mediaQuery(context, "width", 175.06),
                      decoration: new BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(150.0),
                        border: Border.all(color: Colors.grey, width: 0.3),
                      ),
                      child: Text(
                        AppLocalizations.of(context)!.translate('messages_read'),
                        style: TextStyle(
                            fontSize: mediaQuery(context, "height", 30),
                            fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                            fontWeight: FontWeight.normal,
                            color: Colors.black),
                      ),
                    )))
            ),
          ],
        ));
  }

//เส้นคั้น
  Widget border() {
    return Container(
      height: mediaQuery(context, "height", 460),
      decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              spreadRadius: 0,
              blurRadius: 3,
              color: Color(0xff707070).withOpacity(0.2),
              offset: Offset(
                1.1,
                0.0,
              ),
            ),
          ],
          border: Border(
            right: BorderSide(
              color: Color(0xff707070).withOpacity(0.12),
              width: MediaQuery.of(context).size.width * 0.00185185185,
            ),
          )),
    );
  }
}

// สร้างปุ่มครึ่งวงกลม
class CustomHalfCircleClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final Path path = new Path();
    path.lineTo(0.0, size.height / 2);
    path.lineTo(size.width, size.height / 2);
    path.lineTo(size.width, 0);
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) {
    return true;
  }
}
