import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/Theme.dart';

class Invest extends StatefulWidget {
  @override
  _Invest createState() => new _Invest();
}

class _Invest extends State<Invest> {
  List<String> urls = [
    "assets/image/invest_likewallet.png",
    "assets/image/invest_lendex.png",
  ];

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
        child: Scaffold(
            body: Stack(
              children: <Widget>[
                new Container(
                  decoration: BoxDecoration(
                    //การไล่สีพื้นหลัง
                      gradient: LinearGradient(
                        begin: Alignment.topRight,
                        end: Alignment.bottomLeft,
                        stops: [0.2, 0.3, 0.4, 0.6, 0.7],
                        colors: [
                          // Colors are easy thanks to Flutter's Colors class.
                          Color(0xff111112),
                          Color(0xff111112).withOpacity(0.9),
                          Color(0xff111112).withOpacity(0.85),
                          Color(0xff111112).withOpacity(0.8),
                          Color(0xff111112).withOpacity(0.75)
                        ],
                      )),
                ),
                TabBarView(
                  children: <Widget>[
                    HorizontalTab(
                      images: urls,
                      loadMore: _loadMoreItems,
                    ),
//              HorizontalExplicitTab(
//                images: urls, loadMore: _loadMoreItems,
//              ),
//              VerticalTab(images: urls, loadMore: _loadMoreItems)
                  ],
                ),
                Positioned(
                    top: mediaQuery(context, "height", 105),
                    child: backButton(
                        context, LikeWalletAppTheme.gray.withOpacity(0.6))),
              ],
            )),
        length: 1);
  }

  void _loadMoreItems() {
    setState(() {
      urls = new List.from(urls)..addAll(urls);
    });
  }
}

class HorizontalTab extends StatelessWidget {
  final List<String>? images;
  final VoidCallback? loadMore;

  const HorizontalTab({Key? key, this.images, this.loadMore}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final Size cardSize = Size(
        MediaQuery.of(context).size.width * 0.59907407407,
        MediaQuery.of(context).size.height * 0.5735042735);

    return CarouselSlider.builder(
      itemCount: images!.length,
      options: CarouselOptions(
        aspectRatio: 2.0,
        enlargeCenterPage: true,
        autoPlay: true,
      ),
      itemBuilder: (ctx, index, realIdx) {
        return ClipRRect(
          borderRadius: new BorderRadius.circular(16.0),
          child: Image.asset(
            images![index],
            fit: BoxFit.fill,
          ),
        );
      },

    );
    // return SnapList(
    //   padding: EdgeInsets.only(
    //       left: (MediaQuery.of(context).size.width - cardSize.width) / 2),
    //   sizeProvider: (index, data) => cardSize,
    //   separatorProvider: (index, data) => Size(10.0, 10.0),
    //   positionUpdate: (int index) {
    //     if (index == images!.length) {
    //       loadMore!();
    //     }
    //   },
    //   builder: (context, index, data) {
    //     return ClipRRect(
    //       borderRadius: new BorderRadius.circular(16.0),
    //       child: Image.asset(
    //         images![index],
    //         fit: BoxFit.fill,
    //       ),
    //     );
    //   },
    //   count: images!.length,
    // );
  }
}