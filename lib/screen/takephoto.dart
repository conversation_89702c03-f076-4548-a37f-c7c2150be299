import 'dart:io';

import 'package:flutter/material.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/screen_util.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flare_flutter/flare_actor.dart';
import 'package:camera/camera.dart';
import 'package:path/path.dart' show join;
import 'package:path_provider/path_provider.dart';
import 'package:likewallet/kyc/crop_image.dart';
import 'package:likewallet/kyc/kyc.dart';

import 'package:path/path.dart' show basename;
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:likewallet/app_config.dart';

import 'package:dio/dio.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/ImageTheme.dart';

import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:flutter/foundation.dart';
import 'package:fluttertoast/fluttertoast.dart';

enum stateKYCPhoto { FRONT, BACK, SELFIE }

class takePhoto extends StatefulWidget {
  takePhoto({required this.pageFront, required this.firstCamera});
  final CameraDescription firstCamera;
  final int pageFront;
  _takePhoto createState() =>
      new _takePhoto(page: pageFront, firstCamera: firstCamera);
}

class _takePhoto extends State<takePhoto> {
  _takePhoto({required this.page, required this.firstCamera});

  late List cameras;
  final CameraDescription firstCamera;
  late int selectedCameraIdx;
  late CameraDescription selectedCamera;
  late CameraLensDirection lensDirection;

  bool checkFace = false;

  /// ///
  Key containerCamera = UniqueKey();
  late CameraController _controller;
  late Future<void> _initializeControllerFuture;
  String curImage = 'none';
  final int page;
  int number = 0;
  int count = 0;

  bool _saving = false;

  changeContent(value) {
    if (value == 0) {
      setState(() {
        curImage = 'none';
      });
    }
    setState(() {
      number = value;
    });
  }

  bool front_check = false;
  bool end_check = false;
  bool selfie_check = false;
  late SharedPreferences sharedPreferences;

  Future<List<String>> upload(File file, photo) async {
    setState(() {
      _saving = true;
    });
    if (photo == stateKYCPhoto.FRONT) {
      if (file == null) return ['false'];
      String base64Image = base64Encode(file.readAsBytesSync());
      String fileName = file.path.split("/").last;
      var url = Uri.https(env.apiCheck, '/uploadReadIDFromBase');
      final response = await http.post(url, body: {
        "image": base64Image,
        "name": fileName,
      });
      print(response.statusCode);
      final body = json.decode(response.body);

      print(body["result"]);
      print(body["result"]["id"]);
      print(body["result"]["url"]);
      return [body["result"]["url"]];
    } else if (photo == stateKYCPhoto.BACK) {
      if (file == null) return ['false'];
      String base64Image = base64Encode(file.readAsBytesSync());
      String fileName = file.path.split("/").last;

      var url = Uri.https(env.apiCheck, '/uploadBackFromBase');
      final response = await http.post(url, body: {
        "image": base64Image,
        "name": fileName,
      });
      print(response.statusCode);
      final body = json.decode(response.body);

      print(body["result"]);
      print(body["result"]["id"]);
      print(body["result"]["url"]);
      return [body["result"]["url"]];
    } else if (photo == stateKYCPhoto.SELFIE) {
      if (file == null) return ['false'];
      String base64Image = base64Encode(file.readAsBytesSync());
      String fileName = file.path.split("/").last;
      var url = Uri.https(env.apiCheck, '/uploadBackFromBase');
      final response = await http.post(url, body: {
        "image": base64Image,
        "name": fileName,
      });
      print(response.statusCode);
      final body = json.decode(response.body);

      print(body["result"]);
      print(body["result"]["id"]);
      print(body["result"]["url"]);
      return [body["result"]["url"]];
    }else {
      return Future.value();
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    availableCameras().then((availableCameras) {
      cameras = availableCameras;
      if (cameras.length > 0) {
        setState(() {
          // 2
          selectedCameraIdx = 0;
        });
        _initializeControllerFuture =
            _initCameraController(cameras[selectedCameraIdx]).then((void v) {});
      } else {
        print("No camera available");
      }
    }).catchError((err) {
      // 3
      print('Error: $err.code\nError Message: $err.message');
    });

//    _controller = CameraController(
//      // Get a specific camera from the list of available cameras.
//      firstCamera,
//      // Define the resolution to use.
//      ResolutionPreset.max,
//    );

    // Next, initialize the controller. This returns a Future.
//    _initializeControllerFuture = _controller.initialize();
  }

  void showColoredToast(msg, colors) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_LONG,
        backgroundColor: colors,
        textColor: Colors.white);
  }
//
//  Future<void> takePicSelfie() async {
//      try {
//        // Ensure that the camera is initialized.
//
//        // Construct the path where the image should be saved using the path
//        // package.
//        final path = join(
//          // Store the picture in the temp directory.
//          // Find the temp directory using the `path_provider` plugin.
//          (await getTemporaryDirectory()).path,
//          '${DateTime.now()}.png',
//        );
//        final path2 = join(
//          // Store the picture in the temp directory.
//          // Find the temp directory using the `path_provider` plugin.
//          (await getTemporaryDirectory()).path,
//          '${DateTime.now()}2.png',
//        );
//
//        await _scanKey.currentState.stop();
//        // Attempt to take a picture and log where it's been saved.
//        await _scanKey.currentState.takePicture(path);
//
//        await _scanKey.currentState.start();
//        print(path2);
//        ImageProcessor.cropSquare(path, path2, false).then((data){
//          if(!mounted) return;
//          setState(() {
//            curImage = path2;
//          });
//
//        });
//      } catch (e) {
//        // If an error occurs, log the error to the console.
//        print(e);
//      }
//  }

  Future<void> takePic() async {
    // Take the Picture in a try / catch block. If anything goes wrong,
    // catch the error.
    try {
      // Ensure that the camera is initialized.
      await _initCameraController;

      // Construct the path where the image should be saved using the
      // pattern package.
      final path = join(
        // Store the picture in the temp directory.
        // Find the temp directory using the `path_provider` plugin.
        (await getTemporaryDirectory()).path,
        '${DateTime.now()}.png',
      );
      final path2 = join(
        // Store the picture in the temp directory.
        // Find the temp directory using the `path_provider` plugin.
        (await getTemporaryDirectory()).path,
        '${DateTime.now()}2.png',
      );
      // Attempt to take a picture and log where it's been saved.
      await _controller.takePicture();
      ImageProcessor.cropSquare(path, path2, false).then((data) {
        if (!mounted) return;
        setState(() {
          curImage = path2;
        });
      });
      // If the picture was taken, display it on a new screen.
      return Future.value();
    } catch (e) {
      // If an error occurs, log the error to the console.
      print(e);
    }
  }

  Future<bool> _onWillPop() async {
    Navigator.pop(context, 'none');
    return Future.value();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: _onWillPop,
      child: Scaffold(
        backgroundColor: Color(0xff141322),
        body: ModalProgressHUD(
          opacity: 0.1,
          child: page == 1
              ? front()
              : page == 2
              ? back()
              : page == 3
              ? Selfie()
              : Text(""),
          inAsyncCall: _saving,
          progressIndicator: CustomLoading(),
        ),
      ),
    );
  }

  Widget front() {
    return Stack(
      alignment: Alignment.center,
      children: <Widget>[
        //ปุ่ม back
        Positioned(
            top: MediaQuery.of(context).size.height *
                Screen_util("height", 139.33),
            child: GestureDetector(
              child: new Container(
                alignment: Alignment.centerLeft,
                width: MediaQuery.of(context).size.width,
                child: new IconButton(
                  icon: new Icon(
                    Icons.arrow_back_ios,
                    size: MediaQuery.of(context).size.height *
                        Screen_util("height", 44.47),
                  ),
                  color: Color(0xff707071),
                  onPressed: () {
                    Navigator.pop(context, 'none');
                  },
                ),
              ),
            )),
        Positioned(
          top: MediaQuery.of(context).size.height * Screen_util("height", 268),
          child: new Container(
              alignment: Alignment.center,
              width: MediaQuery.of(context).size.width * 0.9,
              child: Text(
                AppLocalizations.of(context)!.translate('front_title'),
                style: TextStyle(
                    color: Color(0xff707071),
                    fontSize: MediaQuery.of(context).size.height *
                        Screen_util("height", 45),
                    fontFamily: 'Proxima Nova'),
              )),
        ),
        if (number == 0)
          Positioned(
            top:
            MediaQuery.of(context).size.height * Screen_util("height", 390),
            child: Container(
              color: Colors.grey.withOpacity(0.3),
              height: MediaQuery.of(context).size.height *
                  Screen_util("height", 794),
              width: MediaQuery.of(context).size.width *
                  Screen_util("width", 1080),
              child: page == 1
                  ? FutureBuilder<void>(
                future: _initializeControllerFuture,
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.done) {
                    // If the Future is complete, display the preview.
                    return AspectRatio(
                      aspectRatio: 1.5,
                      child: ClipRect(
                        child: Transform.scale(
                          scale: 1.5 / _controller.value.aspectRatio,
                          child: Center(
                            child: AspectRatio(
                              aspectRatio: _controller.value.aspectRatio,
                              child: CameraPreview(_controller),
                            ),
                          ),
                        ),
                      ),
                    );
                  } else {
                    // Otherwise, display a loading indicator.
                    return Center(child: CircularProgressIndicator());
                  }
                },
              )
                  : Text(""),
            ),
          ),
        if (number == 1)
          Positioned(
            top:
            MediaQuery.of(context).size.height * Screen_util("height", 390),
            child: Container(
              color: Colors.grey.withOpacity(0.3),
              height: MediaQuery.of(context).size.height *
                  Screen_util("height", 786),
              width: MediaQuery.of(context).size.width *
                  Screen_util("width", 1080),
              child: curImage == 'none'
                  ? Center(child: CircularProgressIndicator())
                  : Container(
                height: MediaQuery.of(context).size.height *
                    Screen_util("height", 786),
                width: MediaQuery.of(context).size.width *
                    Screen_util("width", 1080),
                color: LikeWalletAppTheme.black,
                child: Image.file(
                  File(curImage),
                  height: 600,
                  width: 600,
                ),
              ),
            ),
          ),

        if (number == 0)
          Positioned(
              top: MediaQuery.of(context).size.height *
                  Screen_util("height", 1072),
              left:
              MediaQuery.of(context).size.width * Screen_util("width", 450),
              child: Container(
                height: MediaQuery.of(context).size.height *
                    Screen_util("height", 200),
                width: MediaQuery.of(context).size.width *
                    Screen_util("width", 200),
                decoration: BoxDecoration(
                  color: Color(0xff141322),
                  shape: BoxShape.circle,
                ),
              )),
        BorderCamera(),
        BG(),
        if (number == 0)
          Positioned(
            top: MediaQuery.of(context).size.height *
                Screen_util("height", 1320),
            child: new Container(
                alignment: Alignment.center,
                width: MediaQuery.of(context).size.width * 0.9,
                child: Text(
                  AppLocalizations.of(context)!.translate('front_details'),
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      color: Color(0xff707071),
                      fontSize: MediaQuery.of(context).size.height *
                          Screen_util("height", 45),
                      fontFamily: 'Proxima Nova'),
                )),
          ),
        if (number == 0)
          Positioned(
            top: MediaQuery.of(context).size.height *
                Screen_util("height", 1080),
            child: GestureDetector(
                onTap: () {
                  takePic();
                  Future.delayed(Duration(milliseconds: 200));
                  changeContent(1);
                },
                child: Image.asset(
                  'assets/image/take_photo/button_take_photo.png',
                  height: MediaQuery.of(context).size.height *
                      Screen_util("height", 200),
                  width: MediaQuery.of(context).size.width *
                      Screen_util("width", 200),
                )),
          ),
        if (number == 1)
          Positioned(
            top: MediaQuery.of(context).size.height *
                Screen_util("height", 1284),
            child: GestureDetector(
              onTap: () {
                changeContent(0);
              },
              child: Container(
                  alignment: Alignment.center,
                  height: MediaQuery.of(context).size.height *
                      Screen_util("height", 257),
                  width: MediaQuery.of(context).size.width *
                      Screen_util("width", 257),
                  decoration: BoxDecoration(
                      color: Color(0xff282534),
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: Colors.white,
                        width: MediaQuery.of(context).size.width *
                            Screen_util("height", 3),
                      )),
                  child: Text(
                    AppLocalizations.of(context)!.translate('button_again'),
                    style: TextStyle(
                      color: Color(0xff0FE8D8),
                      fontWeight: FontWeight.bold,
                      fontSize: MediaQuery.of(context).size.height *
                          Screen_util("height", 36),
                      fontFamily:
                      AppLocalizations.of(context)!.translate('font1'),
                    ),
                  )),
            ),
          ),
        //ใช้รูปนี้
        if (number == 1)
          Positioned(
            top: MediaQuery.of(context).size.height *
                Screen_util("height", 1592),
            child: GestureDetector(
              onTap: () {
                if (curImage != 'none') {
                  upload(File(curImage), stateKYCPhoto.FRONT)
                      .then((data) async {
                    if (data[0] != "false") {
                      Navigator.pop(context, data[0]);
                    } else {
                      Navigator.pop(context, 'none');
                    }
                  });
                } else {
                  setState(() {
                    _saving = false;
                  });
                }
              },
              child: Container(
                  alignment: Alignment.center,
                  height: MediaQuery.of(context).size.height *
                      Screen_util("height", 257),
                  width: MediaQuery.of(context).size.width *
                      Screen_util("width", 257),
                  decoration: BoxDecoration(
                    color: Color(0xff0FE8D8),
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Color(0xff000000),
                      width: MediaQuery.of(context).size.width *
                          Screen_util("height", 3),
                    ),
                  ),
                  child: Text(
                    AppLocalizations.of(context)!.translate('button_yes'),
                    style: TextStyle(
                      color: Color(0xff000000),
                      fontSize: MediaQuery.of(context).size.height *
                          Screen_util("height", 36),
                      fontWeight: FontWeight.bold,
                      fontFamily:
                      AppLocalizations.of(context)!.translate('font1'),
                    ),
                  )),
            ),
          ),
      ],
    );
  }

  Widget back() {
    return Stack(
      alignment: Alignment.center,
      children: <Widget>[
        //ปุ่ม back
        Positioned(
            top: MediaQuery.of(context).size.height *
                Screen_util("height", 139.33),
            child: GestureDetector(
              child: new Container(
                alignment: Alignment.centerLeft,
                width: MediaQuery.of(context).size.width,
                child: new IconButton(
                  icon: new Icon(
                    Icons.arrow_back_ios,
                    size: MediaQuery.of(context).size.height *
                        Screen_util("height", 44.47),
                  ),
                  color: Color(0xff707071),
                  onPressed: () => {Navigator.pop(context, 'none')},
                ),
              ),
            )),
        Positioned(
          top: MediaQuery.of(context).size.height * Screen_util("height", 268),
          child: new Container(
              alignment: Alignment.center,
              width: MediaQuery.of(context).size.width * 0.9,
              child: Text(
                AppLocalizations.of(context)!.translate('back_title'),
                style: TextStyle(
                    color: Color(0xff707071),
                    fontSize: MediaQuery.of(context).size.height *
                        Screen_util("height", 45),
                    fontFamily:
                    AppLocalizations.of(context)!.translate('font1')),
              )),
        ),

        if (number == 0)
          Positioned(
            top:
            MediaQuery.of(context).size.height * Screen_util("height", 390),
            child: Container(
              color: Colors.grey.withOpacity(0.3),
              height: MediaQuery.of(context).size.height *
                  Screen_util("height", 794),
              width: MediaQuery.of(context).size.width *
                  Screen_util("width", 1080),
              child: page == 2
                  ? FutureBuilder<void>(
                future: _initializeControllerFuture,
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.done) {
                    // If the Future is complete, display the preview.
                    return AspectRatio(
                      aspectRatio: 1,
                      child: ClipRect(
                        child: Transform.scale(
                          scale: 1.5 / _controller.value.aspectRatio,
                          child: Center(
                            child: AspectRatio(
                              aspectRatio: _controller.value.aspectRatio,
                              child: CameraPreview(_controller),
                            ),
                          ),
                        ),
                      ),
                    );
                  } else {
                    // Otherwise, display a loading indicator.
                    return Center(child: CircularProgressIndicator());
                  }
                },
              )
                  : Text(""),
            ),
          ),
        if (number == 1)
          Positioned(
            top:
            MediaQuery.of(context).size.height * Screen_util("height", 390),
            child: Container(
              color: Colors.grey.withOpacity(0.3),
              height: MediaQuery.of(context).size.height *
                  Screen_util("height", 800),
              width: MediaQuery.of(context).size.width *
                  Screen_util("width", 1080),
              child: curImage == 'none'
                  ? Center(child: CircularProgressIndicator())
                  : Container(
                height: MediaQuery.of(context).size.height *
                    Screen_util("height", 786),
                width: MediaQuery.of(context).size.width *
                    Screen_util("width", 1080),
                color: LikeWalletAppTheme.black,
                child: Image.file(
                  File(curImage),
                  height: 600,
                  width: 600,
                ),
              ),
            ),
          ),

        if (number == 0)
          Positioned(
              top: MediaQuery.of(context).size.height *
                  Screen_util("height", 1072),
              left:
              MediaQuery.of(context).size.width * Screen_util("width", 450),
              child: Container(
                height: MediaQuery.of(context).size.height *
                    Screen_util("height", 200),
                width: MediaQuery.of(context).size.width *
                    Screen_util("width", 200),
                decoration: BoxDecoration(
                  color: Color(0xff141322),
                  shape: BoxShape.circle,
                ),
              )),
        BorderCamera(),
        BG(),
        if (number == 0)
          Positioned(
            top: MediaQuery.of(context).size.height *
                Screen_util("height", 1320),
            child: new Container(
                alignment: Alignment.center,
                width: MediaQuery.of(context).size.width * 0.9,
                child: Text(
                  AppLocalizations.of(context)!.translate('back_details'),
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Color(0xff707071),
                    fontSize: MediaQuery.of(context).size.height *
                        Screen_util("height", 45),
                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  ),
                )),
          ),
        if (number == 0)
          Positioned(
            top: MediaQuery.of(context).size.height *
                Screen_util("height", 1080),
            child: GestureDetector(
                onTap: () {
                  takePic();
                  changeContent(1);
                },
                child: Image.asset(
                  'assets/image/take_photo/button_take_photo.png',
                  height: MediaQuery.of(context).size.height *
                      Screen_util("height", 200),
                  width: MediaQuery.of(context).size.width *
                      Screen_util("width", 200),
                )),
          ),

        if (number == 1)
          Positioned(
            top: MediaQuery.of(context).size.height *
                Screen_util("height", 1284),
            child: GestureDetector(
              onTap: () {
                changeContent(0);
              },
              child: Container(
                  alignment: Alignment.center,
                  height: MediaQuery.of(context).size.height *
                      Screen_util("height", 257),
                  width: MediaQuery.of(context).size.width *
                      Screen_util("width", 257),
                  decoration: BoxDecoration(
                      color: Color(0xff282534),
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: Colors.white,
                        width: MediaQuery.of(context).size.width *
                            Screen_util("height", 3),
                      )),
                  child: Text(
                    AppLocalizations.of(context)!.translate('button_again'),
                    style: TextStyle(
                      color: Color(0xff0FE8D8),
                      fontWeight: FontWeight.bold,
                      fontSize: MediaQuery.of(context).size.height *
                          Screen_util("height", 36),
                      fontFamily:
                      AppLocalizations.of(context)!.translate('font1'),
                    ),
                  )),
            ),
          ),
        //ใช้รูปนี้
        if (number == 1)
          Positioned(
            top: MediaQuery.of(context).size.height *
                Screen_util("height", 1592),
            child: GestureDetector(
              onTap: () {
                if (curImage != 'none') {
                  upload(File(curImage), stateKYCPhoto.FRONT)
                      .then((data) async {
                    if (data[0] != "false") {
                      Navigator.pop(context, data[0]);
                    } else {
                      Navigator.pop(context, 'none');
                    }
                  });
                } else {
                  setState(() {
                    _saving = false;
                  });
                }
              },
              child: Container(
                  alignment: Alignment.center,
                  height: MediaQuery.of(context).size.height *
                      Screen_util("height", 257),
                  width: MediaQuery.of(context).size.width *
                      Screen_util("width", 257),
                  decoration: BoxDecoration(
                    color: Color(0xff0FE8D8),
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Color(0xff000000),
                      width: MediaQuery.of(context).size.width *
                          Screen_util("height", 3),
                    ),
                  ),
                  child: Text(
                    AppLocalizations.of(context)!.translate('button_yes'),
                    style: TextStyle(
                      color: Color(0xff000000),
                      fontSize: MediaQuery.of(context).size.height *
                          Screen_util("height", 36),
                      fontWeight: FontWeight.bold,
                      fontFamily:
                      AppLocalizations.of(context)!.translate('font1'),
                    ),
                  )),
            ),
          ),
      ],
    );
  }

  Widget Selfie() {
    return Stack(
      alignment: Alignment.center,
      children: <Widget>[
        //ปุ่ม back
        Positioned(
            top: MediaQuery.of(context).size.height *
                Screen_util("height", 139.33),
            child: GestureDetector(
              child: new Container(
                alignment: Alignment.centerLeft,
                width: MediaQuery.of(context).size.width,
                child: new IconButton(
                  icon: new Icon(
                    Icons.arrow_back_ios,
                    size: MediaQuery.of(context).size.height *
                        Screen_util("height", 44.47),
                  ),
                  color: Color(0xff707071),
                  onPressed: () => {
                    setState(() {
                      Navigator.pop(context, 'none');
                    })
                  },
                ),
              ),
            )),
        Positioned(
          top: MediaQuery.of(context).size.height * Screen_util("height", 268),
          child: new Container(
              alignment: Alignment.center,
              width: MediaQuery.of(context).size.width * 0.9,
              child: Text(
                AppLocalizations.of(context)!.translate('selfie_title'),
                style: TextStyle(
                    color: Color(0xff707071),
                    fontSize: MediaQuery.of(context).size.height *
                        Screen_util("height", 45),
                    fontFamily:
                    AppLocalizations.of(context)!.translate('font1')),
              )),
        ),

        Positioned(
          top: MediaQuery.of(context).size.height * Screen_util("height", 390),
          child: Container(
            color: Colors.grey.withOpacity(0.3),
            height:
            MediaQuery.of(context).size.height * Screen_util("height", 794),
            width:
            MediaQuery.of(context).size.width * Screen_util("width", 1080),
            child: page == 3
                ? FutureBuilder<void>(
              future: _initializeControllerFuture,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.done) {
                  // If the Future is complete, display the preview.
                  return AspectRatio(
                    aspectRatio: 1,
                    child: ClipRect(
                      child: Transform.scale(
                        scale: 1.5 / _controller.value.aspectRatio,
                        child: Center(
                          child: AspectRatio(
                            aspectRatio: _controller.value.aspectRatio,
                            child: CameraPreview(_controller),
                          ),
                        ),
                      ),
                    ),
                  );
                } else {
                  // Otherwise, display a loading indicator.
                  return Center(child: CircularProgressIndicator());
                }
              },
            )
                : Text(""),
          ),
        ),
        if (number == 1)
          Positioned(
            top:
            MediaQuery.of(context).size.height * Screen_util("height", 390),
            child: Container(
              color: Colors.black,
              height: MediaQuery.of(context).size.height *
                  Screen_util("height", 780),
              width: MediaQuery.of(context).size.width *
                  Screen_util("width", 1080),
            ),
          ),
        if (number == 1)
          Positioned(
            top:
            MediaQuery.of(context).size.height * Screen_util("height", 390),
            child: Container(
              color: Colors.grey.withOpacity(0.3),
              height: MediaQuery.of(context).size.height *
                  Screen_util("height", 786),
              width: MediaQuery.of(context).size.width *
                  Screen_util("width", 1080),
              child: curImage == 'none'
                  ? Center(child: CircularProgressIndicator())
                  : Container(
                height: MediaQuery.of(context).size.height *
                    Screen_util("height", 786),
                width: MediaQuery.of(context).size.width *
                    Screen_util("width", 1080),
                color: LikeWalletAppTheme.black,
                child: Image.file(
                  File(curImage),
                  height: 600,
                  width: 600,
                ),
              ),
            ),
          ),
        if (number == 0)
          Positioned(
              top: MediaQuery.of(context).size.height *
                  Screen_util("height", 1072),
              left:
              MediaQuery.of(context).size.width * Screen_util("width", 450),
              child: Container(
                height: MediaQuery.of(context).size.height *
                    Screen_util("height", 200),
                width: MediaQuery.of(context).size.width *
                    Screen_util("width", 200),
                decoration: BoxDecoration(
                  color: Color(0xff141322),
                  shape: BoxShape.circle,
                ),
              )),
        BorderCamera(),
        BG(),
        if (number == 0)
          Positioned(
            top: MediaQuery.of(context).size.height *
                Screen_util("height", 1320),
            child: new Container(
                alignment: Alignment.center,
                width: MediaQuery.of(context).size.width * 0.9,
                child: Column(
                  children: <Widget>[
                    Text(
                      AppLocalizations.of(context)!.translate('selfie_details1'),
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Color(0xff707071),
                        fontSize: MediaQuery.of(context).size.height *
                            Screen_util("height", 45),
                        fontFamily:
                        AppLocalizations.of(context)!.translate('font1'),
                      ),
                    ),
//                    Row(
//                      children: <Widget>[
//                        Text(
//                          AppLocalizations.of(context)
//                              .translate('selfie_details2'),
//                          style: TextStyle(
//                            color: Color(0xff707071),
//                            fontSize: MediaQuery.of(context).size.height *
//                                Screen_util("height", 45),
//                            fontFamily:
//                                AppLocalizations.of(context)!.translate('font1'),
//                          ),
//                        ),
//                        Text(
//                          AppLocalizations.of(context)
//                              .translate('selfie_details3'),
//                          style: TextStyle(
//                            color: Color(0xffFFFFFF),
//                            fontSize: MediaQuery.of(context).size.height *
//                                Screen_util("height", 45),
//                            fontFamily:
//                                AppLocalizations.of(context)!.translate('font1'),
//                          ),
//                        ),
//                      ],
//                    )
                  ],
                )),
          ),
        if (number == 0)
          Positioned(
            top: MediaQuery.of(context).size.height *
                Screen_util("height", 1080),
            child: GestureDetector(
                onTap: () {
                  takePic();
                  changeContent(1);
                },
                child: Image.asset(
                  'assets/image/take_photo/button_take_photo.png',
                  height: MediaQuery.of(context).size.height *
                      Screen_util("height", 200),
                  width: MediaQuery.of(context).size.width *
                      Screen_util("width", 200),
                )),
          ),
        if (number == 0)
          Positioned(
            bottom:
            MediaQuery.of(context).size.height * Screen_util("height", 20),
            child: Container(
              width: MediaQuery.of(context).size.width,
              height: mediaQuery(context, "height", 750),
              child: FlareActor(
                "assets/animation/kyc/kyc.flr",
                alignment: Alignment.center,
                fit: BoxFit.contain,
                animation: "index",
              ),
            ),
          ),

        if (number == 1)
          Positioned(
            top: MediaQuery.of(context).size.height *
                Screen_util("height", 1284),
            child: GestureDetector(
              onTap: () {
                changeContent(0);
              },
              child: Container(
                  alignment: Alignment.center,
                  height: MediaQuery.of(context).size.height *
                      Screen_util("height", 257),
                  width: MediaQuery.of(context).size.width *
                      Screen_util("width", 257),
                  decoration: BoxDecoration(
                      color: Color(0xff282534),
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: Colors.white,
                        width: MediaQuery.of(context).size.width *
                            Screen_util("height", 3),
                      )),
                  child: Text(
                    AppLocalizations.of(context)!.translate('button_again'),
                    style: TextStyle(
                      color: Color(0xff0FE8D8),
                      fontWeight: FontWeight.bold,
                      fontSize: MediaQuery.of(context).size.height *
                          Screen_util("height", 36),
                      fontFamily:
                      AppLocalizations.of(context)!.translate('font1'),
                    ),
                  )),
            ),
          ),
        if (number == 1)
          Positioned(
            top: MediaQuery.of(context).size.height *
                Screen_util("height", 1592),
            child: GestureDetector(
              onTap: () {
                if (curImage != 'none') {
                  upload(File(curImage), stateKYCPhoto.FRONT)
                      .then((data) async {
                    if (data[0] != "false") {
                      Navigator.pop(context, data[0]);
                    } else {
                      Navigator.pop(context, 'none');
                    }
                  });
                } else {
                  setState(() {
                    _saving = false;
                  });
                }
              },
              child: Container(
                  alignment: Alignment.center,
                  height: MediaQuery.of(context).size.height *
                      Screen_util("height", 257),
                  width: MediaQuery.of(context).size.width *
                      Screen_util("width", 257),
                  decoration: BoxDecoration(
                    color: Color(0xff0FE8D8),
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Color(0xff000000),
                      width: MediaQuery.of(context).size.width *
                          Screen_util("height", 3),
                    ),
                  ),
                  child: Text(
                    AppLocalizations.of(context)!.translate('button_yes'),
                    style: TextStyle(
                      color: Color(0xff000000),
                      fontSize: MediaQuery.of(context).size.height *
                          Screen_util("height", 36),
                      fontWeight: FontWeight.bold,
                      fontFamily:
                      AppLocalizations.of(context)!.translate('font1'),
                    ),
                  )),
            ),
          ),
        Positioned(
          top: MediaQuery.of(context).size.height * Screen_util("height", 100),
          right: 10,
          child: GestureDetector(
            onTap: () async {
              print('switch camera');
//              print(cameraLensDirection);
//
              _onSwitchCamera();
//              if(cameraLensDirection == CameraLensDirection.front){
//                print('front');
//                setState(() async {
//                  cameraLensDirection = CameraLensDirection.back;
//
//                  await _controller.initialize();
//                });
//
//                print(cameraLensDirection);
//              }else{
//                print('back');
//                setState(() async  {
//                  cameraLensDirection = CameraLensDirection.front;
//                  await _controller.initialize();
//                });
//              }
            },
            child: Container(
                alignment: Alignment.center,
                height: MediaQuery.of(context).size.height *
                    Screen_util("height", 150),
                width: MediaQuery.of(context).size.width *
                    Screen_util("width", 150),
                decoration: BoxDecoration(
                  color: Color(0xff0FE8D8),
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Color(0xff000000),
                    width: MediaQuery.of(context).size.width *
                        Screen_util("height", 3),
                  ),
                ),
                child: new Icon(
                  Icons.switch_camera,
                )),
          ),
        )
      ],
    );
  }

  void _onSwitchCamera() {
    selectedCameraIdx =
    selectedCameraIdx < cameras.length - 1 ? selectedCameraIdx + 1 : 0;
    CameraDescription selectedCamera = cameras[selectedCameraIdx];
    _initCameraController(selectedCamera);
  }

  Future _initCameraController(CameraDescription cameraDescription) async {
    if (_controller != null) {
//      await _controller.dispose();
    }

    // 3
    _controller = CameraController(cameraDescription, ResolutionPreset.high);

    // If the controller is updated then update the UI.
    // 4
    _controller.addListener(() {
      // 5
      if (mounted) {
        setState(() {});
      }

      if (_controller.value.hasError) {
        print('Camera error ${_controller.value.errorDescription}');
      }
    });

    // 6
    try {
      await _controller.initialize();
    } on CameraException catch (e) {
//      _showCameraException(e);
      print(e);
    }

    if (mounted) {
      setState(() {});
    }
  }

  Widget BG() {
    return Positioned(
        bottom: 0,
        child: Container(
          child: Image.asset(
            LikeWalletImage.bg_take_photo,
            fit: BoxFit.fill,
            height: mediaQuery(context, 'height', 1325.87),
            width: mediaQuery(context, 'width', 1080),
          ),
        ));
  }

  SetBool_front(bool value) async {
    sharedPreferences = await SharedPreferences.getInstance();
    setState(() {
      front_check = value;
      sharedPreferences.setBool("front_check", front_check);
    });
  }

  SetBool_end(bool value) async {
    sharedPreferences = await SharedPreferences.getInstance();
    setState(() {
      end_check = value;
      sharedPreferences.setBool("end_check", end_check);
    });
  }

  SetBool_selfie(bool value) async {
    sharedPreferences = await SharedPreferences.getInstance();
    setState(() {
      selfie_check = value;
      sharedPreferences.setBool("selfie_check", selfie_check);
    });
  }

  Widget BorderCamera() {
    return Positioned(
      top: MediaQuery.of(context).size.height * Screen_util("height", 470),
      child: Image.asset('assets/image/take_photo/border_camera.png'),
      height: mediaQuery(context, 'height', 608.3),
      width: mediaQuery(context, 'width', 924.53),
    );
  }
}