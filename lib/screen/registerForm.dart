import 'dart:convert';
import 'dart:async';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/libraryman/secret_pass_first.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:likewallet/login/confirmOTP.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:likewallet/login/sing_in.dart';
import 'package:likewallet/app_config.dart';
import 'package:http/http.dart' as http;
import 'package:likewallet/libraryman/auth.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/Theme.dart';
import 'package:flutter/services.dart';
import 'package:scan/scan.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:likewallet/animationPage.dart';
import 'package:likewallet/libraryman/setPin.dart';
import 'package:likewallet/libraryman/keyboard_done_widget.dart';
import 'dart:io';
import 'dart:ui';
import 'package:flutter/rendering.dart';
import 'package:likewallet/bank/banking.dart';
import 'package:image_picker/image_picker.dart';

const String kNavigationExamplePage = '''
<!DOCTYPE html><html>
<head><title>Navigation Delegate Example</title></head>
<body>
<p>
The navigation delegate is set to block navigation to the youtube website.
</p>
<ul>
<ul><a href="https://www.youtube.com/">https://www.youtube.com/</a></ul>
<ul><a href="https://www.google.com/">https://www.google.com/</a></ul>
</ul>
</body>
</html>
''';

class REGISTER_FORM extends StatefulWidget {
  REGISTER_FORM({
    this.checkIf,
    this.phone_number,
    this.prefix,
    this.oldFirstName,
    this.oldLastName,
    this.customToken,
    this.passCode,
  });

  final String? customToken;
  final String? phone_number;
  final String? checkIf;
  final String? prefix;
  final String? oldFirstName;
  final String? oldLastName;
  final String? passCode;
  _REGISTER_FORM createState() => new _REGISTER_FORM(
      customToken: customToken,
      checkIf: checkIf,
      phone_number: phone_number,
      prefix: prefix,
      oldFirstName: oldFirstName,
      oldLastName: oldLastName,
      passCode: passCode);
}

enum FormType { login, register }

class _REGISTER_FORM extends State<REGISTER_FORM> {
  _REGISTER_FORM({
    this.checkIf,
    this.phone_number,
    this.prefix,
    this.oldFirstName,
    this.oldLastName,
    this.customToken,
    this.passCode,
  });
  final String? phone_number;
  final String? checkIf;
  final String? customToken;
  final String? prefix;
  final String? oldFirstName;
  final String? oldLastName;
  final String? passCode;

  String dropdownValue = '+66';
  TextEditingController fname = new TextEditingController();
  TextEditingController lname = new TextEditingController();
  TextEditingController mobile = new TextEditingController();
  TextEditingController ref = new TextEditingController();
  TextEditingController email = new TextEditingController();
  TextEditingController password = new TextEditingController();
  TextEditingController confirmPassword = new TextEditingController();

  providerSMS roundSMS = providerSMS.Twilio;
  FirebaseAuth _auth = FirebaseAuth.instance;
  late String verificationId;
  String firstName = "";
  String lastName = "";
  String phoneNumber = "";

  String refCode = 'no';

  bool agreePolicy = false;
  bool _saving = false;
  late BaseAuth auth;

  FocusNode doneFocusNode = new FocusNode();
  late OverlayEntry? overlayEntry;

  //done button zone
  showOverlay(BuildContext context) {
    if (overlayEntry != null) return;
    OverlayState? overlayState = Overlay.of(context);
    overlayEntry = OverlayEntry(builder: (context) {
      return Positioned(
          bottom: MediaQuery.of(context).viewInsets.bottom,
          right: 0.0,
          left: 0.0,
          child: InputDoneView());
    });

    overlayState!.insert(overlayEntry!);
  }

  removeOverlay() {
    if (overlayEntry != null) {
      overlayEntry!.remove();
      overlayEntry = null;
    }
  }

  @override
  void dispose() {
    doneFocusNode.dispose();
    super.dispose();
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    //check prefix code phone number
    if (oldFirstName != null) {
      setState(() {
        firstName = oldFirstName!;
        fname.text = oldFirstName!;
      });
    }
    if (oldLastName != null) {
      setState(() {
        lastName = oldLastName!;
        lname.text = oldLastName!;
      });
    }
    if (prefix != null) {
      setState(() {
        dropdownValue = prefix!;
      });
    }

    auth = Auth();
    //set onchange
    fname.addListener(setName);
    lname.addListener(setLastName);
    mobile.addListener(setMobile);
    ref.addListener(setRef);

    if (checkIf == 'nouser') {
      if (!mounted) return;
      setState(() {
        mobile.text = phone_number!;
        if (phone_number!.substring(0, 1) == '0') {
          phoneNumber = dropdownValue +
              phone_number!.substring(1, phone_number!.length).toString();
        } else {
          showShortToast(AppLocalizations.of(context)!.translate('prefixzero'));
        }
      });
    }
  }

  Future<String> checkExistUser() async {
    if (email.text.length > 0) {
      String firstPrefix = mobile.text.substring(0, 1);
      if (!(firstPrefix == '+')) {
        if (mobile.text.substring(0, 1) == '0') {
          phoneNumber = dropdownValue +
              mobile.text.substring(1, mobile.text.length).toString();
          print(phoneNumber);
////////////////////////// เช็คEmail อย่างเดียว /////////////////////////////////////////////////////////
          bool checkEmail = await auth.checkExistsUserEmail(email.text);
          print('checkEmail : ' + checkEmail.toString());
          if (checkEmail == true) {
            return 'email';
          } else {
            return 'register';
          }
///////////////////////////////////////////////////////////////////////////////////////////

          // }
          // else {
          //     print('179 line');
          //     showShortToast(
          //         AppLocalizations.of(context)!.translate('prefixzero'));
          //     return 'prefixzero';
          //   }
        } else {
          showShortToast(AppLocalizations.of(context)!.translate('prefixzero'));
          return 'prefixzero';
        }
      }
    } else {
      String firstPrefix = mobile.text.substring(0, 1);
      if (!(firstPrefix == '+')) {
        if (mobile.text.substring(0, 1) == '0') {
          phoneNumber = dropdownValue +
              mobile.text.substring(1, mobile.text.length).toString();
          return 'register';
          // String result = validateMobile(phoneNumber);
          //
          // if (result == 'match') {
          //   bool checkPhone = await auth.checkExistUser(phoneNumber);
          //   if (checkPhone == true) {
          //     return 'phone';
          //   } else {
          //     return 'register';
          //   }
          // } else {
          //   showShortToast(
          //       AppLocalizations.of(context)!.translate('prefixzero'));
          // }
        } else {
          showShortToast(AppLocalizations.of(context)!.translate('prefixzero'));
        }
      }
    }
    return Future.value();
  }

  void showColoredToastColor(msg, color) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_LONG,
        backgroundColor: color,
        textColor: Colors.white);
  }

  void showColoredToast(msg) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_LONG,
        backgroundColor: Colors.red,
        textColor: Colors.white);
  }

  void showShortToast(msg) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_SHORT,
        backgroundColor: Colors.cyan,
        textColor: Colors.white);
  }

  setName() {
    if (!mounted) return;
    setState(() {
      firstName = fname.text;
    });

    print(firstName);
  }

  setRef() {
    if (!mounted) return;
    setState(() {
      refCode = ref.text;
    });

    print(firstName);
  }

  setLastName() {
    if (!mounted) return;
    setState(() {
      lastName = lname.text;
    });

    print(lastName);
  }

  setMobile() {
    if (!mounted) return;
    setState(() {
      phoneNumber = mobile.text;
    });

    print(phoneNumber);
  }

  TextEditingController controller = TextEditingController();
  void initState1() {
    // TODO: implement initState
    super.initState();
    controller.text = 'Initial Value';
  }

  bool monVal = false;

  @override
  Widget build(BuildContext context) {
    // TODO: implement build

    return Scaffold(
      body: ModalProgressHUD(
        opacity: 0.1,
        child: GestureDetector(
          onTap: () {
            FocusScopeNode currentFocus = FocusScope.of(context);

            if (!currentFocus.hasPrimaryFocus) {
              currentFocus.unfocus();
            }
          },
          child: Stack(
            children: <Widget>[
              Container(
                height: double.infinity,
                width: double.infinity,
                child: Image.asset(
                  'assets/image/back.png',
                  fit: BoxFit.fill,
                ),
              ),
              SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    Padding(
                      padding: EdgeInsets.only(
                        top: mediaQuery(context, 'height', 136),
                      ),
                      child: _TEXT1(context),
                    ),
                    Padding(
                        padding: EdgeInsets.only(
                          top: mediaQuery(context, 'height', 150),
                        ),
                        child: _Input()),
                    Padding(
                      padding: EdgeInsets.only(
                        top: mediaQuery(context, 'height', 50),
                        bottom: mediaQuery(context, 'height', 50),
                      ),
                      child: _ButtonContinue(),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        inAsyncCall: _saving,
        progressIndicator: CustomLoading(),
      ),
    );
  }

  Widget _TEXT1(context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Container(
          padding: EdgeInsets.only(
            left: mediaQuery(context, 'width', 35),
          ),
//          color: Colors.amber,
          width: MediaQuery.of(context).size.width / 4,
          child: backButton(context, LikeWalletAppTheme.gray1),
        ),
        Container(
            width: MediaQuery.of(context).size.width / 2,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                new Image.asset(
                  LikeWalletImage.like_point,
                  height: mediaQuery(context, 'height', 137),
                ),
                SizedBox(
                  width: mediaQuery(context, 'height', 30),
                ),
                new Image.asset(
                  LikeWalletImage.like_point_text,
                  height: mediaQuery(context, 'height', 38.9),
                ),
              ],
            )),
        Container(
          width: MediaQuery.of(context).size.width / 4,
        ),
      ],

//        color: Colors.green,
    );
  }

  Widget _Input() {
    return new Container(
      alignment: Alignment.center,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          new Container(
            decoration: BoxDecoration(
              color: LikeWalletAppTheme.bule2,
              border: Border.all(
                color: LikeWalletAppTheme.bule1,
                width: mediaQuery(context, 'height', 0.3),
              ),
              borderRadius: BorderRadius.all(Radius.circular(5.0)),
            ),
            alignment: Alignment.center,
            height: mediaQuery(context, 'height', 156),
            width: mediaQuery(context, 'width', 930),
//                color: Colors.blue,
            child: TextField(
              controller: fname,
              style: TextStyle(
                  fontSize: mediaQuery(context, 'height', 47),
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  color: LikeWalletAppTheme.white),
              decoration: InputDecoration(
                isDense: true,
                contentPadding:
                    EdgeInsets.only(left: mediaQuery(context, 'width', 70)),
//                focusedBorder: OutlineInputBorder(
//                    borderSide: BorderSide(
//                        color: LikeWalletAppTheme.bule1,
//                        width: mediaQuery(context, 'width', 1))),
                hintText: AppLocalizations.of(context)!
                            .translate('register_input_name'),
                hintStyle: TextStyle(
                    fontSize: mediaQuery(context, 'height', 47),
                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
                    color: LikeWalletAppTheme.gray),
                border: InputBorder.none,
//                enabledBorder: OutlineInputBorder(
//                    borderSide: BorderSide(
//                        width: mediaQuery(context, 'width', 0.3),
//                        color: LikeWalletAppTheme.bule1)),
                hoverColor: Colors.white,
                disabledBorder: InputBorder.none,
                focusColor: Colors.white,
                alignLabelWithHint: true,
                fillColor: Colors.white,
              ),
              keyboardType: TextInputType.text,
            ),
          ),
          Padding(
            padding: EdgeInsets.only(
              top: MediaQuery.of(context).size.height * 0.025,
            ),
          ),
          new Container(
            decoration: BoxDecoration(
              color: LikeWalletAppTheme.bule2,
              border: Border.all(
                color: LikeWalletAppTheme.bule1,
                width: mediaQuery(context, 'height', 0.3),
              ),
              borderRadius: BorderRadius.all(Radius.circular(5.0)),
            ),
            alignment: Alignment.center,
            height: mediaQuery(context, 'height', 156),
            width: mediaQuery(context, 'width', 930),
//                color: Colors.blue,
            child: TextField(
              controller: lname,
              style: TextStyle(
                  fontSize: mediaQuery(context, 'height', 47),
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  color: LikeWalletAppTheme.white),
              decoration: InputDecoration(
                isDense: true,
                contentPadding:
                    EdgeInsets.only(left: mediaQuery(context, 'width', 70)),
//                focusedBorder: OutlineInputBorder(
//                    borderSide: BorderSide(
//                        color: LikeWalletAppTheme.bule1,
//                        width: mediaQuery(context, 'width', 1))),
//                contentPadding: EdgeInsets.fromLTRB(20.0, 10.0, 20.0, 10.0),
                hintText: AppLocalizations.of(context)!
                            .translate('register_input_lastname'),
                hintStyle: TextStyle(
                    fontSize: mediaQuery(context, 'height', 47),
                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
                    color: LikeWalletAppTheme.gray),
                border: InputBorder.none,
//                enabledBorder: OutlineInputBorder(
//                    borderSide: BorderSide(
//                        width: mediaQuery(context, 'width', 0.3),
//                        color: LikeWalletAppTheme.bule1)),
                hoverColor: Colors.white,
                disabledBorder: InputBorder.none,
                focusColor: Colors.white,
                alignLabelWithHint: true,
                fillColor: Colors.white,
              ),
              keyboardType: TextInputType.text,
            ),
          ),
          Padding(
            padding: EdgeInsets.only(
              top: MediaQuery.of(context).size.height * 0.025,
            ),
          ),
          //number phone
          Container(
            alignment: Alignment.center,
            height: mediaQuery(context, 'height', 156),
            width: mediaQuery(context, 'width', 930),
            decoration: BoxDecoration(
              color: LikeWalletAppTheme.bule2,
              border: Border.all(
                color: LikeWalletAppTheme.bule1,
                width: mediaQuery(context, 'height', 0.3),
              ),
              borderRadius: BorderRadius.all(Radius.circular(5.0)),
            ),
            child: Row(
              children: <Widget>[
                new Container(
                  width: mediaQuery(context, 'width', 220),
                  padding:
                      EdgeInsets.only(left: mediaQuery(context, 'width', 0)),
                  alignment: Alignment.center,
                  child: DropdownButtonHideUnderline(
                    child: new Theme(
                      data: Theme.of(context).copyWith(
                          canvasColor:
                              LikeWalletAppTheme.bule2.withOpacity(0.99)),
                      child: DropdownButton<String>(
                        iconSize: mediaQuery(context, 'height', 45),
                        value: dropdownValue,
                        elevation: 5,
                        style: TextStyle(
                          color: LikeWalletAppTheme.white,
                          fontSize: mediaQuery(context, 'height', 45),
                        ),
                        iconEnabledColor: LikeWalletAppTheme.white,
                        iconDisabledColor: LikeWalletAppTheme.white,
                        onChanged: (String? newValue) {
                          setState(() {
                            dropdownValue = newValue!;
                          });
                        },
                        items: <String>['+66', '+856', '+855']
                            .map<DropdownMenuItem<String>>((String value) {
                          return DropdownMenuItem<String>(
                            value: value,
                            child: Text(
                              value,
                              style: TextStyle(
                                color: mobile.text.length <= 0
                                    ? LikeWalletAppTheme.gray
                                    : LikeWalletAppTheme.white,
                                fontSize: mediaQuery(context, 'height', 45),
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                    ),
                  ),
                  decoration: BoxDecoration(
                      border: Border(
                    right: BorderSide(
                      color: LikeWalletAppTheme.bule1,
                      width: mediaQuery(context, 'height', 0.3),
                    ),
                  )),
                ),
                new Container(
                  width: mediaQuery(context, 'width', 705),
                  alignment: Alignment.center,
                  child: TextField(
                    controller: mobile,
                    readOnly: true,
                    inputFormatters: [
                      LengthLimitingTextInputFormatter(11),
                    ],
                    style: TextStyle(
                      color: LikeWalletAppTheme.white,
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      fontWeight: FontWeight.normal,
                      fontSize: mediaQuery(context, 'height', 45),
                    ),
                    decoration: InputDecoration(
                      isDense: true,
                      contentPadding: EdgeInsets.only(
                          left: mediaQuery(context, 'width', 70)),
//                      focusedBorder: OutlineInputBorder(
//                          borderSide: BorderSide(
//                              color: LikeWalletAppTheme.bule1,
//                              width: mediaQuery(context, 'width', 1))),
//                contentPadding: EdgeInsets.fromLTRB(20.0, 10.0, 20.0, 10.0),
                      hintText: AppLocalizations.of(context)!
                            .translate('singin_input_mobile'),
                      hintStyle: TextStyle(
                          fontSize: mediaQuery(context, 'height', 47),
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font1'),
                          color: LikeWalletAppTheme.gray),
                      border: InputBorder.none,
//                      enabledBorder: OutlineInputBorder(
//                          borderSide: BorderSide(
//                              width: mediaQuery(context, 'width', 0.3),
//                              color: LikeWalletAppTheme.bule1)),
                      hoverColor: Colors.white,
                      disabledBorder: InputBorder.none,
                      focusColor: Colors.white,
                      alignLabelWithHint: true,
                      fillColor: Colors.white,
                    ),
                    keyboardType: TextInputType.number,
                    focusNode: doneFocusNode,
                  ),
                )
              ],
            ),
          ),

          //email register
          Padding(
            padding: EdgeInsets.only(
              top: MediaQuery.of(context).size.height * 0.025,
            ),
          ),
          new Container(
            decoration: BoxDecoration(
              color: LikeWalletAppTheme.bule2,
              border: Border.all(
                color: LikeWalletAppTheme.bule1,
                width: mediaQuery(context, 'height', 0.3),
              ),
              borderRadius: BorderRadius.all(Radius.circular(5.0)),
            ),
            alignment: Alignment.center,
            height: mediaQuery(context, 'height', 156),
            width: mediaQuery(context, 'width', 930),
            child: TextField(
              controller: email,
              style: TextStyle(
                  fontSize: mediaQuery(context, 'height', 47),
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  color: LikeWalletAppTheme.white),
              decoration: InputDecoration(
                isDense: true,
                contentPadding:
                    EdgeInsets.only(left: mediaQuery(context, 'width', 70)),
//                focusedBorder: OutlineInputBorder(
//                    borderSide: BorderSide(
//                        color: LikeWalletAppTheme.bule1,
//                        width: mediaQuery(context, 'width', 1))),
//                contentPadding: EdgeInsets.fromLTRB(20.0, 10.0, 20.0, 10.0),
                hintText: AppLocalizations.of(context)!
                            .translate('register_input_email'),
                hintStyle: TextStyle(
                    fontSize: mediaQuery(context, 'height', 47),
                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
                    color: LikeWalletAppTheme.gray),
                border: InputBorder.none,
//                enabledBorder: OutlineInputBorder(
//                    borderSide: BorderSide(
//                        width: mediaQuery(context, 'width', 0.3),
//                        color: LikeWalletAppTheme.bule1)),
                hoverColor: Colors.white,
                disabledBorder: InputBorder.none,
                focusColor: Colors.white,
                alignLabelWithHint: true,
                fillColor: Colors.white,
              ),
              keyboardType: TextInputType.text,
            ),
          ),

          //email register
          Padding(
            padding: EdgeInsets.only(
              top: MediaQuery.of(context).size.height * 0.025,
            ),
          ),
          new Container(
            decoration: BoxDecoration(
              color: LikeWalletAppTheme.bule2,
              border: Border.all(
                color: LikeWalletAppTheme.bule1,
                width: mediaQuery(context, 'height', 0.3),
              ),
              borderRadius: BorderRadius.all(Radius.circular(5.0)),
            ),
            alignment: Alignment.center,
            height: mediaQuery(context, 'height', 156),
            width: mediaQuery(context, 'width', 930),
//                color: Colors.blue,
            child: TextField(
              obscureText: true,
              controller: password,
              style: TextStyle(
                  fontSize: mediaQuery(context, 'height', 47),
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  color: LikeWalletAppTheme.white),
              decoration: InputDecoration(
                isDense: true,
                contentPadding:
                    EdgeInsets.only(left: mediaQuery(context, 'width', 70)),
//                focusedBorder: OutlineInputBorder(
//                    borderSide: BorderSide(
//                        color: LikeWalletAppTheme.bule1,
//                        width: mediaQuery(context, 'width', 1))),
//                contentPadding: EdgeInsets.fromLTRB(20.0, 10.0, 20.0, 10.0),
                hintText: AppLocalizations.of(context)!
                            .translate('register_input_password'),
                hintStyle: TextStyle(
                    fontSize: mediaQuery(context, 'height', 47),
                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
                    color: LikeWalletAppTheme.gray),
                border: InputBorder.none,
//                enabledBorder: OutlineInputBorder(
//                    borderSide: BorderSide(
//                        width: mediaQuery(context, 'width', 0.3),
//                        color: LikeWalletAppTheme.bule1)),
                hoverColor: Colors.white,
                disabledBorder: InputBorder.none,
                focusColor: Colors.white,
                alignLabelWithHint: true,
                fillColor: Colors.white,
              ),
              keyboardType: TextInputType.text,
            ),
          ),

          //email register
          Padding(
            padding: EdgeInsets.only(
              top: MediaQuery.of(context).size.height * 0.025,
            ),
          ),
          new Container(
            decoration: BoxDecoration(
              color: LikeWalletAppTheme.bule2,
              border: Border.all(
                color: LikeWalletAppTheme.bule1,
                width: mediaQuery(context, 'height', 0.3),
              ),
              borderRadius: BorderRadius.all(Radius.circular(5.0)),
            ),
            alignment: Alignment.center,
            height: mediaQuery(context, 'height', 156),
            width: mediaQuery(context, 'width', 930),
//                color: Colors.blue,
            child: TextField(
              obscureText: true,
              controller: confirmPassword,
              style: TextStyle(
                  fontSize: mediaQuery(context, 'height', 47),
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  color: LikeWalletAppTheme.white),
              decoration: InputDecoration(
                isDense: true,
                contentPadding:
                    EdgeInsets.only(left: mediaQuery(context, 'width', 70)),
//                focusedBorder: OutlineInputBorder(
//                    borderSide: BorderSide(
//                        color: LikeWalletAppTheme.bule1,
//                        width: mediaQuery(context, 'width', 1))),
//                contentPadding: EdgeInsets.fromLTRB(20.0, 10.0, 20.0, 10.0),
                hintText: AppLocalizations.of(context)!
                            .translate('register_input_password_repeat'),
                hintStyle: TextStyle(
                    fontSize: mediaQuery(context, 'height', 47),
                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
                    color: LikeWalletAppTheme.gray),
                border: InputBorder.none,
//                enabledBorder: OutlineInputBorder(
//                    borderSide: BorderSide(
//                        width: mediaQuery(context, 'width', 0.3),
//                        color: LikeWalletAppTheme.bule1)),
                hoverColor: Colors.white,
                disabledBorder: InputBorder.none,
                focusColor: Colors.white,
                alignLabelWithHint: true,
                fillColor: Colors.white,
              ),
              keyboardType: TextInputType.text,
            ),
          ),
          //end email
          Padding(
            padding: EdgeInsets.only(
              top: MediaQuery.of(context).size.height * 0.025,
            ),
          ),

          Container(
              width: mediaQuery(context, 'width', 930),
              child: Row(
                children: <Widget>[
                  new Container(
                    decoration: BoxDecoration(
                      color: LikeWalletAppTheme.bule2,
                      border: Border.all(
                        color: LikeWalletAppTheme.bule1,
                        width: mediaQuery(context, 'height', 0.3),
                      ),
                      borderRadius: BorderRadius.all(Radius.circular(5.0)),
                    ),
                    alignment: Alignment.center,
                    height: mediaQuery(context, 'height', 156),
                    width: mediaQuery(context, 'width', 597),
//                color: Colors.blue,
                    child: TextField(
                      controller: ref,
                      style: TextStyle(
                          fontSize: mediaQuery(context, 'height', 47),
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font1'),
                          color: LikeWalletAppTheme.white),
                      decoration: InputDecoration(
                        isDense: true,
                        contentPadding: EdgeInsets.only(
                            left: mediaQuery(context, 'width', 70)),
//                        focusedBorder: OutlineInputBorder(
//                            borderSide: BorderSide(
//                                color: LikeWalletAppTheme.bule1,
//                                width: mediaQuery(context, 'width', 1))),
//                contentPadding: EdgeInsets.fromLTRB(20.0, 10.0, 20.0, 10.0),
                        hintText: AppLocalizations.of(context)!
                            .translate('register_input_refer'),
                        hintStyle: TextStyle(
                            fontSize: mediaQuery(context, 'height', 47),
                            fontFamily:
                                AppLocalizations.of(context)!.translate('font1'),
                            color: LikeWalletAppTheme.gray),
                        border: InputBorder.none,
//                        enabledBorder: OutlineInputBorder(
//                            borderSide: BorderSide(
//                                width: mediaQuery(context, 'width', 0.3),
//                                color: LikeWalletAppTheme.bule1)),
                        hoverColor: Colors.white,
                        disabledBorder: InputBorder.none,
                        focusColor: Colors.white,
                        alignLabelWithHint: true,
                        fillColor: Colors.white,
                      ),
                      keyboardType: TextInputType.text,
                    ),
                  ),
                  SizedBox(
                    width: mediaQuery(context, 'width', 30),
                  ),
                  new Container(
                    width: mediaQuery(context, 'width', 102),
                    alignment: Alignment.center,
                    child: new GestureDetector(
                      onTap: () {
                        pasteAddress();
                      },
                      child: ClipOval(
                        child: Container(
                          alignment: Alignment.center,
                          color: Color(0xffDEDEDE).withOpacity(1),
                          height: mediaQuery(context, 'height', 102),
                          // height of the button
                          width: mediaQuery(context, 'height', 102),
                          // width of the button
                          child: new Text(
                            AppLocalizations.of(context)!
                            .translate('bankingSend_to_paste'),
                            style: LikeWalletAppTheme.textStyle(
                                context,
                                30,
                                LikeWalletAppTheme.black,
                                FontWeight.normal,
                                'font1'),
                          ),
                        ),
                      ),
                    ),
                  ),
                  GestureDetector(
                    onTap: () {
                      Navigator.push(context, ScaleRoute(page: scanRefer()));
                    },
                    child: Container(
                      width: mediaQuery(context, 'width', 166.5),
                      child: Image.asset(
                        LikeWalletImage.icon_scan_barcode,
                        color: LikeWalletAppTheme.white,
                        height: mediaQuery(context, 'height', 82.5),
                      ),
                    ),
                  )
                ],
              )),
          Container(
              alignment: Alignment.bottomCenter,
              height: mediaQuery(context, 'height', 156),
              width: mediaQuery(context, 'width', 930),
              child: Row(
                children: <Widget>[
                  Container(
                      height: mediaQuery(context, 'height', 51),
                      width: mediaQuery(context, 'height', 51),
                      color: LikeWalletAppTheme.white,
                      child: Theme(
                        data: Theme.of(context).copyWith(
                          unselectedWidgetColor: LikeWalletAppTheme.white,
                        ),
                        child: Checkbox(
                          checkColor: LikeWalletAppTheme.white,
                          materialTapTargetSize:
                              MaterialTapTargetSize.shrinkWrap,
                          activeColor: LikeWalletAppTheme.bule1,
                          value: agreePolicy,
                          onChanged: (bool? value) {
                            setState(() {
                              agreePolicy = value!;
                              print(agreePolicy);
                            });
                          },
                        ),
                      )),
                  Padding(
                    padding: EdgeInsets.only(
                      left: MediaQuery.of(context).size.width * 0.025,
                    ),
                  ),
                  new Text(
                      AppLocalizations.of(context)!
                            .translate('register_input_agree1'),
                      style: TextStyle(
                          fontSize: mediaQuery(context, 'height', 44),
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font1'),
                          color: LikeWalletAppTheme.white)),
                  GestureDetector(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => WebViewExample()),
                      );
                    },
                    child: new Text(
                        AppLocalizations.of(context)!
                            .translate('register_input_agree2'),
                        style: TextStyle(
                            fontSize: mediaQuery(context, 'height', 44),
                            fontFamily:
                                AppLocalizations.of(context)!.translate('font1'),
                            color: LikeWalletAppTheme.bule1)),
                  ),
                ],
              )),
        ],
      ),
    );
  }

  Widget _ButtonContinue() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: <Widget>[
        new Container(
          alignment: Alignment.center,
          width: mediaQuery(context, 'width', 930),
          child: ButtonTheme(
            minWidth: mediaQuery(context, 'width', 930),
            height: mediaQuery(context, 'height', 132),
            child: TextButton(
              style: ButtonStyle(
                shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(5.0),
                  ),
                ),
                backgroundColor: MaterialStateProperty.resolveWith<Color>(
                      (Set<MaterialState> states) {
                    if (states.contains(MaterialState.disabled)) {
                      return LikeWalletAppTheme.white;
                    }
                    return (firstName != '' && lastName != '' && phoneNumber != '' && agreePolicy != false) ? LikeWalletAppTheme.bule1 : LikeWalletAppTheme.white;
                  },
                ),
              ),
              onPressed: () {
                if (lastName != '' && firstName != '' && phoneNumber != '' && agreePolicy != false) {
                  setState(() {
                    _saving = true;
                  });

                  checkExistUser().then((exits) {
                    if (exits == 'email') {
                      setState(() {
                        _saving = false;
                      });
                      showColoredToast(AppLocalizations.of(context)!.translate('exitsUser').toString());
                    } else if (exits == 'emailphone') {
                      setState(() {
                        _saving = false;
                      });
                    } else if (exits == 'register') {
                      if (email.text.length > 0) {
                        print('registerByEmail 880');
                        _registerByEmail();
                      } else {
                        print('registerByPhone 884');
                        _sendCodeToPhoneNumber();
                      }
                    } else {
                      print(exits);
                      setState(() {
                        _saving = false;
                      });
                      showShortToast(AppLocalizations.of(context)!.translate('prefixzero').toString());
                    }
                  });
                } else {
                  showColoredToast(AppLocalizations.of(context)!.translate('errorRegister').toString());
                }
              },
              child: Text(
                AppLocalizations.of(context)!.translate('register_button'),
                style: TextStyle(
                  color: LikeWalletAppTheme.black,
                  letterSpacing: 0.3,
                  fontSize: mediaQuery(context, 'height', 45),
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _ButtonCancel() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: <Widget>[
        new Container(
          alignment: Alignment.center,
          width: mediaQuery(context, 'width', 930),
          child: ButtonTheme(
            minWidth: mediaQuery(context, 'width', 930),
            height: mediaQuery(context, 'height', 132),
            child:TextButton(
              style: ButtonStyle(
                shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(5.0),
                  ),
                ),
                backgroundColor: MaterialStateProperty.resolveWith<Color>(
                      (Set<MaterialState> states) {
                    return LikeWalletAppTheme.gray;
                  },
                ),
                foregroundColor: MaterialStateProperty.resolveWith<Color>(
                      (Set<MaterialState> states) {
                    return LikeWalletAppTheme.white;
                  },
                ),
              ),
              onPressed: () {
                Navigator.pop(context);
              },
              child: Text(
                AppLocalizations.of(context)!.translate('login_cancel_button'),
                style: TextStyle(
                  fontSize: mediaQuery(context, 'height', 45),
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  String validateMobile(String value) {
    String pattern = r'(^\+?[+0-9]{12,18}$)';
    RegExp regExp = new RegExp(pattern);
    if (value.length == 0) {
      print(value);
      return 'Please enter mobile number';
    } else if (!regExp.hasMatch(value)) {
      print(value + ' not match');
      return 'Please enter valid mobile number';
    }
    print(value + ' match');
    return 'match';
  }

  Future<void> _registerByEmail() async {
    // print(_registerByEmail);
    String firstPrefix = mobile.text.substring(0, 1);

    if (!(firstPrefix == '+')) {
      if (firstPrefix == '0') {
        phoneNumber = dropdownValue +
            mobile.text.substring(1, mobile.text.length).toString();
        // String result = validateMobile(phoneNumber);
        // if (result == 'match') {
        print('match: ' + phoneNumber);
        if (password.text == confirmPassword.text) {
          print(firstName);
          print(lastName);
          try {
            UserCredential user = await FirebaseAuth.instance
                .signInWithCustomToken(customToken.toString())
                .catchError((error) {
              setState(() => _saving = false);
              // showColoredToast(
              //     AppLocalizations.of(context)!.translate('otp_incorrect'));
            });
            var url = Uri.https(env.apiUrl, '/syncEmailWithPhoneNumber');
            String? _token = await auth.getTokenFirebase();
            // print(_token);
            // print(_token);
            print(email.text.trim().toString());

            var response = await http.post(url, body: {
              'email': email.text.trim().toString(),
              '_token': _token!.trim().toString(),
              'password': password.text.trim().toString()
            });
            print('Response status: ${response.statusCode}');
            print('Response body: ${response.body}');
            var body = json.decode(response.body);

            print(body);

            if (body['statusCode'] == 200) {
              auth
                  .singInWithEmailAndPassword(email.text.trim().toString(),
                      password.text.trim().toString())
                  .then((decodeToken) {
                //ผ่าน sync เบอร์
                //success sync
                setState(() {
                  _saving = false;
                });
                print(firstName);
                print(lastName);

                roundSMS = providerSMS.Twilio;
                Navigator.push(
                  context,
                  EnterExitRoute(
                      exitPage: REGISTER_FORM(),
                      enterPage: SetPin(
                          refCode: refCode,
                          firstName: firstName,
                          lastName: lastName,
                          checkIf: 'register',
                          secret: "LikeWallet",
                          roundSMS: roundSMS,
                          codeVerify: passCode,
                          phoneNumber: phoneNumber,
                          pinAgain: false)),
                );
                // Navigator.push(
                //   context,
                //   MaterialPageRoute(
                //       builder: (context) => OTP_PAGE(
                //           checkIf: 'register',
                //           roundSMS: roundSMS,
                //           phoneNumber: phoneNumber,
                //           firstName: firstName,
                //           lastName: lastName,
                //           refCode: refCode)),
                // );
              });
            } else {
              setState(() {
                _saving = false;
              });
              showShortToast(
                  AppLocalizations.of(context)!.translate('errorRegister'));

//                    roundSMS = providerSMS.Email;
//                    //ผ่านแต่ไม่ sync เบอร์
//                    Navigator.push(
//                      context,
//                      MaterialPageRoute(
//                          builder: (context) =>
//                              SecretPassFirst(
//                                  checkIf: 'register',
//                                  roundSMS: roundSMS,
//                                  phoneNumber: phoneNumber,
//                                  firstName: firstName,
//                                  lastName: lastName,
//                                  refCode: refCode)),
//                    );
            }
          } catch (e) {
            showColoredToast(AppLocalizations.of(context)!
                            .translate('errorRegister')
                .toString());
          }
        } else {
          setState(() {
            _saving = false;
          });
          showColoredToast(AppLocalizations.of(context)!
                            .translate('passwordDiff')
              .toString());
        }
        // }
        // else {
        //   showShortToast(AppLocalizations.of(context)!.translate('prefixzero'));
        // }
      } else {
        showShortToast(AppLocalizations.of(context)!.translate('prefixzero'));
      }
    }
  }

  ///send OTP
  Future<void> _sendCodeToPhoneNumber() async {
    String firstPrefix = mobile.text.substring(0, 1);
    if (!(firstPrefix == '+')) {
      if (firstPrefix == '0') {
        phoneNumber = dropdownValue +
            mobile.text.substring(1, mobile.text.length).toString();
        String result = validateMobile(phoneNumber);

        if (result == 'match') {
          // if (roundSMS == providerSMS.Firebase) {
          //   setState(() {
          //     _saving = true;
          //   });
          //   final PhoneVerificationCompleted verificationCompleted =
          //       (AuthCredential phoneAuthCredential) {
          //     print('verified');
          //     setState(() {
          //       _saving = false;
          //       roundSMS = providerSMS.Twilio;
          //     });
          //   };
          //
          //   final PhoneVerificationFailed verificationFailed =
          //       (FirebaseAuthException authException) {
          //     setState(() {
          //       _saving = false;
          //     });
          //     roundSMS = providerSMS.Twilio;
          //     _sendCodeToPhoneNumber();
          //     setState(() {
          //       print(
          //           'Phone number verification failed. Code: ${authException.code}. Message: ${authException.message}');
          //     });
          //   };
          //
          //   final PhoneCodeSent codeSent =
          //       (String verificationId, [int forceResendingToken]) async {
          //     this.verificationId = verificationId;
          //     print("code sent to " + phoneNumber);
          //
          //     setState(() {
          //       _saving = false;
          //     });
          //     Navigator.push(
          //       context,
          //       MaterialPageRoute(
          //           builder: (context) => OTP_PAGE(
          //               checkIf: 'register',
          //               auth: _auth,
          //               verificationId: verificationId,
          //               roundSMS: roundSMS,
          //               firstName: firstName,
          //               lastName: lastName,
          //               refCode: refCode)),
          //     );
          //   };
          //
          //   final PhoneCodeAutoRetrievalTimeout codeAutoRetrievalTimeout =
          //       (String verificationId) {
          //     this.verificationId = verificationId;
          //     print("time out");
          //     setState(() {
          //       _saving = false;
          //     });
          //     roundSMS = providerSMS.Twilio;
          //   };
          //
          //   await FirebaseAuth.instance.verifyPhoneNumber(
          //       phoneNumber: phoneNumber,
          //       timeout: const Duration(seconds: 10),
          //       verificationCompleted: verificationCompleted,
          //       verificationFailed: verificationFailed,
          //       codeSent: codeSent,
          //       codeAutoRetrievalTimeout: codeAutoRetrievalTimeout);
          // } else
          print(roundSMS);
          if (roundSMS == providerSMS.Twilio) {
            UserCredential user = await FirebaseAuth.instance
                .signInWithCustomToken(customToken.toString())
                .catchError((error) {
              setState(() => _saving = false);
              showColoredToast(
                  AppLocalizations.of(context)!.translate('otp_incorrect'));
            });
            setState(() => _saving = false);
            User? users = FirebaseAuth.instance.currentUser;
            users!.getIdTokenResult().then((userDetail) async {
              print(userDetail);
            });
            user.user!.getIdToken().then((value) {
              if (checkIf == 'register') {
                Navigator.push(
                    context,
                    EnterExitRoute(
                        exitPage: OTP_PAGE(),
                        enterPage: SecretPassFirst(
                            checkIf: checkIf,
                            roundSMS: roundSMS,
                            codeVerify: passCode,
                            phoneNumber: phoneNumber,
                            firstName: firstName,
                            lastName: lastName,
                            refCode: refCode)));
              } else {
                setState(() => _saving = false);
                print("refCode $refCode");
                print("firstName $firstName");
                print("lastName $lastName");
                print("roundSMS $roundSMS");
                print("passCode $passCode");
                print("phoneNumber $phoneNumber");
                Navigator.push(
                  context,
                  EnterExitRoute(
                      exitPage: REGISTER_FORM(),
                      enterPage: SetPin(
                          refCode: refCode,
                          firstName: firstName,
                          lastName: lastName,
                          checkIf: 'register',
                          secret: "LikeWallet",
                          roundSMS: roundSMS,
                          codeVerify: passCode,
                          phoneNumber: phoneNumber,
                          pinAgain: false)),
                );
              }
            });
            // setState(() {
            //   _saving = true;
            // });
            // String url = env.apiUrl + '/authNoFirebase';
            //
            // var response =
            //     await http.post(url, body: {'phone_number': phoneNumber});
            // print('Response status: ${response.statusCode}');
            // print('Response body: ${response.body}');
            // var body = json.decode(response.body);
            //
            // if (body['statusCode'] == 200) {
            //   //success register and sign in
            //   //to confirm
            //   setState(() {
            //     _saving = false;
            //   });
            //   print(phoneNumber.length);
            //   Navigator.push(
            //     context,
            //     MaterialPageRoute(
            //         builder: (context) => OTP_PAGE(
            //               checkIf: 'register',
            //               roundSMS: roundSMS,
            //               phoneNumber: phoneNumber,
            //               firstName: firstName,
            //               lastName: lastName,
            //               refCode: refCode,
            //             )),
            //   );
            // } else {
            //   setState(() {
            //     _saving = false;
            //   });
            //   //register failed
            // }
          }
        } else {
          showShortToast(AppLocalizations.of(context)!.translate('prefixzero'));
        }
      } else {
        showShortToast(AppLocalizations.of(context)!.translate('prefixzero'));
      }
    }
  }

  void pasteAddress() async {
    ClipboardData? data = await Clipboard.getData('text/plain');
    ref.text = data!.text!;
  }
//  _launchURL() async {
//    const url = 'https://sites.google.com/view/likewallet/home';
//    if (await canLaunch(url)) {
//      await launch(url);
//    } else {
//      throw 'Could not launch $url';
//    }
//  }
}

//web example

class WebViewExample extends StatefulWidget {
  @override
  _WebViewExampleState createState() => _WebViewExampleState();
}

class _WebViewExampleState extends State<WebViewExample> {
  final Completer<WebViewController> _controller =
      Completer<WebViewController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
//      appBar: AppBar(
//        backgroundColor: Colors.transparent,
//        title: const Text('Terms & Policy'),
//        // This drop down menu demonstrates that Flutter widgets can be shown over the web view.
//
//      ),
      // We're using a Builder here so we have a context that is below the Scaffold
      // to allow calling Scaffold.of(context) so we can show a snackbar.
      body: Builder(builder: (BuildContext context) {
        return WebView(
          initialUrl: 'https://sites.google.com/view/likewallet/home',
          javascriptMode: JavascriptMode.unrestricted,
          onWebViewCreated: (WebViewController webViewController) {
            _controller.complete(webViewController);
          },
          // TODO(iskakaushik): Remove this when collection literals makes it to stable.
          // ignore: prefer_collection_literals
          // javascriptChannels: <JavascriptChannel>[
          //   _toasterJavascriptChannel(context),
          // ].toSet(),
          navigationDelegate: (NavigationRequest request) {
            if (request.url.startsWith('https://www.youtube.com/')) {
              print('blocking navigation to $request}');
              return NavigationDecision.prevent;
            }
            print('allowing navigation to $request');
            return NavigationDecision.navigate;
          },
          onPageStarted: (String url) {
            print('Page started loading: $url');
          },
          onPageFinished: (String url) {
            print('Page finished loading: $url');
          },
          gestureNavigationEnabled: true,
        );
      }),
      floatingActionButton: favoriteButton(),
    );
  }

  // JavascriptChannel _toasterJavascriptChannel(BuildContext context) {
  //   return JavascriptChannel(
  //       name: 'Toaster',
  //       onMessageReceived: (JavascriptMessage message) {
  //         Scaffold.of(context).showSnackBar(
  //           SnackBar(content: Text(message.message)),
  //         );
  //       });
  // }

  Widget favoriteButton() {
    return FutureBuilder<WebViewController>(
        future: _controller.future,
        builder: (BuildContext context,
            AsyncSnapshot<WebViewController> controller) {
          if (controller.hasData) {
            return FloatingActionButton(
              onPressed: () async {
                Navigator.pop(context);
              },
              child: const Icon(Icons.arrow_back_ios),
            );
          }
          return Container();
        });
  }
}

class scanRefer extends StatefulWidget {
  _scanRefer createState() => new _scanRefer();
}

class _scanRefer extends State<scanRefer> {
  // GlobalKey<CameraMlVisionState> qr_scan = GlobalKey();
  // BarcodeDetector detector = FirebaseVision.instance.barcodeDetector();
  late String barcode;
  int scanCount = 0;
  int number = 0;
  late File _image;
  List<String> data = [];
  final ImagePicker _picker = ImagePicker();

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  Future scanFromImage(path) async {
    barcode = (await Scan.parse(path).catchError((onError) {
      throw onError;
    }))!;
    print(barcode);
    if (barcode.substring(0, 2) == '0x') {
      Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(
          builder: (context) => Banking(
            scanActive: 'active',
            address: barcode.toString(),
          ),
        ),
        ModalRoute.withName('/bank'),
      );
    }
  }

  Future getImage() async {
    var image = await _picker.getImage(source: ImageSource.gallery);

    setState(() {
      _image = File(image!.path);

      number = 1;
    });
    print(_image.path);
    scanFromImage(_image.path);
  }

  changeContent(value) {
//    if(value == 0){
//      setState(() {
//        curImage == 'none';
//      });
//    }
    setState(() {
      number = value;
    });
  }

  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xff141322),
      body: Stack(
        alignment: Alignment.center,
        children: <Widget>[
          backPage(),
          title(),
          number == 0
              ? scan()
              : Positioned(
                  top: MediaQuery.of(context).size.height *
                      Screen_util("height", 390),
                  child: Container(
                    color: Colors.grey.withOpacity(0.3),
                    height: MediaQuery.of(context).size.height *
                        Screen_util("height", 780),
                    width: MediaQuery.of(context).size.width *
                        Screen_util("width", 1080),
                  ),
                ),
          BorderCamera(),
          if (number == 0) detail()
        ],
      ),
    );
  }

  Widget backPage() {
    return Positioned(
        top: MediaQuery.of(context).size.height * Screen_util("height", 139.33),
        child: GestureDetector(
          child: new Container(
            alignment: Alignment.centerLeft,
            width: MediaQuery.of(context).size.width,
            child: new IconButton(
              icon: new Icon(
                Icons.arrow_back_ios,
                size: MediaQuery.of(context).size.height *
                    Screen_util("height", 44.47),
              ),
              color: Color(0xff707071),
              onPressed: () => {Navigator.of(context).pop()},
            ),
          ),
        ));
  }

  Widget title() {
    return Positioned(
        top: MediaQuery.of(context).size.height * Screen_util("height", 167),
        child: Container(
            width: MediaQuery.of(context).size.width,
            alignment: Alignment.center,
            child: Column(
              children: <Widget>[
                Container(
                    height: MediaQuery.of(context).size.height *
                        Screen_util("height", 110),
                    width: MediaQuery.of(context).size.width *
                        Screen_util("width", 633.92),
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: Colors.white.withOpacity(0.2),
                          width: MediaQuery.of(context).size.width *
                              Screen_util("width", 2),
                        ),
                      ),
                    ),
                    child: Text(
                      AppLocalizations.of(context)!.translate('scanpay_title'),
                      style: TextStyle(
                        color: Color(0xffB4E60D),
                        fontSize: MediaQuery.of(context).size.height *
                            Screen_util("height", 50),
                        fontFamily: AppLocalizations.of(context)!
                            .translate('font1Light'),
                        letterSpacing: 0.1,
                      ),
                    )),
                GestureDetector(
                    onTap: () {
                      getImage();
                    },
                    child: Container(
                      height: MediaQuery.of(context).size.height *
                          Screen_util("width", 50),
                      width: MediaQuery.of(context).size.width *
                          Screen_util("width", 300),
                      alignment: Alignment.center,
                      child: Text(
                        AppLocalizations.of(context)!
                            .translate('scanpay_select'),
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: MediaQuery.of(context).size.height *
                              Screen_util("height", 30),
                          fontFamily: AppLocalizations.of(context)!
                            .translate('font1Light'),
                          letterSpacing: 0.5,
                        ),
                      ),
                    )),
              ],
            )));
  }

  Widget BorderCamera() {
    return Positioned(
      top: MediaQuery.of(context).size.height * Screen_util("height", 470),
      child: Image.asset('assets/image/border_camera.png'),
      height: mediaQuery(context, 'height', 608.3),
      width: mediaQuery(context, 'width', 924.53),
    );
  }

  Widget scan() {
    return Positioned(
      top: MediaQuery.of(context).size.height * Screen_util("height", 390),
      child: Container(
        color: Colors.grey.withOpacity(0.3),
        height: MediaQuery.of(context).size.height * Screen_util("height", 780),
        width: MediaQuery.of(context).size.width * Screen_util("width", 1080),
        // child: CameraMlVision<List<Barcode>>(
        //   key: qr_scan,
        //   detector: detector.detectInImage,
        //   resolution: ResolutionPreset.high,
        //   onResult: (barcodes) {
        //     if (barcodes == null ||
        //         barcodes.isEmpty ||
        //         data.contains(barcodes.first.displayValue) ||
        //         !mounted) {
        //       return;
        //     }
        //     setState(() {
        //       data.add(barcodes.first.displayValue);
        //       print(barcodes.first.displayValue);
        //       if (barcodes.first.displayValue.substring(0, 2) == '0x') {
        //         scanCount = 1;
        //         qr_scan.currentState.deactivate();
        //
        //         Navigator.pushAndRemoveUntil(
        //           context,
        //           MaterialPageRoute(
        //             builder: (context) => Banking(
        //               scanActive: 'active',
        //               address: barcodes.first.displayValue.toString(),
        //             ),
        //           ),
        //           ModalRoute.withName('/bank'),
        //         );
        //       }
        //     });
        //   },
        //   onDispose: () {
        //     detector.close();
        //   },
        // ),
//        child: new QrCamera(
//          key: qr_scan,
//          onError: (context, error) => Text(
//            error.toString(),
//            style: TextStyle(color: Colors.red),
//          ),
//          qrCodeCallback: (code) {
//            print(qr_scan);
//            if (scanCount == 0) {
//              //check format address ethereum
//              if (code.substring(0, 2) == '0x') {
//                scanCount = 1;
//                qr_scan.currentState.deactivate();
//
//                Navigator.pushAndRemoveUntil(
//                  context,
//                  MaterialPageRoute(
//                    builder: (context) => Banking(
//                      scanActive: 'active',
//                      address: code.toString(),
//                    ),
//                  ),
//                  ModalRoute.withName('/bank'),
//                );
//              }
//            }
//          },
//        ),
      ),
    );
  }

  Widget detail() {
    return Positioned(
      top: MediaQuery.of(context).size.height * Screen_util("height", 1325),
      child: new Container(
          alignment: Alignment.center,
          width: MediaQuery.of(context).size.width * Screen_util("width", 700),
          child: Text(
            AppLocalizations.of(context)!.translate('scanrefer_details'),
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Color(0xff707071),
              fontSize: MediaQuery.of(context).size.height *
                  Screen_util("height", 39),
              fontFamily: AppLocalizations.of(context)!.translate('font1Light'),
              letterSpacing: 0.5,
            ),
          )),
    );
  }

  //เส้นเเนวตั้งเมนู
  Widget border() {
    return Container(
      height: MediaQuery.of(context).size.height * Screen_util("height", 125),
      decoration: BoxDecoration(
          border: Border(
        right: BorderSide(
          color: Colors.white,
          width: MediaQuery.of(context).size.width * 0.00185185185,
        ),
      )),
      alignment: Alignment.topCenter,
    );
  }
}
