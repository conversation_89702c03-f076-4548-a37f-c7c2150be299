import 'package:flutter/material.dart';

class TOUCN_ID_SINGIN extends StatefulWidget {
  _TOUCN_ID_SINGIN createState() => new _TOUCN_ID_SINGIN();
}

class _TOUCN_ID_SINGIN extends State<TOUCN_ID_SINGIN> {
  @override
  void initState() {
    // TODO: implement initState
  }
  buildForPhone(Orientation orientation) {
    return Scaffold(
      backgroundColor: Color(0xff141322),
      body: new Stack(
        children: <Widget>[
          new Container(
            alignment: Alignment.center,
            child: new Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: <Widget>[
                Padding(
                  padding: EdgeInsets.only(
                      top: MediaQuery.of(context).size.height * 0.3),
                  child: new Image.asset(
                    'assets/image/thumbprint.png',
                    scale: 1.5,
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(
                      top: MediaQuery.of(context).size.height * 0.05),
                  child: new Text(
                    'Touch ID To Open',
                    style: TextStyle(
                        color: Color(0xff6C6B6D),
                        fontFamily: 'Nimbus Sans',
                        fontSize: 19),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(
                      top: MediaQuery.of(context).size.height * 0.35),
                  child: ButtonTheme(
                    minWidth: MediaQuery.of(context).size.width * 0.3,
                    height: MediaQuery.of(context).size.width * 0.1,
                    child:TextButton(
                      style: ButtonStyle(
                        shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                          RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                        ),
                        backgroundColor: MaterialStateProperty.resolveWith<Color>(
                              (Set<MaterialState> states) {
                            if (states.contains(MaterialState.disabled)) {
                              return Color(0xff707070).withOpacity(0.2); // Disabled color
                            }
                            return Color(0xff707070).withOpacity(0.2); // Regular color
                          },
                        ),
                      ),
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      child: Text(
                        'Cancel',
                        style: TextStyle(
                          color: Color(0xff939395),
                          fontSize: 16,
                          fontFamily: 'Nimbus Sans',
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  buildForTablet(Orientation orientation) {
    return Scaffold(
      backgroundColor: Color(0xff141322),
      body: new Stack(
        children: <Widget>[
          new Container(
            alignment: Alignment.center,
            child: new Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: <Widget>[
                Padding(
                  padding: EdgeInsets.only(
                      top: MediaQuery.of(context).size.height * 0.3),
                  child: new Image.asset(
                    'assets/image/thumbprint.png',
                    scale: 2,
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(
                      top: MediaQuery.of(context).size.height * 0.05),
                  child: new Text(
                    'Touch ID To Open',
                    style: TextStyle(
                        color: Color(0xff6C6B6D),
                        fontFamily: 'Nimbus Sans',
                        fontSize: 19),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(
                      top: MediaQuery.of(context).size.height * 0.35),
                  child: ButtonTheme(
                    minWidth: MediaQuery.of(context).size.width * 0.3,
                    height: MediaQuery.of(context).size.width * 0.1,
                    child:TextButton(
                      style: ButtonStyle(
                        shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                          RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                        ),
                        backgroundColor: MaterialStateProperty.resolveWith<Color>(
                              (Set<MaterialState> states) {
                            if (states.contains(MaterialState.disabled)) {
                              return Color(0xff707070).withOpacity(0.2); // Disabled color
                            }
                            return Color(0xff707070).withOpacity(0.2); // Regular color
                          },
                        ),
                      ),
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      child: Text(
                        'Cancel',
                        style: TextStyle(
                          color: Color(0xff939395),
                          fontFamily: 'Nimbus Sans',
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final double shortestSide = MediaQuery.of(context).size.shortestSide;
    final bool useMobileLayout = shortestSide < 600.0;
    final Orientation orientation = MediaQuery.of(context).orientation;
    return Scaffold(
      body: useMobileLayout
          ? buildForPhone(orientation)
          : buildForTablet(orientation),
    );
  }
}
