import 'dart:convert';
import 'dart:ui';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:likewallet/chat_in_app/index_chat.dart';
import 'package:likewallet/tabslide/logout.dart';
import 'package:flutter/material.dart';
import 'package:flutter_jailbreak_detection/flutter_jailbreak_detection.dart';
import 'package:share/share.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:dio/dio.dart';
import 'package:provider/provider.dart' as old_provider hide ReadContext;
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:likewallet/tabslide/currency.dart';
import 'package:likewallet/tabslide/profile.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:package_info/package_info.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/animationPage.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/tabslide/registerEmail/registerEmail.dart';
import 'package:likewallet/tabslide/feedback/feedback.dart';
import 'package:likewallet/support/chat.dart';
import 'package:likewallet/middleware/callFireStore.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:likewallet/libraryman/crypto.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:http/http.dart' as http;
import 'package:likewallet/app_config.dart';
import 'package:likewallet/libraryman/configuration_service.dart';
import 'package:likewallet/libraryman/address_service.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/tabslide/language.dart';
import 'package:likewallet/kyc/kyc.dart';
import 'package:likewallet/libraryman/applang.dart';
import 'package:likewallet/middleware/check_maintenance/check_maintenance.dart';
import 'package:likewallet/main.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as riverpod;
import 'package:likewallet/kycSumSub/kycSumSub.dart';
import 'package:likewallet/mvp/mvp_list.dart';
import 'package:likewallet/security/two_fa.dart';
import 'package:likewallet/developer_test.dart';

import 'ContractUS.dart';

class tabslide extends StatefulWidget {
  @override
  _tabslide createState() => _tabslide();
}

class _tabslide extends State<tabslide> {
  bool _activeID = false;
  bool _activeTouchID = false;
  bool _saveSlip = true;
  bool _lineNotify = true;
  bool _activeNotifi = false;
  bool permissionKYC = false;
  bool permissionMvp = false;
  bool permissionLineNotify = false;
  bool _USD = false;
  bool _THB = false;
  bool _LAK = false;
  bool _VND = false;
  bool _GOLD = false;
  bool _LIKE = false;

  bool _ENG = false;
  bool _THAI = false;
  bool _LAO = false;
  bool _CAM = false;
  bool _VIE = false;

  bool touchID = true;
  bool slipSave = true;

  bool checkTransfer = false;

  String USD = "USD";
  String THB = "THB";
  String LAK = "LAK";
  String VND = "VND";
  String GOLD = "GOLD";

  String ENG = "English";
  String THAI = "Thai  ภาษาไทย";
  String LAO = "Lao ພາສາລາວ";
  String CAM = "Cambodia";
  String VIE = "Vietnam Tiếng việt nam";

  late CryptoEncryptInterface Encrypt;

  late String mnemonic;
  late IConfigurationService configETH;
  late IAddressService addressService;
  late CheckAbout checkAbout;
  bool _saving = false;
  late CryptoEncryptInterface encrypt;
  late BaseAuth auth;
  late SharedPreferences sharedPreferences;
  late OnCallFireStore fireStore;
  late PackageInfo packageInfo;
  String version = '..loading';

  TextEditingController _secret = TextEditingController();
  String url_line =
      'https://line.me/ti/g2/SjIseZ6bt7R7PLAz_jPazQ?utm_source=invitation&utm_medium=link_copy&utm_campaign=default';
  String url_contract =
      'https://sites.google.com/likepoint.io/likewallet-help-center/contract';
  String url_facebook = 'https://www.facebook.com/Likewallet-111904457115522';
  String url_wechat = 'http://line.me/ti/p/@893uuozw';
  String url_whatsapp = 'http://line.me/ti/p/@893uuozw';
  String url_twitter = 'https://twitter.com/LikewalletO';

  String url_telegram = 'https://t.me/likewallet';
  String url_youtube =
      'https://www.youtube.com/channel/UCvj2ILK4psSSSrOtfr9bDKg';
  String url_helpcenter = 'https://help.likepoint.io/';
  String message =
      'Android: http://bit.ly/2x9MqUU\niOS : https://apple.co/2UjLHso';
  String url_home = 'https://sites.google.com/view/likewallet/home';
  Dio dio = new Dio();

  String? phoneNumber;

  bool _statusLineNotify = false;

  @override
  void initState() {
    super.initState();
    auth = new Auth();
    encrypt = new CryptoEncrypt();
    checkAbout = OnCheckAbout();
    fireStore = CallFireStore();
    typeLogin();
    chackCurrency();
    getPermission();
  }

  getPermission() async {
    permissionKYC = await checkAbout.checkPermissionMenu(
        tierLevel: context.read(tierLevel).state, page: 'kyc');
    permissionMvp = await checkAbout.checkPermissionMenu(
        tierLevel: context.read(tierLevel).state, page: 'mvp');
    permissionLineNotify = await checkAbout.checkPermissionMenu(
        tierLevel: context.read(tierLevel).state,
        page: 'lineNotifyUnsubscribe');
    User? user = await auth.getCurrentUser();
    setState(() {
      phoneNumber = user!.phoneNumber;
    });
  }

  Future<bool> destroyApp() async {
    final storage = new FlutterSecureStorage();
    SharedPreferences pref = await SharedPreferences.getInstance();
    //save old lang
    final lang = pref.getString('language_code');
    bool check = await pref.clear();

    await storage.deleteAll();
    setState(() {
      pref.setString('language_code', lang ?? 'en');
    });
    return true;
  }

  void _openURL(url) async {
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      // iOS
      if (await canLaunch(url)) {
        await launch(url);
      } else {
        throw 'Could not launch $url';
      }
    }
  }

  void _showSuccess() {
    // flutter defined function
    showDialog(
      context: context,
      builder: (BuildContext context) {
        // return object of type Dialog
        return AlertDialog(
          title: new Text("Transfer"),
          content: new Text("Your data transfer successfuly !"),
          actions: <Widget>[
            // usually buttons at the bottom of the dialog
            new TextButton(
              child: new Text("Okay !"),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  Future<bool> convertData(String typeMoved, context) async {
    if (_secret.text.length > 0) {
      var key = await encrypt.getKeyEncrypt();

      var data = await auth.getTokenFirebase();
      print(data);
      var url = Uri.https(env.apiUrl, '/retrieveToken');
      var response = await http.post(url, body: {
        "password": _secret.text.trim(),
        "keyEncrypt": key[0],
        "ivEncrypt": key[1],
        "removeOld": typeMoved,
        "_token": data
      });

      var body = json.decode(response.body);
      SharedPreferences pref = await SharedPreferences.getInstance();
      addressService = new AddressService(configETH);
      await configETH.setAddress('');
      // await configETH.setMnemonic(body['seed']);
      mnemonic = await configETH.getMnemonic();
      await generateETH(mnemonic, pref);

      print('generated');

      pref.setBool('transferAccount', false);
      setState(() {
        _saving = false;
      });
      return true;
    } else {
      return false;
    }
  }

  Future generateETH(seed, pref) async {
    String getPK = addressService.getPrivateKey(seed);
    configETH.setPrivateKey(getPK);
    addressService.getPublicAddress(getPK).then((address) {
      print(address.toString());
      configETH.setAddress(address.toString());
    });
  }

  void convertOld() {
    // flutter defined function
    showDialog(
      context: context,
      builder: (BuildContext context) {
        // return object of type Dialog
        return AlertDialog(
          title: new Text(
              AppLocalizations.of(context)!.translate('transfer_old_title')),
          backgroundColor: Colors.white,
          content: SingleChildScrollView(
            scrollDirection: Axis.vertical,
            child: new Column(
              children: <Widget>[
                new Text(
                  AppLocalizations.of(context)!.translate('transfer_old_body'),
                  style: TextStyle(color: Colors.red),
                ),
                new Padding(
                  padding: EdgeInsets.fromLTRB(
                      0, MediaQuery.of(context).size.height / 30, 0, 0),
                ),
                new TextFormField(
                  controller: _secret,
                  cursorColor: Color(0xff146D6E),
                  style: TextStyle(color: Colors.black),
                  decoration: InputDecoration(
                    border: OutlineInputBorder(),
                    labelText: AppLocalizations.of(context)!
                        .translate('transfer_old_secret'),
                    hoverColor: Colors.black,
                    disabledBorder: InputBorder.none,
                    focusColor: Colors.black,
                    fillColor: Colors.black,
                    hintStyle: TextStyle(color: Color(0xffADACA0)),
                    focusedBorder: InputBorder.none,
                    helperStyle: TextStyle(color: Color(0xffADACA0)),
                    labelStyle: TextStyle(color: Color(0xffADACA0)),
                  ),
                ),
                new Padding(
                  padding: EdgeInsets.fromLTRB(
                      0, MediaQuery.of(context).size.height / 30, 0, 0),
                ),
                new Text(
                  AppLocalizations.of(context)!
                      .translate('transfer_old_warning'),
                  style: TextStyle(color: Colors.orange),
                ),
                new Padding(
                  padding: EdgeInsets.fromLTRB(
                      0, MediaQuery.of(context).size.height / 30, 0, 0),
                ),
                new Text(
                  AppLocalizations.of(context)!
                      .translate('transfer_old_warning_two'),
                  style: TextStyle(color: Colors.orange),
                ),
              ],
            ),
          ),
          actions: <Widget>[
            // usually buttons at the bottom of the dialog
            TextButton(
              onPressed: () {
                if (!mounted) return;
                setState(() {
                  _saving = true;
                });
                Navigator.of(context).pop();
                convertData('Y', context).then((data) {
                  _showSuccess();
                });
              },
              child: Text(
                AppLocalizations.of(context)!.translate('transfer_old_yesall'),
              ),
            ),
            TextButton(
              onPressed: () {
                if (!mounted) return;
                setState(() {
                  _saving = true;
                });
                Navigator.of(context).pop();
                convertData('N', context).then((data) {
                  _showSuccess();
                });
              },
              child: Text(
                AppLocalizations.of(context)!.translate('yes_ok'),
              ),
            ),
            TextButton(
              child: new Text(AppLocalizations.of(context)!.translate('no_ok')),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return ModalProgressHUD(
      opacity: 0.1,
      child: Drawer(
          child: Container(
        width: MediaQuery.of(context).size.width * 0.75231481481,
        color: LikeWalletAppTheme.bule2,
        child: ListView(
          // Important: Remove any padding from the ListView.
          children: <Widget>[
            Container(
                height: mediaQuery(context, "hieght", 200),
                alignment: Alignment.topLeft,
                child: new IconButton(
                    icon: Image.asset(LikeWalletImage.icon_menu,
                        height: mediaQuery(context, "hieght", 34.07)),
                    onPressed: () => Navigator.of(context).pop())),
            ListTile(
              title: new Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Text(
                    AppLocalizations.of(context)!
                        .translate('tabslide_language'),
                    style: LikeWalletAppTheme.tabslide(context),
                  ),
                  old_provider.Consumer<AppLanguage>(
                      builder: (context, model, child) {
                    return Text(
                      model.appLocal.toString() == 'en'
                          ? AppLocalizations.of(context)!
                              .translate('language_us')
                          : model.appLocal.toString() == 'th'
                              ? AppLocalizations.of(context)!
                                  .translate('language_thai')
                              : model.appLocal.toString() == 'vi'
                                  ? AppLocalizations.of(context)!
                                      .translate('language_vie')
                                  : model.appLocal.toString() == 'km'
                                      ? AppLocalizations.of(context)!
                                          .translate('language_cam')
                                      : model.appLocal.toString() == 'lo'
                                          ? AppLocalizations.of(context)!
                                              .translate('language_lao')
                                          : '',
                      style: LikeWalletAppTheme.tabslide(context),
                    );
                  }),
                ],
              ),
              onTap: () {
                Navigator.push(context, ScaleRoute(page: Language()))
                    .whenComplete(() {
                  chackCurrency();
                });
              },
            ),
            ListTile(
              title: new Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Text(
                    AppLocalizations.of(context)!
                        .translate('tabslide_currency'),
                    style: LikeWalletAppTheme.tabslide(context),
                  ),
                  if (_USD == true)
                    Text(
                      AppLocalizations.of(context)!.translate('currency_us'),
                      style: LikeWalletAppTheme.tabslide(context),
                    ),
                  if (_THB == true)
                    Text(
                      AppLocalizations.of(context)!.translate('currency_thai'),
                      style: LikeWalletAppTheme.tabslide(context),
                    ),
                  if (_VND == true)
                    Text(
                      AppLocalizations.of(context)!.translate('currency_vnd'),
                      style: LikeWalletAppTheme.tabslide(context),
                    ),
                  if (_LAK == true)
                    Text(
                      AppLocalizations.of(context)!.translate('currency_lao'),
                      style: LikeWalletAppTheme.tabslide(context),
                    ),
                  if (_GOLD == true)
                    Text(
                      AppLocalizations.of(context)!.translate('currency_gold'),
                      style: LikeWalletAppTheme.tabslide(context),
                    ),
                  if (_LIKE == true)
                    Text(
                      AppLocalizations.of(context)!.translate('currency_like'),
                      style: LikeWalletAppTheme.tabslide(context),
                    ),
                ],
              ),
              onTap: () {
                Navigator.push(context, ScaleRoute(page: Currency()));
              },
            ),
//                ListTile(
//                  title: new Row(
//                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                    children: <Widget>[
//                      Text(
//                        AppLocalizations.of(context)
//                            .translate('tabslide_username'),
//                        style: TextStyle(
//                            color: Color(0xff939395),
//                            fontFamily:
//                                AppLocalizations.of(context)!.translate('font1'),
//                            fontWeight: FontWeight.w100,
//                            fontSize: MediaQuery.of(context).size.height *
//                                0.01752136752),
//                      ),
//                      Transform.scale(
//                        scale: MediaQuery.of(context).size.height * 0.001,
//                        child: Switch(
//                          value: _activeID,
//                          onChanged: _onChanged,
////                    ,(bool value) => setState(() => {
////                          _activeID = value,
////                          print(_activeID),
////                        }
////                        ),
//                          activeTrackColor: Color(0xff0FE8D8),
//                          inactiveThumbColor: Color(0xff707070),
//                          activeColor: Color(0xff0FE8D8),
//                          //                        inactiveThumbColor: Color(0xff929194),
//                          inactiveTrackColor: Color(0xff929194),
//                        ),
//                      )
//                    ],
//                  ),
//                  onTap: () {
//                    // Update the state of the app.
//                    // ...
//                  },
//                ),
            ListTile(
              title: new Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Text(
                    AppLocalizations.of(context)!.translate('tabslide_touch'),
                    style: LikeWalletAppTheme.tabslide(context),
                  ),
                  Transform.scale(
                    scale: MediaQuery.of(context).size.height * 0.001,
                    child: Switch(
                      value: _activeTouchID,
                      onChanged: _onChanged2,
//                        (bool value) => setState(() => {
//                          _activeTouchID = value,
//                          print(_activeTouchID),
//                        }),
                      activeTrackColor: Color(0xff0FE8D8).withOpacity(0.8),
                      inactiveThumbColor: Color(0xff929194),
                      activeColor: Color(0xff0FE8D8),
                      //                        inactiveThumbColor: Color(0xff929194),
                      inactiveTrackColor: Color(0xff707070),
                    ),
                  )
                ],
              ),
              onTap: () {
                // Update the state of the app.
                // ...
              },
            ),
            ListTile(
              title: new Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Text(
                    AppLocalizations.of(context)!.translate('tabslide_slip'),
                    style: LikeWalletAppTheme.tabslide(context),
                  ),
                  Transform.scale(
                    scale: MediaQuery.of(context).size.height * 0.001,
                    child: Switch(
                      value: _saveSlip,
                      onChanged: _onChangedSlip,
                      activeTrackColor: Color(0xff0FE8D8).withOpacity(0.8),
                      inactiveThumbColor: Color(0xff929194),
                      activeColor: Color(0xff0FE8D8),
                      //                        inactiveThumbColor: Color(0xff929194),
                      inactiveTrackColor: Color(0xff707070),
                    ),
                  )
                ],
              ),
              onTap: () {
                // Update the state of the app.
                // ...
              },
            ),
            permissionLineNotify
                ? StreamBuilder(
                    stream: FirebaseFirestore.instance
                        .collection('lineNotifyUnsubscribe')
                        .where('phoneNumber', isEqualTo: phoneNumber)
                        .snapshots(),
                    builder: (BuildContext context,
                        AsyncSnapshot<QuerySnapshot<Map<String, dynamic>>>
                            snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return Container();
                      } else {
                        if (snapshot.data!.docs.length > 0) {
                          _statusLineNotify = false;
                        } else {
                          _statusLineNotify = true;
                        }
                        return ListTile(
                          title: new Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: <Widget>[
                              Text(
                                'Line Notify',
                                style: LikeWalletAppTheme.tabslide(context),
                              ),
                              Transform.scale(
                                scale:
                                    MediaQuery.of(context).size.height * 0.001,
                                child: Switch(
                                  value: _statusLineNotify,
                                  onChanged: _onChangedLineNotify,
                                  activeTrackColor:
                                      Color(0xff0FE8D8).withOpacity(0.8),
                                  inactiveThumbColor: Color(0xff929194),
                                  activeColor: Color(0xff0FE8D8),
                                  //                        inactiveThumbColor: Color(0xff929194),
                                  inactiveTrackColor: Color(0xff707070),
                                ),
                              )
                            ],
                          ),
                          onTap: () {
                            // Update the state of the app.
                            // ...
                          },
                        );
                      }
                    })
                : Container(),

            ListTile(
              title: new Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Text(
                    AppLocalizations.of(context)!
                        .translate('two_step_2fa_title'),
                    style: LikeWalletAppTheme.tabslide(context),
                  ),
                ],
              ),
              onTap: () {
                Navigator.push(context, ScaleRoute(page: TwoFA()));
              },
            ),
            ListTile(
              title: new Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Text(
                    AppLocalizations.of(context)!.translate('tabslide_profile'),
                    style: LikeWalletAppTheme.tabslide(context),
                  ),
                ],
              ),
              onTap: () {
                Navigator.push(
                    context, ScaleRoute(page: FbCloneProfileStful()));
              },
            ),
            ListTile(
              title: new Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Text(
                    AppLocalizations.of(context)!.translate('tabslide_kyc'),
                    style: LikeWalletAppTheme.tabslide(context),
                  ),
                ],
              ),
              onTap: () async {
                User? user = await auth.getCurrentUser();
                bool kyc = await fireStore.getPermissionPage(
                    tier: context.read(tierLevel).state, page: 'kyc');
                bool kycSumSub = await fireStore.getPermissionPage(
                    tier: context.read(tierLevel).state, page: 'kycSumSub');
                print(kyc);
                print(kycSumSub);
                //call KYC Normal
                if (kyc && !kycSumSub) {
                  // Navigator.pushNamed(context, '/kyc');
                  Navigator.push(
                      context,
                      ScaleRoute(
                        page: KYC(
                          frontPhoto: 'none',
                          backPhoto: 'none',
                          selfiePhoto: 'none',
                        ),
                      ));
                  //call KYC SumSub
                } else if (!kyc && kycSumSub) {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => KYCSumSub(
                            uid: user?.uid ?? '',
                            phone: user?.phoneNumber ?? '')),
                  );
                } else {
                  showColoredToast(
                      AppLocalizations.of(context)!.translate('save_err'));
                }
              },
            ),

//              title: new Row(
//                mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                children: <Widget>[
//                  Text(
//                    AppLocalizations.of(context)!.translate('tabslide_backup'),
//                    style: LikeWalletAppTheme.tabslide(context),
//                  ),
//                ],
//              ),
//              onTap: () {
////                    Navigator.pushNamed(context, '/kyc');
//                AppRoutes.makeFirst(
//                    context,
//                    PinProtect(
//                      redirectPage: 'BACKUP_SECRETKEY',
//                    ));
//              },
//            ),

//            ListTile(
//              title: new Row(
//                mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                children: <Widget>[
//                  Text(
//                    AppLocalizations.of(context)!.translate('tabslide_noti'),
//                    style: LikeWalletAppTheme.tabslide(context),
//                  ),
//                  Transform.scale(
//                    scale: MediaQuery.of(context).size.height * 0.001,
//                    child: Switch(
//                      value: _activeNotifi,
//                      onChanged: _onChanged3,
////                    ,(bool value) => setState(() => {
////                          _activeID = value,
////                          print(_activeID),
////                        }
////                        ),
//                      activeTrackColor: Color(0xff0FE8D8).withOpacity(0.8),
//                      inactiveThumbColor: Color(0xff929194),
//                      activeColor: Color(0xff0FE8D8),
//                      //                        inactiveThumbColor: Color(0xff929194),
//                      inactiveTrackColor: Color(0xff707070),
//                    ),
//                  )
//                ],
//              ),
//              onTap: () {
//                // Update the state of the app.
//                // ...
//              },
//            ),
            ListTile(
              title: Text(
                AppLocalizations.of(context)!.translate('tabslide_email'),
                style: LikeWalletAppTheme.tabslide(context),
              ),
              onTap: () {
                // Update the state of the app.
                // ...
                Navigator.push(
                    context,
                    ScaleRoute(
                      page: RegisterEmail(),
                    ));
              },
            ),
            ListTile(
              title: Text(
                AppLocalizations.of(context)!.translate('tabslide_feedback'),
                style: LikeWalletAppTheme.tabslide(context),
              ),
              onTap: () {
                // Update the state of the app.
                // ...
                Navigator.push(
                    context,
                    ScaleRoute(
                      page: FeedBack(),
                    ));
              },
            ),
            permissionMvp
                ? ListTile(
                    title: Text(
                      'MVP List',
                      style: LikeWalletAppTheme.tabslide(context),
                    ),
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(builder: (context) => MvpListPage()),
                      );
                    },
                  )
                : Container(),
            // developerMode
            //     ? ListTile(
            //         title: Text(
            //           'DeveloperTest',
            //           style: LikeWalletAppTheme.tabslide(context),
            //         ),
            //         onTap: () {
            //           Navigator.push(
            //             context,
            //             MaterialPageRoute(
            //                 builder: (context) => DeveloperTest()),
            //           );
            //         },
            //       )
            //     : Container(),

            ListTile(
              title: Text(
                AppLocalizations.of(context)!.translate('tabslide_contact'),
                style: LikeWalletAppTheme.tabslide(context),
              ),
              onTap: () {
                // Update the state of the app.
                // ...
                // _showDialogContract();
                _openURL(url_contract);
              },
            ),
            ListTile(
              title: Text(
                AppLocalizations.of(context)!.translate('tabslide_logout'),
                style: LikeWalletAppTheme.tabslide(context),
              ),
              onTap: () async {
                await LogoutDialog(context);
                // Update the state of the app.
                // ...
              },
            ),
            checkTransfer == true
                ? ListTile(
                    title: Text(
                      AppLocalizations.of(context)!
                          .translate('tabslide_transfer'),
                      style: LikeWalletAppTheme.tabslide(context),
                    ),
                    onTap: () {
                      convertOld();
                      // Update the state of the app.
                      // ...
                    },
                  )
                : Container(),

            Column(
              children: <Widget>[
                SizedBox(
                  width: MediaQuery.of(context).size.width * 0.58575,
                  child: Divider(
                    color: Color(0xff6C6B6D),
                  ),
                )
              ],
            ),
            ListTile(
              title: Text(
                AppLocalizations.of(context)!
                    .translate('tabslide_joincommunity'),
                style: LikeWalletAppTheme.tabslide(context),
              ),
              onTap: () {
                // Update the state of the app.
                // ...
              },
            ),
            // GestureDetector(
            //   behavior: HitTestBehavior.opaque,
            //   onTap: () async {
            //     print('line');
            //     _openURL(url_line);
            //   },
            //   child: ListTile(
            //     title: new Row(
            //       children: <Widget>[
            //         Text(
            //           'LINE',
            //           style: LikeWalletAppTheme.tabslide(context),
            //         ),
            //         Padding(
            //           padding: EdgeInsets.only(left: 10),
            //           child: Image(
            //             image: AssetImage(LikeWalletImage.icon_line),
            //             height: mediaQuery(context, 'height', 77.91),
            //           ),
            //         )
            //       ],
            //     ),
            //   ),
            // ),
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () async {
                _openURL(url_facebook);
              },
              child: ListTile(
                title: new Row(
                  children: <Widget>[
                    Text(
                      'Facebook',
                      style: LikeWalletAppTheme.tabslide(context),
                    ),
                    Padding(
                      padding: EdgeInsets.only(left: 10),
                      child: Image(
                        image: AssetImage(LikeWalletImage.icon_facebook),
                        height: mediaQuery(context, 'height', 77.91),
                      ),
                    )
                  ],
                ),
              ),
            ),
//            GestureDetector(
//              behavior: HitTestBehavior.opaque,
//              onTap: () async {
//                print('wechat');
////                _openURL('http://line.me/ti/p/@893uuozw');
//              },
//              child: ListTile(
//                title: new Row(
//                  children: <Widget>[
//                    Text(
//                      'WeChat',
//                      style: LikeWalletAppTheme.tabslide(context),
//                    ),
//                    Padding(
//                      padding: EdgeInsets.only(left: 10),
//                      child: Image(
//                        image: AssetImage(LikeWalletImage.icon_wechat),
//                        height: mediaQuery(context, 'height', 77.91),
//                      ),
//                    )
//                  ],
//                ),
//              ),
//            ),
//            ListTile(
//              title: new Row(
//                children: <Widget>[
//                  Text(
//                    'WhatsApp',
//                    style: LikeWalletAppTheme.tabslide(context),
//                  ),
//                  Padding(
//                    padding: EdgeInsets.only(left: 10),
//                    child: Image(
//                      image: AssetImage(LikeWalletImage.icon_whatsapp),
//                      height: mediaQuery(context, 'height', 77.91),
//                    ),
//                  )
//                ],
//              ),
//            ),
            GestureDetector(
              onTap: () async {
                _openURL(url_twitter);
              },
              child: ListTile(
                title: new Row(
                  children: <Widget>[
                    Text(
                      'Twitter',
                      style: LikeWalletAppTheme.tabslide(context),
                    ),
                    Padding(
                      padding: EdgeInsets.only(left: 10),
                      child: Image(
                        image: AssetImage(LikeWalletImage.icon_twitter),
                        height: mediaQuery(context, 'height', 77.91),
                      ),
                    )
                  ],
                ),
              ),
            ),
            // GestureDetector(
            //   onTap: () async {
            //     _openURL(url_telegram);
            //   },
            //   child: ListTile(
            //     title: new Row(
            //       children: <Widget>[
            //         Text(
            //           'Telegram',
            //           style: LikeWalletAppTheme.tabslide(context),
            //         ),
            //         Padding(
            //           padding: EdgeInsets.only(left: 10),
            //           child: Image(
            //             image: AssetImage(LikeWalletImage.icon_telegram),
            //             height: mediaQuery(context, 'height', 77.91),
            //           ),
            //         )
            //       ],
            //     ),
            //   ),
            // ),
//                ListTile(
//                  title: new Row(
//                    children: <Widget>[
//                      Text(
//                        'Reddit',
//                        style: TextStyle(
//                            color: Color(0xff939395),
//                            fontFamily: 'Proxima Nova',
//                            fontWeight: FontWeight.w100,
//                            fontSize: MediaQuery.of(context).size.height *
//                                0.01752136752),
//                      ),
//                      Padding(
//                        padding: EdgeInsets.only(left: 10),
//                        child: Image(
//                          image: AssetImage(
//                              'assets/image/reddit-social-logo-character64.png'),
//                          color: Colors.red,
//                          width: MediaQuery.of(context).size.height *
//                              0.03329487179,
//                        ),
//                      )
//                    ],
//                  ),
//                ),
            GestureDetector(
              onTap: () async {
                _openURL(url_youtube);
              },
              child: ListTile(
                title: new Row(
                  children: <Widget>[
                    Text(
                      'Youtube',
                      style: LikeWalletAppTheme.tabslide(context),
                    ),
                    Padding(
                      padding: EdgeInsets.only(left: 10),
                      child: Image(
                        image: AssetImage(LikeWalletImage.icon_youtube),
                        height: mediaQuery(context, 'height', 77.91),
                      ),
                    )
                  ],
                ),
              ),
            ),
            Column(
              children: <Widget>[
                SizedBox(
                  child: Divider(
                    color: LikeWalletAppTheme.gray2,
                  ),
                )
              ],
            ),
            ListTile(
              title: Text(
                AppLocalizations.of(context)!.translate('chat_operator'),
                style: LikeWalletAppTheme.tabslide(context),
              ),
              onTap: () {
                /// ย้ายไปไลน์ โอ๋เอ๋
                // Navigator.push(context,
                //     MaterialPageRoute(builder: (context) => IndexChatPage()));
                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) =>
                            ContactUSPage()));
              },
            ),
            ListTile(
              title: Text(
                AppLocalizations.of(context)!.translate('tabslide_help'),
                style: LikeWalletAppTheme.tabslide(context),
              ),
              onTap: () {
                _openURL(url_helpcenter);
              },
            ),
            ListTile(
              title: Text(
                AppLocalizations.of(context)!.translate('tabslide_share'),
                style: LikeWalletAppTheme.tabslide(context),
              ),
              onTap: () {
                // Update the state of the app.
                // ...
                Share.share(message, subject: 'Likewallet');
              },
            ),

            ListTile(
              title: Text(
                AppLocalizations.of(context)!.translate('tabslide_privacy'),
                style: LikeWalletAppTheme.tabslide(context),
              ),
              onTap: () {
                showModalBottomSheet(
                    context: context,
                    backgroundColor: LikeWalletAppTheme.bule2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(20),
                          topRight: Radius.circular(20)),
                    ),
                    builder: (BuildContext bc) {
                      return Container(
                          child: Wrap(children: <Widget>[
                            ListTile(
                                title: Text(AppLocalizations.of(context)!.translate('tabslide_privacy_policy'), style: LikeWalletAppTheme.tabslide(context)),
                                onTap: () {
                                  _openURL("https://likewallet.io/privacy-policy");
                                }),
                            ListTile(
                                title: Text(AppLocalizations.of(context)!.translate('tabslide_terms_and_conditions'), style: LikeWalletAppTheme.tabslide(context)),
                                onTap: () {
                                  _openURL("https://likewallet.io/terms-and-conditions");
                                })
                          ]));
                    });
              },
            ),
            ListTile(
              title: new Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Text(
                    AppLocalizations.of(context)!.translate('tabslide_version'),
                    style: LikeWalletAppTheme.tabslide(context),
                  ),
                  Text(
                    version,
                    style: LikeWalletAppTheme.tabslide(context),
                  )
                ],
              ),
              onTap: () {
                _showDialogVersion();
                // Update the state of the app.
                // ...
              },
            ),
            SizedBox(
              height: 500.h,
            ),
          ],
        ),
      )),
      inAsyncCall: _saving,
      progressIndicator: CustomLoading(),
    );
  }

  void _showDialogVersion() {
    // flutter defined function
    showDialog(
      context: context,
      builder: (BuildContext context) {
        // return object of type Dialog
        return AlertDialog(
          backgroundColor: LikeWalletAppTheme.bule2,
          title: new Text(
            AppLocalizations.of(context)!.translate('tabslide_version'),
            style: LikeWalletAppTheme.tabslide(context),
          ),
          content: new Text(
            version,
            style: LikeWalletAppTheme.tabslide(context),
          ),
          actions: <Widget>[
            // usually buttons at the bottom of the dialog
            TextButton(
              child: new Text(
                AppLocalizations.of(context)!.translate('close_name'),
                style: LikeWalletAppTheme.tabslide(context),
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  void _showDialogContract() {
    // flutter defined function
    showDialog(
      context: context,
      builder: (BuildContext context) {
        // return object of type Dialog
        return AlertDialog(
          backgroundColor: Color(0xff141322),
          title: new Text(
            AppLocalizations.of(context)!.translate('contract_title'),
            style: TextStyle(color: Color(0xff6C6B6D)),
          ),
          content: new Text(
            'Email : <EMAIL>',
            style: TextStyle(color: Color(0xff6C6B6D)),
          ),
          actions: <Widget>[
            // usually buttons at the bottom of the dialog
            TextButton(
              child: new Text(
                AppLocalizations.of(context)!.translate('close_name'),
                style: TextStyle(color: Color(0xff6C6B6D)),
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  _onChanged(bool value) async {
    sharedPreferences = await SharedPreferences.getInstance();
    setState(() {
      _activeID = value;
      sharedPreferences.setBool("activeID", _activeID);
      print(_activeID);
//      typeLogin();
    });
  }

  _onChangedLineNotify(bool value) async {
    final result = await FirebaseFirestore.instance
        .collection('lineNotifyUnsubscribe')
        .where('phoneNumber', isEqualTo: phoneNumber)
        .get();
    if (result.docs.length > 0) {
      result.docs.forEach((element) {
        deleteLineNotify(id: element.id, status: value);
      });
    } else {
      addLineNotify(status: value);
    }
  }

  deleteLineNotify({String? id, required bool status}) async {
    FirebaseFirestore.instance
        .collection('lineNotifyUnsubscribe')
        .doc(id)
        .delete()
        .then((value) {
      print('เปิดเเจ้งเตือนผ่านไลค์');
      // setState(() {
      _statusLineNotify = status;
      // });
    });
  }

  addLineNotify({required bool status}) async {
    FirebaseFirestore.instance
        .collection('lineNotifyUnsubscribe')
        .add({"phoneNumber": phoneNumber}).then((value) {
      print('ปิดเเจ้งเตือนผ่านไลค์');
      // setState(() {
      _statusLineNotify = status;
      // });
    });
  }

  _onChanged2(bool value) async {
    sharedPreferences = await SharedPreferences.getInstance();
    setState(() {
      _activeTouchID = value;
      sharedPreferences.setBool("touchID", _activeTouchID);
      print(_activeTouchID);
    });
  }

  _onChangedSlip(bool value) async {
    sharedPreferences = await SharedPreferences.getInstance();
    setState(() {
      _saveSlip = value;
      sharedPreferences.setBool("saveSlip", _saveSlip);
      print(_saveSlip);
    });
  }

  _onChanged3(bool value) async {
    sharedPreferences = await SharedPreferences.getInstance();
    setState(() {
      _activeNotifi = value;
      sharedPreferences.setBool("activeID", _activeNotifi);
      print(_activeNotifi);
//      typeLogin();
    });
  }

  typeLogin() async {
    packageInfo = await PackageInfo.fromPlatform();
    setState(() {
      version = packageInfo.version;
    });

    sharedPreferences = await SharedPreferences.getInstance();
    configETH = new ConfigurationService(sharedPreferences);

    setState(() {
      checkTransfer = sharedPreferences.getBool('transferAccount') ?? false;
    });

    print(checkTransfer);
    setState(() {
      _activeID = sharedPreferences.getBool("activeID") ?? false;
      _activeTouchID = sharedPreferences.getBool("touchID") ?? true;

      _saveSlip = sharedPreferences.getBool("saveSlip") ?? true;
      if (_activeID != null || _activeTouchID != null) {
        if (_activeID) {
          _activeID = sharedPreferences.getBool("activeID") ?? false;
        }
        if (_activeTouchID) {
          _activeTouchID = sharedPreferences.getBool("touchID") ?? true;
        }
      } else {
        _activeID = false;
        _activeTouchID = false;
      }
    });
  }

  chackCurrency() async {
    sharedPreferences = await SharedPreferences.getInstance();

    print(sharedPreferences.getString('currency'));
    if (sharedPreferences.getString('currency') == 'THB') {
      setState(() {
        _THB = sharedPreferences.getBool("THB") ?? true;
      });
    } else {
      setState(() {
        _USD = sharedPreferences.getBool("USD") ?? false;
        _THB = sharedPreferences.getBool("THB") ?? false;
        _LAK = sharedPreferences.getBool("LAK") ?? false;
        _VND = sharedPreferences.getBool("VND") ?? false;
        _GOLD = sharedPreferences.getBool("GOLD") ?? false;
        _LIKE = sharedPreferences.getBool("LIKE") ?? false;

        _ENG = sharedPreferences.getBool("ENG") ?? false;
        _THAI = sharedPreferences.getBool("THAI") ?? false;
        _LAO = sharedPreferences.getBool("LAO") ?? false;
        _CAM = sharedPreferences.getBool("CAM") ?? false;
        _VIE = sharedPreferences.getBool("VIE") ?? false;
      });
    }
  }
}
