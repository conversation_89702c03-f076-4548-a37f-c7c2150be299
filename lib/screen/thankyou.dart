import 'package:flutter/material.dart';
import 'package:likewallet/screen/home.dart';

class THANKYOU extends StatefulWidget {
  _THANKYOU  createState() => new _THANKYOU();
}
class _THANKYOU extends State<THANKYOU> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFFFFFFF),
      body: new Stack(
        alignment: Alignment.center,
        children: <Widget>[
          Positioned(
//          top: MediaQuery.of(context).size.height/5,
            child: new Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: <Widget>[
                new Padding(
                  padding: EdgeInsets.only(top: MediaQuery.of(context).size.height/7,left: MediaQuery.of(context).size.width/14),
                  child: new Row(
                    children: <Widget>[
                      new Text('THANK YOU', style: TextStyle(color: Color(0xff000000), fontSize: 36.0),),
                    ],
                  ),
                ),
                new Padding(
                  padding: EdgeInsets.only(top:MediaQuery.of(context).size.height/40,left: MediaQuery.of(context).size.width/14),
                  child: new Row(
                    children: <Widget>[
                      new Text('Welcome and enjoy your adventures with\n PREMIUM LIKE Wallet.', style: TextStyle(color: Color(0xff000000), fontSize: 15.0),),
                    ],
                  ),
                ),
                Padding(padding: EdgeInsets.only(top:MediaQuery.of(context).size.width/2.45),
                  child:  new ButtonTheme(
                    minWidth: MediaQuery.of(context).size.width/1.2,
                    height: 60,
                    child: TextButton(
                      style: ButtonStyle(
                        shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                          RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                        ),
                        backgroundColor: MaterialStateProperty.resolveWith<Color>(
                              (Set<MaterialState> states) {
                            if (states.contains(MaterialState.disabled)) {
                              return Color(0xff00F1E0); // Disabled color
                            }
                            return Color(0xff00F1E0); // Regular color
                          },
                        ),
                      ),
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(builder: (context) => HomeLikewallet()),
                        );
                      },
                      child: Text(
                        'Go to PREMIUM',
                        style: TextStyle(
                          color: Colors.black,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}