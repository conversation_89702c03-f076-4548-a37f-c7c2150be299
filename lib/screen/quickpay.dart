import 'dart:convert';
import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:likewallet/bank/confirm_transection.dart';
import 'dart:math' as math;
import 'package:likewallet/libraryman/ethcontract.dart';
import 'package:likewallet/libraryman/address_service.dart';
import 'package:likewallet/libraryman/configuration_service.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:http/http.dart' as http;
import 'package:likewallet/app_config.dart';
import 'package:likewallet/setmodel/next_rewards_model.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:intl/intl.dart' as formatIntl;
// import 'package:ads/ads.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:likewallet/libraryman/auth.dart';
// import 'package:firebase_admob/firebase_admob.dart';
import 'package:likewallet/libraryman/AppAds.dart';

class Quickpay extends StatefulWidget {
  Quickpay({this.address, this.name});

  final String? address;
  final String? name;
  _Quickpay createState() => new _Quickpay(address: address, name: name);
}

enum statusClaim { INACTIVE, ACTIVE }

class _Quickpay extends State<Quickpay> with TickerProviderStateMixin {
  _Quickpay({this.address, this.name});
  final String? address;
  final String? name;

  final f = new formatIntl.NumberFormat("###,###");
  late String dropdownValue;
  double nextRewards = 0;
  double upcomingRewards = 0;
  double rewards = 0.0;
  double totalLocked = 0.0;
  late IAddressService addressService;
  late IConfigurationService configETH;
  late BaseETH eth;
  statusClaim isChaim = statusClaim.INACTIVE;
  late String addressETH;
  late String mnemonic;
  late String getPK;
  bool _saving = false;
  int tabMenu = 0;
  // BaseAuth auth;
  String messageUpcoming = "Please, wait next round";
  late List<NextRewards> listRewards;
  bool ads_1 = false;
  bool ads_2 = false;
  bool ads_3 = false;
  bool ads_4 = false;
  int rewardsToday = 0;
  TextEditingController amountText = TextEditingController();

  late String currency;

  @override
  void initState() {
    super.initState();
    currency = 'BHT';
  }

  ///ads admob ////
  ///
  ///
  /// end ads admobb
  ///
  @override
  void dispose() {
//    ads?.dispose();
    super.dispose();
  }

  void showColoredToast(msg, colors) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_LONG,
        backgroundColor: colors,
        textColor: Colors.white);
  }

  void showShortToast(msg) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_SHORT,
        backgroundColor: Colors.cyan,
        textColor: Colors.white);
  }

  setInit() async {
    SharedPreferences pref = await SharedPreferences.getInstance();

    configETH = new ConfigurationService(pref);
    addressService = new AddressService(configETH);
    mnemonic = pref.getString('seed') ?? '';

//    getPK = addressService.getPrivateKey(mnemonic);
    getPK = await configETH.getPrivateKey();

    setState(() {
      addressETH = configETH.getAddress();
    });

    //get address

//    addressService.getPublicAddress(getPK).then((address) {
    print(addressETH);
  }

  buildForPhone(Orientation orientation) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: tabMenu == 1
          ? confirmTransection()
          : SingleChildScrollView(
              child: Stack(
              children: <Widget>[
                new Container(
                  height: MediaQuery.of(context).size.height,
                  decoration: BoxDecoration(
                      gradient: LinearGradient(
                    begin: Alignment.topRight,
                    end: Alignment.bottomLeft,
                    stops: [0.4, 0.6, 0.7, 0.9, 0.95],
                    colors: [
                      // Colors are easy thanks to Flutter's Colors class.
                      Color(0xff111112).withOpacity(0.9),
                      Color(0xff111112).withOpacity(0.8),
                      Color(0xff111112).withOpacity(0.75),
                      Color(0xff111112).withOpacity(0.73),
                      Color(0xff111112).withOpacity(0.69)
                    ],
                  )),
                ),
                Positioned(
                  top: MediaQuery.of(context).size.height * 0.1,
                  child: new Column(
                    children: <Widget>[
                      Container(
                        width: MediaQuery.of(context).size.width * 0.95,
                        alignment: Alignment.centerRight,
                        child: new Text(
                          'Quick pay',
                          textAlign: TextAlign.right,
                          style: TextStyle(
                              fontFamily: 'Proxima Nova',
                              color: Color(0xffFFFFFF),
                              fontSize: 15,
                              fontWeight: FontWeight.normal),
                        ),
                      ),
                      Container(
                        width: MediaQuery.of(context).size.width * 0.95,
                        alignment: Alignment.centerRight,
                        child: new Text(
                          'shop : ' + name!,
                          textAlign: TextAlign.right,
                          style: TextStyle(
                              fontFamily: 'Roboto',
                              color: Color(0xffFFFFFF),
                              fontSize: 30,
                              fontWeight: FontWeight.normal),
                        ),
                      ),
                    ],
                  ),
                ),
                Positioned(
                  top: MediaQuery.of(context).size.height * 0.2,
                  left: MediaQuery.of(context).size.height * 0.02,
                  child: Container(
                      padding: EdgeInsets.only(
                        left: MediaQuery.of(context).size.width * 0.1,
                      ),
                      alignment: Alignment.center,
                      height: MediaQuery.of(context).size.height * 0.1,
                      width: MediaQuery.of(context).size.width * 0.9,
                      child: Column(
                        children: <Widget>[
                          new Text(
                            "Amount BAHT",
                            textAlign: TextAlign.right,
                            style: TextStyle(
                                fontFamily: 'Proxima Nova',
                                color: Color(0xff029A93),
                                fontSize: 32,
                                fontWeight: FontWeight.normal),
                          ),
                        ],
                      )),
                ),
                Positioned(
                  top: MediaQuery.of(context).size.height * 0.25,
                  left: MediaQuery.of(context).size.height * 0.02,
                  child: Container(
                    padding: EdgeInsets.only(
                      left: MediaQuery.of(context).size.width * 0.1,
                    ),
                    alignment: Alignment.center,
                    height: MediaQuery.of(context).size.height * 0.3,
                    width: MediaQuery.of(context).size.width * 0.9,
                    child: TextFormField(
                      controller: amountText,
//                              obscureText: true,
                      autofocus: false,
                      style: TextStyle(
                        fontSize:
                            MediaQuery.of(context).size.height * 0.01794871794,
                        fontFamily: 'Proxima Nova',
                      ),
                      keyboardType: TextInputType.number,
                      decoration: InputDecoration(
                        focusedBorder: InputBorder.none,
                        enabledBorder: OutlineInputBorder(
                            borderSide: BorderSide(
                                width: MediaQuery.of(context).size.width *
                                    0.00055555555,
                                color: Color(0xff707071).withOpacity(0.0))),
                        border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(5.0)),
                        hintText: '0.0',
                        hintStyle: TextStyle(
                          fontSize: MediaQuery.of(context).size.height *
                              0.01794871794,
                          fontFamily: 'Proxima Nova',
                        ),
                        fillColor: Colors.white,
                        filled: true,
                        contentPadding:
                            EdgeInsets.fromLTRB(20.0, 10.0, 20.0, 10.0),
                      ),
                    ),
                  ),
                ),
                Positioned(
                  top: MediaQuery.of(context).size.height * 0.35,
                  left: MediaQuery.of(context).size.height * 0.02,
                  child: Container(
                    padding: EdgeInsets.only(
                      left: MediaQuery.of(context).size.width * 0.1,
                    ),
                    alignment: Alignment.center,
                    height: MediaQuery.of(context).size.height * 0.3,
                    width: MediaQuery.of(context).size.width * 0.9,
                    child: ButtonTheme(
                      minWidth: MediaQuery.of(context).size.width * 0.95,
                      height: MediaQuery.of(context).size.height * 0.07,
                      child: TextButton(
                        style: ButtonStyle(
                          shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                            RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8.0),
                            ),
                          ),
                          backgroundColor: MaterialStateProperty.resolveWith<Color>(
                                (Set<MaterialState> states) {
                              if (states.contains(MaterialState.disabled)) {
                                return Color(0xff150e23).withOpacity(0.8); // Disabled color
                              }
                              return Color(0xff150e23).withOpacity(0.8); // Regular color
                            },
                          ),
                        ),
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => confirmTransection(
                                addressText: address!.trim().toString(),
                                amountSend: currency == 'BHT'
                                    ? (double.parse(amountText.text) * 100).toString()
                                    : '100',
                              ),
                            ),
                          );
                        },
                        child: Text(
                          'Confirm',
                          style: TextStyle(
                            color: Color(0xffB4E60D),
                            fontFamily: 'Proxima Nova',
                            fontSize: 18,
                            fontWeight: FontWeight.normal,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            )),
    );
  }

  buildForTablet(Orientation orientation) {}

  @override
  Widget build(BuildContext context) {
    final double shortestSide = MediaQuery.of(context).size.shortestSide;
    final bool useMobileLayout = shortestSide < 600.0;
    final Orientation orientation = MediaQuery.of(context).orientation;
    return Scaffold(
      body: ModalProgressHUD(
        opacity: 0.1,
        child: useMobileLayout
            ? buildForPhone(orientation)
            : buildForTablet(orientation),
        inAsyncCall: _saving,
        progressIndicator: CustomLoading(),
      ),
    );
  }
}
