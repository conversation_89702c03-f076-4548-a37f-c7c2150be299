import 'dart:convert';
import 'dart:ui';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:likewallet/dialogs/policy_dialog.dart';
import 'package:likewallet/dialogs/terms_and_conditions_dialog.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/login/sing_in.dart';
import 'package:likewallet/screen/registerForm.dart';
import 'package:likewallet/animationPage.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:likewallet/notify.dart';
import 'package:rxdart/subjects.dart';
import 'package:likewallet/receive_notify.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/libraryman/applang.dart';
import 'package:provider/provider.dart';
import 'package:likewallet/login/create_wallet.dart';
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/login/login_new.dart';
import 'package:likewallet/login/sing_in.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
    FlutterLocalNotificationsPlugin();

// Streams are created so that app can respond to notification-related events since the plugin is initialised in the `main` function
final BehaviorSubject<ReceivedNotification> didReceiveLocalNotificationSubject =
    BehaviorSubject<ReceivedNotification>();

final BehaviorSubject<String> selectNotificationSubject =
    BehaviorSubject<String>();

class IndexLike extends StatefulWidget {
  State<StatefulWidget> createState() => new _IndexLike();
}

class _IndexLike extends State<IndexLike> {
  // var page = "SING_IN()";
  late BaseAuth auth;
  bool agreementPolicy = false;
  bool agreementTermsAndCondition = false;
  late AppLanguage appLanguage = Provider.of<AppLanguage>(context);

  bool checkBoxVal = false;

  @override
  void initState() {
    super.initState();
    auth = Auth();
    _requestAndroid();
    _requestIOSPermissions();
    _configureDidReceiveLocalNotificationSubject();
    _configureSelectNotificationSubject();
    _getTermAndPolicy();
    print('ok got it');
  }

  void _requestIOSPermissions() {
    flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            IOSFlutterLocalNotificationsPlugin>()
        ?.requestPermissions(
          alert: true,
          badge: true,
          sound: true,
        );
  }

  void _requestAndroid() async {
    bool statusPermission = await Permission.camera.isGranted;
    if (statusPermission != true) {
      await Permission.camera.request();
    }
    bool statusPermissionMic = await Permission.microphone.isGranted;
    if (statusPermissionMic != true) {
      await Permission.microphone.request();
    }

    bool statusPermissionContacts = await Permission.contacts.isGranted;
    if (statusPermissionContacts != true) {
      await Permission.contacts.request();
    }
  }

  void _configureDidReceiveLocalNotificationSubject() {
    didReceiveLocalNotificationSubject.stream
        .listen((ReceivedNotification receivedNotification) async {
      await showDialog(
        context: context,
        builder: (BuildContext context) => CupertinoAlertDialog(
          title: receivedNotification.title != null
              ? Text(receivedNotification.title.toString())
              : null,
          content: receivedNotification.body != null
              ? Text(receivedNotification.body.toString())
              : null,
          actions: [
            CupertinoDialogAction(
              isDefaultAction: true,
              child: Text('Ok'),
              onPressed: () async {
                Navigator.of(context, rootNavigator: true).pop();
                await Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) =>
                        SecondScreen(receivedNotification.payload.toString()),
                  ),
                );
              },
            )
          ],
        ),
      );
    });
  }

  void _configureSelectNotificationSubject() {
    selectNotificationSubject.stream.listen((String payload) async {
      await Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => SecondScreen(payload)),
      );
    });
  }

  void _getTermAndPolicy() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    setState(() {
      agreementPolicy = prefs.getBool('agreementPolicy') ?? false;
      agreementTermsAndCondition = prefs.getBool('agreementTermsAndCondition') ?? false;
    });
  }

  void _setTermAndPolicy() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    pref.setBool('agreementPolicy', agreementPolicy);
    pref.setBool('agreementTermsAndCondition', agreementTermsAndCondition);
  }

  @override
  void dispose() {
    didReceiveLocalNotificationSubject.close();
    selectNotificationSubject.close();
    super.dispose();
  }

  buildFor480pi(Orientation orientation) {
    return new Stack(
//      textDirection: TextDirection.ltr,
      alignment: Alignment.bottomCenter,
      children: <Widget>[
        new Positioned(
          child: new Container(
            decoration: BoxDecoration(
                image: DecorationImage(
                    image: AssetImage('assets/image/back.png'),
                    fit: BoxFit.cover)),
            width: double.infinity,
            alignment: Alignment.bottomCenter,
            child: new Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                new ButtonBar(
                  alignment: MainAxisAlignment.center,
                  children: <Widget>[
                    new Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisSize: MainAxisSize.max,
                      children: <Widget>[
                        ButtonTheme(
                          minWidth: MediaQuery.of(context).size.width / 1.2,
                          height: MediaQuery.of(context).size.height *
                              Screen_util("height", 319),
                          child: TextButton(
                            style: ButtonStyle(
                              shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                                RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(5.0),
                                ),
                              ),
                              backgroundColor: MaterialStateProperty.resolveWith<Color>(
                                    (Set<MaterialState> states) {
                                  if (states.contains(MaterialState.disabled)) {
                                    return Color(0xff00F1E0); // Disabled color
                                  }
                                  return Color(0xff00F1E0); // Regular color
                                },
                              ),
                            ),
                            onPressed: () {
                              Navigator.pushNamed(context, '/sign_in');
                            },
                            child: Text(
                              AppLocalizations.of(context)!.translate('index_login'),
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 15,
                                fontWeight: FontWeight.normal,
                                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                              ),
                            ),
                          ),
                        ),
                        new Padding(padding: EdgeInsets.only(bottom: 15.0)),
                        ButtonTheme(
                          minWidth: MediaQuery.of(context).size.width / 1.2,
                          height: 50,
                          child: ElevatedButton(
                            style: ButtonStyle(
                              shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                                RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(5.0),
                                ),
                              ),
                              backgroundColor: MaterialStateProperty.resolveWith<Color>(
                                    (Set<MaterialState> states) {
                                  if (states.contains(MaterialState.disabled)) {
                                    return Color(0xffffffff); // Disabled color
                                  }
                                  return Color(0xffffffff); // Regular color
                                },
                              ),
                            ),
                            onPressed: () {
                              Navigator.pushNamed(context, '/register');
                            },
                            child: Text(
                              AppLocalizations.of(context)!.translate('index_register'),
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 15,
                                fontWeight: FontWeight.normal,
                                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                              ),
                            ),
                          )
                        ),
                        new Padding(padding: EdgeInsets.only(bottom: 20.0)),
                      ],
                    )
                  ],
                )
              ],
            ),
          ),
        ),
        new Positioned(
          top: MediaQuery.of(context).size.height / 2.4,
          child: new Container(
            child: new Image.asset(
              'assets/image/logo.png',
              height: mediaQuery(context, 'height', 343.58),
            ),
          ),
        ),
      ],
    );
  }

  buildForPhone(context) {
    return new Stack(
//      textDirection: TextDirection.ltr,
      alignment: Alignment.topCenter,
      children: <Widget>[
        background_image(context),
        new Positioned(
          top: mediaQuery(context, 'height', 100),
          child: _language(context),
        ),
        new Positioned(
            top: mediaQuery(context, 'height', 606.58),
            child: Column(
              children: <Widget>[
                InkWell(
                  onTap: () async {
                    var url =
                        Uri.https('api.likepoint.io', '/checkOldUserName');
                    var response =
                        await http.post(url, body: {'phone_number': ''});
                    var body = json.decode(response.body);
                    print(body);
                  },
                  child: new Container(
                    padding: EdgeInsets.only(
                        bottom: mediaQuery(context, 'height', 50)),
                    child: new Image.asset(
                      'assets/image/logo.png',
                      height: mediaQuery(context, 'height', 226.31),
                    ),
                  ),
                ),
                new Image.asset(
                  'assets/image/likewallet_text.png',
                  width: mediaQuery(context, 'width', 292.2),
                ),
              ],
            )),
        appLanguage.appLocal.toString() == "th"
            ? new Positioned(
                top: mediaQuery(context, 'height', 1500),
                child: Container(
                    margin: EdgeInsets.only(top: 50.sp, left: 50.sp),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text('กรุณาศึกษาและยอมรับ',
                                style: TextStyle(
                                  fontFamily: AppLocalizations.of(context)!
                                      .translate('font1'),
                                  fontSize: 48.sp,
                                  color: Colors.white,
                                )),
                            InkWell(
                              onTap: () async {
                                var resTermsAndCondition = await showDialog(
                                    barrierDismissible: false,
                                    context: context,
                                    builder: (context) {
                                      return TermsAndConditionsDialog();
                                    });
                                setState(() {
                                  agreementTermsAndCondition =
                                      resTermsAndCondition;
                                });
                                _setTermAndPolicy();
                              },
                              child: Text(
                                "ข้อตกลงในการใช้บริการ",
                                style: TextStyle(
                                  color: agreementTermsAndCondition ? LikeWalletAppTheme.bule1 : Colors.orange,
                                  fontFamily: AppLocalizations.of(context)!
                                      .translate('font1'),
                                  fontSize: 48.sp,
                                  fontWeight: FontWeight.bold,
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                            ),
                            Text(' และ ',
                                style: TextStyle(
                                  fontFamily: AppLocalizations.of(context)!
                                      .translate('font1'),
                                  fontSize: 48.sp,
                                  color: Colors.white,
                                )),
                          ],
                        ),
                        SizedBox(height: 25.sp),
                        Row(
                          children: [
                            InkWell(
                              onTap: () async {
                                var resPolicy = await showDialog(
                                    barrierDismissible: false,
                                    context: context,
                                    builder: (context) {
                                      return PolicyDialog();
                                    });
                                setState(() {
                                  agreementPolicy = resPolicy;
                                });
                                _setTermAndPolicy();
                              },
                              child: Text(
                                "นโยบายความเป็นส่วนตัว",
                                style: TextStyle(
                                  color: agreementPolicy ? LikeWalletAppTheme.bule1 : Colors.orange,
                                  fontSize: 48.sp,
                                  fontFamily: AppLocalizations.of(context)!
                                      .translate('font1'),
                                  fontWeight: FontWeight.bold,
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                            ),
                            Text('ของLikeWallet',
                                style: TextStyle(
                                  fontFamily: AppLocalizations.of(context)!
                                      .translate('font1'),
                                  fontSize: 48.sp,
                                  color: Colors.white,
                                )),
                          ],
                        ),
                        SizedBox(height: 25.sp),
                        Text('เพื่อดำเนินการต่อ',
                            style: TextStyle(
                              fontFamily: AppLocalizations.of(context)!
                                  .translate('font1'),
                              fontSize: 48.sp,
                              color: Colors.white,
                            )),
                        SizedBox(height: 30.sp),
                      ],
                    )),
              )
            : new Positioned(
                top: mediaQuery(context, 'height', 1500),
                child: Container(
                    margin: EdgeInsets.only(top: 50.sp, left: 50.sp),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('To continue with LikeWallet, please accept our ',
                            style: TextStyle(
                              fontFamily: AppLocalizations.of(context)!
                                  .translate('font1'),
                              fontSize: 48.sp,
                              color: Colors.white,
                            )),
                        SizedBox(height: 25.sp),
                        Row(
                          children: [
                            InkWell(
                              onTap: () async {
                                var resTermsAndCondition = await showDialog(
                                    barrierDismissible: false,
                                    context: context,
                                    builder: (context) {
                                      return TermsAndConditionsDialog();
                                    });
                                setState(() {
                                  agreementTermsAndCondition =
                                      resTermsAndCondition;
                                });
                                _setTermAndPolicy();
                              },
                              child: Text(
                                "Terms and Conditions",
                                style: TextStyle(
                                  color: agreementTermsAndCondition ? LikeWalletAppTheme.bule1 : Colors.orange,
                                  fontSize: 48.sp,
                                  fontFamily: AppLocalizations.of(context)!
                                      .translate('font1'),
                                  fontWeight: FontWeight.bold,
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                            ),
                            Text(' and ',
                                style: TextStyle(
                                  fontFamily: AppLocalizations.of(context)!
                                      .translate('font1'),
                                  fontSize: 48.sp,
                                  color: Colors.white,
                                )),
                            InkWell(
                              onTap: () async {
                                var resPolicy = await showDialog(
                                    barrierDismissible: false,
                                    context: context,
                                    builder: (context) {
                                      return PolicyDialog();
                                    });
                                setState(() {
                                  agreementPolicy = resPolicy;
                                });
                                _setTermAndPolicy();
                              },
                              child: Text(
                                "Privacy Policy.",
                                style: TextStyle(
                                  color: agreementPolicy ? LikeWalletAppTheme.bule1 : Colors.orange,
                                  fontSize: 48.sp,
                                  fontFamily: AppLocalizations.of(context)!
                                      .translate('font1'),
                                  fontWeight: FontWeight.bold,
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 30.sp),
                      ],
                    )),
              ),
        new Positioned(
          top: mediaQuery(context, 'height', 1760),
          child: Row(
            children: [
              Checkbox(
                  value: checkBoxVal,
                  onChanged: (bool? value) async {
                    SharedPreferences pref = await SharedPreferences.getInstance();
                      checkBoxVal = value!;
                      agreementTermsAndCondition = value;
                      agreementPolicy = value;
                      pref.setBool('agreementPolicy', value);
                      pref.setBool('agreementTermsAndCondition', value);
                      setState(() {});
                  },
                activeColor: LikeWalletAppTheme.bule1,
                checkColor: Colors.white,
                side: BorderSide(color: Colors.white),
              ),
              Container(
                width: 900.w,
                child: Text(
                  'Please Accept Terms and Conditions and Privacy Policy',
                  style: TextStyle(
                    fontFamily: AppLocalizations.of(context)!
                        .translate('font1'),
                    fontSize: 48.sp,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ),
        new Positioned(
          top: mediaQuery(context, 'height', 1950),
          child: new Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              new ButtonBar(
                alignment: MainAxisAlignment.center,
                children: <Widget>[
                  new Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisSize: MainAxisSize.max,
                    children: <Widget>[
                      Container(
                        width: 900.w,
                        height: 132.h,
                        child: TextButton(
                            style: ButtonStyle(
                              backgroundColor: MaterialStateProperty.all<Color>(
                                (!agreementTermsAndCondition ||
                                        !agreementPolicy)
                                    ? Color(0xff00F1E0).withOpacity(0.4)
                                    : Color(0xff00F1E0),
                              ),
                              shape: MaterialStateProperty.all<
                                      RoundedRectangleBorder>(
                                  RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(5.0),
                              )),
                            ),
                            onPressed: () => {
                                  if (agreementTermsAndCondition &&
                                      agreementPolicy)
                                    {
                                      Navigator.push(
                                          context,
                                          CupertinoPageRoute(
                                              builder: (context) => SING_IN()))
                                    }
                                },
                            child: new Text(
                              AppLocalizations.of(context)!
                                  .translate('login_login'),
                              textScaleFactor: 1.0,
                              style: TextStyle(
                                color: Color(0xff000000),
                                fontSize: 50.h,
                                fontWeight: FontWeight.bold,
                                fontFamily: AppLocalizations.of(context)!
                                    .translate('font1'),
                              ),
                            )),
                      ),
                      SizedBox(
                        height: mediaQuery(context, 'height', 10),
                      ),
                      _register(context),
                    ],
                  )
                ],
              )
            ],
          ),
        ),
      ],
    );
  }

  Widget _register(context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: <Widget>[
        new Text(
          AppLocalizations.of(context)!.translate('login_dont'),
          textScaleFactor: 1.0,
          style: TextStyle(
            color: LikeWalletAppTheme.white.withOpacity(0.75),
            fontSize: mediaQuery(context, 'height', 36),
            fontWeight: FontWeight.normal,
            fontFamily: AppLocalizations.of(context)!.translate('font1'),
          ),
        ),
        SizedBox(
          width: mediaQuery(context, 'width', 20),
        ),
        GestureDetector(
          onTap: () {
            if (agreementTermsAndCondition && agreementPolicy) {
              Navigator.push(
                  context,
                  EnterExitRoute(
                      exitPage: IndexLike(), enterPage: CreateWallet()));
            }
          },
          child: new Text(
            AppLocalizations.of(context)!.translate('login_create'),
            textScaleFactor: 1.0,
            style: TextStyle(
              letterSpacing: 0.5,
              color: (!agreementTermsAndCondition || !agreementPolicy)
                  ? LikeWalletAppTheme.bule1.withOpacity(0.40)
                  : LikeWalletAppTheme.bule1.withOpacity(0.75),
              fontSize: mediaQuery(context, 'height', 39),
              fontWeight: FontWeight.normal,
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
            ),
          ),
        ),
      ],
    );
  }

  Widget _language(context) {
    return GestureDetector(
        onTap: () {
          showDialog(
              context: context,
              builder: (BuildContext context) => dialogContent(context));
        },
        child: Container(
            width: mediaQuery(context, 'width', 930),
            alignment: Alignment.centerRight,
            child: Container(
                height: mediaQuery(context, 'height', 95),
                width: mediaQuery(context, 'width', 232),
                decoration: BoxDecoration(
                    border: Border.all(
                        width: mediaQuery(context, 'width', 3),
                        color: LikeWalletAppTheme.white.withOpacity(0.5)),
                    color: LikeWalletAppTheme.bule2,
                    borderRadius: BorderRadius.circular(10)),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    Container(
                        alignment: Alignment.center,
                        width: mediaQuery(context, 'width', 100),
                        child: new Text(
                          AppLocalizations.of(context)!
                              .translate('language_title'),
                          textScaleFactor: 1.0,
                          style: TextStyle(
                            color: LikeWalletAppTheme.white.withOpacity(0.6),
                            fontSize: mediaQuery(context, 'height', 43),
                            fontWeight: FontWeight.bold,
                            fontFamily: AppLocalizations.of(context)!
                                .translate('font1'),
                          ),
                        )),
                    Container(
                        alignment: Alignment.center,
                        width: mediaQuery(context, 'width', 100),
                        child: Icon(
                          Icons.keyboard_arrow_down,
                          color: LikeWalletAppTheme.white.withOpacity(0.6),
                          size: mediaQuery(context, 'height', 60),
                        )),
                  ],
                ))));
  }

//  dialogContent(BuildContext context) {
//    return Container(
//        height: mediaQuery(context, 'height', 95),
//        width: mediaQuery(context, 'width', 232),
//        decoration: BoxDecoration(
//            border:
//                Border.all(color: LikeWalletAppTheme.white.withOpacity(0.1)),
//            color: LikeWalletAppTheme.bule1,
//            borderRadius: BorderRadius.circular(10)),
//        child: Column(
//          mainAxisAlignment: MainAxisAlignment.start,
//          crossAxisAlignment: CrossAxisAlignment.center,
//          children: <Widget>[
//            SizedBox(
//              height: mediaQuery(context, 'height', 670),
//            ),
//
//          ],
//        ));
//  }

  buttonLanguage(text, value, context) {
    var appLanguage = Provider.of<AppLanguage>(context);
    return TextButton(
        onPressed: () {
          print(value);
          appLanguage.changeLanguage(Locale(value));
//              Navigator.pop(context);
          Navigator.of(context).pop();
        },
        child: new Text(
          AppLocalizations.of(context)!.translate(text),
          textScaleFactor: 1.0,
          style: TextStyle(
            color: LikeWalletAppTheme.gray4,
            fontSize: mediaQuery(context, 'height', 57),
            fontWeight: FontWeight.w100,
            fontFamily: AppLocalizations.of(context)!.translate('font1'),
            shadows: <Shadow>[
              Shadow(
                offset: Offset(0.0, 0.0),
                blurRadius: 3.0,
                color: Colors.grey.withOpacity(1),
              ),
            ],
          ),
        ));
  }

  dialogContent(context) {
    return Stack(
      alignment: Alignment.center,
      children: <Widget>[
        Positioned(
          bottom: mediaQuery(context, 'height', 50),
          child: new ClipRect(
            child: new BackdropFilter(
              filter: new ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0),
              child: Align(
//                alignment: Alignment.bottomCenter,
                child: Container(
                  padding: EdgeInsets.only(
                      top: mediaQuery(context, 'height', 100),
                      bottom: mediaQuery(context, 'height', 100)),
                  decoration: BoxDecoration(
                    image: DecorationImage(
                        colorFilter: new ColorFilter.mode(
                            LikeWalletAppTheme.white.withOpacity(0.7),
                            BlendMode.dstIn),
                        image: AssetImage("assets/image/index/modal_bg.png"),
                        fit: BoxFit.fill),
                    borderRadius: BorderRadius.circular(40.0),
                  ),
//                  height: mediaQuery(context, 'height', 1033),
                  width: mediaQuery(context, 'width', 999),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: <Widget>[
                      buttonLanguage('language_us', "en", context),
                      buttonLanguage('language_thai', "th", context),
                      buttonLanguage('language_lao', "lo", context),
                      buttonLanguage('language_cam', "km", context),
                      buttonLanguage('language_vie', "vi", context)
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

//  void dialogContent(BuildContext context) {
//    return Dialog(
//      shape: RoundedRectangleBorder(
//        borderRadius: BorderRadius.circular(20.0),
//      ),
//      backgroundColor: Colors.red,
//      child:
//    );
//  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        child: Scaffold(body: buildForPhone(context)),
        onWillPop: () {
          return Future.value();
        });
  }
}
