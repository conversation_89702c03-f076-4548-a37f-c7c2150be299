
import 'package:flutter/material.dart';
import 'package:likewallet/libraryman/applang.dart';
import 'package:provider/provider.dart';
import 'package:likewallet/libraryman/app_local.dart';


class ChangeLanguage extends StatefulWidget {
  State<StatefulWidget> createState() => new _ChangeLanguage();
}

class _ChangeLanguage extends State<ChangeLanguage> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }
  @override
  Widget build(BuildContext context) {
    var appLanguage = Provider.of<AppLanguage>(context);
    return Scaffold(
      appBar: AppBar(
        title: Text('Likewallet'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            Text(
              AppLocalizations.of(context)!.translate('MessageTEST'),
              style: TextStyle(fontSize: 32),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                ElevatedButton(
                  onPressed: () {
                    print('en');
                    appLanguage.changeLanguage(Locale("en"));
                  },
                  child: Text('English'),
                ),
                ElevatedButton(
                  onPressed: () {
                    print('th');
                    appLanguage.changeLanguage(Locale("th"));
                  },
                  child: Text('ไทย'),
                )
              ],
            )
          ],
        ),
      ),
    );
  }
}