import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:likewallet/chat_in_app/index_chat.dart';
import 'package:likewallet/screen/home.dart';
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/routes.dart';
import 'package:likewallet/icon/icon_home_icons.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:likewallet/support/chat.dart';

import 'ContractUS.dart';

class NavigationBar extends StatefulWidget {
  _NavigationBar createState() => new _NavigationBar();
}

class _NavigationBar extends State<NavigationBar> {
  int _selectPage = 0;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  int curIndex = 0;
  Widget build(BuildContext context) {
    return Container(
      height: 290.h,
      color: Color(0xffF5F5F5),
      child: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          Container(
            color: Color(0xffF5F5F5),
            height: mediaQuery(context, 'height', 218.4),
            width: MediaQuery.of(context).size.width,
          ),
          Positioned(
            top: 50.h,
            width: MediaQuery.of(context).size.width,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 50.w),
              margin: EdgeInsets.only(top: 55.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextButton.icon(
                    onPressed: () {
                      AppRoutes.makeFirst(
                          context, HomeLikewallet(selectPage: 0));
                    },
                    icon: Icon(IconHome.path_43609,
                        size: mediaQuery(context, 'height', 60),
                        color: Color(0xffB3B3B4)),
                    label: Container(),
                  ),
                  TextButton.icon(
                    onPressed: () {
                      AppRoutes.makeFirst(
                          context, HomeLikewallet(selectPage: 1));
                    },
                    icon: Icon(IconHome.path_43608,
                        size: mediaQuery(context, 'height', 60),
                        color: Color(0xffB3B3B4)),
                    label: Container(),
                  ),
                  TextButton.icon(
                    onPressed: () {
                      AppRoutes.makeFirst(
                          context, HomeLikewallet(selectPage: 2));
                    },
                    icon: Icon(IconHome.group_24548,
                        size: mediaQuery(context, 'height', 60),
                        color: Color(0xffB3B3B4)),
                    label: Container(),
                  ),
                  TextButton.icon(
                    onPressed: () {
                      /// ย้ายไปไลน์ โอ๋เอ๋
                      // Navigator.pushReplacement(
                      //   context,
                      //   MaterialPageRoute(
                      //     builder: (context) => IndexChatPage(),
                      //   ),
                      // );
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) =>
                                  ContactUSPage()));
                    },
                    icon: Icon(IconHome.path_58781,
                        size: mediaQuery(context, 'height', 60),
                        color: Color(0xffB3B3B4)),
                    label: Container(),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
