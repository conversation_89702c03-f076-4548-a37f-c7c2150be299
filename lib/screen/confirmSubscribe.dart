import 'package:flutter/material.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:likewallet/screen/thankyou.dart';

class ConfirmSubscribePage extends StatefulWidget {
  _ConfirmSubscribePage createState() => new _ConfirmSubscribePage();
}

class _ConfirmSubscribePage extends State<ConfirmSubscribePage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFFFFFFF),
      body: new Stack(
//        alignment: Alignment.center,
        children: <Widget>[
          ModalProgressHUD(
              opacity: 0.1,
              child: _buildWidget(),
              inAsyncCall: _saving,
              color: Color(0xff707071),
              progressIndicator: _Progress()),
        ],
      ),
    );
  }

  Widget _buildWidget() {
    return Positioned(
//          top: MediaQuery.of(context).size.height/5,
      child: new Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          new Padding(
            padding: EdgeInsets.only(
                top: MediaQuery.of(context).size.height / 5,
                left: MediaQuery.of(context).size.width / 14),
            child: new Row(
              children: <Widget>[
                new Text(
                  'The amount of 3000 LKE will be deducted\nfrom your wallet.',
                  style: TextStyle(color: Color(0xff0000000), fontSize: 15.0),
                ),
              ],
            ),
          ),
          new Padding(
            padding: EdgeInsets.only(
                top: 30, left: MediaQuery.of(context).size.width / 14),
            child: new Row(
              children: <Widget>[
                new Text(
                  'Please tap on PAY to confirm your payment.',
                  style: TextStyle(color: Color(0xff0000000), fontSize: 15.0),
                ),
              ],
            ),
          ),
          Padding(
            padding:
            EdgeInsets.only(top: MediaQuery.of(context).size.width / 2.9),
            child: ButtonTheme(
              minWidth: MediaQuery.of(context).size.width / 1.2,
              height: 60,
              child: TextButton(
                style: ButtonStyle(
                  shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                    RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                  ),
                  backgroundColor: MaterialStateProperty.resolveWith<Color>(
                        (Set<MaterialState> states) {
                      if (states.contains(MaterialState.disabled)) {
                        return Color(0xff0FE8D8); // Disabled color
                      }
                      return Color(0xff0FE8D8); // Regular color
                    },
                  ),
                ),
                onPressed: _loadingView,
                child: Text(
                  'Pay',
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),

            ),
          ),
          Padding(
            padding:
            EdgeInsets.only(top: MediaQuery.of(context).size.width / 20),
            child: ButtonTheme(
              minWidth: MediaQuery.of(context).size.width / 1.2,
              height: 60,
              child: TextButton(
                style: ButtonStyle(
                  shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                    RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                  ),
                  backgroundColor: MaterialStateProperty.resolveWith<Color>(
                        (Set<MaterialState> states) {
                      if (states.contains(MaterialState.disabled)) {
                        return Color(0xffDEDEDE); // Disabled color
                      }
                      return Color(0xffDEDEDE); // Regular color
                    },
                  ),
                ),
                onPressed: () {
                  Navigator.of(context).pop(false);
                },
                child: Text(
                  'Cancel',
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _Progress() {
    return Container(
        alignment: Alignment.topCenter,
        padding: EdgeInsets.only(top: MediaQuery.of(context).size.width / 3),
        child: new Column(
          children: <Widget>[
            new Container(
              child: CircularProgressIndicator(
                strokeWidth: 5,
                backgroundColor: Color(0xff707071),
                valueColor: new AlwaysStoppedAnimation<Color>(Colors.white),
              ),
              width: 150,
              height: 150,
            ),
            Padding(
              padding:
              EdgeInsets.only(top: MediaQuery.of(context).size.width / 20),
              child: new Text(
                'Processing...',
                style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold),
              ),
            )
          ],
        ));
  }

  bool _saving = false;
  Widget? _loadingView() {
    setState(() {
      _saving = true;
    });
    //Simulate a service call
    print('submitting to backend...');
    new Future.delayed(new Duration(seconds: 4), () {
      setState(() {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => THANKYOU()),
        );
      });
    });
  }
}