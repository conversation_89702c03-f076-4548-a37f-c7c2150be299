//import 'package:flutter/material.dart';
//import 'login_page.dart';
//import 'auth.dart';
////import 'home_page.dart';
//import 'index.dart';
//import 'package:shared_preferences/shared_preferences.dart';
////import 'main_wallet.dart';
//import 'package:likewallet/screen/index.dart';
//
//class RootPage extends StatefulWidget {
////  RootPage({this.auth});
////  final BaseAuth auth;
//  @override
//    State<StatefulWidget> createState() => new _RootPageState();
//}
//
//
//enum AuthStatus {
//  notSignedIn,
//  signedIn
//}
//enum SignMnemonic {
//  active,
//  inactive
//}
//
//class _RootPageState extends State<RootPage> {
//
//  AuthStatus authStatus = AuthStatus.notSignedIn;
//  SignMnemonic signMnemonic = SignMnemonic.inactive;
//
//  initState() {
//    // TODO: implement initState
//    super.initState();
////    widget.auth.currentUser().then((userId){
////      setState(() {RootPage
////        authStatus = userId == null ? AuthStatus.notSignedIn : AuthStatus.signedIn;
////        print("setState");
////      });
////    });
//      checkSign();
//  }
//  void checkSign() async {
//    final prefs = await SharedPreferences.getInstance();
////    await prefs.remove("activeUser");
//    String activeUser = await prefs.getString("activeUser");
//    print(activeUser);
//    setState(() {
//      signMnemonic = activeUser == null ? SignMnemonic.inactive : SignMnemonic.active;
//    });
//  }
//
//
//  void _signedIn(){
//    setState(() {
////      authStatus = AuthStatus.signedIn;
//      signMnemonic = SignMnemonic.active;
//    });
//  }
//
//  void _signedOut(){
//    setState(() {
////      authStatus = AuthStatus.notSignedIn;
//      signMnemonic = SignMnemonic.inactive;
//    });
//  }
//
////  @override
////    Widget build(BuildContext context) {
////    switch(authStatus){
////      case AuthStatus.notSignedIn:
////        return new LoginPage(auth: widget.auth, onSignedIn: _signedIn,);
////      case AuthStatus.signedIn:
////        return new HomePage(
////          auth: widget.auth,
////          onSignedOut: _signedOut,
////        );
////
////    }
////  }
//  final List mnemonic = [];
//  @override
//  Widget build(BuildContext context) {
//    switch(signMnemonic){
//      case SignMnemonic.inactive:
//        return new IndexLike();
//      case SignMnemonic.active:
////        return new MainWallet(
////          this.mnemonic
////        );
//        return new IndexLike();
//    }
//  }
//}