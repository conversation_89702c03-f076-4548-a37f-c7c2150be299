import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/screen_util.dart';
import 'package:url_launcher/url_launcher.dart';

class ContactUSPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.black,
        body: Container(
            decoration: BoxDecoration(color: Colors.black),
            alignment: Alignment.center,
            padding: EdgeInsets.all(20),
            child: Container(
              padding: EdgeInsets.only(top: 350.h, left: 50.w, right: 50.w),
              child: ListView(
                children: [
                  Image.asset(LikeWalletImage.like_point,
                      height: mediaQuery(context, 'height', 137)),
                  SizedBox(
                    height: 30,
                  ),
                  Text(
                    'ติดต่อเราได้ง่ายขึ้นกับไลค์วอลเลท',
                    style: TextStyle(
                      fontFamily: AppLocalizations.of(context)!.translate('font1'),
                      fontSize: mediaQuery(context, 'height', 45),
                      color: const Color(0xff00c5c2),
                      letterSpacing: mediaQuery(context, 'height', 6.3),
                      fontWeight: FontWeight.w300,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 10),
                  Text('ไลค์วอลเลท เสนอช่องทางที่สะดวกกว่าในการติดต่อ\nแอดมินของเราเพียงคลิกลิงค์นี้',
                    style: TextStyle(
                      fontFamily: AppLocalizations.of(context)!.translate('font1'),
                      fontSize: mediaQuery(context, 'height', 35),
                      color: const Color(0xff00c5c2),
                      letterSpacing: mediaQuery(context, 'height', 6.3),
                      fontWeight: FontWeight.w300,
                    ),

                  ),
                  InkWell(
                    child: Text(
                      'https://lin.ee/FGA6voL',
                      style: TextStyle(color: Colors.blue),
                    ),
                    onTap: () async {
                      await launch('https://lin.ee/FGA6voL');
                      // Handle link tap, e.g., open in web browser
                    },
                  ),
                  Text('หรือเพิ่มเพื่อน @likewallet ใน LINE\nเราพร้อมดูแลคุณค่ะ',
                      style: TextStyle(
                        fontFamily: AppLocalizations.of(context)!.translate('font1'),
                        fontSize: mediaQuery(context, 'height', 35),
                        color: const Color(0xff00c5c2),
                        letterSpacing: mediaQuery(context, 'height', 6.3),
                        fontWeight: FontWeight.w300,
                      )),
                  SizedBox(height: 20),
                  Divider(),
                  SizedBox(height: 20),
                  Text(
                    'Make Contacting Our Admin Easier',
                    style: TextStyle(
                      fontFamily: AppLocalizations.of(context)!.translate('font1'),
                      fontSize: mediaQuery(context, 'height', 45),
                      color: const Color(0xff00c5c2),
                      letterSpacing: mediaQuery(context, 'height', 6.3),
                      fontWeight: FontWeight.w300,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 10),
                  Text('To make contacting our admin easier, simply click',
                      style: TextStyle(
                        fontFamily: AppLocalizations.of(context)!.translate('font1'),
                        fontSize: mediaQuery(context, 'height', 35),
                        color: const Color(0xff00c5c2),
                        letterSpacing: mediaQuery(context, 'height', 6.3),
                        fontWeight: FontWeight.w300,
                      )),
                  InkWell(
                    child: Text(
                      'https://lin.ee/FGA6voL',
                      style: TextStyle(color: Colors.blue),
                    ),
                    onTap: () async {
                      await launch('https://lin.ee/FGA6voL');
                      // Handle link tap, e.g., open in web browser
                    },
                  ),
                  Text('or add us on LINE: @likewallet\nWe\'re here to help.',
                    style: TextStyle(
                      fontFamily: AppLocalizations.of(context)!.translate('font1'),
                      fontSize: mediaQuery(context, 'height', 35),
                      color: const Color(0xff00c5c2),
                      letterSpacing: mediaQuery(context, 'height', 6.3),
                      fontWeight: FontWeight.w300,
                    ),),
                  Container(
                    margin: EdgeInsets.only(top: 30),
                    alignment: Alignment.center,
                    child: ButtonTheme(
                      minWidth: MediaQuery.of(context).size.width * 0.86112,
                      height: MediaQuery.of(context).size.height * 0.05658024691,
                      child: TextButton(
                        style: ButtonStyle(
                          shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                            RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8.0),
                            ),
                          ),
                          backgroundColor: MaterialStateProperty.resolveWith<Color>(
                                (Set<MaterialState> states) {
                              if (states.contains(MaterialState.disabled)) {
                                return Color(0xff00F1E0); // Disabled color
                              }
                              return Color(0xff00F1E0); // Regular color
                            },
                          ),
                        ),
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        child: Text(
                          AppLocalizations.of(context)!.translate('promtpay_thank'),
                          style: TextStyle(
                            fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                            color: Colors.black,
                            fontSize: MediaQuery.of(context).size.height *
                                Screen_util("height", 42),
                            fontWeight: FontWeight.w100,
                          ),
                        ),
                      ),
                    ),
                  )
                ],
              ),
            )
        ));
  }
}