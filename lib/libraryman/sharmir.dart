// import 'dart:async';
// import 'dart:collection';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:dart_ssss/dart_ssss.dart';
//
// abstract class SharmirInterface {
//   Future<bool> checkLogin();
//   Map<int, List<int>> setSharmir(String text);
//   String combineSharmir(Map<int, List<int>> valueInt);
//   Future<bool> testSharmir(String text);
// }
// class SharmirMan implements SharmirInterface {
//
//   bool login = false;
//
//   @override
//   Future<bool> checkLogin() async {
//     SharedPreferences pref = await SharedPreferences.getInstance();
//     login = pref.getBool('login') ?? false;
//     return login;
//   }
//   Future<bool> testSharmir(String text)  {
//     String manzer = text;
//     List<int> secretInByteValues = manzer.codeUnits;
//     Map<int, List<int>> newshare = new HashMap();
//     SecretScheme ss = new SecretScheme(4, 3);
//     var x = 0;
//     Map<int, List<int>> shares = ss.createShares(secretInByteValues);
//     print(shares);
//     shares.forEach((key, value) {
//       // print('key: $key, value: $value');
//       if(x<3){
//         newshare[key] = value;
//       }
//       x++;
//
//       if(x==3){
//         print(newshare.length);
//         //retrieve sharmir
//         String retrieveSeed = '';
//         List<int> recombinedSecretInBytes = ss.combineShares(newshare);
//         for(var i=0;i<recombinedSecretInBytes.length;i++){
//           retrieveSeed+= String.fromCharCode(recombinedSecretInBytes[i]);
//         }
//       }
//     });
//
//     return Future.value();
//   }
//   Map<int, List<int>> setSharmir(String text)  {
//     String manzer = text;
//     List<int> secretInByteValues = manzer.codeUnits;
//     Map<int, List<int>> newshare = new HashMap();
//     SecretScheme ss = new SecretScheme(4, 3);
//     var x = 0;
//     Map<int, List<int>> shares = ss.createShares(secretInByteValues);
//     print(shares.length);
//     shares.forEach((key, value) {
//       x++;
//       // print('key: $key, value: $value');
//       newshare[key] = value;
//       print('man$x');
//
//     });
//     return newshare;
//   }
//   String combineSharmir(Map<int, List<int>> newshare) {
//     SecretScheme ss = new SecretScheme(4, 3);
//     String retrieveSeed = '';
//     ;
//     List<int> recombinedSecretInBytes = ss.combineShares(newshare);
//     for(var i=0;i<recombinedSecretInBytes.length;i++){
//       retrieveSeed+= String.fromCharCode(recombinedSecretInBytes[i]);
//     }
//     return retrieveSeed;
//   }
//
// }