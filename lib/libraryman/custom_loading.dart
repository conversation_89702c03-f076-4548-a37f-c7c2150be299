import 'package:flare_flutter/flare_actor.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

import 'dart:math';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:likewallet/screen_util.dart';

import 'package:loading_indicator/loading_indicator.dart';

class CustomLoading extends StatefulWidget {
  // ignore: prefer_const_constructors_in_immutables
  CustomLoading({
    Key? key,
    this.color = Colors.cyan,
    this.size = 50.0,
    this.itemBuilder,
    this.duration = const Duration(milliseconds: 1200),
    this.controller,
  })  : assert(
            !(itemBuilder is IndexedWidgetBuilder && color is Color) &&
                !(itemBuilder == null && color == null),
            'You should specify either a itemBuilder or a color'),
        assert(size != null),
        super(key: key);

  final Color? color;
  final double? size;
  final IndexedWidgetBuilder? itemBuilder;
  final Duration? duration;
  final AnimationController? controller;

  @override
  _SpinKitFadingCircleState createState() => _SpinKitFadingCircleState();
}

class _SpinKitFadingCircleState extends State<CustomLoading>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
//    _controller = (widget.controller ??
//        AnimationController(vsync: this, duration: widget.duration))
//      ..repeat();

    _controller = AnimationController(
      duration: const Duration(seconds: 10),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () { return Future.value(); },
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Stack(
              children: <Widget>[
                Center(
                  child: SizedBox(
                    width: mediaQuery(context, 'width', 600),
                    child: LoadingIndicator(
                      colors: [Color(0xFF29D8DA).withOpacity(1)],
                      indicatorType: Indicator.ballClipRotate,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );

//    return Center(
//      child: SizedBox.fromSize(
//        size: Size.square(widget.size),
//        child: Stack(
//          children: [
//            _circle(1, .0),
//            _circle(2, -1.1),
//            _circle(3, -1.0),
//            _circle(4, -0.9),
//            _circle(5, -0.8),
//            _circle(6, -0.7),
//            _circle(7, -0.6),
//            _circle(8, -0.5),
//            _circle(9, -0.4),
//            _circle(10, -0.3),
//            _circle(11, -0.2),
//            _circle(12, -0.1),
//          ],
//        ),
//      ),
//    );
  }

  Widget _circle(int i, [double? delay]) {
    final _size = widget.size! * 0.15, _position = widget.size! * .5;

    return Positioned.fill(
      left: _position,
      top: _position,
      child: Transform(
        transform: Matrix4.rotationZ(30.0 * (i - 1) * 0.0174533),
        child: SpinKitPulse(color: Colors.cyan),
//        child: Align(
//          alignment: Alignment.center,
//          child: FadeTransition(
//            opacity: DelayTween(begin: 0.0, end: 1.0, delay: delay)
//                .animate(_controller),
//            child: SizedBox.fromSize(
//              size: Size.square(_size),
//              child: _itemBuilder(i - 1),
//            ),
//          ),
//        ),
      ),
    );
  }

  Widget _itemBuilder(int index) {
    return widget.itemBuilder != null
        ? widget.itemBuilder!(context, index)
        : DecoratedBox(
            decoration: BoxDecoration(
              color: widget.color,
              shape: BoxShape.circle,
            ),
          );
  }
}
