// const c_erc20 = new ethers.Contract(this.erc20_addr, this.abiContract, wallet);
// const c_lock = new ethers.Contract(this.lock_addr, this.abiLock, wallet);
// const c_NFT = new ethers.Contract(this.contract_NFT, this.abiNFT, wallet);
// const lottery = new ethers.Contract(this.contractLottery, this.abiLottery, wallet);

import 'dart:async';
import 'package:likewallet/jsonDecode/vending.dart';
import 'package:web3dart/web3dart.dart';
import 'package:path/path.dart' show join, dirname;
import 'package:http/http.dart';
import 'package:web3dart/web3dart.dart';
import 'package:web_socket_channel/io.dart';
import 'package:likewallet/app_config.dart';

abstract class LotteryInterface {
  //จำนวน lotto ในกระเป๋าของคนนั้น
  Future<num> NFTbalanceOf({required String pk});
  //ดึง index ของ lotto
  Future<num> tokenOfOwnerByIndex({required String pk, required int i});
  //ดึงรอบของ lotto รอบนั้น
  Future<num> getLotteryIssueIndex({required int i});

  //ดึง เลข ลอตโต้ ของ lotto token นั้น ๆ
  Future<List<dynamic>> getLotteryNumbers({required int i});

  //ดูรางวัลของ lotto token นั้นๆ ว่าถูกรางวัลไหม
  Future<num> getRewardView({required int i});

  //รับรางวัลหลาย ๆชุด
  Future<String> multiClaim(
      {required String pk, required List<dynamic> numberSet});

  //เช็คเวลาของผู้ล็อค หน่วยเป็นวินาที
  // ignore: slash_for_doc_comments
  /**
      IERC20 token; ///address LIKE
      uint256 expire; /// วันหหมดอายุ timestamp
      uint256 block; /// บล็อคที่เริ่ม
      uint256 start; ///เวลาเริ่ม timestamp
      uint256 amount; /// จำนวนที่ล็อค
      statusWithdraw isWithdraw; ///สถานะการถอน 0 1
   **/
  Future<List<dynamic>> timelockhold({required String address, required int i});

  //ถอนออก พร้อมระบุจำนวน และ ออฟชั่นที่ล็อคไว้ 0-3
  Future<String> withdrawLockup(
      {required String pk, required amount, required int i});

  //ดึงรอบปัจจุบันว่า lotto รอบเท่าไหร่
  Future<num> issueIndex();

  //ดึงตั๋วของผู้ล็อคว่าได้กี่สิทธิ์
  Future<num> getAmountLottery({required String address});

  //นับจำนวนตั๋วคงเหลือ ในรอบนั้น ๆ
  Future<num> countTicket({required String address, required int issueIndex});

  //ไลค์ในกระเป๋า
  Future<num> ERC20balanceOf({required String address});

  //ซื้อทีละหลาย ๆเลข
  Future<String> multiBuyLock(
      {required String pk, required List<dynamic> ticketNumber});

  //ฝากเหรียญเพื่อเอาตั๋ว
  Future<String> depositLockup(
      {required String pk, required String amount, required int option});

  //approve ก่อนฝาก และก่อนซื้อครั้งแรกครั้งเดียว
  Future<String> approve({required String pk, required String address});

  //ตรวจสอบว่ามีการ approve ไปหรือยัง addressOwner คือ address คนซื้อ addressUsed คือ address smart contract Lottery / LOCK
  Future<num> allowance(
      {required String addressOwner, required String addressUsed});

  //ตรวจสอบเลขที่ออก ของรอบก่อนๆ และตำแหน่ง 0-3
  Future<num> historyNumbers({required int index, required int position});
  //ตรวจสอบรางวัลที่ออก ของรอบก่อนๆ และตำแหน่ง 0-3
  Future<num> historyAmount({required int index, required int position});
  //ตัวจัดสรรข้อมูล 0-2 จะคืนมาเป็น % สัดส่วนของรางวัล 4 ตัวตรง 3 ตัว 2 ตัว
  Future<num> allocation({required int index});

  //ดึงความยาว ออฟชั่นการล็อค จะคืนเป็นตัวเลข เอาไปวนลูปเช็คต่อ
  Future<num> getOptionLength();

  //ตรวจสอบข้อมูลของออฟชั่นนั้น ๆ 0-3
  Future<num> option({required int option});

  //ดึงจำนวน lottery ทั้งหมดในระบบว่ามีกี่ใบแล้ว
  Future<num> totalAmount();
}

class Lottery implements LotteryInterface {
  final EthereumAddress c_erc20_addr =
      EthereumAddress.fromHex(env.contractLike);
  final EthereumAddress c_lock_addr =
      EthereumAddress.fromHex(env.contractLotteryLock);
  final EthereumAddress c_NFT_addr = EthereumAddress.fromHex(env.contractNFT);
  final EthereumAddress lottery_addr = EthereumAddress.fromHex(env.lottery);
  static String abi_contractLotteryLock = env.abi_contractLotteryLock;
  static String abi_erc20 = env.abiContractLike;
  static String abi_contractNFT = env.abi_contractNFT;
  static String abi_lottery = env.abi_lottery;
  static int gasClaim = int.parse(env.gasClaim.toString());
  String rpcUrl = env.rpcUrl;
  String wsUrl = env.wsUrl;
  static int chainId = int.parse(env.chainId);

  @override
  Future<num> NFTbalanceOf({required String pk}) async {
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    final credentials = await client.credentialsFromPrivateKey(pk);
    final ownAddress = await credentials.extractAddress();
    final c_NFT = DeployedContract(
        ContractAbi.fromJson(abi_contractNFT.toString(), 'LotteryNFT'),
        c_NFT_addr);
    final c_Lock = DeployedContract(
        ContractAbi.fromJson(
            abi_contractLotteryLock.toString(), 'LockupLikepoint'),
        c_lock_addr);
    final c_erc20 = DeployedContract(
        ContractAbi.fromJson(abi_erc20.toString(), 'LIKEPOINT'), c_erc20_addr);
    final c_lottery = DeployedContract(
        ContractAbi.fromJson(abi_lottery.toString(), 'LotteryLikepoint'),
        lottery_addr);

    final balanceOf = c_NFT.function('balanceOf');
    final balanceLikepoint = await client
        .call(contract: c_NFT, function: balanceOf, params: [ownAddress]);
    var newBalance = EtherAmount.inWei(balanceLikepoint.first as BigInt);
    return newBalance.getValueInUnit(EtherUnit.wei);
  }

  @override
  Future<num> tokenOfOwnerByIndex({required String pk, required int i}) async {
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    final credentials = await client.credentialsFromPrivateKey(pk);
    final ownAddress = await credentials.extractAddress();
    final c_NFT = DeployedContract(
        ContractAbi.fromJson(abi_contractNFT.toString(), 'LotteryNFT'),
        c_NFT_addr);
    final c_Lock = DeployedContract(
        ContractAbi.fromJson(
            abi_contractLotteryLock.toString(), 'LockupLikepoint'),
        c_lock_addr);
    final c_erc20 = DeployedContract(
        ContractAbi.fromJson(abi_erc20.toString(), 'LIKEPOINT'), c_erc20_addr);
    final c_lottery = DeployedContract(
        ContractAbi.fromJson(abi_lottery.toString(), 'LotteryLikepoint'),
        lottery_addr);

    final functionCall = c_NFT.function('tokenOfOwnerByIndex');
    final balanceLikepoint = await client.call(
        contract: c_NFT,
        function: functionCall,
        params: [ownAddress, BigInt.from(i)]);
    var newBalance = EtherAmount.inWei(balanceLikepoint.first as BigInt);
    return newBalance.getValueInUnit(EtherUnit.wei);
  }

  @override
  Future<num> getLotteryIssueIndex({required int i}) async {
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });

    final c_NFT = DeployedContract(
        ContractAbi.fromJson(abi_contractNFT.toString(), 'LotteryNFT'),
        c_NFT_addr);
    final c_Lock = DeployedContract(
        ContractAbi.fromJson(
            abi_contractLotteryLock.toString(), 'LockupLikepoint'),
        c_lock_addr);
    final c_erc20 = DeployedContract(
        ContractAbi.fromJson(abi_erc20.toString(), 'LIKEPOINT'), c_erc20_addr);
    final c_lottery = DeployedContract(
        ContractAbi.fromJson(abi_lottery.toString(), 'LotteryLikepoint'),
        lottery_addr);

    final functionCall = c_NFT.function('getLotteryIssueIndex');
    final balanceLikepoint = await client.call(
        contract: c_NFT, function: functionCall, params: [BigInt.from(i)]);
    var newBalance = EtherAmount.inWei(balanceLikepoint.first as BigInt);
    return newBalance.getValueInUnit(EtherUnit.wei);
  }

  @override
  Future<List<dynamic>> getLotteryNumbers({required int i}) async {
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });

    final c_NFT = DeployedContract(
        ContractAbi.fromJson(abi_contractNFT.toString(), 'LotteryNFT'),
        c_NFT_addr);
    final c_Lock = DeployedContract(
        ContractAbi.fromJson(
            abi_contractLotteryLock.toString(), 'LockupLikepoint'),
        c_lock_addr);
    final c_erc20 = DeployedContract(
        ContractAbi.fromJson(abi_erc20.toString(), 'LIKEPOINT'), c_erc20_addr);
    final c_lottery = DeployedContract(
        ContractAbi.fromJson(abi_lottery.toString(), 'LotteryLikepoint'),
        lottery_addr);

    final functionCall = c_NFT.function('getLotteryNumbers');
    final balanceLikepoint = await client.call(
        contract: c_NFT, function: functionCall, params: [BigInt.from(i)]);

    return balanceLikepoint;
  }

  @override
  Future<num> getRewardView({required int i}) async {
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });

    final c_NFT = DeployedContract(
        ContractAbi.fromJson(abi_contractNFT.toString(), 'LotteryNFT'),
        c_NFT_addr);
    final c_Lock = DeployedContract(
        ContractAbi.fromJson(
            abi_contractLotteryLock.toString(), 'LockupLikepoint'),
        c_lock_addr);
    final c_erc20 = DeployedContract(
        ContractAbi.fromJson(abi_erc20.toString(), 'LIKEPOINT'), c_erc20_addr);
    final c_lottery = DeployedContract(
        ContractAbi.fromJson(abi_lottery.toString(), 'LotteryLikepoint'),
        lottery_addr);

    final functionCall = c_lottery.function('getRewardView');
    final balanceLikepoint = await client.call(
        contract: c_lottery, function: functionCall, params: [BigInt.from(i)]);
    var newBalance = EtherAmount.inWei(balanceLikepoint.first as BigInt);
    return newBalance.getValueInUnit(EtherUnit.ether);
  }

  @override
  Future<String> multiClaim(
      {required String pk, required List<dynamic> numberSet}) async {
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    final credentials = await client.credentialsFromPrivateKey(pk);
    final ownAddress = await credentials.extractAddress();
    final c_NFT = DeployedContract(
        ContractAbi.fromJson(abi_contractNFT.toString(), 'LotteryNFT'),
        c_NFT_addr);
    final c_Lock = DeployedContract(
        ContractAbi.fromJson(
            abi_contractLotteryLock.toString(), 'LockupLikepoint'),
        c_lock_addr);
    final c_erc20 = DeployedContract(
        ContractAbi.fromJson(abi_erc20.toString(), 'LIKEPOINT'), c_erc20_addr);
    final c_lottery = DeployedContract(
        ContractAbi.fromJson(abi_lottery.toString(), 'LotteryLikepoint'),
        lottery_addr);

    final functionCall = c_lottery.function('multiClaim');
    final transaction = await client.sendTransaction(
      credentials,
      Transaction.callContract(
        from: ownAddress,
        contract: c_lottery,
        function: functionCall,
        maxGas: gasClaim,
        gasPrice: EtherAmount.inWei(BigInt.from(10 * 1e9)),
        parameters: [numberSet],
      ),
      chainId: chainId,
    );
    return transaction;
  }

  @override
  Future<List<dynamic>> timelockhold(
      {required String address, required int i}) async {
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });

    final c_NFT = DeployedContract(
        ContractAbi.fromJson(abi_contractNFT.toString(), 'LotteryNFT'),
        c_NFT_addr);
    final c_Lock = DeployedContract(
        ContractAbi.fromJson(
            abi_contractLotteryLock.toString(), 'LockupLikepoint'),
        c_lock_addr);
    final c_erc20 = DeployedContract(
        ContractAbi.fromJson(abi_erc20.toString(), 'LIKEPOINT'), c_erc20_addr);
    final c_lottery = DeployedContract(
        ContractAbi.fromJson(abi_lottery.toString(), 'LotteryLikepoint'),
        lottery_addr);

    final functionCall = c_Lock.function('timelockhold');
    final balanceLikepoint = await client.call(
        contract: c_Lock,
        function: functionCall,
        params: [EthereumAddress.fromHex(address), BigInt.from(i)]);
    return balanceLikepoint;
  }

  @override
  Future<String> withdrawLockup(
      {required String pk, required amount, required int i}) async {
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    final credentials = await client.credentialsFromPrivateKey(pk);
    final ownAddress = await credentials.extractAddress();
    final c_NFT = DeployedContract(
        ContractAbi.fromJson(abi_contractNFT.toString(), 'LotteryNFT'),
        c_NFT_addr);
    final c_Lock = DeployedContract(
        ContractAbi.fromJson(
            abi_contractLotteryLock.toString(), 'LockupLikepoint'),
        c_lock_addr);
    final c_erc20 = DeployedContract(
        ContractAbi.fromJson(abi_erc20.toString(), 'LIKEPOINT'), c_erc20_addr);
    final c_lottery = DeployedContract(
        ContractAbi.fromJson(abi_lottery.toString(), 'LotteryLikepoint'),
        lottery_addr);
    final amountWithdraw =
        EtherAmount.fromUnitAndValue(EtherUnit.ether, amount.toString());
    final functionCall = c_Lock.function('withdrawLockup');

    final transaction = await client.sendTransaction(
      credentials,
      Transaction.callContract(
        from: ownAddress,
        contract: c_Lock,
        function: functionCall,
        maxGas: gasClaim,
        gasPrice: EtherAmount.inWei(BigInt.from(10 * 1e9)),
        parameters: [amountWithdraw, BigInt.from(i)],
      ),
      chainId: chainId,
    );
    return transaction;
  }

  @override
  Future<num> issueIndex() async {
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });

    final c_NFT = DeployedContract(
        ContractAbi.fromJson(abi_contractNFT.toString(), 'LotteryNFT'),
        c_NFT_addr);
    final c_Lock = DeployedContract(
        ContractAbi.fromJson(
            abi_contractLotteryLock.toString(), 'LockupLikepoint'),
        c_lock_addr);
    final c_erc20 = DeployedContract(
        ContractAbi.fromJson(abi_erc20.toString(), 'LIKEPOINT'), c_erc20_addr);
    final c_lottery = DeployedContract(
        ContractAbi.fromJson(abi_lottery.toString(), 'LotteryLikepoint'),
        lottery_addr);

    final functionCall = c_lottery.function('issueIndex');
    final balanceLikepoint = await client
        .call(contract: c_lottery, function: functionCall, params: []);
    var newBalance = EtherAmount.inWei(balanceLikepoint.first as BigInt);
    return newBalance.getValueInUnit(EtherUnit.wei);
  }

  @override
  Future<num> getAmountLottery({required String address}) async {
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });

    final c_NFT = DeployedContract(
        ContractAbi.fromJson(abi_contractNFT.toString(), 'LotteryNFT'),
        c_NFT_addr);
    final c_Lock = DeployedContract(
        ContractAbi.fromJson(
            abi_contractLotteryLock.toString(), 'LockupLikepoint'),
        c_lock_addr);
    final c_erc20 = DeployedContract(
        ContractAbi.fromJson(abi_erc20.toString(), 'LIKEPOINT'), c_erc20_addr);
    final c_lottery = DeployedContract(
        ContractAbi.fromJson(abi_lottery.toString(), 'LotteryLikepoint'),
        lottery_addr);

    final functionCall = c_Lock.function('getAmountLottery');
    final balanceLikepoint = await client.call(
        contract: c_Lock,
        function: functionCall,
        params: [EthereumAddress.fromHex(address)]);
    var newBalance = EtherAmount.inWei(balanceLikepoint.first as BigInt);
    return newBalance.getValueInUnit(EtherUnit.wei);
  }

  @override
  Future<num> countTicket(
      {required String address, required int issueIndex}) async {
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });

    final c_NFT = DeployedContract(
        ContractAbi.fromJson(abi_contractNFT.toString(), 'LotteryNFT'),
        c_NFT_addr);
    final c_Lock = DeployedContract(
        ContractAbi.fromJson(
            abi_contractLotteryLock.toString(), 'LockupLikepoint'),
        c_lock_addr);
    final c_erc20 = DeployedContract(
        ContractAbi.fromJson(abi_erc20.toString(), 'LIKEPOINT'), c_erc20_addr);
    final c_lottery = DeployedContract(
        ContractAbi.fromJson(abi_lottery.toString(), 'LotteryLikepoint'),
        lottery_addr);

    final functionCall = c_lottery.function('countTicket');
    final balanceLikepoint = await client.call(
        contract: c_lottery,
        function: functionCall,
        params: [EthereumAddress.fromHex(address), BigInt.from(issueIndex)]);
    var newBalance = EtherAmount.inWei(balanceLikepoint.first as BigInt);
    // print(newBalance.getInWei);
    return newBalance.getValueInUnit(EtherUnit.wei);
  }

  @override
  Future<num> ERC20balanceOf({required String address}) async {
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });

    final c_NFT = DeployedContract(
        ContractAbi.fromJson(abi_contractNFT.toString(), 'LotteryNFT'),
        c_NFT_addr);
    final c_Lock = DeployedContract(
        ContractAbi.fromJson(
            abi_contractLotteryLock.toString(), 'LockupLikepoint'),
        c_lock_addr);
    final c_erc20 = DeployedContract(
        ContractAbi.fromJson(abi_erc20.toString(), 'LIKEPOINT'), c_erc20_addr);
    final c_lottery = DeployedContract(
        ContractAbi.fromJson(abi_lottery.toString(), 'LotteryLikepoint'),
        lottery_addr);

    final functionCall = c_erc20.function('balanceOf');
    final balanceLikepoint = await client.call(
        contract: c_erc20,
        function: functionCall,
        params: [EthereumAddress.fromHex(address)]);
    var newBalance = EtherAmount.inWei(balanceLikepoint.first as BigInt);
    return newBalance.getValueInUnit(EtherUnit.ether);
  }

  @override
  Future<String> multiBuyLock(
      {required String pk, required List<dynamic> ticketNumber}) async {
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    final credentials = await client.credentialsFromPrivateKey(pk);
    final ownAddress = await credentials.extractAddress();
    int nonce = await client.getTransactionCount(ownAddress);
    final c_NFT = DeployedContract(
        ContractAbi.fromJson(abi_contractNFT.toString(), 'LotteryNFT'),
        c_NFT_addr);
    final c_Lock = DeployedContract(
        ContractAbi.fromJson(
            abi_contractLotteryLock.toString(), 'LockupLikepoint'),
        c_lock_addr);
    final c_erc20 = DeployedContract(
        ContractAbi.fromJson(abi_erc20.toString(), 'LIKEPOINT'), c_erc20_addr);
    final c_lottery = DeployedContract(
        ContractAbi.fromJson(abi_lottery.toString(), 'LotteryLikepoint'),
        lottery_addr);
    // EtherAmount.inWei(balanceLikepoint.first as BigInt)
    final amount =
        EtherAmount.fromUnitAndValue(EtherUnit.ether, int.parse('1'));
    final functionCall = c_lottery.function('multiBuyLock');
    print(amount);
    int two = 1;
    final transaction = await client.sendTransaction(
      credentials,
      Transaction.callContract(
          from: ownAddress,
          contract: c_lottery,
          function: functionCall,
          maxGas: gasClaim,
          nonce: nonce,
          gasPrice: EtherAmount.inWei(BigInt.from(10 * 1e9)),
          parameters: [amount.getInWei, ticketNumber]),
      chainId: chainId,
    );
    return transaction;
  }

  @override
  Future<String> depositLockup(
      {required String pk, required String amount, required int option}) async {
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    final credentials = await client.credentialsFromPrivateKey(pk);
    final ownAddress = await credentials.extractAddress();

    final c_NFT = DeployedContract(
        ContractAbi.fromJson(abi_contractNFT.toString(), 'LotteryNFT'),
        c_NFT_addr);
    final c_Lock = DeployedContract(
        ContractAbi.fromJson(
            abi_contractLotteryLock.toString(), 'LockupLikepoint'),
        c_lock_addr);
    final c_erc20 = DeployedContract(
        ContractAbi.fromJson(abi_erc20.toString(), 'LIKEPOINT'), c_erc20_addr);
    final c_lottery = DeployedContract(
        ContractAbi.fromJson(abi_lottery.toString(), 'LotteryLikepoint'),
        lottery_addr);
    final amountDeposit = EtherAmount.fromUnitAndValue(EtherUnit.ether, amount);
    final functionCall = c_Lock.function('depositLockup');

    final transaction = await client.sendTransaction(
      credentials,
      Transaction.callContract(
        from: ownAddress,
        contract: c_Lock,
        function: functionCall,
        maxGas: gasClaim,
        gasPrice: EtherAmount.inWei(BigInt.from(10 * 1e9)),
        parameters: [amountDeposit, BigInt.from(option)],
      ),
      chainId: chainId,
    );
    return transaction;
  }

  @override
  Future<String> approve({required String pk, required String address}) async {
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    final credentials = await client.credentialsFromPrivateKey(pk);
    final ownAddress = await credentials.extractAddress();
    int nonce = await client.getTransactionCount(ownAddress);
    print("nonce" + nonce.toString());
    final c_NFT = DeployedContract(
        ContractAbi.fromJson(abi_contractNFT.toString(), 'LotteryNFT'),
        c_NFT_addr);
    final c_Lock = DeployedContract(
        ContractAbi.fromJson(
            abi_contractLotteryLock.toString(), 'LockupLikepoint'),
        c_lock_addr);
    final c_erc20 = DeployedContract(
        ContractAbi.fromJson(abi_erc20.toString(), 'LIKEPOINT'), c_erc20_addr);
    final c_lottery = DeployedContract(
        ContractAbi.fromJson(abi_lottery.toString(), 'LotteryLikepoint'),
        lottery_addr);
    // final amountDeposit = EtherAmount.fromUnitAndValue(EtherUnit.ether, amount);
    final functionCall = c_erc20.function('approve');

    final transaction = await client.sendTransaction(
      credentials,
      Transaction.callContract(
        from: ownAddress,
        contract: c_erc20,
        function: functionCall,
        maxGas: gasClaim,
        nonce: nonce,
        gasPrice: EtherAmount.inWei(BigInt.from(10 * 1e9)),
        parameters: [
          EthereumAddress.fromHex(address),
          EtherAmount.inWei(BigInt.parse(
                  '0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff'))
              .getInWei
        ],
      ),
      chainId: chainId,
    );
    print(transaction);
    return transaction;
  }

  @override
  Future<num> allowance(
      {required String addressOwner, required String addressUsed}) async {
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });

    final c_NFT = DeployedContract(
        ContractAbi.fromJson(abi_contractNFT.toString(), 'LotteryNFT'),
        c_NFT_addr);
    final c_Lock = DeployedContract(
        ContractAbi.fromJson(
            abi_contractLotteryLock.toString(), 'LockupLikepoint'),
        c_lock_addr);
    final c_erc20 = DeployedContract(
        ContractAbi.fromJson(abi_erc20.toString(), 'LIKEPOINT'), c_erc20_addr);
    final c_lottery = DeployedContract(
        ContractAbi.fromJson(abi_lottery.toString(), 'LotteryLikepoint'),
        lottery_addr);

    final functionCall = c_erc20.function('allowance');
    final balanceLikepoint = await client.call(
        contract: c_erc20,
        function: functionCall,
        params: [
          EthereumAddress.fromHex(addressOwner),
          EthereumAddress.fromHex(addressUsed)
        ]);
    var newBalance = EtherAmount.inWei(balanceLikepoint.first as BigInt);
    return newBalance.getValueInUnit(EtherUnit.ether);
  }

  @override
  Future<num> historyNumbers(
      {required int index, required int position}) async {
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });

    final c_NFT = DeployedContract(
        ContractAbi.fromJson(abi_contractNFT.toString(), 'LotteryNFT'),
        c_NFT_addr);
    final c_Lock = DeployedContract(
        ContractAbi.fromJson(
            abi_contractLotteryLock.toString(), 'LockupLikepoint'),
        c_lock_addr);
    final c_erc20 = DeployedContract(
        ContractAbi.fromJson(abi_erc20.toString(), 'LIKEPOINT'), c_erc20_addr);
    final c_lottery = DeployedContract(
        ContractAbi.fromJson(abi_lottery.toString(), 'LotteryLikepoint'),
        lottery_addr);

    final functionCall = c_lottery.function('historyNumbers');
    final balanceLikepoint = await client.call(
        contract: c_lottery,
        function: functionCall,
        params: [BigInt.from(index), BigInt.from(position)]);
    var newBalance = EtherAmount.inWei(balanceLikepoint.first as BigInt);
    return newBalance.getValueInUnit(EtherUnit.wei);
  }

  @override
  Future<num> historyAmount({required int index, required int position}) async {
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });

    final c_NFT = DeployedContract(
        ContractAbi.fromJson(abi_contractNFT.toString(), 'LotteryNFT'),
        c_NFT_addr);
    final c_Lock = DeployedContract(
        ContractAbi.fromJson(
            abi_contractLotteryLock.toString(), 'LockupLikepoint'),
        c_lock_addr);
    final c_erc20 = DeployedContract(
        ContractAbi.fromJson(abi_erc20.toString(), 'LIKEPOINT'), c_erc20_addr);
    final c_lottery = DeployedContract(
        ContractAbi.fromJson(abi_lottery.toString(), 'LotteryLikepoint'),
        lottery_addr);

    final functionCall = c_lottery.function('historyAmount');
    final balanceLikepoint = await client.call(
        contract: c_lottery,
        function: functionCall,
        params: [BigInt.from(index), BigInt.from(position)]);
    var newBalance = EtherAmount.inWei(balanceLikepoint.first as BigInt);
    return newBalance.getValueInUnit(EtherUnit.ether);
  }

  @override
  Future<num> allocation({required int index}) async {
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });

    final c_NFT = DeployedContract(
        ContractAbi.fromJson(abi_contractNFT.toString(), 'LotteryNFT'),
        c_NFT_addr);
    final c_Lock = DeployedContract(
        ContractAbi.fromJson(
            abi_contractLotteryLock.toString(), 'LockupLikepoint'),
        c_lock_addr);
    final c_erc20 = DeployedContract(
        ContractAbi.fromJson(abi_erc20.toString(), 'LIKEPOINT'), c_erc20_addr);
    final c_lottery = DeployedContract(
        ContractAbi.fromJson(abi_lottery.toString(), 'LotteryLikepoint'),
        lottery_addr);

    final functionCall = c_lottery.function('allocation');
    final balanceLikepoint = await client.call(
        contract: c_lottery,
        function: functionCall,
        params: [BigInt.from(index)]);
    var newBalance = EtherAmount.inWei(balanceLikepoint.first as BigInt);
    return newBalance.getValueInUnit(EtherUnit.wei);
  }

  @override
  Future<num> getOptionLength() async {
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });

    final c_NFT = DeployedContract(
        ContractAbi.fromJson(abi_contractNFT.toString(), 'LotteryNFT'),
        c_NFT_addr);
    final c_Lock = DeployedContract(
        ContractAbi.fromJson(
            abi_contractLotteryLock.toString(), 'LockupLikepoint'),
        c_lock_addr);
    final c_erc20 = DeployedContract(
        ContractAbi.fromJson(abi_erc20.toString(), 'LIKEPOINT'), c_erc20_addr);
    final c_lottery = DeployedContract(
        ContractAbi.fromJson(abi_lottery.toString(), 'LotteryLikepoint'),
        lottery_addr);

    final functionCall = c_Lock.function('getOptionLength');
    final balanceLikepoint =
        await client.call(contract: c_Lock, function: functionCall, params: []);
    var newBalance = EtherAmount.inWei(balanceLikepoint.first as BigInt);
    return newBalance.getValueInUnit(EtherUnit.wei);
  }

  @override
  Future<num> option({required int option}) async {
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });

    final c_NFT = DeployedContract(
        ContractAbi.fromJson(abi_contractNFT.toString(), 'LotteryNFT'),
        c_NFT_addr);
    final c_Lock = DeployedContract(
        ContractAbi.fromJson(
            abi_contractLotteryLock.toString(), 'LockupLikepoint'),
        c_lock_addr);
    final c_erc20 = DeployedContract(
        ContractAbi.fromJson(abi_erc20.toString(), 'LIKEPOINT'), c_erc20_addr);
    final c_lottery = DeployedContract(
        ContractAbi.fromJson(abi_lottery.toString(), 'LotteryLikepoint'),
        lottery_addr);

    final functionCall = c_Lock.function('option');
    final balanceLikepoint = await client.call(
        contract: c_Lock,
        function: functionCall,
        params: [BigInt.from(option)]);
    var newBalance = EtherAmount.inWei(balanceLikepoint.first as BigInt);
    return newBalance.getValueInUnit(EtherUnit.wei);
  }

  @override
  Future<num> totalAmount() async {
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });

    final c_NFT = DeployedContract(
        ContractAbi.fromJson(abi_contractNFT.toString(), 'LotteryNFT'),
        c_NFT_addr);
    final c_Lock = DeployedContract(
        ContractAbi.fromJson(
            abi_contractLotteryLock.toString(), 'LockupLikepoint'),
        c_lock_addr);
    final c_erc20 = DeployedContract(
        ContractAbi.fromJson(abi_erc20.toString(), 'LIKEPOINT'), c_erc20_addr);
    final c_lottery = DeployedContract(
        ContractAbi.fromJson(abi_lottery.toString(), 'LotteryLikepoint'),
        lottery_addr);

    final functionCall = c_lottery.function('totalAmount');
    final balanceLikepoint = await client
        .call(contract: c_lottery, function: functionCall, params: []);
    var newBalance = EtherAmount.inWei(balanceLikepoint.first as BigInt);
    return newBalance.getValueInUnit(EtherUnit.ether);
  }
}
