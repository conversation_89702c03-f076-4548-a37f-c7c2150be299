import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/animationPage.dart';
import 'package:likewallet/libraryman/setPin.dart';
import 'package:likewallet/screen_util.dart';

import 'dart:convert';

import 'package:fluttertoast/fluttertoast.dart';
import 'package:likewallet/animationPage.dart';
import 'package:likewallet/libraryman/custom_loading.dart';

import 'auth.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:likewallet/screen/index.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:likewallet/login/confirmOTP.dart';
import 'package:likewallet/login/sing_in.dart';
import 'package:likewallet/screen/choice_user.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:flutter/cupertino.dart';
import 'package:likewallet/libraryman/app_local.dart';

import 'package:likewallet/libraryman/setPin.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:likewallet/login/sing_in.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:likewallet/libraryman/crypto.dart';

import 'package:likewallet/app_config.dart';
import 'package:http/http.dart' as http;

class SecretPass extends StatefulWidget {
  SecretPass(
      {this.roundSMS,
      this.codeVerify,
      this.phoneNumber,
      this.firstName,
      this.lastName,
      this.checkIf,
      this.refCode,
      this.numberID});

  final providerSMS? roundSMS;
  final String? checkIf;
  final String? codeVerify;
  final String? phoneNumber;
  final String? firstName;
  final String? lastName;
  final String? refCode;
  final String? numberID;

  @override
  State<StatefulWidget> createState() => new _SecretPass(
      roundSMS: roundSMS,
      codeVerify: codeVerify,
      phoneNumber: phoneNumber,
      firstName: firstName,
      lastName: lastName,
      checkIf: checkIf,
      refCode: refCode,
      numberID: numberID);
}

setToken(String _token) async {
  SharedPreferences pref = await SharedPreferences.getInstance();
  pref.setString("_token", _token);
  return true;
}

enum FormType { login, register }

class _SecretPass extends State<SecretPass> {
  _SecretPass(
      {this.roundSMS,
      this.codeVerify,
      this.phoneNumber,
      this.firstName,
      this.lastName,
      this.checkIf,
      this.refCode,
      this.numberID});

  final String? checkIf;
  final providerSMS? roundSMS;
  final String? codeVerify;
  final String? phoneNumber;
  final String? firstName;
  final String? lastName;
  final String? refCode;
  final String? numberID;

  late String keyEncrypt;
  late String ivEncrypt;
  int incorrect = 0;

  bool _saving = false;

  late CryptoEncryptInterface Encrypt;
  TextEditingController _secret = TextEditingController();
  void showShortToast(msg, Color color) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_SHORT,
        backgroundColor: color,
        textColor: Colors.white);
  }

  void showShortToastLong(msg, Color color) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_LONG,
        backgroundColor: color,
        textColor: Colors.white);
  }

  void incorrectSecret() async {
    var url = Uri.https(env.apiUrl, '/incorrectSecret');
    var response = http.post(url, body: {'phone_number': phoneNumber});
  }

  void toChoice() async {
    if (!mounted) return;
    setState(() {
      _saving = true;
    });

    final storage = new FlutterSecureStorage();
    await storage.write(key: 'secretpass', value: _secret.text);
    var url = Uri.https(env.apiUrl, '/verifySecret');

    var response = await http.post(url, body: {
      'phone_number': phoneNumber,
      'secret': _secret.text,
      'keyEncrypt': keyEncrypt,
      'ivEncrypt': ivEncrypt,
    });

    try {
      var body = json.decode(response.body);

      if (body['statusCode'] == 200) {
        if (!mounted) return;
        setState(() {
          _saving = false;
        });
        //key success
        Navigator.push(
          context,
          EnterExitRoute(
              exitPage: SING_IN(),
              enterPage: SetPin(
                refCode: refCode,
                firstName: firstName,
                lastName: lastName,
                checkIf: checkIf,
                secret: _secret.text,
                roundSMS: roundSMS,
                codeVerify: codeVerify,
                phoneNumber: phoneNumber,
                pinAgain: false,
              )),
        );
      } else {
        if (!mounted) return;
        setState(() {
          _saving = false;
        });
        //key wrong
        incorrect += 1;
        if (incorrect >= 5) {
          //show dialog
          incorrectSecret();
          showShortToast(
              AppLocalizations.of(context)!.translate('secret_notifications'),
              Colors.cyanAccent);
        } else {
          showShortToast(
              AppLocalizations.of(context)!.translate('secret_incorrect'),
              Colors.red);
        }
      }
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _saving = false;
      });
      incorrect += 1;
      if (incorrect >= 5) {
        //show dialog
        incorrectSecret();
        showShortToast(
            AppLocalizations.of(context)!.translate('secret_notifications'),
            Colors.cyanAccent);
      } else {
        showShortToast(
            AppLocalizations.of(context)!.translate('secret_incorrect'),
            Colors.red);
      }

      //key wrong
    }
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    Encrypt = CryptoEncrypt();
    Encrypt.generateKey().then((data) {
      //can generate key to encrypt
      if (data) {
        Encrypt.getKeyEncrypt().then((dataKey) {
          keyEncrypt = dataKey[0];
          ivEncrypt = dataKey[1];
        });
      } else {
        //something is wrong !
      }
    });
  }

  setLogin() {}
  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return ModalProgressHUD(

        opacity: 0.1,
        inAsyncCall: _saving,
        progressIndicator: CustomLoading(),
        child: new Scaffold(
            resizeToAvoidBottomInset: false,
            // resizeToAvoidBottomPadding: false,
            backgroundColor: Colors.black,
            body: GestureDetector(
                onTap: () {
                  FocusScopeNode currentFocus = FocusScope.of(context);
                  if (!currentFocus.hasPrimaryFocus) {
                    currentFocus.unfocus();
                  }
                },
                child: Stack(
                  alignment: Alignment.center,
                  children: <Widget>[
                    background_image(context),
                    Positioned(
                      left: mediaQuery(context, 'height', 0),
                      top: mediaQuery(context, 'height', 120),
                      child: backNav(),
                    ),
                    Positioned(
                        top: mediaQuery(context, 'height', 302),
                        child: _title()),
                    Positioned(
                        top: mediaQuery(context, 'height', 458),
                        child: _input()),
                    Positioned(
                        top: mediaQuery(context, 'height', 900),
                        child: _button()),
                    Positioned(
                        top: mediaQuery(context, 'height', 1200),
                        child: _title2()),
                    Positioned(
                        top: mediaQuery(context, 'height', 1500),
                        child: _forget()),
                  ],
                ))));
  }

  Widget backNav() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Container(
          padding: EdgeInsets.only(
            left: mediaQuery(context, 'width', 35),
          ),
          width: MediaQuery.of(context).size.width / 4,
          child: backButton(context, LikeWalletAppTheme.gray1),
        ),
      ],

//        color: Colors.green,
    );
  }
  Widget _title() {
    return new Text(
      AppLocalizations.of(context)!.translate('secret_title_second'),
      textAlign: TextAlign.center,
      style: TextStyle(
          fontFamily: AppLocalizations.of(context)!.translate('font1'),
          color: LikeWalletAppTheme.gray1,
          fontSize: mediaQuery(context, 'height', 40)),
    );
  }

  Widget _input() {
    return Container(
      decoration: BoxDecoration(
        color: LikeWalletAppTheme.bule2,
        border: Border.all(
          color: LikeWalletAppTheme.bule1,
          width: mediaQuery(context, 'height', 0.3),
        ),
        borderRadius: BorderRadius.all(Radius.circular(5.0)),
      ),
      alignment: Alignment.center,
      height: mediaQuery(context, 'height', 156),
      width: mediaQuery(context, 'width', 930),
//                color: Colors.blue,
      child: TextFormField(
        controller: _secret,
        obscureText: true,
        style: TextStyle(
            fontSize: mediaQuery(context, 'height', 47),
            fontFamily: AppLocalizations.of(context)!.translate('font1'),
            color: LikeWalletAppTheme.white),
        decoration: InputDecoration(
//                  focusedBorder: OutlineInputBorder(
//                      borderSide: BorderSide(
//                          color: LikeWalletAppTheme.bule1,
//                          width: mediaQuery(context, 'width', 1))),
          contentPadding: EdgeInsets.only(
            left: MediaQuery.of(context).size.width * 0.03,
          ),
          border: InputBorder.none,
//                  enabledBorder: OutlineInputBorder(
//                      borderSide: BorderSide(
//                          width: mediaQuery(context, 'width', 0.3),
//                          color: LikeWalletAppTheme.bule1)),
          hoverColor: Colors.white,
          disabledBorder: InputBorder.none,
          focusColor: Colors.white,
          alignLabelWithHint: true,
          fillColor: Colors.white,
        ),
        keyboardType: TextInputType.text,
      ),
    );
  }

  Widget _button() {
    return ButtonTheme(
      minWidth: mediaQuery(context, 'width', 439.14),
      height: mediaQuery(context, 'height', 132),
      child: TextButton(
        style: ButtonStyle(
          shape: MaterialStateProperty.all<RoundedRectangleBorder>(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(5.0),
            ),
          ),
          backgroundColor: MaterialStateProperty.resolveWith<Color>(
                (Set<MaterialState> states) {
              if (states.contains(MaterialState.disabled)) {
                return LikeWalletAppTheme.bule1; // Disabled color
              }
              return LikeWalletAppTheme.bule1; // Regular color
            },
          ),
        ),
        onPressed: () {
          FocusScopeNode currentFocus = FocusScope.of(context);
          if (!currentFocus.hasPrimaryFocus) {
            currentFocus.unfocus();
          }
          toChoice();
        },
        child: Text(
          AppLocalizations.of(context)!.translate('secret_button'),
          style: TextStyle(
            fontFamily: AppLocalizations.of(context)!.translate('font'),
            fontSize: mediaQuery(context, 'height', 51),
            color: Colors.black,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _title2() {
    return new Text(
      AppLocalizations.of(context)!.translate('secret_title2'),
      textAlign: TextAlign.center,
      style: TextStyle(
          fontFamily: AppLocalizations.of(context)!.translate('font1'),
          color: LikeWalletAppTheme.gray1,
          fontSize: mediaQuery(context, 'height', 40)),
    );
  }
  Widget _forget() {
    return new Row(
      children: <Widget>[
        IconButton(
            icon: Icon(
              Icons.vpn_key,
              color: LikeWalletAppTheme.gray1,
            ),
            onPressed: () {
              forgetSecret();
            }),
        GestureDetector(
          onTap: () {
            forgetSecret();
          },
          child: new Text(
            AppLocalizations.of(context)!.translate('forget_secret'),
            textAlign: TextAlign.center,
            style: TextStyle(
                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                color: LikeWalletAppTheme.gray1,
                fontSize: mediaQuery(context, 'height', 40)),
          ),
        )
      ],
    );
  }

  forgetSecret() {
    incorrectSecret();
    showShortToastLong(
        AppLocalizations.of(context)!.translate('forget_secret_notify'),
        Colors.cyan);
  }
}
