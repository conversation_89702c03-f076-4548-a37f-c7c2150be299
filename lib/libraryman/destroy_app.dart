import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:likewallet/libraryman/auth.dart';

Future<bool> destroyApp() async {
  BaseAuth auth;
  auth = new Auth();
  final storage = new FlutterSecureStorage();
  SharedPreferences pref = await SharedPreferences.getInstance();
  //save old lang
  final lang = pref.getString('language_code');
  bool check = await pref.clear();
  await storage.deleteAll();
  pref.setString('language_code', lang ?? 'en');
  await auth.signOut();
  return true;
}
