
abstract class SetFormat {
  String formatBank(String accountNumber);
  String formatMobile(String phoneNumber);
  String formatIDCrad(String phoneNumber);
}

class SetFormatString implements SetFormat {
  @override
  String formatBank(String accountNumber) {
    print('set format');
    try {
      var first = accountNumber.substring(0, 3);
      var middle = accountNumber.substring(3, 4);
      var middle2 = accountNumber.substring(4, 9);
      var last = accountNumber.substring(9);
      var formatNew = first + '-' + middle + '-' + middle2 + '-' + last;
      print(formatNew);
      return formatNew;
    } catch (e) {
      print(e);
      return "bank account not available.";
    }
  }

  @override
  String formatMobile(String phoneNumber) {
    print('set format');
    try {
      var first = phoneNumber.substring(0, 3);
      var last = phoneNumber.substring(3);
      var formatNew = first + '-' + last;
      print(formatNew);
      return formatNew;
    } catch (e) {
      print(e);
      return "bank account not available.";
    }
  }

  @override
  String formatIDCrad(String phoneNumber) {
    print('set format');
    try {
      var first = phoneNumber.substring(0, 1);
      var second = phoneNumber.substring(1, 5);
      var third = phoneNumber.substring(5, 10);
      var fourth = phoneNumber.substring(10, 12);
      var fifth = phoneNumber.substring(12, 13);
      var formatNew =
          first + ' ' + second + ' ' + third + ' ' + fourth + ' ' + fifth;
      print(formatNew);
      return formatNew;
    } catch (e) {
      print(e);
      return "bank account not available.";
    }
  }
}

