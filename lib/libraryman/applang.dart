import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AppLanguage extends ChangeNotifier {
  Locale _appLocale = Locale('en');

  Locale get appLocal => _appLocale;
  fetchLocale() async {
    var prefs = await SharedPreferences.getInstance();
    if (prefs.getString('language_code') == null) {
      _appLocale = Locale('en');

      return Null;
    }
    _appLocale = Locale(prefs.getString('language_code') ?? 'en');
    return Null;
  }


  void changeLanguage(Locale type) async {
    var prefs = await SharedPreferences.getInstance();

    if (_appLocale == type) {
      return;
    }
    if (type == Locale("th")) {
      _appLocale = Locale("th");
      await prefs.setString('language_code', 'th');
      await prefs.setString('countryCode', '');
    } else if(type == Locale("en")) {
      _appLocale = Locale("en");
      await prefs.setString('language_code', 'en');
      await prefs.setString('countryCode', 'US');
    } else if(type == Locale("vi")) {
      _appLocale = Locale("vi");
      await prefs.setString('language_code', 'vi');
      await prefs.setString('countryCode', '');
    } else if(type == Locale("km")) {
      _appLocale = Locale("km");
      await prefs.setString('language_code', 'km');
      await prefs.setString('countryCode', '');
    } else if(type == Locale("lo")) {
      _appLocale = Locale("lo");
      await prefs.setString('language_code', 'lo');
      await prefs.setString('countryCode', '');
    } else{
      _appLocale = Locale("en");
      await prefs.setString('language_code', 'en');
      await prefs.setString('countryCode', 'US');
    }
    notifyListeners();
  }
}