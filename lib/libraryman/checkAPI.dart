import 'dart:async';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:likewallet/app_config.dart';
import 'package:http/http.dart' as http;

abstract class CheckAPI {
  Future<bool> checkActive();
}

class APIChecker implements CheckAPI {
  @override
  Future<bool> checkActive() async {
    var url = Uri.https(env.apiUrl, '/checkActive');
    try {
      var body = await http.get(url).timeout(const Duration(seconds: 3));
      return true;
    } catch (e) {
      return false;
    }
  }
}
