//
// import 'dart:io';
//
// import 'package:flutter/material.dart';
//
// import 'package:ads/ads.dart';
// import 'package:firebase_admob/firebase_admob.dart';
//
// class AppAds {
//   static Ads _ads;
//
//   static final String appId = Platform.isAndroid
//       ? 'ca-app-pub-6895644642953128~2650754504'
//       : 'ca-app-pub-6895644642953128~8449876092';
//
//   static final String videoUnitId = Platform.isAndroid
//       ? 'ca-app-pub-6895644642953128/4709941638'
//       : 'ca-app-pub-6895644642953128/9379814383';
//
//   /// Assign a listener.
//   static MobileAdListener _eventListener = (MobileAdEvent event) {
//     if (event == MobileAdEvent.clicked) {
//       print("_eventListener: The opened ad is clicked on.");
//     }
//   };
//
//   static void showVideo({State<StatefulWidget> state, videoListener}) =>
//       _ads?.showVideoAd(
//         adUnitId: videoUnitId,
//         state: state,
//
//         keywords: [
//           'game',
//           'ethereum',
//           'bitcoin',
//           'isuzu',
//           'pubg',
//           'Insurance',
//           'Loans',
//           'Mortgage',
//           'Attorney',
//           'Credit',
//           'Lawyer',
//           'Donate',
//           'Degree',
//           'Hosting',
//           'Claim',
//           'Conference Call',
//           'Trading',
//           'Software',
//           'Recovery',
//           'Transfer',
//           'Gas/Electicity',
//           'Classes',
//           'Rehab',
//           'Treatment',
//           'Cord Blood',
//           'น้ำหอม',
//           'กาแฟ',
//           'เกม',
//           'ร้านขายเสื้อผ้า',
//           'ร้านค้าปลีก',
//           'ไฟฉาย',
//           'โทรศัพท์',
//           'เงินกู้',
//           'เงินด่วน',
//           'ร้านอาหาร',
//           'เพลง',
//           'covid-19',
//           'ร้านนั่งชิลด์'
//         ],
//
//         contentUrl: 'https://www.ibm.com',
//         childDirected: true,
// //      testDevices: ["Redmi Note 6 Pro"],
//         testDevices: null,
//         testing: false,
//         listener: videoListener,
//       );
//   static void showBanner(
//           {String adUnitId,
//           AdSize size,
//           List<String> keywords,
//           String contentUrl,
//           bool childDirected,
//           List<String> testDevices,
//           bool testing,
//           MobileAdListener listener,
//           State state,
//           double anchorOffset,
//           AnchorType anchorType}) =>
//       _ads?.showBannerAd(
//           adUnitId: adUnitId,
//           size: size,
//           keywords: keywords,
//           contentUrl: contentUrl,
//           childDirected: childDirected,
//           testDevices: testDevices,
//           testing: testing,
//           listener: listener,
//           state: state,
//           anchorOffset: anchorOffset,
//           anchorType: anchorType);
//
//   static void hideBanner() => _ads?.closeBannerAd();
//
//   /// Call this static function in your State object's initState() function.
//   static void init() => _ads ??= Ads(appId);
//
//   /// Remember to call this in the State object's dispose() function.
//   static void dispose() => _ads?.dispose();
// }
