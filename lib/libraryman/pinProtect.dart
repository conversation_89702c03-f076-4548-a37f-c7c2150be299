import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:likewallet/animationPage.dart';
import 'package:flutter/services.dart';
import 'package:likewallet/screen/tabslide.dart';
import 'package:likewallet/tabslide/backupKey.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/setmodel/news_modal_model.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:likewallet/screen/home.dart';
import 'package:local_auth/local_auth.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:likewallet/libraryman/custom_loading.dart';

import 'package:likewallet/notifications/show_news.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../passcode_screen/keyboard.dart';
import '../passcode_screen/passcode_screen.dart';

class PinProtect extends StatefulWidget {
  PinProtect({this.redirectPage});
  final String? redirectPage;
  @override
  _PinProtect createState() => _PinProtect(redirectPage: redirectPage);
}

class _PinProtect extends State<PinProtect> {
  _PinProtect({this.redirectPage});
  final String? redirectPage;

  final StreamController<bool> _verificationNotifier =
  StreamController<bool>.broadcast();

  bool _saving = false;
  //biometric
  final LocalAuthentication localAuth = LocalAuthentication();
  late bool _canCheckBiometrics;
  late List<BiometricType> _availableBiometrics;
  String _authorized = 'Not Authorized';
  bool didAuthenticate = false;
  late String pinProtect;

  late SharedPreferences prefs;
  List<NewsModal> urlNews = [];

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return ModalProgressHUD(
      opacity: 0.1,
      child: Scaffold(
//      backgroundColor: Colors.black.withOpacity(0.1),
        body: WillPopScope(
          onWillPop: () async {
            return true;
          },
          child: Center(
            child: pinVerify(),
          ),
        ),
      ),
      inAsyncCall: _saving,
      progressIndicator: CustomLoading(),
    );
  }

  ///pass_code
  @override
  void initState() {
    super.initState();
    //TODO ข่าวสารที่คิดว่าไม่ได้ใข้งาน
    // Future.delayed(Duration.zero, () {
    //   var now = new DateTime.now();
    //   FirebaseFirestore.instance
    //       .collection('notificationNews')
    //       .where('status', isEqualTo: 'active')
    //       .snapshots()
    //       .listen((data) {
    //     var arrayData = [];
    //     for (var doc in data.docs) {
    //       if (int.parse(doc.data()["startAt"] != ''
    //                   ? doc.data()["startAt"].toString()
    //                   : '0') <=
    //               now.millisecondsSinceEpoch / 1000 &&
    //           int.parse(doc.data()["endAt"] != ''
    //                   ? doc.data()["endAt"].toString()
    //                   : '0') >=
    //               now.millisecondsSinceEpoch / 1000) {
    //         print(doc.data());
    //         var jsonData = {
    //           'url': doc.data()["url"],
    //           'path': doc.data()["path"],
    //           'title': doc.data()["title"],
    //           'id': doc.id
    //         };
    //         arrayData.add(jsonData);
    //       }
    //     }
    //     if (arrayData.length > 0) {
    //       final bodyInCast = arrayData.cast<Map<String, dynamic>>();
    //       urlNews = bodyInCast
    //           .map<NewsModal>((json) => NewsModal.fromJson(json))
    //           .toList();
    //     }
    //   });

    print(redirectPage);
    // });
    setInit();
  }

  setInit() async {
    prefs = await SharedPreferences.getInstance();
    pinProtect = prefs.getString('pinProtect') ?? '';
    bool canCheckBiometrics = await localAuth.canCheckBiometrics;

    bool checkScan = prefs.getBool('touchID') ?? true;
    if (checkScan == false) {
      //disable touch id scan

      print('here');
    } else {
      if (canCheckBiometrics) {
        prefs.setBool('touchID', true);
        // const iosStrings = const IOSAuthMessages(
        //     cancelButton: 'cancel',
        //     goToSettingsButton: 'settings',
        //     goToSettingsDescription: 'Please set up your Touch ID.',
        //     lockOut: 'Please reenable your Touch ID');

        List<BiometricType> availableBiometrics =
        await localAuth.getAvailableBiometrics();
        // print(availableBiometrics);
        if (Platform.isIOS) {
          if (availableBiometrics.contains(BiometricType.face)) {
            // Face ID.
            didAuthenticate = await localAuth.authenticate(
                localizedReason: 'Please show face');
          } else if (availableBiometrics.contains(BiometricType.fingerprint)) {
            // Touch ID.
//          didAuthenticate = await localAuth.authenticateWithBiometrics(localizedReason: 'Please touch fingerprint');
            didAuthenticate = await localAuth.authenticate(
                localizedReason: 'Please touch fingerprint');
          }
        } else {
          didAuthenticate = await localAuth.authenticate(
              localizedReason: 'Please touch fingerprint');
        }

        if (didAuthenticate == false) {
          print('cancel');
        } else {
          //pass to home
          accessPass();
        }
      } else {
        //show pin
        prefs.setBool('touchID', false);
      }
    }
  }

  //verifyPin
  Widget pinVerify() {
    return PasscodeScreen(
      title: AppLocalizations.of(context)!.translate('enter_pin'),
      explain: '',
      type: 'pinProtect',
      passwordEnteredCallback: _onPasscodeEntered,
      cancelLocalizedText:
      AppLocalizations.of(context)!.translate('pin_cancel'),
      deleteLocalizedText:
      AppLocalizations.of(context)!.translate('pin_delete'),
      shouldTriggerVerification: _verificationNotifier.stream,
      backgroundColor: Colors.black.withOpacity(0.8),
      cancelCallback: _onPasscodeCancelled,
      fingerPrint: setInit,
      isValidCallback: accessPass,
      keyboardUIConfig: KeyboardUIConfig(
        digitBorderWidth: MediaQuery.of(context).size.height * 0.001,
        digitTextStyle: TextStyle(
            fontSize: MediaQuery.of(context).size.height * 0.035,
            color: Color(0xffFFFFFF)),
        digitSize: MediaQuery.of(context).size.height * 0.1,
        deleteButtonTextStyle: TextStyle(
            fontSize: MediaQuery.of(context).size.height * 0.025,
            color: Color(0xffFFFFFF)),
//
      ),
      circleUIConfig: CircleUIConfig(
//              borderColor: ,
//              fillColor: color,
          circleSize: MediaQuery.of(context).size.height * 0.03),
    );
  }

  //permission access
  accessPass() {
    setState(() {
      _saving = true;
    });
    new Future.delayed(new Duration(milliseconds: 0), () {
      if (redirectPage == 'HOME') {
//        prefs.remove('notification_news');
        if (!mounted) return;

        setState(() {
          _saving = false;
          Navigator.pushReplacement(context,
              MaterialPageRoute(builder: (context) => HomeLikewallet()));
          //TODO อาจไม่ได้ใช้งาน
          // if (urlNews.length > 0) {
          //   for (var i = 0; i < urlNews.length; i++) {
          //     List<String> olddata =
          //         prefs.getStringList('notification_news') ?? ['no'];
          //     //หากเกิน 20 ให้ลบทิ้งจะได้ไม่เปลืองพื้นที่
          //     if (olddata.length > 20) {
          //       prefs.remove('notification_news');
          //     }
          //     //ค้นหาว่ามีโฆษณานี้ขึ้นไปหรือยัง
          //     var len = olddata
          //         .where((u) => (u.contains(urlNews[i].id.toString())))
          //         .toList();
          //     print(len);
          //     print('len' + len.length.toString());
          //     //นับว่าถ้าค้นหาไม่เจอให้แสดง ads นี้
          //     if (len.length == 0) {
          //       olddata.add(urlNews[i].id.toString());
          //       prefs.setStringList('notification_news', olddata);
          //
          //       print(urlNews.length);
          //       //ป้อน ads เข้าไป
          //       showDialog(
          //         context: context,
          //         builder: (BuildContext context) => dialogContent(
          //             title: urlNews[i].title,
          //             context: context,
          //             url: urlNews[i].url,
          //             path: urlNews[i].path),
          //       );
          //     }
          //   }
          // }
        });
      } else if (redirectPage == 'TRANSFER') {
        if (!mounted) return;
        setState(() {
          _saving = false;
        });
      } else if (redirectPage == 'BACKUP_SECRETKEY') {
        if (!mounted) return;
        setState(() {
          _saving = false;
        });
        if (!mounted) return;
        setState(() {
          Navigator.pushReplacement(
              context,
              EnterExitRoute(
                enterPage: BackupSecretKey(),
                exitPage: tabslide(),
              ));
        });
      } else {
        if (Platform.isAndroid) {
          SystemNavigator.pop();
        } else {
          exit(0);
        }
      }
    });
  }

  _onPasscodeCancelled() {}
  //check match pin code
  _onPasscodeEntered(String enteredPasscode) {
    print(enteredPasscode);
    bool isValid = pinProtect == enteredPasscode;
    if (isValid) {
      localAuth.stopAuthentication();
      setState(() {
        didAuthenticate = false;
      });

      _verificationNotifier.add(isValid);
    } else {
      _verificationNotifier.add(false);
    }
  }

  @override
  void dispose() {
    _verificationNotifier.close();
    super.dispose();
  }
}