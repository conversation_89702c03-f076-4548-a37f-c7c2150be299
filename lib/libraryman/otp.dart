import 'dart:async';
import 'dart:async' as prefix0;
import 'dart:convert';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:http/http.dart' as http;
import 'package:likewallet/app_config.dart';
import 'package:likewallet/quickpay/favorites/list.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:likewallet/model/promtpay.dart';
import 'package:likewallet/login/sing_in.dart';

abstract class AbstractOTP {
  Future<bool> sendCodeToPhoneNumber(_phoneNumberController);
  Future<dynamic> sendCodeToPhoneNumberWithMKT(_phoneNumberController);
}

class OTPHandle implements AbstractOTP {
  @override

  /// Sends the code to the specified phone number.
  Future<bool> sendCodeToPhoneNumber(_phoneNumberController) async {
    try {
      var url = Uri.https(env.apiUrl, '/authNoFirebase');
      var response =
          await http.post(url, body: {'phone_number': _phoneNumberController});
      print('Response status: ${response.statusCode}');
      print('Response body: ${response.body}');

      var body = json.decode(response.body);
      if (body['statusCode'] == 200) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      return false;
    }
  }

  /// Sends the code to the specified phone number.
  Future<dynamic> sendCodeToPhoneNumberWithMKT(_phoneNumberController) async {
    var url = Uri.https(env.apiUrl, '/authNoFirebaseWithMKT');
    var response =
        await http.post(url, body: {'phone_number': _phoneNumberController});
    print('Response status: ${response.statusCode}');
    print('Response body: ${response.body}');
    var body = json.decode(response.body);

    if (body['detail'] == 'OK.') {
      //success register and sign in
      //to confirm
      return body['result'];
    } else {
      return {};
    }
  }
}
