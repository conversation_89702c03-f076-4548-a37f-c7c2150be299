import 'dart:async';
import 'package:flutter/material.dart';
import 'package:likewallet/animationPage.dart';
import 'package:likewallet/login/sing_in.dart';
import 'package:likewallet/screen_util.dart';
import 'package:local_auth/local_auth.dart';
import 'package:likewallet/libraryman/confirmSetPin.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:likewallet/libraryman/app_local.dart';

import '../passcode_screen/keyboard.dart';
import '../passcode_screen/passcode_screen.dart';

class SetPin extends StatefulWidget {
  SetPin(
      {this.checkIf,
        this.secret,
        this.roundSMS,
        this.codeVerify,
        this.phoneNumber,
        this.pinAgain,
        this.firstName,
        this.lastName,
        this.refCode,
        this.numberID});

  final bool? pinAgain;
  final String? checkIf;
  final String? secret;
  final providerSMS? roundSMS;
  final String? phoneNumber;
  final String? codeVerify;
  final String? refCode;

  final String? firstName;
  final String? lastName;
  final String? numberID;

  @override
  _SetPin createState() => _SetPin(
      refCode: refCode,
      checkIf: checkIf,
      firstName: firstName,
      lastName: lastName,
      codeVerify: codeVerify,
      phoneNumber: phoneNumber,
      roundSMS: roundSMS,
      secret: secret,
      pinAgain: pinAgain,
      numberID: numberID);
}

class _SetPin extends State<SetPin> {
  _SetPin(
      {this.refCode,
        this.firstName,
        this.lastName,
        this.checkIf,
        this.secret,
        this.roundSMS,
        this.codeVerify,
        this.phoneNumber,
        this.pinAgain,
        this.numberID});

  final String? checkIf;
  final String? secret;
  final providerSMS? roundSMS;
  final String? phoneNumber;
  final String? codeVerify;
  final bool? pinAgain;
  final String? firstName;
  final String? lastName;
  final String? refCode;
  final String? numberID;

  final StreamController<bool> _verificationNotifier =
  StreamController<bool>.broadcast();

  bool isAuthenticated = false;
  //biometric
  final LocalAuthentication localAuth = LocalAuthentication();
  late bool _canCheckBiometrics;
  late List<BiometricType> _availableBiometrics;
  String _authorized = 'Not Authorized';
  late bool didAuthenticate;
  late String firstDigit;
  late String secDigit;
  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return pinVerify();
  }

  ///pass_code
  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    Future.delayed(Duration.zero, () {
      if (pinAgain!) {
        Fluttertoast.showToast(
            msg: AppLocalizations.of(context)!.translate('setpin_wrong'),
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.CENTER,
            timeInSecForIosWeb: 1,
            backgroundColor: Colors.red,
            textColor: Colors.white,
            fontSize: 16.0);
      }
    });
  }

  Widget pinVerify() {
    return PasscodeScreen(
      title: AppLocalizations.of(context)!.translate('setpin'),
      explain: AppLocalizations.of(context)!.translate('setpin_explain'),
      type: 'setPin',
      passwordEnteredCallback: _onPasscodeEntered,
      cancelLocalizedText: '',
      deleteLocalizedText: '',
      shouldTriggerVerification: _verificationNotifier.stream,
      backgroundColor: Colors.black.withOpacity(0.8),
      cancelCallback: _onPasscodeCancelled,
      isValidCallback: accessPass,
      keyboardUIConfig: KeyboardUIConfig(
        digitBorderWidth: MediaQuery.of(context).size.height * 0.001,
        digitTextStyle: TextStyle(
            fontSize: MediaQuery.of(context).size.height * 0.035,
            color: Color(0xffFFFFFF)),
        digitSize: MediaQuery.of(context).size.height * 0.1,
        deleteButtonTextStyle: TextStyle(
            fontSize: MediaQuery.of(context).size.height * 0.025,
            color: Color(0xffFFFFFF)), type: 'setPin',
      ),
      circleUIConfig: CircleUIConfig(
          extraSize: 1.0,
//              borderColor: ,
//              fillColor: color,
          circleSize: mediaQuery(context, 'height', 31)),
    );
  }

  accessPass() {
//    AppRoutes.makeFirst(context, HomeLikewallet());
  }
  _onPasscodeCancelled() {}
  _onPasscodeEntered(String enteredPasscode) {
    Navigator.push(
      context,
      EnterExitRoute(
          exitPage: SING_IN(),
          enterPage: ConfirmSetPin(
            refCode: refCode,
            checkIf: checkIf,
            firstName: firstName,
            lastName: lastName,
            passcode: enteredPasscode,
            secret: secret,
            roundSMS: roundSMS,
            codeVerify: codeVerify,
            phoneNumber: phoneNumber,
            numberID: numberID,
          )),
    );
  }

  @override
  void dispose() {
    _verificationNotifier.close();
    super.dispose();
  }
//
}