import 'dart:async';
import 'package:likewallet/jsonDecode/vending.dart';
import 'package:web3dart/web3dart.dart';
import 'package:path/path.dart' show join, dirname;
import 'package:http/http.dart';
import 'package:web3dart/web3dart.dart';
import 'package:web_socket_channel/io.dart';
import 'package:likewallet/app_config.dart';

abstract class BaseETHV2 {
  //V1
  Future<num> getBalance({required String address});
  Future<num> getBalanceLock({required String address});
  Future<num> getBalanceLockAuto({required String address});
  Future<String> getBalanceLockWei({required String address});
  Future<String> sendTransaction(
      {required String pk, required String to, required String value});
  Future<String> lockLikePoint({required String pk, required String value});
  Future<String> sendApprove({required String value, required String pk});
  Future<String> requestUnlock(
      {required String pk, required String value, required String all});
  Future<String> unlockLikePoint({required String pk, required String value});
  Future<List<String>> getUnlockDate({required String address});
  Future<String> ClaimRewards({required String pk});
  Future<num> checkRewards({required String address});
  Future<String> checkClaim({required String address});
  Future<String> getRound();
  Future<String> getDepositTime({required String address});
  Future<int> isWithdraw({required String address});
  Future<num> getNativeBalance({required String address});
  Future<List<String>> claimRewardsAndLock({required String pk});
  Future<int> getGas();
  //V2
  Future<String> lockLikePointV2({required String pk, required String value});
  Future<String> requestUnlockV2({required String pk, required String value});
  Future<String> unlockLikePointV2({required String pk});
  Future<String> getInterestV2({required String address});
  Future<String> borrowV2({required String pk, required String value});
  Future<List<dynamic>> getLoanV2({required String address});
  Future<String> repayAllV2({required String pk});
  Future<String> repayV2({required String pk, required String value});
  Future<dynamic> getCurrentDeptV2({required String address});
  Future<String> getAPY();
}

class EthContractV2 implements BaseETHV2 {
  final EthereumAddress contractAddrLike =
      EthereumAddress.fromHex(env.contractLike);

  final EthereumAddress contractLock =
      EthereumAddress.fromHex(env.contractLock);
  final EthereumAddress contractAirdrop =
      EthereumAddress.fromHex(env.contractAirdrop);
  final EthereumAddress contractSlot =
      EthereumAddress.fromHex(env.contractSlotMachine);
  final EthereumAddress contractMessage =
      EthereumAddress.fromHex(env.contractMessage);
  final EthereumAddress contractLoan =
      EthereumAddress.fromHex(env.contractLoan);

  static int chainId = int.parse(env.chainId);
  static String abiContractLock = env.abiContractLock;
  static String abiContract = env.abiContractLike;
  static String abiContractAirdrop = env.abiContractAirdrop;
  static String abiContractSlot = env.abiContractSlot;
  static String abiContractLoan = env.abiContractLoan;
  static int gasClaim = int.parse(env.gasClaim.toString());
  static String abiContractMessage = env.abiContractMessage;
  String rpcUrl = env.rpcUrl;
  String wsUrl = env.wsUrl;
//V1

  @override
  Future<num> getNativeBalance({required String address}) async {
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    final balance =
        await client.getBalance(new EthereumAddress.fromHex(address));

    print('We have ${balance.getInEther} Native');

    return balance.getValueInUnit(EtherUnit.ether);
  }

  @override
  Future<int> getGas() async {
    return gasClaim;
  }

  @override
  Future<num> getBalance({required String address}) async {
    final contract = DeployedContract(
        ContractAbi.fromJson(abiContract.toString(), 'LIKEPOINT'),
        contractAddrLike);

    final balanceOf = contract.function('balanceOf');
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    final balanceLikepoint = await client.call(
        contract: contract,
        function: balanceOf,
        params: [new EthereumAddress.fromHex(address)]);

    var newBalance = EtherAmount.inWei(balanceLikepoint.first as BigInt);

    print('We have ${newBalance.getInEther} LIKE');

    return newBalance.getValueInUnit(EtherUnit.ether);
  }

  @override
  Future<String> sendTransaction(
      {required String pk, required String to, required String value}) async {
    // TODO: implement signTransaction

    try {
      int valueSend = value.indexOf('.');
      final EthereumAddress receiver = EthereumAddress.fromHex(to);
      final amount = EtherAmount.fromUnitAndValue(
          EtherUnit.ether, value.substring(0, valueSend));
      final client = Web3Client(rpcUrl, Client(), socketConnector: () {
        return IOWebSocketChannel.connect(wsUrl).cast<String>();
      });
      final credentials = await client.credentialsFromPrivateKey(pk);
      final ownAddress = await credentials.extractAddress();
      print(ownAddress);
      final contract = DeployedContract(
          ContractAbi.fromJson(abiContract.toString(), 'LIKEPOINT'),
          contractAddrLike);
      print(amount.getInWei);
      final transfer = contract.function('transfer');
      final transaction = await client.sendTransaction(
        credentials,
        Transaction.callContract(
          from: ownAddress,
          contract: contract,
          function: transfer,
          maxGas: gasClaim,
          gasPrice: EtherAmount.inWei(BigInt.from(10 * 1e9)),
          parameters: [receiver, amount.getInWei],
        ),
        chainId: chainId,
      );

      return transaction;
    } catch (e) {
      print(e);
      final EthereumAddress receiver = EthereumAddress.fromHex(to);
      final amount = EtherAmount.fromUnitAndValue(EtherUnit.ether, value);
      final client = Web3Client(rpcUrl, Client(), socketConnector: () {
        return IOWebSocketChannel.connect(wsUrl).cast<String>();
      });
      final credentials = await client.credentialsFromPrivateKey(pk);
      final ownAddress = await credentials.extractAddress();
      print(ownAddress);
      final contract = DeployedContract(
          ContractAbi.fromJson(abiContract.toString(), 'LIKEPOINT'),
          contractAddrLike);
      print(amount.getInWei);
      final transfer = contract.function('transfer');
      final transaction = await client.sendTransaction(
        credentials,
        Transaction.callContract(
          from: ownAddress,
          contract: contract,
          function: transfer,
          maxGas: gasClaim,
          gasPrice: EtherAmount.inWei(BigInt.from(10 * 1e9)),
          parameters: [receiver, amount.getInWei],
        ),
        chainId: chainId,
      );

      return transaction;
    }
  }

  @override
  Future<String> lockLikePoint(
      {required String pk, required String value}) async {
    // TODO: implement signTransaction
    EtherAmount amount = EtherAmount.fromUnitAndValue(
        EtherUnit.ether, int.parse(value.split(".")[0]));
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    final credentials = await client.credentialsFromPrivateKey(pk);
    final ownAddress = await credentials.extractAddress();
    print(ownAddress);
    final contract = DeployedContract(
        ContractAbi.fromJson(abiContractLock.toString(), 'LockERC20'),
        contractLock);
    print(amount.getInWei);
    final transfer = contract.function('depositToken');
    int nonce = await client.getTransactionCount(ownAddress);
    final contractApprove = DeployedContract(
        ContractAbi.fromJson(abiContract.toString(), 'LIKEPOINT'),
        contractAddrLike);
    print(amount.getInWei);

    //ตรวจสอบการ allowance ก่อนว่า มีการ allowance  ค้างไว้ไหม
    final allower = contractApprove.function('allowance');
    final balanceApprove = await client
        .call(contract: contractApprove, function: allower, params: [
      new EthereumAddress.fromHex(ownAddress.toString()),
      new EthereumAddress.fromHex(contractLock.toString())
    ]);
    var approved = EtherAmount.inWei(balanceApprove.first as BigInt);
    print('get in wei : ' + approved.getInWei.toString());
    print('get in wei 2: ' +
        EtherAmount.fromUnitAndValue(EtherUnit.wei, '1000000000000000000')
            .getInWei
            .toString());

    if (approved.getInWei ==
        EtherAmount.fromUnitAndValue(EtherUnit.wei, '0').getInWei) {
      final transferApprove = contractApprove.function('approve');

      final transactionApprove = await client.sendTransaction(
        credentials,
        Transaction.callContract(
            from: ownAddress,
            contract: contractApprove,
            function: transferApprove,
            maxGas: gasClaim,
            nonce: nonce,
            gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
            parameters: [
              contractLock,
              EtherAmount.inWei(BigInt.parse(
                      '0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff'))
                  .getInWei
            ]
//            parameters: [contractLock, amount.getInWei],
            ),
        chainId: chainId,
      );

      final transactionLock = await client.sendTransaction(
        credentials,
        Transaction.callContract(
          from: ownAddress,
          contract: contract,
          function: transfer,
          maxGas: gasClaim,
          nonce: nonce + 1,
          gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
          parameters: [contractAddrLike, amount.getInWei, BigInt.from(10)],
        ),
        chainId: chainId,
      );
//  String transactionApprove='';
      return transactionApprove + ":" + transactionLock;
    } else {
      if (amount.getInWei > approved.getInWei) {
        BigInt secondAmount = amount.getInWei - approved.getInWei;
        print(secondAmount);
        amount = approved;
        final transactionLock = await client.sendTransaction(
          credentials,
          Transaction.callContract(
            from: ownAddress,
            contract: contract,
            function: transfer,
            maxGas: gasClaim,
            nonce: nonce,
            gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
            parameters: [contractAddrLike, amount.getInWei, BigInt.from(10)],
          ),
          chainId: chainId,
        );

        final transferApprove = contractApprove.function('approve');

        final transactionApprove = await client.sendTransaction(
          credentials,
          Transaction.callContract(
            from: ownAddress,
            contract: contractApprove,
            function: transferApprove,
            maxGas: gasClaim,
            nonce: nonce + 1,
            gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
            parameters: [
              contractMessage,
              EtherAmount.fromUnitAndValue(
                      EtherUnit.wei, secondAmount.toString())
                  .getInWei
            ],
          ),
          chainId: chainId,
        );

        final transactionLock2 = await client.sendTransaction(
          credentials,
          Transaction.callContract(
            from: ownAddress,
            contract: contract,
            function: transfer,
            maxGas: gasClaim,
            nonce: nonce + 2,
            gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
            parameters: [
              contractAddrLike,
              EtherAmount.fromUnitAndValue(
                      EtherUnit.wei, secondAmount.toString())
                  .getInWei,
              BigInt.from(10)
            ],
          ),
          chainId: chainId,
        );
        return transactionApprove + ":" + transactionLock2;
      } else {
        //ลองลบ approve
        final transactionLock = await client.sendTransaction(
          credentials,
          Transaction.callContract(
            from: ownAddress,
            contract: contract,
            function: transfer,
            maxGas: gasClaim,
            nonce: nonce,
            gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
            parameters: [contractAddrLike, amount.getInWei, BigInt.from(10)],
          ),
          chainId: chainId,
        );
        return 'no' + ":" + transactionLock;
      }
    }
  }

  @override
  Future<String> requestUnlock(
      {required String pk, required String value, required String all}) async {
    EtherAmount amount;
    if (all == 'yes') {
      amount = EtherAmount.fromUnitAndValue(EtherUnit.wei, value);
    } else {
      amount = EtherAmount.fromUnitAndValue(
          EtherUnit.ether, int.parse(value.split(".")[0]));
    }
    print("amount $amount");
    print("getInWei" + amount.getInWei.toString());

    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    final credentials = await client.credentialsFromPrivateKey(pk);
    final ownAddress = await credentials.extractAddress();
    print(ownAddress);
    final contract = DeployedContract(
        ContractAbi.fromJson(abiContractLock.toString(), 'LockERC20'),
        contractLock);
    print("contract $contract");
    int nonce = await client.getTransactionCount(ownAddress);
    final transfer = contract.function('requestWithdraw');
    try {
      final transaction = await client.sendTransaction(
        credentials,
        // Transaction.callContract(
        //   from: ownAddress,
        //   contract: contract,
        //   function: transfer,
        //   maxGas: gasClaim,
        //   nonce: nonce,
        //   gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
        //   parameters: [contractAddrLike, amount.getInWei, BigInt.from(10)],
        // ),
        Transaction.callContract(
          from: ownAddress,
          contract: contract,
          function: transfer,
          maxGas: gasClaim,
          nonce: nonce,
          gasPrice: EtherAmount.inWei(BigInt.from(10 * 1e9)),
          parameters: [contractAddrLike, amount.getInWei],
        ),
        chainId: chainId,
      );
      return transaction;
    } catch (e) {
      return 'Transaction Error [$e]';
    }
  }

  @override
  Future<String> unlockLikePoint(
      {required String pk, required String value}) async {
    final amount = EtherAmount.fromUnitAndValue(
        EtherUnit.ether, int.parse(value.split(".")[0]));
    print("amount $amount");
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    final credentials = await client.credentialsFromPrivateKey(pk);
    final ownAddress = await credentials.extractAddress();
    print(ownAddress);
    final contract = DeployedContract(
        ContractAbi.fromJson(abiContractLock.toString(), 'LockERC20'),
        contractLock);
    print(amount.getInWei);
    final transfer = contract.function('withdrawToken');
    final transaction = await client.sendTransaction(
      credentials,
      Transaction.callContract(
        from: ownAddress,
        contract: contract,
        function: transfer,
        maxGas: gasClaim,
        gasPrice: EtherAmount.inWei(BigInt.from(10 * 1e9)),
        parameters: [contractAddrLike, amount.getInWei],
      ),
      chainId: chainId,
    );
    return transaction;
  }

  @override
  Future<num> getBalanceLock({required String address}) async {
    final contract = DeployedContract(
        ContractAbi.fromJson(abiContractLock.toString(), 'LockERC20'),
        contractLock);
    try {
      final balanceOf = contract.function('getLock');
      final client = Web3Client(rpcUrl, Client(), socketConnector: () {
        return IOWebSocketChannel.connect(wsUrl).cast<String>();
      });
      print(new EthereumAddress.fromHex(address));
      print(contractAddrLike);

      final balanceLikepoint = await client.call(
          contract: contract,
          function: balanceOf,
          params: [contractAddrLike, new EthereumAddress.fromHex(address)]);

      var newBalance = EtherAmount.inWei(balanceLikepoint.first as BigInt);

      print('We have lock ${newBalance.getInEther} LIKE');

      return newBalance.getValueInUnit(EtherUnit.ether);
    } catch (e) {
      print(e);
      return 0.0;
    }
  }

  @override
  Future<num> getBalanceLockAuto({required String address}) async {
    final contract = DeployedContract(
        ContractAbi.fromJson(abiContractLock.toString(), 'LockERC20'),
        contractLock);

    final balanceOf = contract.function('getAmount');
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    print(new EthereumAddress.fromHex(address));
    print(contractAddrLike);
    try {
      final balanceLikepoint = await client.call(
          contract: contract,
          function: balanceOf,
          params: [contractAddrLike, new EthereumAddress.fromHex(address)]);

      var newBalance = EtherAmount.inWei(balanceLikepoint.first as BigInt);

      print('We have lock ${newBalance.getInEther} LIKE');

      return newBalance.getValueInUnit(EtherUnit.ether);
    } catch (e) {
      print(e);
      return 0;
    }
  }

  @override
  Future<String> getBalanceLockWei({required String address}) async {
    final contract = DeployedContract(
        ContractAbi.fromJson(abiContractLock.toString(), 'LockERC20'),
        contractLock);
    try {
      final balanceOf = contract.function('getLock');
      final client = Web3Client(rpcUrl, Client(), socketConnector: () {
        return IOWebSocketChannel.connect(wsUrl).cast<String>();
      });
      print(new EthereumAddress.fromHex(address));
      print(contractAddrLike);

      final balanceLikepoint = await client.call(
          contract: contract,
          function: balanceOf,
          params: [contractAddrLike, new EthereumAddress.fromHex(address)]);

      var newBalance = EtherAmount.inWei(balanceLikepoint.first as BigInt);

      print('We have lock ${newBalance.getInEther} LIKE');

      return newBalance.getInWei.toString();
    } catch (e) {
      print(e);
      return "0";
    }
  }

  @override
  Future<String> sendApprove(
      {required String value, required String pk}) async {
    // TODO: implement signTransaction
    final amount =
        EtherAmount.fromUnitAndValue(EtherUnit.ether, int.parse(value));
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    final credentials = await client.credentialsFromPrivateKey(pk);
    final ownAddress = await credentials.extractAddress();
    print(ownAddress);
    final contractApprove = DeployedContract(
        ContractAbi.fromJson(abiContract.toString(), 'LIKEPOINT'),
        contractAddrLike);
    print(amount.getInWei);
    final transferApprove = contractApprove.function('approve');

    final transaction = await client.sendTransaction(
      credentials,
      Transaction.callContract(
        from: ownAddress,
        contract: contractApprove,
        function: transferApprove,
        maxGas: gasClaim,
        gasPrice: EtherAmount.inWei(BigInt.from(10 * 1e9)),
        parameters: [contractLock, amount.getInWei],
      ),
      chainId: chainId,
    );

    return transaction;
  }

  @override
  Future<int> isWithdraw({required String address}) async {
    final contract = DeployedContract(
        ContractAbi.fromJson(abiContractLock.toString(), 'LockERC20'),
        contractLock);

    final balanceOf = contract.function('tokens');
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    print('isWithdraw');
    try {
      final dataLock = await client.call(
          contract: contract,
          function: balanceOf,
          params: [contractAddrLike, new EthereumAddress.fromHex(address)]);
      var withdrawStatus = dataLock[5];

//      print('withdrawStatus '+ withdrawStatus.toString());
      return int.parse(withdrawStatus.toString());
    } catch (e) {
      return 0;
    }

//    return newBalance.getValueInUnit(EtherUnit.ether);
  }

  @override
  Future<List<String>> getUnlockDate({required String address}) async {
    final contract = DeployedContract(
        ContractAbi.fromJson(abiContractLock.toString(), 'LockERC20'),
        contractLock);

    final balanceOf = contract.function('tokens');
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    try {
      final dataLock = await client.call(
          contract: contract,
          function: balanceOf,
          params: [contractAddrLike, new EthereumAddress.fromHex(address)]);

//    print('Data Lock ${dataLock[1]} ');
//    print(new DateTime.now().millisecondsSinceEpoch/1000);
      //check time unlock
      double nextUnlock = dataLock[1].toDouble();
      double currentTime = new DateTime.now().millisecondsSinceEpoch / 1000;
      double diffUnlock = nextUnlock - currentTime;

      double minute;
      double hour;
      int Hour;
      int Min;

      if (diffUnlock > 0) {
        minute = diffUnlock / 60 % 60;
        hour = diffUnlock / 3600;
        Hour = int.parse(hour.toString().split(".")[0]);
        Min = int.parse(minute.toString().split(".")[0]);
        print(Hour.toString());
        print(Min.toString());
      } else {
        Hour = 0;
        Min = 0;
        diffUnlock = 0;
      }

      print('dataLock4' + dataLock.toString());
      return [
        Hour.toString(),
        Min.toString(),
        diffUnlock.toString(),
        dataLock[7].toString(),
        dataLock[5].toString()
      ];
    } catch (e) {
      return ['0', '0', '0', '0', '0'];
    }

//    return newBalance.getValueInUnit(EtherUnit.ether);
  }

  @override
  Future<String> ClaimRewards({required String pk}) async {
    // TODO: implement signTransaction
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    final credentials = await client.credentialsFromPrivateKey(pk);
    final ownAddress = await credentials.extractAddress();
    print(ownAddress);
    final contractApprove = DeployedContract(
        ContractAbi.fromJson(abiContractAirdrop.toString(), 'Airdrop'),
        contractAirdrop);

    final transactionGetRewards = contractApprove.function('getRewards');
    print(contractAirdrop);
    int nonce = await client.getTransactionCount(ownAddress);
    final transaction = await client.sendTransaction(
      credentials,
      Transaction.callContract(
        from: ownAddress,
        contract: contractApprove,
        function: transactionGetRewards,
        maxGas: gasClaim,
        nonce: nonce,
        gasPrice: EtherAmount.inWei(BigInt.from(10 * 1e9)),
        parameters: [contractAddrLike, contractLock],
      ),
      chainId: chainId,
    );
    return transaction;
  }

  @override
  Future<num> checkRewards({required String address}) async {
    final contract = DeployedContract(
        ContractAbi.fromJson(abiContractAirdrop.toString(), 'Airdrop'),
        contractAirdrop);

    final checkRewards = contract.function('checkRewards');
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    final balanceClaim = await client.call(
        contract: contract,
        function: checkRewards,
        params: [
          contractAddrLike,
          contractLock,
          new EthereumAddress.fromHex(address)
        ]);

    var newBalance = EtherAmount.inWei(balanceClaim.first as BigInt);

    print('We have chaim rewards ${newBalance.getInEther} LIKE');

    return newBalance.getValueInUnit(EtherUnit.ether);
  }

  @override
  Future<String> checkClaim({required String address}) async {
    final contract = DeployedContract(
        ContractAbi.fromJson(abiContractAirdrop.toString(), 'Airdrop'),
        contractAirdrop);

    final checkClaim = contract.function('Claim');
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    final dataClaimer = await client.call(
        contract: contract,
        function: checkClaim,
        params: [contractAddrLike, new EthereumAddress.fromHex(address)]);

    print("dataClaimer " + dataClaimer[0].toString());
    return dataClaimer[1].toString();
  }

  @override
  Future<String> getDepositTime({required String address}) async {
    final contract = DeployedContract(
        ContractAbi.fromJson(abiContractLock.toString(), 'LockERC20'),
        contractLock);

    final getDepositTime = contract.function('getDepositTime');
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    final dataClaimer = await client.call(
        contract: contract,
        function: getDepositTime,
        params: [contractAddrLike, new EthereumAddress.fromHex(address)]);

    print("dataClaimer " + dataClaimer[0].toString());
    return dataClaimer[0].toString();
  }

  @override
  Future<String> getRound() async {
    final contract = DeployedContract(
        ContractAbi.fromJson(abiContractAirdrop.toString(), 'Airdrop'),
        contractAirdrop);

    final checkClaim = contract.function('Round');
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    final dataClaimer = await client.call(
        contract: contract, function: checkClaim, params: [contractAddrLike]);

    print("getRound " + dataClaimer[0].toString());
    return dataClaimer[0].toString();
  }

  //###SLOT MACHINE####//
  //created 22/02/2020
  //author: prapat polchan
  @override
  Future<String> buyTicketSlotMachine(
      {required String pk, required String value}) async {
    // TODO: implement signTransaction
    final amount = EtherAmount.fromUnitAndValue(
        EtherUnit.ether, int.parse(value.split(".")[0]));
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    final credentials = await client.credentialsFromPrivateKey(pk);
    final ownAddress = await credentials.extractAddress();
    print(ownAddress);
    final contract = DeployedContract(
        ContractAbi.fromJson(abiContractSlot.toString(), 'SlotMachine'),
        contractSlot);
    print(amount.getInWei);
    final buyTicket = contract.function('buyTicket');
    int nonce = await client.getTransactionCount(ownAddress);
    final contractApprove = DeployedContract(
        ContractAbi.fromJson(abiContract.toString(), 'LIKEPOINT'),
        contractAddrLike);
    print(amount.getInWei);
    final transferApprove = contractApprove.function('approve');

    final transactionApprove = await client.sendTransaction(
      credentials,
      Transaction.callContract(
        from: ownAddress,
        contract: contractApprove,
        function: transferApprove,
        maxGas: gasClaim,
        nonce: nonce,
        gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
        parameters: [contractSlot, amount.getInWei],
      ),
      chainId: chainId,
    );

    print(contractAddrLike);
    print(amount.getInWei);
    final transactionBuy = await client.sendTransaction(
      credentials,
      Transaction.callContract(
        from: ownAddress,
        contract: contract,
        function: buyTicket,
        maxGas: gasClaim,
        nonce: nonce + 1,
        gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
        parameters: [contractAddrLike, amount.getInWei, 'ticket'],
      ),
      chainId: chainId,
    );
    return transactionApprove + ":" + transactionBuy;
  }

  @override
  Future<num> getTicketSlotMachine({required String address}) async {
    final contract = DeployedContract(
        ContractAbi.fromJson(abiContractSlot.toString(), 'SlotMachine'),
        contractSlot);

    final tricket = contract.function('ticket');
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    print(new EthereumAddress.fromHex(address));
    try {
      final tricketNumber = await client.call(
          contract: contract,
          function: tricket,
          params: [contractAddrLike, new EthereumAddress.fromHex(address)]);

      var tricketHave = EtherAmount.inWei(tricketNumber.first as BigInt);

      print('We have ticket ${tricketHave} tickets');

      return tricketHave.getValueInUnit(EtherUnit.wei);
    } catch (e) {
      print(e);
      return 0;
    }
  }

  @override
  Future<num> getPrizeSlotMachine() async {
    final contract = DeployedContract(
        ContractAbi.fromJson(abiContractSlot.toString(), 'SlotMachine'),
        contractSlot);

    final prize = contract.function('prize');
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    try {
      final bigPrize = await client.call(
          contract: contract, function: prize, params: [contractAddrLike]);

      var prizeValue = EtherAmount.inWei(bigPrize.first as BigInt);

      print('Big prize is ${prizeValue} LIKE');

      return prizeValue.getValueInUnit(EtherUnit.ether);
    } catch (e) {
      print(e);
      return 0;
    }
  }

  @override
  Future<String> subTicket({required String pk}) async {
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    final credentials = await client.credentialsFromPrivateKey(pk);
    final ownAddress = await credentials.extractAddress();
    print(ownAddress);
    final contract = DeployedContract(
        ContractAbi.fromJson(abiContractSlot.toString(), 'SlotMachine'),
        contractSlot);
    final subTicket = contract.function('subTicket');

    final transaction = await client.sendTransaction(
      credentials,
      Transaction.callContract(
        from: ownAddress,
        contract: contract,
        function: subTicket,
        maxGas: gasClaim,
        gasPrice: EtherAmount.inWei(BigInt.from(10 * 1e9)),
        parameters: [
          contractAddrLike,
          BigInt.from(1),
          "Decrease ticket for roll Likewallet"
        ],
      ),
      chainId: chainId,
    );
    return transaction;
  }

  //vending
  @override
  Future<String> transferVending(
      {required Vending vending,
      required String abiCode,
      required String pk,
      required String contractName}) async {
    EtherAmount amount = EtherAmount.fromUnitAndValue(
        EtherUnit.ether, int.parse(vending.amount.toString().split(".")[0]));

    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    final credentials = await client.credentialsFromPrivateKey(pk);
    final ownAddress = await credentials.extractAddress();
    print(ownAddress);

    final contract = DeployedContract(
        ContractAbi.fromJson(abiCode, contractName),
        EthereumAddress.fromHex(vending.addr));
    print(amount.getInWei);
    final transfer = contract.function(vending.function);
    int nonce = await client.getTransactionCount(ownAddress);
    final contractApprove = DeployedContract(
        ContractAbi.fromJson(abiContract.toString(), 'LIKEPOINT'),
        contractAddrLike);

    final allower = contractApprove.function('allowance');
    final balanceApprove = await client
        .call(contract: contractApprove, function: allower, params: [
      new EthereumAddress.fromHex(ownAddress.toString()),
      new EthereumAddress.fromHex(vending.addr)
    ]);
    var approved = EtherAmount.inWei(balanceApprove.first as BigInt);

    print('get in wei : ' + approved.getInWei.toString());
    print('get in wei 2: ' +
        EtherAmount.fromUnitAndValue(EtherUnit.wei, '1000000000000000000')
            .getInWei
            .toString());

    //ถ้า approve == 0 ให้ทำการ approve ปกติ
    if (approved.getInWei ==
        EtherAmount.fromUnitAndValue(EtherUnit.wei, '0').getInWei) {
      final transferApprove = contractApprove.function('approve');
//      final appv = EtherAmount.inWei(BigInt.parse('0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff'));
//      print(appv.getInWei);
      final transactionApprove = await client.sendTransaction(
        credentials,
        Transaction.callContract(
          from: ownAddress,
          contract: contractApprove,
          function: transferApprove,
          maxGas: gasClaim,
          nonce: nonce,
          gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
          parameters: [
            EthereumAddress.fromHex(vending.addr),
            EtherAmount.inWei(BigInt.parse(
                    '0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff'))
                .getInWei
          ],
        ),
        chainId: chainId,
      );

//      (string machine_id, uint256 amount, address likeAddr, string ref_code, string payment_id)
      final transfervending = await client.sendTransaction(
        credentials,
        Transaction.callContract(
          from: ownAddress,
          contract: contract,
          function: transfer,
          maxGas: gasClaim,
          nonce: nonce + 1,
          gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
          parameters: [
            vending.machine_id,
            amount.getInWei,
            contractAddrLike,
            vending.ref_code,
            vending.payment_id
          ],
        ),
        chainId: chainId,
      );
      return transfervending;
    } else {
      // ถ้า approve ไว้แล้วน้อยกว่าค่าจะที่โอน ให้ทำการ ส่งก้อนแรกแล้ว approve เพิ่มแล้วส่งที่เหลือ
      if (amount.getInWei > approved.getInWei) {
        final transferApprove = contractApprove.function('approve');
        final transactionApprove = await client.sendTransaction(
          credentials,
          Transaction.callContract(
            from: ownAddress,
            contract: contractApprove,
            function: transferApprove,
            maxGas: gasClaim,
            nonce: nonce,
            gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
            parameters: [
              EthereumAddress.fromHex(vending.addr),
              EtherAmount.inWei(BigInt.parse('0')).getInWei
            ],
          ),
          chainId: chainId,
        );
        final transactionApprove2 = await client.sendTransaction(
          credentials,
          Transaction.callContract(
            from: ownAddress,
            contract: contractApprove,
            function: transferApprove,
            maxGas: gasClaim,
            nonce: nonce + 1,
            gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
            parameters: [
              EthereumAddress.fromHex(vending.addr),
              EtherAmount.inWei(BigInt.parse(
                      '0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff'))
                  .getInWei
            ],
          ),
          chainId: chainId,
        );

        final transfervending = await client.sendTransaction(
          credentials,
          Transaction.callContract(
            from: ownAddress,
            contract: contract,
            function: transfer,
            maxGas: gasClaim,
            nonce: nonce + 2,
            gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
            parameters: [
              vending.machine_id,
              amount.getInWei,
              contractAddrLike,
              vending.ref_code,
              vending.payment_id
            ],
          ),
          chainId: chainId,
        );
        return transfervending;
      } else {
//        print(message);
        final transferMessage = await client.sendTransaction(
          credentials,
          Transaction.callContract(
            from: ownAddress,
            contract: contract,
            function: transfer,
            maxGas: gasClaim,
            nonce: nonce,
            gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
            parameters: [
              vending.machine_id,
              amount.getInWei,
              contractAddrLike,
              vending.ref_code,
              vending.payment_id
            ],
          ),
          chainId: chainId,
        );
//  String transactionApprove='';
        return transferMessage;
      }
    }
  }

  //transfer message
  @override
  Future<String> transferMessage(
      {required String pk,
      required String to,
      required String value,
      required String message}) async {
    // TODO: implement signTransaction

    EtherAmount amount = EtherAmount.fromUnitAndValue(
        EtherUnit.ether, int.parse(value.split(".")[0]));

    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    final credentials = await client.credentialsFromPrivateKey(pk);
    final ownAddress = await credentials.extractAddress();
    print(ownAddress);

    final contract = DeployedContract(
        ContractAbi.fromJson(
            abiContractMessage.toString(), 'TransferWithMessageTRC20'),
        contractMessage);
    print(amount.getInWei);
    final transfer = contract.function('transferMessage');
    int nonce = await client.getTransactionCount(ownAddress);
    final contractApprove = DeployedContract(
        ContractAbi.fromJson(abiContract.toString(), 'LIKEPOINT'),
        contractAddrLike);

    final allower = contractApprove.function('allowance');
    final balanceApprove = await client
        .call(contract: contractApprove, function: allower, params: [
      new EthereumAddress.fromHex(ownAddress.toString()),
      new EthereumAddress.fromHex(contractMessage.toString())
    ]);
    var approved = EtherAmount.inWei(balanceApprove.first as BigInt);

    print('get in wei : ' + approved.getInWei.toString());
    print('get in wei 2: ' +
        EtherAmount.fromUnitAndValue(EtherUnit.wei, '1000000000000000000')
            .getInWei
            .toString());

    //ถ้า approve == 0 ให้ทำการ approve ปกติ
    if (approved.getInWei ==
        EtherAmount.fromUnitAndValue(EtherUnit.wei, '0').getInWei) {
      final transferApprove = contractApprove.function('approve');
//      final appv = EtherAmount.inWei(BigInt.parse('0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff'));
//      print(appv.getInWei);
      final transactionApprove = await client.sendTransaction(
        credentials,
        Transaction.callContract(
          from: ownAddress,
          contract: contractApprove,
          function: transferApprove,
          maxGas: gasClaim,
          nonce: nonce,
          gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
          parameters: [
            contractMessage,
            EtherAmount.inWei(BigInt.parse(
                    '0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff'))
                .getInWei
          ],
        ),
        chainId: chainId,
      );

      print(message);
      final transferMessage = await client.sendTransaction(
        credentials,
        Transaction.callContract(
          from: ownAddress,
          contract: contract,
          function: transfer,
          maxGas: gasClaim,
          nonce: nonce + 1,
          gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
          parameters: [
            contractAddrLike,
            EthereumAddress.fromHex(to),
            amount.getInWei,
            message
          ],
        ),
        chainId: chainId,
      );
      return transferMessage;
    } else {
      // ถ้า approve ไว้แล้วน้อยกว่าค่าจะที่โอน ให้ทำการ ส่งก้อนแรกแล้ว approve เพิ่มแล้วส่งที่เหลือ
      if (amount.getInWei > approved.getInWei) {
        BigInt secondAmount = amount.getInWei - approved.getInWei;
        print(secondAmount);
        amount = approved;
        final transferMessage = await client.sendTransaction(
          credentials,
          Transaction.callContract(
            from: ownAddress,
            contract: contract,
            function: transfer,
            maxGas: gasClaim,
            nonce: nonce,
            gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
            parameters: [
              contractAddrLike,
              EthereumAddress.fromHex(to),
              amount.getInWei,
              message
            ],
          ),
          chainId: chainId,
        );

        final transferApprove = contractApprove.function('approve');

        final transactionApprove = await client.sendTransaction(
          credentials,
          Transaction.callContract(
            from: ownAddress,
            contract: contractApprove,
            function: transferApprove,
            maxGas: gasClaim,
            nonce: nonce + 1,
            gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
            parameters: [
              contractMessage,
              EtherAmount.fromUnitAndValue(
                      EtherUnit.wei, secondAmount.toString())
                  .getInWei
            ],
          ),
          chainId: chainId,
        );
        final transferMessage2 = await client.sendTransaction(
          credentials,
          Transaction.callContract(
            from: ownAddress,
            contract: contract,
            function: transfer,
            maxGas: gasClaim,
            nonce: nonce + 2,
            gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
            parameters: [
              contractAddrLike,
              EthereumAddress.fromHex(to),
              EtherAmount.fromUnitAndValue(
                      EtherUnit.wei, secondAmount.toString())
                  .getInWei,
              message
            ],
          ),
          chainId: chainId,
        );
        return transferMessage2;
      } else {
//        print(amount.getInWei);
//        final transferApprove = contractApprove.function('approve');
//
//        final transactionApprove = await client.sendTransaction(
//            credentials,
//            Transaction.callContract(
//              from: ownAddress,
//              contract: contractApprove,
//              function: transferApprove,
//              maxGas: 100000,
//              nonce: nonce,
//              gasPrice: EtherAmount.inWei(BigInt.from(4*1e9)),
//
//              parameters: [contractMessage, amount.getInWei],
//            ),
//
//        );
//
//        print(message);
        final transferMessage = await client.sendTransaction(
          credentials,
          Transaction.callContract(
            from: ownAddress,
            contract: contract,
            function: transfer,
            maxGas: gasClaim,
            nonce: nonce,
            gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
            parameters: [
              contractAddrLike,
              EthereumAddress.fromHex(to),
              amount.getInWei,
              message
            ],
          ),
          chainId: chainId,
        );
//  String transactionApprove='';
        return transferMessage;
      }
    }
  }

  @override
  Future<String> dynamicSendContract(
      {required String pk,
      required String contractAddress,
      required String abi,
      required String callFunction,
      required String to,
      required String value,
      required String message}) async {
    // TODO: implement signTransaction

    EtherAmount amount = EtherAmount.fromUnitAndValue(
        EtherUnit.ether, int.parse(value.split(".")[0]));
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    final credentials = await client.credentialsFromPrivateKey(pk);
    final ownAddress = await credentials.extractAddress();
    print(ownAddress);
    final contract = DeployedContract(
        ContractAbi.fromJson(abi.toString(), callFunction),
        EthereumAddress.fromHex(contractAddress));
    print(amount.getInWei);
    final transfer = contract.function(callFunction);
    int nonce = await client.getTransactionCount(ownAddress);
    final contractApprove = DeployedContract(
        ContractAbi.fromJson(abiContract.toString(), 'LIKEPOINT'),
        contractAddrLike);
    print(amount.getInWei);
    final transferApprove = contractApprove.function('approve');

    final allower = contractApprove.function('allowance');
    final balanceApprove = await client
        .call(contract: contractApprove, function: allower, params: [
      new EthereumAddress.fromHex(ownAddress.toString()),
      new EthereumAddress.fromHex(contractAddress.toString())
    ]);
    var approved = EtherAmount.inWei(balanceApprove.first as BigInt);
    if (approved.getInWei ==
        EtherAmount.fromUnitAndValue(EtherUnit.wei, '0').getInWei) {
      final transactionApprove = await client.sendTransaction(
        credentials,
        Transaction.callContract(
          from: ownAddress,
          contract: contractApprove,
          function: transferApprove,
          maxGas: gasClaim,
          nonce: nonce,
          gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
          parameters: [
            EthereumAddress.fromHex(contractAddress),
            EtherAmount.inWei(BigInt.parse(
                    '0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff'))
                .getInWei
          ],
        ),
        chainId: chainId,
      );

      print(message);
      final transferMessage = await client.sendTransaction(
        credentials,
        Transaction.callContract(
          from: ownAddress,
          contract: contract,
          function: transfer,
          maxGas: gasClaim,
          nonce: nonce + 1,
          gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
          parameters: [
            contractAddrLike,
            EthereumAddress.fromHex(to),
            amount.getInWei,
            message
          ],
        ),
        chainId: chainId,
      );
//  String transactionApprove='';
      return transferMessage;
    } else {
      if (amount.getInWei > approved.getInWei) {
        BigInt secondAmount = amount.getInWei - approved.getInWei;
        amount = approved;
        final transferMessage = await client.sendTransaction(
          credentials,
          Transaction.callContract(
            from: ownAddress,
            contract: contract,
            function: transfer,
            maxGas: gasClaim,
            nonce: nonce,
            gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
            parameters: [
              contractAddrLike,
              EthereumAddress.fromHex(to),
              amount.getInWei,
              message
            ],
          ),
          chainId: chainId,
        );

        final transferApprove = contractApprove.function('approve');
        final transactionApprove = await client.sendTransaction(
          credentials,
          Transaction.callContract(
            from: ownAddress,
            contract: contractApprove,
            function: transferApprove,
            maxGas: gasClaim,
            nonce: nonce + 1,
            gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
            parameters: [
              EthereumAddress.fromHex(contractAddress),
              EtherAmount.inWei(BigInt.parse(
                      '0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff'))
                  .getInWei
            ],
          ),
          chainId: chainId,
        );

        final transferMessage2 = await client.sendTransaction(
          credentials,
          Transaction.callContract(
            from: ownAddress,
            contract: contract,
            function: transfer,
            maxGas: gasClaim,
            nonce: nonce + 2,
            gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
            parameters: [
              contractAddrLike,
              EthereumAddress.fromHex(to),
              EtherAmount.fromUnitAndValue(
                      EtherUnit.wei, secondAmount.toString())
                  .getInWei,
              message
            ],
          ),
          chainId: chainId,
        );
        return transferMessage2;
      } else {
        final transferMessage = await client.sendTransaction(
          credentials,
          Transaction.callContract(
            from: ownAddress,
            contract: contract,
            function: transfer,
            maxGas: gasClaim,
            nonce: nonce,
            gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
            parameters: [
              contractAddrLike,
              EthereumAddress.fromHex(to),
              amount.getInWei,
              message
            ],
          ),
          chainId: chainId,
        );
//  String transactionApprove='';
        return transferMessage;
      }
    }
  }

  @override
  Future<bool> checkAddress({required String address}) async {
    try {
      EthereumAddress.fromHex(address);
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<List<String>> claimRewardsAndLock({required String pk}) async {
    // TODO: implement signTransaction
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });

    final contract = DeployedContract(
        ContractAbi.fromJson(abiContractAirdrop.toString(), 'Airdrop'),
        contractAirdrop);

    final checkRewards = contract.function('checkRewards');

    final credentials = await client.credentialsFromPrivateKey(pk);
    final ownAddress = await credentials.extractAddress();
    final balanceClaim = await client.call(
        contract: contract,
        function: checkRewards,
        params: [contractAddrLike, contractLock, ownAddress]);

    var newBalance = EtherAmount.inWei(balanceClaim.first as BigInt);
    print('balanceClaim ' + balanceClaim.first.toString());
    print('newBalance ' + newBalance.toString());

    ///approve
    final contractApprove = DeployedContract(
        ContractAbi.fromJson(abiContract.toString(), 'LIKEPOINT'),
        contractAddrLike);

    //ตรวจสอบการ allowance ก่อนว่า มีการ allowance  ค้างไว้ไหม
    final allower = contractApprove.function('allowance');
    final balanceApprove = await client
        .call(contract: contractApprove, function: allower, params: [
      new EthereumAddress.fromHex(ownAddress.toString()),
      new EthereumAddress.fromHex(contractLock.toString())
    ]);
    var approved = EtherAmount.inWei(balanceApprove.first as BigInt);
    //approve
    int nonce = await client.getTransactionCount(ownAddress);
    final contractLockup = DeployedContract(
        ContractAbi.fromJson(abiContractLock.toString(), 'LockERC20'),
        contractLock);
    final transfer = contractLockup.function('depositToken');
    final transactionGetRewards = contract.function('getRewards');
    print(contractAirdrop);

    print('approve allowance is ' + approved.getInWei.toString());
    if (approved.getInWei ==
        EtherAmount.fromUnitAndValue(EtherUnit.wei, '0').getInWei) {
      final transferApprove = contractApprove.function('approve');
      final transaction = await client.sendTransaction(
        credentials,
        Transaction.callContract(
          from: ownAddress,
          contract: contract,
          function: transactionGetRewards,
          nonce: nonce,
          maxGas: gasClaim,
          gasPrice: EtherAmount.inWei(BigInt.from(10 * 1e9)),
          parameters: [contractAddrLike, contractLock],
        ),
        chainId: chainId,
      );

      final transactionApprove = await client.sendTransaction(
        credentials,
        Transaction.callContract(
            from: ownAddress,
            contract: contractApprove,
            function: transferApprove,
            maxGas: gasClaim,
            nonce: nonce + 1,
            gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
            parameters: [
              contractLock,
              EtherAmount.inWei(BigInt.parse(
                      '0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff'))
                  .getInWei
            ]),
        chainId: chainId,
      );
      final amount = EtherAmount.inWei(balanceClaim.first as BigInt).getInWei;
      final transactionLock = await client.sendTransaction(
        credentials,
        Transaction.callContract(
          from: ownAddress,
          contract: contractLockup,
          function: transfer,
          maxGas: gasClaim,
          nonce: nonce + 2,
          gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
          parameters: [contractAddrLike, amount, BigInt.from(10)],
        ),
        chainId: chainId,
      );

      print('transaction ' + transaction);
      print('transaction approve ' + transactionApprove);
      print('transactionLock ' + transactionLock);
      return [transaction, transactionLock];
    } else {
      print('approve allowance is more 0' + approved.getInWei.toString());

      final transaction = await client.sendTransaction(
        credentials,
        Transaction.callContract(
          from: ownAddress,
          contract: contract,
          function: transactionGetRewards,
          nonce: nonce,
          maxGas: gasClaim,
          gasPrice: EtherAmount.inWei(BigInt.from(10 * 1e9)),
          parameters: [contractAddrLike, contractLock],
        ),
        chainId: chainId,
      );
      print(transaction);
      print('transactionLock start ');
      print('transaction balance claim ' + balanceClaim.first.toString());
      final amount = EtherAmount.inWei(balanceClaim.first as BigInt).getInWei;
      final transactionLock = await client.sendTransaction(
        credentials,
        Transaction.callContract(
          from: ownAddress,
          contract: contractLockup,
          function: transfer,
          maxGas: gasClaim,
          nonce: nonce + 1,
          gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
          parameters: [contractAddrLike, amount, BigInt.from(10)],
        ),
        chainId: chainId,
      );
      print('transactionLock ' + transactionLock);
      return [transaction, transactionLock];
    }
  }

  ////Start Contract Version V2V2

  @override
  Future<String> unlockLikePointV2({required String pk}) async {
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    final credentials = await client.credentialsFromPrivateKey(pk);
    final ownAddress = await credentials.extractAddress();
    print(ownAddress);
    final contract = DeployedContract(
        ContractAbi.fromJson(abiContractLock.toString(), 'LockERC20'),
        contractLock);
    int nonce = await client.getTransactionCount(ownAddress);
    final transfer = contract.function('withdrawToken');
    final transaction = await client.sendTransaction(
      credentials,
      Transaction.callContract(
        from: ownAddress,
        contract: contract,
        function: transfer,
        maxGas: gasClaim,
        nonce: nonce,
        gasPrice: EtherAmount.inWei(BigInt.from(10 * 1e9)),
        parameters: [contractAddrLike],
      ),
      chainId: chainId,
    );
    return transaction;
  }

  @override
  Future<String> requestUnlockV2(
      {required String pk, required String value}) async {
    final amount = EtherAmount.fromUnitAndValue(
        EtherUnit.ether, int.parse(value.split(".")[0]));
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    final credentials = await client.credentialsFromPrivateKey(pk);
    final ownAddress = await credentials.extractAddress();
    print(ownAddress);
    final contract = DeployedContract(
        ContractAbi.fromJson(abiContractLock.toString(), 'LockERC20'),
        contractLock);
    final transfer = contract.function('requestWithdraw');
    final transaction = await client.sendTransaction(
      credentials,
      Transaction.callContract(
        from: ownAddress,
        contract: contract,
        function: transfer,
        maxGas: gasClaim,
        gasPrice: EtherAmount.inWei(BigInt.from(10 * 1e9)),
        parameters: [contractAddrLike, amount.getInWei],
      ),
      chainId: chainId,
    );
    return transaction;
  }

  @override
  Future<String> lockLikePointV2(
      {required String pk, required String value}) async {
    // TODO: implement signTransaction
    EtherAmount amount = EtherAmount.fromUnitAndValue(
        EtherUnit.ether, int.parse(value.split(".")[0]));
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    final credentials = await client.credentialsFromPrivateKey(pk);
    final ownAddress = await credentials.extractAddress();
    print(ownAddress);
    final contract = DeployedContract(
        ContractAbi.fromJson(abiContractLock.toString(), 'LockERC20'),
        contractLock);
    print(amount.getInWei);
    final transfer = contract.function('depositToken');
    int nonce = await client.getTransactionCount(ownAddress);
    final contractApprove = DeployedContract(
        ContractAbi.fromJson(abiContract.toString(), 'LIKEPOINT'),
        contractAddrLike);
    print(amount.getInWei);

    //ตรวจสอบการ allowance ก่อนว่า มีการ allowance  ค้างไว้ไหม
    final allower = contractApprove.function('allowance');
    final balanceApprove = await client
        .call(contract: contractApprove, function: allower, params: [
      new EthereumAddress.fromHex(ownAddress.toString()),
      new EthereumAddress.fromHex(contractLock.toString())
    ]);
    var approved = EtherAmount.inWei(balanceApprove.first as BigInt);
    if (approved.getInWei ==
        EtherAmount.fromUnitAndValue(EtherUnit.wei, '0').getInWei) {
      final transferApprove = contractApprove.function('approve');

      final transactionApprove = await client.sendTransaction(
        credentials,
        Transaction.callContract(
            from: ownAddress,
            contract: contractApprove,
            function: transferApprove,
            maxGas: gasClaim,
            nonce: nonce,
            gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
            parameters: [
              contractLock,
              EtherAmount.inWei(BigInt.parse(
                      '0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff'))
                  .getInWei
            ]
//            parameters: [contractLock, amount.getInWei],
            ),
        chainId: chainId,
      );

      final transactionLock = await client.sendTransaction(
        credentials,
        Transaction.callContract(
          from: ownAddress,
          contract: contract,
          function: transfer,
          maxGas: gasClaim,
          nonce: nonce + 1,
          gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
          parameters: [contractAddrLike, amount.getInWei, BigInt.from(10)],
        ),
        chainId: chainId,
      );
//  String transactionApprove='';
      return transactionApprove + ":" + transactionLock;
    } else {
      if (amount.getInWei > approved.getInWei) {
        BigInt secondAmount = amount.getInWei - approved.getInWei;
        print(secondAmount);
        amount = approved;
        final transactionLock = await client.sendTransaction(
          credentials,
          Transaction.callContract(
            from: ownAddress,
            contract: contract,
            function: transfer,
            maxGas: gasClaim,
            nonce: nonce,
            gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
            parameters: [contractAddrLike, amount.getInWei, BigInt.from(10)],
          ),
          chainId: chainId,
        );

        final transferApprove = contractApprove.function('approve');

        final transactionApprove = await client.sendTransaction(
          credentials,
          Transaction.callContract(
            from: ownAddress,
            contract: contractApprove,
            function: transferApprove,
            maxGas: gasClaim,
            nonce: nonce + 1,
            gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
            parameters: [
              contractMessage,
              EtherAmount.fromUnitAndValue(
                      EtherUnit.wei, secondAmount.toString())
                  .getInWei
            ],
          ),
          chainId: chainId,
        );

        final transactionLock2 = await client.sendTransaction(
          credentials,
          Transaction.callContract(
            from: ownAddress,
            contract: contract,
            function: transfer,
            maxGas: gasClaim,
            nonce: nonce + 2,
            gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
            parameters: [
              contractAddrLike,
              EtherAmount.fromUnitAndValue(
                      EtherUnit.wei, secondAmount.toString())
                  .getInWei,
              BigInt.from(10)
            ],
          ),
          chainId: chainId,
        );
        return transactionApprove + ":" + transactionLock2;
      } else {
        //ลองลบ approve
        final transactionLock = await client.sendTransaction(
          credentials,
          Transaction.callContract(
            from: ownAddress,
            contract: contract,
            function: transfer,
            maxGas: gasClaim,
            nonce: nonce,
            gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
            parameters: [contractAddrLike, amount.getInWei, BigInt.from(10)],
          ),
          chainId: chainId,
        );
        return 'no' + ":" + transactionLock;
      }
    }
  }

  //ฟังก์ชั่นสำหรับคำนวนเงินกู้ได้สูงสุด
  @override
  Future<String> getInterestV2({required String address}) async {
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });

    //เรียก contract Loan
    final loan = DeployedContract(
        ContractAbi.fromJson(abiContractLoan.toString(), 'Loan'), contractLoan);
    //เรียกฟังก์ชั่น maxumumBorrow
    final maximum = loan.function('maximumBorrow');
    //สั่งเรียก maximumBorrow และได้ผลลัพธ์กลับมา
    final callMaximum = await client.call(
        sender: new EthereumAddress.fromHex(address),
        contract: loan,
        function: maximum,
        params: [contractAddrLike, contractLock]);

    var maximumBorrow =
        EtherAmount.inWei(callMaximum.first as BigInt).getInEther;
    return maximumBorrow.toString();
  }

  //ฟังก์ชั่นเริ่มสัญญากู้ยืม
  @override
  Future<String> borrowV2({required String pk, required String value}) async {
    print('start borrowV2');
    print(value);
    try {
      final amount = EtherAmount.fromUnitAndValue(EtherUnit.ether, value);

      print('connect web3');
      final client = Web3Client(rpcUrl, Client(), socketConnector: () {
        return IOWebSocketChannel.connect(wsUrl).cast<String>();
      });
      final credentials = await client.credentialsFromPrivateKey(pk);
      final ownAddress = await credentials.extractAddress();

      print('connect contract like');
      final contractApprove = DeployedContract(
          ContractAbi.fromJson(abiContract.toString(), 'LIKEPOINT'),
          contractAddrLike);
      print(amount.getInWei);

      //ตรวจสอบการ allowance ก่อนว่า มีการ allowance  ค้างไว้ไหม
      print('checking allower');
      final allower = contractApprove.function('allowance');
      final balanceApprove = await client
          .call(contract: contractApprove, function: allower, params: [
        new EthereumAddress.fromHex(ownAddress.toString()),
        new EthereumAddress.fromHex(contractLoan.toString())
      ]);

      print('balance approve ' + balanceApprove.toString());

      var approved = EtherAmount.inWei(balanceApprove.first as BigInt);
      int nonce = await client.getTransactionCount(ownAddress);

      //loan
      final contract = DeployedContract(
          ContractAbi.fromJson(abiContractLoan.toString(), 'Loan'),
          contractLoan);

      final initLoan = contract.function('initLoan');
      if (approved.getInWei ==
          EtherAmount.fromUnitAndValue(EtherUnit.wei, '0').getInWei) {
        final transferApprove = contractApprove.function('approve');
        final transactionApprove = await client.sendTransaction(
          credentials,
          Transaction.callContract(
              from: ownAddress,
              contract: contractApprove,
              function: transferApprove,
              maxGas: gasClaim,
              nonce: nonce,
              gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
              parameters: [
                contractLoan,
                EtherAmount.inWei(BigInt.parse(
                        '0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff'))
                    .getInWei
              ]),
          chainId: chainId,
        );

        final transactionInitLoan = await client.sendTransaction(
          credentials,
          Transaction.callContract(
            from: ownAddress,
            contract: contract,
            function: initLoan,
            nonce: nonce + 1,
            maxGas: gasClaim,
            gasPrice: EtherAmount.inWei(BigInt.from(10 * 1e9)),
            parameters: [contractAddrLike, contractLock, amount.getInWei],
          ),
          chainId: chainId,
        );

        print('tx 1' + transactionApprove.toString());
        print('tx loan ' + transactionInitLoan.toString());
        return transactionInitLoan;
      } else {
        final transactionInitLoan = await client.sendTransaction(
          credentials,
          Transaction.callContract(
            from: ownAddress,
            contract: contract,
            function: initLoan,
            maxGas: gasClaim,
            gasPrice: EtherAmount.inWei(BigInt.from(10 * 1e9)),
            parameters: [contractAddrLike, contractLock, amount.getInWei],
          ),
          chainId: chainId,
        );
        print('tx loan ' + transactionInitLoan.toString());
        return transactionInitLoan;
      }
    } catch (e) {
      print('something crash');
      print(e);
      return 'error';
    }
  }

  Future<List<dynamic>> getLoanV2({required String address}) async {
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });

    //เรียก contract Loan
    final loan = DeployedContract(
        ContractAbi.fromJson(abiContractLoan.toString(), 'Loan'), contractLoan);
    //เรียกฟังก์ชั่น maxumumBorrow
    final loanContract = loan.function('contractLoans');
    //สั่งเรียก maximumBorrow และได้ผลลัพธ์กลับมา
    final callLoanContract = await client.call(
//        sender: new EthereumAddress.fromHex(address),
        contract: loan,
        function: loanContract,
        params: [new EthereumAddress.fromHex(address)]);

//    print(callLoanContract);

    //callback //array 10 ตำแหน่ง ["0address คนยืม", "1.วลายืม", "2.อัตราดอกเบี้ย", "3.เวลาสิ้นสุดสัญญา", "4หนี้คงเหลือ", "5. ดอกเบี้ยที่จ่ายแล้ว", "6. จำนวนเงินต้น", "7. หลักประกัน", "8. จ่ายเงินต้นมาแล้วทั้งหมด", "9. จ่ายดอกเบี้ยมาแล้วทั้งหมด", "10. สถานะการกู้ยืม 1 ยังกู้อยู่ 0 หมดสัญญาแล้ว"]
    return callLoanContract;
  }

  //function สำหรับการชำระหนี้คืนทั้งหมด ดอกเบี้ยจะคำนวนเองใน contract
  @override
  Future<String> repayAllV2({required String pk}) async {
    print('start repayAllV2');
    try {
      print('connect web3');
      final client = Web3Client(rpcUrl, Client(), socketConnector: () {
        return IOWebSocketChannel.connect(wsUrl).cast<String>();
      });
      final credentials = await client.credentialsFromPrivateKey(pk);
      final ownAddress = await credentials.extractAddress();

      print('connect contract like');
      final contractA = DeployedContract(
          ContractAbi.fromJson(abiContractLoan.toString(), 'Loan'),
          contractLoan);

      int nonce = await client.getTransactionCount(ownAddress);

      final rePayAll = contractA.function('rePayAll');

      //check approve
      final contractApprove = DeployedContract(
          ContractAbi.fromJson(abiContract.toString(), 'LIKEPOINT'),
          contractAddrLike);
      //ตรวจสอบการ allowance ก่อนว่า มีการ allowance  ค้างไว้ไหม
      final allower = contractApprove.function('allowance');
      final balanceApprove = await client
          .call(contract: contractApprove, function: allower, params: [
        new EthereumAddress.fromHex(ownAddress.toString()),
        new EthereumAddress.fromHex(contractLock.toString())
      ]);
      var approved = EtherAmount.inWei(balanceApprove.first as BigInt);
      if (approved.getInWei ==
          EtherAmount.fromUnitAndValue(EtherUnit.wei, '0').getInWei) {
        final transferApprove = contractApprove.function('approve');
        final transactionApprove = await client.sendTransaction(
          credentials,
          Transaction.callContract(
              from: ownAddress,
              contract: contractApprove,
              function: transferApprove,
              maxGas: gasClaim,
              nonce: nonce,
              gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
              parameters: [
                contractLock,
                EtherAmount.inWei(BigInt.parse(
                        '0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff'))
                    .getInWei
              ]
//            parameters: [contractLock, amount.getInWei],
              ),
          chainId: chainId,
        );
        //pay
        final transactionRePayAll = await client.sendTransaction(
          credentials,
          Transaction.callContract(
            from: ownAddress,
            contract: contractA,
            function: rePayAll,
            nonce: nonce + 1,
            maxGas: gasClaim,
            gasPrice: EtherAmount.inWei(BigInt.from(10 * 1e9)),
            parameters: [contractAddrLike, contractLock],
          ),
          chainId: chainId,
        );
        print('tx repayall ' + transactionRePayAll.toString());
        return transactionRePayAll;
      } else {
        final transactionRePayAll = await client.sendTransaction(
          credentials,
          Transaction.callContract(
            from: ownAddress,
            contract: contractA,
            function: rePayAll,
            nonce: nonce,
            maxGas: gasClaim,
            gasPrice: EtherAmount.inWei(BigInt.from(10 * 1e9)),
            parameters: [contractAddrLike, contractLock],
          ),
          chainId: chainId,
        );
        print('tx repayall ' + transactionRePayAll.toString());
        return transactionRePayAll;
      }
    } catch (e) {
      print('something crash');
      print(e);
      return 'error';
    }
  }

  //ชำระหนี้คืน
  @override
  Future<String> repayV2({required String pk, required String value}) async {
    print('start repayV2');
    try {
      final amount = EtherAmount.fromUnitAndValue(EtherUnit.ether, value);

      print('connect web3');
      final client = Web3Client(rpcUrl, Client(), socketConnector: () {
        return IOWebSocketChannel.connect(wsUrl).cast<String>();
      });
      final credentials = await client.credentialsFromPrivateKey(pk);
      final ownAddress = await credentials.extractAddress();

      print('connect contract like');
      final contractA = DeployedContract(
          ContractAbi.fromJson(abiContractLoan.toString(), 'Loan'),
          contractLoan);

      int nonce = await client.getTransactionCount(ownAddress);

      final rePay = contractA.function('rePay');

      //check approve
      final contractApprove = DeployedContract(
          ContractAbi.fromJson(abiContract.toString(), 'LIKEPOINT'),
          contractAddrLike);
      //ตรวจสอบการ allowance ก่อนว่า มีการ allowance  ค้างไว้ไหม
      final allower = contractApprove.function('allowance');
      final balanceApprove = await client
          .call(contract: contractApprove, function: allower, params: [
        new EthereumAddress.fromHex(ownAddress.toString()),
        new EthereumAddress.fromHex(contractLock.toString())
      ]);
      var approved = EtherAmount.inWei(balanceApprove.first as BigInt);
      if (approved.getInWei ==
          EtherAmount.fromUnitAndValue(EtherUnit.wei, '0').getInWei) {
        final transferApprove = contractApprove.function('approve');
        final transactionApprove = await client.sendTransaction(
          credentials,
          Transaction.callContract(
              from: ownAddress,
              contract: contractApprove,
              function: transferApprove,
              maxGas: gasClaim,
              nonce: nonce,
              gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
              parameters: [
                contractLock,
                EtherAmount.inWei(BigInt.parse(
                        '0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff'))
                    .getInWei
              ]
//            parameters: [contractLock, amount.getInWei],
              ),
          chainId: chainId,
        );
        //pay
        //pay
        final transactionRePayAll = await client.sendTransaction(
          credentials,
          Transaction.callContract(
            from: ownAddress,
            contract: contractA,
            function: rePay,
            nonce: nonce + 1,
            maxGas: gasClaim,
            gasPrice: EtherAmount.inWei(BigInt.from(10 * 1e9)),
            parameters: [contractAddrLike, contractLock, amount.getInWei],
          ),
          chainId: chainId,
        );
        print('tx repayall ' + transactionRePayAll.toString());
        return transactionRePayAll;
      } else {
        //pay
        final transactionRePayAll = await client.sendTransaction(
          credentials,
          Transaction.callContract(
            from: ownAddress,
            contract: contractA,
            function: rePay,
            maxGas: gasClaim,
            gasPrice: EtherAmount.inWei(BigInt.from(10 * 1e9)),
            parameters: [contractAddrLike, contractLock, amount.getInWei],
          ),
          chainId: chainId,
        );
        return transactionRePayAll;
      }
    } catch (e) {
      print('something crash');
      print(e);
      return 'error';
    }
  }

  //ดึงดอกเบี้ยและ สัญญา
  @override
  Future<dynamic> getCurrentDeptV2({required String address}) async {
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });

    //เรียก contract Loan
    final loan = DeployedContract(
        ContractAbi.fromJson(abiContractLoan.toString(), 'Loan'), contractLoan);
    //เรียกฟังก์ชั่น maxumumBorrow
    final loanContract = loan.function('getCurrentDept');
    //สั่งเรียก maximumBorrow และได้ผลลัพธ์กลับมา
    final callLoanContract = await client.call(
//        sender: new EthereumAddress.fromHex(address),
        contract: loan,
        function: loanContract,
        params: [new EthereumAddress.fromHex(address)]);

    print(callLoanContract);
    //callback1 //array 10 ตำแหน่ง ["0address คนยืม", "1.วลายืม", "2.อัตราดอกเบี้ย", "3.เวลาสิ้นสุดสัญญา", "4หนี้คงเหลือ", "5. ดอกเบี้ยที่จ่ายแล้ว", "6. จำนวนเงินต้น", "7. หลักประกัน", "8. จ่ายเงินต้นมาแล้วทั้งหมด", "9. จ่ายดอกเบี้ยมาแล้วทั้งหมด", "10. สถานะการกู้ยืม 1 ยังกู้อยู่ 0 หมดสัญญาแล้ว"]
    //callback2 interest
    return callLoanContract;
  }

  @override
  Future<String> getAPY() async {
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });

    //เรียก contract Loan
    final loan = DeployedContract(
        ContractAbi.fromJson(abiContractLoan.toString(), 'Loan'), contractLoan);
    //เรียกฟังก์ชั่น maxumumBorrow
    final loanContract = loan.function('getAPY');
    //สั่งเรียก maximumBorrow และได้ผลลัพธ์กลับมา
    final callLoanContract = await client.call(
//        sender: new EthereumAddress.fromHex(address),
        contract: loan,
        function: loanContract,
        params: []);

    print(callLoanContract);
    //callback1 //array 10 ตำแหน่ง ["0address คนยืม", "1.วลายืม", "2.อัตราดอกเบี้ย", "3.เวลาสิ้นสุดสัญญา", "4หนี้คงเหลือ", "5. ดอกเบี้ยที่จ่ายแล้ว", "6. จำนวนเงินต้น", "7. หลักประกัน", "8. จ่ายเงินต้นมาแล้วทั้งหมด", "9. จ่ายดอกเบี้ยมาแล้วทั้งหมด", "10. สถานะการกู้ยืม 1 ยังกู้อยู่ 0 หมดสัญญาแล้ว"]
    //callback2 interest
    return callLoanContract.toString();
  }
}
