import 'dart:async';
import 'dart:async' as prefix0;
import 'dart:convert';

import 'dart:io';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:http/http.dart' as http;
import 'package:likewallet/app_config.dart';
import 'package:likewallet/libraryman/crypto.dart';
import 'package:likewallet/quickpay/favorites/list.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:likewallet/model/promtpay.dart';

import 'package:likewallet/model/listgame.dart';


abstract class AbstractServiceHTTP {
  Future<String> getFee(String address);
  Future<bool> checkTransferAccount(
      {String address, String phone_number, String token});
  Future<double> getCurrentFee({required String currency});
  Future<List> getQuickpayShop();
  Future<List> listSpendkpayShop();

  Future<String> getVersionApp();
  Future<List> createOrderPromptpay(_token, walletNumber, address, amount);
  Future<String> penddingPromptpayBuy(
      {token,
      partnerTxnUid,
      requestDt,
      merchantId,
      terminalId,
      qrType,
      origPartnerTxnUid});
  Future<String> claimSlotMachine({token, id});
  Future<String> getSellAddress({String bank});
  Future<String> getUpdateiOS();
  Future<List?> getGame();
  Future<String> getCovidURL();
  Future<String> checkEmailExists(
      {String email, String password, String token});
  Future<String> checkSync({String token});
  Future<Map<String, String>> checkTwoFA({String token});
  Future<Map<String, String>> enableTwoFA({required String token, required String secret, required String otp});
  Future<Map<String, String>> disableTwoFA({required String token, required String otp});
  Future<Map<String, String>> verifyTwoFA({required String token, required String otp});
  Future<String> callMnemonic({required String token});
}

class ServiceHTTP implements AbstractServiceHTTP {
  @override
  Future<String> getFee(String address) async {
    var url = Uri.https(env.apiFee, '/sendETH');

    var response = await http.post(url, body: {'address': address});

    var body = response.body;

    return body.toString();
  }

  @override
  Future<double> getCurrentFee({required String currency}) async {
    print('currency : '+ currency);
    var url = Uri.https(env.apiUrl,'/getCurrentFeeNew', { 'currency': currency});
    var response = await http.get(url);
    var body = json.decode(response.body);
    return double.parse(body['fee'].toString());
  }

  @override
  Future<String> getUpdateiOS() async {
    var url = Uri.https(env.apiUrl, '/getUpdateiOS');
    var response = await http.post(url, body: {});
    if (response.statusCode == 200) {
      var bodyParse = json.decode(response.body);
      String address = bodyParse["result"].toString();
      return address;
    } else {
      return 'failed';
    }
  }

  @override
  Future<String> getVersionApp() async {
    final fireStore = FirebaseFirestore.instance;
    var version;
    if (Platform.isAndroid) {
      version = await fireStore.collection('version').doc('android').get();
    } else {
      version = await fireStore.collection('version').doc('ios').get();
    }
    return version.data()['version'];
  }

  @override
  Future<bool> checkTransferAccount(
      {String? address, String? phone_number, String? token}) async {
    var urlcheckPhoneNumber = Uri.https(env.apiUrl, '/getPhoneFromToken');

    var getPhone = await http.post(urlcheckPhoneNumber, body: {'token': token});

    var bodyPhone = json.decode(getPhone.body);

    // print(bodyPhone['result']);

    if (bodyPhone['statusCode'] == 200) {
      var url2 = Uri.https(env.OldAPI, '/checkOldUser');

      print('checkTransfer');
      var response2 = await http.post(url2, body: {'phone_number': bodyPhone['result'].toString()});

      var body2 = json.decode(response2.body);

      if (body2['statusCode'] == 200) {
        var url = Uri.https(env.OldAPI, '/checkTransfer');
        var response = await http.post(url, body: {'address': address});

        var body = json.decode(response.body);
        // print(body);
        if (body['statusCode'] == 200) {
          print('address' + body['result']);
          print(body["result"]);
          print(address);
          if (body["result"] == address) {
            return false;
          } else {
            return true;
          }
        } else {
          return true;
        }
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  @override
  Future<List> getQuickpayShop() async {
    print('getQuickayShop');
    List group = [];
    List<list> search = [];
    List<list> _list = [];
    var url = Uri.https(env.apiUrl, '/listQucikpayShop');
    var response = await http.post(url, body: {
      "apiKey": env.APIKEY,
      "secretKey": env.SECRETKEY,
    });
    if (response.statusCode == 200) {
      var body = json.decode(response.body);
      // print(response.body);
      for (Map<String, dynamic> user in body["result"]) {
        _list.add(list.fromJson(user));
        search = _list;
      }
      for (var i = 0; i < body["group"].length; i++) {
        group.add(body["group"][i]);
      }
    }

    return [_list, group];
  }

  @override
  Future<List> listSpendkpayShop() async {
    print('listSpendkpayShop');
    List group = [];
    List<list> search = [];
    List<list> _list = [];
    var url = Uri.https(env.apiUrl, '/listSpendkpayShop');
    var response = await http.post(url, body: {
      "apiKey": env.APIKEY,
      "secretKey": env.SECRETKEY,
    });
    if (response.statusCode == 200) {
      var body = json.decode(response.body);
      // print(response.body);
      for (Map<String, dynamic> user in body["result"]) {
        _list.add(list.fromJson(user));
        search = _list;
      }
      for (var i = 0; i < body["group"].length; i++) {
        group.add(body["group"][i]);
      }
    }

    return [_list, group];
  }

  @override
  Future<List?> getGame() async {
    print('getGame');
    List<listgame> search = [];
    List<listgame> _list = [];
    var url = Uri.https(env.apiUrl, '/listGame');
    var response = await http.post(url, body: {
      "apiKey": env.APIKEY,
      "secretKey": env.SECRETKEY,
    });
    if (response.statusCode == 200) {
      // print(response.body.toString());
      var body = json.decode(response.body);
      for (Map<String, dynamic> user in body["result"]) {
        _list.add(listgame.fromJson(user));
        search = _list;
      }
    }

    return _list;
  }

  @override
  Future<List> createOrderPromptpay(
      _token, walletNumber, address, amount) async {
    List<PromptPayBuy> prompt = [];
    List<PromptPayBuy> _list = [];
    var url = Uri.https(env.apiUrl, '/createOrderBuy');
    var response = await http.post(url, body: {
      "_token": _token,
      "walletNumber": walletNumber,
      "address": address,
      "amount": amount.toString()
    });
    if (response.statusCode == 200) {
      var bodyParse = json.decode(response.body);

//     print(bodyParse);
      final bodyInCast = bodyParse["result"].cast<Map<String, dynamic>>();
      prompt = bodyInCast
          .map<PromptPayBuy>((json) => PromptPayBuy.fromJson(json))
          .toList();
    } else {
      prompt = [];
    }

    return prompt;
  }

  @override
  Future<String> penddingPromptpayBuy(
      {token,
      partnerTxnUid,
      requestDt,
      merchantId,
      terminalId,
      qrType,
      origPartnerTxnUid}) async {
    var url = Uri.https(env.apiUrl, '/inquiryBuy');
    var response = await http.post(url, body: {
      "_token": token,
      "partnerTxnUid": partnerTxnUid,
      "requestDt": requestDt,
      "merchantId": merchantId,
      "terminalId": terminalId,
      "qrType": qrType,
      "origPartnerTxnUid": origPartnerTxnUid
    });
    String paid;
    if (response.statusCode == 200) {
      var bodyParse = json.decode(response.body);
      paid = bodyParse['result']['txnStatus'];
    } else {
      paid = 'error';
    }

    return paid;
  }

  @override
  Future<String> claimSlotMachine({token, id}) async {
    var url = Uri.https(env.apiUrl, '/claimPrizeSlot');
    var response = await http.post(url, body: {"_token": token, "id": id});
    if (response.statusCode == 200) {
      var bodyParse = json.decode(response.body);
      var jsonDecode = json.decode(bodyParse["result"]);
      final tx = jsonDecode["tx"];
      return tx;
    } else {
      return 'failed';
    }
  }

  @override
  Future<String> getSellAddress({String? bank}) async {
    var url = Uri.https(env.apiUrl, '/getSellAddressNew');
    var response = await http.post(url, body: {'bank': bank});
    if (response.statusCode == 200) {
      var bodyParse = json.decode(response.body);
      String address = bodyParse["result"].toString();
      return address;
    } else {
      return 'failed';
    }
  }

  @override
  Future<String> getCovidURL() async {
    var url = Uri.https(env.apiUrl, '/getCovid');
    var response = await http.post(url, body: {});
    if (response.statusCode == 200) {
      var bodyParse = json.decode(response.body);
      String address = bodyParse["result"].toString();
      return address;
    } else {
      return 'failed';
    }
  }

  @override
  Future<String> checkEmailExists(
      {String? email, String? password, String? token}) async {
    var url = Uri.https(env.apiUrl, '/checkEmailExists');
    var response = await http.post(url ,
        body: {"email": email, "password": password, "_token": token});
    if (response.statusCode == 200) {
      var bodyParse = json.decode(response.body);
      String exists = bodyParse["result"].toString();
      print(exists);
      if (exists == 'true') {
        return 'true';
      } else if (exists == 'update') {
        return 'update';
      } else {
        return 'false';
      }
    } else {
      return 'false';
    }
  }

  @override
  Future<String> checkSync({String? token}) async {
    var url = Uri.https(env.apiUrl, '/checkSync');
    var response = await http.post(url, body: {"_token": token});
    if (response.statusCode == 200) {
      var bodyParse = json.decode(response.body);
      String status = bodyParse["statusCode"].toString();
      String result = bodyParse["result"].toString();
      print(status);
      if (status == '200') {
        return result;
      } else {
        return 'no';
      }
    } else {
      return 'no';
    }
  }



  @override
  Future<Map<String, String>> checkTwoFA({String? token}) async {
    var url = Uri.https(env.apiUrl, '/checkTwoFA');
    var response = await http.post(url, body: {"token": token});
    if (response.statusCode == 200) {
      var bodyParse = json.decode(response.body);
      String status = bodyParse["statusCode"].toString();
      String result = bodyParse["result"]["result"].toString();
      String secret = bodyParse["result"]["newSecret"]["secret"].toString();
      String uri = bodyParse["result"]["newSecret"]["uri"].toString();
      String qr = bodyParse["result"]["newSecret"]["qr"].toString();

      if (status == '200') {
        var callback = {
          'result': result,
          'secret': secret,
          'uri': uri,
          'qr': qr
        };
        return callback;
      } else {
        var callback = {
          'result': 'no',
        };
        return callback;
      }
    } else {
      var callback = {
        'result': 'no',
      };
      return callback;
    }
  }

  @override
  Future<Map<String, String>> enableTwoFA({required String token, required String secret, required String otp}) async {
    var url = Uri.https(env.apiUrl, '/enableTwoFA');
    var response = await http.post(url, body: {"token": token, 'secret': secret, 'otp': otp});
    if (response.statusCode == 200) {
      var bodyParse = json.decode(response.body);
      String status = bodyParse["statusCode"].toString();
      String result = bodyParse["result"]["result"].toString();

      if (status == '200') {
        var callback = {
          'result': result,
        };
        return callback;
      } else {
        var callback = {
          'result': 'no',
        };
        return callback;
      }
    } else {
      var callback = {
        'result': 'no',
      };
      return callback;
    }
  }

  @override
  Future<Map<String, String>> disableTwoFA({required String token, required String otp}) async {
    var url = Uri.https(env.apiUrl, '/disableTwoFA');
    var response = await http.post(url, body: {"token": token, 'secret': '', 'otp': otp});
    if (response.statusCode == 200) {
      var bodyParse = json.decode(response.body);
      String status = bodyParse["statusCode"].toString();
      String result = bodyParse["result"]["result"].toString();

      if (status == '200') {
        var callback = {
          'result': result,
        };
        return callback;
      } else {
        var callback = {
          'result': 'no',
        };
        return callback;
      }
    } else {
      var callback = {
        'result': 'no',
      };
      return callback;
    }
  }

  @override
  Future<Map<String, String>> verifyTwoFA({required String token, required String otp}) async {
    var url = Uri.https(env.apiUrl, '/verifyTwoFA');
    var response = await http.post(url, body: {"token": token, 'secret': '', 'otp': otp});
    if (response.statusCode == 200) {
      var bodyParse = json.decode(response.body);
      String status = bodyParse["statusCode"].toString();
      String result = bodyParse["result"]["result"].toString();

      if (status == '200') {
        var callback = {
          'result': result,
        };
        return callback;
      } else {
        var callback = {
          'result': 'no',
        };
        return callback;
      }
    } else {
      var callback = {
        'result': 'no',
      };
      return callback;
    }
  }

  @override
  Future<String> callMnemonic({required String token}) async {
    var url = Uri.https(env.apiUrl, '/signInWithTokenByApp');
    late CryptoEncryptInterface encrypt;
    encrypt = new CryptoEncrypt();
    var key = await encrypt.getKeyEncrypt();
    var response = await http.post(url,
        body: {
          "token": token,
          "keyEncrypt": key[0],
          "ivEncrypt": key[1],
        });
    if (response.statusCode == 200) {
      var bodyParse = json.decode(response.body);
      String status = bodyParse["statusCode"].toString();
      String callback = bodyParse["seed"].toString();

      if (status == '200') {
        String mnemonic = await encrypt.decrypt(callback);
        return mnemonic;
      } else {
        return 'no';
      }
    } else {
      return 'no';
    }
  }
}
