
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/animationPage.dart';
import 'package:likewallet/libraryman/setPin.dart';
import 'package:likewallet/screen_util.dart';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:likewallet/libraryman/setPin.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:likewallet/login/sing_in.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:likewallet/libraryman/crypto.dart';

class SecretPassFirst extends StatefulWidget {
  SecretPassFirst(
      {this.roundSMS,
      this.codeVerify,
      this.phoneNumber,
      this.firstName,
      this.lastName,
      this.checkIf,
      this.refCode,
      this.numberID});

  final providerSMS? roundSMS;
  final String? checkIf;
  final String? codeVerify;
  final String? phoneNumber;
  final String? firstName;
  final String? lastName;
  final String? refCode;
  final String? numberID;
  @override
  State<StatefulWidget> createState() => new _SecretPassFirst(
      roundSMS: roundSMS,
      codeVerify: codeVerify,
      phoneNumber: phoneNumber,
      firstName: firstName,
      lastName: lastName,
      checkIf: checkIf,
      refCode: refCode,
      numberID: numberID);
}

setToken(String _token) async {
  SharedPreferences pref = await SharedPreferences.getInstance();
  pref.setString("_token", _token);
  return true;
}

enum FormType { login, register }

class _SecretPassFirst extends State<SecretPassFirst> {
  _SecretPassFirst(
      {this.roundSMS,
      this.codeVerify,
      this.phoneNumber,
      this.firstName,
      this.lastName,
      this.checkIf,
      this.refCode,
      this.numberID});

  final String? checkIf;
  final providerSMS? roundSMS;
  final String? codeVerify;
  final String? phoneNumber;
  final String? firstName;
  final String? lastName;
  final String? refCode;
  final String? numberID;

  late CryptoEncryptInterface Encrypt;
  TextEditingController _secret = TextEditingController();
  TextEditingController _idcard = TextEditingController();

  void toChoice() async {

//    _onRemember();
    final storage = new FlutterSecureStorage();
    String randomString = "LikeWallet";
    await storage.write(key: 'secretpass', value: 'LikeWallet');
    print(roundSMS);
    Navigator.push(
      context,
      EnterExitRoute(
          exitPage: SecretPassFirst(),
          enterPage: SetPin(
            refCode: refCode,
            firstName: firstName,
            lastName: lastName,
            checkIf: checkIf,
            secret: 'LikeWallet',
            roundSMS: roundSMS,
            codeVerify: codeVerify,
            phoneNumber: phoneNumber,
            numberID: numberID ?? _idcard.text,
            pinAgain: false,
          )),
    );

  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }


  Future<dynamic> _onRemember() async {
    return showDialog(
      context: context,
      builder: (context) => new AlertDialog(
        title: new Text(
          AppLocalizations.of(context)!.translate('remember_secret_title'),
          style: TextStyle(
              color: Colors.black,
              fontFamily: AppLocalizations.of(context)!.translate('font2'),
              fontWeight: FontWeight.normal,
              fontStyle: FontStyle.normal,
              fontSize: mediaQuery(context, "height", 45)),
        ),
        content: new Text(
          AppLocalizations.of(context)!.translate('remember_secret_detail'),
          style: TextStyle(
              color: Colors.black,
              fontFamily: AppLocalizations.of(context)!.translate('font2'),
              fontWeight: FontWeight.normal,
              fontStyle: FontStyle.normal,
              fontSize: mediaQuery(context, "height", 45)),
        ),
        actions: <Widget>[
          ElevatedButton(
            onPressed: () async {
              final storage = new FlutterSecureStorage();
              String randomString = "LikeWallet";
              await storage.write(key: 'secretpass', value: 'LikeWallet');
              print(roundSMS);
              Navigator.push(
                context,
                EnterExitRoute(
                    exitPage: SecretPassFirst(),
                    enterPage: SetPin(
                      refCode: refCode,
                      firstName: firstName,
                      lastName: lastName,
                      checkIf: checkIf,
                      secret: 'LikeWallet',
                      roundSMS: roundSMS,
                      codeVerify: codeVerify,
                      phoneNumber: phoneNumber,
                      numberID: numberID ?? _idcard.text,
                      pinAgain: false,
                    )),
              );
            },
            child: new Text(
              AppLocalizations.of(context)!.translate('close_app_yes'),
              style: TextStyle(
                  color: Colors.black,
                  fontFamily:
                  AppLocalizations.of(context)!.translate('font2'),
                  fontWeight: FontWeight.normal,
                  fontStyle: FontStyle.normal,
                  fontSize: mediaQuery(context, "height", 45)),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return new Scaffold(
        resizeToAvoidBottomInset: false,
        // resizeToAvoidBottomPadding: false,
        backgroundColor: Colors.black,
        body: GestureDetector(
            onTap: () {
              FocusScopeNode currentFocus = FocusScope.of(context);
              if (!currentFocus.hasPrimaryFocus) {
                currentFocus.unfocus();
              }
            },
            child: Stack(
              alignment: Alignment.center,
              children: <Widget>[
                background_image(context),
//                Positioned(
//                    top: mediaQuery(context, 'height', 302), child: _title()),
//                Positioned(
//                    top: mediaQuery(context, 'height', 458), child: _input()),
                Positioned(
                    top: mediaQuery(context, 'height', 797), child: _title2()),
                Positioned(
                    top: mediaQuery(context, 'height', 992),
                    child: _input_idcard()),
                Positioned(
                    top: mediaQuery(context, 'height', 1245), child: _button()),
              ],
            )));
  }

  Widget _title() {
    return new Text(
      AppLocalizations.of(context)!.translate('secret_title1'),
      textAlign: TextAlign.center,
      style: TextStyle(
          fontFamily: AppLocalizations.of(context)!.translate('font1'),
          color: LikeWalletAppTheme.gray,
          fontSize: mediaQuery(context, 'height', 40)),
    );
  }

  Widget _input() {
    return Container(
      width: mediaQuery(context, 'width', 930),
      height: mediaQuery(context, 'height', 158),
      decoration: BoxDecoration(
          color: LikeWalletAppTheme.bule2,
          border: Border.all(
              width: mediaQuery(context, 'width', 1),
              color: LikeWalletAppTheme.bule3),
          borderRadius: BorderRadius.all(Radius.circular(5.0))),
      child: new TextFormField(
        controller: _secret,
        cursorColor: LikeWalletAppTheme.bule3,
        style: TextStyle(
            fontFamily: AppLocalizations.of(context)!.translate('font1'),
            color: LikeWalletAppTheme.gray1,
            fontSize: mediaQuery(context, 'height', 40)),
        obscureText: true,
        decoration: InputDecoration(
//          focusedBorder: OutlineInputBorder(
//              borderSide: BorderSide(
//                  color: LikeWalletAppTheme.bule1,
//                  width: mediaQuery(context, 'width', 1))),
//          enabledBorder: OutlineInputBorder(
//              borderSide: BorderSide(
//                  width: mediaQuery(context, 'width', 0.3),
//                  color: LikeWalletAppTheme.bule1)),
          contentPadding: EdgeInsets.only(
            left: MediaQuery.of(context).size.width * 0.03,
          ),
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(5.0)),
          hoverColor: Colors.white,
          disabledBorder: InputBorder.none,
          focusColor: Colors.white,
          alignLabelWithHint: true,
          fillColor: Colors.white,
        ),
      ),
    );
  }

  Widget _title2() {
    return Column(
      children: <Widget>[
        Text(
          AppLocalizations.of(context)!.translate('secret_title2'),
          textAlign: TextAlign.center,
          style: TextStyle(
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              color: LikeWalletAppTheme.gray,
              fontSize: mediaQuery(context, 'height', 40)),
        ),
        Text(
          AppLocalizations.of(context)!.translate('id_title1'),
          textAlign: TextAlign.center,
          style: TextStyle(
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              color: LikeWalletAppTheme.gray,
              fontSize: mediaQuery(context, 'height', 40)),
        )
      ],
    );
  }

  Widget _input_idcard() {
    return Container(
      width: mediaQuery(context, 'width', 930),
      height: mediaQuery(context, 'height', 158),
      decoration: BoxDecoration(
          color: LikeWalletAppTheme.bule2,
          border: Border.all(
              width: mediaQuery(context, 'width', 1),
              color: LikeWalletAppTheme.bule3),
          borderRadius: BorderRadius.all(Radius.circular(5.0))),
      child: new TextFormField(
        controller: _idcard,
        cursorColor: LikeWalletAppTheme.bule3,
        style: TextStyle(
            fontFamily: AppLocalizations.of(context)!.translate('font1'),
            fontSize: mediaQuery(context, 'width', 45),
            color: LikeWalletAppTheme.white),
        inputFormatters: [
          LengthLimitingTextInputFormatter(13),
        ],
        decoration: InputDecoration(
          contentPadding: EdgeInsets.only(
            left: MediaQuery.of(context).size.width * 0.03,
          ),
          hintText: AppLocalizations.of(context)!.translate('profile_idcard'),
          hintStyle: TextStyle(
              fontSize: mediaQuery(context, 'height', 47),
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              color: LikeWalletAppTheme.white),
          border: InputBorder.none,
//                  enabledBorder: OutlineInputBorder(
//                      borderSide: BorderSide(
//                          width: mediaQuery(context, 'width', 0.3),
//                          color: LikeWalletAppTheme.bule1)),
          hoverColor: Colors.white,
          disabledBorder: InputBorder.none,
          focusColor: Colors.white,
          alignLabelWithHint: true,
          fillColor: Colors.white,
        ),
      ),
    );
  }

  Widget _button() {
    return ButtonTheme(
      minWidth: mediaQuery(context, 'width', 439.14),
      height: mediaQuery(context, 'height', 132),
      child:TextButton(
        style: ButtonStyle(
          shape: MaterialStateProperty.all<RoundedRectangleBorder>(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(5.0),
            ),
          ),
          backgroundColor: MaterialStateProperty.resolveWith<Color>(
                (Set<MaterialState> states) {
              if (states.contains(MaterialState.disabled)) {
                return Color(0xff00F1E0); // Disabled color
              }
              return Color(0xff00F1E0); // Regular color
            },
          ),
        ),
        onPressed: () {
          FocusScopeNode currentFocus = FocusScope.of(context);
          if (!currentFocus.hasPrimaryFocus) {
            currentFocus.unfocus();
          }
          toChoice();
        },
        child: Text(
          AppLocalizations.of(context)!.translate('secret_button'),
          style: TextStyle(
            fontFamily: AppLocalizations.of(context)!.translate('font'),
            fontSize: mediaQuery(context, 'height', 51),
            color: Colors.black,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
}
