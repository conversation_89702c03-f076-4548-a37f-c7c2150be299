import 'dart:async';
import 'package:flutter/material.dart';
import 'package:likewallet/libraryman/app_local.dart';

import 'package:likewallet/libraryman/setPin.dart';
import 'package:likewallet/login/sing_in.dart';
import 'package:likewallet/passcode_screen/keyboard.dart';
import 'package:likewallet/passcode_screen/passcode_screen.dart';

import 'package:shared_preferences/shared_preferences.dart';
import 'package:local_auth/local_auth.dart';
import 'package:likewallet/screen/choice_user.dart';

class ConfirmSetPin extends StatefulWidget {
  ConfirmSetPin(
      {this.refCode,
        this.firstName,
        this.lastName,
        this.passcode,
        this.checkIf,
        this.secret,
        this.roundSMS,
        this.codeVerify,
        this.phoneNumber,
        this.numberID});

  final String? checkIf;
  final String? secret;
  final providerSMS? roundSMS;
  final String? phoneNumber;
  final String? codeVerify;
  final String? passcode;
  final String? refCode;
  final String? firstName;
  final String? lastName;
  final String? numberID;

  @override
  _ConfirmSetPin createState() => _ConfirmSetPin(
      checkIf: checkIf,
      passcode: passcode,
      secret: secret,
      roundSMS: roundSMS,
      phoneNumber: phoneNumber,
      codeVerify: codeVerify,
      firstName: firstName,
      lastName: lastName,
      refCode: refCode,
      numberID: numberID);
}

class _ConfirmSetPin extends State<ConfirmSetPin> {
  _ConfirmSetPin(
      {this.refCode,
        this.firstName,
        this.lastName,
        this.passcode,
        this.checkIf,
        this.secret,
        this.roundSMS,
        this.codeVerify,
        this.phoneNumber,
        this.numberID});

  final String? checkIf;
  final String? secret;
  final providerSMS? roundSMS;
  final String? phoneNumber;
  final String? codeVerify;
  final String? passcode;
  final String? firstName;
  final String? lastName;

  final String? numberID;
  final String? refCode;

  final StreamController<bool> _verificationNotifier =
  StreamController<bool>.broadcast();

  bool isAuthenticated = false;
  //biometric
  final LocalAuthentication localAuth = LocalAuthentication();
  late bool _canCheckBiometrics;
  late List<BiometricType> _availableBiometrics;
  String _authorized = 'Not Authorized';
  late bool didAuthenticate;
  late String firstDigit;
  late String secDigit;

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return pinVerify();
  }

  ///pass_code
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  Widget pinVerify() {
    return PasscodeScreen(
      title: AppLocalizations.of(context)!.translate('setpin_again'),
      explain: '',
      type: 'confirmPin',
      passwordEnteredCallback: _onPasscodeEntered,
      cancelLocalizedText: '',
      deleteLocalizedText: '',
      shouldTriggerVerification: _verificationNotifier.stream,
      backgroundColor: Colors.black.withOpacity(0.8),
      cancelCallback: _onPasscodeCancelled,
      keyboardUIConfig: KeyboardUIConfig(
        digitBorderWidth: MediaQuery.of(context).size.height * 0.001,
        digitTextStyle: TextStyle(
            fontSize: MediaQuery.of(context).size.height * 0.035,
            color: Color(0xffFFFFFF)),
        digitSize: MediaQuery.of(context).size.height * 0.1,
        deleteButtonTextStyle: TextStyle(
            fontSize: MediaQuery.of(context).size.height * 0.025,
            color: Color(0xffFFFFFF)),
//
      ),
      circleUIConfig: CircleUIConfig(
//              borderColor: ,
//              fillColor: color,
          circleSize: MediaQuery.of(context).size.height * 0.03),
    );
  }

  accessPass() {
//    AppRoutes.makeFirst(context, HomeLikewallet());
  }
  _onPasscodeCancelled() {}
  _onPasscodeEntered(String enteredPasscode) async {
    if (this.passcode == enteredPasscode) {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setString('pinProtect', enteredPasscode);
      Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) => ChoiceUser(
              refCode: refCode,
              checkIf: checkIf,
              firstName: firstName,
              lastName: lastName,
              secret: secret,
              roundSMS: roundSMS,
              codeVerify: codeVerify,
              phoneNumber: phoneNumber,
              numberID: numberID,
            )),
      );
      // Navigator.pus
//        context,
//        MaterialPageRoute(
//            builder: (context) => ChoiceUser(
//                  refCode: refCode,
//                  checkIf: checkIf,
//                  firstName: firstName,
//                  lastName: lastName,
//                  secret: secret,
//                  roundSMS: roundSMS,
//                  codeVerify: codeVerify,
//                  phoneNumber: phoneNumber,
//                  numberID: numberID,
//                )),
//      );
    } else {
      Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) => SetPin(
                refCode: refCode,
                checkIf: checkIf,
                firstName: firstName,
                lastName: lastName,
                secret: secret,
                roundSMS: roundSMS,
                codeVerify: codeVerify,
                phoneNumber: phoneNumber,
                numberID: numberID,
                pinAgain: true)),
      );
    }
  }

  @override
  void dispose() {
    _verificationNotifier.close();
    super.dispose();
  }
//
}