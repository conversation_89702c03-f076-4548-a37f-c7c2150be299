import 'package:firebase_auth/firebase_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:likewallet/libraryman/crypto.dart';
import 'package:likewallet/libraryman/serviceHTTP.dart';

abstract class IConfigurationService {
  Future<void> setMnemonic(String value);
  Future<void> setupDone(bool value);
  Future<void> setPrivateKey(String value);
  Future<void> setAddress(String value);

  Future<String> getMnemonic();
  String getAddress();
  Future<String> getPrivateKey();
  bool didSetupWallet();
}

class ConfigurationService implements IConfigurationService {
  SharedPreferences _preferences;
  ConfigurationService(this._preferences);

  final storage = FlutterSecureStorage();
  late CryptoEncryptInterface Encrypt;

  @override
  Future<void> setMnemonic(String value) async {
    Encrypt = CryptoEncrypt();
    String mnemonic = await Encrypt.decrypt(value);
    await storage.write(key: 'mnemonic', value: mnemonic);
    await _preferences.setString("mnemonic", mnemonic);
  }

  @override
  Future<void> setPrivateKey(String value) async {
    await storage.write(key: 'privateKey', value: value);
    // await _preferences.setString("privateKey", value);
  }

  @override
  Future<void> setAddress(String value) async {
    // await storage.write(key: 'addressETH', value: value);
    await _preferences.setString("addressETH", value);
  }

  @override
  Future<void> setupDone(bool value) async {
    await _preferences.setBool("didSetupWallet", value);
  }

  // gets
  @override
  Future<String> getMnemonic() async {
    // AbstractServiceHTTP APIHttp = ServiceHTTP();
    // String mnemonic = await APIHttp.callMnemonic(token: await FirebaseAuth.instance.currentUser!.getIdToken());
    // return mnemonic;
    return  _preferences.getString("mnemonic") ?? await storage.read(key: 'mnemonic') ?? 'no';
  }

    // return  _preferences.getString("mnemonic") ?? await storage.read(key: 'mnemonic') ?? 'no';
  // }

  // Future<String> getMnemonic() async {
  //  return  _preferences.getString("mnemonic") ?? await storage.read(key: 'mnemonic') ?? 'no';
  // }

  @override
  Future<String> getPrivateKey() async {
    // return _preferences.getString("privateKey") ?? '';
    return await storage.read(key: 'privateKey') ?? '';
  }

  @override
  String getAddress() {
    return _preferences.getString("addressETH") ?? '';
  }

  @override
  bool didSetupWallet() {
    return _preferences.getBool("didSetupWallet") ?? false;
  }
}
