import 'dart:convert';
import 'dart:ui';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart' as formatIntl;
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:likewallet/libraryman/configuration_service.dart';
import 'package:likewallet/middleware/getLanguage.dart';
import 'package:likewallet/model/poi.dart';
import 'package:likewallet/poi/poi_detail.dart';
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/service/app_service.dart';
import 'package:lottie/lottie.dart';
import 'package:likewallet/app_config.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:rounded_loading_button/rounded_loading_button.dart';
import 'package:shared_preferences/shared_preferences.dart';

class POIHistory extends StatefulWidget {
  _POIHistory createState() => new _POIHistory();
}

class _POIHistory extends State<POIHistory> with TickerProviderStateMixin {
  bool _saving = true;
  bool _switchClaim = false;
  late BaseAuth auth;
  late IConfigurationService configETH;
  late OnLanguage language;
  String addressETH = '';
  String lang = '';
  String uid = '';
  String dataUnlock = '';
  bool popup = false;
  String dateLikeClaim = "";
  int likeClaim = 0;
  int end = 0;
  int start = 1;
  List<PoiM> poiData = [];
  final formatNum = new formatIntl.NumberFormat("###,###.##");

  final RoundedLoadingButtonController _btnGetNewData =
      RoundedLoadingButtonController();
  final RoundedLoadingButtonController _btnClaimAll =
  RoundedLoadingButtonController();
  final RoundedLoadingButtonController _btnLoading =
  RoundedLoadingButtonController();

  @override
  void initState() {
    super.initState();
    auth = new Auth();
    callAddress();
  }

  @override
  void dispose() {
    super.dispose();
  }

  callAddress() async {
    try {
      final user = await auth.getCurrentUser();
      SharedPreferences pref = await SharedPreferences.getInstance();
      configETH = new ConfigurationService(pref);
      setState(() {
        uid = user!.uid;
        addressETH = configETH.getAddress();
      });
      print("configETH =>> $addressETH");
      await getLikeClaim();
      await getHistory();
    } catch (e) {
      Fluttertoast.showToast(
          msg: "Error on getClaimByRunning : ${e.toString()}",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.CENTER,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.red,
          textColor: Colors.white,
          fontSize: 16.0);
    }
  }

  getLikeClaim() async {
    try {
      var url = Uri.parse(env.apiLikepointBCT + '/puzzle/getTimelockable');
      var headers = {
        'x-api-key': 'z2x4VbpEt57VoXKysG8xl2tP45tvOQuy1bl0qErU',
      };
      Map body = {'address': addressETH.toString()};

      final response = await http.post(url, body: body, headers: headers);

      likeClaim = 0;

      // print("XXX =<<< ${response.statusCode}");
      // print("XXX =<<< ${response.body.statusCode}");
      var bodyData = json.decode(response.body);
      if (bodyData['statusCode'] == 200) {
        likeClaim = bodyData['timeunlockable'];
        dateLikeClaim = bodyData['date'];

        print(bodyData);
      }else {
        likeClaim = 0;
      }
    } catch (e) {
      Fluttertoast.showToast(
          msg: "Error on getLikeClaim : ${e.toString()}",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.CENTER,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.red,
          textColor: Colors.white,
          fontSize: 16.0);
    }
  }

  getHistory() async {
    try {
      var url = Uri.parse(
          env.apiLikepointBCT + '/puzzle/getHistoryTimelockByAddress');
      var headers = {
        'x-api-key': 'z2x4VbpEt57VoXKysG8xl2tP45tvOQuy1bl0qErU',
      };
      Map body = {
        'address': addressETH.toString(),
        'end': end.toString(),
        'start': start.toString()
      };
      final response = await http.post(url, body: body, headers: headers);

      if (response.statusCode == 200) {
        var bodyData = json.decode(response.body);
        if (bodyData['result'].length == 0) {
          _btnGetNewData.reset();
        } else {
          setState(() {
            dataUnlock = bodyData['date'];
            poiData.addAll(poiMFromJson(bodyData['result'] as List));
          });
          _btnGetNewData.reset();
        }

        setInit();
      } else {
        Fluttertoast.showToast(
            msg: "Error on getHistory",
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.CENTER,
            timeInSecForIosWeb: 1,
            backgroundColor: Colors.red,
            textColor: Colors.white,
            fontSize: 16.0);
      }
    } catch (e) {
      Fluttertoast.showToast(
          msg: "Error on getHistory : ${e.toString()}",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.CENTER,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.red,
          textColor: Colors.white,
          fontSize: 16.0);
    }
  }

  setInit() async {
    language = CallLanguage();
    lang = await language.getLanguage();
    setState(() {
      _saving = false;
    });
  }

  void showColoredToast(msg, colors) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_LONG,
        backgroundColor: colors,
        textColor: Colors.white);
  }

  void showShortToast(msg) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_SHORT,
        backgroundColor: Colors.cyan,
        textColor: Colors.white);
  }

  @override
  Widget build(BuildContext context) {
    String messageUpcoming =
        AppLocalizations.of(context)!.translate('hourlyRewards_please');
    screenUtil(context);
    return ModalProgressHUD(
      inAsyncCall: _saving,
      opacity: 0.1,
      progressIndicator: CustomLoading(),
      child: Scaffold(
        backgroundColor: Color(0xFF11161E),
        body: SingleChildScrollView(
          child: _saving
              ? Container()
              : Container(
                  decoration: BoxDecoration(
                      gradient: LinearGradient(
                    begin: Alignment.topRight,
                    end: Alignment.bottomLeft,
                    colors: [
                      Color(0xFF071F40),
                      Color(0xFF080B0F),
                      Color(0xFF080B0F),
                      Color(0xFF080B0F),
                    ],
                  )),
                  child: Stack(
                    children: [
                      Column(
                        children: [
                          Container(
                            // height: 410.sp,
                              width: double.infinity,
                              child: Lottie.asset(
                                  'assets/animatedIcons/partner_reward.lottie.json')),
                          SizedBox(height: 35.h),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              SizedBox(
                                width: 120.w,
                              ),
                              SvgPicture.asset(
                                LikeWalletImage.locked_Likepoint_Rewards,
                                fit: BoxFit.contain,
                                height: mediaQuery(context, 'height', 100.47),
                              ),
                              SizedBox(
                                width: 40.w,
                              ),
                              RichText(
                                text: TextSpan(
                                  text:
                                      "${AppLocalizations.of(context)!.translate('Your_Locked_Likepoint_Rewards')} \n",
                                  style: TextStyle(
                                    fontFamily: AppLocalizations.of(context)!
                                        .translate('font4'),
                                    fontSize: 45.sp,
                                    fontWeight: FontWeight.w400,
                                    color: Colors.white,
                                  ),
                                  children: <TextSpan>[
                                    TextSpan(
                                        text: AppLocalizations.of(context)!
                                            .translate(
                                                'from_LikeWallet_Partners'),
                                        style: TextStyle(
                                          fontSize: 38.sp,
                                          color: Color(0xFF738097),
                                          fontWeight: FontWeight.w300,
                                        )),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          SizedBox(
                            height: 40.h,
                          ),
                          Text(
                            AppLocalizations.of(context)!.translate(
                                'Including_total_Locked_Likepoint_accrued'),
                            style: TextStyle(
                              fontFamily: AppLocalizations.of(context)!
                                  .translate('font4'),
                              fontSize: 35.sp,
                              fontWeight: FontWeight.w300,
                              color: Color(0xFF738097),
                            ),
                          ),
                          SizedBox(
                            height: 20.h,
                          ),
                          Container(
                            padding: EdgeInsets.only(left: 50.sp, right: 50.sp),
                            child: Container(
                                padding: EdgeInsets.only(
                                    left: 20.sp, right: 20.sp, top: 20.sp),
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(50.sp),
                                    border: Border.all(
                                        color: Color(0xFF4E5D75), width: 2.sp)),
                                child: Column(
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Container(
                                          margin: EdgeInsets.only(left: 370.w),
                                          child: Column(
                                            children: [
                                              Text(
                                                "${formatNum.format(likeClaim).toString()} LIKE",
                                                style: TextStyle(
                                                  fontFamily:
                                                      AppLocalizations.of(
                                                              context)!
                                                          .translate('font4'),
                                                  fontSize: 50.sp,
                                                  fontWeight: FontWeight.w600,
                                                  color: Colors.white,
                                                ),
                                              ),
                                              Text(
                                                "฿${formatNum.format(likeClaim / 100).toString()}",
                                                style: TextStyle(
                                                  fontFamily:
                                                      AppLocalizations.of(
                                                              context)!
                                                          .translate('font4'),
                                                  fontSize: 35.sp,
                                                  fontWeight: FontWeight.w300,
                                                  color: Color(0xFF738097),
                                                  height: 3.sp
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        _switchClaim
                                            ? InkWell(
                                                onTap: () {
                                                  setState(() {
                                                    _switchClaim = false;
                                                  });
                                                },
                                                child: SvgPicture.asset(
                                                  LikeWalletImage
                                                      .icon_roll_back,
                                                  fit: BoxFit.contain,
                                                  height: mediaQuery(context,
                                                      'height', 200.47),
                                                  // width: mediaQuery(context, 'width', 100.64),
                                                ),
                                              )
                                            : InkWell(
                                                onTap: () {
                                                  setState(() {
                                                    _switchClaim = true;
                                                  });
                                                },
                                                child: SvgPicture.asset(
                                                  LikeWalletImage
                                                      .icon_select_poi,
                                                  fit: BoxFit.contain,
                                                  height: mediaQuery(context,
                                                      'height', 200.47),
                                                  // width: mediaQuery(context, 'width', 100.64),
                                                ),
                                              )
                                      ],
                                    ),
                                    _switchClaim
                                        ? Container(
                                            padding:
                                                EdgeInsets.only(bottom: 30.sp),
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                likeClaim > 0 ?
                                                InkWell(
                                                  onTap: () {
                                                    setState(() {
                                                      popup = true;
                                                    });
                                                  },
                                                  child: Container(
                                                    width: 400.sp,
                                                    padding: EdgeInsets.only(
                                                        top: 10.sp,
                                                        bottom: 10.sp,
                                                        left: 30.sp,
                                                        right: 30.sp),
                                                    decoration: BoxDecoration(
                                                        color:
                                                            Color(0xFF784EFF),
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(
                                                                    50.sp),
                                                        border: Border.all(
                                                            color: Color(
                                                                0xFF784EFF),
                                                            width: 2.sp)),
                                                    child: Text(
                                                      AppLocalizations.of(
                                                              context)!
                                                          .translate(
                                                              'lock_and_earn'),
                                                      textAlign:
                                                          TextAlign.center,
                                                      style: TextStyle(
                                                        fontFamily:
                                                            AppLocalizations.of(
                                                                    context)!
                                                                .translate(
                                                                    'font4'),
                                                        fontSize: 35.sp,
                                                        fontWeight:
                                                            FontWeight.w300,
                                                        color: Colors.white,
                                                      ),
                                                    ),
                                                  ),
                                                )
                                                :InkWell(
                                      child: Container(
                                        width: 400.sp,
                                        padding: EdgeInsets.only(
                                            top: 10.sp,
                                            bottom: 10.sp,
                                            left: 30.sp,
                                            right: 30.sp),
                                        decoration: BoxDecoration(
                                            color:
                                            Color(0xFF784EFF).withOpacity(0.3),
                                            borderRadius:
                                            BorderRadius
                                                .circular(
                                                50.sp),
                                            border: Border.all(
                                                color: Color(
                                                    0xFF784EFF).withOpacity(0.3),
                                                width: 2.sp)),
                                        child: Text(
                                          AppLocalizations.of(
                                              context)!
                                              .translate(
                                              'lock_and_earn'),
                                          textAlign:
                                          TextAlign.center,
                                          style: TextStyle(
                                            fontFamily:
                                            AppLocalizations.of(
                                                context)!
                                                .translate(
                                                'font4'),
                                            fontSize: 35.sp,
                                            fontWeight:
                                            FontWeight.w300,
                                            color: Colors.white.withOpacity(0.3),
                                          ),
                                        ),
                                      ),
                                    ),
                                                SizedBox(
                                                  width: 30.w,
                                                ),

                                                likeClaim > 0 ?
                                                RoundedLoadingButton(
                                                  animateOnTap: likeClaim == 0 ? false : true,
                                                  height: 90.sp,
                                                  width: 400.sp,
                                                  color: Color(0xFF1F9E5F),
                                                  successColor:
                                                  Color(0xFF1F9E5F),
                                                  successIcon:
                                                  Icons.check,
                                                  controller:
                                                  _btnClaimAll,
                                                  onPressed: () async {
                                                    if(likeClaim != 0) {
                                                      await claimPOIAll();
                                                    }
                                                  },
                                                  valueColor:
                                                  Colors.white,
                                                  borderRadius: 50.sp,
                                                  child: Text(
                                                    AppLocalizations.of(
                                                        context)!
                                                        .translate(
                                                        'unlock'),
                                                    textAlign: TextAlign
                                                        .center,
                                                    style: TextStyle(
                                                      fontFamily:
                                                      AppLocalizations.of(
                                                          context)!
                                                          .translate(
                                                          'font4'),
                                                      fontSize: 35.sp,
                                                      fontWeight:
                                                      FontWeight
                                                          .w300,
                                                      color: Colors.white,
                                                    ),
                                                  ),
                                                )
                                                :RoundedLoadingButton(
                                                  animateOnTap: likeClaim == 0 ? false : true,
                                                  height: 90.sp,
                                                  width: 400.sp,
                                                  color: Color(0xFF1F9E5F).withOpacity(0.3),
                                                  successColor:
                                                  Color(0xFF1F9E5F).withOpacity(0.3),
                                                  successIcon:
                                                  Icons.check,
                                                  controller:
                                                  _btnClaimAll,
                                                  onPressed: () {},
                                                  valueColor:
                                                  Colors.white.withOpacity(0.3),
                                                  borderRadius: 50.sp,
                                                  child: Text(
                                                    AppLocalizations.of(
                                                        context)!
                                                        .translate(
                                                        'unlock'),
                                                    textAlign: TextAlign
                                                        .center,
                                                    style: TextStyle(
                                                      fontFamily:
                                                      AppLocalizations.of(
                                                          context)!
                                                          .translate(
                                                          'font4'),
                                                      fontSize: 35.sp,
                                                      fontWeight:
                                                      FontWeight
                                                          .w300,
                                                      color: Colors.white.withOpacity(0.3),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ))
                                        : Container()
                                  ],
                                )),
                          ),
                          SizedBox(
                            height: 30.h,
                          ),
                          ListView.builder(
                              padding: EdgeInsets.zero,
                              shrinkWrap: true,
                              physics: ClampingScrollPhysics(),
                              itemCount: poiData.length,
                              itemBuilder: (BuildContext context, int index) {
                                _btnLoading.start();
                                return Container(
                                    padding: EdgeInsets.only(
                                        left: 40.sp,
                                        right: 40.sp,
                                        bottom: 25.sp),
                                    child: Stack(
                                      children: [
                                        Container(
                                          padding: EdgeInsets.only(
                                              left: 40.sp,
                                              right: 40.sp,
                                              top: 30.sp,
                                              bottom: 10.sp),
                                          decoration: BoxDecoration(
                                              color:
                                                  Color(0xFF161C26),
                                              borderRadius:
                                                  BorderRadius.circular(50.sp),
                                              border: Border.all(
                                                  color: Color(0xFF161C26),
                                                  width: 2.sp)),
                                          child: Column(
                                            children: [
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  Text(
                                                    "${AppLocalizations.of(context)!.translate('Total_Locked_Likepoint_on')}:",
                                                    style: TextStyle(
                                                      fontFamily:
                                                          AppLocalizations.of(
                                                                  context)!
                                                              .translate(
                                                                  'font4'),
                                                      fontSize: 35.sp,
                                                      fontWeight:
                                                          FontWeight.w400,
                                                      color: Color(0xFF8A96AB),
                                                    ),
                                                  ),
                                                  Text(
                                                    AppService.dateFormatByLang(
                                                        lang,
                                                        poiData[index]
                                                            .createTime),
                                                    style: TextStyle(
                                                      fontFamily:
                                                          AppLocalizations.of(
                                                                  context)!
                                                              .translate(
                                                                  'font4'),
                                                      fontSize: 35.sp,
                                                      fontWeight:
                                                          FontWeight.w400,
                                                      color: Colors.white,
                                                    ),
                                                  )
                                                ],
                                              ),
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  Text(
                                                    "${AppLocalizations.of(context)!.translate('From')}:",
                                                    style: TextStyle(
                                                      fontFamily:
                                                          AppLocalizations.of(
                                                                  context)!
                                                              .translate(
                                                                  'font4'),
                                                      fontSize: 35.sp,
                                                      fontWeight:
                                                          FontWeight.w400,
                                                      color: Color(0xFF8A96AB),
                                                    ),
                                                  ),
                                                  Text(
                                                    AppService.activityBUByLang(
                                                        lang,
                                                        poiData[index]
                                                            .payBuMember
                                                            .toString()),
                                                    style: TextStyle(
                                                      fontFamily:
                                                          AppLocalizations.of(
                                                                  context)!
                                                              .translate(
                                                                  'font4'),
                                                      fontSize: 35.sp,
                                                      fontWeight:
                                                          FontWeight.w600,
                                                      color: Colors.white,
                                                    ),
                                                  )
                                                ],
                                              ),
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  Text(
                                                    "${AppLocalizations.of(context)!.translate('Status')}:",
                                                    style: TextStyle(
                                                      fontFamily:
                                                          AppLocalizations.of(
                                                                  context)!
                                                              .translate(
                                                                  'font4'),
                                                      fontSize: 35.sp,
                                                      fontWeight:
                                                          FontWeight.w400,
                                                      color: Color(0xFF8A96AB),
                                                    ),
                                                  ),
                                                  AppService.statusPOI(
                                                      context,
                                                      lang,
                                                      dataUnlock,
                                                      poiData[index]
                                                          .timeUnlock
                                                          .toString())
                                                ],
                                              ),
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  Container(
                                                    child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        Row(
                                                          children: [
                                                            Text(
                                                                formatNum.format(
                                                                    double.parse(poiData[
                                                                            index]
                                                                        .point
                                                                        .toString())),
                                                                style:
                                                                    TextStyle(
                                                                  fontFamily: AppLocalizations.of(
                                                                          context)!
                                                                      .translate(
                                                                          'font4'),
                                                                  fontSize:
                                                                      60.sp,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w600,
                                                                  color: Colors
                                                                      .white,
                                                                      height: 5.sp
                                                                )),
                                                            SizedBox(
                                                                width: 20.w),
                                                            SvgPicture.asset(
                                                              LikeWalletImage
                                                                  .coin_like,
                                                              fit: BoxFit
                                                                  .contain,
                                                              height: 40.sp,
                                                              // width: mediaQuery(context, 'width', 30.64),
                                                            )
                                                          ],
                                                        ),
                                                        Text(
                                                          "฿${formatNum.format(double.parse(((poiData[index].point) / 100).toString()))}",
                                                          style: TextStyle(
                                                            fontFamily:
                                                                AppLocalizations.of(
                                                                        context)!
                                                                    .translate(
                                                                        'font4'),
                                                            fontSize: 35.sp,
                                                            fontWeight:
                                                                FontWeight.w400,
                                                            color: Color(
                                                                0xFF8A96AB),
                                                            height: 3.sp
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  Container(
                                                    child: SvgPicture.asset(
                                                      LikeWalletImage.poi_line,
                                                      fit: BoxFit.contain,
                                                      height: 150.sp,
                                                      // width: mediaQuery(context, 'width', 100.64),
                                                    ),
                                                  ),
                                                  Container(
                                                    child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .end,
                                                      children: [
                                                        Text(
                                                          "${AppLocalizations.of(context)!.translate('end_date')}:",
                                                          style: TextStyle(
                                                            fontFamily:
                                                                AppLocalizations.of(
                                                                        context)!
                                                                    .translate(
                                                                        'font4'),
                                                            fontSize: 35.sp,
                                                            fontWeight:
                                                                FontWeight.w400,
                                                            color: Color(
                                                                0xFF8A96AB),
                                                          ),
                                                        ),
                                                        Text(
                                                          AppService
                                                              .dateFormatByLang(
                                                                  lang,
                                                                  poiData[index]
                                                                      .timeUnlock),
                                                          style: TextStyle(
                                                            fontFamily:
                                                                AppLocalizations.of(
                                                                        context)!
                                                                    .translate(
                                                                        'font4'),
                                                            fontSize: 35.sp,
                                                            fontWeight:
                                                                FontWeight.w400,
                                                            color: Color(
                                                                0xFF0078FF),
                                                          ),
                                                        ),
                                                        Text(
                                                          AppService
                                                              .dateTimeByLang(
                                                                  lang,
                                                                  poiData[index]
                                                                      .timeUnlock),
                                                          style: TextStyle(
                                                            fontFamily:
                                                                AppLocalizations.of(
                                                                        context)!
                                                                    .translate(
                                                                        'font4'),
                                                            fontSize: 35.sp,
                                                            fontWeight:
                                                                FontWeight.w400,
                                                            color: Color(
                                                                0xFF0078FF),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  )
                                                ],
                                              ),
                                              Container(
                                                child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  children: [
                                                    poiData[index].timeUnlockStatus == 'unlock' ? Container(
                                                        margin:
                                                        EdgeInsets.only(
                                                            left: 20.sp),
                                                        child: Row(
                                                          children: [
                                                            SvgPicture.asset(
                                                              LikeWalletImage
                                                                  .circle_check,
                                                              fit: BoxFit.contain,
                                                              height: 50.sp,
                                                            ),
                                                            SizedBox(
                                                              width: 20.w,
                                                            ),
                                                            Text(
                                                             "${AppLocalizations.of(context)!.translate('unlocked')}!",
                                                              style: TextStyle(
                                                                fontFamily:
                                                                AppLocalizations.of(
                                                                    context)!
                                                                    .translate(
                                                                    'font4'),
                                                                fontSize: 40.sp,
                                                                fontWeight:
                                                                FontWeight.w400,
                                                                color: Color(
                                                                    0xFF1F9E5F),
                                                              ),
                                                            ),
                                                          ],
                                                        )
                                                       ): Container(
                                                        child: Row(
                                                      children: [
                                                        InkWell(
                                                          onTap: () {
                                                            if (AppService
                                                                .checkStatusPOI(
                                                                    context,
                                                                    lang,
                                                                    dataUnlock,
                                                                    poiData[index]
                                                                        .timeUnlock
                                                                        .toString())) {
                                                              setState(() {
                                                                poiData[index]
                                                                        .popupContinueLock =
                                                                    true;
                                                              });
                                                            }
                                                          },
                                                          child: Container(
                                                            width: 385.sp,
                                                            padding:
                                                                EdgeInsets.only(
                                                                    top: 10.sp,
                                                                    bottom:
                                                                        10.sp,
                                                                    left: 30.sp,
                                                                    right:
                                                                        30.sp),
                                                            decoration: BoxDecoration(
                                                                color: AppService.checkStatusPOI(
                                                                        context,
                                                                        lang,
                                                                        dataUnlock,
                                                                        poiData[index]
                                                                            .timeUnlock
                                                                            .toString())
                                                                    ? Color(
                                                                        0xFF784EFF)
                                                                    : Color(0xFF784EFF)
                                                                        .withOpacity(
                                                                            0.3),
                                                                borderRadius:
                                                                    BorderRadius.circular(
                                                                        50.sp),
                                                                border: Border.all(
                                                                    color: AppService.checkStatusPOI(
                                                                            context,
                                                                            lang,
                                                                            dataUnlock,
                                                                            poiData[index].timeUnlock.toString())
                                                                        ? Color(0xFF784EFF)
                                                                        : Color(0xFF784EFF).withOpacity(0.3),
                                                                    width: 2.sp)),
                                                            child: Text(
                                                              AppLocalizations.of(
                                                                      context)!
                                                                  .translate(
                                                                      'lock_and_earn'),
                                                              textAlign:
                                                                  TextAlign
                                                                      .center,
                                                              style: TextStyle(
                                                                fontFamily: AppLocalizations.of(
                                                                        context)!
                                                                    .translate(
                                                                        'font4'),
                                                                fontSize: 35.sp,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w300,
                                                                color: AppService.checkStatusPOI(
                                                                        context,
                                                                        lang,
                                                                        dataUnlock,
                                                                        poiData[index]
                                                                            .timeUnlock
                                                                            .toString())
                                                                    ? Colors
                                                                        .white
                                                                    : Colors
                                                                        .white
                                                                        .withOpacity(
                                                                            0.3),
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                        SizedBox(
                                                          width: 30.w,
                                                        ),
                                                        poiData[index].timeUnlockStatus == 'claim' ?
                                                        Container(
                                                          width: 90.sp,
                                                          height: 90.sp,
                                                          child: RoundedLoadingButton(
                                                            color: Color(0xFF1F9E5F),
                                                            child: Text(
                                                              '.',
                                                              style: TextStyle(
                                                                fontFamily: AppLocalizations.of(context)!
                                                                    .translate('font4'),
                                                                fontSize: 35.sp,
                                                                color: Colors.white,
                                                              ),
                                                            ),
                                                            controller: _btnLoading,
                                                            onPressed: () {},
                                                          ),
                                                        ):
                                                        InkWell(
                                                          onTap: () async {
                                                            if (AppService
                                                                .checkStatusPOI(
                                                                context,
                                                                lang,
                                                                dataUnlock,
                                                                poiData[index]
                                                                    .timeUnlock
                                                                    .toString())) {
                                                              setState(() {
                                                                poiData[index].timeUnlockStatus = "claim";
                                                              });
                                                              await EasyLoading
                                                                  .show(
                                                                status: AppLocalizations
                                                                    .of(
                                                                    context)!
                                                                    .translate(
                                                                    'loading_send_text1'),
                                                                maskType:
                                                                EasyLoadingMaskType
                                                                    .black,
                                                                dismissOnTap:
                                                                false,
                                                              );

                                                              var resClaim =
                                                              await getClaimByRunning(
                                                                  poiData[index]
                                                                      .running);
                                                              if (resClaim) {
                                                                setState(() {
                                                                  poiData[index].timeUnlockStatus = "unlock";
                                                                });
                                                              }
                                                            }
                                                          },
                                                          child: Container(
                                                            width: 385.sp,
                                                            padding:
                                                            EdgeInsets.only(
                                                                top: 10.sp,
                                                                bottom:
                                                                10.sp,
                                                                left: 30.sp,
                                                                right:
                                                                30.sp),
                                                            decoration: BoxDecoration(
                                                                color: AppService.checkStatusPOI(
                                                                    context,
                                                                    lang,
                                                                    dataUnlock,
                                                                    poiData[index]
                                                                        .timeUnlock
                                                                        .toString())
                                                                    ? Color(
                                                                    0xFF1F9E5F)
                                                                    : Color(0xFF1F9E5F)
                                                                    .withOpacity(
                                                                    0.3),
                                                                borderRadius:
                                                                BorderRadius.circular(
                                                                    50.sp),
                                                                border: Border.all(
                                                                    color: AppService.checkStatusPOI(
                                                                        context,
                                                                        lang,
                                                                        dataUnlock,
                                                                        poiData[index].timeUnlock.toString())
                                                                        ? Color(0xFF1F9E5F)
                                                                        : Color(0xFF1F9E5F).withOpacity(0.3),
                                                                    width: 2.sp)),
                                                            child: Text(
                                                              AppLocalizations.of(
                                                                  context)!
                                                                  .translate(
                                                                  'unlock'),
                                                              textAlign:
                                                              TextAlign
                                                                  .center,
                                                              style: TextStyle(
                                                                fontFamily: AppLocalizations.of(
                                                                    context)!
                                                                    .translate(
                                                                    'font4'),
                                                                fontSize: 35.sp,
                                                                fontWeight:
                                                                FontWeight
                                                                    .w300,
                                                                color: AppService.checkStatusPOI(
                                                                    context,
                                                                    lang,
                                                                    dataUnlock,
                                                                    poiData[index]
                                                                        .timeUnlock
                                                                        .toString())
                                                                    ? Colors
                                                                    .white
                                                                    : Colors
                                                                    .white
                                                                    .withOpacity(
                                                                    0.3),
                                                              ),
                                                            ),
                                                          ),
                                                        ),

                                                      ],
                                                    )),
                                                    InkWell(
                                                      onTap: () {
                                                        Navigator.push(
                                                            context,
                                                            MaterialPageRoute(
                                                                builder:
                                                                    (context) =>
                                                                        POIDetail(
                                                                          poiData:
                                                                              poiData[index],
                                                                          dataUnlock:
                                                                              dataUnlock,
                                                                        )));
                                                      },
                                                      child: Container(
                                                          margin:
                                                              EdgeInsets.only(
                                                                  left: 20.sp),
                                                          child:
                                                              SvgPicture.asset(
                                                            LikeWalletImage
                                                                .dot_3,
                                                            fit: BoxFit.contain,
                                                            height: 150.sp,
                                                            // width: mediaQuery(context, 'width', 100.64),
                                                          )),
                                                    )
                                                  ],
                                                ),
                                              )
                                            ],
                                          ),
                                        ),
                                        if (poiData[index].popupContinueLock)
                                          Container(
                                            height: 600.sp,
                                            decoration: BoxDecoration(
                                              color: Color(0xFFFFFFFF)
                                                  .withOpacity(0.25),
                                              borderRadius: BorderRadius.all(
                                                  Radius.circular(50.sp)),
                                            ),
                                            child: ClipRRect(
                                              borderRadius:
                                                  BorderRadius.circular(50.sp),
                                              child: BackdropFilter(
                                                  filter: ImageFilter.blur(
                                                      sigmaX: 35.0,
                                                      sigmaY: 35.0),
                                                  child: Container(
                                                    margin:
                                                        const EdgeInsets.only(
                                                            left: 15.0,
                                                            right: 33.0,
                                                            top: 10),
                                                    //use margin instead of padding,
                                                    clipBehavior:
                                                        Clip.antiAlias,
                                                    //to clip the child within the decoration (or try hardEdge too)
                                                    decoration: BoxDecoration(
                                                        borderRadius:
                                                            BorderRadius.all(
                                                                Radius.circular(
                                                                    50.sp))),
                                                    child: Container(
                                                        width: 950.sp,
                                                        child: Column(
                                                          crossAxisAlignment:
                                                              CrossAxisAlignment
                                                                  .end,
                                                          children: [
                                                            Flexible(
                                                              flex: 1,
                                                              child: InkWell(
                                                                onTap: () {
                                                                  setState(() {
                                                                    poiData[index]
                                                                            .popupContinueLock =
                                                                        false;
                                                                  });
                                                                },
                                                                child:
                                                                    Container(
                                                                  margin: EdgeInsets.only(
                                                                      left: 500
                                                                          .sp,
                                                                      bottom: 50
                                                                          .sp),
                                                                  child:
                                                                      SvgPicture
                                                                          .asset(
                                                                    LikeWalletImage
                                                                        .close_bg_close,
                                                                    fit: BoxFit
                                                                        .contain,
                                                                    height: mediaQuery(
                                                                        context,
                                                                        'height',
                                                                        130),
                                                                  ),
                                                                ),
                                                              ),
                                                            ),
                                                            Align(
                                                                alignment:
                                                                    Alignment
                                                                        .topCenter,
                                                                child: Text(
                                                                  '${AppLocalizations.of(context)!.translate('ok')}!',
                                                                  style:
                                                                      TextStyle(
                                                                    fontFamily: AppLocalizations.of(
                                                                            context)!
                                                                        .translate(
                                                                            'font4'),
                                                                    fontSize:
                                                                        50.sp,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .w600,
                                                                    color: Colors
                                                                        .white,
                                                                  ),
                                                                )),
                                                            Align(
                                                                alignment:
                                                                    Alignment
                                                                        .topCenter,
                                                                child: Text(
                                                                  '${AppLocalizations.of(context)!.translate('continue_lock')}',
                                                                  style:
                                                                      TextStyle(
                                                                    fontFamily: AppLocalizations.of(
                                                                            context)!
                                                                        .translate(
                                                                            'font4'),
                                                                    fontSize:
                                                                        45.sp,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .w400,
                                                                    color: Colors
                                                                        .white,
                                                                  ),
                                                                )),
                                                          ],
                                                        )),
                                                  )),
                                            ),
                                          )
                                        else
                                          Container()
                                      ],
                                    ));
                              }),
                          SizedBox(
                            height: 30.h,
                          ),
                          Container(
                            width: 500.h,
                            height: 150.sp,
                            child: RoundedLoadingButton(
                              color: Color(0xFF333333),
                              child: Text(
                                'โหลดประวัติเพิ่มเติม',
                                style: TextStyle(
                                  fontFamily: AppLocalizations.of(context)!
                                      .translate('font4'),
                                  fontSize: 40.sp,
                                  fontWeight: FontWeight.w300,
                                  color: Colors.white,
                                ),
                              ),
                              controller: _btnGetNewData,
                              onPressed: () {
                                setState(() {
                                  end = end + 1;
                                  start = start + 1;
                                });
                                getHistory();
                              },
                            ),
                          ),
                          SizedBox(
                            height: 30.h,
                          ),
                        ],
                      ),
                      popup ? Container(
                        margin: EdgeInsets.only(top: 719.sp, left: 50.sp),
                        height: 339.sp,
                        decoration: BoxDecoration(
                          color: Color(0xFFFFFFFF)
                              .withOpacity(0.25),
                          borderRadius: BorderRadius.all(
                              Radius.circular(50.sp)),
                        ),
                        child: ClipRRect(
                          borderRadius:
                          BorderRadius.circular(50.sp),
                          child: BackdropFilter(
                              filter: ImageFilter.blur(
                                  sigmaX: 35.0,
                                  sigmaY: 35.0),
                              child: Container(
                                margin: EdgeInsets.only(
                                    // top: 5.sp,
                                    right: 50.sp
                                ),
                                //use margin instead of padding,
                                clipBehavior:
                                Clip.antiAlias,
                                //to clip the child within the decoration (or try hardEdge too)
                                decoration: BoxDecoration(
                                    borderRadius:
                                    BorderRadius.all(
                                        Radius.circular(
                                            50.sp))),
                                child: Container(
                                    width: 1020.sp,
                                    child: Column(
                                      crossAxisAlignment:
                                      CrossAxisAlignment
                                          .end,
                                      children: [
                                        Flexible(
                                          flex: 1,
                                          child: InkWell(
                                            onTap: () {
                                              setState(() {
                                                popup = false;
                                              });
                                            },
                                            child:
                                            Container(
                                              margin: EdgeInsets.only(
                                                  left: 500
                                                      .sp),
                                              child:
                                              SvgPicture
                                                  .asset(
                                                LikeWalletImage
                                                    .close_bg_close,
                                                fit: BoxFit
                                                    .contain,
                                                height: mediaQuery(
                                                    context,
                                                    'height',
                                                    130),
                                              ),
                                            ),
                                          ),
                                        ),
                                        Align(
                                            alignment:
                                            Alignment
                                                .topCenter,
                                            child: Text(
                                              '${AppLocalizations.of(context)!.translate('ok')}!',
                                              style:
                                              TextStyle(
                                                fontFamily: AppLocalizations.of(
                                                    context)!
                                                    .translate(
                                                    'font4'),
                                                fontSize:
                                                50.sp,
                                                fontWeight:
                                                FontWeight
                                                    .w600,
                                                color: Colors
                                                    .white,
                                                height: 2.sp
                                              ),
                                            )),
                                        Align(
                                            alignment:
                                            Alignment
                                                .topCenter,
                                            child: Text(
                                              '${AppLocalizations.of(context)!.translate('continue_lock')}',
                                              style:
                                              TextStyle(
                                                fontFamily: AppLocalizations.of(
                                                    context)!
                                                    .translate(
                                                    'font4'),
                                                fontSize:
                                                45.sp,
                                                fontWeight:
                                                FontWeight
                                                    .w400,
                                                color: Colors
                                                    .white,
                                              ),
                                            )),
                                      ],
                                    )),
                              )),
                        ),
                      ) : Container(),
                      Positioned(
                          top: 60.sp,
                          left: 20.sp,
                          child: InkWell(
                            onTap: () {
                              Navigator.pop(context);
                            },
                            child: Image.asset(
                              LikeWalletImage.icon_back_poi,
                              width: 150.sp,
                              height: 150.sp,
                            ),
                          )),
                    ],
                  ),
                ),
        ),
      ),
    );
  }

  getClaimByRunning(String running) async {
    try {
      var url =
          Uri.parse(env.apiLikepointBCT + '/puzzle/claimTimelockableOneCol');
      var headers = {
        'x-api-key': 'z2x4VbpEt57VoXKysG8xl2tP45tvOQuy1bl0qErU',
      };
      Map body = {'address': addressETH.toLowerCase(), 'uid': uid, 'col': running};


      final response = await http.post(url, body: body, headers: headers);
      if (response.statusCode == 200) {
        var bodyData = json.decode(response.body);
        await getLikeClaim();
        await EasyLoading.dismiss();
        return true;
      } else {
        Fluttertoast.showToast(
            msg: "Error on getClaimByRunning",
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.CENTER,
            timeInSecForIosWeb: 1,
            backgroundColor: Colors.red,
            textColor: Colors.white,
            fontSize: 16.0);
        return false;
      }
    } catch (e) {
      print(e);
      Fluttertoast.showToast(
          msg: "Error on getClaimByRunning : ${e.toString()}",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.CENTER,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.red,
          textColor: Colors.white,
          fontSize: 16.0);
      return false;
    }
  }


  claimPOIAll() async {
    try {
      await EasyLoading
          .show(
        status: AppLocalizations
            .of(
            context)!
            .translate(
            'loading_send_text1'),
        maskType:
        EasyLoadingMaskType
            .black,
        dismissOnTap:
        false,
      );
      var url =
      Uri.parse(env.apiLikepointBCT + '/puzzle/claimTimelockable');
      var headers = {
        'x-api-key': 'z2x4VbpEt57VoXKysG8xl2tP45tvOQuy1bl0qErU',
      };
      Map body = {'address': addressETH.toLowerCase(), 'uid': uid, 'date': dateLikeClaim};

      final response = await http.post(url, body: body, headers: headers);
      if (response.statusCode == 200) {
        _btnClaimAll.success();
        await getLikeClaim();
        setState(() {
          end = 0;
          start = 1;
          poiData.clear();
        });
        await getHistory();
        await EasyLoading.dismiss();
        return true;
      } else {
        Fluttertoast.showToast(
            msg: "Error on claimPOIAll",
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.CENTER,
            timeInSecForIosWeb: 1,
            backgroundColor: Colors.red,
            textColor: Colors.white,
            fontSize: 16.0);
        return false;
      }
    } catch (e) {
      print(e);
      Fluttertoast.showToast(
          msg: "Error on claimPOIAll : ${e.toString()}",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.CENTER,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.red,
          textColor: Colors.white,
          fontSize: 16.0);
      return false;
    }
  }
}
