import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart' as formatIntl;
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:likewallet/middleware/getLanguage.dart';
import 'package:likewallet/model/poi.dart';
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/service/app_service.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';

class POIDetail extends StatefulWidget {
  POIDetail({Key? key, required this.poiData, required this.dataUnlock})
      : super(key: key);
  final PoiM poiData;
  final String dataUnlock;

  _POIDetail createState() => new _POIDetail(this.poiData, this.dataUnlock);
}

class _POIDetail extends State<POIDetail> {
  final PoiM poiData;
  final String dataUnlock;

  _POIDetail(this.poiData, this.dataUnlock);

  late OnLanguage language;
  final formatNum = new formatIntl.NumberFormat("###,###.##");
  bool _saving = false;
  String lang = '';

  @override
  void initState() {
    super.initState();
    setInit();
    debugPrint(poiData.toString());
  }

  setInit() async {
    language = CallLanguage();
    lang = await language.getLanguage();
    setState(() {
      _saving = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    String messageUpcoming =
        AppLocalizations.of(context)!.translate('hourlyRewards_please');
    screenUtil(context);
    return ModalProgressHUD(
      inAsyncCall: _saving,
      opacity: 0.1,
      progressIndicator: CustomLoading(),
      child: Scaffold(
          backgroundColor: Color(0xFF11161E),
          body: SingleChildScrollView(
              child: _saving
                  ? Container()
                  : Stack(
                      children: [
                       Positioned(
                           child:  InkWell(
                         onTap: () {
                           Navigator.pop(context);
                         },
                         child: Container(
                           margin: EdgeInsets.only(top: 50.sp),
                           child: SvgPicture.asset(
                             LikeWalletImage.poi_detail_back,
                             fit: BoxFit.contain,
                             height: mediaQuery(context, 'height', 145.47),
                           ),
                         ),
                       )),
                        Center(
                          child: Container(
                            margin: EdgeInsets.only(top: 160.sp),
                            child: Text(lang == "th" ? "รายละเอียด":
                              "Details",
                              style: TextStyle(
                                fontFamily: AppLocalizations.of(context)!
                                    .translate('font4'),
                                fontSize: 45.sp,
                                fontWeight: FontWeight.w400,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                        Container(
                          margin: EdgeInsets.only(top: 300.sp),
                          padding: EdgeInsets.only(left: 60.sp, right: 60.sp),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    "${AppLocalizations.of(context)!.translate('earning_date')}:",
                                    style: TextStyle(
                                      fontFamily: AppLocalizations.of(context)!
                                          .translate('font4'),
                                      fontSize: 40.sp,
                                      fontWeight: FontWeight.w400,
                                      color: Color(0xFF8A96AB),
                                    ),
                                  ),
                                  Text(
                                    AppService.dateFormatByLang(
                                        lang, poiData.createTime),
                                    style: TextStyle(
                                      fontFamily: AppLocalizations.of(context)!
                                          .translate('font4'),
                                      fontSize: 40.sp,
                                      fontWeight: FontWeight.w400,
                                      color: Colors.white,
                                    ),
                                  )
                                ],
                              ),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    "${AppLocalizations.of(context)!.translate('From')}:",
                                    style: TextStyle(
                                      fontFamily: AppLocalizations.of(context)!
                                          .translate('font4'),
                                      fontSize: 40.sp,
                                      fontWeight: FontWeight.w400,
                                      color: Color(0xFF8A96AB),
                                    ),
                                  ),
                                  Text(
                                    AppService.activityBUByLang(
                                        lang, poiData.payBuMember.toString()),
                                    style: TextStyle(
                                      fontFamily: AppLocalizations.of(context)!
                                          .translate('font4'),
                                      fontSize: 40.sp,
                                      fontWeight: FontWeight.w600,
                                      color: Colors.white,
                                    ),
                                  )
                                ],
                              ),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    "${AppLocalizations.of(context)!.translate('Status')}:",
                                    style: TextStyle(
                                      fontFamily: AppLocalizations.of(context)!
                                          .translate('font4'),
                                      fontSize: 40.sp,
                                      fontWeight: FontWeight.w400,
                                      color: Color(0xFF8A96AB),
                                    ),
                                  ),
                                  AppService.statusPOI(context, lang,
                                      dataUnlock, poiData.timeUnlock.toString())
                                ],
                              ),
                              Divider(
                                color: Color(0xFF334157),
                              ),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    "${AppLocalizations.of(context)!.translate('total_locked_likepoint')}",
                                    style: TextStyle(
                                      fontFamily: AppLocalizations.of(context)!
                                          .translate('font4'),
                                      fontSize: 40.sp,
                                      fontWeight: FontWeight.w400,
                                      color: Color(0xFF8A96AB),
                                    ),
                                  ),
                                  Container(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.end,
                                      children: [
                                        Text(
                                            formatNum.format(double.parse(
                                                poiData.point.toString())),
                                            style: TextStyle(
                                              fontFamily:
                                                  AppLocalizations.of(context)!
                                                      .translate('font4'),
                                              fontSize: 42.sp,
                                              fontWeight: FontWeight.w600,
                                              color: Colors.white,
                                            )),
                                        Text(
                                          "฿${formatNum.format(double.parse(((poiData.point) / 100).toString()))}",
                                          style: TextStyle(
                                            fontFamily:
                                                AppLocalizations.of(context)!
                                                    .translate('font4'),
                                            fontSize: 35.sp,
                                            fontWeight: FontWeight.w400,
                                            color: Color(0xFF8A96AB),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(
                                height: 80.h,
                              ),
                              Text(
                                "${AppLocalizations.of(context)!.translate('campaigns')}:",
                                style: TextStyle(
                                  fontFamily: AppLocalizations.of(context)!
                                      .translate('font4'),
                                  fontSize: 40.sp,
                                  fontWeight: FontWeight.w400,
                                  color: Color(0xFF0078FF),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          padding: EdgeInsets.only(left: 30.sp, right: 30.sp),
                          child: Column(
                            children: [
                              Container(
                                padding: EdgeInsets.only(left: 40.sp,right: 40.sp,top: 50.sp,bottom: 50.sp),
                                margin: EdgeInsets.only(top: 835.sp),
                                height: 280.sp,
                                decoration: BoxDecoration(
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(20)),
                                    gradient: LinearGradient(
                                      begin: Alignment.topCenter,
                                      end: Alignment.bottomCenter,
                                      colors: [
                                        Color(0xFF1D2532),
                                        Color(0xFF11161E),
                                      ],
                                    )),
                                child: Column(
                                  children: [
                                    SizedBox(
                                      height: 70.h,
                                      child: Row(
                                        mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            "${poiData.nameActivity}",
                                            style: TextStyle(
                                              fontFamily:
                                              AppLocalizations.of(context)!
                                                  .translate('font4'),
                                              fontSize: 42.sp,
                                              fontWeight: FontWeight.w400,
                                              color: Color(0xFF8A96AB),
                                            ),
                                          ),
                                          Text(
                                              formatNum.format(double.parse(
                                                  poiData.point.toString())),
                                              style: TextStyle(
                                                fontFamily:
                                                AppLocalizations.of(
                                                    context)!
                                                    .translate('font4'),
                                                fontSize: 42.sp,
                                                fontWeight: FontWeight.w500,
                                                color: Colors.white,
                                              )),
                                        ],
                                      ),
                                    ),
                                    Row(
                                      mainAxisAlignment:
                                      MainAxisAlignment.end,
                                      children: [
                                        Text(
                                          "฿${formatNum.format(double.parse(((poiData.point) / 100).toString()))}",
                                          style: TextStyle(
                                            fontFamily: AppLocalizations.of(
                                                context)!
                                                .translate('font4'),
                                            fontSize: 35.sp,
                                            fontWeight: FontWeight.w400,
                                            color: Color(0xFF8A96AB),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                              Container(
                                  padding: EdgeInsets.only(
                                      left: 30.sp, right: 30.sp),
                                  // padding: EdgeInsets.all(10.sp),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          Container(
                                            height: 25.sp,
                                            width: 25.sp,
                                            decoration: BoxDecoration(
                                                color: Color(0xFF0078FF),
                                                shape: BoxShape.circle),
                                          ),
                                          SizedBox(
                                            width: 10.w,
                                          ),
                                          if (lang == "th") Text(
                                            "ล็อคไลค์พอยท์จำนวนนี้ได้ถูกล็อคเอาไว้อัตโนมัติ และ",
                                            style: TextStyle(
                                              fontFamily:
                                              AppLocalizations.of(context)!
                                                  .translate('font4'),
                                              fontSize: 40.sp,
                                              fontWeight: FontWeight.w300,
                                              color: Color(0xFF738097),
                                            ),
                                          ) else Text(
                                            "This amount of Likepoint is locked and won’t be",
                                            style: TextStyle(
                                              fontFamily:
                                              AppLocalizations.of(context)!
                                                  .translate('font4'),
                                              fontSize: 40.sp,
                                              fontWeight: FontWeight.w300,
                                              color: Color(0xFF738097),
                                            ),
                                          ),
                                        ],
                                      ),
                                      if (lang == "th") RichText(
                                        text: TextSpan(
                                          text:
                                          "จะยังไม่สามารถปลดล็อคได้",
                                          style: TextStyle(
                                            fontFamily:
                                            AppLocalizations.of(context)!
                                                .translate('font4'),
                                            fontSize: 45.sp,
                                            fontWeight: FontWeight.w300,
                                            color: Color(0xFF738097),
                                          ),
                                          children: <TextSpan>[
                                            TextSpan(
                                                text:
                                                "จนกว่าจะครบกำหนด \n",
                                                style: TextStyle(
                                                  fontSize: 45.sp,
                                                  color: Color(0xFF738097),
                                                  fontWeight: FontWeight.w300,
                                                )),
                                          ],
                                        ),
                                      ) else RichText(
                                        text: TextSpan(
                                          text:
                                          "available until it is available for claim or unlock\n",
                                          style: TextStyle(
                                            fontFamily:
                                            AppLocalizations.of(context)!
                                                .translate('font4'),
                                            fontSize: 45.sp,
                                            fontWeight: FontWeight.w300,
                                            color: Color(0xFF738097),
                                          ),
                                          children: <TextSpan>[
                                            TextSpan(
                                                text:
                                                "when locked period ends. \n",
                                                style: TextStyle(
                                                  fontSize: 45.sp,
                                                  color: Color(0xFF738097),
                                                  fontWeight: FontWeight.w300,
                                                )),
                                          ],
                                        ),
                                      ),
                                      Row(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            "${AppLocalizations.of(context)!.translate('locked_period')}",
                                            textAlign: TextAlign.left,
                                            style: TextStyle(
                                              fontFamily: AppLocalizations.of(context)!
                                                  .translate('font4'),
                                              fontSize: 40.sp,
                                              fontWeight: FontWeight.w400,
                                              color: Color(0xFF8A96AB),
                                            ),
                                          ),
                                          Text(
                                            "${poiData.dayLock} ${lang == "th" ? "วัน": "Days"}",
                                            style: TextStyle(
                                              fontFamily: AppLocalizations.of(context)!
                                                  .translate('font4'),
                                              fontSize: 40.sp,
                                              fontWeight: FontWeight.w600,
                                              color: Colors.white,
                                            ),
                                          )
                                        ],
                                      ),
                                      SizedBox(
                                        height: 25.h,
                                      ),
                                      Row(
                                        mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            "${AppLocalizations.of(context)!.translate('start_date')}",
                                            style: TextStyle(
                                              fontFamily: AppLocalizations.of(context)!
                                                  .translate('font4'),
                                              fontSize: 40.sp,
                                              fontWeight: FontWeight.w400,
                                              color: Color(0xFF8A96AB),
                                            ),
                                          ),
                                          Text(
                                            AppService.dateFormatByLang(
                                                lang, poiData.createTime),
                                            style: TextStyle(
                                              fontFamily: AppLocalizations.of(context)!
                                                  .translate('font4'),
                                              fontSize: 40.sp,
                                              fontWeight: FontWeight.w600,
                                              color: Colors.white,
                                            ),
                                          )
                                        ],
                                      ),
                                      SizedBox(
                                        height: 25.h,
                                      ),
                                      Row(
                                        mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            "${AppLocalizations.of(context)!.translate('end_date')}",
                                            style: TextStyle(
                                              fontFamily: AppLocalizations.of(context)!
                                                  .translate('font4'),
                                              fontSize: 40.sp,
                                              fontWeight: FontWeight.w400,
                                              color: Color(0xFF8A96AB),
                                            ),
                                          ),
                                          Text(
                                            AppService.dateFormatByLang(
                                                lang, poiData.timeUnlock),
                                            style: TextStyle(
                                              fontFamily: AppLocalizations.of(context)!
                                                  .translate('font4'),
                                              fontSize: 40.sp,
                                              fontWeight: FontWeight.w600,
                                              color: Colors.white,
                                            ),
                                          )
                                        ],
                                      ),
                                      SizedBox(
                                        height: 25.h,
                                      ),
                                      Row(
                                        mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            "${AppLocalizations.of(context)!.translate('days_passed')}",
                                            style: TextStyle(
                                              fontFamily: AppLocalizations.of(context)!
                                                  .translate('font4'),
                                              fontSize: 40.sp,
                                              fontWeight: FontWeight.w400,
                                              color: Color(0xFF8A96AB),
                                            ),
                                          ),
                                          Text("${DateTime.now().difference(DateTime.parse(poiData.createTime)).inDays} ${lang == "th" ? "วัน": "Days"}",
                                            style: TextStyle(
                                              fontFamily: AppLocalizations.of(context)!
                                                  .translate('font4'),
                                              fontSize: 40.sp,
                                              fontWeight: FontWeight.w600,
                                              color: Colors.white,
                                            ),
                                          )
                                        ],
                                      ),
                                      SizedBox(
                                        height: 25.h,
                                      ),
                                      Row(
                                        mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            "${AppLocalizations.of(context)!.translate('days_left')}",
                                            style: TextStyle(
                                              fontFamily: AppLocalizations.of(context)!
                                                  .translate('font4'),
                                              fontSize: 40.sp,
                                              fontWeight: FontWeight.w400,
                                              color: Color(0xFF8A96AB),
                                            ),
                                          ),
                                          Text("${(int.parse(poiData.dayLock.toString()) - DateTime.now().difference(DateTime.parse(poiData.createTime)).inDays).isNegative ? 0 : int.parse(poiData.dayLock.toString()) - DateTime.now().difference(DateTime.parse(poiData.createTime)).inDays} ${lang == "th" ? "วัน": "Days"}",
                                            style: TextStyle(
                                              fontFamily: AppLocalizations.of(context)!
                                                  .translate('font4'),
                                              fontSize: 40.sp,
                                              fontWeight: FontWeight.w600,
                                              color: Colors.white,
                                            ),
                                          )
                                        ],
                                      ),
                                      SizedBox(
                                        height: 25.h,
                                      ),
                                      Row(
                                        mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            "${AppLocalizations.of(context)!.translate('get_rewards_from_this_lock')}",
                                            style: TextStyle(
                                              fontFamily: AppLocalizations.of(context)!
                                                  .translate('font4'),
                                              fontSize: 40.sp,
                                              fontWeight: FontWeight.w400,
                                              color: Color(0xFF8A96AB),
                                            ),
                                          ),
                                          Text("${lang == "th" ? "เป็นประจำทุกวัน": "Daily"}",
                                            style: TextStyle(
                                              fontFamily: AppLocalizations.of(context)!
                                                  .translate('font4'),
                                              fontSize: 40.sp,
                                              fontWeight: FontWeight.w600,
                                              color: Colors.white,
                                            ),
                                          )
                                        ],
                                      ),
                                      SizedBox(
                                        height: 100.h,
                                      ),
                                      Row(
                                        children: [
                                          Container(
                                            height: 25.sp,
                                            width: 25.sp,
                                            decoration: BoxDecoration(
                                                color: Color(0xFF0078FF),
                                                shape: BoxShape.circle),
                                          ),
                                          SizedBox(
                                            width: 10.w,
                                          ),
                                          if (lang == "th") Text(
                                            "ในระหว่างที่ล็อคอยู่นี้ ล็อคไลค์พอยท์จะทำให้ท่านได้รับ",
                                            style: TextStyle(
                                              fontFamily:
                                                  AppLocalizations.of(context)!
                                                      .translate('font4'),
                                              fontSize: 40.sp,
                                              fontWeight: FontWeight.w300,
                                              color: Color(0xFF738097),
                                            ),
                                          ) else Text(
                                            "During this lock period, you will earn daily",
                                            style: TextStyle(
                                              fontFamily:
                                              AppLocalizations.of(context)!
                                                  .translate('font4'),
                                              fontSize: 40.sp,
                                              fontWeight: FontWeight.w300,
                                              color: Color(0xFF738097),
                                            ),
                                          ),
                                        ],
                                      ),
                                      if (lang == "th") RichText(
                                        text: TextSpan(
                                          text:
                                              "รางวัลจากการล็อคเป็นประจำทุกวัน ",
                                          style: TextStyle(
                                            fontFamily:
                                                AppLocalizations.of(context)!
                                                    .translate('font4'),
                                            fontSize: 45.sp,
                                            fontWeight: FontWeight.w300,
                                            color: Color(0xFF738097),
                                          ),
                                          children: <TextSpan>[
                                            TextSpan(
                                                text:
                                                    "อย่าลืมเข้าไปกด\nเคลมรางวัลกันนะครับ! \n\n",
                                                style: TextStyle(
                                                  fontSize: 45.sp,
                                                  color: Color(0xFFE771FD),
                                                  fontWeight: FontWeight.w300,
                                                )),
                                            TextSpan(
                                                text:
                                                    "หากวันไหนไม่กดรับ รางวัลจะหมดอายุ และถูกนำกลับไป \n"
                                                    "แบ่งปันในไลค์พอยท์คอมมูนิตี้ในวันถัดไป \n\n",
                                                style: TextStyle(
                                                  fontFamily:
                                                      AppLocalizations.of(
                                                              context)!
                                                          .translate('font4'),
                                                  fontSize: 45.sp,
                                                  fontWeight: FontWeight.w300,
                                                  color: Color(0xFF738097),
                                                )),
                                            TextSpan(
                                                text:
                                                    "แจกรางวัลทุกวัน 11.00 น.",
                                                style: TextStyle(
                                                  fontSize: 45.sp,
                                                  color: Color(0xFFE771FD),
                                                  fontWeight: FontWeight.w300,
                                                )),
                                          ],
                                        ),
                                      ) else RichText(
                                        text: TextSpan(
                                          text:
                                          "rewards from this locked amount. ",
                                          style: TextStyle(
                                            fontFamily:
                                            AppLocalizations.of(context)!
                                                .translate('font4'),
                                            fontSize: 45.sp,
                                            fontWeight: FontWeight.w300,
                                            color: Color(0xFF738097),
                                          ),
                                          children: <TextSpan>[
                                            TextSpan(
                                                text:
                                                "Don’t forget to\ncheck in and claim your rewards every day. \n\n",
                                                style: TextStyle(
                                                  fontSize: 45.sp,
                                                  color: Color(0xFFE771FD),
                                                  fontWeight: FontWeight.w300,
                                                )),
                                            TextSpan(
                                                text:
                                                "If you don’t claim your rewards before next \n"
                                                    "distributing, the rewards will be expired. Your \n"
                                                    "rewards will be taken back to share to everyone in\n"
                                                    "the Likepoint Community on the next day.\n\n",
                                                style: TextStyle(
                                                  fontFamily:
                                                  AppLocalizations.of(
                                                      context)!
                                                      .translate('font4'),
                                                  fontSize: 45.sp,
                                                  fontWeight: FontWeight.w300,
                                                  color: Color(0xFF738097),
                                                )),
                                            TextSpan(
                                                text:
                                                "Daily distribute at 11.00 AM.",
                                                style: TextStyle(
                                                  fontSize: 45.sp,
                                                  color: Color(0xFFE771FD),
                                                  fontWeight: FontWeight.w300,
                                                )),
                                          ],
                                        ),
                                      ),
                                      SizedBox(
                                        height: 100.h,
                                      ),
                                    ],
                                  )
                                  // Row(
                                  //   children: [
                                  //     Container(
                                  //       height: 25.sp,
                                  //       width: 25.sp,
                                  //       decoration: BoxDecoration(
                                  //           color: Color(0xFF0078FF),
                                  //           shape: BoxShape.circle
                                  //       ),
                                  //     ),

                                  //   ],
                                  // ),
                                  )
                            ],
                          ),
                        )
                      ],
                    ))),
    );
  }
}
