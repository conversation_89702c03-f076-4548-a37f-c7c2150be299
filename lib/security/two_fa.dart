import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/animationPage.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/libraryman/open_web_notitle.dart';
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/tabslide/registerEmail/OTPEmail.dart';
import 'package:otp/otp.dart';
import 'dart:convert';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/animationPage.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/tabslide/registerEmail/OTPEmail.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:likewallet/libraryman/serviceHTTP.dart';
import 'package:likewallet/tabslide/registerEmail/registerEmail_Success.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:likewallet/app_config.dart';
import 'package:http/http.dart' as http;
import 'package:qr_flutter/qr_flutter.dart';

class TwoFA extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return _TwoFA();
  }
}

class _TwoFA extends State<TwoFA> {
  TextEditingController twofa = new TextEditingController();

  bool _twofa = false;
  bool _autoValidate = false;
  bool _saving = true;
  late AbstractServiceHTTP apiCall;
  late BaseAuth auth;
  String generateCode = '..generating';
  GlobalKey globalKey = new GlobalKey();
  final _formKey = GlobalKey<FormState>();
  String qrCode = '';
  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    apiCall = ServiceHTTP();
    auth = Auth();
    init();
  }

  init() {
    auth.getTokenFirebase().then((_token) {
      apiCall.checkTwoFA(token: _token!).then((_enable) {
        // print(_enable);
        if (_enable["result"] == 'true') {
          // print(_enable);
          // print(_enable["secret"]);
          if (!mounted) return;
          setState(() {
            _saving = false;
            // twofa.text = _enable;
            _twofa = true;
          });
        } else if (_enable["result"] != 'no') {
          if (!mounted) return;

          setState(() {
            _saving = false;
            generateCode = _enable["secret"].toString();
            qrCode = _enable["uri"].toString();
          });
        } else {
          if (!mounted) return;
          setState(() {
            _saving = false;
          });
        }
      });
    });
  }

  openGuide() {
    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) => WebOpenNoTitle(
                  title: 'Tutorial',
                  url: 'https://help.likepoint.io/2fa',
                )));
  }

  Widget generateCodeWidget() {
    double c_width = MediaQuery.of(context).size.width * 0.8;

    return new Container(
      padding: const EdgeInsets.all(16.0),
      width: c_width,
      child: new Column(
        children: <Widget>[
          Center(
            child: RepaintBoundary(
                key: globalKey,
                child: Container(
                    child: Stack(alignment: Alignment(0, 0), children: <Widget>[
                  Container(
                    height: 530.h,
                    width: 530.h,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.16),
                          offset: Offset(0, 5),
                          blurRadius: 12,
                        ),
                      ],
                    ),
                  ),
                  Container(
                    height: 600.h,
                    width: 600.h,
                    child: QrImageView(
                      backgroundColor: LikeWalletAppTheme.white,
                      data: qrCode,
                      size: 530.h,
                      embeddedImage: AssetImage(LikeWalletImage.qr_likepoint),
                      embeddedImageStyle: QrEmbeddedImageStyle(
                        size: Size(
                          mediaQuery(context, 'height', 100.6),
                          mediaQuery(context, 'height', 100.6),
                        ),
                      ),
                      errorCorrectionLevel: QrErrorCorrectLevel.Q,
                      gapless: false,
                      version: 9,
                    ),
                  ),
                ]))),
          ),
          SizedBox(
            height: 10.h,
          ),
          Text(
            AppLocalizations.of(context)!.translate('two_step_2fa_code') +
                ' : ' +
                generateCode,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: LikeWalletAppTheme.bule3,
              fontSize: mediaQuery(context, 'height', 35),
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
            ),
          ),
          Padding(padding: EdgeInsets.only(left: 20)),
          GestureDetector(
            child: Icon(
              Icons.copy,
              color: LikeWalletAppTheme.bule3,
            ),
            onTap: () {
              Clipboard.setData(new ClipboardData(text: generateCode))
                  .then((d) {
                showShortToast(
                    AppLocalizations.of(context)!
                            .translate('two_step_2fa_copy') +
                        generateCode.toString(),
                    Colors.lightBlueAccent);
              });
            },
          )
        ],
      ),
    );
  }

  void showShortToast(msg, Color color) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_SHORT,
        backgroundColor: color,
        textColor: Colors.white);
  }

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return ModalProgressHUD(
        inAsyncCall: _saving,
        opacity: 0.1,
        progressIndicator: CustomLoading(),
        child: GestureDetector(
          onTap: () {
            FocusScopeNode currentFocus = FocusScope.of(context);
            if (!currentFocus.hasPrimaryFocus) {
              currentFocus.unfocus();
            }
          },
          child: Scaffold(
            backgroundColor: LikeWalletAppTheme.bule2,
            body: SingleChildScrollView(
              child: Form(
                key: _formKey,
                autovalidateMode: _autoValidate == true
                    ? AutovalidateMode.always
                    : AutovalidateMode.disabled,
                child: Container(
                  height: MediaQuery.of(context).size.height,
                  decoration: BoxDecoration(
                    image: DecorationImage(
                      image: AssetImage('assets/image/back.png'),
                      fit: BoxFit.cover,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: <Widget>[
                      //พื้นหลัง
                      Container(
                        height: mediaQuery(context, 'height', 499),
                        decoration: BoxDecoration(
                            color: LikeWalletAppTheme.bule2,
                            boxShadow: [
                              BoxShadow(
                                spreadRadius: 1.0,
                                blurRadius: 8.0,
                                color:
                                    LikeWalletAppTheme.black.withOpacity(0.5),
                                offset: Offset(0, 3),
                              )
                            ]),
                        child: Column(
                          children: [
                            //พื้นหลัง
                            Container(
                              padding: EdgeInsets.only(
                                top: mediaQuery(context, 'height', 132),
                                left: 0,
                              ),
                              child:
                                  backButton(context, LikeWalletAppTheme.gray),
                            ),
                            SizedBox(
                              height: mediaQuery(context, 'height', 50),
                            ),
                            Expanded(
                              child: Container(
                                width: mediaQuery(context, 'width', 927),
                                child: Text(
                                  AppLocalizations.of(context)!
                                      .translate('two_step_2fa_header'),
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    color: LikeWalletAppTheme.white
                                        .withOpacity(0.6),
                                    letterSpacing: 0.3,
                                    fontSize: mediaQuery(context, 'height', 39),
                                    fontFamily: AppLocalizations.of(context)!
                                        .translate('font1'),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(
                            top: mediaQuery(context, 'height', 52)),
                        child: new TextButton(
                            style: ButtonStyle(
                                shape: MaterialStateProperty.all(
                                    new RoundedRectangleBorder(
                                  borderRadius: new BorderRadius.circular(5.0),
                                )),
                                foregroundColor:
                                    MaterialStateProperty.all<Color>(
                                        LikeWalletAppTheme.white),
                                backgroundColor:
                                    MaterialStateProperty.all<Color>(
                                        LikeWalletAppTheme.darkerText)),
                            onPressed: openGuide,
                            child: Container(
                                width: mediaQuery(context, 'height', 250),
                                child: Row(
                                  children: [
                                    new Text(
                                      AppLocalizations.of(context)!.translate(
                                          'two_step_2fa_button_tutorial'),
                                      style: TextStyle(
                                          letterSpacing: 0.3,
                                          color: LikeWalletAppTheme.bule1,
                                          fontSize:
                                              mediaQuery(context, 'height', 45),
                                          fontFamily:
                                              AppLocalizations.of(context)!
                                                  .translate('font1'),
                                          fontWeight: FontWeight.bold),
                                    ),
                                    Icon(
                                      Icons.book_sharp,
                                      color: LikeWalletAppTheme.bule1,
                                    )
                                  ],
                                ))),
                      ),
                      _twofa == true
                          ? Padding(
                              padding: EdgeInsets.only(
                                  top: mediaQuery(context, 'height', 52)),
                              child: Card(
                                  child: Container(
                                width: mediaQuery(context, 'height', 940),
                                height: mediaQuery(context, 'height', 100),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: <Widget>[
                                    Text(
                                      AppLocalizations.of(context)!
                                          .translate('two_step_2fa_enable'),
                                      style: TextStyle(
                                        color: LikeWalletAppTheme.bule3,
                                        fontSize:
                                            mediaQuery(context, 'height', 50),
                                        fontFamily:
                                            AppLocalizations.of(context)!
                                                .translate('font1'),
                                      ),
                                    ),
                                    Icon(
                                      Icons.check_circle,
                                      color: LikeWalletAppTheme.bule3,
                                    )
                                  ],
                                ),
                              )),
                            )
                          : Container(),
                      //input email
                      _twofa == true
                          ? Padding(
                              padding: EdgeInsets.only(
                                  top: mediaQuery(context, 'height', 52)),
                              child: _input(
                                  'two_step_2fa_generate',
                                  twofa,
                                  TextInputType.number,
                                  false,
                                  'two_step_2fa_desc'),
                            )
                          : Container(
                              child: Column(
                                children: [
                                  Padding(
                                      padding: EdgeInsets.only(
                                          top:
                                              mediaQuery(context, 'height', 52),
                                          bottom: mediaQuery(
                                              context, 'height', 100)),
                                      child: Card(
                                        child: generateCodeWidget(),
                                      )),
                                  Padding(
                                    padding: EdgeInsets.only(
                                        top: mediaQuery(context, 'height', 52)),
                                    child: _input(
                                        'two_step_2fa_generate',
                                        twofa,
                                        TextInputType.number,
                                        false,
                                        'two_step_2fa_desc'),
                                  )
                                ],
                              ),
                            ),
                      Padding(
                        padding: EdgeInsets.only(
                            top: mediaQuery(context, 'height', 98)),
                        child: _button(),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ));
  }

  Widget _input(hintText, controller, TextInputType, bool, validatorText) {
    return Container(
      margin: EdgeInsets.only(
          top: mediaQuery(context, 'height', 20),
          bottom: mediaQuery(context, 'height', 20)),
      decoration: BoxDecoration(
        color: Color(0xff141322),
        border: Border.all(
          color: Color(0xff6C6B6D),
          width: 1.0,
        ),
        borderRadius: BorderRadius.all(Radius.circular(10.0)),
      ),
      alignment: Alignment.center,
      height: mediaQuery(context, 'height', 150),
      width: mediaQuery(context, 'width', 930),
      child: TextFormField(
          controller: controller,
          style: TextStyle(
              letterSpacing: 0.3,
              fontSize: mediaQuery(context, 'height', 47),
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              color: LikeWalletAppTheme.white),
          decoration: InputDecoration(
            contentPadding: EdgeInsets.only(
                left: mediaQuery(context, 'width', 70),
                top: mediaQuery(context, 'width', 30)),
            border: InputBorder.none,
            hoverColor: Colors.white,
            disabledBorder: InputBorder.none,
            focusColor: Colors.white,
            alignLabelWithHint: true,
            fillColor: Colors.white,
            hintText: AppLocalizations.of(context)!.translate(hintText),
            hintStyle: TextStyle(
                letterSpacing: 0.3,
                fontSize: mediaQuery(context, 'height', 47),
                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                color: LikeWalletAppTheme.gray),
            suffixIcon: IconButton(
              onPressed: () {
                Clipboard.getData('text/plain').then((value) {
                  twofa.text = value!.text.toString();
                });
              },
              icon: Icon(
                Icons.paste,
                color: LikeWalletAppTheme.bule3,
              ),
            ),
          ),
          obscureText: bool,
          keyboardType: TextInputType),
    );
  }

  Widget _button() {
    return new Container(
      alignment: Alignment.center,
      width: mediaQuery(context, 'width', 930),
      child: ButtonTheme(
        minWidth: mediaQuery(context, 'width', 930),
        height: mediaQuery(context, 'height', 132),
        child: new TextButton(
            style: ButtonStyle(
                shape: MaterialStateProperty.all(new RoundedRectangleBorder(
                  borderRadius: new BorderRadius.circular(5.0),
                )),
                foregroundColor:
                    MaterialStateProperty.all<Color>(LikeWalletAppTheme.white),
                backgroundColor:
                    MaterialStateProperty.all<Color>(LikeWalletAppTheme.bule1)),
            onPressed: _validateInputs,
            child: new Text(
              _twofa == true
                  ? AppLocalizations.of(context)!
                      .translate('two_step_2fa_button_disable')
                  : AppLocalizations.of(context)!
                      .translate('two_step_2fa_button_enable'),
              style: TextStyle(
                  letterSpacing: 0.3,
                  color: LikeWalletAppTheme.black,
                  fontSize: mediaQuery(context, 'height', 45),
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  fontWeight: FontWeight.bold),
            )),
      ),
    );
  }

  Future<bool> enableTwoFA() async {
    var url = Uri.https(env.apiUrl, '/enableTwoFA');
    String? _token = await auth.getTokenFirebase();
    print(twofa.text.trim().toString());
    var response = await http.post(url,
        body: {'twofa': twofa.text.trim().toString(), '_token': _token});
    print('Response status: ${response.statusCode}');
    print('Response body: ${response.body}');
    var body = json.decode(response.body);
    print(body['statusCode']);
    print(body);
    if (body['statusCode'] == 200) {
      return true;
    } else {
      print(body['statusCode']);
      return false;
    }
  }

  void _validateInputs() {
    if (!mounted) return;
    setState(() {
      _saving = true;
    });

    if (_twofa == true) {
      //disable zone
      auth.getTokenFirebase().then((_token) {
        apiCall
            .disableTwoFA(token: _token.toString(), otp: twofa.text)
            .then((result) {
          if (result["result"].toString() == 'true') {
            init();
            setState(() {
              _twofa = false;
              _saving = false;
              twofa.text = '';
            });
            showShortToast(
                AppLocalizations.of(context)!
                    .translate('two_step_2fa_success_disable'),
                Colors.lightBlueAccent);
          } else {
            setState(() {
              _twofa = false;
              _saving = false;
              twofa.text = '';
            });
            showShortToast(
                AppLocalizations.of(context)!.translate('two_step_2fa_error'),
                Colors.orange);
          }
        });
      });
    } else {
      //enable zone
      auth.getTokenFirebase().then((_token) {
        apiCall
            .enableTwoFA(
                token: _token.toString(), secret: generateCode, otp: twofa.text)
            .then((result) {
          if (result["result"].toString() == 'true') {
            setState(() {
              _twofa = true;
              _saving = false;
              twofa.text = '';
            });
            showShortToast(
                AppLocalizations.of(context)!
                        .translate('two_step_2fa_success_enable') +
                    generateCode.toString(),
                Colors.lightBlueAccent);
          } else {
            setState(() {
              _twofa = false;
              _saving = false;
              twofa.text = '';
            });
            showShortToast(
                AppLocalizations.of(context)!.translate('two_step_2fa_error'),
                Colors.orange);
          }
        });
      });
    }
  }

  String validateEmail(String value) {
    Pattern pattern =
        r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$';
    RegExp regex = new RegExp(pattern.toString());
    if (!regex.hasMatch(value))
      return AppLocalizations.of(context)!
          .translate('registerEmail_textField_email');
    else
      return 'error';
  }

  String validatePassword(String value) {
    if (value.length < 6)
      return AppLocalizations.of(context)!
          .translate('registerEmail_textField_password');
    else
      return 'error';
  }
}
