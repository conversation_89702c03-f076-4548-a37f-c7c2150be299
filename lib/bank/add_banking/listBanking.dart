// import 'dart:convert';
// import 'dart:io';
// import 'package:cached_network_image/cached_network_image.dart';
// import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:flutter/material.dart';
// import 'package:likewallet/Theme.dart';
// import 'package:likewallet/libraryman/app_local.dart';
// import 'package:likewallet/libraryman/custom_loading.dart';
// import 'package:likewallet/middleware/callCamera.dart';
// import 'package:likewallet/screen_util.dart';
// import 'package:http/http.dart' as http;
// import 'package:likewallet/libraryman/auth.dart';
// import 'package:likewallet/ImageTheme.dart';
//
// import 'package:likewallet/app_config.dart';
// import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
//
// class ListBanking extends StatefulWidget {
//   ListBanking({this.symbol});
//   final String symbol;
//   _ListBanking createState() => new _ListBanking(symbol: symbol);
// }
//
// class _ListBanking extends State<ListBanking> {
//   _ListBanking({this.symbol});
//   final _formKey = GlobalKey<FormState>();
//   final String symbol;
//   String uploadURl;
//   OnCallCamera camera;
//   BaseAuth auth;
//
//   @override
//   void initState() {
//     // TODO: implement initState
//     super.initState();
//     auth = new Auth();
//     camera = CallCamera();
//     setSymbol();
//   }
//
//   final fireStore = FirebaseFirestore.instance;
//   final TextEditingController nameAccount = TextEditingController();
//   final TextEditingController numberAccount = TextEditingController();
//   final TextEditingController bookingURL = TextEditingController();
//   final TextEditingController bookingSelfieURL = TextEditingController();
//   bool show = false;
//   List data;
//   String uid;
//   String url = '';
//   String fullname = '';
//   List listbank = [];
//   bool _loading = false;
//   Future<List<ListBank>> _getListBank(country) async {
//     final response = await http.post(env.apiUrl + '/listBankWithdraw');
//     setState(() {
//       data = json.decode(response.body)['result'];
//       print(data);
//       data.forEach((doc) {
//         if (doc['country'] == country) {
//           listbank.add(doc);
//         }
//       });
//     });
//   }
//
//   Future<dynamic> setSymbol() async {
//     String country;
//     if (symbol == 'THB') country = 'TH';
//     if (symbol == 'USD') country = 'KM';
//     if (symbol == 'LAK') country = 'LAO';
//
//     await _getListBank(country);
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     // TODO: implement build
//     return Scaffold(
//       body: GestureDetector(
//         onTap: () {
//           // setState(() {
//           //   // show = false;
//           // });
//         },
//         child: ModalProgressHUD(
//             inAsyncCall: _loading,
//             opacity: 0.3,
//             progressIndicator: CustomLoading(),
//             child: Stack(children: <Widget>[
//               //พื้นหลังสี Blue2
//               _background(),
//               //รายการบัญชีที่สามารถทรายการได้
//               _buildListView(),
//               //หัวข้อ
//               _head(),
//               //ฟอร์มเพิ่มบัญชีธนาคาร
//               _addAccountBanking(context, show),
//               //ปุ่มกลับ
//               Positioned(
//                   top: mediaQuery(context, 'height', 100),
//                   child: backButton(context, LikeWalletAppTheme.gray)),
//             ])),
//       ),
//     );
//   }
//
//   Widget _background() {
//     return Container(
//         height: MediaQuery.of(context).size.height,
//         decoration: BoxDecoration(
//             gradient: LinearGradient(
//           begin: Alignment.topCenter,
//           end: Alignment.bottomCenter,
//           stops: [0.4, 0.6, 0.7, 0.8, 0.85],
//           colors: [
//             // Colors are easy thanks to Flutter's Colors class.
//             LikeWalletAppTheme.bule2.withOpacity(1),
//             LikeWalletAppTheme.bule2.withOpacity(0.94),
//             LikeWalletAppTheme.bule2.withOpacity(0.92),
//             LikeWalletAppTheme.bule2.withOpacity(0.9),
//             LikeWalletAppTheme.bule2.withOpacity(0.88),
//           ],
//         )));
//   }
//
//   Widget _head() {
//     return Column(
//       children: <Widget>[
//         Container(
//           color: LikeWalletAppTheme.bule2,
//           height: mediaQuery(context, 'height', 256),
//           child: Container(
//             padding: EdgeInsets.only(bottom: mediaQuery(context, 'height', 20)),
//             alignment: Alignment.bottomCenter,
//             width: MediaQuery.of(context).size.width,
//             decoration: BoxDecoration(
//               border: Border(
//                 bottom: BorderSide(
//                   color: Colors.grey,
//                   width: mediaQuery(context, 'width', 1.5),
//                 ),
//               ),
//             ),
//             child: new Text(
//               AppLocalizations.of(context)!.translate('title_bank'),
//               style: TextStyle(
//                   letterSpacing: 1,
//                   fontFamily: AppLocalizations.of(context)!.translate('font1'),
//                   color: LikeWalletAppTheme.white.withOpacity(1),
//                   fontSize: mediaQuery(context, 'height', 42),
//                   fontWeight: FontWeight.w500),
//             ),
//           ),
//         ),
//       ],
//     );
//   }
//
//   Widget _buildListView() {
//     return Padding(
//         padding: EdgeInsets.only(
//           top: mediaQuery(context, 'height', 256),
//         ),
//         child: ListView.builder(
//             padding: const EdgeInsets.all(16.0),
//             itemCount: listbank == null ? 0 : listbank.length,
//             itemBuilder: (context, index) {
//               return FlatButton(
//                 onPressed: () {
//                   setState(() {
//                     show = true;
//                     url = listbank[index]['url'];
//                     fullname = listbank[index]['short'];
//                     print(listbank[index]['url']);
//                     print(listbank[index]['fullname']);
//                   });
//                 },
//                 child: _buildImageColumn(listbank[index]),
//               );
//             }));
//   }
//
//   Widget _buildImageColumn(dynamic item) => Container(
//       color: Colors.transparent,
//       margin: const EdgeInsets.all(4),
//       child: ListTile(
//         leading: new CachedNetworkImage(
//           imageUrl: item['url'],
//           placeholder: (context, url) => new CircularProgressIndicator(),
//           errorWidget: (context, url, error) => new Icon(Icons.error),
//           fadeOutDuration: new Duration(seconds: 1),
//           fadeInDuration: new Duration(seconds: 3),
//           height: mediaQuery(context, 'height', 126),
//         ),
//         title: Text(
//           item['fullname'],
//           style: TextStyle(
//             color: LikeWalletAppTheme.white,
//             fontSize: mediaQuery(context, 'height', 42),
//             fontFamily: AppLocalizations.of(context)!.translate('font1'),
//           ),
//         ),
//       ));
//
//   Widget _addAccountBanking(context, show) {
//     return GestureDetector(
//         onTap: () {
//           FocusScopeNode currentFocus = FocusScope.of(context);
//
//           if (!currentFocus.hasPrimaryFocus) {
//             currentFocus.unfocus();
//           }
//         },
//         child: Container(
//             decoration: BoxDecoration(
//               borderRadius: BorderRadius.only(
//                   bottomRight: Radius.circular(37.0),
//                   bottomLeft: Radius.circular(37.0)),
//               color: LikeWalletAppTheme.white,
//             ),
//             child: AnimatedContainer(
//               duration: Duration(milliseconds: 300),
//               alignment: Alignment.topCenter,
//               width: MediaQuery.of(context).size.width,
//               height: show
//                   ? mediaQuery(context, 'height', 1900.11)
//                   : mediaQuery(context, 'height', 0),
//               decoration: BoxDecoration(
//                 gradient: LinearGradient(
//                   begin: Alignment.topCenter,
//                   end: Alignment.bottomCenter,
//                   stops: [0.4, 0.6, 0.7, 0.8, 0.85],
//                   colors: [
//                     // Colors are easy thanks to Flutter's Colors class.
//                     LikeWalletAppTheme.white.withOpacity(1),
//                     LikeWalletAppTheme.white.withOpacity(0.94),
//                     LikeWalletAppTheme.white.withOpacity(0.92),
//                     LikeWalletAppTheme.white.withOpacity(0.9),
//                     LikeWalletAppTheme.white.withOpacity(0.9),
//                   ],
//                 ),
//                 borderRadius: BorderRadius.only(
//                     bottomRight: Radius.circular(35.0),
//                     bottomLeft: Radius.circular(35.0)),
//                 boxShadow: [
//                   new BoxShadow(
//                       color: Colors.black.withOpacity(0.16),
//                       offset: new Offset(0, 3),
//                       blurRadius: 3.0,
//                       spreadRadius: 0.0),
//                 ],
//                 color: LikeWalletAppTheme.white,
//               ),
//               child: SingleChildScrollView(
//                 child: url != ''
//                     ? Column(
//                         children: <Widget>[
//                           _name(),
//                           _inputAccountBanking(),
//                           _buttonOK()
//                         ],
//                       )
//                     : Container(),
//               ),
//             )));
//   }
//
//   Widget _name() {
//     return Container(
//         height: mediaQuery(context, 'height', 256),
//         alignment: Alignment.bottomCenter,
//         padding: EdgeInsets.only(
//           bottom: mediaQuery(context, 'height', 20),
//         ),
//         width: double.infinity,
//         decoration: BoxDecoration(
//           border: Border(
//             bottom: BorderSide(
//               color: LikeWalletAppTheme.gray,
//               width: mediaQuery(context, 'width', 1.5),
//             ),
//           ),
//         ),
//         child: Text(
//           AppLocalizations.of(context)!.translate('add_account_details'),
//           style: TextStyle(
//               fontFamily: AppLocalizations.of(context)!.translate('font1'),
//               color: LikeWalletAppTheme.bule2,
//               fontSize: mediaQuery(context, 'height', 42),
//               fontWeight: FontWeight.w500),
//         ));
//   }
//
//   Widget _selectedAccount() {
//     return GestureDetector(
//       onTap: () {
//         // setState(() => show = false);
//       },
//       child: Row(
//         children: <Widget>[
//           Container(
//             margin: EdgeInsets.only(
//               right: mediaQuery(context, 'width', 50),
//             ),
//             child: new CachedNetworkImage(
//               imageUrl: url,
//               placeholder: (context, url) => new CircularProgressIndicator(),
//               errorWidget: (context, url, error) => new Icon(Icons.error),
//               fadeOutDuration: new Duration(seconds: 1),
//               fadeInDuration: new Duration(seconds: 3),
//               height: mediaQuery(context, 'height', 126),
//             ),
//           ),
//           Text(
//             fullname,
//             style: TextStyle(
//               color: LikeWalletAppTheme.gray,
//               fontSize: mediaQuery(context, 'height', 42),
//               fontFamily: AppLocalizations.of(context)!.translate('font1'),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
//
//   Widget _inputAccountBanking() {
//     return Padding(
//         padding: EdgeInsets.only(
//           top: mediaQuery(context, 'height', 100),
//           left: mediaQuery(context, 'width', 150),
//           right: mediaQuery(context, 'width', 150),
//         ),
//         child: Form(
//             key: _formKey,
//             child: Column(
//               children: <Widget>[
//                 _selectedAccount(),
//                 Container(
//                   padding: EdgeInsets.only(
//                     top: mediaQuery(context, 'height', 50),
//                   ),
//                   child: TextFormField(
//                     controller: nameAccount,
//                     keyboardType: TextInputType.text,
//                     validator: (val) => val.length < 12
//                         ? AppLocalizations.of(context)
//                             .translate('bankingcash_alert_name_account')
//                         : null,
//                     style: TextStyle(
//                       color: LikeWalletAppTheme.gray1,
//                       fontSize: MediaQuery.of(context).size.height *
//                           Screen_util('height', 48),
//                     ),
//                     decoration: InputDecoration(
//                         labelText: AppLocalizations.of(context)
//                             .translate('add_account_name'),
//                         labelStyle: TextStyle(
//                             color: LikeWalletAppTheme.gray,
//                             fontFamily:
//                                 AppLocalizations.of(context)!.translate('font1'),
//                             fontSize: MediaQuery.of(context).size.height *
//                                 Screen_util('height', 34),
//                             fontWeight: FontWeight.normal),
//                         enabledBorder: UnderlineInputBorder(
//                             borderSide: BorderSide(
//                                 color:
//                                     LikeWalletAppTheme.gray.withOpacity(0.5))),
//                         focusedBorder: UnderlineInputBorder(
//                             borderSide: BorderSide(
//                                 color:
//                                     LikeWalletAppTheme.gray.withOpacity(0.5)))),
//                   ),
//                 ),
//                 Container(
//                   padding: EdgeInsets.only(
//                     top: mediaQuery(context, 'height', 80),
//                   ),
//                   child: TextFormField(
//                     controller: numberAccount,
//                     validator: (val) => val.length < 10
//                         ? AppLocalizations.of(context)
//                             .translate('bankingcash_alert_name_number_account')
//                         : null,
//                     style: TextStyle(
//                       color: LikeWalletAppTheme.gray,
//                       fontSize: MediaQuery.of(context).size.height *
//                           Screen_util('height', 48),
//                     ),
//                     keyboardType: TextInputType.datetime,
//                     decoration: InputDecoration(
//                         labelText: AppLocalizations.of(context)
//                             .translate('add_account_number'),
//                         labelStyle: TextStyle(
//                             color: LikeWalletAppTheme.gray,
//                             fontFamily:
//                                 AppLocalizations.of(context)!.translate('font1'),
//                             fontSize: MediaQuery.of(context).size.height *
//                                 Screen_util('height', 34),
//                             fontWeight: FontWeight.normal),
//                         enabledBorder: UnderlineInputBorder(
//                             borderSide: BorderSide(
//                                 color:
//                                     LikeWalletAppTheme.gray.withOpacity(0.5))),
//                         focusedBorder: UnderlineInputBorder(
//                             borderSide: BorderSide(
//                                 color:
//                                     LikeWalletAppTheme.gray.withOpacity(0.5)))
//                         //border: InputBorder.none
//                         ),
//                   ),
//                 ),
//                 // Container(
//                 //     padding: EdgeInsets.only(
//                 //       top: mediaQuery(context, 'height', 80),
//                 //     ),
//                 //     child: Row(
//                 //       mainAxisAlignment: MainAxisAlignment.center,
//                 //       crossAxisAlignment: CrossAxisAlignment.center,
//                 //       children: [
//                 //         SizedBox(
//                 //           width: mediaQuery(context, 'width', 650),
//                 //           child: TextFormField(
//                 //             controller: bookingURL,
//                 //             readOnly: true,
//                 //             validator: (val) => val.length < 10
//                 //                 ? AppLocalizations.of(context)!.translate(
//                 //                     'bankingcash_alert_name_booking_url')
//                 //                 : null,
//                 //             style: TextStyle(
//                 //               color: LikeWalletAppTheme.gray,
//                 //               fontSize: MediaQuery.of(context).size.height *
//                 //                   Screen_util('height', 48),
//                 //             ),
//                 //             keyboardType: TextInputType.datetime,
//                 //             decoration: InputDecoration(
//                 //                 labelText: 'BOOK BANKING',
//                 //                 labelStyle: TextStyle(
//                 //                     color: LikeWalletAppTheme.gray,
//                 //                     fontFamily: AppLocalizations.of(context)
//                 //                         .translate('font1'),
//                 //                     fontSize:
//                 //                         MediaQuery.of(context).size.height *
//                 //                             Screen_util('height', 34),
//                 //                     fontWeight: FontWeight.normal),
//                 //                 enabledBorder: UnderlineInputBorder(
//                 //                     borderSide: BorderSide(
//                 //                         color: LikeWalletAppTheme.gray
//                 //                             .withOpacity(0.5))),
//                 //                 focusedBorder: UnderlineInputBorder(
//                 //                     borderSide: BorderSide(
//                 //                         color: LikeWalletAppTheme.gray
//                 //                             .withOpacity(0.5)))
//                 //                 //border: InputBorder.none
//                 //                 ),
//                 //           ),
//                 //         ),
//                 //         Expanded(
//                 //           child: InkWell(
//                 //             onTap: () => uploadBooking(bookingURL),
//                 //             child: Icon(Icons.add_a_photo_outlined,
//                 //                 size: MediaQuery.of(context).size.height *
//                 //                     Screen_util('height', 70)),
//                 //           ),
//                 //         ),
//                 //       ],
//                 //     )),
//                 // Container(
//                 //     padding: EdgeInsets.only(
//                 //       top: mediaQuery(context, 'height', 80),
//                 //     ),
//                 //     child: Row(
//                 //       children: [
//                 //         SizedBox(
//                 //           width: mediaQuery(context, 'width', 650),
//                 //           child: TextFormField(
//                 //             controller: bookingSelfieURL,
//                 //             readOnly: true,
//                 //             validator: (val) => val.length < 10
//                 //                 ? AppLocalizations.of(context)!.translate(
//                 //                     'bankingcash_alert_name_booking_url')
//                 //                 : null,
//                 //             style: TextStyle(
//                 //               color: LikeWalletAppTheme.gray,
//                 //               fontSize: MediaQuery.of(context).size.height *
//                 //                   Screen_util('height', 48),
//                 //             ),
//                 //             keyboardType: TextInputType.datetime,
//                 //             decoration: InputDecoration(
//                 //                 labelText: 'BOOK BANKING',
//                 //                 labelStyle: TextStyle(
//                 //                     color: LikeWalletAppTheme.gray,
//                 //                     fontFamily: AppLocalizations.of(context)
//                 //                         .translate('font1'),
//                 //                     fontSize:
//                 //                         MediaQuery.of(context).size.height *
//                 //                             Screen_util('height', 34),
//                 //                     fontWeight: FontWeight.normal),
//                 //                 enabledBorder: UnderlineInputBorder(
//                 //                     borderSide: BorderSide(
//                 //                         color: LikeWalletAppTheme.gray
//                 //                             .withOpacity(0.5))),
//                 //                 focusedBorder: UnderlineInputBorder(
//                 //                     borderSide: BorderSide(
//                 //                         color: LikeWalletAppTheme.gray
//                 //                             .withOpacity(0.5)))
//                 //                 //border: InputBorder.none
//                 //                 ),
//                 //           ),
//                 //         ),
//                 //         Expanded(
//                 //           child: InkWell(
//                 //             onTap: () => uploadBooking(bookingSelfieURL),
//                 //             child: Icon(Icons.add_a_photo_outlined,
//                 //                 size: MediaQuery.of(context).size.height *
//                 //                     Screen_util('height', 70)),
//                 //           ),
//                 //         ),
//                 //       ],
//                 //     )),
// //            _buttonOK()
//               ],
//             )));
//   }
//
//   uploadBooking(TextEditingController controller) async {
//     var image = await camera.getImage();
//     if (image == null) {
//       print('ยังไม่ได่เลือกรูป');
//       setState(() => controller.clear());
//     } else {
//       String upload = await camera.uploadImage(file: File(image.path));
//       if (upload == '' || upload == 'false' || upload == null) {
//         setState(() => controller.clear());
//         showShortToast(
//             AppLocalizations.of(context)!.translate('bankingcash_error_url'),
//             Colors.red);
//       } else {
//         setState(() {
//           show = true;
//           controller.text = upload;
//         });
//       }
//     }
//   }
//
//   Widget _buttonOK() {
//     return Container(
//         alignment: Alignment.center,
//         margin: EdgeInsets.only(
//           top: mediaQuery(context, 'height', 100),
//         ),
//         child: Row(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: <Widget>[
//             GestureDetector(
//               onTap: () {
//                 setState(() {
//                   show = false;
//                 });
//               },
//               child: Container(
//                   width: mediaQuery(context, 'width', 930) / 2,
//                   child: Row(
//                     mainAxisAlignment: MainAxisAlignment.start,
//                     children: <Widget>[
//                       Image.asset(
//                         LikeWalletImage.icon_button_cancel_white,
//                         height: mediaQuery(context, 'height', 123),
//                       ),
//                       Text(
//                           AppLocalizations.of(context)
//                               .translate('add_account_cancel'),
//                           style: TextStyle(
//                             fontFamily:
//                                 AppLocalizations.of(context)!.translate('font1'),
//                             color: LikeWalletAppTheme.gray1,
//                             fontWeight: FontWeight.normal,
//                             fontSize: MediaQuery.of(context).size.height *
//                                 Screen_util("height", 36),
//                           )),
//                     ],
//                   )),
//             ),
//             GestureDetector(
//                 onTap: () {
//                   if (_formKey.currentState.validate()) {
//                     // Process data.
//                     print('ddf');
//                   }
//                   // // If the form is valid, display a Snackbar.
//                   // if (nameAccount.text.toString().length < 5 ||
//                   //     numberAccount.text.toString().length < 10) {
//                   //   showColoredToast(AppLocalizations.of(context)
//                   //       .translate('add_account_check_input'));
//                   // } else {
//                   //   String pattern = r'(^(?:[0]9)?[0-9]{10,}$)';
//                   //   RegExp regex = new RegExp(pattern);
//                   //   if (numberAccount.text.length == 0) {
//                   //     showShortToast(
//                   //         AppLocalizations.of(context)
//                   //             .translate('add_account_alert'),
//                   //         Colors.cyan);
//                   //     return 'Please enter mobile number';
//                   //   } else if (!regex.hasMatch(numberAccount.text)) {
//                   //     print('not match');
//                   //     showShortToast(
//                   //         AppLocalizations.of(context)
//                   //             .translate('add_account_alert'),
//                   //         Colors.red);
//                   //     return 'Please enter valid mobile number';
//                   //   } else if (regex.hasMatch(numberAccount.text)) {
//                   //     saveFavorite();
//                   //     Navigator.pop(context);
//                   //   }
//                   // }
//                 },
//                 child: Container(
//                     width: mediaQuery(context, 'width', 930) / 2,
//                     child: Row(
//                       mainAxisAlignment: MainAxisAlignment.end,
//                       children: <Widget>[
//                         Text(
//                             AppLocalizations.of(context)
//                                 .translate('add_account_add'),
//                             style: TextStyle(
//                               fontFamily: AppLocalizations.of(context)
//                                   .translate('font1'),
//                               color: LikeWalletAppTheme.gray1,
//                               fontWeight: FontWeight.normal,
//                               fontSize: MediaQuery.of(context).size.height *
//                                   Screen_util("height", 36),
//                             )),
//                         Image.asset(
//                           LikeWalletImage.icon_button_next_black,
//                           height: mediaQuery(context, 'height', 123),
//                         ),
//                       ],
//                     )))
//           ],
//         ));
//   }
//
//   void saveFavorite() async {
//     await auth.getCurrentUser().then((decodeToken) {
//       print(decodeToken);
//       uid = decodeToken.uid;
//       print(uid);
//       print(fullname);
//       print(nameAccount.text.toString());
//       print(numberAccount.text.toString());
//       fireStore.collection('favorites').doc(uid).collection('bank').add({
//         'urlBank': url,
//         'nameBank': fullname,
//         'nameAccount': nameAccount.text.toString(),
//         'numberAccount': numberAccount.text.toString(),
//       });
//     });
//     setState(() {
//       show = false;
//     });
//   }
// }
//
// class ListBank {
//   final String url;
//   final String short;
//   final String fullname;
//
//   ListBank({
//     this.url,
//     this.short,
//     this.fullname,
//   });
//
//   factory ListBank.fromJson(Map<String, dynamic> json) {
//     return ListBank(
//       url: json['url'],
//       short: json['short'],
//       fullname: json['fullname'],
//     );
//   }
// }
// import 'dart:convert';
// import 'dart:io';
// import 'package:cached_network_image/cached_network_image.dart';
// import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:flutter/material.dart';
// import 'package:likewallet/Theme.dart';
// import 'package:likewallet/libraryman/app_local.dart';
// import 'package:likewallet/libraryman/custom_loading.dart';
// import 'package:likewallet/middleware/callCamera.dart';
// import 'package:likewallet/screen_util.dart';
// import 'package:http/http.dart' as http;
// import 'package:likewallet/libraryman/auth.dart';
// import 'package:likewallet/ImageTheme.dart';
//
// import 'package:likewallet/app_config.dart';
// import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
//
// class ListBanking extends StatefulWidget {
//   ListBanking({this.symbol});
//   final String symbol;
//   _ListBanking createState() => new _ListBanking(symbol: symbol);
// }
//
// class _ListBanking extends State<ListBanking> {
//   _ListBanking({this.symbol});
//   final _formKey = GlobalKey<FormState>();
//   final String symbol;
//   String uploadURl;
//   OnCallCamera camera;
//   BaseAuth auth;
//
//   @override
//   void initState() {
//     // TODO: implement initState
//     super.initState();
//     auth = new Auth();
//     camera = CallCamera();
//     setSymbol();
//   }
//
//   final fireStore = FirebaseFirestore.instance;
//   final TextEditingController nameAccount = TextEditingController();
//   final TextEditingController numberAccount = TextEditingController();
//   final TextEditingController bookingURL = TextEditingController();
//   final TextEditingController bookingSelfieURL = TextEditingController();
//   bool show = false;
//   List data;
//   String uid;
//   String url = '';
//   String fullname = '';
//   List listbank = [];
//   bool _loading = false;
//   Future<List<ListBank>> _getListBank(country) async {
//     final response = await http.post(env.apiUrl + '/listBankWithdraw');
//     setState(() {
//       data = json.decode(response.body)['result'];
//       print(data);
//       data.forEach((doc) {
//         if (doc['country'] == country) {
//           listbank.add(doc);
//         }
//       });
//     });
//   }
//
//   Future<dynamic> setSymbol() async {
//     String country;
//     if (symbol == 'THB') country = 'TH';
//     if (symbol == 'USD') country = 'KM';
//     if (symbol == 'LAK') country = 'LAO';
//
//     await _getListBank(country);
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     // TODO: implement build
//     return Scaffold(
//       body: GestureDetector(
//         onTap: () {
//           // setState(() {
//           //   // show = false;
//           // });
//         },
//         child: ModalProgressHUD(
//             inAsyncCall: _loading,
//             opacity: 0.3,
//             progressIndicator: CustomLoading(),
//             child: Stack(children: <Widget>[
//               //พื้นหลังสี Blue2
//               _background(),
//               //รายการบัญชีที่สามารถทรายการได้
//               _buildListView(),
//               //หัวข้อ
//               _head(),
//               //ฟอร์มเพิ่มบัญชีธนาคาร
//               _addAccountBanking(context, show),
//               //ปุ่มกลับ
//               Positioned(
//                   top: mediaQuery(context, 'height', 100),
//                   child: backButton(context, LikeWalletAppTheme.gray)),
//             ])),
//       ),
//     );
//   }
//
//   Widget _background() {
//     return Container(
//         height: MediaQuery.of(context).size.height,
//         decoration: BoxDecoration(
//             gradient: LinearGradient(
//           begin: Alignment.topCenter,
//           end: Alignment.bottomCenter,
//           stops: [0.4, 0.6, 0.7, 0.8, 0.85],
//           colors: [
//             // Colors are easy thanks to Flutter's Colors class.
//             LikeWalletAppTheme.bule2.withOpacity(1),
//             LikeWalletAppTheme.bule2.withOpacity(0.94),
//             LikeWalletAppTheme.bule2.withOpacity(0.92),
//             LikeWalletAppTheme.bule2.withOpacity(0.9),
//             LikeWalletAppTheme.bule2.withOpacity(0.88),
//           ],
//         )));
//   }
//
//   Widget _head() {
//     return Column(
//       children: <Widget>[
//         Container(
//           color: LikeWalletAppTheme.bule2,
//           height: mediaQuery(context, 'height', 256),
//           child: Container(
//             padding: EdgeInsets.only(bottom: mediaQuery(context, 'height', 20)),
//             alignment: Alignment.bottomCenter,
//             width: MediaQuery.of(context).size.width,
//             decoration: BoxDecoration(
//               border: Border(
//                 bottom: BorderSide(
//                   color: Colors.grey,
//                   width: mediaQuery(context, 'width', 1.5),
//                 ),
//               ),
//             ),
//             child: new Text(
//               AppLocalizations.of(context)!.translate('title_bank'),
//               style: TextStyle(
//                   letterSpacing: 1,
//                   fontFamily: AppLocalizations.of(context)!.translate('font1'),
//                   color: LikeWalletAppTheme.white.withOpacity(1),
//                   fontSize: mediaQuery(context, 'height', 42),
//                   fontWeight: FontWeight.w500),
//             ),
//           ),
//         ),
//       ],
//     );
//   }
//
//   Widget _buildListView() {
//     return Padding(
//         padding: EdgeInsets.only(
//           top: mediaQuery(context, 'height', 256),
//         ),
//         child: ListView.builder(
//             padding: const EdgeInsets.all(16.0),
//             itemCount: listbank == null ? 0 : listbank.length,
//             itemBuilder: (context, index) {
//               return FlatButton(
//                 onPressed: () {
//                   setState(() {
//                     show = true;
//                     url = listbank[index]['url'];
//                     fullname = listbank[index]['short'];
//                     print(listbank[index]['url']);
//                     print(listbank[index]['fullname']);
//                   });
//                 },
//                 child: _buildImageColumn(listbank[index]),
//               );
//             }));
//   }
//
//   Widget _buildImageColumn(dynamic item) => Container(
//       color: Colors.transparent,
//       margin: const EdgeInsets.all(4),
//       child: ListTile(
//         leading: new CachedNetworkImage(
//           imageUrl: item['url'],
//           placeholder: (context, url) => new CircularProgressIndicator(),
//           errorWidget: (context, url, error) => new Icon(Icons.error),
//           fadeOutDuration: new Duration(seconds: 1),
//           fadeInDuration: new Duration(seconds: 3),
//           height: mediaQuery(context, 'height', 126),
//         ),
//         title: Text(
//           item['fullname'],
//           style: TextStyle(
//             color: LikeWalletAppTheme.white,
//             fontSize: mediaQuery(context, 'height', 42),
//             fontFamily: AppLocalizations.of(context)!.translate('font1'),
//           ),
//         ),
//       ));
//
//   Widget _addAccountBanking(context, show) {
//     return GestureDetector(
//         onTap: () {
//           FocusScopeNode currentFocus = FocusScope.of(context);
//
//           if (!currentFocus.hasPrimaryFocus) {
//             currentFocus.unfocus();
//           }
//         },
//         child: Container(
//             decoration: BoxDecoration(
//               borderRadius: BorderRadius.only(
//                   bottomRight: Radius.circular(37.0),
//                   bottomLeft: Radius.circular(37.0)),
//               color: LikeWalletAppTheme.white,
//             ),
//             child: AnimatedContainer(
//               duration: Duration(milliseconds: 300),
//               alignment: Alignment.topCenter,
//               width: MediaQuery.of(context).size.width,
//               height: show
//                   ? mediaQuery(context, 'height', 1900.11)
//                   : mediaQuery(context, 'height', 0),
//               decoration: BoxDecoration(
//                 gradient: LinearGradient(
//                   begin: Alignment.topCenter,
//                   end: Alignment.bottomCenter,
//                   stops: [0.4, 0.6, 0.7, 0.8, 0.85],
//                   colors: [
//                     // Colors are easy thanks to Flutter's Colors class.
//                     LikeWalletAppTheme.white.withOpacity(1),
//                     LikeWalletAppTheme.white.withOpacity(0.94),
//                     LikeWalletAppTheme.white.withOpacity(0.92),
//                     LikeWalletAppTheme.white.withOpacity(0.9),
//                     LikeWalletAppTheme.white.withOpacity(0.9),
//                   ],
//                 ),
//                 borderRadius: BorderRadius.only(
//                     bottomRight: Radius.circular(35.0),
//                     bottomLeft: Radius.circular(35.0)),
//                 boxShadow: [
//                   new BoxShadow(
//                       color: Colors.black.withOpacity(0.16),
//                       offset: new Offset(0, 3),
//                       blurRadius: 3.0,
//                       spreadRadius: 0.0),
//                 ],
//                 color: LikeWalletAppTheme.white,
//               ),
//               child: SingleChildScrollView(
//                 child: url != ''
//                     ? Column(
//                         children: <Widget>[
//                           _name(),
//                           _inputAccountBanking(),
//                           _buttonOK()
//                         ],
//                       )
//                     : Container(),
//               ),
//             )));
//   }
//
//   Widget _name() {
//     return Container(
//         height: mediaQuery(context, 'height', 256),
//         alignment: Alignment.bottomCenter,
//         padding: EdgeInsets.only(
//           bottom: mediaQuery(context, 'height', 20),
//         ),
//         width: double.infinity,
//         decoration: BoxDecoration(
//           border: Border(
//             bottom: BorderSide(
//               color: LikeWalletAppTheme.gray,
//               width: mediaQuery(context, 'width', 1.5),
//             ),
//           ),
//         ),
//         child: Text(
//           AppLocalizations.of(context)!.translate('add_account_details'),
//           style: TextStyle(
//               fontFamily: AppLocalizations.of(context)!.translate('font1'),
//               color: LikeWalletAppTheme.bule2,
//               fontSize: mediaQuery(context, 'height', 42),
//               fontWeight: FontWeight.w500),
//         ));
//   }
//
//   Widget _selectedAccount() {
//     return GestureDetector(
//       onTap: () {
//         // setState(() => show = false);
//       },
//       child: Row(
//         children: <Widget>[
//           Container(
//             margin: EdgeInsets.only(
//               right: mediaQuery(context, 'width', 50),
//             ),
//             child: new CachedNetworkImage(
//               imageUrl: url,
//               placeholder: (context, url) => new CircularProgressIndicator(),
//               errorWidget: (context, url, error) => new Icon(Icons.error),
//               fadeOutDuration: new Duration(seconds: 1),
//               fadeInDuration: new Duration(seconds: 3),
//               height: mediaQuery(context, 'height', 126),
//             ),
//           ),
//           Text(
//             fullname,
//             style: TextStyle(
//               color: LikeWalletAppTheme.gray,
//               fontSize: mediaQuery(context, 'height', 42),
//               fontFamily: AppLocalizations.of(context)!.translate('font1'),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
//
//   Widget _inputAccountBanking() {
//     return Padding(
//         padding: EdgeInsets.only(
//           top: mediaQuery(context, 'height', 100),
//           left: mediaQuery(context, 'width', 150),
//           right: mediaQuery(context, 'width', 150),
//         ),
//         child: Form(
//             key: _formKey,
//             child: Column(
//               children: <Widget>[
//                 _selectedAccount(),
//                 Container(
//                   padding: EdgeInsets.only(
//                     top: mediaQuery(context, 'height', 50),
//                   ),
//                   child: TextFormField(
//                     controller: nameAccount,
//                     keyboardType: TextInputType.text,
//                     validator: (val) => val.length < 12
//                         ? AppLocalizations.of(context)
//                             .translate('bankingcash_alert_name_account')
//                         : null,
//                     style: TextStyle(
//                       color: LikeWalletAppTheme.gray1,
//                       fontSize: MediaQuery.of(context).size.height *
//                           Screen_util('height', 48),
//                     ),
//                     decoration: InputDecoration(
//                         labelText: AppLocalizations.of(context)
//                             .translate('add_account_name'),
//                         labelStyle: TextStyle(
//                             color: LikeWalletAppTheme.gray,
//                             fontFamily:
//                                 AppLocalizations.of(context)!.translate('font1'),
//                             fontSize: MediaQuery.of(context).size.height *
//                                 Screen_util('height', 34),
//                             fontWeight: FontWeight.normal),
//                         enabledBorder: UnderlineInputBorder(
//                             borderSide: BorderSide(
//                                 color:
//                                     LikeWalletAppTheme.gray.withOpacity(0.5))),
//                         focusedBorder: UnderlineInputBorder(
//                             borderSide: BorderSide(
//                                 color:
//                                     LikeWalletAppTheme.gray.withOpacity(0.5)))),
//                   ),
//                 ),
//                 Container(
//                   padding: EdgeInsets.only(
//                     top: mediaQuery(context, 'height', 80),
//                   ),
//                   child: TextFormField(
//                     controller: numberAccount,
//                     validator: (val) => val.length < 10
//                         ? AppLocalizations.of(context)
//                             .translate('bankingcash_alert_name_number_account')
//                         : null,
//                     style: TextStyle(
//                       color: LikeWalletAppTheme.gray,
//                       fontSize: MediaQuery.of(context).size.height *
//                           Screen_util('height', 48),
//                     ),
//                     keyboardType: TextInputType.datetime,
//                     decoration: InputDecoration(
//                         labelText: AppLocalizations.of(context)
//                             .translate('add_account_number'),
//                         labelStyle: TextStyle(
//                             color: LikeWalletAppTheme.gray,
//                             fontFamily:
//                                 AppLocalizations.of(context)!.translate('font1'),
//                             fontSize: MediaQuery.of(context).size.height *
//                                 Screen_util('height', 34),
//                             fontWeight: FontWeight.normal),
//                         enabledBorder: UnderlineInputBorder(
//                             borderSide: BorderSide(
//                                 color:
//                                     LikeWalletAppTheme.gray.withOpacity(0.5))),
//                         focusedBorder: UnderlineInputBorder(
//                             borderSide: BorderSide(
//                                 color:
//                                     LikeWalletAppTheme.gray.withOpacity(0.5)))
//                         //border: InputBorder.none
//                         ),
//                   ),
//                 ),
//                 // Container(
//                 //     padding: EdgeInsets.only(
//                 //       top: mediaQuery(context, 'height', 80),
//                 //     ),
//                 //     child: Row(
//                 //       mainAxisAlignment: MainAxisAlignment.center,
//                 //       crossAxisAlignment: CrossAxisAlignment.center,
//                 //       children: [
//                 //         SizedBox(
//                 //           width: mediaQuery(context, 'width', 650),
//                 //           child: TextFormField(
//                 //             controller: bookingURL,
//                 //             readOnly: true,
//                 //             validator: (val) => val.length < 10
//                 //                 ? AppLocalizations.of(context)!.translate(
//                 //                     'bankingcash_alert_name_booking_url')
//                 //                 : null,
//                 //             style: TextStyle(
//                 //               color: LikeWalletAppTheme.gray,
//                 //               fontSize: MediaQuery.of(context).size.height *
//                 //                   Screen_util('height', 48),
//                 //             ),
//                 //             keyboardType: TextInputType.datetime,
//                 //             decoration: InputDecoration(
//                 //                 labelText: 'BOOK BANKING',
//                 //                 labelStyle: TextStyle(
//                 //                     color: LikeWalletAppTheme.gray,
//                 //                     fontFamily: AppLocalizations.of(context)
//                 //                         .translate('font1'),
//                 //                     fontSize:
//                 //                         MediaQuery.of(context).size.height *
//                 //                             Screen_util('height', 34),
//                 //                     fontWeight: FontWeight.normal),
//                 //                 enabledBorder: UnderlineInputBorder(
//                 //                     borderSide: BorderSide(
//                 //                         color: LikeWalletAppTheme.gray
//                 //                             .withOpacity(0.5))),
//                 //                 focusedBorder: UnderlineInputBorder(
//                 //                     borderSide: BorderSide(
//                 //                         color: LikeWalletAppTheme.gray
//                 //                             .withOpacity(0.5)))
//                 //                 //border: InputBorder.none
//                 //                 ),
//                 //           ),
//                 //         ),
//                 //         Expanded(
//                 //           child: InkWell(
//                 //             onTap: () => uploadBooking(bookingURL),
//                 //             child: Icon(Icons.add_a_photo_outlined,
//                 //                 size: MediaQuery.of(context).size.height *
//                 //                     Screen_util('height', 70)),
//                 //           ),
//                 //         ),
//                 //       ],
//                 //     )),
//                 // Container(
//                 //     padding: EdgeInsets.only(
//                 //       top: mediaQuery(context, 'height', 80),
//                 //     ),
//                 //     child: Row(
//                 //       children: [
//                 //         SizedBox(
//                 //           width: mediaQuery(context, 'width', 650),
//                 //           child: TextFormField(
//                 //             controller: bookingSelfieURL,
//                 //             readOnly: true,
//                 //             validator: (val) => val.length < 10
//                 //                 ? AppLocalizations.of(context)!.translate(
//                 //                     'bankingcash_alert_name_booking_url')
//                 //                 : null,
//                 //             style: TextStyle(
//                 //               color: LikeWalletAppTheme.gray,
//                 //               fontSize: MediaQuery.of(context).size.height *
//                 //                   Screen_util('height', 48),
//                 //             ),
//                 //             keyboardType: TextInputType.datetime,
//                 //             decoration: InputDecoration(
//                 //                 labelText: 'BOOK BANKING',
//                 //                 labelStyle: TextStyle(
//                 //                     color: LikeWalletAppTheme.gray,
//                 //                     fontFamily: AppLocalizations.of(context)
//                 //                         .translate('font1'),
//                 //                     fontSize:
//                 //                         MediaQuery.of(context).size.height *
//                 //                             Screen_util('height', 34),
//                 //                     fontWeight: FontWeight.normal),
//                 //                 enabledBorder: UnderlineInputBorder(
//                 //                     borderSide: BorderSide(
//                 //                         color: LikeWalletAppTheme.gray
//                 //                             .withOpacity(0.5))),
//                 //                 focusedBorder: UnderlineInputBorder(
//                 //                     borderSide: BorderSide(
//                 //                         color: LikeWalletAppTheme.gray
//                 //                             .withOpacity(0.5)))
//                 //                 //border: InputBorder.none
//                 //                 ),
//                 //           ),
//                 //         ),
//                 //         Expanded(
//                 //           child: InkWell(
//                 //             onTap: () => uploadBooking(bookingSelfieURL),
//                 //             child: Icon(Icons.add_a_photo_outlined,
//                 //                 size: MediaQuery.of(context).size.height *
//                 //                     Screen_util('height', 70)),
//                 //           ),
//                 //         ),
//                 //       ],
//                 //     )),
// //            _buttonOK()
//               ],
//             )));
//   }
//
//   uploadBooking(TextEditingController controller) async {
//     var image = await camera.getImage();
//     if (image == null) {
//       print('ยังไม่ได่เลือกรูป');
//       setState(() => controller.clear());
//     } else {
//       String upload = await camera.uploadImage(file: File(image.path));
//       if (upload == '' || upload == 'false' || upload == null) {
//         setState(() => controller.clear());
//         showShortToast(
//             AppLocalizations.of(context)!.translate('bankingcash_error_url'),
//             Colors.red);
//       } else {
//         setState(() {
//           show = true;
//           controller.text = upload;
//         });
//       }
//     }
//   }
//
//   Widget _buttonOK() {
//     return Container(
//         alignment: Alignment.center,
//         margin: EdgeInsets.only(
//           top: mediaQuery(context, 'height', 100),
//         ),
//         child: Row(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: <Widget>[
//             GestureDetector(
//               onTap: () {
//                 setState(() {
//                   show = false;
//                 });
//               },
//               child: Container(
//                   width: mediaQuery(context, 'width', 930) / 2,
//                   child: Row(
//                     mainAxisAlignment: MainAxisAlignment.start,
//                     children: <Widget>[
//                       Image.asset(
//                         LikeWalletImage.icon_button_cancel_white,
//                         height: mediaQuery(context, 'height', 123),
//                       ),
//                       Text(
//                           AppLocalizations.of(context)
//                               .translate('add_account_cancel'),
//                           style: TextStyle(
//                             fontFamily:
//                                 AppLocalizations.of(context)!.translate('font1'),
//                             color: LikeWalletAppTheme.gray1,
//                             fontWeight: FontWeight.normal,
//                             fontSize: MediaQuery.of(context).size.height *
//                                 Screen_util("height", 36),
//                           )),
//                     ],
//                   )),
//             ),
//             GestureDetector(
//                 onTap: () {
//                   if (_formKey.currentState.validate()) {
//                     // Process data.
//                     print('ddf');
//                   }
//                   // // If the form is valid, display a Snackbar.
//                   // if (nameAccount.text.toString().length < 5 ||
//                   //     numberAccount.text.toString().length < 10) {
//                   //   showColoredToast(AppLocalizations.of(context)
//                   //       .translate('add_account_check_input'));
//                   // } else {
//                   //   String pattern = r'(^(?:[0]9)?[0-9]{10,}$)';
//                   //   RegExp regex = new RegExp(pattern);
//                   //   if (numberAccount.text.length == 0) {
//                   //     showShortToast(
//                   //         AppLocalizations.of(context)
//                   //             .translate('add_account_alert'),
//                   //         Colors.cyan);
//                   //     return 'Please enter mobile number';
//                   //   } else if (!regex.hasMatch(numberAccount.text)) {
//                   //     print('not match');
//                   //     showShortToast(
//                   //         AppLocalizations.of(context)
//                   //             .translate('add_account_alert'),
//                   //         Colors.red);
//                   //     return 'Please enter valid mobile number';
//                   //   } else if (regex.hasMatch(numberAccount.text)) {
//                   //     saveFavorite();
//                   //     Navigator.pop(context);
//                   //   }
//                   // }
//                 },
//                 child: Container(
//                     width: mediaQuery(context, 'width', 930) / 2,
//                     child: Row(
//                       mainAxisAlignment: MainAxisAlignment.end,
//                       children: <Widget>[
//                         Text(
//                             AppLocalizations.of(context)
//                                 .translate('add_account_add'),
//                             style: TextStyle(
//                               fontFamily: AppLocalizations.of(context)
//                                   .translate('font1'),
//                               color: LikeWalletAppTheme.gray1,
//                               fontWeight: FontWeight.normal,
//                               fontSize: MediaQuery.of(context).size.height *
//                                   Screen_util("height", 36),
//                             )),
//                         Image.asset(
//                           LikeWalletImage.icon_button_next_black,
//                           height: mediaQuery(context, 'height', 123),
//                         ),
//                       ],
//                     )))
//           ],
//         ));
//   }
//
//   void saveFavorite() async {
//     await auth.getCurrentUser().then((decodeToken) {
//       print(decodeToken);
//       uid = decodeToken.uid;
//       print(uid);
//       print(fullname);
//       print(nameAccount.text.toString());
//       print(numberAccount.text.toString());
//       fireStore.collection('favorites').doc(uid).collection('bank').add({
//         'urlBank': url,
//         'nameBank': fullname,
//         'nameAccount': nameAccount.text.toString(),
//         'numberAccount': numberAccount.text.toString(),
//       });
//     });
//     setState(() {
//       show = false;
//     });
//   }
// }
//
// class ListBank {
//   final String url;
//   final String short;
//   final String fullname;
//
//   ListBank({
//     this.url,
//     this.short,
//     this.fullname,
//   });
//
//   factory ListBank.fromJson(Map<String, dynamic> json) {
//     return ListBank(
//       url: json['url'],
//       short: json['short'],
//       fullname: json['fullname'],
//     );
//   }
// }
import 'dart:convert';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/bank/add_banking/exampleKYCBnak.dart';
import 'package:likewallet/device_utils.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/screen_util.dart';
import 'package:http/http.dart' as http;
import 'package:likewallet/libraryman/auth.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:likewallet/app_config.dart';

class ListBanking extends StatefulWidget {
  ListBanking({required this.symbol});
  final String symbol;
  _ListBanking createState() => new _ListBanking(symbol: symbol);
}

class _ListBanking extends State<ListBanking> {
  _ListBanking({required this.symbol});
  final String symbol;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    auth = new Auth();
    setSymbol();
  }

  final _formKey = GlobalKey<FormState>();
  late BaseAuth auth;

  final fireStore = FirebaseFirestore.instance;
  final TextEditingController nameAccount = TextEditingController();
  final TextEditingController numberAccount = TextEditingController();
  bool show = false;
  late List data;
  late String uid;
  String url = '';
  String fullname = '';
  List listbank = [];
  _getListBank(country) async {
    var url = Uri.https(env.apiUrl, '/listBankWithdraw');
    final response = await http.post(url);
    setState(() {
      data = json.decode(response.body)['result'];
      // print(data);
      data.forEach((doc) {
        if (doc['country'] == country) {
          listbank.add(doc);
        }
      });
    });
  }

  Future<dynamic> setSymbol() async {
    String country = 'TH';
    if (symbol == 'THB') country = 'TH';
    if (symbol == 'USD') country = 'KM';
    if (symbol == 'LAK') country = 'LAO';

    await _getListBank(country);
  }

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Scaffold(
      body: GestureDetector(
          onTap: () {
            setState(() {
              DeviceUtils.hideKeyboard(context);
            });
          },
          child: Stack(children: <Widget>[
            //พื้นหลังสี Blue2
            _background(),
            //รายการบัญชีที่สามารถทรายการได้
            _buildListView(),
            //หัวข้อ
            _head(),
            //ฟอร์มเพิ่มบัญชีธนาคาร
            _addAccountBanking(context, show),
            //ปุ่มกลับ
            Positioned(
                top: mediaQuery(context, 'height', 100),
                child: backButton(context, LikeWalletAppTheme.gray)),
          ])),
    );
  }

  Widget _background() {
    return Container(
        height: MediaQuery.of(context).size.height,
        decoration: BoxDecoration(
            gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          stops: [0.4, 0.6, 0.7, 0.8, 0.85],
          colors: [
            // Colors are easy thanks to Flutter's Colors class.
            LikeWalletAppTheme.bule2.withOpacity(1),
            LikeWalletAppTheme.bule2.withOpacity(0.94),
            LikeWalletAppTheme.bule2.withOpacity(0.92),
            LikeWalletAppTheme.bule2.withOpacity(0.9),
            LikeWalletAppTheme.bule2.withOpacity(0.88),
          ],
        )));
  }

  Widget _head() {
    return Column(
      children: <Widget>[
        Container(
          color: LikeWalletAppTheme.bule2,
          height: mediaQuery(context, 'height', 256),
          child: Container(
            padding: EdgeInsets.only(bottom: mediaQuery(context, 'height', 20)),
            alignment: Alignment.bottomCenter,
            width: MediaQuery.of(context).size.width,
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: Colors.grey,
                  width: mediaQuery(context, 'width', 1.5),
                ),
              ),
            ),
            child: new Text(
              AppLocalizations.of(context)!.translate('title_bank'),
              style: TextStyle(
                  letterSpacing: 1,
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  color: LikeWalletAppTheme.white.withOpacity(1),
                  fontSize: mediaQuery(context, 'height', 42),
                  fontWeight: FontWeight.w500),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildListView() {
    return Padding(
        padding: EdgeInsets.only(
          top: mediaQuery(context, 'height', 256),
        ),
        child: ListView.builder(
            padding: const EdgeInsets.all(16.0),
            itemCount: listbank == null ? 0 : listbank.length,
            itemBuilder: (context, index) {
              return TextButton(
                onPressed: () {
                  setState(() {
                    show = true;
                    url = listbank[index]['url'];
                    fullname = listbank[index]['short'];
                    print(listbank[index]['url']);
                    print(listbank[index]['fullname']);
                  });
                },
                child: _buildImageColumn(listbank[index]),
              );
            }));
  }

  Widget _buildImageColumn(dynamic item) => Container(
      color: Colors.transparent,
      margin: const EdgeInsets.all(4),
      child: ListTile(
        leading: new CachedNetworkImage(
          imageUrl: item['url'],
          placeholder: (context, url) => new CircularProgressIndicator(),
          // errorWidget: (context, url, error) => new Icon(Icons.error),
          fadeOutDuration: new Duration(seconds: 1),
          fadeInDuration: new Duration(seconds: 3),
          height: mediaQuery(context, 'height', 126),
        ),
        title: Text(
          item['fullname'],
          style: TextStyle(
            color: LikeWalletAppTheme.white,
            fontSize: mediaQuery(context, 'height', 42),
            fontFamily: AppLocalizations.of(context)!.translate('font1'),
          ),
        ),
      ));

  Widget _addAccountBanking(context, show) {
    return GestureDetector(
        onTap: () {},
        child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                  bottomRight: Radius.circular(37.0),
                  bottomLeft: Radius.circular(37.0)),
              color: LikeWalletAppTheme.white,
            ),
            child: AnimatedContainer(
              duration: Duration(milliseconds: 300),
              alignment: Alignment.topCenter,
              width: MediaQuery.of(context).size.width,
              height: show
                  ? mediaQuery(context, 'height', 1250.11)
                  : mediaQuery(context, 'height', 0),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  stops: [0.4, 0.6, 0.7, 0.8, 0.85],
                  colors: [
                    // Colors are easy thanks to Flutter's Colors class.
                    LikeWalletAppTheme.white.withOpacity(1),
                    LikeWalletAppTheme.white.withOpacity(0.94),
                    LikeWalletAppTheme.white.withOpacity(0.92),
                    LikeWalletAppTheme.white.withOpacity(0.9),
                    LikeWalletAppTheme.white.withOpacity(0.9),
                  ],
                ),
                borderRadius: BorderRadius.only(
                    bottomRight: Radius.circular(35.0),
                    bottomLeft: Radius.circular(35.0)),
                boxShadow: [
                  new BoxShadow(
                      color: Colors.black.withOpacity(0.16),
                      offset: new Offset(0, 3),
                      blurRadius: 3.0,
                      spreadRadius: 0.0),
                ],
                color: LikeWalletAppTheme.white,
              ),
              child: SingleChildScrollView(
                child: url != null
                    ? Column(
                        children: <Widget>[
                          _name(),
                          _inputAccountBanking(),
                          _buttonOK()
                        ],
                      )
                    : Container(),
              ),
            )));
  }

  Widget _name() {
    return Container(
        height: mediaQuery(context, 'height', 256),
        alignment: Alignment.bottomCenter,
        padding: EdgeInsets.only(
          bottom: mediaQuery(context, 'height', 20),
        ),
        width: double.infinity,
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: LikeWalletAppTheme.gray,
              width: mediaQuery(context, 'width', 1.5),
            ),
          ),
        ),
        child: Text(
          AppLocalizations.of(context)!.translate('add_account_details'),
          style: TextStyle(
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              color: LikeWalletAppTheme.bule2,
              fontSize: mediaQuery(context, 'height', 42),
              fontWeight: FontWeight.w500),
        ));
  }

  Widget _selectedAccount() {
    return GestureDetector(
      onTap: () {
        setState(() {
          show = false;
        });
      },
      child: Row(
        children: <Widget>[
          Container(
            // padding: EdgeInsets.only(
            //   right: mediaQuery(context, 'width', 50),
            // ),
            child: url == ''
                ? Container()
                : CachedNetworkImage(
                    imageUrl: url,
                    placeholder: (context, url) =>
                        new CircularProgressIndicator(),
                    // errorWidget: (context, url, error) => new Icon(Icons.error),
                    fadeOutDuration: new Duration(seconds: 1),
                    fadeInDuration: new Duration(seconds: 3),
                    height: mediaQuery(context, 'height', 126),
                  ),
          ),
          SizedBox(
            width: 20.w,
          ),
          Text(
            fullname,
            style: TextStyle(
              color: LikeWalletAppTheme.gray,
              fontSize: mediaQuery(context, 'height', 42),
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _inputAccountBanking() {
    return Padding(
        padding: EdgeInsets.only(
          top: mediaQuery(context, 'height', 100),
          left: mediaQuery(context, 'width', 150),
          right: mediaQuery(context, 'width', 150),
        ),
        child: Form(
            key: _formKey,
            child: Column(
              children: <Widget>[
                _selectedAccount(),
                Container(
                  padding: EdgeInsets.only(
                    top: mediaQuery(context, 'height', 50),
                  ),
                  child: TextFormField(
                    controller: nameAccount,
                    keyboardType: TextInputType.text,
                    validator: (val) =>
                        val!.length < 12 ? 'กรุณาใส่ชื่อบัญชี' : null,
                    style: TextStyle(
                      color: LikeWalletAppTheme.gray1,
                      fontSize: MediaQuery.of(context).size.height *
                          Screen_util('height', 48),
                    ),
                    decoration: InputDecoration(
                        labelText: AppLocalizations.of(context)!
                            .translate('add_account_name'),
                        labelStyle: TextStyle(
                            color: LikeWalletAppTheme.gray,
                            fontFamily: AppLocalizations.of(context)!
                                .translate('font1'),
                            fontSize: MediaQuery.of(context).size.height *
                                Screen_util('height', 34),
                            fontWeight: FontWeight.normal),
                        enabledBorder: UnderlineInputBorder(
                            borderSide: BorderSide(
                                color:
                                    LikeWalletAppTheme.gray.withOpacity(0.5))),
                        focusedBorder: UnderlineInputBorder(
                            borderSide: BorderSide(
                                color:
                                    LikeWalletAppTheme.gray.withOpacity(0.5)))),
                  ),
                ),
                Container(
                  padding: EdgeInsets.only(
                    top: mediaQuery(context, 'height', 80),
                  ),
                  child: TextFormField(
                    controller: numberAccount,
                    validator: (val) =>
                        val!.length < 10 ? 'กรุณาใส่เลขบัญชี' : null,
                    style: TextStyle(
                      color: LikeWalletAppTheme.gray,
                      fontSize: MediaQuery.of(context).size.height *
                          Screen_util('height', 48),
                    ),
                    keyboardType: TextInputType.datetime,
                    decoration: InputDecoration(
                        labelText: AppLocalizations.of(context)!
                            .translate('add_account_number'),
                        labelStyle: TextStyle(
                            color: LikeWalletAppTheme.gray,
                            fontFamily: AppLocalizations.of(context)!
                                .translate('font1'),
                            fontSize: MediaQuery.of(context).size.height *
                                Screen_util('height', 34),
                            fontWeight: FontWeight.normal),
                        enabledBorder: UnderlineInputBorder(
                            borderSide: BorderSide(
                                color:
                                    LikeWalletAppTheme.gray.withOpacity(0.5))),
                        focusedBorder: UnderlineInputBorder(
                            borderSide: BorderSide(
                                color:
                                    LikeWalletAppTheme.gray.withOpacity(0.5)))
                        //border: InputBorder.none
                        ),
                  ),
                ),
//            _buttonOK()
              ],
            )));
  }

  Widget _buttonOK() {
    return Container(
        alignment: Alignment.center,
        margin: EdgeInsets.only(
          top: mediaQuery(context, 'height', 100),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            GestureDetector(
              onTap: () {
                setState(() {
                  show = false;
                });
              },
              child: Container(
                  width: mediaQuery(context, 'width', 930) / 2,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: <Widget>[
                      Image.asset(
                        LikeWalletImage.icon_button_cancel_white,
                        height: mediaQuery(context, 'height', 123),
                      ),
                      Text(
                          AppLocalizations.of(context)!
                              .translate('add_account_cancel'),
                          style: TextStyle(
                            fontFamily: AppLocalizations.of(context)!
                                .translate('font1'),
                            color: LikeWalletAppTheme.gray1,
                            fontWeight: FontWeight.normal,
                            fontSize: MediaQuery.of(context).size.height *
                                Screen_util("height", 36),
                          )),
                    ],
                  )),
            ),
            GestureDetector(
                onTap: () async {
                  await DeviceUtils.hideKeyboard(context);
                  print('ddf');
                  // If the form is valid, display a Snackbar.
                  if (nameAccount.text.toString().length < 5 ||
                      numberAccount.text.toString().length < 10) {
                    showColoredToast(AppLocalizations.of(context)!
                        .translate('add_account_check_input'));
                  } else {
                    String pattern = r'(^(?:[0]9)?[0-9]{10,}$)';
                    RegExp regex = new RegExp(pattern);
                    if (numberAccount.text.length == 0) {
                      showShortToast(
                          AppLocalizations.of(context)!
                              .translate('add_account_alert'),
                          Colors.cyan);
                      // return 'Please enter mobile number';
                    } else if (!regex.hasMatch(numberAccount.text)) {
                      print('not match');
                      showShortToast(
                          AppLocalizations.of(context)!
                              .translate('add_account_alert'),
                          Colors.red);
                      // return 'Please enter valid mobile number';
                    } else if (regex.hasMatch(numberAccount.text)) {
                      var data = await auth.getCurrentUser();
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) => ExampleKYCBank(
                                    nameBank: fullname,
                                    imageBank: url,
                                    nameBookBank: nameAccount.text,
                                    numberBookBank: numberAccount.text,
                                    phone: data!.phoneNumber.toString(),
                                    uid: data.uid,
                                  )));
                    }
                  }
                },
                child: Container(
                    width: mediaQuery(context, 'width', 930) / 2,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: <Widget>[
                        Text(
                            AppLocalizations.of(context)!
                                .translate('add_account_add'),
                            style: TextStyle(
                              fontFamily: AppLocalizations.of(context)!
                                  .translate('font1'),
                              color: LikeWalletAppTheme.gray1,
                              fontWeight: FontWeight.normal,
                              fontSize: MediaQuery.of(context).size.height *
                                  Screen_util("height", 36),
                            )),
                        Image.asset(
                          LikeWalletImage.icon_button_next_black,
                          height: mediaQuery(context, 'height', 123),
                        ),
                      ],
                    )))
          ],
        ));
  }

  void saveFavorite() async {
    await auth.getCurrentUser().then((decodeToken) {
      // print(decodeToken);
      uid = decodeToken!.uid;
      print(uid);
      print(fullname);
      print(nameAccount.text.toString());
      print(numberAccount.text.toString());
      fireStore.collection('favorites').doc(uid).collection('bank').add({
        'urlBank': url,
        'nameBank': fullname,
        'nameAccount': nameAccount.text.toString(),
        'numberAccount': numberAccount.text.toString(),
      });
    });
    setState(() {
      show = false;
    });
  }
}

class ListBank {
  final String url;
  final String short;
  final String fullname;

  ListBank({
    required this.url,
    required this.short,
    required this.fullname,
  });

  factory ListBank.fromJson(Map<String, dynamic> json) {
    return ListBank(
      url: json['url'],
      short: json['short'],
      fullname: json['fullname'],
    );
  }
}
