import 'dart:convert';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/bank/add_banking/listBanking.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:likewallet/libraryman/open_web_notitle.dart';
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:likewallet/libraryman/keyboard_done_widget.dart';
import 'dart:io' show Platform;
import 'package:likewallet/app_config.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:likewallet/libraryman/auth.dart';

import 'package:likewallet/libraryman/configuration_service.dart';
import 'package:likewallet/libraryman/address_service.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:shared_preferences/shared_preferences.dart';

class MethodBuy extends StatefulWidget {
  MethodBuy({required this.rate, required this.amount, required this.fee});
  final double rate;
  final double amount;
  final double fee;
  _MethodBuy createState() =>
      new _MethodBuy(rate: rate, amount: amount, fee: fee);
}

class _MethodBuy extends State<MethodBuy> {
  _MethodBuy({required this.rate, required this.amount,required this.fee});
  final double rate;
  final double amount;
  final double fee;
  late File _imageFile;
  late SharedPreferences pref;
  late IAddressService addressService;
  late IConfigurationService configETH;

  late BaseAuth auth;
  FirebaseFirestore fireStore = FirebaseFirestore.instance;
  FocusNode doneFocusNode = new FocusNode();
  FocusNode doneFocusNodeAmount = new FocusNode();
  late OverlayEntry? overlayEntry;
  bool show = false;
  TextEditingController accountNumber = TextEditingController();
  TextEditingController photo = TextEditingController();
  TextEditingController amountBuy = TextEditingController();
  final ImagePicker _picker = ImagePicker();
  int keyword = 1;
  String bank = "";
  bool _saving = false;
  KeyboardVisibilityController keyboardVisibilityController = KeyboardVisibilityController();

  void showColoredToast(msg, colors) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_LONG,
        backgroundColor: colors,
        textColor: Colors.white);
  }

  List ListData = [];
  Future<List<String>> upload(File file) async {
    if (file == null) return ['false'];
    String base64Image = base64Encode(file.readAsBytesSync());
    String fileName = file.path.split("/").last;
    var url = Uri.https(env.apiCheck, '/uploadImageBuy');
    final response = await http.post(url, body: {
      "image": base64Image,
      "name": fileName,
    });
    print(response.statusCode);
    final body = json.decode(response.body);

    print(body["result"]);
    print(body["result"]["id"]);
    print(body["result"]["url"]);
    return [body["result"]["id"], body["result"]["url"]];
  }

  Future<bool> saveSlip(String pathPic) async {
    User? user = await auth.getCurrentUser();
    var url = Uri.https(env.apiUrl, '/buyLikepoint');
    var response = await http.post(url, body: {
      '_token': await auth.getTokenFirebase(),
      'paymentMethod': 'bank',
      'bankName': 'Likewallet',
      'accountNumber': 'Likewallet',
      'baht': amountBuy.text.toString(),
      'slip': pathPic,
      'phoneNumber': user!.phoneNumber,
      'address': configETH.getAddress()
    });
    print(response);
    var body = json.decode(response.body);
    print(body["statusCode"]);
    int statusCode = body["statusCode"];
    if (statusCode == 200) {
      print('save');
      setState(() {
        _saving = false;
        print(_saving);
      });
      return true;
    } else {
      setState(() {
        _saving = false;
      });
      return false;
    }
  }

  setInit() async {
    pref = await SharedPreferences.getInstance();
    configETH = new ConfigurationService(pref);
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    auth = Auth();
    setInit();
    //keyboard show done
    if (Platform.isIOS) {
      doneFocusNode.addListener(() {
        bool hasFocus = doneFocusNode.hasFocus;
        if (hasFocus)
          showOverlay(context);
        else
          removeOverlay();
      });
      doneFocusNodeAmount.addListener(() {
        bool hasFocus = doneFocusNodeAmount.hasFocus;
        if (hasFocus)
          showOverlay(context);
        else
          removeOverlay();
      });

      keyboardVisibilityController.onChange.listen((bool visible) {
        removeOverlay();
      });
    }
  }

  //done button zone
  showOverlay(BuildContext context) {
    if (overlayEntry != null) return;
    OverlayState? overlayState = Overlay.of(context);
    overlayEntry = OverlayEntry(builder: (context) {
      return Positioned(
          bottom: MediaQuery.of(context).viewInsets.bottom,
          right: 0.0,
          left: 0.0,
          child: InputDoneView());
    });

    overlayState!.insert(overlayEntry!);
  }

  removeOverlay() {
    if (overlayEntry != null) {
      overlayEntry!.remove();
      overlayEntry = null;
    }
  }

  @override
  void dispose() {
    doneFocusNode.dispose();
    doneFocusNodeAmount.dispose();
    super.dispose();
  }

  buildForPhone() {
    return Scaffold(
//      bottomNavigationBar: NavigationBar(),
      backgroundColor: LikeWalletAppTheme.white,
      body: Stack(
        alignment: Alignment.center,
        children: <Widget>[
          _head(),

          Positioned(
            top: mediaQuery(context, 'height', 100),
            child: backButton(context, LikeWalletAppTheme.gray),
          ),

//          Positioned(
//              top: mediaQuery(context, 'height', 255),
//              child: _ContainerAccount()),
          Positioned(
            top: MediaQuery.of(context).size.height * 0.***********,
            left: MediaQuery.of(context).size.width * 0.15,
            right: MediaQuery.of(context).size.width * 0.15,
            child: new Container(
//              color:Colors.blue,
              height: MediaQuery.of(context).size.height * 0.1,
              width: MediaQuery.of(context).size.width * 0.2,
              alignment: Alignment.topCenter,
              child: new Container(
                alignment: Alignment.bottomCenter,
                height: MediaQuery.of(context).size.height * 0.03,
                width: MediaQuery.of(context).size.width * 0.9,
                child: Text(
                  bank,
                  style: TextStyle(
                      color: Color(0xff141322).withOpacity(1),
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      fontWeight: FontWeight.normal,
                      fontSize:
                          MediaQuery.of(context).size.height * 0.***********),
                ),
              ),
            ),
          ),
          Positioned(
            top: MediaQuery.of(context).size.height *
                Screen_util("height", 1000),
            child: _howTo(),
          ),
        ],
      ),
    );
  }

  Future<void> captureImage(ImageSource imageSource) async {
    try {
      final imageFile = await _picker.getImage(source: imageSource);
      setState(() {
        photo.text = imageFile!.path.substring(0, 20) + '....';
        _imageFile = File(imageFile.path);
      });
    } catch (e) {
      print(e);
    }
  }



  @override
  Widget build(BuildContext context) {
    return ModalProgressHUD(
        inAsyncCall: _saving,
        opacity: 0.1,
        progressIndicator: CustomLoading(),
        child: Scaffold(body: buildForPhone()));
  }

  Widget _head() {
    return Column(
      children: <Widget>[
        Container(
          color: LikeWalletAppTheme.bule2,
          height: mediaQuery(context, 'height', 256),
          child: Container(
            padding: EdgeInsets.only(bottom: mediaQuery(context, 'height', 50)),
            alignment: Alignment.bottomCenter,
            width: MediaQuery.of(context).size.width,
            child: new Text(
              AppLocalizations.of(context)!.translate('slip_title'),
              style: TextStyle(
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  color: LikeWalletAppTheme.lemon.withOpacity(1),
                  fontSize: mediaQuery(context, 'height', 42),
                  fontWeight: FontWeight.bold),
            ),
          ),
        ),
      ],
    );
  }

  bool selected = false;
  Widget _howTo() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: <Widget>[
        TextButton.icon(
            onPressed: () {
              Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => WebOpenNoTitle(
                          title: AppLocalizations.of(context)!.translate('how_to_buy_bank_transfer'),
                          url:
                              "https://sites.google.com/prachakij.com/likewallet-buy/home")));
            },
            icon: Icon(
              Icons.help_outline,
              color: Colors.grey,
            ),
            label: Text(
              AppLocalizations.of(context)!.translate('how_to_buy_bank_transfer'),
              style: TextStyle(color: Colors.grey),
            )),
      ],
    );
  }

  Widget _ContainerAccount() {
    return GestureDetector(
        onTap: () {
          setState(() {
            selected = !selected;
          });
        },
        child: AnimatedContainer(
            alignment: Alignment.bottomCenter,
            width: MediaQuery.of(context).size.width,
            height: selected
                ? mediaQuery(context, 'height', 600)
                : mediaQuery(context, 'height', 356),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                  bottomRight: Radius.circular(40.0),
                  bottomLeft: Radius.circular(40.0)),
              gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    LikeWalletAppTheme.bule2,
                    LikeWalletAppTheme.bule2,
                    LikeWalletAppTheme.bule2.withOpacity(0.89)
                  ]),
              boxShadow: [
                new BoxShadow(
                    color: Colors.black.withOpacity(0.16),
                    offset: new Offset(0, 3),
                    blurRadius: 5.0,
                    spreadRadius: 1.0),
              ],
              color: LikeWalletAppTheme.white,
            ),
            duration: Duration(milliseconds: 200),
            child: Column(
              children: <Widget>[
                SizedBox(
                    height: mediaQuery(context, 'height', 200),
                    child: _ButtonAddAccount()),
              ],
            )));
  }

  Widget s() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: <Widget>[
        Container(
          margin: EdgeInsets.only(right: mediaQuery(context, 'width', 30)),
          child: Image.network(
            'https://kasikornbank.com/th/personal/Digital-banking/PublishingImages/QR/apply-icon-01.png',
            height: mediaQuery(context, 'height', 87.76),
          ),
        ),
        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text('Suwadee Phongsopa (SCB)',
                style: TextStyle(
                  color: LikeWalletAppTheme.white,
                )),
            Text('148-2-73808-9',
                style: TextStyle(
                  color: LikeWalletAppTheme.white,
                )),
          ],
        ),
      ],
    );
  }

  Widget _ButtonAddAccount() {
    return TextButton(
        onPressed: () {
          print('s');
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => ListBanking(symbol: '',)),
          );
        },
        child: Row(
          mainAxisAlignment: ListData.length < 1
              ? MainAxisAlignment.center
              : MainAxisAlignment.end,
          children: <Widget>[
            Text('Add bank account',
                style: TextStyle(
                  color: LikeWalletAppTheme.gray2,
                  fontSize: mediaQuery(
                    context,
                    'height',
                    39,
                  ),
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                )),
            Icon(
              Icons.add_circle,
              color: LikeWalletAppTheme.gray2,
              size: mediaQuery(context, 'height', 103),
            ),
          ],
        ));
  }
}
