import 'dart:io';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:likewallet/Theme.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/middleware/callCamera.dart';
import 'package:likewallet/screen_util.dart';

class ExampleKYCBank extends StatefulWidget {
  ExampleKYCBank({
    required this.nameBank,
    required this.imageBank,
    required this.nameBookBank,
    required this.numberBookBank,
    required this.uid,
    required this.phone,
  });

  final String nameBank;
  final String imageBank;
  final String nameBookBank;
  final String numberBookBank;
  final String uid;
  final String phone;

  _ExampleKYCBank createState() => new _ExampleKYCBank();
}

class _ExampleKYCBank extends State<ExampleKYCBank> {
  late OnCallCamera camera;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    camera = CallCamera();
  }

  saveBooking({required String image}) async {
    final res = await FirebaseFirestore.instance
        .collection('kycCashOut')
        .doc('bookBank')
        .collection(widget.uid)
        .doc(widget.numberBookBank)
        .set({
      "nameBank": widget.nameBank,
      "imageBank": widget.imageBank,
      "nameBookBank": widget.nameBookBank,
      "numberBookBank": widget.numberBookBank,
      "phone": widget.phone,
      "timestamp": DateTime.now(),
      "image": image,
      "status": "pending",
      "note": ""
    }).then((value) async {
      Navigator.of(context).pop();
      Navigator.of(context).pop();

      showShortToast(
          AppLocalizations.of(context)!.translate('add_account_alert_success'),
          LikeWalletAppTheme.bule1);
    });
  }

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Scaffold(
      backgroundColor: LikeWalletAppTheme.bule2,
      body: SingleChildScrollView(
        child: Container(
          height: MediaQuery.of(context).size.height,
          child: Column(
            children: [
              SizedBox(height: 139.h),
              Padding(
                padding: EdgeInsets.only(left: 75.w),
                child: backButton(context, Colors.grey),
              ),
              SizedBox(height: 74.h),
              text(AppLocalizations.of(context)!.translate('kyc_bank_line1')),
              SizedBox(height: 39.h),
              text(AppLocalizations.of(context)!.translate('kyc_bank_line2')),
              SizedBox(height: 73.h),
              CachedNetworkImage(
                imageUrl: 'https://bit.ly/30pcZkm',
                placeholder: (context, url) => Center(
                    child: SpinKitFadingCircle(
                  color: LikeWalletAppTheme.bule1,
                  size: 70.h,
                )),
                // errorWidget: (context, url, error) => new Icon(Icons.error),
                fadeOutDuration: new Duration(seconds: 1),
                fadeInDuration: new Duration(seconds: 3),
                height: 942.h,
              ),
              SizedBox(height: 172.h),
              text(AppLocalizations.of(context)!.translate('kyc_bank_line3')),
              SizedBox(height: 73.h),
              buttonOK(),
              Expanded(child: Container()),
            ],
          ),
        ),
      ),
    );
  }

  Widget text(text) => Container(
        width: 879.w,
        child: Text(
          text,
          style: TextStyle(
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              color: LikeWalletAppTheme.gray1,
              fontWeight: FontWeight.normal,
              fontSize: 39.h),
          textAlign: TextAlign.center,
        ),
      );

  Widget buttonOK() => InkWell(
        onTap: () {
          getCamera();
        },
        child: Container(
          alignment: Alignment.center,
          height: MediaQuery.of(context).size.height *
              0.***********, // height of the button
          width: MediaQuery.of(context).size.height *
              0.***********, // width of the button
          child: new Text(
            AppLocalizations.of(context)!.translate('bankingTran_button'),
            style: TextStyle(
                color: Color(0xffB4E60D),
                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                fontWeight: FontWeight.normal,
                fontSize: MediaQuery.of(context).size.height * 0.************),
          ),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Color(0xff2B3038).withOpacity(1),
            boxShadow: [
              BoxShadow(
                spreadRadius: 5,
                blurRadius: 14,
                color: Colors.black.withOpacity(0.16),
                offset: Offset(
                  6.0,
                  8.0,
                ),
              ),
            ],
          ),
        ),
      );

  Future getCamera() async {
    print('callCamera');
    var image = await camera.getImage();
    if (image == null) {
      print('ยังไม่ได่เลือกรูป');
    } else {
      print(image);
      String upload = await camera.uploadImage(file: File(image.path));
      print(upload);
      if (upload == '' || upload == 'false' || upload == null) {
      } else {
        saveBooking(image: upload);
      }
      return null;
    }
  }
}
