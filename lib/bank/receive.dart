import 'dart:convert';
import 'dart:typed_data';
import 'dart:io';
import 'dart:ui';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/close_maintenance.dart';

import 'package:likewallet/libraryman/app_local.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:likewallet/libraryman/configuration_service.dart';
import 'package:likewallet/main.dart';
import 'package:likewallet/middleware/check_maintenance/check_maintenance.dart';
import 'package:likewallet/middleware/getLanguage.dart';
import 'package:likewallet/model/pageStatus.dart';
import 'package:likewallet/screen_util.dart';
import 'package:path_provider/path_provider.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'dart:ui' as ui;
import 'package:flutter/rendering.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:share/share.dart';
import 'package:likewallet/Theme.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as riverpod;
import 'package:shared_preferences/shared_preferences.dart';

/* ----------------| RECEIVE |--------------*/
class Receive extends StatefulWidget {
  // Receive({this.dataString});
  // final String dataString;
  @override
  State<StatefulWidget> createState() {
    return _Receive();
  }
}

class _Receive extends State<Receive>
    with WidgetsBindingObserver, TickerProviderStateMixin {
  // _Receive({this.dataString});
  String dataString = '';
  late IConfigurationService configETH;
  GlobalKey<ScaffoldState> _scaffoldKey = new GlobalKey();
  GlobalKey globalKey = new GlobalKey();
  late CheckAbout checkAbout;
  late OnLanguage language;
  bool inside = false;
  late Uint8List imageInMemory;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    checkAbout = OnCheckAbout();
    language = CallLanguage();
    checkFirst();
  }

  checkFirst() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    configETH = new ConfigurationService(pref);
    setState(() => dataString = configETH.getAddress());
    PageMaintenance statusPage = await checkAbout.checkTierPermission(
        tierLevel: context.read(tierLevel).state, page: 'receive');

    if (statusPage.status == 'active') {
      //เริ่มทำงาน
      print('active');
      // setInitState();
    } else {
      //ปิด maintenance
      print('inactive');
      final lang = await language.getLanguage();
      final title = await checkAbout.selectLanguage(
          language: lang, detail: statusPage.title);
      final detail = await checkAbout.selectLanguage(
          language: lang, detail: statusPage.detail);
      final detailTime = await checkAbout.selectLanguage(
          language: lang, detail: statusPage.detail_time);
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
            builder: (context) => CloseMaintenance(
              title: title,
              detail: detail,
              detailTime: detailTime,
              url: statusPage.url,
            )),
      );
    }
  }

  void showShortToast(msg) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_SHORT,
        backgroundColor: Colors.cyan,
        textColor: Colors.white);
  }

  Future<Uint8List> _capturePng() async {
    print('inside');
    inside = true;
    RenderRepaintBoundary boundary = globalKey.currentContext!.findRenderObject() as RenderRepaintBoundary;

    ui.Image image = await boundary.toImage(pixelRatio: 3.0);
    ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    Uint8List pngBytes = byteData!.buffer.asUint8List();
    String bs64 = base64Encode(pngBytes);
    print("go here");
    if (Platform.isAndroid) {
      if (await Permission.photos.request().isGranted) {
        // Either the permission was already granted before or the user just granted it.
      }

      if (await Permission.photos.isRestricted) {
        // The OS restricts access, for example because of parental controls.
      }
//      bool statusPermision = await Permission.photos.isGranted;
//      if (statusPermision != true) {
//        await Permission.manageExternalStorage.request();  
//      }

//      print('permission ' + statusPermision.toString());

    } else {
      bool statusPermision = await Permission.photos.isGranted;
      if (statusPermision != true) {
        await Permission.manageExternalStorage.request();
        await Permission.photos.request();
      }
    }

    // Use plugin [path_provider] to export image to storage
    final result = await ImageGallerySaver.saveImage(
        Uint8List.fromList(pngBytes.buffer.asInt8List()));
    print(result);
    print('png done');
    FadeSaveReceipt(
        context, AppLocalizations.of(context)!.translate('save_qr_code'), this);

    setState(() {
      imageInMemory = pngBytes;
      inside = false;
    });

    return pngBytes;
  }

  @override
  Widget build(BuildContext context) {
    // ScreenUtil.init(BoxConstraints(
    //     maxWidth: MediaQuery.of(context).size.width,
    //     maxHeight: MediaQuery.of(context).size.height) as BuildContext, designSize: Size(1080, 2340));

    // TODO: implement build
    return SingleChildScrollView(
        child: Stack(
          children: <Widget>[
            Align(
              alignment: Alignment.center,
              child: new Container(
                decoration: BoxDecoration(
                  color: LikeWalletAppTheme.white1,
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    stops: [0.0, 0.2, 0.5],
                    colors: [
                      // Colors are easy thanks to Flutter's Colors class.
                      Colors.white,
                      Colors.white,
                      LikeWalletAppTheme.white1
                    ],
                  ),
                ),
                // height: mediaQuery(context, 'height', 1500),
                padding: EdgeInsets.only(
                  top: mediaQuery(context, 'height', 200),
                ),
                width: MediaQuery.of(context).size.width,
                child: new Column(
                  children: <Widget>[
                    new Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: <Widget>[
                        new Container(
                          alignment: Alignment.center,
                          margin: EdgeInsets.only(
                            top: mediaQuery(context, 'height', 135),
                            bottom: mediaQuery(context, 'height', 75),
                          ),
                          // height: MediaQuery.of(context).size.height * 0.1,
                          width: mediaQuery(context, 'height', 678),
                          child: new Text(
                            dataString,
                            style: TextStyle(
                                color: Color(0xff707071),
                                fontFamily: 'Proxima Nova Light',
                                fontWeight: FontWeight.normal,
                                letterSpacing: 1.5,
                                fontSize: mediaQuery(context, 'height', 39)),
                            textAlign: TextAlign.center,
                          ),
//                   color:  Color(0xff6C6B6D),
                        ),
                      ],
                    ),
                    new Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[],
                    ),
                    Center(
                      child: RepaintBoundary(
                          key: globalKey,
                          child: Container(
                              child: Stack(
                                  alignment: Alignment(0, 0),
                                  children: <Widget>[
                                    Container(
                                      height: 530.h,
                                      width: 530.h,
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.black.withOpacity(0.16),
                                            offset: Offset(0, 5),
                                            blurRadius: 12,
                                          ),
                                        ],
                                      ),
                                    ),
                                    Container(
                                      height: 600.h,
                                      width: 600.h,
                                      child: QrImageView(
                                        backgroundColor: LikeWalletAppTheme.white,
                                        data: dataString,
                                        size: 530.h,
                                        embeddedImage:
                                        AssetImage(LikeWalletImage.qr_likepoint),
                                        embeddedImageStyle: QrEmbeddedImageStyle(
                                          size: Size(
                                            mediaQuery(context, 'height', 100.6),
                                            mediaQuery(context, 'height', 100.6),
                                          ),
                                        ),
                                        errorCorrectionLevel: QrErrorCorrectLevel.Q,
                                        gapless: false,
                                        version: 9,
                                      ),
                                    ),
                                  ]))),
                    ),
                    SizedBox(
                      height: mediaQuery(context, 'height', 105),
                    ),
                    new Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        TextButton(
                          onPressed: () {
                            _capturePng();
                          },
                          child: Column(
                            children: <Widget>[
                              Image.asset(
                                'assets/image/Save_receipt.png',
                                height: mediaQuery(context, 'height', 120),
                              ),
                              SizedBox(
                                height: mediaQuery(context, 'height', 30),
                              ),
                              Text(
                                AppLocalizations.of(context)!.translate('button_qr_save'),
                                style: TextStyle(
                                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                                  fontSize: mediaQuery(context, 'height', 38),
                                  color: LikeWalletAppTheme.gray1,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                        new TextButton(
                          onPressed: () {
                            Clipboard.setData(new ClipboardData(text: dataString))
                                .then((d) {
                              showShortToast('Copied address');
                            });
                          },
                          child: Container(
                            child: Column(
                              children: <Widget>[
                                Image.asset(
                                  LikeWalletImage.icon_copy_address,
                                  height: mediaQuery(context, 'height', 120),
                                ),
                                SizedBox(
                                  height: mediaQuery(context, 'height', 30),
                                ),
                                Text(
                                  AppLocalizations.of(context)!
                                      .translate('bankingreceive_button_copy'),
                                  style: TextStyle(
                                    fontFamily: AppLocalizations.of(context)!
                                        .translate('font1'),
                                    fontSize: mediaQuery(context, 'height', 38),
                                    color: LikeWalletAppTheme.gray1,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                        ),
                        TextButton(
                          onPressed: () {
                            Share.share(dataString, subject: 'LIKE');
                          },
                          child: Container(
                            child: Column(
                              children: <Widget>[
                                Image.asset(
                                  LikeWalletImage.icon_feedback,
                                  height: mediaQuery(context, 'height', 120),
                                ),
                                SizedBox(
                                  height: mediaQuery(context, 'height', 30),
                                ),
                                Text(
                                  AppLocalizations.of(context)!
                                      .translate('bankingreceive_button_address'),
                                  style: TextStyle(
                                    fontFamily: AppLocalizations.of(context)!
                                        .translate('font1'),
                                    fontSize: mediaQuery(context, 'height', 38),
                                    color: LikeWalletAppTheme.gray1,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ));
  }

  Future<void> _captureAndSharePng() async {
    try {
      RenderRepaintBoundary boundary = globalKey.currentContext!.findRenderObject() as RenderRepaintBoundary;
      var image = await boundary.toImage();
      ByteData? byteData = await image.toByteData(format: ImageByteFormat.png);
      Uint8List pngBytes = byteData!.buffer.asUint8List();
      final tempDir = await getTemporaryDirectory();
      final file = await new File('${tempDir.path}/image.png').create();
      await file.writeAsBytes(pngBytes);
      final channel = const MethodChannel('channel:me.alfian.share/share');
      channel.invokeMethod('shareFile', 'image.png');
    } catch (e) {
      print(e.toString());
    }
  }

//  Future<Uint8List> _capturePng() async {
//    RenderRepaintBoundary boundary =
//        globalKey.currentContext.findRenderObject();
//    ui.Image image = await boundary.toImage(pixelRatio: 3.0);
//    ByteData byteData = await image.toByteData(format: ui.ImageByteFormat.png);
//    Uint8List pngBytes = byteData.buffer.asUint8List();
//    String bs64 = base64Encode(pngBytes);
////      print(pngBytes);
////      print(bs64);
////      if (!(await checkPermission())) await requestPermission();
//
//    if (Platform.isAndroid) {
//      PermissionStatus permission = await PermissionHandler()
//          .checkPermissionStatus(PermissionGroup.storage);
//      print(permission);
//      if (permission != PermissionStatus.granted) {
//        Map<PermissionGroup, PermissionStatus> permissions =
//            await PermissionHandler()
//                .requestPermissions([PermissionGroup.storage]);
//      }
//    } else {
//      PermissionStatus permission = await PermissionHandler()
//          .checkPermissionStatus(PermissionGroup.photos);
//      print(permission);
//      if (permission != PermissionStatus.granted) {
//        Map<PermissionGroup, PermissionStatus> permissions =
//            await PermissionHandler()
//                .requestPermissions([PermissionGroup.photos]);
//      }
//    }
//
//    final tempDir = await getTemporaryDirectory();
//    final file = await new File('${tempDir.path}/image.png').create();
//    await file.writeAsBytes(pngBytes);
//    final channel = const MethodChannel('channel:me.alfian.share/share');
//    channel.invokeMethod('shareFile', 'image.png');
//
////    // Use plugin [path_provider] to export image to storage
////    final result = await ImageGallerySaver.saveImage(
////        Uint8List.fromList(pngBytes.buffer.asInt8List()));
////    print(result);
////    print('png done');
////    _snackSample(context);
////    setState(() {
////      imageInMemory = pngBytes;
////      inside = false;
////    });
//
//    return pngBytes;
//  }

//   Widget? _snackSample(context) {
//     Scaffold.of(context).showSnackBar(new SnackBar(
//       content: const Text('Save receipt success!'),
//       backgroundColor: Colors.lightGreen,
//     ));
//   }
}