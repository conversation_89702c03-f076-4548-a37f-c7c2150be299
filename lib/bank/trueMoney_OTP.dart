import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:pattern_formatter/pattern_formatter.dart';

class trueMoney_OTP extends StatefulWidget {
  _trueMoney_OTP createState() => new _trueMoney_OTP();
}

class _trueMoney_OTP extends State<trueMoney_OTP> {
  buildForPhone(Orientation orientation) {
    return Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: Color(0xFFFFFFFF),
        body: SingleChildScrollView(
            child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              SizedBox(height: MediaQuery.of(context).size.height * 0.1),
              new Container(
                alignment: Alignment.center,
                height: MediaQuery.of(context).size.height * 0.18,
                width: MediaQuery.of(context).size.width * 0.9,
                child: Image.asset(
                  'assets/image/truemoneyLogo.png',
                  scale: 1.5,
                ),
              ),
              new Container(
                decoration: BoxDecoration(
                  color: Color(0xffFFFFFF),
                  border: Border(
                    bottom: BorderSide(
                      //                    <--- top side
                      color: Colors.black12,
                      width: 1.0,
                    ),
                  ),
                ),
                alignment: Alignment.bottomLeft,
                padding: EdgeInsets.only(
                    left: MediaQuery.of(context).size.width * 0.0),
                height: MediaQuery.of(context).size.height * 0.1,
                width: MediaQuery.of(context).size.width * 0.9,
                child: new Text(
                  'LIKE Wallet',
                  style: TextStyle(
                      color: Color(0xff000000).withOpacity(1),
                      fontFamily: 'Proxima Nova',
                      fontWeight: FontWeight.bold,
                      fontSize: 21.0),
                ),
              ),
              SizedBox(height: MediaQuery.of(context).size.height * 0.05),
              new Container(
                alignment: Alignment.bottomLeft,
                padding: EdgeInsets.only(
                    left: MediaQuery.of(context).size.width * 0.0),
                height: MediaQuery.of(context).size.height * 0.05,
                width: MediaQuery.of(context).size.width * 0.9,
                child: new Text(
                  'OTP Reference',
                  style: TextStyle(
                      color: Color(0xffB3B3B4).withOpacity(1),
                      fontFamily: 'Proxima Nova',
                      fontWeight: FontWeight.normal,
                      fontSize: 14.0),
                ),
              ),
              new Container(
                alignment: Alignment.topLeft,
                padding: EdgeInsets.only(
                    left: MediaQuery.of(context).size.width * 0.0),
                height: MediaQuery.of(context).size.height * 0.05,
                width: MediaQuery.of(context).size.width * 0.9,
                child: new Text(
                  '123-45',
                  style: TextStyle(
                      color: Color(0xff000000).withOpacity(1),
                      fontFamily: 'Proxima Nova',
                      fontWeight: FontWeight.normal,
                      fontSize: 15.0),
                ),
              ),
              new Container(
                alignment: Alignment.bottomLeft,
                padding: EdgeInsets.only(
                    left: MediaQuery.of(context).size.width * 0.0),
                height: MediaQuery.of(context).size.height * 0.05,
                width: MediaQuery.of(context).size.width * 0.9,
                child: new Text(
                  'Amount',
                  style: TextStyle(
                      color: Color(0xffB3B3B4).withOpacity(1),
                      fontFamily: 'Proxima Nova',
                      fontWeight: FontWeight.normal,
                      fontSize: 14.0),
                ),
              ),
              new Container(
                  alignment: Alignment.topLeft,
                  padding: EdgeInsets.only(
                      left: MediaQuery.of(context).size.width * 0.0),
                  height: MediaQuery.of(context).size.height * 0.05,
                  width: MediaQuery.of(context).size.width * 0.9,
                  child: Row(
                    children: <Widget>[
                      new Text(
                        '6,000.00',
                        style: TextStyle(
                            color: Color(0xff000000).withOpacity(1),
                            fontFamily: 'Proxima Nova',
                            fontWeight: FontWeight.normal,
                            fontSize: 15.0),
                      ),
                      new Text(
                        '  THB ',
                        style: TextStyle(
                            color: Color(0xff000000).withOpacity(1),
                            fontFamily: 'Proxima Nova',
                            fontWeight: FontWeight.normal,
                            fontSize: 15.0),
                      ),
                    ],
                  )),
              new Container(
                alignment: Alignment.centerLeft,
                padding: EdgeInsets.only(
                    left: MediaQuery.of(context).size.width * 0.0),
                height: MediaQuery.of(context).size.height * 0.05,
                width: MediaQuery.of(context).size.width * 0.9,
                child: new Text(
                  'Enter OTP',
                  style: TextStyle(
                      color: Color(0xff000000).withOpacity(1),
                      fontFamily: 'Proxima Nova',
                      fontWeight: FontWeight.bold,
                      fontSize: 14.0),
                ),
              ),
              new Container(
                decoration: BoxDecoration(
                  borderRadius: new BorderRadius.circular(3.0),
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      blurRadius: 2,
                      color: Colors.black.withOpacity(.2),
                      offset: Offset(
                        0.0,
                        3.0,
                      ),
                    ),
                  ],
                  //shape: BoxShape.rectangle,
                  //border: Border.all(),
                ),
                alignment: Alignment.centerLeft,
                padding: EdgeInsets.only(
                    left: MediaQuery.of(context).size.width * 0.0),
                height: MediaQuery.of(context).size.height * 0.07,
                width: MediaQuery.of(context).size.width * 0.9,
                child: TextFormField(
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    enabledBorder: OutlineInputBorder(
                        borderSide:
                            BorderSide(width: 0.1, color: Colors.black)),
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10.0)),
                    hintText: 'Enter OTP',
                    prefix: Container(
                      width: MediaQuery.of(context).size.height * 0.0,
                      height: MediaQuery.of(context).size.height * 0.03,
                      margin: const EdgeInsets.only(right: 3.0),
                      padding: const EdgeInsets.only(top: 2.0),
                    ),
                  ),
                  validator: (String? value) {
                    if (value!.length < 8) {
                      return 'Password must be at least 8 characters long.';
                    }
                    return null;
                  },
                ),
              ),
              SizedBox(height: MediaQuery.of(context).size.height * 0.02),
              GestureDetector(
                onTap: (() => {Navigator.pop(context, '/trueMoney_OTP')}),
                child: Container(
                  alignment: Alignment.center,
                  height: MediaQuery.of(context).size.height * 0.07,
                  width: MediaQuery.of(context).size.width * 0.9,
                  child: ButtonTheme(
                    minWidth: MediaQuery.of(context).size.width * 0.95,
                    height: MediaQuery.of(context).size.height * 0.07,
                    child: TextButton(
                      style: ButtonStyle(
                        shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                          RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                        ),
                        backgroundColor: MaterialStateProperty.resolveWith<Color>(
                              (Set<MaterialState> states) {
                            if (states.contains(MaterialState.disabled)) {
                              return Color(0xff000000).withOpacity(1); // Disabled color
                            }
                            return Color(0xff000000).withOpacity(1); // Regular color
                          },
                        ),
                      ),
                      onPressed: () {
                        Navigator.pushNamed(context, '/completeTransaction');
                      },
                      child: Text(
                        'Verify',
                        style: TextStyle(
                          color: Color(0xffFFFFFF),
                          fontFamily: 'Proxima Nova',
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              new Container(
                  alignment: Alignment.bottomLeft,
                  padding: EdgeInsets.only(
                      left: MediaQuery.of(context).size.width * 0.0),
                  height: MediaQuery.of(context).size.height * 0.05,
                  width: MediaQuery.of(context).size.width * 0.9,
                  child: Row(
                    children: <Widget>[
                      new Text(
                        'Didn’t get the code?',
                        style: TextStyle(
                            color: Color(0xff000000).withOpacity(1),
                            fontFamily: 'Proxima Nova',
                            fontWeight: FontWeight.normal,
                            fontSize: 13.0),
                      ),
                      new Text(
                        ' Resend Term of service',
                        style: TextStyle(
                            color: Color(0xff1A56F0).withOpacity(1),
                            fontFamily: 'Proxima Nova',
                            fontWeight: FontWeight.normal,
                            fontSize: 13.0),
                      ),
                    ],
                  )),
            ],
          ),
        )));
  }

  buildForTablet(Orientation orientation) {
    return Scaffold();
  }

  int tabMenu = 0;
  changeMenu(_number) async {
    setState(() {
      tabMenu = _number;
    });
  }

  @override
  Widget build(BuildContext context) {
    final double shortestSide = MediaQuery.of(context).size.shortestSide;
    final bool useMobileLayout = shortestSide < 600.0;
    final Orientation orientation = MediaQuery.of(context).orientation;
    return Scaffold(
      body: useMobileLayout
          ? buildForPhone(orientation)
          : buildForTablet(orientation),
    );
  }
}
