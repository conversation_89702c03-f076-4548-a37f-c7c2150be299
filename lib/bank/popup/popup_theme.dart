import 'dart:io';
import 'dart:ui';

// import 'package:app_settings/app_settings.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/chat_in_app/index_chat.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/screen/ContractUS.dart';
import 'package:likewallet/screen_util.dart';

popupDialog(context, headText, detailText) {
  Dialog simpleDialog = Dialog(
    elevation: 500,
    backgroundColor: Colors.transparent,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(30.0),
    ),
    child: Container(
      height: mediaQuery(context, 'height', 554.63),
      width: mediaQuery(context, 'width', 929.64),
      color: Colors.transparent,
      margin: EdgeInsets.only(bottom: mediaQuery(context, 'height', 600)),
      child: new ClipRect(
        child: new BackdropFilter(
          filter: new ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
          child: Container(
            decoration: BoxDecoration(
              color: LikeWalletAppTheme.white.withOpacity(0.6),
              borderRadius: BorderRadius.all(Radius.circular(20.0)),
            ),
            height: mediaQuery(context, 'height', 554.63),
            width: mediaQuery(context, 'width', 929.64),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Text(
                  AppLocalizations.of(context)!.translate(headText),
                  style: TextStyle(
                    letterSpacing: 0.3,
                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
                    color: LikeWalletAppTheme.black.withOpacity(1),
                    fontSize: mediaQuery(context, "height", 56),
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(
                      bottom: mediaQuery(context, 'height', 80)),
                  width: mediaQuery(context, 'width', 777.62),
                  child: Text(
                    AppLocalizations.of(context)!.translate(detailText),
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      letterSpacing: 0.3,
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      color: LikeWalletAppTheme.black.withOpacity(1),
                      fontSize: mediaQuery(context, "height", 42),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Container(
                    width: mediaQuery(context, 'width', 777.62),
                    decoration: BoxDecoration(
                      border: Border(
                        top: BorderSide(
                          //                   <--- left side
                          color: LikeWalletAppTheme.black.withOpacity(0.4),
                          width: mediaQuery(context, 'width', 1),
                        ),
                      ),
                    ),
                    child: Row(
                      children: <Widget>[
                        GestureDetector(
                          onTap: () async {
                            Navigator.of(context).pop();
                            // AppSettings.openAppSettings();
                          },
                          child: Container(
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              border: Border(
                                right: BorderSide(
                                  //                   <--- left side
                                  color:
                                      LikeWalletAppTheme.black.withOpacity(0.4),
                                  width: mediaQuery(context, 'width', 1),
                                ),
                              ),
                            ),
                            height: mediaQuery(context, 'height', 127.66),
                            width: mediaQuery(context, 'width', 777.62) / 2,
                            child: Text(
                              AppLocalizations.of(context)!
                            .translate('permission_setting'),
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                letterSpacing: 0.3,
                                fontFamily: AppLocalizations.of(context)!
                            .translate('font1'),
                                color:
                                    LikeWalletAppTheme.bule1_7.withOpacity(1),
                                fontSize: mediaQuery(context, "height", 52),
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                        GestureDetector(
                          onTap: () {
                            Navigator.of(context).pop();
                          },
                          child: Container(
                            alignment: Alignment.center,
                            height: mediaQuery(context, 'height', 127.66),
                            width: mediaQuery(context, 'width', 777.62) / 2,
                            child: Text(
                              AppLocalizations.of(context)!
                            .translate('permission_cancel'),
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                letterSpacing: 0.3,
                                fontFamily: AppLocalizations.of(context)!
                            .translate('font1'),
                                color:
                                    LikeWalletAppTheme.bule1_7.withOpacity(1),
                                fontSize: mediaQuery(context, "height", 52),
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                      ],
                    )),
              ],
            ),
          ),
        ),
      ),
    ),
  );
  showDialog(context: context, builder: (BuildContext context) => simpleDialog);
}

class CustomPopup {
  static showErrorDialog(context, headText, detailText) async {
    return showDialog<void>(
      context: context,
      barrierColor: Color(0xFF050505).withOpacity(0.6),
      barrierDismissible: true, // user must tap button!
      builder: (BuildContext context) {
        return Material(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(16.0),
          child: Center(
            child: Padding(
              padding: EdgeInsets.all(24),
              child: Stack(
                alignment: Alignment.topRight,
                children: [
                  Container(
                    height: 200,
                    width: 345,
                    padding: EdgeInsets.only(
                        top: 20, left: 25, right: 25, bottom: 24),
                    decoration: BoxDecoration(
                        color: Color(0xFF1D2532),
                        borderRadius: BorderRadius.circular(16)),
                    child: Column(
                      children: [
                        Text(
                          headText,
                          style: TextStyle(
                              fontFamily: "IBMPlexSansThai",
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Color(0xFFE9ECF5),
                              height: 1.2),
                        ),
                        SizedBox(
                          height: 16,
                        ),
                        Text(
                          detailText,
                          style: TextStyle(
                              fontFamily: "IBMPlexSansThai",
                              fontSize: 14,
                              color: Color(0xFFE9ECF5),
                              height: 1.2),
                          textAlign: TextAlign.center,
                        ),
                        Spacer(),
                        Row(
                          children: [
                            Stack(
                              clipBehavior: Clip.none,
                              children: [
                                Container(
                                  height: 36,
                                  width: 96,
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(18),
                                      gradient: LinearGradient(
                                        begin: Alignment.centerLeft,
                                        end: Alignment.centerRight,
                                        stops: [0.1, 0.9],
                                        colors: [
                                          Color(0xFF1AAEFC),
                                          Color(0xFF384AE8)
                                        ],
                                      )),
                                child:GestureDetector(
                                  onTap: () => Navigator.of(context).pop(),
                                  child: Center(
                                      child: Text(
                                        AppLocalizations.of(context)!.translate('cancel_btn'),
                                        style: TextStyle(
                                            fontSize: 14,
                                            color: Color(0xFFEBEDFD),
                                            fontWeight: FontWeight.w500),
                                      )),
                                ),
                                ),
                              ],
                            ),
                            SizedBox(
                              width: 9,
                            ),
                            Container(
                              height: 36,
                              width: mediaQuery(context, 'width', 470),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(18),
                                  color:
                                  Color(0xFF0078FF).withOpacity(0.1),
                                  border: Border.all(
                                      color: Color(0xFF0078FF))),
                            child:GestureDetector(
                              onTap: () {
                                /// ย้ายไปไลน์ โอ๋เอ๋
                                //   Navigator.push(
                                //       context,
                                //       MaterialPageRoute(
                                //           builder: (context) =>
                                //               IndexChatPage()));
                                Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) =>
                                            ContactUSPage()));
                                },
                              child: Center(
                                  child: Text(
                                    AppLocalizations.of(context)!.translate('app_assistant_btn'),
                                    style: TextStyle(
                                        fontSize: 14,
                                        color: Color(0xFFE9ECF5)),
                                  )),
                            ),
                            ),
                          ],
                        )
                      ],
                    ),
                  ),
                  GestureDetector(
                    onTap: () => Navigator.of(context).pop(),
                    child: Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: SvgPicture.asset("assets/svgs/dialog_close.svg"),
                    ),
                  )
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}