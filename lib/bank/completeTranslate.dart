import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:likewallet/bank/banking.dart';
import 'package:likewallet/libraryman/app_local.dart';

class CompleteTranslate extends StatefulWidget {
  CompleteTranslate({this.title, this.detail, this.buttonText, this.back});
  final title;
  final detail;
  final buttonText;
  final back;

  _CompleteTranslate createState() => new _CompleteTranslate(title: title, detail: detail, buttonText: buttonText, back: back);


}

class _CompleteTranslate extends State<CompleteTranslate> {
  _CompleteTranslate({this.title, this.detail, this.buttonText, this.back});
  final title;
  final detail;
  final buttonText;
  final back;

  buildForPhone(Orientation orientation) {
    return Scaffold(
      backgroundColor: Color(0xFFFFFFFF),
      body: Align(
        alignment: Alignment.center,
        child: Column(
          children: <Widget>[
            SizedBox(
              height: MediaQuery.of(context).size.height * 0.15,
            ),
            new Container(
              alignment: Alignment.center,
              height: MediaQuery.of(context).size.height * 0.1,
              width: MediaQuery.of(context).size.width * 0.15,
              child: Image.asset(
                'assets/image/complete.png',
                scale: 1,
              ),
            ),
            new Container(
              alignment: Alignment.center,
              height: MediaQuery.of(context).size.height * 0.05,
              width: MediaQuery.of(context).size.width * 0.8,
              child: new Text(
                AppLocalizations.of(context)!.translate(title),
                style: TextStyle(
                    color: Color(0xff000000).withOpacity(1),
                    fontFamily: 'Proxima Nova',
                    fontWeight: FontWeight.normal,
                    fontSize: 15.0),
              ),
            ),
            new Container(
              alignment: Alignment.center,
              height: MediaQuery.of(context).size.height * 0.2,
              width: MediaQuery.of(context).size.width * 0.8,
              child: new Text(
                AppLocalizations.of(context)!.translate(detail),
                style: TextStyle(
                    color: Color(0xff000000).withOpacity(1),
                    fontFamily: 'Proxima Nova',
                    fontWeight: FontWeight.normal,
                    fontSize: 17.0),
              ),
            ),
            new Container(
              alignment: Alignment.center,
              height: MediaQuery.of(context).size.height * 0.07,
              width: MediaQuery.of(context).size.width * 0.9,
              child: ButtonTheme(
                minWidth: MediaQuery.of(context).size.width * 0.95,
                height: MediaQuery.of(context).size.height * 0.07,
                child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      shape: RoundedRectangleBorder(
                        borderRadius:  BorderRadius.circular(8.0),
                      ),
                      disabledBackgroundColor: Color(0xff150e23).withOpacity(0.8),
                      backgroundColor: Color(0xff150e23).withOpacity(0.8),
                    ),
                    onPressed: (() => {
                      Navigator.pushAndRemoveUntil(
                          context,
                          MaterialPageRoute(builder: (context) => back),
                              (Route<dynamic> route) => false)
                    }),
                    child: new Text(
                      AppLocalizations.of(context)!.translate(buttonText),
                      style: TextStyle(
                          color: Color(0xffB4E60D),
                          fontFamily: 'Proxima Nova',
                          fontSize: 18,
                          fontWeight: FontWeight.normal),
                    )),
              ),
            ),
          ],
        ),
      ),
    );
  }

  buildForTablet(Orientation orientation) {
    return Scaffold();
  }

  int tabMenu = 0;
  changeMenu(_number) async {
    setState(() {
      tabMenu = _number;
    });
  }

  @override
  Widget build(BuildContext context) {
    final double shortestSide = MediaQuery.of(context).size.shortestSide;
    final bool useMobileLayout = shortestSide < 600.0;
    final Orientation orientation = MediaQuery.of(context).orientation;
    return Scaffold(
      body: useMobileLayout
          ? buildForPhone(orientation)
          : buildForTablet(orientation),
    );
  }
}
