import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:delayed_display/delayed_display.dart';

import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/Theme.dart';

import 'package:likewallet/bank/buylike/choose_payment.dart';
import 'package:likewallet/bank/receive.dart';
import 'package:likewallet/bank/send.dart';

import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:likewallet/bank/cash/cash.dart';

import 'package:likewallet/jsonDecode/vending.dart';

import 'package:likewallet/libraryman/app_local.dart';
import 'package:flutter/material.dart';
import 'dart:async';
import 'package:flutter/services.dart';
import 'package:likewallet/main.dart';
import 'package:likewallet/middleware/check_maintenance/check_maintenance.dart';
import 'package:likewallet/model/bank/exchange_fiat.dart';
import 'package:likewallet/screen/tabslide.dart';
import 'package:likewallet/screen_util.dart';

import 'package:shared_preferences/shared_preferences.dart';
import 'package:likewallet/screen/NavigationBar.dart' as nav;
import 'dart:ui';
import 'package:flutter/rendering.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:likewallet/libraryman//ethcontract.dart';
import 'package:likewallet/middleware/get_mnemonic.dart';
import 'package:likewallet/middleware/check_auth.dart';
import 'package:intl/intl.dart';
import 'package:likewallet/libraryman/address_service.dart';
import 'package:likewallet/libraryman/configuration_service.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:http/http.dart' as http;
import 'package:likewallet/app_config.dart';
import 'package:likewallet/libraryman/checkAPI.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:likewallet/libraryman/setFormat.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:likewallet/libraryman/serviceHTTP.dart';
import 'package:intl/intl.dart' as formatIntl;
import 'package:likewallet/quickpay/favorites/list.dart';
import 'package:likewallet/libraryman/keyboard_done_widget.dart';
import 'dart:io';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class Banking extends StatefulWidget {
  Banking(
      {this.phone,
      this.selectPage,
      this.source,
      this.shopID,
      this.scanActive,
      this.address,
      this.titleContact,
      this.contract,
      this.abi,
      this.callFunction,
      this.selectedAddr,
      this.vending,
      this.isVending});
  final String? phone;
  final String? source;
  final selectPage;
  final shopID;
  final scanActive;
  final String? contract;
  final String? address;
  final String? titleContact;
  final String? abi;
  final String? callFunction;
  final String? selectedAddr;
  final Vending? vending;
  final bool? isVending;

  _Banking createState() => new _Banking(
      selectPage: selectPage,
      source: source,
      shopID: shopID,
      scanActive: scanActive,
      address: address,
      contract: contract,
      abi: abi,
      callFunction: callFunction,
      selectedAddr: selectedAddr,
      vending: vending,
      isVending: isVending);
}

class _Banking extends State<Banking> with TickerProviderStateMixin {
  final TextEditingController controller = new TextEditingController();
  TextEditingController amountSend = TextEditingController();
  TextEditingController textCash = TextEditingController();
  TextEditingController textAccountNumber = TextEditingController();

  _Banking(
      {this.selectPage,
      this.source,
      this.shopID,
      this.scanActive,
      this.address,
      this.contract,
      this.abi,
      this.callFunction,
      this.selectedAddr,
      this.vending,
      this.isVending});

  final selectPage;
  final String? source;
  final shopID;
  final scanActive;
  final String? address;
  final String? contract;
  final String? abi;
  final String? callFunction;
  final String? selectedAddr;
  final Vending? vending;
  final bool? isVending;

  bool showfavorite = false;
  final fireStore = FirebaseFirestore.instance;
  double price = 0;
  final f = new formatIntl.NumberFormat("###,###.##");
  final formatCurrency = new NumberFormat.decimalPattern();
  int running = 0;
  bool hideButton = false;
  StreamController<String> streamBalance = StreamController<String>.broadcast();
  StreamController<String> streamBalanceLocked =
      StreamController<String>.broadcast();
  StreamController<double> streamAvailable =
      StreamController<double>.broadcast();
  StreamController<double> streamBalanceLock =
      StreamController<double>.broadcast();

  GlobalKey<ScaffoldState> _scaffoldKey = new GlobalKey();
//  GlobalKey globalKey = new GlobalKey();

  String _total_pay = '300.00';
  double rate = 100.00;
  double amountInput = 0.00;
  String accountNumber = '';
  int curIndex = 0;
  String currency = 'THB';
  double fee = 0.00;
  int keyword = 1;
  String barcode = "";
  String symbol_1 = 'LIKE';
  String symbol_2 = 'LIKE';
  String balance = 'Loading..';
  double balanceLIKE = 0.00;
  String locked_balance = 'Loading..';
  double rateCurrency = 0.0;
  double balanceLIKELock = 0.00;
  String available = 'Loading..';
  String bank = '';
  late String pketh;
  late BaseETH eth;
  String fiat = 'none';
  List<Widget> tabBar = [];
  List<Widget> tabBarBody = [];
  List permission = [false, false, false, false];
  late GetMnemonic seed;
  late String mnemonic;
  late CheckAuth Logon;
  late IAddressService addressService;
  late IConfigurationService configETH;
  late BaseAuth FirebaseAuth;
  bool login = false;
  late CheckAPI checkAPI;
  late SetFormat setFormat;
  TextEditingController addressText = TextEditingController();
  final TextEditingController nameFavorite = TextEditingController();
  final TextEditingController addressFavorite = TextEditingController();
  late TabController _tabController =
      new TabController(vsync: this, length: tabBar.length);
  StreamController<String> streamCurrencyAmount =
      StreamController<String>.broadcast();

  String balanceTHB = '0';

  late String photoFavorite;
  late File? _imageFavorite;
  bool _autoValidate = false;
//  final formKey = GlobalKey<FormState>();
  bool delete = false;
  bool showlike = false;

  int tabMenu = 0;
  var crossFadeView = CrossFadeState.showFirst;
  bool _saving = false;
  late String toAddress;
  late AbstractServiceHTTP APIHttp;
  var _opacity = 1.0;
  OverlayEntry? overlayEntry;
  String _dataString = "";
  late CheckAbout checkAbout;
  //ร้านค้า
  List<list> _list = [];
  List group = [];
  List<list> search = [];
  late String uid;
  String firstName = '';
  String lastName = '';
  //kyc session
  bool kycStatus = false;
  FocusNode amountFocusNode = new FocusNode();
  FocusNode amountBuyFocusNode = new FocusNode();
  FocusNode amountCashFocusNode = new FocusNode();
  final formatBank = new NumberFormat("###-###-####");
  bool permissionSend = false;
  bool permissionReceive = false;
  bool permissionBuyLike = false;
  bool permissionCash = false;
  late BaseAuth auth;

  void showColoredToast(msg) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_LONG,
        backgroundColor: Colors.red,
        textColor: Colors.white);
  }

  Future hotReloadBalance() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    configETH = new ConfigurationService(pref);
    addressService = new AddressService(configETH);
    mnemonic = await configETH.getMnemonic();
    generateETH(mnemonic, pref);
    reloadBalance(configETH.getAddress(), fiat);
  }

  Future reloadBalance(address, fiat) async {
    print(address);
    eth.getBalance(address: address).then((balance) {
      final exchangeFiat =
          fireStore.collection('exchangeFiat').withConverter<ExchangeFiat>(
                fromFirestore: (snapshot, _) =>
                    ExchangeFiat.fromJson(snapshot.data()!),
                toFirestore: (model, _) => model.toJson(),
              );
      exchangeFiat
          .doc(fiat == 'none' ? "THB-THB" : "THB-" + fiat)
          .get()
          .then((ds) {
        print(ds.data()!.main);
        print("THB-" + fiat);
        if (fiat == 'THB') {
          if (!mounted) return;
          setState(() {
            balanceTHB = f.format((balance / 100).floorToDouble()).toString();
          });
          streamCurrencyAmount.sink.add(balanceTHB);
        } else if (fiat == 'LIKE') {
          if (!mounted) return;
          setState(() {
            balanceTHB = f.format((balance).floorToDouble()).toString();
          });
          streamCurrencyAmount.sink.add(balanceTHB);
        } else if (fiat == 'GOLD') {
          if (!mounted) return;
          setState(() {
            balanceTHB = f
                .format((balance / 100 / ds.data()!.rate.toDouble())
                    .floorToDouble())
                .toString();
          });

          streamCurrencyAmount.sink.add(balanceTHB);
        } else {
          if (!mounted) return;
          setState(() {
            balanceTHB = f
                .format((balance / 100 * ds.data()!.rate.toDouble())
                    .floorToDouble())
                .toString();
          });

          streamCurrencyAmount.sink.add(balanceTHB);
        }
      });
    });
  }

  //done button zone
  showOverlay(BuildContext context) {
    if (overlayEntry != null) return;
    OverlayState? overlayState = Overlay.of(context);
    overlayEntry = OverlayEntry(builder: (context) {
      return Positioned(
          bottom: MediaQuery.of(context).viewInsets.bottom,
          right: 0.0,
          left: 0.0,
          child: InputDoneView());
    });

    overlayState!.insert(overlayEntry!);
  }

  removeOverlay() {
    if (overlayEntry != null) {
      overlayEntry!.remove();
      overlayEntry = null;
    }
  }

  Future<bool> getShop() async {
    //เรียก API ร้านค้า
    var data = await APIHttp.getQuickpayShop();

    setState(() {
      _list = data[0];
      search = data[0];
      group = data[1];
    });
    return true;
  }

  void pasteAddress() async {
    ClipboardData? data = await Clipboard.getData('text/plain');
    addressText.text = data!.text!;
    toAddress = data.text!;
  }

  Future generateETH(seed, pref) async {
    print('banking page');
    if (!mounted) return;
    setState(() {
      _dataString = configETH.getAddress();
    });
    eth.getBalance(address: _dataString).then((avaiBalance) {
      available = f.format(avaiBalance).toString();
      if (!mounted) return;
      setState(() {
        balanceLIKE = avaiBalance.toDouble();
      });
      streamAvailable.sink.add(balanceLIKE);

      eth.getBalanceLock(address: _dataString).then((balanceLock) async {
        print(balanceLock);
        balance = f.format(avaiBalance + balanceLock);
        balanceLIKELock = balanceLock.toDouble();
        locked_balance = f.format(balanceLock).toString();
        streamBalance.sink.add(balance);
        streamBalanceLock.sink.add(balanceLIKELock);
        streamBalanceLocked.sink.add(locked_balance);
        if (!mounted) return;
        setState(() {
          _saving = false;
        });
      });
    });
  }

  _changeText(_symbol) {
    final exchangeFiat =
        fireStore.collection('exchangeFiat').withConverter<ExchangeFiat>(
              fromFirestore: (snapshot, _) =>
                  ExchangeFiat.fromJson(snapshot.data()!),
              toFirestore: (model, _) => model.toJson(),
            );
    exchangeFiat
        .doc(_symbol == 'none' ? "THB-LIKE" : "THB-" + _symbol)
        .get()
        .then((DocumentSnapshot<ExchangeFiat> ds) {
      print(ds.data()!.rate.toDouble());
      if (ds.data()!.to == 'LIKE') {
        if (!mounted) return;
        setState(() {
          rateCurrency = ds.data()!.rate.toDouble();
        });
      } else {
        if (!mounted) return;
        setState(() {
          rateCurrency = 1 / ds.data()!.rate.toDouble();
        });
      }
    });
    if (!mounted) return;
    setState(() {
      print(_symbol);
      symbol_1 = _symbol;
    });
  }

  changeMenu(_number) async {
    if (!mounted) return;
    setState(() {
      tabMenu = _number;
    });
  }

  @override
  void dispose() {
    // TODO: implement dispose
    streamBalance.close();
    streamBalanceLocked.close();
    streamBalanceLock.close();
    streamAvailable.close();
    amountFocusNode.dispose();
    amountBuyFocusNode.dispose();
    amountCashFocusNode.dispose();
    super.dispose();
  }

  void showShortToast(msg) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_SHORT,
        backgroundColor: Colors.cyan,
        textColor: Colors.white);
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    checkAbout = OnCheckAbout();
    auth = new Auth();

    if (!mounted) return;
    setState(() {
      _saving = true;
    });
    // get();
    print(contract);
    APIHttp = new ServiceHTTP();
    Logon = new CurCheckAuth();
    seed = new MnemonicRetrieve();
    eth = new EthContract();
    FirebaseAuth = Auth();
    checkAPI = APIChecker();
    setFormat = SetFormatString();
    setInit();
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  }

  checkPermission({required String tier}) async {
    permission[0] =
        await checkAbout.checkPermissionMenu(tierLevel: tier, page: 'send');
    if (permission[0] == true) {
      tabBar.add(Container(
          alignment: Alignment.center,
          height: mediaQuery(context, 'height', 129.75),
          width: mediaQuery(context, 'width', 160),
          child: Text(
            AppLocalizations.of(context)!.translate('banking_send'),
            textScaleFactor: 1,
            maxLines: 1,
          )));
      tabBarBody.add(DelayedDisplay(
        fadingDuration: const Duration(milliseconds: 1000),
        slidingBeginOffset: const Offset(-1.0, 0.0),
        child: SEND(
            selectedAddr: selectedAddr.toString(),
            source: source.toString(),
            shopID: shopID,
            scanActive: scanActive,
            titleContact: widget.titleContact.toString(),
            address: address.toString(),
            contract: contract.toString(),
            abi: abi.toString(),
            callFunction: callFunction.toString(),
            vending: vending,
            isVending: isVending),
      ));
    }

    permission[1] =
        await checkAbout.checkPermissionMenu(tierLevel: tier, page: 'receive');
    if (permission[1] == true) {
      tabBar.add(Container(
          alignment: Alignment.center,
          height: mediaQuery(context, 'height', 129.75),
          width: mediaQuery(context, 'width', 160),
          child: Text(
            AppLocalizations.of(context)!.translate('banking_receive'),
            textScaleFactor: 1,
            maxLines: 1,
          )));
      tabBarBody.add(DelayedDisplay(
        slidingBeginOffset: const Offset(1.0, 0.0),
        fadingDuration: const Duration(milliseconds: 800),
        child: Receive(),
      ));
    }

    permission[2] =
        await checkAbout.checkPermissionMenu(tierLevel: tier, page: 'buylike');
    if (permission[2] == true) {
      tabBar.add(Container(
          alignment: Alignment.center,
          height: mediaQuery(context, 'height', 129.75),
          width: mediaQuery(context, 'width', 180),
          child: Text(
            AppLocalizations.of(context)!.translate('banking_buy'),
            textScaleFactor: 1,
            maxLines: 1,
          )));
      tabBarBody.add(DelayedDisplay(
        slidingBeginOffset: const Offset(1.0, 0.0),
        fadingDuration: const Duration(milliseconds: 800),
        child: ChoosePayment(
            selectPage: selectPage,
            source: source.toString(),
            shopID: shopID,
            scanActive: scanActive,
            address: address.toString(),
            contract: contract.toString(),
            abi: abi.toString(),
            callFunction: callFunction.toString()),
      ));
    }

    permission[3] =
        await checkAbout.checkPermissionMenu(tierLevel: tier, page: 'cash');
    if (permission[3] == true) {
      tabBar.add(Container(
          alignment: Alignment.center,
          height: mediaQuery(context, 'height', 129.75),
          width: mediaQuery(context, 'width', 160),
          child: Text(
            AppLocalizations.of(context)!.translate('banking_cash'),
            textScaleFactor: 1,
            maxLines: 1,
          )));
      tabBarBody.add(DelayedDisplay(
        fadingDuration: const Duration(milliseconds: 1000),
        slidingBeginOffset: const Offset(-1.0, 0.0),
        child: DelayedDisplay(
          slidingBeginOffset: const Offset(1.0, 0.0),
          fadingDuration: const Duration(milliseconds: 800),
          child: CASH(
              selectPage: selectPage,
              source: source.toString(),
              shopID: shopID,
              scanActive: scanActive,
              address: address.toString(),
              contract: contract.toString(),
              abi: abi.toString(),
              callFunction: callFunction.toString()),
        ),
      ));
    }

    _tabController = new TabController(vsync: this, length: tabBar.length);
    _tabController.addListener(_handleTabSelection);
    if (!mounted) return;
    setState(() {
      _saving = false;
    });
  }

  void _handleTabSelection() {
    if (!mounted) return;
    setState(() {});
  }

  void setPay() async {
    getShop().then((result) {
      print(result);
      if (result) {
        print('result getShop' + result.toString());
        if (!mounted) return;
        setState(() {
          toAddress = _list[shopID].address.toString().trim().toString();
          addressText.text = _list[shopID].title.toString();
        });
      }
    });
  }

  setInit() async {
    await checkPermission(tier: context.read(tierLevel).state);
    // await checkPermission(tier: '2');
    print('open banking');
    SharedPreferences pref = await SharedPreferences.getInstance();
    fiat = pref.getString('currency') ?? 'none';

    if (!mounted) return;
    setState(() {
      kycStatus = pref.getBool('kycActive') ?? false;
    });
    currency = pref.getString('currency') ?? 'THB';

    _changeText(currency);
    APIHttp.getCurrentFee(currency: currency).then((getFee) {
      if (!mounted) return;
      setState(() {
        fee = getFee;
      });
      print(fee);
    });

    configETH = new ConfigurationService(pref);
    addressService = new AddressService(configETH);

    login = pref.getBool('login') ?? false;
    if (login) {
      mnemonic = await configETH.getMnemonic();
      generateETH(mnemonic, pref);
    }
    hotReloadBalance();

    // FirebaseAuth.getCurrentUser().then((decodeToken) async {
    //   FirebaseFirestore.instance.collection('users').get().then((value) {
    //     value.docs.forEach((element) {
    //       // setState(() {
    //       //   firstName = element.data()['firstName'];
    //       //   lastName = element.data()['lastName'];
    //       // });
    //       print(element.data()['firstName']);
    //     });
    //   });
    // });

    auth.getCurrentUser().then((decodeToken) {
      // print(decodeToken);
      uid = decodeToken!.uid;

      fireStore
          .collection('users')
          .doc(decodeToken.uid)
          .get()
          .then((DocumentSnapshot<Map<String, dynamic>> ds) {
        if (!mounted) return;
        setState(() {
          firstName = ds.data()!['firstName'];
          lastName = ds.data()!['lastName'];
        });
      });
    });
  }

  // searchName() async {
  //   auth.getCurrentUser().then((snapshot) {
  //     final users = FirebaseFirestore.instance
  //         .collection('users')
  //         .withConverter<UsersModel>(
  //           fromFirestore: (snapshot, _) =>
  //               UsersModel.fromJson(snapshot.data()!),
  //           toFirestore: (model, _) => model.toJson(),
  //         );
  //
  //     users.doc(snapshot!.uid).get().then((ds) {
  //       if (!mounted) return;
  //       setState(() {
  //         fromName = ds.data()!.firstName + " " + ds.data()!.lastName;
  //         _saving = false;
  //       });
  //     });
  //   });
  // }

  changeAmount() {
    print(amountSend.text);
  }

  updateRecived() {
    if (double.parse(textCash.text.replaceAll(",", "")) >= balanceLIKE) {
      if (!mounted) return;
      setState(() {
        amountInput = double.parse(
            (balanceLIKE / rate.toDouble() - fee).toStringAsFixed(2));
      });
    } else {
      print('here');
      if (!mounted) return;
      setState(() {
        amountInput = double.parse(
            (double.parse(textCash.text.replaceAll(",", "")) / rate.toDouble() -
                    fee)
                .toStringAsFixed(2));
      });
    }
  }

  updateAccountNumber() {
    if (!mounted) return;
    setState(() {
      accountNumber = textAccountNumber.text;
    });
  }

//เส้นเเนวตั้งเมนู
  Widget border() {
    return Container(
      height: MediaQuery.of(context).size.height * 0.025,
      decoration: BoxDecoration(
          border: Border(
        right: BorderSide(
          color: Color(0xff789613),
          width: MediaQuery.of(context).size.width * 0.***********,
        ),
      )),
    );
  }

//  void _validateInputs(_imageFavorite) {
//    if (formKey.currentState.validate()) {
////    If all data are correct then save data to out variables
//      if (_imageFavorite != null) {
//        _saving = true;
//        upload(_imageFavorite);
//        Navigator.of(context).pop();
//      } else {
//        _saving = true;
//        Navigator.of(context).pop();
//        addFavorite();
//      }
//      formKey.currentState.context;
//    } else {
////    If all data are not valid then start auto validation.
//      setState(() {
//        _autoValidate = true;
//      });
//    }
//  }

//-----upload  photo
  upload(File file) async {
    if (file == null) return ['false'];
    String base64Image = base64Encode(file.readAsBytesSync());
    String fileName = file.path.split("/").last;
    var url = Uri.https(env.apiCheck, '/uploadBinary');
    final response = await http.post(url, body: {
      "image": base64Image,
      "name": fileName,
    });
    print(response.statusCode);
    final body = json.decode(response.body);

    print(body["result"]);
    print(body["result"]["id"]);
    print(body["result"]["url"]);
    if (!mounted) return;
    setState(() {
      photoFavorite = body["result"]["url"];
    });
    await addFavorite();
//    return [body["result"]["url"]];
  }

//-----show preview photo from add favorite
//  Future preview() async {
//    var image = await ImagePicker.pickImage(source: ImageSource.gallery);
//
//    setState(() {
//      _imageFavorite = image;
//    });
//    print(_imageFavorite.path);
//    Navigator.of(context).pop();
//
//    await AddFavortieForm(context);
//  }

//-----add list favorite
  Future addFavorite() async {
    fireStore
        .collection('favorites')
        .doc(uid)
        .collection('transfer')
        .doc()
        .set({
      'nameFavorite': nameFavorite.text,
      'addressFavorite': addressFavorite.text,
      'photoFavorite': photoFavorite.toString(),
    });
    nameFavorite.clear();
    addressFavorite.clear();
    _imageFavorite = null;
    _saving = false;
  }

//-----get uid
  getFavorites() async {
    //เรียก API รายการโปรด
    FirebaseAuth.getCurrentUser().then((decodeToken) {
      // print(decodeToken);
      if (!mounted) return;
      setState(() {
        uid = decodeToken!.uid;
      });
      print(uid);
    });
  }

//-----delete list favorites
  deleteFavorites(bool value) {
    if (!mounted) return;
    setState(() {
      delete = value;
      print(delete);
    });
  }

//-----send address to textfield
  void sendAddress(address) async {
    print(address);
    if (!mounted) return;
    setState(() {
      addressText.text = address;
      toAddress = address;
    });
  }

  void pasteAddressFavorite() async {
    ClipboardData? data = await Clipboard.getData('text/plain');

    addressFavorite.text = data!.text!;
  }

/* ----------------|Open QR Code |--------------*/
  Future scan() async {
    Navigator.pushNamed(context, '/scanPay');
  }

  buildForPhone(Orientation orientation) {
    return DefaultTabController(
      length: tabBar.length,
      child: Scaffold(
          drawer: tabslide(),
          key: _scaffoldKey,
          resizeToAvoidBottomInset: false,
          backgroundColor: LikeWalletAppTheme.white1,
          body: Stack(
            children: <Widget>[
              Container(
                margin:
                    EdgeInsets.only(top: mediaQuery(context, 'hieght', 400)),
                child: tabViewFour(),
              ),
              _appBar(),
              AppBarUi(),
//              _menu(),
              Positioned(
                top: mediaQuery(context, 'height', 455),
                child: Container(
                    padding: EdgeInsets.only(
                      top: mediaQuery(context, 'height', 9),
                      bottom: mediaQuery(context, 'height', 9),
                      left: mediaQuery(context, 'width', 36),
                      right: mediaQuery(context, 'width', 36),
                    ),
                    alignment: Alignment.center,
                    width: MediaQuery.of(context).size.width,
                    height: mediaQuery(context, 'height', 149),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(84.0),
                      color: LikeWalletAppTheme.bule2_4,
                      boxShadow: [
                        BoxShadow(
                          color: LikeWalletAppTheme.black.withOpacity(0.35),
                          offset: Offset(0, -1),
                          spreadRadius: 2,
                          blurRadius: 5,
                        ),
                      ],
                    ),
                    child: tab()),
              ),
            ],
          )),
    );
  }

  tab() {
    return TabBar(
      controller: _tabController,
      isScrollable: true,
      indicatorColor: Colors.yellow,
      labelStyle: TextStyle(
        fontSize: mediaQuery(context, 'height', 42),
        fontFamily: AppLocalizations.of(context)!.translate('font1'),
        fontWeight: FontWeight.bold,
      ), //For Selected tab
      unselectedLabelStyle: TextStyle(
        fontSize: mediaQuery(context, 'height', 42),
        fontFamily: AppLocalizations.of(context)!.translate('font1'),
        fontWeight: FontWeight.normal,
      ),
      labelColor: LikeWalletAppTheme.white,
      indicator: BoxDecoration(
          color: LikeWalletAppTheme.lemon,
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            stops: [0.0, 1],
            colors: [
              // Colors are easy thanks to Flutter's Colors class.
              LikeWalletAppTheme.lemon.withOpacity(1),
              LikeWalletAppTheme.lemon.withOpacity(0.7),
            ],
          ),
          borderRadius: BorderRadius.circular(84)),
      tabs: List<Widget>.generate(tabBar.length, (int index) {
        return tabBar[index];
      }),
    );
  }

  tabViewThree() {
    return TabBarView(
      controller: _tabController,
      children: [
        DelayedDisplay(
          fadingDuration: const Duration(milliseconds: 1000),
          slidingBeginOffset: const Offset(-1.0, 0.0),
          child: SEND(
            selectedAddr: selectedAddr.toString(),
            source: source.toString(),
            shopID: shopID,
            scanActive: scanActive,
            address: address.toString(),
            contract: contract.toString(),
            abi: abi.toString(),
            callFunction: callFunction.toString(),
            vending: vending as Vending,
            isVending: isVending as bool,
            titleContact: '',
          ),
        ),
        DelayedDisplay(
          slidingBeginOffset: const Offset(1.0, 0.0),
          fadingDuration: const Duration(milliseconds: 800),
          child: Receive(),
        ),
        DelayedDisplay(
          slidingBeginOffset: const Offset(1.0, 0.0),
          fadingDuration: const Duration(milliseconds: 800),
          child: CASH(
              selectPage: selectPage,
              source: source.toString(),
              shopID: shopID,
              scanActive: scanActive,
              address: address.toString(),
              contract: contract.toString(),
              abi: abi.toString(),
              callFunction: callFunction.toString()),
        ),
      ],
    );
  }

  tabViewFour() {
    return TabBarView(
      controller: _tabController,
      children: List<Widget>.generate(tabBarBody.length, (int index) {
        return tabBarBody[index];
      }),
    );
  }

  @override
  Widget build(BuildContext context) {
    final Orientation orientation = MediaQuery.of(context).orientation;
    return GestureDetector(
        onTap: () {
          FocusScopeNode currentFocus = FocusScope.of(context);
          if (!currentFocus.hasPrimaryFocus) {
            currentFocus.unfocus();
            showfavorite = false;
          }
        },
        child: ModalProgressHUD(
            opacity: 0.1,
            child: Scaffold(
              bottomNavigationBar: nav.NavigationBar(),
              body: GestureDetector(
                onTap: () {
                  FocusScopeNode currentFocus = FocusScope.of(context);
                  if (!currentFocus.hasPrimaryFocus) {
                    currentFocus.unfocus();
                  }
                },
                child: buildForPhone(orientation),
              ),
            ),
            inAsyncCall: _saving,
            progressIndicator: CustomLoading()));
  }

  Widget _headDetail() {
    return Container(
      margin: EdgeInsets.only(
        top: mediaQuery(context, 'height', 100),
      ),
      child: _available(),
    );
  }

  Widget _available() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: <Widget>[
        Container(
          // height: mediaQuery(context, 'height', 126),
          margin: EdgeInsets.only(left: mediaQuery(context, 'width', 20)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Text(
                'Available Likepont',
                style: TextStyle(
                  fontFamily: 'Proxima Nova',
                  fontSize: 33.h,
                  color: const Color(0x4dffffff),
                  // letterSpacing: 3.3000000000000003.w,
                  letterSpacing: 3.sp,
                ),
                textAlign: TextAlign.left,
              ),
              StreamBuilder(
                  stream: streamAvailable.stream,
                  initialData: 0,
                  builder: (context, snapshot) {
                    return Text(
                      f.format(snapshot.data).toString(),
                      style: TextStyle(
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font2'),
                        fontSize: 77.h,
                        color: const Color(0xffffffff),
                      ),
                    );
                  }),
              StreamBuilder(
                  stream: streamCurrencyAmount.stream,
                  initialData: '0',
                  builder: (context, snapshot) {
                    return new Text(
                      fiat == 'none'
                          ? '= THB ' + snapshot.data.toString()
                          : fiat == "GOLD"
                              ? " = " +
                                  fiat +
                                  " " +
                                  snapshot.data.toString() +
                                  " g "
                              : " = " + fiat + " " + snapshot.data.toString(),
                      style: TextStyle(
                          color: Color(0xff6C6B6D),
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font1'),
                          fontSize: mediaQuery(context, 'height', 36),
                          fontWeight: FontWeight.normal),
                    );
                  }),
            ],
          ),
        ),
      ],
    );
  }

  Widget _swicthMenu() {
    return Container(
      width: mediaQuery(context, "width ", 848),
      margin: EdgeInsets.only(
          left: mediaQuery(context, 'width', 116),
          right: mediaQuery(context, 'width', 116)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          Container(
            margin: EdgeInsets.only(right: mediaQuery(context, 'width', 30)),
            height: mediaQuery(context, "height", 111.44),
            decoration: BoxDecoration(
              border: Border(
                left: BorderSide(
                    color: LikeWalletAppTheme.bule1.withOpacity(0.5),
                    width: mediaQuery(context, 'width', 1)),
              ),
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Text(
                  'Total',
                  style: TextStyle(
                      letterSpacing: 0.5,
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      color: LikeWalletAppTheme.bule1.withOpacity(0.5),
                      fontSize: mediaQuery(context, 'width', 33),
                      fontWeight: FontWeight.w200),
                ),
//                streamBalance
                StreamBuilder(
                    stream: streamBalance.stream,
                    initialData: '0',
                    builder: (context, snapshot) {
                      return Text(
                        snapshot.data.toString(),
                        style: TextStyle(
                            fontFamily: AppLocalizations.of(context)!
                                .translate('font1'),
                            color: LikeWalletAppTheme.bule1,
                            fontSize: mediaQuery(context, 'width', 33),
                            fontWeight: FontWeight.w200),
                      );
                    }),
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.only(right: mediaQuery(context, 'width', 30)),
            height: mediaQuery(context, "height", 111.44),
            decoration: BoxDecoration(
              border: Border(
                left: BorderSide(
                    color: LikeWalletAppTheme.bule1,
                    width: mediaQuery(context, 'width', 1)),
              ),
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Text(
                  'Locked',
                  style: TextStyle(
                      letterSpacing: 0.5,
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      color: LikeWalletAppTheme.bule1.withOpacity(0.5),
                      fontSize: mediaQuery(context, 'width', 33),
                      fontWeight: FontWeight.w200),
                ),
//                streamBalanceLock
                StreamBuilder(
                    stream: streamBalanceLocked.stream,
                    initialData: '0',
                    builder: (context, snapshot) {
                      return Text(
                        snapshot.data.toString(),
                        style: TextStyle(
                            fontFamily: AppLocalizations.of(context)!
                                .translate('font1'),
                            color: LikeWalletAppTheme.bule1,
                            fontSize: mediaQuery(context, 'width', 33),
                            fontWeight: FontWeight.w200),
                      );
                    }),
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.only(right: mediaQuery(context, 'width', 30)),
            height: mediaQuery(context, "height", 111.44),
            decoration: BoxDecoration(
              border: Border(
                left: BorderSide(
                    color: LikeWalletAppTheme.bule1.withOpacity(0.5),
                    width: mediaQuery(context, 'width', 1)),
              ),
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Text(
                  'Available',
                  style: TextStyle(
                      letterSpacing: 0.5,
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      color: LikeWalletAppTheme.bule1.withOpacity(0.5),
                      fontSize: mediaQuery(context, 'width', 33),
                      fontWeight: FontWeight.w100),
                ),
                StreamBuilder(
                    stream: streamAvailable.stream,
                    initialData: '0',
                    builder: (context, snapshot) {
                      return Text(
                        snapshot.data.toString(),
                        style: TextStyle(
                            fontFamily: AppLocalizations.of(context)!
                                .translate('font1'),
                            color: LikeWalletAppTheme.bule1,
                            fontSize: mediaQuery(context, 'width', 33),
                            fontWeight: FontWeight.w200),
                      );
                    }),
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.only(right: mediaQuery(context, 'width', 30)),
            height: mediaQuery(context, "height", 111.44),
            decoration: BoxDecoration(
              border: Border(
                left: BorderSide(
                    color: LikeWalletAppTheme.bule1.withOpacity(0.5),
                    width: mediaQuery(context, 'width', 1)),
              ),
            ),
          ),
        ],
      ),
    );
//    Row(
//      mainAxisAlignment: MainAxisAlignment.end,
//      crossAxisAlignment: CrossAxisAlignment.end,
//      children: <Widget>[
//        Expanded(
//          child: Column(
//            crossAxisAlignment: CrossAxisAlignment.start,
//            children: <Widget>[
//              Text(
//                'df',
//                style: TextStyle(
//                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
//                    color: LikeWalletAppTheme.bule1,
//                    fontSize: mediaQuery(context, 'width', 33),
//                    fontWeight: FontWeight.w200),
//              ),
//              Text(
//                'amount',
//                style: TextStyle(
//                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
//                    color: LikeWalletAppTheme.bule1,
//                    fontSize: mediaQuery(context, 'width', 33),
//                    fontWeight: FontWeight.w200),
//              ),
//            ],
//          ),
//        ),
//        Container(
//          margin: EdgeInsets.only(right: mediaQuery(context, 'width', 30)),
//          height: mediaQuery(context, "height", 111.44),
//          decoration: BoxDecoration(
//            border: Border(
//              left: BorderSide(
//                  color: LikeWalletAppTheme.bule1,
//                  width: mediaQuery(context, 'width', 1)),
//            ),
//          ),
//        ),
//        Expanded(
//          child: Column(
//            crossAxisAlignment: CrossAxisAlignment.start,
//            children: <Widget>[
//              Text(
//                'Total',
//                style: TextStyle(
//                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
//                    color: LikeWalletAppTheme.bule1,
//                    fontSize: mediaQuery(context, 'width', 33),
//                    fontWeight: FontWeight.w200),
//              ),
//              Text(
//                '456,500',
//                style: TextStyle(
//                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
//                    color: LikeWalletAppTheme.bule1,
//                    fontSize: mediaQuery(context, 'width', 33),
//                    fontWeight: FontWeight.w200),
//              ),
//            ],
//          ),
//        ),
//        Container(
//          margin: EdgeInsets.only(right: mediaQuery(context, 'width', 30)),
//          height: mediaQuery(context, "height", 111.44),
//          decoration: BoxDecoration(
//            border: Border(
//              left: BorderSide(
//                  color: LikeWalletAppTheme.bule1,
//                  width: mediaQuery(context, 'width', 1)),
//            ),
//          ),
//        ),
//        Expanded(
//          child: Column(
//            crossAxisAlignment: CrossAxisAlignment.start,
//            children: <Widget>[
//              Text(
//                'Total',
//                style: TextStyle(
//                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
//                    color: LikeWalletAppTheme.bule1,
//                    fontSize: mediaQuery(context, 'width', 33),
//                    fontWeight: FontWeight.w200),
//              ),
//              Text(
//                '456,500',
//                style: TextStyle(
//                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
//                    color: LikeWalletAppTheme.gray1,
//                    fontSize: mediaQuery(context, 'width', 33),
//                    fontWeight: FontWeight.w200),
//              ),
//            ],
//          ),
//        ),
//        Container(
//          height: mediaQuery(context, "height", 111.44),
//          decoration: BoxDecoration(
//            border: Border(
//              left: BorderSide(
//                  color: LikeWalletAppTheme.gray1,
//                  width: mediaQuery(context, 'width', 1)),
//            ),
//          ),
//        ),
//      ],
//    );
  }

  Widget _headText(value, stream) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        new Container(
          alignment: Alignment.centerLeft,
          height: mediaQuery(context, 'height', 60),
          width: mediaQuery(context, 'width', 360),
          child: new Text(
            AppLocalizations.of(context)!.translate(value),
            style: TextStyle(
              letterSpacing: 1.0,
              color: LikeWalletAppTheme.bule1.withOpacity(0.7),
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              fontWeight: FontWeight.w100,
              fontSize: mediaQuery(context, 'height', 36),
            ),
          ),
        ),
        new Container(
          alignment: Alignment.centerLeft,
          height: mediaQuery(context, 'height', 60),
          width: mediaQuery(context, 'width', 360),
          child: StreamBuilder(
            stream: stream.stream,
            initialData: '...loading',
            builder: (context, snapshot) {
              return new Text(
                snapshot.data.toString(),
                style: TextStyle(
                  letterSpacing: 0.5,
                  color: LikeWalletAppTheme.bule1.withOpacity(0.7),
                  fontFamily: 'Proxima Nova',
                  fontWeight: FontWeight.w100,
                  fontSize: mediaQuery(context, 'height', 36),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _appBar() {
    return Container(
        height: mediaQuery(context, 'height', 528),
        child: AppBar(
          actions: <Widget>[
            // Adobe XD layer: 'Path 59001' (shape)
          ],
          leading: Container(),
          // centerTitle: true,
          flexibleSpace: Container(
            width: MediaQuery.of(context).size.width,
            height: mediaQuery(context, 'height', 126),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topRight,
                end: Alignment.bottomLeft,
                colors: <Color>[
                  LikeWalletAppTheme.bule2,
                  LikeWalletAppTheme.bule2_4,
                  LikeWalletAppTheme.bule2_4,
                ],
              ),
            ),
            child: _headDetail(),
          ),
//          bottom:
        ));
  }

  Widget AppBarUi() {
    return Positioned(
        top: mediaQuery(context, "height", 126),
        width: mediaQuery(context, "width", 1080),
        child:
            Row(mainAxisAlignment: MainAxisAlignment.start, children: <Widget>[
          Padding(
            padding: EdgeInsets.only(left: 75.w),
            child:
                Image.asset(LikeWalletImage.icon_title_name, height: 72.07.h),
          ),
          SizedBox(width: 26.w),
          Text(
            firstName + ' ' + lastName,
            style: TextStyle(
              fontFamily: 'Proxima Nova',
              fontSize: 39.h,
              color: const Color(0x4dffffff),
              // letterSpacing: 1.17.,
            ),
            textAlign: TextAlign.left,
          ),
        ]));
  }

  Widget _menu() {
    return AnimatedPositioned(
      left: _tabController.index == 0
          ? mediaQuery(context, 'width', 44)
          : _tabController.index == 1
              ? mediaQuery(context, 'width', 250.5)
              : _tabController.index == 2
                  ? mediaQuery(context, 'width', 551)
                  : _tabController.index == 3
                      ? mediaQuery(context, 'width', 789)
                      : mediaQuery(context, 'width', 0),
      duration: Duration(milliseconds: 0),
      child: Container(
        alignment: Alignment.center,
        width: mediaQuery(context, 'width', 248),
        height: mediaQuery(context, 'height', 129.8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(84.0),
          gradient: RadialGradient(
            center: Alignment(0.0, 0.0),
            radius: 0.5,
            colors: [const Color(0xffc7ea54), const Color(0xffb4e60d)],
            stops: [0.0, 1.0],
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0x1a000000),
              offset: Offset(0, 0),
              blurRadius: 4,
            ),
          ],
        ),
        child: Text(
          _tabController.index == 0
              ? AppLocalizations.of(context)!.translate('banking_send')
              : _tabController.index == 1
                  ? AppLocalizations.of(context)!.translate('banking_receive')
                  : _tabController.index == 2
                      ? AppLocalizations.of(context)!.translate('banking_buy')
                      : _tabController.index == 3
                          ? AppLocalizations.of(context)!
                              .translate('banking_cash')
                          : '',
          style: TextStyle(
            fontFamily: AppLocalizations.of(context)!.translate('font1'),
            fontSize: mediaQuery(context, 'height', 42),
            color: const Color(0xcc141322),
            letterSpacing: 0.3,
            fontWeight: FontWeight.w600,
            shadows: [
              Shadow(
                color: const Color(0x29000000),
                offset: Offset(0, 2),
                blurRadius: 2,
              )
            ],
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}

/* ----------------| COPY Address |--------------*/
class CustomToolTip extends StatelessWidget {
  String text;
  CustomToolTip({required this.text});
  @override
  Widget build(BuildContext context) {
    return new GestureDetector(
      child: new Tooltip(
          preferBelow: false, message: "Copy", child: new Text(text)),
      onTap: () {
        Clipboard.setData(new ClipboardData(text: text));
      },
    );
  }
}
