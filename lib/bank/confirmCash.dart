import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:likewallet/bank/bill_transection.dart';
import 'package:likewallet/bank/navigation_bar/navigation_bar_white.dart';
import 'package:likewallet/model/bank/users.dart';
import 'package:likewallet/screen/NavigationBar.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:likewallet/Theme.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:likewallet/libraryman/setFormat.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:likewallet/libraryman//ethcontract.dart';
import 'package:likewallet/middleware/get_mnemonic.dart';
import 'package:likewallet/middleware/check_auth.dart';
import 'package:likewallet/libraryman/address_service.dart';
import 'package:likewallet/libraryman/configuration_service.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:http/http.dart' as http;
import 'package:likewallet/app_config.dart';
import 'package:likewallet/bank/completeTX.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
//use for check active API before use withdraw
import 'package:likewallet/libraryman/checkAPI.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:likewallet/libraryman/setFormat.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:likewallet/bank/choiceTopay.dart';
import 'package:likewallet/screen/home.dart';
import 'package:likewallet/libraryman/serviceHTTP.dart';
import 'package:likewallet/screen_util.dart';
import 'package:dio/dio.dart';

class ConfirmCash extends StatefulWidget {
  ConfirmCash(
      {required this.rate,
      required this.amount,
      required this.fee,
      required this.nameAccount,
      required this.accountNumber,
      required this.typePay,
      required this.symbol,
      required this.totalSell});
  final double rate;
  final double amount;
  final double fee;
  final String nameAccount;
  final String accountNumber;
  final String typePay;
  final String symbol;
  final double totalSell;

  _ConfirmCash createState() => new _ConfirmCash(
      rate: rate,
      amount: amount,
      fee: fee,
      nameAccount: nameAccount,
      accountNumber: accountNumber,
      typePay: typePay,
      symbol: symbol,
      totalSell: totalSell);
}

class _ConfirmCash extends State<ConfirmCash> {
  _ConfirmCash(
      {required this.rate,
      required this.amount,
      required this.fee,
      required this.nameAccount,
      required this.accountNumber,
      required this.typePay,
      required this.symbol,
      required this.totalSell});

  final double rate;
  final double amount;
  final double fee;
  final String nameAccount;
  final String accountNumber;
  final String typePay;
  final String symbol;

  final double totalSell;

  TextEditingController note = TextEditingController();
  late String pketh;
  late BaseETH eth;
  late GetMnemonic seed;
  late String mnemonic;
  late CheckAuth Logon;
  late IAddressService addressService;
  late IConfigurationService configETH;
  late BaseAuth FirebaseAuth;
  late CheckAPI checkAPI;
  late SetFormat setFormat;
  late BaseAuth auth;
  late AbstractServiceHTTP APIHttp;
  bool _saving = false;
  Dio dio = new Dio();
  String destName = "no";
  String fromName = "no";
  final GlobalKey<State> _keyLoader = new GlobalKey<State>();

  void showColoredToast(msg) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_LONG,
        backgroundColor: Colors.red,
        textColor: Colors.white);
  }

  void showShortToast(msg) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_SHORT,
        backgroundColor: Colors.cyan,
        textColor: Colors.white);
  }

  Future<String> signTransaction() async {
    String tx;
    String value = await seed.getMnemonic();
    String addressSell = await APIHttp.getSellAddress(bank: typePay);
    mnemonic = value.split(":")[0];
    pketh = value.split(":")[1];

    print('signTransaction');
    print(((amount * rate) + (fee * rate)).toString());
    print((totalSell).toString());
    tx = await eth.sendTransaction(
        pk: pketh,
        to: addressSell,
        value: symbol == 'THB'
            ? ((amount * rate) + (fee * rate)).toString()
            : (totalSell).toString());

    print(tx);
    return tx;
  }

  requestPayout() async {
    checkAPI.checkActive().then((statusAPI) {
      if (statusAPI == true) {
        signTransaction().then((tx) {
          print(tx);
          if(tx == '0'){
            setState(() {
              Navigator.of(context, rootNavigator: true).pop();
              // Navigator.of(_keyLoader.currentContext, rootNavigator: true).pop();
            });
            showColoredToast('Service not available in this time.');
          }else {
            FirebaseFirestore.instance
                .collection('logs')
                .doc('withdrawAPP')
                .collection('newlikewallet')
                .doc()
                .set({'tx': tx, 'owner': accountNumber.toString()});

            var url = Uri.https(env.apiUrl, '/sellLikepoint');

            FirebaseAuth.getTokenFirebase().then((_token) async {
              var response = await http.post(url, body: {
                '_token': _token,
                'paymentMethod': 'bank',
                'bankName': typePay.toString(),
                'accountNumber': accountNumber.toString(),
                'tx': tx.toString(),
                'amount': symbol == 'THB'
                    ? (amount * rate).toString()
                    : totalSell.toString(),
                'baht': amount.toString(),
                'fee': fee.toString(),
                'note': note.text != null ? note.text.toString() : 'no note'
              });
              print(response);
              var body = json.decode(response.body);
              print(body["statusCode"]);
              int statusCode = body["statusCode"];
              if (statusCode == 200) {
                setState(() {
                  _saving = false;
                  print(_saving);
                });

                Navigator.pushAndRemoveUntil(
                    context,
                    MaterialPageRoute(
                      builder: (context) =>
                          CompleteTX(
                            title: AppLocalizations.of(context)!
                                .translate('promptpay_withdraw'),
                            detail: AppLocalizations.of(context)!
                                .translate('promptpay_details'),
                            buttonText:
                            AppLocalizations.of(context)!.translate(
                                'backtohome'),
                            back: HomeLikewallet(),
                          ),
                    ),
                        (Route<dynamic> route) => false);
              } else {
//show error and contact support
                print('error');
              }
            });
          }
        });
      } else {
        setState(() {
          Navigator.of(context, rootNavigator: true).pop();
          // Navigator.of(_keyLoader.currentContext, rootNavigator: true).pop();
        });
        showColoredToast('Service not available in this time.');
      }
    });
  }

  Future<void> _handleSubmit(BuildContext context) async {
    try {
      Dialogs.showLoadingDialog(context, _keyLoader); //invoking logi
    } catch (error) {
      print(error);
    }
  }

  @override
  void initState() {
// TODO: implement initState
    super.initState();
    APIHttp = ServiceHTTP();
    setFormat = SetFormatString();
    Logon = new CurCheckAuth();
    seed = new MnemonicRetrieve();
    eth = new EthContract();
    FirebaseAuth = Auth();
    checkAPI = APIChecker();
    setFormat = SetFormatString();
    auth = Auth();
    setState(() {
      _saving = true;
    });
    setInit();
  }

  setInit() async {
    searchName();
  }

  searchName() async {
    auth.getCurrentUser().then((decodeToken) {
      // print(decodeToken);
      FirebaseFirestore.instance
          .collection('users')
          .doc(decodeToken!.uid)
          .get()
          .then((ds) {
        setState(() {
          fromName = ds.data()!['firstName'] + " " + ds.data()!['lastName'];
          _saving = false;
        });
      });
    });
    // auth.getCurrentUser().then((snapshot) {
    //   final users = FirebaseFirestore.instance
    //       .collection('users')
    //       .withConverter<UsersModel>(
    //         fromFirestore: (snapshot, _) =>
    //             UsersModel.fromJson(snapshot.data()!),
    //         toFirestore: (model, _) => model.toJson(),
    //       );
    //   users.doc(snapshot!.uid).get().then((DocumentSnapshot<UsersModel> ds) {
    //     if (!mounted) return;
    //     setState(() {
    //       fromName = ds.data()!.firstName + " " + ds.data()!.lastName;
    //       _saving = false;
    //     });
    //   });
    // });
  }

  int tabMenu = 0;

  changeMenu(_number) async {
    setState(() {
      tabMenu = _number;
    });
  }

  buildForPhone(Orientation orientation) {
    return Scaffold(
      bottomNavigationBar: NavigationBarWhite(),
      resizeToAvoidBottomInset: false,
      // resizeToAvoidBottomPadding: false,
      backgroundColor: Color(0xFFFFFFFF),
      body: SingleChildScrollView(
        child: Stack(
//        alignment: Alignment.center,
          children: <Widget>[
            Column(
              children: <Widget>[
                Container(
                  color: Color(0xff141322),
                  height: MediaQuery.of(context).size.height * 0.10945299145,
                ),
                Container(
                  height: MediaQuery.of(context).size.height * 0.55476923076,
//                  decoration: BoxDecoration(
//                    color: Color(0xffFFFFFF),
//                    boxShadow: [
//                      BoxShadow(
//                        spreadRadius: 0,
//                        blurRadius: 4,
//                        color: Color(0xff707071).withOpacity(0.5),
//                        offset: Offset(
//                          0.0,
//                          2.0,
//                        ),
//                      ),
//                    ],
//                  ),
                ),
                Container(
                  height: MediaQuery.of(context).size.height * 0.1,
                ),
              ],
            ),
            Positioned(
              top: MediaQuery.of(context).size.height * 0.08608547008,
              child: GestureDetector(
                onTap: () => {Navigator.of(context).pop()},
                child: Container(
                  decoration: BoxDecoration(
                    color: Color(0xffB4E60D),
                    borderRadius: new BorderRadius.only(
                        bottomRight: Radius.circular(40.0),
                        topRight: Radius.circular(40.0)),
                  ),
                  height: MediaQuery.of(context).size.height * 0.04797863247,
                  width: MediaQuery.of(context).size.width * 0.18317592592,
                  child: Icon(
                    Icons.arrow_back_ios,
//                        color: Colors.blue,
                    size: MediaQuery.of(context).size.height * 0.0190042735,
                  ),
                ),
              ),
            ),
            Positioned(
              top: mediaQuery(context, 'height', 175),
              child: Container(
                alignment: Alignment.center,
                width: MediaQuery.of(context).size.width,
                child: new Text(
                  AppLocalizations.of(context)!.translate('comfirmcash_title'),
                  style: TextStyle(
                      letterSpacing: 0.5,
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      color: Color(0xffFFFFFF).withOpacity(1),
                      fontSize: mediaQuery(context, 'height', 45),
                      fontWeight: FontWeight.w500),
                ),
              ),
            ),
            // Cash From
            Positioned(
              top: mediaQuery(context, 'height', 444),
              left: mediaQuery(context, 'width', 180),
              child: Container(
                child: new Text(
                  AppLocalizations.of(context)!.translate('comfirm_exchange'),
                  style: TextStyle(
                      letterSpacing: 1,
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      color: LikeWalletAppTheme.gray.withOpacity(1),
                      fontSize: mediaQuery(context, 'height', 27),
                      fontWeight: FontWeight.w100),
                ),
              ),
            ),
            // Cash From Value
            Positioned(
              top: mediaQuery(context, 'height', 427),
              left: mediaQuery(context, 'width', 436),
              child: Container(
                alignment: Alignment.topLeft,
                child: Text(
                  fromName,
//                  amount.toString() + ' BAHT',
                  style: TextStyle(
                      color: Color(0xff707071),
                      fontFamily: 'Proxima Nova',
                      fontWeight: FontWeight.normal,
                      fontSize:
                          MediaQuery.of(context).size.height * 0.***********),
                ),
              ),
            ),
            // Amount
            Positioned(
              top: mediaQuery(context, 'height', 510),
              left: mediaQuery(context, 'width', 180),
              child: Container(
                child: new Text(
                  AppLocalizations.of(context)!.translate('comfirm_amount'),
                  style: TextStyle(
                      letterSpacing: 1,
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      color: LikeWalletAppTheme.gray.withOpacity(1),
                      fontSize: mediaQuery(context, 'height', 27),
                      fontWeight: FontWeight.w100),
                ),
              ),
            ),
            // Amount
            Positioned(
              top: mediaQuery(context, 'height', 494),
              left: mediaQuery(context, 'width', 436),
              child: Container(
                alignment: Alignment.topLeft,
                child: Text(
                  amount.toString() + ' ' + symbol,
                  style: TextStyle(
                      color: Color(0xff707071),
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      fontWeight: FontWeight.normal,
                      fontSize:
                          MediaQuery.of(context).size.height * 0.***********),
                ),
              ),
            ),
            //icon_arrow
            Positioned(
              top: mediaQuery(context, 'height', 603.24),
              left: mediaQuery(context, 'width', 436.08),
              child: new Container(
                child: Image.asset(
                  'assets/image/down_arrow.png',
                  height: MediaQuery.of(context).size.height * 0.04786324786,
                ),
              ),
            ),
            //TO
            Positioned(
              top: mediaQuery(context, 'height', 791),
              left: mediaQuery(context, 'width', 180),
              child: Container(
                child: new Text(
                  AppLocalizations.of(context)!.translate('comfirm_to'),
                  style: TextStyle(
                      letterSpacing: 1,
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      color: LikeWalletAppTheme.gray.withOpacity(1),
                      fontSize: mediaQuery(context, 'height', 27),
                      fontWeight: FontWeight.w100),
                ),
              ),
            ),
            //TO
            Positioned(
              top: mediaQuery(context, 'height', 775),
              left: mediaQuery(context, 'width', 436),
              child: Container(
                alignment: Alignment.topLeft,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      nameAccount.toString(),
                      style: TextStyle(
                          color: Color(0xff707071),
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font1'),
                          fontWeight: FontWeight.normal,
                          fontSize: MediaQuery.of(context).size.height *
                              0.***********),
                    ),
                    // SizedBox(
                    //   width: mediaQuery(context, 'width', 20),
                    // ),
                  ],
                ),
              ),
            ),
            Positioned(
              top: mediaQuery(context, 'height', 841),
              left: mediaQuery(context, 'width', 436),
              child: Container(
                alignment: Alignment.topLeft,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      typePay == 'promptpay' && accountNumber.length == 13
                          ? setFormat.formatIDCrad(accountNumber)
                          : typePay == 'promptpay' || typePay == 'truemoney'
                              ? setFormat.formatMobile(accountNumber)
                              : setFormat.formatBank(accountNumber),
                      style: TextStyle(
                          color: Color(0xff707071),
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font1'),
                          fontWeight: FontWeight.normal,
                          fontSize: MediaQuery.of(context).size.height *
                              0.***********),
                    ),
                  ],
                ),
              ),
            ),
            //Amount
            Positioned(
              top: mediaQuery(context, 'height', 1041),
              left: mediaQuery(context, 'width', 180),
              child: Container(
                child: new Text(
                  AppLocalizations.of(context)!.translate('comfirm_amount'),
//                  AppLocalizations.of(context)!.translate('comfirmcash_fee'),
                  style: TextStyle(
                      letterSpacing: 1,
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      color: LikeWalletAppTheme.gray.withOpacity(1),
                      fontSize: mediaQuery(context, 'height', 27),
                      fontWeight: FontWeight.w100),
                ),
              ),
            ),
            //Amount
            Positioned(
              top: mediaQuery(context, 'height', 1031),
              left: mediaQuery(context, 'width', 436),
              child: Container(
                alignment: Alignment.topLeft,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      amount.toString() + ' ' + symbol,
                      style: TextStyle(
                          color: Color(0xff707071),
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font1'),
                          fontWeight: FontWeight.normal,
                          fontSize: MediaQuery.of(context).size.height *
                              0.***********),
                    ),
                  ],
                ),
              ),
            ),
            //Fee
            Positioned(
              top: mediaQuery(context, 'height', 1188),
              left: mediaQuery(context, 'width', 180),
              child: Container(
                child: new Text(
                  AppLocalizations.of(context)!.translate('comfirmcash_fee'),
                  style: TextStyle(
                      letterSpacing: 1,
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      color: LikeWalletAppTheme.gray.withOpacity(1),
                      fontSize: mediaQuery(context, 'height', 27),
                      fontWeight: FontWeight.w100),
                ),
              ),
            ),
            //Fee
            Positioned(
              top: mediaQuery(context, 'height', 1173),
              left: mediaQuery(context, 'width', 436),
              child: Container(
                alignment: Alignment.topLeft,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      fee.toString() + ' ' + symbol,
                      style: TextStyle(
                          color: Color(0xff707071),
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font1'),
                          fontWeight: FontWeight.normal,
                          fontSize: MediaQuery.of(context).size.height *
                              0.***********),
                    ),
                  ],
                ),
              ),
            ),

            Positioned(
              top: mediaQuery(context, 'height', 1365),
              left: mediaQuery(context, 'width', 728),
              child: new GestureDetector(
                onTap: () async {
//                billTransection();
//                   print((amount * rate).toString());
                  await _handleSubmit(context);
                  requestPayout();
                },
                child: ClipOval(
                  child: Container(
                    alignment: Alignment.center,
                    color: Color(0xff2B3038).withOpacity(1),
                    height: MediaQuery.of(context).size.height * 0.10854700854,
                    // height of the button
                    width: MediaQuery.of(context).size.height * 0.10854700854,
                    // width of the button
                    child: new Text(
                      AppLocalizations.of(context)!
                          .translate('comfirmcash_button'),
                      style: TextStyle(
                          color: Color(0xffB4E60D),
                          fontFamily: 'Proxima Nova',
                          fontWeight: FontWeight.normal,
                          fontSize: MediaQuery.of(context).size.height *
                              0.018194017094),
                    ),
                  ),
                ),
              ),
            ),
//          Positioned(
//            top: MediaQuery.of(context).size.height * 0.25,
//            left: MediaQuery.of(context).size.width * 0.07,
//            right: MediaQuery.of(context).size.width * 0.07,
//            child: new Container(
////              alignment: Alignment.center,
//              child: Row(
//                mainAxisAlignment: MainAxisAlignment.center,
//                crossAxisAlignment: CrossAxisAlignment.start,
//                children: <Widget>[
//                  new Container(
////                    color: Colors.amber,
//                    padding: EdgeInsets.only(
//                        left: MediaQuery.of(context).size.width * 0),
//                    alignment: Alignment.bottomLeft,
//                    height: MediaQuery.of(context).size.height * 0.045,
//                    width: MediaQuery.of(context).size.width * 0.45,
//                    child: Text(
//                      'FEE',
//                      style: TextStyle(
//                          color: Color(0xff000000).withOpacity(1),
//                          fontFamily: 'Proxima Nova',
//                          fontWeight: FontWeight.normal,
//                          fontSize: 13.0),
//                    ),
//                  ),
//                  new Container(
////                    color: Colors.blue,
//                    alignment: Alignment.bottomLeft,
//                    height: MediaQuery.of(context).size.height * 0.05,
//                    width: MediaQuery.of(context).size.width * 0.25,
////                          color: Colors.green,
//                    child: Text(
//                      fee.toString(),
//                      style: TextStyle(
//                          color: Color(0xff000000).withOpacity(1),
//                          fontFamily: 'Roboto',
//                          fontWeight: FontWeight.normal,
//                          fontSize: 28.0),
//                    ),
//                  ),
//                  new Container(
////                    color: Colors.blueGrey,
//                    alignment: Alignment.bottomLeft,
//                    height: MediaQuery.of(context).size.height * 0.045,
//                    width: MediaQuery.of(context).size.width * 0.12,
//                    child: Text(
//                      '  BAHT',
//                      style: TextStyle(
//                          color: Color(0xff000000).withOpacity(1),
//                          fontFamily: 'Roboto',
//                          fontWeight: FontWeight.normal,
//                          fontSize: 13.0),
//                    ),
//                  ),
//                ],
//              ),
//            ),
//          ),
//          Positioned(
//            top: MediaQuery.of(context).size.height * 0.3,
//            left: MediaQuery.of(context).size.width * 0.07,
//            right: MediaQuery.of(context).size.width * 0.07,
//            child: new Container(
////              alignment: Alignment.center,
//              child: Row(
//                mainAxisAlignment: MainAxisAlignment.center,
//                crossAxisAlignment: CrossAxisAlignment.start,
//                children: <Widget>[
//                  new Container(
////                    color: Colors.amber,
//                    padding: EdgeInsets.only(
//                        left: MediaQuery.of(context).size.width * 0),
//                    alignment: Alignment.bottomLeft,
//                    height: MediaQuery.of(context).size.height * 0.045,
//                    width: MediaQuery.of(context).size.width * 0.45,
//                    child: Text(
//                      'ACCOUNT NUMBER',
//                      style: TextStyle(
//                          color: Color(0xff000000).withOpacity(1),
//                          fontFamily: 'Proxima Nova',
//                          fontWeight: FontWeight.normal,
//                          fontSize: 13.0),
//                    ),
//                  ),
//                  new Container(
////                    color: Colors.blue,
//                    alignment: Alignment.bottomLeft,
//                    height: MediaQuery.of(context).size.height * 0.05,
//                    width: MediaQuery.of(context).size.width * 0.35,
////                          color: Colors.green,
//                    child: Text(
//                      typePay == 'promptpay'
//                          ? setFormat.formatMobile(accountNumber)
//                          : typePay == 'truemoney'
//                              ? setFormat.formatMobile(accountNumber)
//                              : setFormat.formatBank(accountNumber),
//                      style: TextStyle(
//                          color: Color(0xff000000).withOpacity(1),
//                          fontFamily: 'Roboto',
//                          fontWeight: FontWeight.normal,
//                          fontSize: 18.0),
//                    ),
//                  ),
//                ],
//              ),
//            ),
//          ),
//          Positioned(
//            top: MediaQuery.of(context).size.height * 0.35,
//            left: MediaQuery.of(context).size.width * 0.07,
//            right: MediaQuery.of(context).size.width * 0.07,
//            child: new Container(
////              alignment: Alignment.center,
//              child: Row(
//                mainAxisAlignment: MainAxisAlignment.center,
//                crossAxisAlignment: CrossAxisAlignment.start,
//                children: <Widget>[
//                  new Container(
////                    color: Colors.amber,
//                    padding: EdgeInsets.only(
//                        left: MediaQuery.of(context).size.width * 0),
//                    alignment: Alignment.bottomLeft,
//                    height: MediaQuery.of(context).size.height * 0.045,
//                    width: MediaQuery.of(context).size.width * 0.45,
//                    child: Text(
//                      'TYPE PAY',
//                      style: TextStyle(
//                          color: Color(0xff000000).withOpacity(1),
//                          fontFamily: 'Proxima Nova',
//                          fontWeight: FontWeight.normal,
//                          fontSize: 13.0),
//                    ),
//                  ),
//                  new Container(
////                    color: Colors.blue,
//                    alignment: Alignment.bottomLeft,
//                    height: MediaQuery.of(context).size.height * 0.05,
//                    width: MediaQuery.of(context).size.width * 0.35,
////                          color: Colors.green,
//                    child: Text(
//                      typePay,
//                      style: TextStyle(
//                          color: Color(0xff000000).withOpacity(1),
//                          fontFamily: 'Roboto',
//                          fontWeight: FontWeight.normal,
//                          fontSize: 16.0),
//                    ),
//                  ),
//                ],
//              ),
//            ),
//          ),
//          Positioned(
//            top: MediaQuery.of(context).size.height * 0.47,
//            left: MediaQuery.of(context).size.width * 0.07,
//            right: MediaQuery.of(context).size.width * 0.025,
//            child: new Container(
//              alignment: Alignment.centerLeft,
//              child: new Text(
//                'WILL BE DEPOSIT INTO ACCOUNT',
//                textAlign: TextAlign.center,
//                style: TextStyle(
//                    fontFamily: 'Proxima Nova',
//                    color: Color(0xff000000),
//                    fontSize: 14,
//                    fontWeight: FontWeight.normal),
//              ),
//            ),
//          ),
//          Positioned(
//            top: MediaQuery.of(context).size.height * 0.5,
//            left: MediaQuery.of(context).size.width * 0.07,
//            right: MediaQuery.of(context).size.width * 0.025,
//            child: new Container(
//                alignment: Alignment.centerLeft,
//                child: Row(
//                  children: <Widget>[
//                    new Text(
//                      typePay == 'promptpay'
//                          ? setFormat.formatMobile(accountNumber)
//                          : typePay == 'truemoney'
//                              ? setFormat.formatMobile(accountNumber)
//                              : setFormat.formatBank(accountNumber),
//                      textAlign: TextAlign.center,
//                      style: TextStyle(
//                          fontFamily: 'Proxima Nova',
//                          color: Color(0xffACE000),
//                          fontSize: 14,
//                          fontWeight: FontWeight.normal),
//                    ),
////                new Text(
////                  'WITHIN 24 HOURS ',
////                  textAlign: TextAlign.center,
////                  style: TextStyle(
////                      fontFamily: 'Proxima Nova',
////                      color: Color(0xff000000),
////                      fontSize: 14,
////                      fontWeight: FontWeight.normal),
////                ),
//                  ],
//                )),
//          ),
//          Positioned(
//            top: MediaQuery.of(context).size.height * 0.55,
//            left: MediaQuery.of(context).size.width * 0.07,
//            right: MediaQuery.of(context).size.width * 0.025,
//            child: new Container(
//              alignment: Alignment.centerLeft,
//              child: new Text(
//                'NOTE',
//                style: TextStyle(
//                    color: Color(0xff707071).withOpacity(0.5),
//                    fontFamily: 'Proxima Nova',
//                    fontWeight: FontWeight.normal,
//                    fontSize: 13.0),
//              ),
//            ),
//          ),
//          Positioned(
//            top: MediaQuery.of(context).size.height * 0.58,
//            left: MediaQuery.of(context).size.width * 0.07,
//            right: MediaQuery.of(context).size.width * 0.07,
//            child: new Container(
//              alignment: Alignment.centerLeft,
//              child: new Container(
////                padding: EdgeInsets.only(
////                    left: MediaQuery.of(context).size.height *
////                        0.05),
//                height: MediaQuery.of(context).size.height * 0.12,
//                width: MediaQuery.of(context).size.width * 0.9,
//                child: Material(
//                  color: Colors.white,
//                  elevation: 3.0,
//                  shadowColor: Colors.black,
//                  child: TextFormField(
//                    controller: note,
//                    keyboardType: TextInputType.text,
//                    maxLines: 4,
//                    style: TextStyle(fontSize: 18),
////                              obscureText: true,
//                    autofocus: false,
//                    decoration: InputDecoration(
//                      hintText: '',
//                      hintStyle: TextStyle(
//                          fontSize: 18,
//                          color: Color(0xff6C6B6D).withOpacity(0.5)),
//                      fillColor: Colors.white,
//                      filled: true,
//                      contentPadding:
//                          EdgeInsets.fromLTRB(20.0, 10.0, 20.0, 10.0),
//                      enabledBorder: OutlineInputBorder(
//                          borderRadius: BorderRadius.circular(15.0),
//                          borderSide:
//                              BorderSide(color: Colors.white, width: 3.0)),
//                    ),
//                  ),
//                ),
//              ),
//            ),
//          ),
//          Positioned(
//            top: MediaQuery.of(context).size.height * 0.8,
//            left: MediaQuery.of(context).size.width * 0.07,
//            right: MediaQuery.of(context).size.width * 0.07,
//            child: new Container(
//              alignment: Alignment.centerLeft,
//              child: new Container(
//                alignment: Alignment.center,
//                height: MediaQuery.of(context).size.height * 0.07,
//                width: MediaQuery.of(context).size.width * 0.9,
//                child: ButtonTheme(
//                  minWidth: MediaQuery.of(context).size.width / 1.2,
//                  height: MediaQuery.of(context).size.height * 0.07,
//                  child: new FlatButton(
//                      onPressed: () {
////                      Navigator.of(context).pushNamed('/billTransection');
//
//                        requestPayout();
//                      },
//                      shape: new RoundedRectangleBorder(
//                        borderRadius: new BorderRadius.circular(8.0),
//                      ),
//                      disabledColor: Color(0xff150e23).withOpacity(0.8),
//                      color: Color(0xff150e23).withOpacity(0.8),
////                      onPressed: () {_sendTransaction();},
//                      child: new Text(
//                        'Confirm',
//                        style: TextStyle(
//                            color: Color(0xffB4E60D),
//                            fontSize: 18,
//                            fontWeight: FontWeight.normal),
//                      )),
//                ),
//              ),
//            ),
//          ),
          ],
        ),
      ),
    );
  }

  buildForTablet(Orientation orientation) {
    return Scaffold();
  }

  @override
  Widget build(BuildContext context) {
    final double shortestSide = MediaQuery.of(context).size.shortestSide;
    final bool useMobileLayout = shortestSide < 600.0;
    final Orientation orientation = MediaQuery.of(context).orientation;
    return ModalProgressHUD(
      opacity: 0.1,
      child: Scaffold(
        body: useMobileLayout
            ? buildForPhone(orientation)
            : buildForPhone(orientation),
      ),
      inAsyncCall: _saving,
      progressIndicator: CustomLoading(),
    );
  }

  Widget? _loadingView() {
    setState(() {
      _saving = true;
    });
//Simulate a service call
    print('submitting to backend...');
    new Future.delayed(new Duration(seconds: 2), () {
      setState(() {
        changeMenu(1);
      });
    });
  }
}

class Dialogs {
  static Future<void> showLoadingDialog(
      BuildContext context, GlobalKey key) async {
    return showDialog<void>(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return new WillPopScope(
              onWillPop: () async => false,
              child: SimpleDialog(
                  key: key,
                  backgroundColor: Colors.white.withOpacity(0.8),
                  children: <Widget>[
                    Center(
                      child: Column(children: [
                        CircularProgressIndicator(),
                        SizedBox(
                          height: 50.h,
                        ),
                        Text(
                          AppLocalizations.of(context)!
                              .translate('alert_cash_out'),
                          style: TextStyle(color: Colors.blueAccent),
                        )
                      ]),
                    )
                  ]));
        });
  }
}
