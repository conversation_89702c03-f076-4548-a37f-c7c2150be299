import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:likewallet/bank/banking.dart';
import 'package:likewallet/libraryman/app_local.dart';

class CompleteTransaction extends StatefulWidget {
  _CompleteTransaction createState() => new _CompleteTransaction();
}

class _CompleteTransaction extends State<CompleteTransaction> {
  buildForPhone(Orientation orientation) {
    return Scaffold(
      backgroundColor: Color(0xFFFFFFFF),
      body: Align(
        alignment: Alignment.center,
        child: Column(
          children: <Widget>[
            SizedBox(
              height: MediaQuery.of(context).size.height * 0.15,
            ),
            new Container(
              alignment: Alignment.center,
              height: MediaQuery.of(context).size.height * 0.1,
              width: MediaQuery.of(context).size.width * 0.15,
              child: Image.asset(
                'assets/image/complete.png',
                scale: 1,
              ),
            ),
            new Container(
              alignment: Alignment.center,
              height: MediaQuery.of(context).size.height * 0.05,
              width: MediaQuery.of(context).size.width * 0.8,
              child: new Text(
                AppLocalizations.of(context)!.translate('complete_titel1'),
                style: TextStyle(
                    color: Color(0xff000000).withOpacity(1),
                    fontFamily: AppLocalizations.of(context)!.translate('font'),
                    fontWeight: FontWeight.normal,
                    fontSize: 15.0),
              ),
            ),
            new Container(
              alignment: Alignment.center,
              height: MediaQuery.of(context).size.height * 0.2,
              width: MediaQuery.of(context).size.width * 0.8,
              child: new Text(
                AppLocalizations.of(context)!.translate('complete_titel2'),
                style: TextStyle(
                    color: Color(0xff000000).withOpacity(1),
                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
                    fontWeight: FontWeight.normal,
                    fontSize: 17.0),
              ),
            ),
            new Container(
              alignment: Alignment.center,
              height: MediaQuery.of(context).size.height * 0.07,
              width: MediaQuery.of(context).size.width * 0.9,
              child: ButtonTheme(
                minWidth: MediaQuery.of(context).size.width * 0.95,
                height: MediaQuery.of(context).size.height * 0.07,
                child: TextButton(
                  style: ButtonStyle(
                    shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                      RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                    ),
                    backgroundColor: MaterialStateProperty.resolveWith<Color>(
                          (Set<MaterialState> states) {
                        if (states.contains(MaterialState.disabled)) {
                          return Color(0xff141322).withOpacity(1); // Disabled color
                        }
                        return Color(0xff141322).withOpacity(1); // Regular color
                      },
                    ),
                  ),
                  onPressed: () {
                    Navigator.pushAndRemoveUntil(
                      context,
                      MaterialPageRoute(
                        builder: (context) => Banking(
                          selectPage: 1,
                          vending: null!,
                          abi: '',
                          callFunction: '',
                          selectedAddr: '',
                          contract: '',
                          titleContact: '',
                          address: '',
                          source: '',
                          phone: '',
                          isVending: false,
                        ),
                      ),
                          (Route<dynamic> route) => false,
                    );
                  },
                  child: Text(
                    AppLocalizations.of(context)!.translate('complete_back'),
                    style: TextStyle(
                      color: Color(0xffB4E60D),
                      fontFamily: 'Proxima Nova',
                      fontSize: 18,
                      fontWeight: FontWeight.normal,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  buildForTablet(Orientation orientation) {
    return Scaffold();
  }

  int tabMenu = 0;
  changeMenu(_number) async {
    setState(() {
      tabMenu = _number;
    });
  }

  @override
  Widget build(BuildContext context) {
    final double shortestSide = MediaQuery.of(context).size.shortestSide;
    final bool useMobileLayout = shortestSide < 600.0;
    final Orientation orientation = MediaQuery.of(context).orientation;
    return Scaffold(
      body: useMobileLayout
          ? buildForPhone(orientation)
          : buildForTablet(orientation),
    );
  }
}
