import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:image_crop/image_crop.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/screen_util.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'dart:io';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
// import 'package:simple_image_crop/simple_image_crop.dart';
import 'package:camera/camera.dart';
import 'package:path/path.dart' show join;
import 'package:path_provider/path_provider.dart';
import 'package:likewallet/kyc/crop_image.dart';
import 'package:likewallet/kyc/kyc.dart';

import 'package:path/path.dart' show basename;
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:likewallet/app_config.dart';

import 'package:dio/dio.dart';
import 'package:image_picker/image_picker.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:flutter/foundation.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:likewallet/Theme.dart';

enum stateKYCPhoto { FRONT, BACK, SELFIE }

class FavoriteTakePhoto extends StatefulWidget {
  _FavoriteTakePhoto createState() => new _FavoriteTakePhoto();
}

class _FavoriteTakePhoto extends State<FavoriteTakePhoto> {
  late List cameras;
//  final CameraDescription firstCamera;
  late int selectedCameraIdx;
  late CameraDescription selectedCamera;
  late CameraLensDirection lensDirection;

  bool checkFace = false;

  /// ///
  final cropKey = GlobalKey<CropState>();
  Key containerCamera = UniqueKey();
  late CameraController _controller;
  late Future<void> _initializeControllerFuture;
  String curImage = 'none';
  late File imagefile;
//  final int page;
  int number = 0;
  int count = 0;
  final ImagePicker _picker = ImagePicker();

  bool _saving = false;
  changeContent(value) {
    if (value == 0) {
      setState(() {
        curImage = 'none';
      });
    }
    setState(() {
      number = value;
      imagefile = null as File;
    });
  }

  bool selfie_check = false;
  late SharedPreferences sharedPreferences;

  @override
  void dispose() {
    super.dispose();
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    availableCameras().then((availableCameras) {
      cameras = availableCameras;
      if (cameras.length > 0) {
        setState(() {
          // 2
          selectedCameraIdx = 0;
        });
        _initializeControllerFuture =
            _initCameraController(cameras[selectedCameraIdx]).then((void v) {});
      } else {
        print("No camera available");
      }
    }).catchError((err) {
      // 3
      print('Error: $err.code\nError Message: $err.message');
    });

//    _controller = CameraController(
//      // Get a specific camera from the list of available cameras.
//      firstCamera,
//      // Define the resolution to use.
//      ResolutionPreset.max,
//    );

    // Next, initialize the controller. This returns a Future.
//    _initializeControllerFuture = _controller.initialize();
  }

  void showColoredToast(msg, colors) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_LONG,
        backgroundColor: colors,
        textColor: Colors.white);
  }

  Future<void> takePic() async {
    // Take the Picture in a try / catch block. If anything goes wrong,
    // catch the error.
    try {
      // Ensure that the camera is initialized.
      await _initCameraController;

      // Construct the path where the image should be saved using the
      // pattern package.
      final path = join(
        // Store the picture in the temp directory.
        // Find the temp directory using the `path_provider` plugin.
        (await getTemporaryDirectory()).path,
        '${DateTime.now()}.png',
      );
      final path2 = join(
        // Store the picture in the temp directory.
        // Find the temp directory using the `path_provider` plugin.
        (await getTemporaryDirectory()).path,
        '${DateTime.now()}2.png',
      );
      // Attempt to take a picture and log where it's been saved.
      await _controller.takePicture();
      ImageProcessor.cropSquare(path, path2, false).then((data) {
        if (!mounted) return;
        setState(() {
          curImage = path2;
        });
      });
      // If the picture was taken, display it on a new screen.

    } catch (e) {
      // If an error occurs, log the error to the console.
      print(e);
    }
  }

  @override
  Widget build(BuildContext context) {
    final args = ModalRoute.of(context)!.settings.arguments;
    return Scaffold(
      backgroundColor: Color(0xff141322),
      body: ModalProgressHUD(
        opacity: 0.1,
        child: Selfie(args),
        inAsyncCall: _saving,
        progressIndicator: CustomLoading(),
      ),
    );
  }

  Widget Selfie(args) {
    return Stack(
      alignment: Alignment.center,
      children: <Widget>[
        //ปุ่ม back
        Positioned(
          top: mediaQuery(context, 'height', 136),
          child: backButton(context, LikeWalletAppTheme.gray),
        ),
        if (number == 0) cameraScreen(),
        title(),
        if (number == 0)
          Positioned(
            top: MediaQuery.of(context).size.height *
                Screen_util("height", 1100),
//            left: MediaQuery.of(context).size.width * Screen_util("width", 461),
            child: GestureDetector(
                onTap: () {
                  takePic();
                  changeContent(1);
                },
                child: Image.asset(
                  LikeWalletImage.button_take_photo,
                  height: MediaQuery.of(context).size.height *
                      Screen_util("height", 200),
                  width: MediaQuery.of(context).size.width *
                      Screen_util("width", 200),
                )),
          ),

        if (number == 1) previewPhotos(),
//        FocusCamera(),
        if (number == 0) BorderCamera(),
        BG(),
        Positioned(
          top: MediaQuery.of(context).size.height * Screen_util("height", 1100),
//            left: MediaQuery.of(context).size.width * Screen_util("width", 461),
          child: GestureDetector(
              onTap: () {
                takePic();

                changeContent(1);
              },
              child: Image.asset(
                'assets/image/button_take_photo.png',
                height: MediaQuery.of(context).size.height *
                    Screen_util("height", 200),
                width: MediaQuery.of(context).size.width *
                    Screen_util("width", 200),
              )),
        ),
        if (number == 1)
          Positioned(
            top: MediaQuery.of(context).size.height *
                Screen_util("height", 1344),
            child: GestureDetector(
              onTap: () {
                changeContent(0);
              },
              child: Container(
                  alignment: Alignment.center,
                  height: MediaQuery.of(context).size.height *
                      Screen_util("height", 257),
                  width: MediaQuery.of(context).size.width *
                      Screen_util("width", 257),
                  decoration: BoxDecoration(
                    color: LikeWalletAppTheme.white2,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        spreadRadius: 0,
                        blurRadius: 5,
                        color: LikeWalletAppTheme.black.withOpacity(0.2),
                        offset: Offset(
                          0.0,
                          1.0,
                        ),
                      ),
                    ],
                  ),
                  child: Text(
                    AppLocalizations.of(context)!.translate('button_again'),
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      letterSpacing: 1,
                      color: LikeWalletAppTheme.white.withOpacity(0.6),
                      fontWeight: FontWeight.w100,
                      fontSize: MediaQuery.of(context).size.height *
                          Screen_util("height", 30),
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                    ),
                  )),
            ),
          ),
        if (number == 1)
          Positioned(
            top: MediaQuery.of(context).size.height *
                Screen_util("height", 1652),
            child: GestureDetector(
              onTap: () async {
                // final crop = cropKey.currentState;
                final scale = cropKey.currentState!.scale;
                final area = cropKey.currentState!.area;
                if (imagefile != null) {

                  final sample = await ImageCrop.sampleImage(
                    file: imagefile,
                    preferredSize: (2000 / scale).round(),
                  );

                  if (area == null) {
                    // cannot crop, widget is not setup
                    return;
                  }

                  final croppedFile = await ImageCrop.cropImage(
                    file: sample,
                    area: area,
                  );
                  Navigator.pop(context, croppedFile);
                  // final croppedFile =
                  //     await crop!.cropCompleted(imagefile, pictureQuality: 600);
                  // Navigator.pop(context, croppedFile);
                } else if (curImage != null) {

                  final sample = await ImageCrop.sampleImage(
                    file: File(curImage),
                    preferredSize: (2000 / scale).round(),
                  );

                  if (area == null) {
                    // cannot crop, widget is not setup
                    return;
                  }

                  final croppedFile = await ImageCrop.cropImage(
                    file: sample,
                    area: area,
                  );
                  Navigator.pop(context, croppedFile);
                }
              },
              child: Container(
                  alignment: Alignment.center,
                  height: MediaQuery.of(context).size.height *
                      Screen_util("height", 257),
                  width: MediaQuery.of(context).size.width *
                      Screen_util("width", 257),
                  decoration: BoxDecoration(
                    color: LikeWalletAppTheme.white2,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        spreadRadius: 0,
                        blurRadius: 5,
                        color: LikeWalletAppTheme.black.withOpacity(0.2),
                        offset: Offset(
                          0.0,
                          1.0,
                        ),
                      ),
                    ],
                  ),
                  child: Text(
                    AppLocalizations.of(context)!.translate('button_yes'),
                    style: TextStyle(
                      letterSpacing: 0.8,
                      color: LikeWalletAppTheme.bule1,
                      fontSize: MediaQuery.of(context).size.height *
                          Screen_util("height", 30),
                      fontWeight: FontWeight.w100,
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                    ),
                  )),
            ),
          ),
      ],
    );
  }

  Widget title() {
    return number == 1

        ///เเสดงมื่อมีค่ารูป
        ? Positioned(
            top:
                MediaQuery.of(context).size.height * Screen_util("height", 240),
            child: Container(
                width: MediaQuery.of(context).size.width,
                alignment: Alignment.center,
                child: Container(
                    height: MediaQuery.of(context).size.height *
                        Screen_util("height", 110),
                    width: MediaQuery.of(context).size.width *
                        Screen_util("width", 500),
                    alignment: Alignment.center,
                    child: Text(
                      AppLocalizations.of(context)!
                            .translate('favorite_take_photo_adjust'),
                      style: TextStyle(
                        color: LikeWalletAppTheme.bule1_2,
                        fontSize: MediaQuery.of(context).size.height *
                            Screen_util("height", 50),
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                        letterSpacing: 1,
                      ),
                    ))))

        ///เเสดงเมื่อไม่มีรูปห
        : Positioned(
            top:
                MediaQuery.of(context).size.height * Screen_util("height", 167),
            child: Column(
              children: <Widget>[
                Container(
                    height: MediaQuery.of(context).size.height *
                        Screen_util("height", 110),
                    width: MediaQuery.of(context).size.width *
                        Screen_util("width", 500),
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: Colors.white.withOpacity(0.2),
                          width: MediaQuery.of(context).size.width *
                              Screen_util("width", 2),
                        ),
                      ),
                    ),
                    child: Text(
                      AppLocalizations.of(context)!
                            .translate('favorite_take_photo_head'),
                      style: TextStyle(
                        color: LikeWalletAppTheme.bule1_2,
                        fontSize: MediaQuery.of(context).size.height *
                            Screen_util("height", 50),
                        fontFamily: AppLocalizations.of(context)!
                            .translate('font1Light'),
                        letterSpacing: 1,
                      ),
                    )),
                GestureDetector(
                    onTap: () {
                      getImage();
                    },
                    child: Container(
                      height: MediaQuery.of(context).size.height *
                          Screen_util("width", 50),
                      width: MediaQuery.of(context).size.width *
                          Screen_util("width", 300),
                      alignment: Alignment.center,
                      child: Text(
                        AppLocalizations.of(context)!
                            .translate('favorite_take_photo_title'),
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: MediaQuery.of(context).size.height *
                              Screen_util("height", 30),
                          fontFamily: AppLocalizations.of(context)!
                            .translate('font1Light'),
                          letterSpacing: 0.5,
                        ),
                      ),
                    )),
              ],
            ));
  }

  Widget cameraScreen() {
    return Positioned(
      top: MediaQuery.of(context).size.height * Screen_util("height", 390),
      child: Container(
          color: Colors.grey.withOpacity(0.3),
          height:
              MediaQuery.of(context).size.height * Screen_util("height", 794),
          width: MediaQuery.of(context).size.width * Screen_util("width", 1080),
          child: FutureBuilder<void>(
            future: _initializeControllerFuture,
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.done) {
                // If the Future is complete, display the preview.
                return AspectRatio(
                  aspectRatio: 1.5,
                  child: ClipRect(
                    child: Transform.scale(
                      scale: 1.5 / _controller.value.aspectRatio,
                      child: Center(
                        child: AspectRatio(
                          aspectRatio: _controller.value.aspectRatio,
                          child: CameraPreview(_controller),
                        ),
                      ),
                    ),
                  ),
                );
              } else {
                // Otherwise, display a loading indicator.
                return Center(child: CircularProgressIndicator());
              }
            },
          )),
    );
  }

  Widget previewPhotos() {
    return Positioned(
      top: MediaQuery.of(context).size.height * Screen_util("height", 390),
      child: Container(
          height:
              MediaQuery.of(context).size.height * Screen_util("height", 786),
          width: MediaQuery.of(context).size.width * Screen_util("width", 1080),
          color: Colors.white.withOpacity(0.3),
          child: curImage == 'none' && imagefile == null
              ? Center(child: CircularProgressIndicator())
              : curImage == 'none' && imagefile != null
                  ? Container(
                      height: MediaQuery.of(context).size.height *
                          Screen_util("height", 786),
                      width: MediaQuery.of(context).size.width *
                          Screen_util("width", 1080),
                      color: LikeWalletAppTheme.black,
                      child: Crop(
                        // chipShape: 'circle',
                        key: cropKey,
                        // chipRadius: 100,
                        // chipShape: 'rect',
                        maximumScale: 3,
                        image: FileImage(imagefile),
                      ),
                    )
                  : Container(
                      height: MediaQuery.of(context).size.height *
                          Screen_util("height", 786),
                      width: MediaQuery.of(context).size.width *
                          Screen_util("width", 1080),
                      color: LikeWalletAppTheme.black,
                      child: Crop(
                        // chipShape: 'circle',
                        key: cropKey,
                        // chipRadius: 100,
                        // chipShape: 'rect',
                        maximumScale: 3,
                        image: FileImage(File(curImage)),
                      ),
                    )),
    );
  }

  Future getImage() async {

    var image = await _picker.getImage(source: ImageSource.gallery);
    setState(() {
      imagefile = File(image!.path);

      print(imagefile);

      ///ถ้ายังไม่ได้เลือกจะคืนค่า nullไม่เปลี่ยนเเปลง UI
      if (imagefile == null) {
        number = 0;
      } else {
        number = 1;
      }
    });
//    Navigator.of(context).pushReplacementNamed(
//      'crop_page',
//      arguments: {'image': image, 'status': 1},
//    );
  }

  Widget BorderCamera() {
    return Positioned(
      top: MediaQuery.of(context).size.height * Screen_util("height", 470),
      child: Image.asset(LikeWalletImage.border_camera),
      height: mediaQuery(context, 'height', 608.3),
      width: mediaQuery(context, 'width', 924.53),
    );
  }

  Widget FocusCamera() {
    return Positioned(
      top: MediaQuery.of(context).size.height * Screen_util("height", 390),
      child: Image.asset(
        LikeWalletImage.focus_photo,
        fit: BoxFit.contain,
        width: mediaQuery(context, 'width', 1080),
      ),
    );
  }

  Widget BG() {
    return Positioned(
        bottom: 0,
        child: Container(
          child: Image.asset(
            LikeWalletImage.bg_take_photo,
            fit: BoxFit.fill,
            height: mediaQuery(context, 'height', 1325.87),
            width: mediaQuery(context, 'width', 1080),
          ),
        ));
  }

  void _onSwitchCamera() {
    selectedCameraIdx =
        selectedCameraIdx < cameras.length - 1 ? selectedCameraIdx + 1 : 0;
    CameraDescription selectedCamera = cameras[selectedCameraIdx];
    _initCameraController(selectedCamera);
  }

  Future _initCameraController(CameraDescription cameraDescription) async {
    if (_controller != null) {
//      await _controller.dispose();
    }

    // 3
    _controller = CameraController(cameraDescription, ResolutionPreset.high);

    // If the controller is updated then update the UI.
    // 4
    _controller.addListener(() {
      // 5
      if (mounted) {
        setState(() {});
      }

      if (_controller.value.hasError) {
        print('Camera error ${_controller.value.errorDescription}');
      }
    });

    // 6
    try {
      await _controller.initialize();
    } on CameraException catch (e) {
//      _showCameraException(e);
      print(e);
    }

    if (mounted) {
      setState(() {});
    }
  }

  SetBool_selfie(bool value) async {
    sharedPreferences = await SharedPreferences.getInstance();
    setState(() {
      selfie_check = value;
      sharedPreferences.setBool("selfie_check", selfie_check);
    });
  }

  Future<Null> showImage(BuildContext context, File file) async {
    new FileImage(file)
        .resolve(new ImageConfiguration())
        .addListener(ImageStreamListener((ImageInfo info, bool _) {
      print('-------------------------------------------$info');
    }));
    return showDialog<Null>(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
              title: Text(
                'Current screenshot：',
                style: TextStyle(
                    fontFamily: 'Roboto',
                    fontWeight: FontWeight.w300,
                    color: Theme.of(context).primaryColor,
                    letterSpacing: 1.1),
              ),
              content: Image.file(file));
        });
  }
}
//import 'dart:io';
//import 'dart:async';
//
//import 'package:flutter/material.dart';
//import 'package:image_picker/image_picker.dart';
//import 'package:simple_image_crop/simple_image_crop.dart';
//
//enum _sheetType { gallery, camera }
//
//class MsyApp extends StatelessWidget {
//  @override
//  Widget build(BuildContext context) {
//    return MaterialApp(
//      debugShowCheckedModeBanner: false,
//      initialRoute: "/",
//      routes: {
//        "crop_page": (context) => SimpleCropRoute(),
//        "/": (context) => MyHomeRoute()
//      },
//    );
//  }
//}
//
//class MyHomeRoute extends StatefulWidget {
//  @override
//  _MyHomeRouteState createState() => new _MyHomeRouteState();
//}
//
//class _MyHomeRouteState extends State<MyHomeRoute> {
//  final cropKey = GlobalKey<ImgCropState>();
//
//  Future getImage(type) async {
//    var image = await ImagePicker.pickImage(
//        source: type == _sheetType.gallery
//            ? ImageSource.gallery
//            : ImageSource.camera);
//    if (image == null) return;
//    Navigator.of(context).pop();
//    Navigator.of(context).pushNamed('crop_page', arguments: {'image': image});
//  }
//
//  void _showActionSheet() {
//    showModalBottomSheet(
//        context: context,
//        builder: (BuildContext context) {
//          return SafeArea(
//            child: Column(
//              mainAxisSize: MainAxisSize.min, // 设置最小的弹出
//              children: <Widget>[
//                new ListTile(
//                  leading: new Icon(Icons.photo_camera),
//                  title: new Text("相机拍照"),
//                  onTap: () async {
//                    getImage(_sheetType.camera);
//                  },
//                ),
//                new ListTile(
//                  leading: new Icon(Icons.photo_library),
//                  title: new Text("相册选择"),
//                  onTap: () async {
//                    getImage(_sheetType.gallery);
//                  },
//                ),
//              ],
//            ),
//          );
//        });
//  }
//
//  @override
//  Widget build(BuildContext context) {
//    return Scaffold(
//      appBar: AppBar(
//        title: Text('select image'),
//      ),
//      floatingActionButton: FloatingActionButton(
//        onPressed: _showActionSheet,
//        tooltip: 'Increment',
//        child: Icon(Icons.add),
//      ),
//    );
//  }
//}
//
//class SimpleCropRoute extends StatefulWidget {
//  @override
//  _SimpleCropRouteState createState() => _SimpleCropRouteState();
//}
//
//class _SimpleCropRouteState extends State<SimpleCropRoute> {
//  final cropKey = GlobalKey<ImgCropState>();
//
//  Future<Null> showImage(BuildContext context, File file) async {
//    new FileImage(file)
//        .resolve(new ImageConfiguration())
//        .addListener(ImageStreamListener((ImageInfo info, bool _) {
//      print('-------------------------------------------$info');
//    }));
//    return showDialog<Null>(
//        context: context,
//        builder: (BuildContext context) {
//          return AlertDialog(
//              title: Text(
//                'Current screenshot：',
//                style: TextStyle(
//                    fontFamily: 'Roboto',
//                    fontWeight: FontWeight.w300,
//                    color: Theme.of(context).primaryColor,
//                    letterSpacing: 1.1),
//              ),
//              content: Image.file(file));
//        });
//  }
//
//  @override
//  Widget build(BuildContext context) {
//    final Map args = ModalRoute.of(context).settings.arguments;
//    return Scaffold(
//        appBar: AppBar(
//          elevation: 0,
//          title: Text(
//            'Zoom and Crop',
//            style: TextStyle(color: Colors.black),
//          ),
//          backgroundColor: Colors.white,
//          leading: new IconButton(
//            icon:
//                new Icon(Icons.navigate_before, color: Colors.black, size: 40),
//            onPressed: () => Navigator.of(context).pop(),
//          ),
//        ),
//        body: Container(
////          padding: EdgeInsets.all(30),
//          height: 400,
//
//          child: ImgCrop(
//            chipShape: 'circle',
//            key: cropKey,
////            chipRadius: 500,
//            // chipShape: 'rect',
////            maximumScale: 3,
//            image: FileImage(args['image']),
//          ),
//        ),
//        floatingActionButton: FloatingActionButton(
//          onPressed: () async {
//            final crop = cropKey.currentState;
//            final croppedFile =
//                await crop.cropCompleted(args['image'], pictureQuality: 600);
//            showImage(context, croppedFile);
//          },
//          tooltip: 'Increment',
//          child: Text('Crop'),
//        ));
//  }
//}
