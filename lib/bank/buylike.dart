import 'dart:async';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/bank/add_banking/payment_buy.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:likewallet/model/bank/exchange_fiat.dart';
import 'package:likewallet/screen_util.dart';
import 'dart:ui';
import 'package:flutter/rendering.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:likewallet/libraryman/open_web_notitle.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:likewallet/libraryman//ethcontract.dart';
import 'package:likewallet/middleware/get_mnemonic.dart';
import 'package:likewallet/middleware/check_auth.dart';
import 'package:intl/intl.dart';
import 'package:likewallet/libraryman/address_service.dart';
import 'package:likewallet/libraryman/configuration_service.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
//use for check active API before use withdraw
import 'package:likewallet/libraryman/checkAPI.dart';
import 'package:likewallet/libraryman/setFormat.dart';
import 'package:likewallet/libraryman/serviceHTTP.dart';
import 'package:intl/intl.dart' as formatIntl;
import 'package:likewallet/quickpay/favorites/list.dart';
import 'package:likewallet/libraryman/keyboard_done_widget.dart';
import 'dart:io' show File, Platform;

import '../animationPage.dart';

/* ----------------| RECEIVE |--------------*/
class BuyLike extends StatefulWidget {
  BuyLike({
    required this.selectPage,
    required this.source,
    required this.shopID,
    required this.scanActive,
    required this.address,
    required this.contract,
    required this.abi,
    required this.callFunction});
  final String source;
  final selectPage;
  final shopID;
  final scanActive;
  final String contract;
  final String address;
  final String abi;
  final String callFunction;
  @override
  State<StatefulWidget> createState() {
    return _BuyLike(
        selectPage: selectPage,
        source: source,
        shopID: shopID,
        scanActive: scanActive,
        address: address,
        contract: contract,
        abi: abi,
        callFunction: callFunction);
  }
}

class _BuyLike extends State<BuyLike> {
  final TextEditingController controller = new TextEditingController();
//  TextEditingController amountSend = TextEditingController();
  TextEditingController textCash = TextEditingController();
  TextEditingController textAccountNumber = TextEditingController();

  _BuyLike({
    required this.selectPage,
    required this.source,
    required this.shopID,
    required this.scanActive,
    required this.address,
    required this.contract,
    required this.abi,
    required this.callFunction});

  final selectPage;
  final String source;
  final shopID;
  final scanActive;
  final String address;
  final String contract;
  final String abi;
  final String callFunction;

  bool showfavorite = false;

  final fireStore = FirebaseFirestore.instance;
  double price = 0;
  final f = new formatIntl.NumberFormat("###,###");
  final formatCurrency = new NumberFormat.decimalPattern();

  StreamController<String> streamBalance = StreamController<String>();
  StreamController<String> streamBalanceLocked = StreamController<String>();
  StreamController<String> streamAvailable = StreamController<String>();
  StreamController<double> streamBalanceLock = StreamController<double>();

  GlobalKey<ScaffoldState> _scaffoldKey = new GlobalKey();
  GlobalKey globalKey = new GlobalKey();

  double rate = 100.00;
  double amount = 0.00;
  String accountNumber = '';
  String currency = 'THB';
  double fee = 0.00;

  int keyword = 1;
  String barcode = "";
  String symbol_1 = 'LIKE';
  String symbol_2 = 'LIKE';
  String balance = 'Loading..';
  double balanceLIKE = 0.00;
  String locked_balance = 'Loading..';
  double rateCurrency = 0.0;
  double balanceLIKELock = 0.00;
  String available = 'Loading..';
  String bank = '';
  late String pketh;
  late BaseETH eth;
  late GetMnemonic seed;
  late String mnemonic;
  late CheckAuth Logon;
  late IAddressService addressService;
  late IConfigurationService configETH;
  late BaseAuth FirebaseAuth;
  bool login = false;
  late CheckAPI checkAPI;
  late SetFormat setFormat;
  TextEditingController addressText = TextEditingController();
  final TextEditingController nameFavorite = TextEditingController();
  final TextEditingController addressFavorite = TextEditingController();
  late String photoFavorite;
  late File? _imageFavorite;
  bool _autoValidate = false;
  final formKey = GlobalKey<FormState>();
  bool delete = false;

  int tabMenu = 0;
  var crossFadeView = CrossFadeState.showFirst;
  bool _saving = false;
  late String toAddress;
  late AbstractServiceHTTP APIHttp;
  var _opacity = 1.0;
  late OverlayEntry? overlayEntry;
  String _dataString = "";
  //ร้านค้า
  List<list> _list = [];
  List group = [];
  List<list> search = [];
  late String uid;
  KeyboardVisibilityController keyboardVisibilityController = KeyboardVisibilityController();
  //kyc session
  bool kycStatus = false;
  FocusNode amountFocusNode = new FocusNode();
  FocusNode amountBuyFocusNode = new FocusNode();
  FocusNode amountCashFocusNode = new FocusNode();
  final formatBank = new NumberFormat("###-###-####");

  _changePrice(String value) {
    setState(() => price = double.parse(value) / 100);
  }

  void showColoredToast(msg) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_LONG,
        backgroundColor: Colors.red,
        textColor: Colors.white);
  }

  //done button zone
  showOverlay(BuildContext context) {
    if (overlayEntry != null) return;
    OverlayState? overlayState = Overlay.of(context);
    overlayEntry = OverlayEntry(builder: (context) {
      return Positioned(
          bottom: MediaQuery.of(context).viewInsets.bottom,
          right: 0.0,
          left: 0.0,
          child: InputDoneView());
    });

    overlayState!.insert(overlayEntry!);
  }

  removeOverlay() {
    if (overlayEntry != null) {
      overlayEntry!.remove();
      overlayEntry = null;
    }
  }

  Future<bool> getShop() async {
    //เรียก API ร้านค้า
    var data = await APIHttp.getQuickpayShop();
    setState(() {
      _list = data[0];
      search = data[0];
      group = data[1];
    });
    return true;
  }

  void pasteAddress() async {
    ClipboardData? data = await Clipboard.getData('text/plain');
    addressText.text = data!.text!;
    toAddress = data.text!;
  }

  Future generateETH(seed, pref) async {
    setState(() {
      _dataString = configETH.getAddress();
    });
    eth.getBalance(address: _dataString).then((avaiBalance) {
      available = 'LIKE ' + f.format(avaiBalance).toString();
      setState(() {
        balanceLIKE = avaiBalance.toDouble();
      });
      streamAvailable.sink.add(available);

      eth.getBalanceLock(address: _dataString).then((balanceLock) {
        print(balanceLock);
        balance = 'LIKE ' + f.format(avaiBalance + balanceLock);
        balanceLIKELock = balanceLock.toDouble();
        locked_balance = 'LIKE ' + f.format(balanceLock).toString();
        streamBalance.sink.add(balance);
        streamBalanceLock.sink.add(balanceLIKELock);
        streamBalanceLocked.sink.add(locked_balance);
      });
    });
  }

  _changeText(_symbol) {
    final exchangeFiat = fireStore
        .collection('exchangeFiat')
        .withConverter<ExchangeFiat>(
          fromFirestore: (snapshot, _) => ExchangeFiat.fromJson(snapshot.data()!),
          toFirestore: (model, _) => model.toJson(),
        );
        exchangeFiat.doc(_symbol == 'none' ? "THB-LIKE" : "THB-" + _symbol)
        .get()
        .then((DocumentSnapshot<ExchangeFiat> ds) {
      print(ds.data()!.rate);
      if (ds.data()!.to == 'LIKE') {
        if (!mounted) return;
        setState(() {
          rateCurrency = ds.data()!.rate.toDouble();
        });
      } else {
        if (!mounted) return;
        setState(() {
          rateCurrency = 1 / ds.data()!.rate;
        });
      }
    });

    if (!mounted) return;
    setState(() {
      print(_symbol);
      symbol_1 = _symbol;
    });
  }

  changeMenu(_number) async {
    setState(() {
      tabMenu = _number;
    });
  }

  @override
  void dispose() {
    // TODO: implement dispose
//    streamBalance.close();
//    streamBalanceLocked.close();
//    streamBalanceLock.close();

    streamAvailable.close();
    amountFocusNode.dispose();
    amountBuyFocusNode.dispose();
    amountCashFocusNode.dispose();
    super.dispose();
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    APIHttp = new ServiceHTTP();
//    amountSend.addListener(changeAmount);
    textCash.addListener(updateRecived);
    textAccountNumber.addListener(updateAccountNumber);
    Logon = new CurCheckAuth();
    seed = new MnemonicRetrieve();
    eth = new EthContract();
    FirebaseAuth = Auth();
    checkAPI = APIChecker();
    setFormat = SetFormatString();
    if (Platform.isIOS) {
      amountFocusNode.addListener(() {
        bool hasFocus = amountFocusNode.hasFocus;
        if (hasFocus)
          showOverlay(context);
        else
          removeOverlay();
      });

      amountBuyFocusNode.addListener(() {
        bool hasFocus = amountBuyFocusNode.hasFocus;
        if (hasFocus)
          showOverlay(context);
        else
          removeOverlay();
      });
      amountCashFocusNode.addListener(() {
        bool hasFocus = amountCashFocusNode.hasFocus;
        if (hasFocus)
          showOverlay(context);
        else
          removeOverlay();
      });
      keyboardVisibilityController.onChange.listen((bool visible) {
        removeOverlay();
      });
    }

    setInit();
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  }

  void setPay() async {
    getShop().then((result) {
      // print(result);
      if (result) {
        print('result getShop' + result.toString());
        setState(() {
          toAddress = _list[shopID].address.toString().trim().toString();
          addressText.text = _list[shopID].title.toString();
        });
      }
    });
  }

  setInit() async {
    print('open banking');

    SharedPreferences pref = await SharedPreferences.getInstance();
    if (!mounted) return;
    setState(() {
      kycStatus = pref.getBool('kycActive') ?? false;
    });
    currency = pref.getString('currency') ?? 'THB';

    _changeText(currency);
    APIHttp.getCurrentFee(currency: currency).then((getFee) {
      setState(() {
        fee = getFee;
      });
      print(fee);
    });

    configETH = new ConfigurationService(pref);
    addressService = new AddressService(configETH);

    login = pref.getBool('login') ?? false;
    if (login) {
      mnemonic = await configETH.getMnemonic();
      generateETH(mnemonic, pref);
    }
  }

  changeAmount() {
//    print(amountSend.text);
  }

  updateRecived() {
    if (double.parse(textCash.text.replaceAll(",", "")) >= balanceLIKE) {
      setState(() {
        amount = double.parse((balanceLIKE / rate - fee).toStringAsFixed(2));
      });
    } else {
      print('here');
      setState(() {
        amount = double.parse(
            (double.parse(textCash.text.replaceAll(",", "")) / rate - fee)
                .toStringAsFixed(2));
      });
    }
  }

  updateAccountNumber() {
    setState(() {
      accountNumber = textAccountNumber.text;
    });
  }

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return SingleChildScrollView(
      child: GestureDetector(
          onTap: () {
            FocusScopeNode currentFocus = FocusScope.of(context);
          },
          child: Stack(
            alignment: Alignment.topLeft,
            clipBehavior: Clip.none,
            fit: StackFit.passthrough,
            children: <Widget>[
              if (tabMenu == 1) _buy_confirm(context),
              if (tabMenu == 2) _buy_payment(context),
              if (tabMenu == 0) _buy_like(context)
            ],
          )),
    );
  }

  Widget _buy_like(context) {
    return Align(
      alignment: Alignment.center,
      child: new Padding(
          padding: EdgeInsets.only(top: mediaQuery(context, 'height', 100)),
          // พื้นหลัง
          child: new Container(
            height: mediaQuery(context, 'height', 1500),
            width: MediaQuery.of(context).size.width,
            decoration: BoxDecoration(
              color: Color(0xffF5F5F5),
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                stops: [0.0, 0.2, 0.5],
                colors: [
                  // Colors are easy thanks to Flutter's Colors class.
                  Colors.white,
                  Colors.white,
                  LikeWalletAppTheme.white1
                ],
              ),
            ),
            child: new Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                // จำนวนที่ต้องการเเลก
                _buy_like_title(),
                // input จำนวนที่ต้องการเเลก
                new Row(children: <Widget>[
                  _buy_like_inputAmount(),
                  _buy_like_symbol()
                ]),
                //เว้ยเเถว
                SizedBox(
                  height: mediaQuery(context, 'height', 200),
                ),
                // จำนวนที่จะได้รับ
                _buy_like_amount(),
                //ค่าธรรมเนียม fee
                _buy_like_fee(),
                SizedBox(
                  //old 0.165
                  height: MediaQuery.of(context).size.height * 0.09,
                ),
                //วิธีเลือกซื้อ
                _howTo(),
                //ปุ่มเเลก
                _buy_like_button()
              ],
            ),
          )),
    );
  }

  Widget _buy_like_title() {
    return new Container(
      padding: EdgeInsets.only(
        left: mediaQuery(context, 'width', 75),
      ),
      height: mediaQuery(context, 'height', 80),
      child: new Text(
          AppLocalizations.of(context)!.translate('bankingbuy_amount'),
          style: LikeWalletAppTheme.textStyle(context, 35,
              LikeWalletAppTheme.gray1, FontWeight.normal, 'font1')),
    );
  }

  Widget _buy_like_inputAmount() {
    return Column(children: <Widget>[
      new Row(
        children: <Widget>[
          Padding(
              padding: EdgeInsets.only(
            left: mediaQuery(context, 'width', 75),
          )),
          new Container(
            decoration: BoxDecoration(
              border: Border.all(width: 0.5, color: LikeWalletAppTheme.gray),
              borderRadius: new BorderRadius.circular(10.0),
              color: LikeWalletAppTheme.white,
              boxShadow: [
                BoxShadow(
                  spreadRadius: 0,
                  blurRadius: 2,
                  color: LikeWalletAppTheme.gray1.withOpacity(0.5),
                  offset: Offset(
                    0.0,
                    1.0,
                  ),
                ),
              ],
            ),
            alignment: Alignment.centerLeft,
            height: mediaQuery(context, 'height', 161),
            width: mediaQuery(context, 'width', 735.18),
            child: TextFormField(
              textAlign: TextAlign.left,
              style: TextStyle(
                  fontSize: mediaQuery(context, 'height', 83),
                  fontFamily: AppLocalizations.of(context)!.translate('font2'),
                  color: LikeWalletAppTheme.black),
              keyboardType: TextInputType.number,
              controller: controller,
              focusNode: amountBuyFocusNode,
              decoration: InputDecoration(
                focusedBorder: InputBorder.none,
                enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                        width: 0.1,
                        color: LikeWalletAppTheme.gray1.withOpacity(0.0))),
                border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10.0)),
                hintText: '0',
                hintStyle: TextStyle(
                    fontSize: mediaQuery(context, 'height', 83),
                    fontFamily: AppLocalizations.of(context)!.translate('font2'),
                    color: LikeWalletAppTheme.gray1),
                contentPadding: EdgeInsets.fromLTRB(20.0, 10.0, 20.0, 10.0),
              ),
              onFieldSubmitted: _changePrice,
            ),
          ),
        ],
      ),
      new Container(
        padding: EdgeInsets.only(
          top: mediaQuery(context, 'height', 20),
        ),
//                          alignment: Alignment.centerLeft,
        child: Text(
          AppLocalizations.of(context)!.translate('bankingbuy_about'),
          style: TextStyle(
              color: LikeWalletAppTheme.gray1,
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              fontWeight: FontWeight.normal,
              fontSize: MediaQuery.of(context).size.height *
                  Screen_util("height", 36)),
        ),
      ),
    ]);
  }

  Widget _buy_like_symbol() {
    return new Container(
      alignment: Alignment.topCenter,
      height: mediaQuery(context, 'height', 161),
      width: mediaQuery(context, 'width', 200),
      child: new Text(
        'LIKE',
        style: TextStyle(
          color: LikeWalletAppTheme.black,
          fontFamily: AppLocalizations.of(context)!.translate('font1'),
          fontWeight: FontWeight.normal,
          fontSize: mediaQuery(context, 'height', 71),
        ),
      ),
    );
  }

  Widget _buy_like_amount() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        new Container(
          padding: EdgeInsets.only(
            left: mediaQuery(context, 'width', 75),
          ),
          alignment: Alignment.bottomLeft,
          height: mediaQuery(context, 'height', 105),
          width: mediaQuery(context, 'width', 300),
          child: Text(
            AppLocalizations.of(context)!.translate('bankingbuy_get'),
            style: TextStyle(
              color: LikeWalletAppTheme.gray1.withOpacity(1),
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              fontWeight: FontWeight.normal,
              fontSize: mediaQuery(context, 'height', 36),
            ),
          ),
        ),
        new Container(
          padding: EdgeInsets.only(
            right: mediaQuery(context, 'height', 50),
          ),
          alignment: Alignment.bottomRight,
          height: mediaQuery(context, 'height', 105),
          width: mediaQuery(context, 'width', 590),
          child: Text(
            '$price',
            style: TextStyle(
              color: LikeWalletAppTheme.black,
              fontFamily: AppLocalizations.of(context)!.translate('font2'),
              fontWeight: FontWeight.bold,
              fontSize: mediaQuery(context, 'height', 84),
            ),
          ),
        ),
        new Container(
          alignment: Alignment.bottomLeft,
          height: mediaQuery(context, 'height', 93),
          width: mediaQuery(context, 'width', 100),
          child: Text(
            AppLocalizations.of(context)!.translate('bankingbuy_likepoint'),
            style: TextStyle(
              color: LikeWalletAppTheme.gray1.withOpacity(1),
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              fontWeight: FontWeight.normal,
              fontSize: mediaQuery(context, 'height', 36),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buy_like_fee() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        new Container(
          padding: EdgeInsets.only(
            left: mediaQuery(context, 'width', 75),
          ),
          alignment: Alignment.centerLeft,
          height: mediaQuery(context, 'height', 105),
          width: mediaQuery(context, 'width', 540),
          child: Text(
            AppLocalizations.of(context)!.translate('bankingbuy_fee'),
            style: TextStyle(
              color: LikeWalletAppTheme.gray1.withOpacity(1),
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              fontWeight: FontWeight.normal,
              fontSize: mediaQuery(context, 'height', 36),
            ),
          ),
        ),
        new Container(
          alignment: Alignment.bottomRight,
          height: mediaQuery(context, 'height', 105),
          width: mediaQuery(context, 'width', 390),
          child: Text(
            '0',
            style: TextStyle(
              color: LikeWalletAppTheme.black,
              fontFamily: AppLocalizations.of(context)!.translate('font2'),
              fontWeight: FontWeight.w200,
              fontSize: mediaQuery(context, 'height', 84),
            ),
          ),
        ),
        new Container(
          alignment: Alignment.bottomLeft,
          height: mediaQuery(context, 'height', 93),
          width: mediaQuery(context, 'width', 100),
          child: Text(
            AppLocalizations.of(context)!.translate('bankingbuy_likepoint'),
            style: TextStyle(
              color: LikeWalletAppTheme.gray1.withOpacity(0.5),
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              fontWeight: FontWeight.normal,
              fontSize: mediaQuery(context, 'height', 36),
            ),
          ),
        ),
      ],
    );
  }

  Widget _howTo() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: <Widget>[
        ElevatedButton.icon(
            onPressed: () {
              Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => WebOpenNoTitle(
                          title: AppLocalizations.of(context)!
                            .translate('how_to_buy_bank_transfer'),
                          url:
                              "https://sites.google.com/prachakij.com/likewallet-buy/home")));
            },
            icon: Icon(
              Icons.help_outline,
              color: Colors.grey,
            ),
            label: Text(
              AppLocalizations.of(context)!
                            .translate('how_to_buy_bank_transfer'),
              style: TextStyle(color: Colors.grey),
            )),
      ],
    );
  }

  Widget _buy_like_button() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: <Widget>[
        Container(
            width: mediaQuery(context, 'width', 930) / 2,
            height: mediaQuery(context, 'height', 133),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: <Widget>[
                Image.asset(
                  LikeWalletImage.icon_button_cancel_white,
                  height: mediaQuery(context, 'height', 133),
                ),
                Text(
                  AppLocalizations.of(context)!.translate('bankingbuy_back'),
                  style: TextStyle(
                      fontFamily: 'Noto Sans',
                      fontSize: mediaQuery(context, 'height', 36),
                      color: LikeWalletAppTheme.gray.withOpacity(0.8)),
                  textAlign: TextAlign.right,
                )
              ],
            )),
        GestureDetector(
          onTap: () {
            changeMenu(1);
          },
          child: Container(
              width: mediaQuery(context, 'width', 930) / 2,
              height: mediaQuery(context, 'height', 133),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: <Widget>[
                  Text(
                    AppLocalizations.of(context)!.translate('bankingbuy_button'),
                    style: TextStyle(
                        fontFamily: 'Noto Sans',
                        fontSize: mediaQuery(context, 'height', 36),
                        color: LikeWalletAppTheme.gray.withOpacity(0.8)),
                    textAlign: TextAlign.right,
                  ),
                  Image.asset(
                    LikeWalletImage.icon_button_next_black,
                    height: mediaQuery(context, 'height', 133),
                  ),
                ],
              )),
        ),
//            child: new FlatButton(
//                shape: new RoundedRectangleBorder(
//                  borderRadius: new BorderRadius.circular(8.0),
//                ),
//                disabledColor: Color(0xff141322).withOpacity(1),
//                color: Color(0xff141322).withOpacity(1),
//                onPressed: () => {
//                      changeMenu(1),
//                    },
//                child: new Text(
//                  AppLocalizations.of(context)!.translate('bankingbuy_button'),
//                  style: TextStyle(
//                      color: LikeWalletAppTheme.lemon,
//                      fontFamily:
//                          AppLocalizations.of(context)!.translate('font1Light'),
//                      fontSize: mediaQuery(context, 'height', 59),
//                      fontWeight: FontWeight.w100),
//                )),
      ],
    );
  }

  Widget _buy_confirm(context) {
    return new Stack(
      children: <Widget>[
        //พื้นหลัง
        Align(
          alignment: Alignment.center,
          child: new Padding(
              padding: EdgeInsets.only(top: mediaQuery(context, 'height', 50)),
              child: new Container(
//                height: MediaQuery.of(context).size.height / 1.3,
                width: MediaQuery.of(context).size.width,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    stops: [1],
                    colors: [
                      // Colors are easy thanks to Flutter's Colors class.
                      Colors.white
                    ],
                  ),
                ),
                child: new Column(
                  children: <Widget>[
                    SizedBox(
                      height: MediaQuery.of(context).size.height * 0.1,
                    ),
                    new Container(
                      height: MediaQuery.of(context).size.height * 0.2,
                      width: MediaQuery.of(context).size.width * 0.9,
                      decoration: BoxDecoration(
                        border:
                            Border.all(width: 0.3, color: Color(0xff707071)),
                        borderRadius: BorderRadius.all(Radius.circular(
                                5.0) //         <--- border radius here
                            ),
                      ),
                      child: new Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: <Widget>[
                          //หัวข้อยืนยันการเเรก
                          _buy_confirm_title(),
                          //ยอดซื้อ
                          _buy_confirm_like(),
                          //เว้นเเถว
                          SizedBox(
                            height: MediaQuery.of(context).size.height * 0.02,
                          ),
                          //ยอดรวม
                          _buy_confirm_total(),
                          _buy_confirm_total_line2()
                        ],
                      ),
                    ),
                    SizedBox(
                      height: MediaQuery.of(context).size.height * 0.23,
                    ),
                    //ปุ่มต่อไป
                    _buy_confirm_button()
                  ],
                ),
              )),
        ),
        //ปุุ่มกลับ
        Positioned(
            top: MediaQuery.of(context).size.height * Screen_util("height", 50),
            child: Row(
              children: <Widget>[
                new GestureDetector(
                  onTap: (() => {changeMenu(0)}),
                  child: Container(
                      alignment: Alignment.centerRight,
                      height: MediaQuery.of(context).size.height * 0.06,
                      width: MediaQuery.of(context).size.width * 0.1,
                      child: Icon(
                        Icons.arrow_back_ios,
                        size: MediaQuery.of(context).size.height *
                            Screen_util("height'", 27),
                        color: Color(0xff6C6B6D).withOpacity(0.5),
                      )),
                ),
              ],
            )),
        //หัวข้อ
        Positioned(
          top: MediaQuery.of(context).size.height * Screen_util("height", 50),
          child: new Container(
            alignment: Alignment.center,
            padding: EdgeInsets.only(
                left: MediaQuery.of(context).size.width * 0.19567),
            height: MediaQuery.of(context).size.height * 0.06,
            width: MediaQuery.of(context).size.width * 0.8,
            child: new Text(
              AppLocalizations.of(context)!.translate('bankingbuy_order'),
              style: TextStyle(
                  color: LikeWalletAppTheme.gray1.withOpacity(0.5),
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  fontWeight: FontWeight.bold,
                  fontSize: mediaQuery(context, 'height', 45)),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buy_confirm_title() {
    return new Container(
      padding: EdgeInsets.only(left: MediaQuery.of(context).size.width * 0.07),
      alignment: Alignment.bottomLeft,
      height: MediaQuery.of(context).size.height * 0.05,
      width: MediaQuery.of(context).size.width * 0.25,
      child: Text(
        AppLocalizations.of(context)!.translate('bankingbuy_buy'),
        style: TextStyle(
            color: Color(0xff6C6B6D).withOpacity(0.5),
            fontFamily: AppLocalizations.of(context)!.translate('font1'),
            fontWeight: FontWeight.normal,
            fontSize:
                MediaQuery.of(context).size.height * Screen_util("height", 36)),
      ),
    );
  }

  Widget _buy_confirm_like() {
    return Row(
      children: <Widget>[
        new Container(
          padding:
              EdgeInsets.only(left: MediaQuery.of(context).size.width * 0.07),
          alignment: Alignment.centerLeft,
          height: MediaQuery.of(context).size.height * 0.03,
          width: MediaQuery.of(context).size.width * 0.3,
          child: Text(
            formatCurrency.format(price),
            style: TextStyle(
                color: Color(0xff141322).withOpacity(1),
                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                fontWeight: FontWeight.normal,
                fontSize: MediaQuery.of(context).size.height *
                    Screen_util("height", 45)),
          ),
        ),
        new Container(
          padding:
              EdgeInsets.only(left: MediaQuery.of(context).size.width * 0.07),
          alignment: Alignment.centerLeft,
          height: MediaQuery.of(context).size.height * 0.03,
          width: MediaQuery.of(context).size.width * 0.4,
          child: Text(
            AppLocalizations.of(context)!.translate('bankingbuy_likepoint'),
            style: TextStyle(
                color: Color(0xff141322).withOpacity(1),
                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                fontWeight: FontWeight.normal,
                fontSize: MediaQuery.of(context).size.height *
                    Screen_util("height", 36)),
          ),
        ),
      ],
    );
  }

  Widget _buy_confirm_total() {
    return new Container(
      padding: EdgeInsets.only(left: MediaQuery.of(context).size.width * 0.07),
      alignment: Alignment.bottomLeft,
      height: MediaQuery.of(context).size.height * 0.03,
      width: MediaQuery.of(context).size.width * 0.25,
      child: Text(
        AppLocalizations.of(context)!.translate('bankingbuy_total'),
        style: TextStyle(
            color: Color(0xff6C6B6D).withOpacity(0.5),
            fontFamily: AppLocalizations.of(context)!.translate('font1'),
            fontWeight: FontWeight.normal,
            fontSize:
                MediaQuery.of(context).size.height * Screen_util("height", 36)),
      ),
    );
  }

  Widget _buy_confirm_total_line2() {
    return Row(
      children: <Widget>[
        new Container(
          padding:
              EdgeInsets.only(left: MediaQuery.of(context).size.width * 0.07),
          alignment: Alignment.centerLeft,
          height: MediaQuery.of(context).size.height * 0.03,
          width: MediaQuery.of(context).size.width * 0.3,
          child: Text(
            formatCurrency.format(price / rate),
            style: TextStyle(
                color: Color(0xff141322).withOpacity(1),
                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                fontWeight: FontWeight.normal,
                fontSize: MediaQuery.of(context).size.height *
                    Screen_util("height", 45)),
          ),
        ),
        new Container(
          padding:
              EdgeInsets.only(left: MediaQuery.of(context).size.width * 0.07),
          alignment: Alignment.centerLeft,
          height: MediaQuery.of(context).size.height * 0.03,
          width: MediaQuery.of(context).size.width * 0.4,
          child: Text(
            AppLocalizations.of(context)!.translate('bankingbuy_symbol'),
            style: TextStyle(
                color: Color(0xff141322).withOpacity(1),
                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                fontWeight: FontWeight.normal,
                fontSize: MediaQuery.of(context).size.height *
                    Screen_util("height", 36)),
          ),
        ),
      ],
    );
  }

  Widget _buy_confirm_button() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: <Widget>[
        new Container(
          alignment: Alignment.center,
          child: ButtonTheme(
            minWidth:
                MediaQuery.of(context).size.width * Screen_util("width", 930),
            height:
                MediaQuery.of(context).size.height * Screen_util("height", 132),
            child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  shape: RoundedRectangleBorder(
                    borderRadius:  BorderRadius.circular(8.0),
                  ),
                  disabledBackgroundColor: Color(0xff141322).withOpacity(1),
                  backgroundColor: Color(0xff141322).withOpacity(1),
                ),
                onPressed: () => {
                  changeMenu(2),
                },
                child: new Text(
                  AppLocalizations.of(context)!.translate('bankingbuy_button'),
                  style: TextStyle(
                      color: Color(0xffB4E60D),
                      fontFamily:
                      AppLocalizations.of(context)!.translate('font1'),
                      fontSize: MediaQuery.of(context).size.height *
                          Screen_util("height", 52),
                      fontWeight: FontWeight.normal),
                )),
          ),
        ),
      ],
    );
  }

  Widget _buy_payment(context) {
    return new Stack(
      alignment: Alignment.topLeft,
      clipBehavior: Clip.none,
      fit: StackFit.passthrough,
      children: <Widget>[
        // พื้นหลังขาว
        Align(
          alignment: Alignment.center,
          child: new Padding(
              padding: EdgeInsets.only(
                top: MediaQuery.of(context).size.height *
                    Screen_util("height", 50),
              ),
              child: new Container(
//                height: MediaQuery.of(context).size.height / 1.3,
                width: MediaQuery.of(context).size.width,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    stops: [1],
                    colors: [
                      // Colors are easy thanks to Flutter's Colors class.
                      Colors.white
                    ],
                  ),
                ),
                child: new Column(
                  children: <Widget>[
                    SizedBox(
                      height: MediaQuery.of(context).size.height * 0.1,
                    ),
                    //รายการซื้อด้วยการโอน
                    _buy_payment_transfer(),
                    SizedBox(
                      height: MediaQuery.of(context).size.height * 0.03,
                    ),
                  ],
                ),
              )),
        ),
        //ปุ่มกลับ
        Positioned(
            top: MediaQuery.of(context).size.height * Screen_util("height", 50),
            child: Row(
              children: <Widget>[
                new GestureDetector(
                  onTap: (() => {changeMenu(1)}),
                  child: Container(
                      alignment: Alignment.centerRight,
                      height: MediaQuery.of(context).size.height * 0.06,
                      width: MediaQuery.of(context).size.width * 0.1,
                      child: Icon(
                        Icons.arrow_back_ios,
                        size: MediaQuery.of(context).size.height *
                            Screen_util("height'", 27),
                        color: Color(0xff6C6B6D).withOpacity(0.5),
                      )),
                ),
              ],
            )),
        //หัวข้อ
        Positioned(
          top: MediaQuery.of(context).size.height * Screen_util("height", 50),
          child: new Container(
            alignment: Alignment.center,
            padding: EdgeInsets.only(
                left: MediaQuery.of(context).size.width * 0.19567),
            height: MediaQuery.of(context).size.height * 0.06,
            width: MediaQuery.of(context).size.width * 0.8,
            child: new Text(
              AppLocalizations.of(context)!.translate('buy_payment_title'),
              style: TextStyle(
                  color: LikeWalletAppTheme.gray1.withOpacity(0.5),
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  fontWeight: FontWeight.bold,
                  fontSize: mediaQuery(context, 'height', 45)),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buy_payment_transfer() {
    return GestureDetector(
      onTap: (() => {
            Navigator.push(context,
                EnterExitRoute(enterPage: PaymentBuy(amount: 0, fee: 0, rate: 0,)))
          }),
      child: Container(
          alignment: Alignment.center,
          height: MediaQuery.of(context).size.height * 0.1,
          width: MediaQuery.of(context).size.width * 0.9,
          decoration: BoxDecoration(
            border: Border.all(
                width: 0.3, color: Color(0xff707071).withOpacity(0.5)),
            borderRadius: BorderRadius.all(
                Radius.circular(10.0) //         <--- border radius here
                ),
            boxShadow: [
              BoxShadow(
                blurRadius: 2.0,
                color: Colors.black.withOpacity(.2),
                offset: Offset(0.0, 5.0),
              ),
            ],
            color: Colors.white,
          ),
          child: Row(
            children: <Widget>[
              Container(
                height: MediaQuery.of(context).size.height * 0.035,
                width: MediaQuery.of(context).size.width * 0.1,
                alignment: Alignment.centerRight,
                padding:
                    EdgeInsets.all(MediaQuery.of(context).size.height * 0.03),
                margin: EdgeInsets.only(
                    left: MediaQuery.of(context).size.width * 0.04),
                decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Color(0xffB4E60D),
                      width: 4.0,
                    )),
              ),
              //ช่องทางการจ่ายเงิน
              Container(
                alignment: Alignment.centerLeft,
                padding:
                    EdgeInsets.all(MediaQuery.of(context).size.height * 0.02),
                child: new Text(
                  AppLocalizations.of(context)!
                            .translate('buy_payment_transfer_banking'),
                  style: TextStyle(
                      color: LikeWalletAppTheme.black,
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      fontWeight: FontWeight.bold,
                      fontSize: 18.0),
                ),
              ),
              Image.asset(
                LikeWalletImage.icon_transfer,
                scale: 3,
              ),
            ],
          )),
    );
  }
}
