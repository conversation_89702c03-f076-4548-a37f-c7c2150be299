import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:intl/intl.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/bank/banking.dart';
import 'package:likewallet/app_config.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:likewallet/libraryman/configuration_service.dart';
import 'package:likewallet/middleware/getLanguage.dart';
import 'package:likewallet/setmodel/withdraw_model.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

class ContactDetails extends StatefulWidget {
  ContactDetails({
    required this.name,
    required this.phone,
    required this.address,
    this.color,
  });
  final String name;
  final String phone;
  final String address;
  final Color? color;

  @override
  _ContactDetailsState createState() => _ContactDetailsState();
}

class _ContactDetailsState extends State<ContactDetails> {
  late List<Contact> _contacts;
  int tabSelect = 1;
  AlignmentGeometry _alignment = Alignment.centerLeft;
  late String languageCode;
  String addressETH = '';
  final formatNum = new NumberFormat("###,###.##");
  late List<Withdraw_history> listWithdraw;
  ////////////////////////////////////////////////////////
  late OnLanguage language;
  late IConfigurationService configETH;
  late BaseAuth FirebaseAuth;
  ////////////////////////////////////////////////////////

  @override
  void initState() {
    super.initState();
    language = CallLanguage();
    FirebaseAuth = Auth();
    setInit();
    refreshContacts();
  }

  setInit() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    languageCode = await language.getLanguage();
    configETH = new ConfigurationService(pref);
    addressETH = configETH.getAddress();
    // await getTransactions(addressETH: addressETH);
  }

  Future<List<Withdraw_history>> getTransactions(
      {required String addressETH}) async {
    // var url = Uri.http(env.apiUrl, '/checkPending');
    var url = Uri.http(env.apiUrl, '/getHistoryNew');
    var _token = await FirebaseAuth.getTokenFirebase();
    // print("_token" + _token!);
    print("addressETH" + addressETH.toString());
    var response =
        await http.post(url, body: {'_token': _token, 'address': addressETH});
    var body = json.decode(response.body);
    final body2 = await body["result"].cast<Map<String, dynamic>>();
    // setState(() {
    //   listWithdraw = body2
    //       .map<Withdraw_history>((json) => Withdraw_history.fromJson(json))
    //       .toList();
    // });
    return listWithdraw = [];
  }

  Future<void> refreshContacts() async {
    // Load without thumbnails initially.
    var contacts = (await FlutterContacts.getContacts(
            withThumbnail: false))
        .toList();
//      var contacts = (await ContactsService.getContactsForPhone("8554964652"))
//          .toList();
    setState(() {
      _contacts = contacts;
    });

    // Lazy load thumbnails after rendering initial contacts.
    // for (final contact in contacts) {
    //   ContactsService.getAvatar(contact).then((avatar) {
    //     if (avatar == null) return; // Don't redraw if no change.
    //     if (!mounted) return;
    //     setState(() => contact.avatar = avatar);
    //   });
    // }
  }

  void _changeAlignment() {
    setState(() {
      _alignment = _alignment == Alignment.centerRight
          ? Alignment.centerLeft
          : Alignment.centerRight;
    });
  }


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          SizedBox(height: 133.h),
          backButton(context, Colors.black),
          SizedBox(height: 50.h),
          profile(),
          SizedBox(height: 59.h),
          nameAndPhone(),
          SizedBox(height: 157.5.h),
          _appBar(),
          tabSelect == 1
              ? Expanded(child: Container())
              : SizedBox(height: 50.h),
          tabSelect == 1 ? send() : transactions(),
          SizedBox(height: 175.h),
        ],
      ),
    );
  }

  Widget profile() {
    return Container(
      height: 304.h,
      width: 344.99.w,
      child: Stack(
        children: [
          Container(
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: widget.color,
              borderRadius: BorderRadius.circular(100.0),
            ),
            child: Text(widget.name.substring(0, 1).toString()),
          ),
          Positioned(
            bottom: 0,
            right: 12.w,
            child: Image.asset(
              LikeWalletImage.iconLikeWallet,
              height: 85.h,
              width: 85.h,
            ),
          )
        ],
      ),
    );
  }

  Widget nameAndPhone() {
    return Column(
      children: [
        Text(
          widget.name,
          style: TextStyle(
            fontFamily: AppLocalizations.of(context)!.translate('font1'),
            fontSize: 48.h,
            color: const Color(0xff3c3c43),
            height: 0.875,
          ),
          textHeightBehavior:
              TextHeightBehavior(applyHeightToFirstAscent: false),
          textAlign: TextAlign.center,
        ),
        Text(
          widget.phone,
          style: TextStyle(
            fontFamily: AppLocalizations.of(context)!.translate('font1'),
            fontSize: 39.h,
            color: const Color(0xff3c3c43),
            letterSpacing: 7.800000000000001.w,
            height: 1.0769230769230769,
          ),
          textHeightBehavior:
              TextHeightBehavior(applyHeightToFirstAscent: false),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _appBar() {
    return Align(
      alignment: Alignment.center,
      child: Container(
          width: 902.0.w,
          height: 80.0.h,
          decoration: BoxDecoration(
            color: Color(0xff201F2D).withOpacity(0.08),
            borderRadius: BorderRadius.circular(100.0.h),
            border: Border.all(width: 2.0.sp, color: const Color(0x4dffffff)),
          ),
          child: Stack(
            children: [
              InkWell(
                onTap: () => _changeAlignment(),
                child: Container(
                  height: 120.0,
                  width: 902.0.w,
                  child: AnimatedAlign(
                    alignment: _alignment,
                    curve: Curves.ease,
                    duration: Duration(milliseconds: 300),
                    child: Container(
                      decoration: BoxDecoration(
                        color: Color(0xff474652),
                        borderRadius: BorderRadius.only(
                          topLeft: tabSelect == 1
                              ? Radius.circular(50.0)
                              : Radius.circular(50.0),
                          bottomLeft: tabSelect == 1
                              ? Radius.circular(50.0)
                              : Radius.circular(050.0),
                          bottomRight: tabSelect == 2
                              ? Radius.circular(50.0)
                              : Radius.circular(50.0),
                          topRight: tabSelect == 2
                              ? Radius.circular(50.0)
                              : Radius.circular(50.0),
                        ),
                      ),
                      width: 900.0.w / 2,
                      height: 80.0.h,
                    ),
                  ),
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _tabSelect1(Alignment.centerRight, 'NEWS'),
                  _tabSelect2(Alignment.centerLeft, 'NOTIFICATIONS')
                ],
              ),
            ],
          )),
    );
  }

  Widget _tabSelect1(index, text) {
    return Expanded(
      child: InkWell(
        onTap: () async {
          if (tabSelect == 2) {
            _changeAlignment();
            await Future.delayed(Duration(milliseconds: 150)).then((value) {
              setState(() => tabSelect = 1);
            });
          }
        },
        child: Container(
          alignment: Alignment.center,
          height: double.infinity,
          child: Text(
            text,
            // AppLocalizations.of(context)!.translate(text),
            style: TextStyle(
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              fontSize: 30.h,
              color: _alignment == Alignment.centerLeft
                  ? Color(0xff08e8de)
                  : Colors.black.withOpacity(0.8),
              letterSpacing: 1.5.sp,
              shadows: [
                Shadow(
                  color: const Color(0x29000000),
                  offset: Offset(0, 3.sp),
                  blurRadius: 6.sp,
                )
              ],
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  Widget _tabSelect2(index, text) {
    return Expanded(
      child: InkWell(
        onTap: () async {
          if (tabSelect == 1) {
            _changeAlignment();
            await Future.delayed(Duration(milliseconds: 150))
                .then((value) async {
              setState(() => tabSelect = 2);
              await getTransactions(addressETH: addressETH);
            });
          }
        },
        child: Container(
          alignment: Alignment.center,
          height: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(100.0.h),
          ),
          child: Text(
            text,
            // AppLocalizations.of(context)!.translate(text),
            style: TextStyle(
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              fontSize: 30.h,
              color: _alignment == Alignment.centerRight
                  ? Color(0xff08e8de)
                  : Colors.black.withOpacity(0.8),
              letterSpacing: 1.5.sp,
              shadows: [
                Shadow(
                  color: const Color(0x29000000),
                  offset: Offset(0, 3.sp),
                  blurRadius: 6.sp,
                )
              ],
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  Widget send() {
    return InkWell(
      onTap: () {
        Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => Banking(
                      selectedAddr: 'address',
                      address: widget.address,
                      source: 'contact',
                      titleContact: widget.name,
                      isVending: false,

                      // ignore: null_check_always_fails
                    )));
      },
      child: Container(
        decoration: BoxDecoration(
          color: Color(0xff201F2D),
          borderRadius: BorderRadius.circular(100.0.h),
          border: Border.all(width: 2.0.sp, color: const Color(0x4dffffff)),
        ),
        width: 884.w,
        alignment: Alignment.center,
        height: 132.h,
        child: Text(
          'Send',
          style: TextStyle(
            fontFamily: AppLocalizations.of(context)!.translate('font1'),
            fontSize: 42.h,
            color: LikeWalletAppTheme.bule1,
            letterSpacing: 1.5.sp,
            shadows: [
              Shadow(
                color: const Color(0x29000000),
                offset: Offset(0, 3.sp),
                blurRadius: 6.sp,
              )
            ],
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  var today = DateTime.now();
  Widget transactions() {
    return Center(
      child: Text(
        //+190 is timezone +7
        AppLocalizations.of(context)!.translate('update_system'),
        style: TextStyle(
            color: LikeWalletAppTheme.gray3.withOpacity(0.9),
            letterSpacing: 0.3,
            fontWeight: FontWeight.w100,
            fontFamily: AppLocalizations.of(context)!.translate('font1'),
            fontSize: 49.h),
      ),
    );
    // return Expanded(
    // child: listWithdraw.isNotEmpty
    //     ? SpinKitFadingCircle(
    //         color: LikeWalletAppTheme.bule1,
    //         size: 250.h,
    //       )
    //     : Center(
    //         child: Text(
    //           //+190 is timezone +7
    //           AppLocalizations.of(context)!.translate('update_system'),
    //           style: TextStyle(
    //               color: LikeWalletAppTheme.gray3.withOpacity(0.9),
    //               letterSpacing: 0.3,
    //               fontWeight: FontWeight.w100,
    //               fontFamily:
    //                   AppLocalizations.of(context)!.translate('font1'),
    //               fontSize: 49.h),
    //         ),
    //       ),
    // Stack(
    //           children: <Widget>[
    //             GroupedListView<dynamic, String>(
    //                 groupBy: (element) => DateTime.fromMillisecondsSinceEpoch(
    //                         (element.updateTime) * 1000)
    //                     .toString()
    //                     .substring(0, 10),
    //                 elements: listDate,
    //                 order: GroupedListOrder.DESC,
    //                 useStickyGroupSeparators: false,
    //                 sort: false,
    //                 groupSeparatorBuilder: (String value) {
    //                   var diff =
    //                       DateTime.parse(today.toString().substring(0, 10))
    //                           .difference(DateTime.parse(value));
    //                   return Padding(
    //                     padding: EdgeInsets.only(top: 0.h),
    //                     child: Text(
    //                       diff.inDays == 0
    //                           ? AppLocalizations.of(context)
    //                               .translate('history_today')
    //                           : diff.inDays == 1
    //                               ? AppLocalizations.of(context)
    //                                   .translate('history_yesterday')
    //                               : DateFormat('dd MMMM yyyy', languageCode)
    //                                   .format(DateTime.parse(value)),
    //                       textAlign: TextAlign.center,
    //                       style: TextStyle(
    //                           color: LikeWalletAppTheme.gray4.withOpacity(0.6),
    //                           fontFamily: AppLocalizations.of(context)
    //                               .translate('font1'),
    //                           fontSize: diff.inDays == 0 ? 40.h : 36.h,
    //                           fontWeight: FontWeight.normal),
    //                     ),
    //                   );
    //                 },
    //                 itemBuilder: (c, element) {
    //                   print(element.to);
    //                   return _body(element);
    //                 }),
    //           ],
    //         ),
    // );
  }

  Widget _body(index) {
    return Padding(
      padding: EdgeInsets.only(top: 20.h),
      child: GestureDetector(
        onTap: () {
          setState(() {
            index.isExpanded = !index.isExpanded;
          });
        },
//         child: index.isExpanded
//             ? Container(
//                 decoration: BoxDecoration(
//                   borderRadius: BorderRadius.circular(8.0),
//                   color: index.isExpanded
//                       ? Colors.transparent
//                       : LikeWalletAppTheme.white,
//                 ),
//                 // height: mediaQuery(context, 'height', 250),
//                 // width: mediaQuery(context, 'width', 999),
//                 margin: EdgeInsets.only(
//                   right: 98.w,
//                   left: 98.w,
//                 ),
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Row(
//                       mainAxisAlignment: MainAxisAlignment.center,
//                       children: <Widget>[
//                         Container(
//                           decoration: BoxDecoration(
//                             borderRadius: BorderRadius.circular(8.0),
//                           ),
//                           child: Row(
//                             crossAxisAlignment: CrossAxisAlignment.start,
// //                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                             children: <Widget>[
//                               Container(
//                                 margin: EdgeInsets.only(
//                                   top: 40.h,
//                                   right: 25.w,
//                                 ),
//                                 child: index.type == 'cash'
//                                     ? SvgPicture.asset(
//                                         LikeWalletImage.icon_minus,
//                                         fit: BoxFit.fill,
//                                         height: 66.h,
//                                       )
//                                     : index.type == "transaction"
//                                         ? index.accountNumber == addressETH
//                                             ? SvgPicture.asset(
//                                                 LikeWalletImage.icon_minus,
//                                                 fit: BoxFit.fill,
//                                                 height: 66.h,
//                                               )
//                                             : SvgPicture.asset(
//                                                 LikeWalletImage.icon_plus,
//                                                 height: 66.h)
//                                         : SvgPicture.asset(
//                                             LikeWalletImage.icon_minus,
//                                             height: 66.h),
//                               ),
//                               Column(
//                                 mainAxisAlignment: MainAxisAlignment.center,
//                                 crossAxisAlignment: CrossAxisAlignment.start,
//                                 children: <Widget>[
//                                   Text(
//                                     index.accountNumber == addressETH
//                                         ? AppLocalizations.of(context)
//                                             .translate('history_send')
//                                         : AppLocalizations.of(context)
//                                             .translate('history_received'),
//                                     style: TextStyle(
//                                       color: LikeWalletAppTheme.black
//                                           .withOpacity(0.9),
//                                       letterSpacing: 0.3,
//                                       fontFamily: AppLocalizations.of(context)
//                                           .translate('font1'),
//                                       fontSize: 39.h,
//                                     ),
//                                   ),
//                                   Row(
//                                     children: <Widget>[
//                                       Text(
//                                         index.type == 'cash'
//                                             ? 'Withdraw ' +
//                                                 formatNum.format(
//                                                     double.parse(index.baht))
//                                             : index.type == 'transaction'
//                                                 ? index.accountNumber ==
//                                                         addressETH
//                                                     ? formatNum.format(
//                                                         double.parse(
//                                                             index.baht))
//                                                     : formatNum.format(
//                                                         double.parse(
//                                                             index.baht))
//                                                 : 'Nothing',
//                                         style: TextStyle(
//                                             color: LikeWalletAppTheme.gray3
//                                                 .withOpacity(0.9),
//                                             letterSpacing: 0.3,
//                                             fontFamily:
//                                                 AppLocalizations.of(context)
//                                                     .translate('font1'),
//                                             fontSize: 36.h),
//                                       ),
//                                       SizedBox(
//                                         width: 25.w,
//                                       ),
//                                       Text(
//                                         index.type == 'cash'
//                                             ? ' BAHT'
//                                             : index.type == 'transaction'
//                                                 ? index.accountNumber ==
//                                                         addressETH
//                                                     ? AppLocalizations.of(
//                                                             context)
//                                                         .translate(
//                                                             'history_like')
//                                                     : AppLocalizations.of(
//                                                             context)
//                                                         .translate(
//                                                             'history_like')
//                                                 : 'Nothing',
//                                         style: TextStyle(
//                                             color: LikeWalletAppTheme.gray3
//                                                 .withOpacity(0.9),
//                                             letterSpacing: 0.3,
//                                             fontWeight: FontWeight.w100,
//                                             fontFamily:
//                                                 AppLocalizations.of(context)
//                                                     .translate('font1'),
//                                             fontSize: 36.h),
//                                       ),
//                                       SizedBox(
//                                         width: 20.w,
//                                       ),
//                                     ],
//                                   ),
//                                   index.bankName == 'sendLike'
//                                       ? GestureDetector(
//                                           onTap: () {
//                                             Navigator.push(
//                                                 context,
//                                                 MaterialPageRoute(
//                                                   builder: (context) =>
//                                                       WebOpen(txid: index.tx),
//                                                 ));
//                                           },
//                                           child: Text(
//                                             AppLocalizations.of(context)!.translate(
//                                                 'history_transaction_success'),
//                                             style: TextStyle(
//                                                 color: LikeWalletAppTheme.green
//                                                     .withOpacity(0.9),
//                                                 letterSpacing: 0.3,
//                                                 fontFamily:
//                                                     AppLocalizations.of(context)
//                                                         .translate('font1'),
//                                                 fontSize: 39.h),
//                                           ))
//                                       : index.status == '3'
//                                           ? GestureDetector(
//                                               onTap: () {
//                                                 Navigator.push(
//                                                     context,
//                                                     MaterialPageRoute(
//                                                       builder: (context) =>
//                                                           WebOpenURL(
//                                                         url: index.slip,
//                                                       ),
//                                                     ));
//                                               },
//                                               child: Text(
//                                                 AppLocalizations.of(context)
//                                                     .translate(
//                                                         'history_transaction_success'),
//                                                 style: TextStyle(
//                                                     color: LikeWalletAppTheme
//                                                         .green
//                                                         .withOpacity(0.9),
//                                                     letterSpacing: 0.3,
//                                                     fontFamily:
//                                                         AppLocalizations.of(
//                                                                 context)
//                                                             .translate('font1'),
//                                                     fontSize: 39.h),
//                                               ))
//                                           : Text(
//                                               AppLocalizations.of(context)
//                                                   .translate(
//                                                       'history_transaction'),
//                                               style: TextStyle(
//                                                   color: LikeWalletAppTheme
//                                                       .bule1_5
//                                                       .withOpacity(0.9),
//                                                   letterSpacing: 0.3,
//                                                   fontFamily:
//                                                       AppLocalizations.of(
//                                                               context)
//                                                           .translate('font1'),
//                                                   fontSize: 39.h),
//                                             ),
//                                 ],
//                               )
//                             ],
//                           ),
//                         ),
//                         Expanded(
//                           child: Container(),
//                         ),
//                         Column(
//                           mainAxisAlignment: MainAxisAlignment.center,
//                           crossAxisAlignment: CrossAxisAlignment.end,
//                           children: <Widget>[
//                             Text(
//                               index.title == 'recv'
//                                   ? index.to.toString() == 'Reward'
//                                       ? AppLocalizations.of(context)
//                                               .translate('history_from') +
//                                           " " +
//                                           AppLocalizations.of(context)
//                                               .translate('history_reward')
//                                       : index.to.toString() == 'no'
//                                           ? AppLocalizations.of(context)
//                                                   .translate('history_from') +
//                                               " " +
//                                               AppLocalizations.of(context)
//                                                   .translate('history_no')
//                                           : AppLocalizations.of(context)
//                                                   .translate('history_to') +
//                                               " " +
//                                               index.to.toString()
//                                   : index.to.toString() == 'Lock'
//                                       ? AppLocalizations.of(context)
//                                               .translate('history_to') +
//                                           " " +
//                                           AppLocalizations.of(context)
//                                               .translate('history_lock')
//                                       : index.to == 'Pay'
//                                           ? AppLocalizations.of(context)
//                                                   .translate('history_to') +
//                                               " " +
//                                               AppLocalizations.of(context)
//                                                   .translate('history_pay')
//                                           : AppLocalizations.of(context)
//                                                   .translate('history_to') +
//                                               " " +
//                                               index.to.toString(),
//                               style: TextStyle(
//                                   color:
//                                       LikeWalletAppTheme.black.withOpacity(0.9),
//                                   fontFamily: AppLocalizations.of(context)
//                                       .translate('font1'),
//                                   fontSize: 39.h),
//                             ),
//                             Text(
//                               //+190 is timezone +7
//                               " " +
//                                   DateFormat("HH:mm a")
//                                       .format(
//                                           DateTime.fromMillisecondsSinceEpoch(
//                                               (index.updateTime) * 1000))
//                                       .toString(),
//                               style: TextStyle(
//                                   color:
//                                       LikeWalletAppTheme.gray3.withOpacity(0.9),
//                                   letterSpacing: 0.3,
//                                   fontWeight: FontWeight.w100,
//                                   fontFamily: AppLocalizations.of(context)
//                                       .translate('font1'),
//                                   fontSize: 39.h),
//                             ),
//                             Row(
//                               children: [
//                                 Text(
//                                   //+190 is timezone +7
//                                   DateFormat("dd MMMM yyyy", languageCode)
//                                       .format(
//                                           DateTime.fromMillisecondsSinceEpoch(
//                                               (index.updateTime) * 1000))
//                                       .toString(),
//                                   style: TextStyle(
//                                       color: LikeWalletAppTheme.gray3
//                                           .withOpacity(0.9),
//                                       letterSpacing: 0.3,
//                                       fontWeight: FontWeight.w100,
//                                       fontFamily: AppLocalizations.of(context)
//                                           .translate('font1'),
//                                       fontSize: 39.h),
//                                 ),
//                               ],
//                             )
//                           ],
//                         ),
//                       ],
//                     ), // Tex
//                     Container(
//                       padding: EdgeInsets.only(
//                         left: 90.w,
//                       ),
//                       child: Text(
//                         index.bankName == 'sendLike'
//                             ? AppLocalizations.of(context)
//                                     .translate('message_note') +
//                                 ': ' +
//                                 index.message
//                             : AppLocalizations.of(context)
//                                     .translate('message_note') +
//                                 ': ' +
//                                 ' ',
//                         style: TextStyle(
//                             color: LikeWalletAppTheme.gray3.withOpacity(0.9),
//                             letterSpacing: 0.3,
//                             fontWeight: FontWeight.w100,
//                             fontFamily:
//                                 AppLocalizations.of(context)!.translate('font1'),
//                             fontSize: 39.h),
//                       ),
//                     ),
//                   ],
//                 ))
//             : Container(
//                 decoration: BoxDecoration(
//                   borderRadius: BorderRadius.circular(8.0),
//                   color: index.isExpanded
//                       ? Colors.transparent
//                       : LikeWalletAppTheme.white,
//                   boxShadow: [
//                     BoxShadow(
//                       color: Colors.grey.withOpacity(0.1),
//                       blurRadius: 5.0,
//                       offset: const Offset(0.0, 0.0),
//                     ),
//                   ],
//                 ),
//                 height: 166.h,
//                 width: 999.w,
//                 padding: EdgeInsets.only(right: 57.w, left: 57.w),
//                 margin: EdgeInsets.only(right: 40.w, left: 40.w, bottom: 10.h),
//                 // child: Row(
//                 //   mainAxisAlignment: MainAxisAlignment.center,
//                 //   children: <Widget>[
//                 //     Row(
//                 //       children: <Widget>[
//                 //         Container(
//                 //           margin: EdgeInsets.only(
//                 //             right: 25.w,
//                 //           ),
//                 //           child: index.type == 'cash'
//                 //               ? SvgPicture.asset(LikeWalletImage.icon_minus,
//                 //                   fit: BoxFit.fill, height: 66.h)
//                 //               : index.type == "transaction"
//                 //                   ? index.accountNumber == addressETH
//                 //                       ? SvgPicture.asset(
//                 //                           LikeWalletImage.icon_minus,
//                 //                           fit: BoxFit.fill,
//                 //                           height: 66.h)
//                 //                       : SvgPicture.asset(
//                 //                           LikeWalletImage.icon_plus,
//                 //                           height: 66.h)
//                 //                   : SvgPicture.asset(LikeWalletImage.icon_minus,
//                 //                       height: 66.h),
//                 //         ),
//                 //         Column(
//                 //           mainAxisAlignment: MainAxisAlignment.center,
//                 //           crossAxisAlignment: CrossAxisAlignment.start,
//                 //           children: <Widget>[
//                 //             Text(
//                 //               index.accountNumber == addressETH
//                 //                   ? AppLocalizations.of(context)
//                 //                       .translate('history_send')
//                 //                   : index.type == 'cash'
//                 //                       ? AppLocalizations.of(context)
//                 //                           .translate('history_cashout')
//                 //                       : AppLocalizations.of(context)
//                 //                           .translate('history_received'),
//                 //               style: TextStyle(
//                 //                   color:
//                 //                       LikeWalletAppTheme.black.withOpacity(0.9),
//                 //                   letterSpacing: 0.3,
//                 //                   fontFamily: AppLocalizations.of(context)
//                 //                       .translate('font1'),
//                 //                   fontSize: 39.h),
//                 //             ),
//                 //             Row(
//                 //               children: <Widget>[
//                 //                 Text(
//                 //                   index.type == 'cash'
//                 //                       ? AppLocalizations.of(context)
//                 //                               .translate('history_withdraw') +
//                 //                           " " +
//                 //                           formatNum
//                 //                               .format(double.parse(index.baht))
//                 //                       : index.type == 'transaction'
//                 //                           ? index.accountNumber == addressETH
//                 //                               ? formatNum.format(
//                 //                                   double.parse(index.baht))
//                 //                               : formatNum.format(
//                 //                                   double.parse(index.baht))
//                 //                           : 'Nothing',
//                 //                   style: TextStyle(
//                 //                       color: LikeWalletAppTheme.gray3
//                 //                           .withOpacity(0.9),
//                 //                       letterSpacing: 0.3,
//                 //                       fontFamily: AppLocalizations.of(context)
//                 //                           .translate('font1'),
//                 //                       fontSize: 36.h),
//                 //                 ),
//                 //                 SizedBox(width: 25.w),
//                 //                 Text(
//                 //                   index.type == 'cash'
//                 //                       ? AppLocalizations.of(context)
//                 //                           .translate('bankingbuy_symbol')
//                 //                       : index.type == 'transaction'
//                 //                           ? index.accountNumber == addressETH
//                 //                               ? ' LIKE'
//                 //                               : ' LIKE'
//                 //                           : 'Nothing',
//                 //                   style: TextStyle(
//                 //                     color: LikeWalletAppTheme.gray3
//                 //                         .withOpacity(0.9),
//                 //                     letterSpacing: 0.3,
//                 //                     fontWeight: FontWeight.w100,
//                 //                     fontFamily: AppLocalizations.of(context)
//                 //                         .translate('font1'),
//                 //                     fontSize: 36.h,
//                 //                   ),
//                 //                 ),
//                 //                 SizedBox(width: 20.w),
//                 //               ],
//                 //             )
//                 //           ],
//                 //         )
//                 //       ],
//                 //     ),
//                 //     Expanded(
//                 //       child: Container(),
//                 //     ),
//                 //     Column(
//                 //       mainAxisAlignment: MainAxisAlignment.center,
//                 //       crossAxisAlignment: CrossAxisAlignment.end,
//                 //       children: <Widget>[
//                 //         Text(
//                 //           index.title == 'recv'
//                 //               ? index.to == 'Reward'
//                 //                   ? AppLocalizations.of(context)
//                 //                           .translate('history_from') +
//                 //                       " " +
//                 //                       AppLocalizations.of(context)
//                 //                           .translate('history_reward')
//                 //                   : index.to.toString() == 'no'
//                 //                       ? AppLocalizations.of(context)
//                 //                               .translate('history_from') +
//                 //                           " " +
//                 //                           AppLocalizations.of(context)
//                 //                               .translate('history_no')
//                 //                       : AppLocalizations.of(context)
//                 //                               .translate('history_from') +
//                 //                           " " +
//                 //                           index.to.toString()
//                 //               : index.to.toString() == 'Lock'
//                 //                   ? AppLocalizations.of(context)
//                 //                           .translate('history_to') +
//                 //                       " " +
//                 //                       AppLocalizations.of(context)
//                 //                           .translate('history_lock')
//                 //                   : index.to.toString() == 'Pay'
//                 //                       ? AppLocalizations.of(context)
//                 //                               .translate('history_to') +
//                 //                           " " +
//                 //                           AppLocalizations.of(context)
//                 //                               .translate('history_pay')
//                 //                       : AppLocalizations.of(context)
//                 //                               .translate('history_to') +
//                 //                           " " +
//                 //                           index.to.toString(),
//                 //           style: TextStyle(
//                 //               color: LikeWalletAppTheme.black.withOpacity(0.9),
//                 //               fontFamily: AppLocalizations.of(context)
//                 //                   .translate('font1'),
//                 //               fontSize: 39.h),
//                 //         ),
//                 //         Text(
//                 //           //+190 is timezone +7
//                 //           DateFormat("HH:mm a")
//                 //               .format(DateTime.fromMillisecondsSinceEpoch(
//                 //                   (index.updateTime) * 1000))
//                 //               .toString(),
//                 //           style: TextStyle(
//                 //               color: LikeWalletAppTheme.gray3.withOpacity(0.9),
//                 //               letterSpacing: 0.3,
//                 //               fontWeight: FontWeight.w100,
//                 //               fontFamily: AppLocalizations.of(context)
//                 //                   .translate('font1'),
//                 //               fontSize: 39.h),
//                 //         ),
//                 //       ],
//                 //     ),
//                 //   ],
//                 // ),
//
//               ),
      ),
    );
  }
}
