import 'dart:async';
import 'dart:math';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/bank/contact/contact_details.dart';
import 'package:likewallet/bank/contact/main.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/model/contact.dart';
import 'package:likewallet/screen_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ModalInsideModal extends StatefulWidget {
  ModalInsideModal({Key? key, this.reverse = false, required this.contacts})
      : super(key: key);
  final bool reverse;
  final List<Contacts> contacts;
  @override
  _ModalInsideModal createState() =>
      _ModalInsideModal(contacts: contacts, reverse: reverse, key: key);
}

class _ModalInsideModal extends State<ModalInsideModal> {
  _ModalInsideModal({Key? key, this.reverse = false, required this.contacts});
  final bool reverse;
  final List<Contacts> contacts;
  List colors = [
    Color(0xff8694FF),
    Color(0xff2AE5D6),
    Color(0xffADE000),
    Color(0xffBB89FF)
  ];
  Random random = new Random();
  List<Contacts> _list = [];
  List<Contacts> search = [];

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    setState(() {
      _list = contacts;
      search = contacts;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Material(
        color: Colors.red,
        child: CupertinoPageScaffold(
          backgroundColor: Color(0xFFF5F5F5),
          navigationBar: CupertinoNavigationBar(
              leading: Container(),
              middle: Text(
                'CONTACTS',
                style: TextStyle(
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  fontSize: 36.h,
                  color: const Color(0xff3c3c43),
                  letterSpacing: 1.08,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.left,
              )),
          child: SafeArea(
              bottom: false,
              child: SingleChildScrollView(
                child: Container(
                  height: MediaQuery.of(context).size.height,
                  child: Column(
                    children: [
                      SizedBox(height: 42.h),
                      Container(
                        height: 176.h,
                        width: 935.w,
                        child: Stack(
                          children: [inputSearch(context), addContact()],
                        ),
                      ),
                      SizedBox(height: 55.h),
                      Padding(
                        padding: EdgeInsets.only(left: 80.w),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Text(
                              'LikeWallet Friends',
                              style: TextStyle(
                                fontFamily: AppLocalizations.of(context)!
                                    .translate('font1'),
                                fontSize: 33.h,
                                color: const Color(0xcc3c3c43),
                                letterSpacing: 0.99,
                              ),
                              textAlign: TextAlign.left,
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: 52.h),
                      Card(
                          margin: EdgeInsets.symmetric(horizontal: 42.w),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(15.0),
                          ),
                          color: Colors.white,
                          elevation: 3.0,
                          child: Padding(
                              padding: EdgeInsets.symmetric(vertical: 30.h),
                              child: _getContactList(context))),
                      SizedBox(height: 52.h),
                    ],
                  ),
                ),
              )),
        ));
  }

  Widget _getContactList(context) {
    return _list != null
        //Build a list view of all contacts, displaying their avatar and
        // display name
        ? Column(
            children: _list
                .map(
                  (i) => Container(
                    margin: EdgeInsets.only(
                        left: 67.w, right: 67.w, top: 24.h, bottom: 24.h),
                    child: InkWell(
                      onTap: () {
                        if (i.address != '') {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => ContactDetails(
                                        name: i.name ?? '',
                                        phone: i.phoneNumber ?? '',
                                        address: i.address ?? '',
                                        color: i.color ?? '',
                                      )));
                        } else {
                          print('ไม่พบ Address');
                          showShortToast('ไม่พบ Address', Colors.red);
                        }
                      },
                      child: Row(
                        children: [
                          Container(
                            height: 144.h,
                            width: 155.w,
                            child: Stack(
                              children: [
                                Container(
                                  alignment: Alignment.center,
                                  height: 132.h,
                                  width: 155.w,
                                  decoration: BoxDecoration(
                                    color: i.color,
                                    borderRadius:
                                        BorderRadius.circular(100.0.h),
                                  ),
                                  child:
                                      Text(i.name.substring(0, 1).toString()),
                                ),
                                Positioned(
                                  bottom: 0,
                                  right: 12.w,
                                  child: Image.asset(
                                    LikeWalletImage.iconLikeWallet,
                                    height: 60.h,
                                    width: 60.h,
                                  ),
                                )
                              ],
                            ),
                          ),
                          SizedBox(width: 55.w),
                          Flexible(
                            child: Container(
                              padding: new EdgeInsets.only(right: 13.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    i.name ?? '',
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 1,
                                    style: TextStyle(
                                      fontFamily: AppLocalizations.of(context)!
                                          .translate('font1'),
                                      fontSize: 27.h,
                                      color: const Color(0xcc3c3c43),
                                      letterSpacing: 0.8099999999999999.w,
                                      height: 1.1111111111111112,
                                    ),
                                    textHeightBehavior: TextHeightBehavior(
                                        applyHeightToFirstAscent: false),
                                    textAlign: TextAlign.center,
                                  ),
                                  Text(
                                    i.phoneNumber ?? '',
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 1,
                                    style: TextStyle(
                                      fontFamily: AppLocalizations.of(context)!
                                          .translate('font1'),
                                      fontSize: 27.h,
                                      color: const Color(0xcc3c3c43),
                                      letterSpacing: 0.8099999999999999.w,
                                      height: 1.1111111111111112,
                                    ),
                                    textHeightBehavior: TextHeightBehavior(
                                        applyHeightToFirstAscent: false),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                )
                .toList(),
          )
        : Center(
            child: Center(
              child: Text(
                AppLocalizations.of(context)!
                    .translate('not_contact_available'),
                style: TextStyle(
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  fontSize: 39.h,
                  color: const Color(0xff3c3c43),
                  letterSpacing: 0.99,
                ),
                textAlign: TextAlign.left,
              ),
            ),
          );
  }

  _openContactForm() async {
    print("_openContactForm");
    // try {
    //   var contact = await FlutterContacts.openContactForm(
    //       iOSLocalizedLabels: iOSLocalizedLabels);
    //   // refreshContacts();
    // } on FormOperationException catch (e) {
    //   switch (e.errorCode) {
    //     case FormOperationErrorCode.FORM_OPERATION_CANCELED:
    //     case FormOperationErrorCode.FORM_COULD_NOT_BE_OPEN:
    //     case FormOperationErrorCode.FORM_OPERATION_UNKNOWN_ERROR:
    //     default:
    //       print(e.errorCode);
    //   }
    // }
  }

  Widget addContact() {
    return Positioned(
      bottom: 0,
      right: 58.w,
      child: SizedBox(
        width: 139.0.w,
        height: 118.0.h,
        child: InkWell(
          onTap: () async {
            _openContactForm();
          },
          child: Stack(
            children: <Widget>[
              Container(
                height: 118.0.h,
                width: 139.0.w,
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(66.0),
                    color: const Color(0xff201f2d),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0x1a000000),
                        offset: Offset(0, 16),
                        blurRadius: 25,
                      ),
                    ],
                  ),
                ),
              ),
              Container(
                height: 118.0.h,
                width: 139.0.w,
                child: SvgPicture.string(
                  '<svg viewBox="122.0 1765.1 34.5 34.5" ><path transform="translate(-43.36, 1388.43)" d="M 197.8597412109375 391.94873046875 L 184.6353149414062 391.94873046875 L 184.6353149414062 378.7242736816406 C 184.6353149414062 377.6098327636719 183.7324523925781 376.7070007324219 182.6180114746094 376.7070007324219 C 181.5035705566406 376.7070007324219 180.6007385253906 377.6098327636719 180.6007385253906 378.7242736816406 L 180.6007385253906 391.94873046875 L 167.3762817382812 391.94873046875 C 166.2618408203125 391.94873046875 165.3590087890625 392.8515625 165.3590087890625 393.9660034179688 C 165.3590087890625 395.0795593261719 166.2618408203125 395.9833068847656 167.3762817382812 395.9833068847656 L 180.6007385253906 395.9833068847656 L 180.6007385253906 409.2068481445312 C 180.6007385253906 410.3204040527344 181.5035705566406 411.22412109375 182.6180114746094 411.22412109375 C 183.7324523925781 411.22412109375 184.6353149414062 410.3204040527344 184.6353149414062 409.2068481445312 L 184.6353149414062 395.9833068847656 L 197.8597412109375 395.9833068847656 C 198.9742126464844 395.9833068847656 199.8770446777344 395.0795593261719 199.8770446777344 393.9660034179688 C 199.8770446777344 392.8515625 198.9742126464844 391.94873046875 197.8597412109375 391.94873046875 Z" fill="#0fe8d8" stroke="none" stroke-width="1" stroke-miterlimit="10" stroke-linecap="butt" /></svg>',
                  allowDrawingOutsideViewBox: true,
                  fit: BoxFit.fill,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget inputSearch(context) {
    final _debouncer = Debouncer(milliseconds: 500);
    return Container(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            height: 115.h,
            width: 935.w,
            decoration: BoxDecoration(
              color: Colors.grey.withOpacity(0.08),
              borderRadius: new BorderRadius.circular(40.0),
            ),
            child: Padding(
              padding: EdgeInsets.only(left: 15, right: 15, top: 0),
              child: TextFormField(
                decoration: InputDecoration(
                  isDense: true,
                  icon: Icon(
                    Icons.search,
                    color: Colors.grey,
                    size: 42.h,
                  ),
                  border: InputBorder.none,
                  hintText: 'SEARCH',
                  hintStyle: TextStyle(
                      color: Colors.grey.withOpacity(0.6),
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      fontStyle: FontStyle.normal,
                      fontWeight: FontWeight.w500,
                      fontSize: 36.h),
                  // labelText: 'Email',
                ),
                onChanged: (string) {
                  // _debouncer.run(() {
                  setState(() {
                    _list = search
                        .where((u) => (u.name
                                .toLowerCase()
                                .contains(string.toLowerCase()) ||
                            u.phoneNumber
                                .toLowerCase()
                                .contains(string.toLowerCase())))
                        .toList();
                    // });
                    print(search);
                  });
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class Debouncer {
  final int milliseconds;
  late VoidCallback action;
  late Timer _timer;

  Debouncer({required this.milliseconds});

  run(VoidCallback action) {
    if (_timer != null) {
      _timer.cancel();
    }
    _timer = Timer(Duration(milliseconds: milliseconds), action);
  }
}
