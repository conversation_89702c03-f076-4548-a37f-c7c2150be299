import 'dart:convert';
import 'dart:math';

import 'package:clippy_flutter/clippy_flutter.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:image_picker/image_picker.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/bank/contact/contact_details.dart';
import 'package:likewallet/bank/contact/modal_inside_modal.dart';
import 'package:likewallet/close_maintenance.dart';
import 'package:likewallet/device_utils.dart';
import 'package:likewallet/jsonDecode/vending.dart';
import 'package:likewallet/libraryman/app_local.dart';

import 'package:flutter_svg/flutter_svg.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'dart:async';
import 'package:flutter/services.dart';
import 'package:likewallet/main.dart';
import 'package:likewallet/middleware/check_maintenance/check_maintenance.dart';
import 'package:likewallet/middleware/getLanguage.dart';
import 'package:likewallet/model/contact.dart';
import 'package:likewallet/model/pageStatus.dart';
import 'package:likewallet/quickpay/scanPay.dart';
import 'package:likewallet/quickpay/scanAddress.dart';

import 'package:likewallet/screen_util.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:likewallet/bank/confirm_transection.dart';
import 'dart:ui';
import 'package:flutter/rendering.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:likewallet/middleware/permissionContact.dart';
import 'package:likewallet/libraryman//ethcontract.dart';
import 'package:likewallet/middleware/get_mnemonic.dart';
import 'package:likewallet/middleware/check_auth.dart';
import 'package:intl/intl.dart';
import 'package:likewallet/libraryman/address_service.dart';
import 'package:likewallet/libraryman/configuration_service.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:http/http.dart' as http;
import 'package:likewallet/app_config.dart';
import 'package:likewallet/bank/favoriteTakePhoto.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
//use for check active API before use withdraw
import 'package:likewallet/libraryman/checkAPI.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:likewallet/libraryman/setFormat.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:likewallet/bank/choiceTopay.dart';
import 'package:likewallet/quickpay/scanAddress.dart';
import 'package:likewallet/quickpay/scanPay.dart';

import 'package:flutter_riverpod/flutter_riverpod.dart' as riverpod;
import 'package:likewallet/screen/NavigationBar.dart';
import 'package:likewallet/libraryman/serviceHTTP.dart';
import 'package:intl/intl.dart' as formatIntl;
import 'package:likewallet/quickpay/favorites/list.dart';
import 'package:likewallet/libraryman/keyboard_done_widget.dart';
import 'dart:io' show File, Platform;

import 'package:likewallet/libraryman/auth.dart';

/* ----------------| RECEIVE |--------------*/
class SEND extends StatefulWidget {
  SEND(
      {required this.source,
      this.shopID,
      this.scanActive,
      required this.address,
      required this.titleContact,
      this.contract,
      required this.abi,
      required this.callFunction,
      required this.selectedAddr,
      this.vending,
      this.isVending});

  final String source;
  final shopID;
  final scanActive;
  final String? contract;
  final String address;
  final String titleContact;
  final String abi;
  final String callFunction;
  final String selectedAddr;
  final Vending? vending;
  final bool? isVending;

  @override
  State<StatefulWidget> createState() {
    return _SEND(
        source: source,
        shopID: shopID,
        scanActive: scanActive,
        address: address,
        titleContact: titleContact,
        contract: contract,
        abi: abi,
        callFunction: callFunction,
        selectedAddr: selectedAddr,
        vending: vending,
        isVending: isVending);
  }
}

class _SEND extends State<SEND> {
  final TextEditingController controller = new TextEditingController();
  TextEditingController amountSend = TextEditingController();
  TextEditingController textCash = TextEditingController();
  TextEditingController textAccountNumber = TextEditingController();
  _SEND(
      {required this.source,
      this.shopID,
      this.scanActive,
      required this.address,
      required this.titleContact,
      this.contract,
      required this.abi,
      required this.callFunction,
      required this.selectedAddr,
      this.vending,
      this.isVending});

  final String source;
  final scanActive;
  final shopID;
  final String address;
  final String titleContact;
  final String? contract;
  final String abi;
  final String callFunction;
  final String selectedAddr;
  final Vending? vending;
  final bool? isVending;

  bool showfavorite = false;
  final fireStore = FirebaseFirestore.instance;
  double price = 0;
  final f = new formatIntl.NumberFormat("###,###.##");
  final formatCurrency = new NumberFormat.decimalPattern();
  StreamController<String> streamBalance = StreamController<String>();
  StreamController<String> streamBalanceLocked = StreamController<String>();
  StreamController<String> streamAvailable = StreamController<String>();
  StreamController<double> streamBalanceLock = StreamController<double>();
  GlobalKey<ScaffoldState> _scaffoldKey = new GlobalKey();
  GlobalKey globalKey = new GlobalKey();
  double rate = 100.00;
  double amount = 0.00;
  String accountNumber = '';
  String currency = 'THB';
  double fee = 0.00;
  late String _inputErrorText;
  int keyword = 1;
  String barcode = "";
  String symbol_1 = 'LIKE';
  String symbol_2 = 'LIKE';
  String balance = 'Loading..';
  double balanceLIKE = 0.00;
  String locked_balance = 'Loading..';
  double rateCurrency = 0.0;
  double balanceLIKELock = 0.00;
  String available = 'Loading..';
  String bank = '';
  late String pketh;
  late BaseETH eth;
  late GetMnemonic seed;
  late String mnemonic;
  late CheckAuth Logon;
  late IAddressService addressService;
  late IConfigurationService configETH;
  late BaseAuth auth;
  bool login = false;
  late CheckAPI checkAPI;
  late SetFormat setFormat;
  var selected;
  String dropdownValue = '+66';
  TextEditingController addressText = TextEditingController();
  TextEditingController phoneNumber = TextEditingController();
  final TextEditingController nameFavorite = TextEditingController();
  final TextEditingController addressFavorite = TextEditingController();
  final ImagePicker _picker = ImagePicker();
  late List<Contacts> _contacts;
  String? photoFavorite;
  File? _imageFavorite;
  bool _autoValidate = false;

  final GlobalKey<FormState> formKey =
      GlobalKey<FormState>(debugLabel: 'form_key');

  late FocusNode myFocusNode;
  late FocusNode controllerNode;
  bool delete = false;
  bool callphoto = false;

  int tabMenu = 0;
  var crossFadeView = CrossFadeState.showFirst;
  bool _saving = false;
  String toAddress = 'noaddress';
  late AbstractServiceHTTP APIHttp;
  var _opacity = 1.0;
  late OverlayEntry? overlayEntry;
  String _dataString = "";
  List colors = [
    Color(0xff8694FF),
    Color(0xff2AE5D6),
    Color(0xffADE000),
    Color(0xffBB89FF)
  ];
  Random random = new Random();
  Dio dio = new Dio();

  //ร้านค้า
  List<list> _list = [];
  List group = [];
  List<list> search = [];
  late String uid;
  List symbol = ['THB', 'LAK', 'VND', 'USD', 'GOLD', 'LIKE'];
  String Symbol = 'THB';
  bool showcurrency = false;
  bool showsymbol = false;
  bool showbutton = false;
  bool contactLoading = false;
  String amountValue = '0';
  late List<dynamic> abiVending;
  late String contractVending;
  late CheckAbout checkAbout;
  late OnLanguage language;
  //kyc session
  bool kycStatus = false;
  FocusNode amountFocusNode = new FocusNode();
  FocusNode amountBuyFocusNode = new FocusNode();
  FocusNode amountCashFocusNode = new FocusNode();
  final formatBank = new NumberFormat("###-###-####");
  late OnContact contact;
  List<Contacts> getContactMobile = [];
  bool syncContact = false;
  bool actionSyncContact = false;
  KeyboardVisibilityController keyboardVisibilityController = KeyboardVisibilityController();

  void showColoredToast(msg) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_LONG,
        backgroundColor: Colors.red,
        textColor: Colors.white);
  }

  Future<String> searchAddressFromPhone() async {
    setState(() {
      toAddress = 'noaddress';
    });
    try {
      var _token = await auth.getTokenFirebase();
      Response response =
          await dio.post('https://' + env.apiUrl + "/searchNameNew", data: {
        "term": selected == 'address'
            ? addressText.text
            : dropdownValue +
                phoneNumber.text
                    .substring(1, phoneNumber.text.length)
                    .toString(),
        "_token": _token
      });
      print(response.data);
      if (response.data["statusCode"] == 200) {
        if (response.data["result"]["name"] != "no") {
          setState(() {
            toAddress = response.data["result"]["address"];
          });
          return response.data["result"]["address"];
        } else {
          setState(() {
            toAddress = 'noaddress';
          });
          return 'noaddress';
        }
      } else {
        setState(() {
          toAddress = 'noaddress';
        });
        return 'noaddress';
      }
    } catch (e) {
      setState(() {
        toAddress = 'noaddress';
      });
      return 'noaddress';
    }
  }

  //done button zone
  showOverlay(BuildContext context) {
    if (overlayEntry != null) return;
    OverlayState? overlayState = Overlay.of(context);
    overlayEntry = OverlayEntry(builder: (context) {
      return Positioned(
          bottom: MediaQuery.of(context).viewInsets.bottom,
          right: 0.0,
          left: 0.0,
          child: InputDoneView());
    });

    overlayState!.insert(overlayEntry!);
  }

  removeOverlay() {
    if (overlayEntry != null) {
      overlayEntry!.remove();
      overlayEntry = null;
    }
  }

  Future<bool> getShop() async {
    //เรียก API ร้านค้า
    var data = await APIHttp.getQuickpayShop();
    setState(() {
      _list = data[0];
      search = data[0];
      group = data[1];
    });
    return true;
  }

  void pasteAddress() async {
    ClipboardData? data = await Clipboard.getData('text/plain');
    print('paste');
    setState(() {
      selected = 'address';
    });
    if (selected == 'address') {
      setState(() {
        addressText.text = data!.text!;
        toAddress = data.text!;
      });
    } else {
      setState(() {
        phoneNumber.text = data!.text!;
        toAddress = 'noaddress';
      });
    }
  }

  Future generateETH(seed, pref) async {
    print('send page');
    setState(() {
      _dataString = configETH.getAddress();
    });
    eth.getBalance(address: _dataString).then((avaiBalance) {
      available = 'LIKE ' + f.format(avaiBalance).toString();
      if (!mounted) return;
      setState(() {
        balanceLIKE = avaiBalance.toDouble();
      });
      streamAvailable.sink.add(available);

      eth.getBalanceLock(address: _dataString).then((balanceLock) {
        print(balanceLock);
        balance = 'LIKE ' + f.format(avaiBalance + balanceLock);
        balanceLIKELock = balanceLock.toDouble();
        locked_balance = 'LIKE ' + f.format(balanceLock).toString();
        streamBalance.sink.add(balance);
        streamBalanceLock.sink.add(balanceLIKELock);
        streamBalanceLocked.sink.add(locked_balance);

        if (!mounted) return;

        setState(() {
          _saving = false;
        });
      });
    });
  }

  _changeText(_symbol) {
    fireStore
        .collection('exchangeFiat')
        .doc(_symbol == 'none' ? "THB-LIKE" : "THB-" + _symbol)
        .get()
        .then((DocumentSnapshot<Map<String, dynamic>> ds) {
      print(ds.data()!["rate"]);

      if (ds.data()!["to"] == 'LIKE') {
        setState(() {
          rateCurrency = ds.data()!["rate"];
        });
        print(rateCurrency);
      } else {
        setState(() {
          rateCurrency = 1 / ds.data()!["rate"];
        });
        print(rateCurrency);
      }
    });
    setState(() {
      print(_symbol);
      symbol_1 = _symbol;
    });
  }

  changeMenu(_number) async {
    setState(() {
      tabMenu = _number;
    });
  }

  _changeCurrency(_symbol) async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    _changeText(_symbol);
    pref.setString('currency', _symbol);
//    int lastIndex = allCurrency.length - 1;
//    int _index = allCurrency.indexOf(current);
//    if (_index == lastIndex) {
//      _index = 0;
//      _changeText(allCurrency[_index]);
//      pref.setString('currency', allCurrency[_index]);
//    } else {
//      _changeText(allCurrency[_index + 1]);
//      pref.setString('currency', allCurrency[_index + 1]);
//    }
  }

  Future<List<Contacts>> getContacts({required BuildContext context}) async {
    List phoneContact = [];
    setState(() {
      contactLoading = true;
    });
    final PermissionStatus permissionStatus = await contact.permissionContact();
    print(permissionStatus);
    if (permissionStatus == PermissionStatus.granted) {
      final contacts = await FlutterContacts.getContacts();

      contacts.forEach((element) async {
        element.phones.forEach((phone) async {
          if (phone.number.toString().substring(0, 1) == '0') {
            final p = phone.number.toString();
            phoneContact.add(p
                .replaceFirst('0', '+66')
                .replaceAll(' ', '')
                .replaceAll('-', ''));
            phoneContact.add(p
                .replaceFirst('0', '+885')
                .replaceAll(' ', '')
                .replaceAll('-', ''));
            phoneContact.add(p
                .replaceFirst('0', '+888')
                .replaceAll(' ', '')
                .replaceAll('-', ''));
          } else {}
        });
      });
      int i = 0;

      phoneContact.forEach((data) async {
        QuerySnapshot<Map<String, dynamic>> phone = await FirebaseFirestore
            .instance
            .collection('addressDNS')
            .where('phoneNumber', isEqualTo: data)
            .get();
        // print(phone);

        if (phone.docs.isNotEmpty) {
          phone.docs.forEach((phone) async {
            if (i % 4 == 0) {
              i = 0;
            }
            var setData = {
              'name': phone.data()['name'],
              'phoneNumber': phone.data()['phoneNumber'],
              'address': phone.data()['address'],
              'color': colors[i++],
            };
            print(setData);
            // if (mounted) return;
            setState(() {
              getContactMobile.add(Contacts.fromJson(setData));
            });
          });
          setState(() {
            contactLoading = false;
          });
        } else {
          if (mounted) return;
          setState(() {
            contactLoading = false;
            print('ไม่มีข้อมูล');
          });
        }
      });
      return Future.value();
    } else {
      setState(() {
        contactLoading = false;
      });
      //If permissions have been denied show standard cupertino alert dialog
      showDialog(
          context: context,
          builder: (BuildContext context) => CupertinoAlertDialog(
                title: Text('Permissions error'),
                content: Text('Please enable contacts access '
                    'permission in system settings'),
                actions: <Widget>[
                  CupertinoDialogAction(
                    child: Text('OK'),
                    onPressed: () => Navigator.of(context).pop(),
                  )
                ],
              ));
      return Future.value();
    }
  }

  @override
  void dispose() {
    // TODO: implement dispose
    streamAvailable.close();
    amountFocusNode.dispose();
    amountBuyFocusNode.dispose();
    amountCashFocusNode.dispose();
    myFocusNode.dispose();
    selected = null;
    super.dispose();
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    checkAbout = OnCheckAbout();
    language = CallLanguage();
    contact = CallContact();
    checkFirst();
    checkContactList();
    // prefs.g
  }



  checkContactList() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    var data = pref.getBool("syncContact");
    print('syncContact');
    // print(data);
    if(data == null) {
      setState(() {
        syncContact = false;
        actionSyncContact = false;
      });
    }else{
      if(data) {
        setState(() {
          syncContact = true;
          actionSyncContact = true;
        });
      }else {
        setState(() {
          actionSyncContact = true;
        });
      }
    }
  }
  checkFirst() async {
    PageMaintenance statusPage = await checkAbout.checkTierPermission(
        tierLevel: context.read(tierLevel).state, page: 'send');
    if (statusPage.status == 'active') {
      //เริ่มทำงาน
      print('active');
      setInitState();
    } else {
      //ปิด maintenance
      print('inactive');
      final lang = await language.getLanguage();
      final title = await checkAbout.selectLanguage(
          language: lang, detail: statusPage.title);
      final detail = await checkAbout.selectLanguage(
          language: lang, detail: statusPage.detail);
      final detailTime = await checkAbout.selectLanguage(
          language: lang, detail: statusPage.detail_time);
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
            builder: (context) => CloseMaintenance(
                  title: title,
                  detail: detail,
                  detailTime: detailTime,
                  url: statusPage.url,
                )),
      );
    }
  }

  setInitState() async {
    Future.delayed(Duration.zero, () async {
      auth = new Auth();
      myFocusNode = FocusNode();
      controllerNode = FocusNode();
      print(selectedAddr);
      if (!mounted) return;
      setState(() {
        _saving = true;
        selected = selectedAddr;
      });
      FirebaseFirestore.instance
          .collection('fee')
          .doc('THB')
          .get()
          .then((DocumentSnapshot<Map<String, dynamic>> rate) {
        setState(() {
          fee = double.parse(rate.data()!["rate"].toString());
          print("fee is : " + fee.toString());
        });
      });

      APIHttp = new ServiceHTTP();
      amountSend.addListener(changeAmount);
      Logon = new CurCheckAuth();
      seed = new MnemonicRetrieve();
      eth = new EthContract();

      checkAPI = APIChecker();
      setFormat = SetFormatString();

      if (Platform.isIOS) {
        amountFocusNode.addListener(() {
          bool hasFocus = amountFocusNode.hasFocus;
          if (hasFocus)
            showOverlay(context);
          else
            removeOverlay();
        });

        amountBuyFocusNode.addListener(() {
          bool hasFocus = amountBuyFocusNode.hasFocus;
          if (hasFocus)
            showOverlay(context);
          else
            removeOverlay();
        });
        amountCashFocusNode.addListener(() {
          bool hasFocus = amountCashFocusNode.hasFocus;
          if (hasFocus)
            showOverlay(context);
          else
            removeOverlay();
        });
        keyboardVisibilityController.onChange.listen((bool visible) {
          removeOverlay();
        });
      }

      if (scanActive == 'active') {
        setState(() {
          addressText.text = address;
          toAddress = address;
        });
      }
      if (source == 'favorite') {
        setPay();

        print('call favorite');
      }
      if (source == 'contact') {
        setPayContact();
        print('call contact');
      }
      setInit();
      getFavorites();

      // final contacts = await getContacts(context: context);
    });
  }

  void setPay() async {
    getShop().then((result) {
      // print(result);
      if (result) {
        // print('result getShop' + result.toString());
        setState(() {
          for (var i = 0; i < _list.length; i++) {
            if (_list[i].running == shopID) {
              toAddress = _list[i].address.toString().trim();
              addressText.text = _list[i].title.toString();
              break;
            }
          }
        });
      }
    });
  }

  void setPayContact() async {
    setState(() {
      toAddress = address.toString();
      addressText.text = titleContact;
      print(toAddress);
      print(addressText.text.toString());
    });
  }

  setInit() async {
    print('open banking');

    SharedPreferences pref = await SharedPreferences.getInstance();
    if (!mounted) return;
    setState(() {
      kycStatus = pref.getBool('kycActive') ?? false;
    });

    currency = pref.getString('currency') ?? 'THB';

    if (!mounted) return;
    setState(() {
      Symbol = currency;
    });
    _changeCurrency(currency);
//    APIHttp.getCurrentFee().then((getFee) {
//      setState(() {
//        fee = getFee;
//      });
//      print(fee);
//    });
//
    configETH = new ConfigurationService(pref);
    addressService = new AddressService(configETH);
//    if(!mounted) return;
//
    login = pref.getBool('login') ?? false;
    if (login) {
      mnemonic = await configETH.getMnemonic();
      generateETH(mnemonic, pref);
    }
  }

  String validateNumber(String value) {
    String pattern = r'(^[0-9.]{1,}$)';
    RegExp regExp = new RegExp(pattern);
    if (value.length == 0) {
      print(value);
      return 'Please enter mobile number';
    } else if (!regExp.hasMatch(value)) {
      print(value + ' not match');
      return 'Please enter valid mobile number';
    }
    print(value + ' match');
    return 'match';
  }

  bool validateFirstNumber(String value) {
    String pattern = r'(^[0-9]?\d)';
    RegExp regExp = new RegExp(pattern);
    if (value.length == 0) {
      print(value);
      return false;
    } else if (!regExp.hasMatch(value)) {
      print(value + ' not match2');

      return false;
    }
    print(value + ' match2');
    return true;
  }

  changeAmount() {
    print(amountSend.text);

//    if(Platform.isIOS){
    var valid = validateNumber(amountSend.text);
    if (valid == 'match') {
      setState(() {
        amountValue = amountSend.text;
      });

      if (amountSend.text.length > 0) {
        setState(() {
          showcurrency = true;
          showbutton = true;
        });
      } else {
        setState(() {
          showcurrency = false;
          showsymbol = false;
          showbutton = false;
        });
      }
    } else {
//        showColoredToast('only number');
    }

//    }
  }

//-----Form AddFavortie
  AddFavortieForm(BuildContext context) async {
    return showDialog(
        context: context,
        builder: (context) {
          return Stack(
            alignment: Alignment.topCenter,
            children: <Widget>[
              AlertDialog(
                scrollable: true,
                backgroundColor: Colors.white.withOpacity(1),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30.0),
                ),
                content: Container(
                  width: MediaQuery.of(context).size.width *
                      Screen_util('width', 934),
                  child: Form(
                    key: formKey,
                    // autovalidate: _autoValidate,
                    child: GestureDetector(
                      onTap: () {
                        FocusScopeNode currentFocus = FocusScope.of(context);
                        if (!currentFocus.hasPrimaryFocus) {
                          currentFocus.unfocus();
                        }
                      },
                      child: Column(children: <Widget>[
                        Row(children: <Widget>[
                          Container(
                            child: Image.asset(
                              LikeWalletImage.icon_favorite_white,
                              height: mediaQuery(context, 'height', 95),
                            ),
                          ),
                          Container(
                              child: new Text(
                            ' ' +
                                AppLocalizations.of(context)!
                                    .translate('favorite_title'),
                            style: TextStyle(
                                letterSpacing: 1,
                                color: Color(0xff707071),
                                fontFamily: AppLocalizations.of(context)!
                                    .translate('font1'),
                                fontSize: MediaQuery.of(context).size.height *
                                    Screen_util('height', 33),
                                fontWeight: FontWeight.normal),
                          )),
                        ]),
                        Container(
                          height: MediaQuery.of(context).size.height *
                              Screen_util('height', 344),
                          width: MediaQuery.of(context).size.height *
                              Screen_util('height', 700),
                          child: Stack(
                            alignment: Alignment.center,
                            children: <Widget>[
                              Container(
                                height: MediaQuery.of(context).size.height *
                                    Screen_util('height', 253.98),
                                width: MediaQuery.of(context).size.height *
                                    Screen_util('height', 253.98),
                                child: Stack(
                                    alignment: Alignment.center,
                                    children: <Widget>[
                                      Container(
                                          color: Colors.transparent,
                                          child: _imageFavorite != null
                                              ? Container(
                                                  padding: EdgeInsets.all(5),
                                                  child: new CircleAvatar(
                                                    backgroundImage:
                                                        new FileImage(
                                                            _imageFavorite!),
                                                    radius: 120,
                                                  ))
                                              : Container(
                                                  child: new Image.asset(
                                                    LikeWalletImage
                                                        .defaultProfile,
                                                    fit: BoxFit.cover,
                                                  ),
                                                )),
                                      Positioned(
                                        right: mediaQuery(context, 'width', 0),
                                        bottom:
                                            mediaQuery(context, 'height', 0),
                                        child: GestureDetector(
                                            onTap: () async {
                                              var image =
                                                  await _picker.pickImage(
                                                      source:
                                                          ImageSource.camera);
                                              // final photo = await Navigator.push(
                                              //   context,
                                              //   MaterialPageRoute(
                                              //     builder: (context) =>
                                              //         FavoriteTakePhoto(),
                                              //   ),
                                              // );
                                              setState(() {
                                                _imageFavorite =
                                                    File(image!.path);
                                              });

                                              myFocusNode.requestFocus();
                                            },
                                            child: Container(
                                              height: mediaQuery(
                                                  context, 'height', 95),
                                              padding: EdgeInsets.only(
                                                  right: MediaQuery.of(context)
                                                          .size
                                                          .width *
                                                      Screen_util('width', 20),
                                                  bottom: MediaQuery.of(context)
                                                          .size
                                                          .height *
                                                      Screen_util(
                                                          'height', 10)),
                                              alignment: Alignment.bottomRight,
                                              child: Image.asset(
                                                LikeWalletImage.icon_edit_photo,
                                              ),
                                            )),
                                      ),
                                    ]),
                              ),

//                              AnimatedPositioned(
//                                  right: mediaQuery(context, 'height', 30),
//                                  top: mediaQuery(context, 'height', 0),
//                                  child: GestureDetector(
//                                    onTap: () {
//                                      preview();
//                                    },
//                                    child: Container(
//                                        padding: EdgeInsets.all(12),
//                                        decoration: BoxDecoration(
//                                          shape: BoxShape.circle,
//                                          color: LikeWalletAppTheme.white,
//                                          boxShadow: [
//                                            BoxShadow(
//                                              color:
//                                                  Colors.grey.withOpacity(0.5),
//                                              spreadRadius: 2,
//                                              blurRadius: 4,
//                                              offset: Offset(1,
//                                                  2), // changes position of shadow
//                                            ),
//                                          ],
//                                        ),
//                                        height:
//                                            mediaQuery(context, 'height', 146),
//                                        width:
//                                            mediaQuery(context, 'height', 146),
//                                        child: Image.asset(
//                                          LikeWalletImage.contact_us_photo,
//                                          height:
//                                              mediaQuery(context, 'height', 50),
//                                        )),
//                                  ),
//                                  duration: Duration(milliseconds: 500)),
//                              AnimatedPositioned(
//                                  right: mediaQuery(context, 'height', 30),
//                                  bottom: mediaQuery(context, 'height', 0),
//                                  child: Container(
//                                      padding: EdgeInsets.all(12),
//                                      decoration: BoxDecoration(
//                                        shape: BoxShape.circle,
//                                        color: LikeWalletAppTheme.white,
//                                        boxShadow: [
//                                          BoxShadow(
//                                            color: Colors.grey.withOpacity(0.5),
//                                            spreadRadius: 2,
//                                            blurRadius: 4,
//                                            offset: Offset(1,
//                                                2), // changes position of shadow
//                                          ),
//                                        ],
//                                      ),
//                                      height:
//                                          mediaQuery(context, 'height', 146),
//                                      width: mediaQuery(context, 'height', 146),
//                                      child: Image.asset(
//                                        LikeWalletImage.contact_us_takePhoto,
//                                        height:
//                                            mediaQuery(context, 'height', 50),
//                                      )),
//                                  duration: Duration(milliseconds: 500)),
                            ],
                          ),
                        ),
                        Container(
                          margin: EdgeInsets.only(
                            bottom: mediaQuery(context, "width", 30),
                          ),
                          width: mediaQuery(context, 'width', 600),
                          child: TextFormField(
                            controller: nameFavorite,
                            focusNode: myFocusNode,
                            validator: (val) => val!.length < 1
                                ? 'กรุณาใส่ชื่อรายการโปรด'
                                : null,
                            style: TextStyle(
                                color: LikeWalletAppTheme.black,
                                fontFamily: AppLocalizations.of(context)!
                                    .translate('font1'),
                                fontSize: MediaQuery.of(context).size.height *
                                    Screen_util('height', 42),
                                fontWeight: FontWeight.normal),
                            decoration: InputDecoration(
                              contentPadding: EdgeInsets.only(
                                top: mediaQuery(context, "height", 50),
                              ),
                              labelText: AppLocalizations.of(context)!
                                  .translate('favorite_name'),
                              labelStyle: TextStyle(
                                  color: Color(0xff707071),
                                  fontFamily: AppLocalizations.of(context)!
                                      .translate('font1'),
                                  fontSize: MediaQuery.of(context).size.height *
                                      Screen_util('height', 35),
                                  fontWeight: FontWeight.normal),
                            ),
                          ),
                        ),
                        Container(
                          margin: EdgeInsets.only(
                            top: mediaQuery(context, "width", 30),
                            bottom: mediaQuery(context, "width", 30),
                          ),
                          width: mediaQuery(context, 'width', 600),
                          child: TextFormField(
                            controller: addressFavorite,
                            validator: (val) => val!.length < 20
                                ? 'กรุณาใส่ชื่อรายการโปรด'
                                : null,
                            style: TextStyle(
                                color: LikeWalletAppTheme.black,
                                fontFamily: AppLocalizations.of(context)!
                                    .translate('font1'),
                                fontSize: MediaQuery.of(context).size.height *
                                    Screen_util('height', 42),
                                fontWeight: FontWeight.normal),
                            decoration: InputDecoration(
                              contentPadding: EdgeInsets.only(
                                top: mediaQuery(context, "height", 10),
                              ),
                              labelText: AppLocalizations.of(context)!
                                  .translate('favorite_address'),
                              suffix: new Container(
                                  margin: EdgeInsets.only(
                                      left: mediaQuery(context, 'width', 35)),
                                  height: mediaQuery(context, 'height', 55),
                                  width: mediaQuery(context, 'width', 55),
                                  child: GestureDetector(
                                    child: new Image.asset(
                                      LikeWalletImage.icon_scan_barcode,
                                      fit: BoxFit.fill,
                                    ),
                                    onTap: () {
                                      scanAdd();
                                    },
                                  )),
                              labelStyle: TextStyle(
                                  color: Color(0xff707071),
                                  fontFamily: AppLocalizations.of(context)!
                                      .translate('font1'),
                                  fontSize: MediaQuery.of(context).size.height *
                                      Screen_util('height', 35),
                                  fontWeight: FontWeight.normal),
                            ),
                          ),
                        ),
                        SizedBox(
                          height: MediaQuery.of(context).size.height *
                              Screen_util("height", 50),
                        ),
                        Align(
                            alignment: Alignment.centerRight,
                            child: GestureDetector(
                                onTap: () {
                                  _validateInputs(_imageFavorite);
                                },
                                child: Container(
                                    alignment: Alignment.center,
                                    height: MediaQuery.of(context).size.height *
                                        Screen_util("height", 223),
                                    width: MediaQuery.of(context).size.height *
                                        Screen_util("height", 223),
                                    decoration: new BoxDecoration(
                                      color: Color(0xff2B3038),
                                      shape: BoxShape.circle,
                                    ),
                                    child: Stack(
                                      alignment: Alignment.center,
                                      children: <Widget>[
                                        Text(
                                            AppLocalizations.of(context)!
                                                .translate('favorite_ok'),
                                            style: TextStyle(
                                              fontFamily:
                                                  AppLocalizations.of(context)!
                                                      .translate('font1'),
                                              color: Color(0xffB4E60D),
                                              fontWeight: FontWeight.bold,
                                              fontSize: MediaQuery.of(context)
                                                      .size
                                                      .height *
                                                  Screen_util("height", 35),
                                            )),
                                      ],
                                    ))))
                      ]),
                    ),
                  ),
                ),
              ),
            ],
          );
        });
  }

  void _validateInputs(File? _imageFavorite) {
    if (formKey.currentState!.validate()) {
//    If all data are correct then save data to out variables
//       print(_imageFavorite);
      if (_imageFavorite != null) {
        _saving = true;
        upload(_imageFavorite);
        Navigator.of(context).pop();
//        addFavorite();
      } else {
        _saving = true;
        Navigator.of(context).pop();
        addFavorite();
      }
      formKey.currentState!.context;
    } else {
//    If all data are not valid then start auto validation.
      setState(() {
        _autoValidate = true;
      });
    }
  }

//-----upload  photo
  upload(File file) async {
    if (file == null) return ['false'];
    String base64Image = base64Encode(file.readAsBytesSync());
    String fileName = file.path.split("/").last;
    var url = Uri.https(env.apiCheck, '/uploadBinary');
    final response = await http.post(url, body: {
      "image": base64Image,
      "name": fileName,
    });
    if (response.statusCode == 413) {
      showColoredToast('Image file is too large');
    }
    print(response.statusCode);
    final body = json.decode(response.body);
    print(body["result"]);
    print(body["result"]["id"]);
    print(body["result"]["url"]);
    setState(() {
      photoFavorite = body["result"]["url"];
    });

    await addFavorite();
  }

//-----show preview photo from add favorite
  Future preview() async {
    var image = await _picker.getImage(source: ImageSource.gallery);
    setState(() {
      _imageFavorite = File(image!.path);
    });
    myFocusNode.dispose();
//    myFocusNode.requestFocus();

//    Navigator.push(context, CropImage())

//     Navigator.of(context).pop();
//    Navigator.of(context).popUntil((route) => route.isFirst);
//     await AddFavortieForm(context);
  }

//-----add list favorite
  Future addFavorite() async {
    print("manzer" + uid);
    fireStore
        .collection('favorites')
        .doc(uid)
        .collection('transfer')
        .doc()
        .set({
      'nameFavorite': nameFavorite.text,
      'addressFavorite': addressFavorite.text,
      'photoFavorite': photoFavorite.toString(),
    });
    nameFavorite.clear();
    addressFavorite.clear();
    _imageFavorite = null;
    _saving = false;
  }

//-----get uid
  getFavorites() async {
    //เรียก API รายการโปรด
    auth.getCurrentUser().then((decodeToken) {
      print(decodeToken);
      setState(() {
        uid = decodeToken!.uid;
      });
      print(uid);
    });
  }

//-----delete list favorites
  deleteFavorites(bool value) {
    setState(() {
      delete = value;
      print(delete);
    });
  }

//-----send address to textfield
  void sendAddress(address) async {
    print(address);
    setState(() {
      phoneNumber.text = '';
      addressText.text = address;
      selected = 'address';
      toAddress = address;
      showfavorite = false;
    });
  }

  void pasteAddressFavorite() async {
    ClipboardData? data = await Clipboard.getData('text/plain');
    addressFavorite.text = data!.text!;
  }

/* ----------------|Open QR Code |--------------*/
  Future scan() async {
    addressFavorite.text = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => scanPay()),
    );
  }

/* ----------------|Open QR Code  Address|--------------*/
  Future scanAdd() async {
    addressFavorite.text = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => scanAddress()),
    );
  }

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return SingleChildScrollView(
        key: _scaffoldKey,
        child: GestureDetector(
            onTap: () {
              FocusScopeNode currentFocus = FocusScope.of(context);
              if (!currentFocus.hasPrimaryFocus) {
                currentFocus.unfocus();
              }
              setState(() {
                showsymbol = false;
                showfavorite = false;
              });
            },
            child: Stack(
              alignment: Alignment.topCenter,
              children: <Widget>[
                Container(
                  padding: EdgeInsets.only(
                    top: mediaQuery(context, 'height', 300),
                  ),
                  height: mediaQuery(context, 'height', 1770),
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                    color: Color(0xffF5F5F5),
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      stops: [0.0, 0.2, 0.5],
                      colors: [
                        // Colors are easy thanks to Flutter's Colors class.
                        Colors.white,
                        Colors.white,
                        Color(0xffF5F5F5)
                      ],
                    ),
                  ),
                  child: new Column(
                    children: <Widget>[
                      /// controller Scan | Button Favorites | Pates
                      _head(),
                      SizedBox(height: mediaQuery(context, 'height', 17)),

                      /// Input Address or Number phone
                      selected == null
                          ? _selectInput()
                          : selected == 'address'
                              ? _inputAddress()
                              : selected == 'phone'
                                  ? _inputNumber()
                                  : _selectInput(),
                      SizedBox(
                        height: mediaQuery(context, 'height', 8),
                      ),

                      ///Text Amount
                      Container(
                          margin: EdgeInsets.only(
                              top: mediaQuery(context, 'height', 0)),
                          height: mediaQuery(context, 'height', 719),
                          width: mediaQuery(context, 'width', 945.66),
                          child: Stack(
                            children: <Widget>[
                              Positioned(
                                top: mediaQuery(context, 'height', 30),
                                left: mediaQuery(context, 'width', 0),
                                child: _amountLike(),
                              ),
                              Positioned(
                                top: mediaQuery(context, 'height', 340),
                                left: mediaQuery(context, 'width', 0),
                                child: _exchangeRate(),
                              ),
                              Positioned(
                                top: mediaQuery(context, 'height', 238),
                                left: mediaQuery(context, 'width', 440),
                                child: _iconEqual(),
                              ),
                              // Positioned(
                              //   bottom: mediaQuery(context, 'height', 0),
                              //   left: mediaQuery(context, 'width', 0),
                              //   child: _fee(),
                              // ),
//                                _showSymbol()
                            ],
                          )),
                      syncContact && !showbutton ? _getContactList() : Container(),
                    ],
                  ),
                ),

                ///-------- AnimatedPositioned UP & DOWN--------//

                _buttonNext(),

                ///-------- Button Back Page to Store--------//

                _buttonBackStore(),

                ///-------- AnimatedPositioned UP & DOWN--------//
                _showSymbol(),

                ///-------- AnimatedPositioned UP & DOWN--------//
                _favoriteForm(),

                actionSyncContact ? Container() : Container(
                  margin: EdgeInsets.only(top:  300.sp),
                  child: dialogContent(context),
                )
              ],
            )));
  }

  dialogContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
                decoration: BoxDecoration(
                  borderRadius: new BorderRadius.only(
                    topLeft: Radius.circular(
                      mediaQuery(context, 'h', 50),
                    ),
                    topRight: Radius.circular(
                      mediaQuery(context, 'h', 50),
                    ),
                    bottomRight: Radius.circular(
                      mediaQuery(context, 'h', 50),
                    ),
                    bottomLeft: Radius.circular(
                      mediaQuery(context, 'h', 50),
                    ),
                  ),
                  gradient: LinearGradient(
                    begin: Alignment.bottomCenter,
                    end: Alignment.topCenter,
                    stops: [0, 1.0],
                    colors: [
                      Color(0xff505DFF),
                      Color(0xff3948FD),
                    ],
                  ),
                ),
                width: mediaQuery(context, 'width', 989),
                child: Column(
                  children: [
                    Container(
                      margin: EdgeInsets.only(top: 100.sp),
                      child: Image.asset(
                        LikeWalletImage.contactList,
                        fit: BoxFit.fitWidth,
                        // height: 1342.h,
                        width: 389.w,
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.all(50.sp),
                      child:  Text(
                        AppLocalizations.of(context)!
                            .translate('contact_list_title'),
                        style: TextStyle(
                          fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                          fontSize:  45.h,
                          color: const Color(0xffffffff),
                          fontWeight: FontWeight.w300,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.only(left: 70.sp, right: 70.sp),
                      child:  Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          InkWell(
                            onTap: () async {
                              getContacts(context: context);
                              SharedPreferences pref = await SharedPreferences.getInstance();
                              pref.setBool("syncContact", true);
                              setState(() {
                                syncContact = true;
                                actionSyncContact = true;
                              });
                            },
                            child: Container(
                                width: 100,
                                height: 40,
                                decoration: BoxDecoration(
                                    color: Colors.transparent,
                                    border: Border.all(
                                      color: Colors.green,
                                    ),
                                    borderRadius: BorderRadius.all(Radius.circular(20))
                                ),
                                child: Center(
                                    child: Text(
                                      AppLocalizations.of(context)!
                                          .translate('contact_import'),
                                      style: TextStyle(
                                        color: Colors.green,
                                        fontSize:  35.h,
                                        fontFamily:
                                        AppLocalizations.of(context)!.translate('font1'),
                                      ),
                                    ))),
                          ),
                          InkWell(
                            onTap: () async {
                              SharedPreferences pref = await SharedPreferences.getInstance();
                              pref.setBool("syncContact", false);
                              setState(() {
                                syncContact = false;
                                actionSyncContact = true;
                              });

                            },
                            child: Container(
                                width: 100,
                                height: 40,
                                decoration: BoxDecoration(
                                    color: Colors.transparent,
                                    border: Border.all(
                                      color: Colors.red,
                                    ),
                                    borderRadius: BorderRadius.all(Radius.circular(20))
                                ),
                                child: Center(
                                    child: Text(
                                      AppLocalizations.of(context)!
                                          .translate('contact_disable'),
                                      style: TextStyle(
                                        color: Colors.red,
                                        fontSize:  35.h,
                                        fontFamily:
                                        AppLocalizations.of(context)!.translate('font1'),
                                      ),
                                    ))),
                          )
                        ],
                      ),
                    ),
                    SizedBox(
                      height: 117.h,
                    ),
                  ],
                )),
          ],
        )
      ],
    );
  }

  List<Widget> listContact = [];

  Widget _getContactList() {
    return shopID != null
        ? Container()
        : Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    padding: EdgeInsets.only(left: 75.w),
                    alignment: Alignment.centerLeft,
                    child: Text(
                      'QUICK CONTACTS',
                      style: TextStyle(
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                        fontSize: 33.h,
                        color: const Color(0xff3c3c43),
                        letterSpacing: 0.99,
                      ),
                      textAlign: TextAlign.left,
                    ),
                  ),
                  SizedBox(
                    height: 50.h,
                  ),
                  InkWell(
                    onTap: () {
                      // if (_contacts != null) {
                      showBarModalBottomSheet(
                        expand: true,
                        context: context,
                        backgroundColor: Colors.transparent,
                        builder: (context) => ModalInsideModal(
                          contacts: getContactMobile,
                        ),
                      );
                      // } else {
                      //   showColoredToast('กำลังรอข้อมูล');
                      // }
                    },
                    // onTap: () async {
                    // Navigator.push(
                    //     context,
                    //     MaterialPageRoute(
                    //         builder: (context) =>
                    //             ContactsExampleApp()));
                    // final data =
                    //     await contact.getContacts(context: context);
                    // },
                    child: Container(
                      margin: EdgeInsets.only(right: 70.w),
                      height: 30.35.h,
                      width: 30.35.h,
                      child: Transform.rotate(
                        angle: 3.1,
                        alignment: Alignment.center, //origin: Offset(100, -100)
                        child: SvgPicture.string(
                          '<svg viewBox="-281.5 3213.3 24.5 36.3" ><path  d="M -259.5249938964844 3249.675048828125 C -260.1029968261719 3249.675048828125 -260.6839904785156 3249.47607421875 -261.156005859375 3249.068115234375 L -281.5329895019531 3231.508056640625 L -261.156005859375 3213.947021484375 C -260.1109924316406 3213.0458984375 -258.531005859375 3213.162109375 -257.6300048828125 3214.208984375 C -256.72900390625 3215.2548828125 -256.8460083007812 3216.8330078125 -257.8919982910156 3217.73388671875 L -273.8739929199219 3231.508056640625 L -257.8919982910156 3245.281005859375 C -256.8460083007812 3246.18310546875 -256.72900390625 3247.760986328125 -257.6300048828125 3248.806884765625 C -258.125 3249.381103515625 -258.822998046875 3249.675048828125 -259.5249938964844 3249.675048828125 Z" fill="#231f20" stroke="none" stroke-width="1" stroke-miterlimit="10" stroke-linecap="butt" /></svg>',
                          allowDrawingOutsideViewBox: true,
                          fit: BoxFit.fitHeight,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              Container(
                margin: EdgeInsets.only(left: 75.h - 24.h),
                height: 300.h,
                width: MediaQuery.of(context).size.width,
                child: contactLoading
                    ? Center(
                        child: SpinKitFadingCircle(
                        color: LikeWalletAppTheme.bule1,
                        size: 150.h,
                      ))
                    : getContactMobile.length != 0
                        //Build a list view of all contacts, displaying their avatar and
                        // display name
                        ? ListView.builder(
                            // physics: NeverScrollableScrollPhysics(),
                            itemCount: getContactMobile.length,
                            scrollDirection: Axis.horizontal,
                            itemBuilder: (BuildContext context, int index) {
                              print(getContactMobile.length);
                              // Contact contact = _contacts?.elementAt(index);
                              return InkWell(
                                onTap: () {
                                  if (getContactMobile[index].address != '') {
                                    Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                            builder: (context) =>
                                                ContactDetails(
                                                  name: getContactMobile[index]
                                                          .name ??
                                                      '',
                                                  phone: getContactMobile[index]
                                                          .phoneNumber ??
                                                      '',
                                                  address:
                                                      getContactMobile[index]
                                                              .address ??
                                                          '',
                                                  color: getContactMobile[index]
                                                      .color,
                                                )));
                                  } else {
                                    print('ไม่พบ Address');
                                    showShortToast('ไม่พบ Address', Colors.red);
                                  }
                                },
                                child: Container(
                                  margin: EdgeInsets.only(
                                      left: 24.w, right: 24.w, top: 24.h),
                                  // width: 155.w,
                                  height: MediaQuery.of(context).size.height,
                                  child: Column(
                                    children: [
                                      Container(
                                        height: 144.h,
                                        width: 155.w,
                                        child: Stack(
                                          children: [
                                            Container(
                                              alignment: Alignment.center,
                                              height: 132.h,
                                              width: 155.w,
                                              decoration: BoxDecoration(
                                                color: getContactMobile[index]
                                                    .color,
                                                borderRadius:
                                                    BorderRadius.circular(
                                                        100.0.h),
                                              ),
                                              child: Text(
                                                  getContactMobile[index]
                                                      .name
                                                      .substring(0, 1)
                                                      .toString()),
                                            ),
                                            Positioned(
                                              bottom: 0,
                                              right: 12.w,
                                              child: Image.asset(
                                                LikeWalletImage.iconLikeWallet,
                                                height: 60.h,
                                                width: 60.h,
                                              ),
                                            )
                                          ],
                                        ),
                                      ),
                                      SizedBox(height: 21.h),
                                      Row(
                                        children: [
                                          Container(
                                            width: 155.h,
                                            child: Text(
                                              getContactMobile[index].name,
                                              style: TextStyle(
                                                fontFamily: AppLocalizations.of(
                                                        context)!
                                                    .translate('font1'),
                                                fontSize: 27.h,
                                                color: const Color(0xcc3c3c43),
                                                letterSpacing:
                                                    0.8099999999999999.w,
                                                height: 1.1111111111111112,
                                              ),
                                              textHeightBehavior:
                                                  TextHeightBehavior(
                                                      applyHeightToFirstAscent:
                                                          false),
                                              textAlign: TextAlign.center,
                                            ),
                                          ),
                                          // Text(
                                          //   _contacts[index].phoneNumber,
                                          //   style: TextStyle(
                                          //     fontFamily: AppLocalizations.of(context)
                                          //         .translate('font1'),
                                          //     fontSize: 27.h,
                                          //     color: const Color(0xcc3c3c43),
                                          //     letterSpacing: 0.8099999999999999.w,
                                          //     height: 1.1111111111111112,
                                          //   ),
                                          //   textHeightBehavior: TextHeightBehavior(
                                          //       applyHeightToFirstAscent: false),
                                          //   textAlign: TextAlign.center,
                                          // ),
                                        ],
                                      )
                                    ],
                                  ),
                                ),
                              );
                            },
                          )
                        : Center(
                            child: Text(
                              'ไม่พบข้อมูล',
                              style: TextStyle(
                                fontFamily: AppLocalizations.of(context)!
                                    .translate('font1'),
                                fontSize: 36.h,
                                color: const Color(0xcc3c3c43),
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),

                // Center(
                //         child: Text(
                //           AppLocalizations.of(context)
                //               .translate('not_contact_available'),
                //           style: TextStyle(
                //             fontFamily:
                //                 AppLocalizations.of(context)!.translate('font1'),
                //             fontSize: 39.h,
                //             color: const Color(0xff3c3c43),
                //             letterSpacing: 0.99,
                //           ),
                //           textAlign: TextAlign.left,
                //         ),
                //       ),
              ),
            ],
          );
  }

  /// controller Scan | Button Favorites | Pates
  Widget _head() {
    return Container(
      width: mediaQuery(context, 'width', 930),
      child: new Row(
        children: <Widget>[
          new Container(
            height: mediaQuery(context, 'height', 120),
            alignment: Alignment.bottomCenter,
            child: new Text(
              AppLocalizations.of(context)!.translate('bankingSend_to'),
              style: LikeWalletAppTheme.textStyle(context, 40,
                  LikeWalletAppTheme.gray1, FontWeight.normal, 'font1'),
            ),
          ),
          Expanded(
            child: Container(),
          ),
          new Container(
              height: mediaQuery(context, 'height', 120),
              width: mediaQuery(context, 'width', 155),
              child: GestureDetector(
                child: new Image.asset(
                  LikeWalletImage.icon_scan_barcode,
                  scale: 3.8,
                ),
                onTap: () {
                  scan();
                },
              )),
          new Container(
              height: mediaQuery(context, 'height', 102),
              width: mediaQuery(context, 'height', 155),
              alignment: Alignment.center,
              child: GestureDetector(
                child: new Image.asset(
                  LikeWalletImage.icon_favorite_black,
                  height: mediaQuery(context, 'height', 102),
                  width: mediaQuery(context, 'height', 102),
                ),
                onTap: () => setState(() {
                  deleteFavorites(false);
                  DeviceUtils.hideKeyboard(context);
                  showfavorite ? showfavorite = false : showfavorite = true;
                }),
              )),
          new Container(
            width: mediaQuery(context, 'width', 155),
            alignment: Alignment.center,
            child: new GestureDetector(
              onTap: () {
                pasteAddress();
              },
              child: ClipOval(
                child: Container(
                  alignment: Alignment.center,
                  color: Color(0xffDEDEDE).withOpacity(1),
                  height: mediaQuery(context, 'height', 102),
                  // height of the button
                  width: mediaQuery(context, 'height', 102),
                  // width of the button
                  child: new Text(
                    AppLocalizations.of(context)!
                        .translate('bankingSend_to_paste'),
                    style: LikeWalletAppTheme.textStyle(context, 30,
                        LikeWalletAppTheme.black, FontWeight.normal, 'font1'),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  ///ที่อยู่กระเป่า
  Widget _inputAddress() {
    return Container(
        decoration: BoxDecoration(
          border: Border.all(
              width: mediaQuery(context, 'width', 0.3),
              color: LikeWalletAppTheme.gray),
          borderRadius: new BorderRadius.circular(5.0),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: LikeWalletAppTheme.black.withOpacity(0.1),
              spreadRadius: 1,
              blurRadius: 3,
              offset: Offset(0, 0), // changes position of shadow
            ),
          ],
        ),
        alignment: Alignment.centerLeft,
        height: mediaQuery(context, 'height', 115),
        width: mediaQuery(context, 'width', 935.38),
        child: Row(
          children: <Widget>[
            Expanded(
              child: Padding(
                padding:
                    EdgeInsets.only(left: mediaQuery(context, 'width', 50)),
                child: TextFormField(
                  onChanged: (value) {
                    if (value != "") {
                      setState(() {
                        // addressText.text = value;
                        toAddress = value;
                        showbutton = true;
                      });
                    } else {
                      setState(() {
                        showbutton = false;
                      });
                    }
                  },
                  controller: addressText,
                  style: LikeWalletAppTheme.textStyle(context, 42,
                      LikeWalletAppTheme.gray1, FontWeight.w100, 'font1'),
                  keyboardType: TextInputType.text,
                  textAlignVertical: TextAlignVertical.center,
                  decoration: InputDecoration(
                    isDense: true,
//                    contentPadding: new EdgeInsets.symmetric(
//                        vertical: .0, horizontal: 10.0),
                    focusedBorder: InputBorder.none,
//                  contentPadding:
//                      EdgeInsets.only(left: mediaQuery(context, 'width', 50)),
//                border: OutlineInputBorder(
//                    borderRadius: BorderRadius.circular(5.0)),
                    hintText: AppLocalizations.of(context)!
                        .translate('bankingSend_to_address'),
                    hintStyle: LikeWalletAppTheme.textStyle(context, 42,
                        LikeWalletAppTheme.gray1, FontWeight.w100, 'font1'),
//              fillColor: Colors.white,
                    border: InputBorder.none,
                  ),
                ),
              ),
            ),
            GestureDetector(
                onTap: () {
                  setState(() {
                    addressText.text = '';
                    toAddress = 'noaddress';
                  });
                  setSelectInput(null);
                },
                child: Container(
                  height: mediaQuery(context, 'height', 35),
                  margin:
                      EdgeInsets.only(right: mediaQuery(context, 'width', 40)),
                  child: Image.asset(
                    LikeWalletImage.icon_back,
                  ),
                ))
          ],
        ));
  }

  ///เบอร์โทร
  Widget _inputNumber() {
    return Container(
        decoration: BoxDecoration(
          border: Border.all(
              width: mediaQuery(context, 'width', 0.3),
              color: LikeWalletAppTheme.gray),
          borderRadius: new BorderRadius.circular(5.0),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: LikeWalletAppTheme.black.withOpacity(0.1),
              spreadRadius: 1,
              blurRadius: 3,
              offset: Offset(0, 0), // changes position of shadow
            ),
          ],
        ),
        alignment: Alignment.centerLeft,
        height: mediaQuery(context, 'height', 115),
        width: mediaQuery(context, 'width', 935.38),
        child: Row(
          children: <Widget>[
            Container(
              decoration: BoxDecoration(
                color: LikeWalletAppTheme.gray6,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(5.0),
                    bottomLeft: Radius.circular(5.0)),
              ),
              width: mediaQuery(context, 'width', 193.36),
              alignment: Alignment.center,
              child: DropdownButtonHideUnderline(
                child: DropdownButton<String>(
                  value: dropdownValue,
                  elevation: 15,
                  style: TextStyle(color: Color(0xffADACA0)),
                  iconEnabledColor: LikeWalletAppTheme.black,
                  icon: Icon(
                    Icons.keyboard_arrow_down,
                    size: mediaQuery(context, 'height', 40),
                  ),
                  iconDisabledColor: LikeWalletAppTheme.black,
                  onChanged: (String? newValue) {
                    setState(() {
                      dropdownValue = newValue.toString();
                    });
                  },
                  items: <String>['+66', '+855', '+856']
                      .map<DropdownMenuItem<String>>((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(
                        value,
                        style: TextStyle(
                          color: LikeWalletAppTheme.black,
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
            Expanded(
              child: Padding(
                padding:
                    EdgeInsets.only(left: mediaQuery(context, 'width', 50)),
                child: TextFormField(
                  onChanged: (value) {
                    if (value != "") {
                      setState(() {
                        showbutton = true;
                      });
                    } else {
                      setState(() {
                        showbutton = false;
                      });
                    }
                  },
                  controller: phoneNumber,
                  style: LikeWalletAppTheme.textStyle(context, 42,
                      LikeWalletAppTheme.gray1, FontWeight.w100, 'font1'),
                  keyboardType: TextInputType.phone,
                  textAlignVertical: TextAlignVertical.center,
                  inputFormatters: [
                    LengthLimitingTextInputFormatter(15),
                  ],
                  decoration: InputDecoration(
                    isDense: true,
                    focusedBorder: InputBorder.none,
                    hintText: AppLocalizations.of(context)!
                        .translate('bankingSend_to_mobile_number'),
                    hintStyle: LikeWalletAppTheme.textStyle(context, 42,
                        LikeWalletAppTheme.gray1, FontWeight.w100, 'font1'),
//              fillColor: Colors.white,
                    border: InputBorder.none,
                  ),
                ),
              ),
            ),
            GestureDetector(
                onTap: () {
                  setState(() {
                    phoneNumber.text = '';
                    toAddress = 'noaddress';
                  });
                  setSelectInput(null);
                },
                child: Container(
                  height: mediaQuery(context, 'height', 35),
                  margin:
                      EdgeInsets.only(right: mediaQuery(context, 'width', 40)),
                  child: Image.asset(
                    LikeWalletImage.icon_back,
                  ),
                ))
          ],
        ));
  }

  ///ช่องกรอกจำนวน
  Widget _amountLike() {
    return Container(
      padding: EdgeInsets.only(
        left: mediaQuery(context, 'width', 0),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: <Widget>[
          Container(
            padding: EdgeInsets.only(
              top: mediaQuery(context, 'height', 20),
              left: mediaQuery(context, 'width', 56),
              bottom: mediaQuery(context, 'height', 20),
            ),
            width: mediaQuery(context, 'width', 671),
            height: mediaQuery(context, 'height', 290),
            decoration: BoxDecoration(
              borderRadius:
                  BorderRadius.circular(mediaQuery(context, 'height', 16)),
              color: LikeWalletAppTheme.white,
              boxShadow: [
                BoxShadow(
                  color: const Color(0x1a000000),
                  offset: Offset(1, 1),
                  blurRadius: 7,
                  spreadRadius: 1,
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Expanded(
//                  margin:
//                      EdgeInsets.only(bottom: mediaQuery(context, 'height', 0)),
                  child: Text(
                    AppLocalizations.of(context)!
                        .translate('bankingSend_to_amount'),
                    style: TextStyle(
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      fontSize: mediaQuery(context, 'height', 36),
                      color: LikeWalletAppTheme.gray4.withOpacity(0.3),
                      letterSpacing: 0.3,
                    ),
                    textAlign: TextAlign.left,
                  ),
                ),
                Container(
                  alignment: Alignment.centerLeft,
                  height: mediaQuery(context, 'height', 153),
                  child: TextFormField(
                    textAlignVertical: TextAlignVertical.center,
                    textAlign: TextAlign.start,
                    style: TextStyle(
                        fontSize: mediaQuery(context, 'height', 83),
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font2'),
                        color: LikeWalletAppTheme.gray1),
                    keyboardType: TextInputType.number,
                    controller: amountSend,
                    focusNode: amountFocusNode,
                    decoration: InputDecoration(
                      focusedBorder: InputBorder.none,
                      enabledBorder: InputBorder.none,
                      hintText: '0',
                      hintStyle: TextStyle(
                          fontSize: mediaQuery(context, 'height', 83),
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font2'),
                          color: LikeWalletAppTheme.gray1),
//                      contentPadding: EdgeInsets.only(
//                        bottom: mediaQuery(context, 'height', 20),
//                      ),
                    ),
                  ),
                ),
                Expanded(
//                  margin:
//                      EdgeInsets.only(top: mediaQuery(context, 'height', 0)),
                  child: Text(
                    Symbol == 'THB'
                        ? AppLocalizations.of(context)!.translate('symbol_th')
                        : Symbol == 'USD'
                            ? AppLocalizations.of(context)!
                                .translate('symbol_us')
                            : Symbol == 'LAK'
                                ? AppLocalizations.of(context)!
                                    .translate('symbol_lak')
                                : Symbol == 'VND'
                                    ? AppLocalizations.of(context)!
                                        .translate('symbol_vn')
                                    : Symbol == 'GOLD'
                                        ? AppLocalizations.of(context)!
                                            .translate('symbol_gold')
                                        : Symbol == 'LIKE'
                                            ? AppLocalizations.of(context)!
                                                .translate('symbol_like')
                                            : AppLocalizations.of(context)!
                                                .translate('symbol_th'),
                    style: TextStyle(
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      fontSize: mediaQuery(context, 'height', 33),
                      color: LikeWalletAppTheme.gray4.withOpacity(0.3),
                      letterSpacing: 0.3,
                    ),
                    textAlign: TextAlign.left,
                  ),
                ),
              ],
            ),
          ),
          Container(
              padding: EdgeInsets.only(
                left: mediaQuery(context, 'width', 40),
              ),
              child: _switchSymbol())
        ],
      ),
    );
  }

  ///ช่องกรอกจำนวน
  Widget _exchangeRate() {
    return Container(
      padding: EdgeInsets.only(
        left: mediaQuery(context, 'width', 0),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: <Widget>[
          Container(
            width: mediaQuery(context, 'width', 671),
            height: mediaQuery(context, 'height', 290),
            decoration: BoxDecoration(
              borderRadius:
                  BorderRadius.circular(mediaQuery(context, 'height', 16)),
              color: LikeWalletAppTheme.white,
              boxShadow: [
                BoxShadow(
                  color: const Color(0x1a000000),
                  offset: Offset(1, 1),
                  blurRadius: 7,
                  spreadRadius: 1,
                ),
              ],
            ),
            child: Stack(
//              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Positioned(
                  left: mediaQuery(context, 'width', 56),
                  top: mediaQuery(context, 'height', 15),
                  child: Container(
                    margin: EdgeInsets.only(
                        bottom: mediaQuery(context, 'height', 10)),
                    child: Text(
                      AppLocalizations.of(context)!
                          .translate('banking_send_equal'),
                      style: TextStyle(
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                        fontSize: mediaQuery(context, 'height', 36),
                        color: LikeWalletAppTheme.gray4.withOpacity(0.3),
                        letterSpacing: 0.3,
                      ),
                      textAlign: TextAlign.left,
                    ),
                  ),
                ),
                Positioned(
                  left: mediaQuery(context, 'width', 56),
                  top: mediaQuery(context, 'height', 68.5),
                  child: Container(
                      alignment: Alignment.centerLeft,
                      height: mediaQuery(context, 'height', 153),
                      child: Text(
                        amountValue == ''
                            ? '0'
                            : validateFirstNumber(amountValue) == true
                                ? f
                                    .format((double.parse(amountValue) *
                                        rateCurrency *
                                        rate))
                                    .toString()
                                : amountValue.substring(0, 1) == '.'
                                    ? f.format(
                                        (double.parse('0' + amountValue) *
                                            rateCurrency *
                                            rate))
                                    : '0',
                        style: TextStyle(
                            fontSize: mediaQuery(context, 'height', 83),
                            fontFamily: AppLocalizations.of(context)!
                                .translate('font2'),
                            color: LikeWalletAppTheme.gray1),
                      )),
                ),
                Positioned(
                  left: mediaQuery(context, 'width', 56),
                  bottom: mediaQuery(context, 'height', 15),
                  child: Container(
                    child: Text(
                      AppLocalizations.of(context)!.translate('cash_likepoint'),
                      style: TextStyle(
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                        fontSize: mediaQuery(context, 'height', 33),
                        color: LikeWalletAppTheme.gray4.withOpacity(0.3),
                        letterSpacing: 0.3,
                      ),
                      textAlign: TextAlign.left,
                    ),
                  ),
                ),
                Positioned(
                  right: mediaQuery(context, 'width', 45),
                  bottom: mediaQuery(context, 'height', 15),
                  child: Container(
                    padding: EdgeInsets.only(
                      left: mediaQuery(context, 'width', 40),
                    ),
                    child: Text(
                      'LIKE',
                      style: TextStyle(
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font2'),
                        fontSize: mediaQuery(context, 'height', 50),
                        color: LikeWalletAppTheme.black,
                        fontWeight: FontWeight.w500,
                        letterSpacing: 0.3,
                      ),
                      textAlign: TextAlign.left,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _iconEqual() {
    return Container(
        padding: EdgeInsets.only(
          left: mediaQuery(context, 'width', 0),
        ),
        child: Image.asset(
          LikeWalletImage.icon_equal,
          height: mediaQuery(context, 'height', 228),
        ));
  }

//  ///ช่องกรอกจำนวน
//  Widget _inputLike() {
//    return new Container(
//      padding: EdgeInsets.only(
//        left: mediaQuery(context, 'width', 75),
//        right: mediaQuery(context, 'width', 40),
//      ),
//      child: new Column(
//        mainAxisAlignment: MainAxisAlignment.start,
//        crossAxisAlignment: CrossAxisAlignment.start,
//        children: <Widget>[
//          Row(
//            children: <Widget>[
//              new Container(
//                margin: EdgeInsets.only(),
//                decoration: BoxDecoration(
//                  border: Border.all(
//                      width: mediaQuery(context, 'width', 0.3),
//                      color: LikeWalletAppTheme.gray),
//                  borderRadius: new BorderRadius.circular(5.0),
//                  color: Colors.white,
//                  boxShadow: [
//                    BoxShadow(
//                      spreadRadius: 0,
//                      blurRadius: 2,
//                      color: Color(0xff707071).withOpacity(0.5),
//                      offset: Offset(
//                        0.0,
//                        1.0,
//                      ),
//                    ),
//                  ],
//                ),
//                alignment: Alignment.centerLeft,
//                height: mediaQuery(context, 'height', 161),
//                width: mediaQuery(context, 'width', 671),
//                child: TextFormField(
//                  controller: amountSend,
//                  style: LikeWalletAppTheme.textStyle(context, 83,
//                      LikeWalletAppTheme.gray1, FontWeight.w100, 'font2'),
//                  keyboardType: TextInputType.number,
//                  focusNode: amountFocusNode,
//                  decoration: InputDecoration(
//                    contentPadding:
//                        EdgeInsets.only(left: mediaQuery(context, 'width', 50)),
//                    focusedBorder: InputBorder.none,
//                    border: InputBorder.none,
//                    hintText: '0.00',
//                    hintStyle: LikeWalletAppTheme.textStyle(context, 83,
//                        LikeWalletAppTheme.black, FontWeight.w100, 'font2'),
//                    fillColor: Colors.white,
//                  ),
////                  onFieldSubmitted: (value) {
////                    print(value);
////                    if (value.isNotEmpty) {
////                      setState(() {
////                        showcurrency = true;
////                        showbutton = true;
////                      });
////                    } else {
////                      setState(() {
////                        showcurrency = false;
////                        showsymbol = false;
////                        showbutton = false;
////                      });
////                    }
////                  },
////                    showcurrency
//                ),
//              ),
//            ],
//          ),
//        ],
//      ),
//    );
//  }

  ///เเสดงสกุลที่เลือก ตั้งค่าทเริ่มที่ THB
  Widget _switchSymbol() {
    return GestureDetector(
        onTap: () {
          setState(() {
            showsymbol = !showsymbol;
            print(showsymbol);
            showcurrency = false;
            showbutton = true;
          });
        },
        child: Container(
          margin: EdgeInsets.only(left: mediaQuery(context, 'width', 0)),
          height: mediaQuery(context, 'height', 161),
          width: mediaQuery(context, 'width', 270),
          child: Row(
            children: <Widget>[
              Text(
                Symbol,
                style: TextStyle(
                    fontSize: mediaQuery(context, 'height', 71),
                    color: LikeWalletAppTheme.black,
                    fontWeight: FontWeight.w700,
                    fontFamily:
                        AppLocalizations.of(context)!.translate('font1'),
                    shadows: [
                      Shadow(
                        blurRadius: 7,
                        color: LikeWalletAppTheme.gray.withOpacity(0.2),
                        offset: Offset(0.0, 0.0),
                      ),
                    ]),
              ),
              SizedBox(
                width: mediaQuery(context, 'width', 20),
              ),
              SvgPicture.asset(
                LikeWalletImage.icon_dropdown,
                fit: BoxFit.contain,
                height: mediaQuery(context, 'height', 23.47),
                width: mediaQuery(context, 'width', 23.64),
              ),
            ],
          ),
        ));
  }

  ///ปุ่มถัดไป
  Widget _buttonNext() {
    return AnimatedPositioned(
        top: showbutton
            ? mediaQuery(context, 'height', 1341)
            : mediaQuery(context, 'height', 2430),
        duration: Duration(milliseconds: 500),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            GestureDetector(
              onTap: () {
                setState(() {
                  showbutton = false;
                  amountSend.clear();
                  amountValue = '0';
                  showcurrency = false;
                });
              },
              child: Container(
                  width: mediaQuery(context, 'width', 930) / 2,
                  height: mediaQuery(context, 'height', 133),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: <Widget>[
                      Image.asset(
                        LikeWalletImage.icon_button_cancel_white,
                        height: mediaQuery(context, 'height', 133),
                      ),
                      Text(
                        AppLocalizations.of(context)!
                            .translate('bankingbuy_back'),
                        style: TextStyle(
                            fontFamily: 'Noto Sans',
                            fontSize: mediaQuery(context, 'height', 36),
                            color: LikeWalletAppTheme.gray.withOpacity(0.8)),
                        textAlign: TextAlign.right,
                      )
                    ],
                  )),
            ),
            GestureDetector(
              onTap: () {
                // print();
                print((double.parse(amountValue) * rateCurrency * rate));
                print(balanceLIKE);
                if ((double.parse(amountValue) * rateCurrency * rate) <=
                    balanceLIKE) {
                  print(toAddress);
                  print(selected);
                  if (selected == 'address'
                      ? addressText.text.length > 0
                          ? addressText.text.substring(0, 1) == '+'
                          : false
                      : false || selected == 'phone'
                          ? phoneNumber.text.length > 0
                              ? phoneNumber.text.substring(0, 1) == '0'
                              : false
                          : false) {
                    searchAddressFromPhone().then((address) {
                      print(address);

                      if (toAddress != 'noaddress') {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) => confirmTransection(
                                  titleName: selected == 'address'
                                      ? addressText.text.toString()
                                      : phoneNumber.text.toString(),
                                  contract: contract != null
                                      ? contract != 'no'
                                          ? contract
                                          : 'no'
                                      : 'no',
                                  addressText: toAddress.trim().toString(),
                                  amountSend: amountValue.trim().toString(),
                                  rateCurrency: rateCurrency,
                                  vending: vending,
                                  abi: contract != null
                                      ? contract != 'no'
                                          ? abi
                                          : 'no'
                                      : 'no',
                                  callFunction: contract != null
                                      ? contract != 'no'
                                          ? callFunction
                                          : 'no'
                                      : 'no')),
                        );
                      } else {
                        //toast
                        showColoredToast(AppLocalizations.of(context)!
                            .translate('please_reciever'));
                      }
                    });
                  } else {
                    if (toAddress != 'noaddress') {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => confirmTransection(
                                titleName: addressText.text.toString(),
                                addressText: toAddress.trim().toString(),
                                amountSend: amountValue.trim().toString(),
                                rateCurrency: rateCurrency,
                                contract: contract != null
                                    ? contract != 'no'
                                        ? contract
                                        : 'no'
                                    : 'no',
                                abi: contract != null
                                    ? contract != 'no'
                                        ? abi
                                        : 'no'
                                    : 'no',
                                callFunction: contract != null
                                    ? contract != 'no'
                                        ? callFunction
                                        : 'no'
                                    : 'no')),
                      );
                    } else {
                      //toast
                      showColoredToast(AppLocalizations.of(context)!
                          .translate('please_reciever'));
                    }
                  }
                } else {
                  showColoredToast(
                      AppLocalizations.of(context)!.translate('not_enough'));
                }
              },
              child: Container(
                  width: mediaQuery(context, 'width', 930) / 2,
                  height: mediaQuery(context, 'height', 133),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: <Widget>[
                      Text(
                        AppLocalizations.of(context)!
                            .translate('bankingbuy_button'),
                        style: TextStyle(
                            fontFamily: 'Noto Sans',
                            fontSize: mediaQuery(context, 'height', 36),
                            color: LikeWalletAppTheme.gray.withOpacity(0.8)),
                        textAlign: TextAlign.right,
                      ),
                      Image.asset(
                        LikeWalletImage.icon_button_next_black,
                        height: mediaQuery(context, 'height', 133),
                      ),
                    ],
                  )),
            ),

//            child: new FlatButton(
//                shape: new RoundedRectangleBorder(
//                  borderRadius: new BorderRadius.circular(8.0),
//                ),
//                disabledColor: Color(0xff141322).withOpacity(1),
//                color: Color(0xff141322).withOpacity(1),
//                onPressed: () => {
//                      changeMenu(1),
//                    },
//                child: new Text(
//                  AppLocalizations.of(context)!.translate('bankingbuy_button'),
//                  style: TextStyle(
//                      color: LikeWalletAppTheme.lemon,
//                      fontFamily:
//                          AppLocalizations.of(context)!.translate('font1Light'),
//                      fontSize: mediaQuery(context, 'height', 59),
//                      fontWeight: FontWeight.w100),
//                )),
          ],
        ));
//        child: Container(
//          alignment: Alignment.center,
//          child: ButtonTheme(
//            minWidth: mediaQuery(context, 'width', 930),
//            height: mediaQuery(context, 'height', 132),
//            child: new FlatButton(
//                shape: new RoundedRectangleBorder(
//                  borderRadius: new BorderRadius.circular(8.0),
//                ),
//                disabledColor: LikeWalletAppTheme.bule2,
//                color: LikeWalletAppTheme.bule2,
//                onPressed: () {
//                  print(toAddress);
//                  if (toAddress != 'noaddress') {
//                    Navigator.push(
//                      context,
//                      MaterialPageRoute(
//                          builder: (context) => confirmTransection(
//                              titleName: addressText.text.toString(),
//                              addressText: toAddress.trim().toString(),
//                              amountSend: amountValue.trim().toString(),
//                              rateCurrency: rateCurrency,
//                              contract: contract != null
//                                  ? contract != 'no' ? contract : 'no'
//                                  : 'no',
//                              abi: contract != null
//                                  ? contract != 'no' ? abi : 'no'
//                                  : 'no',
//                              callFunction: contract != null
//                                  ? contract != 'no' ? callFunction : 'no'
//                                  : 'no')),
//                    );
//                  } else {
//                    //toast
//                    showColoredToast(AppLocalizations.of(context)
//                        .translate('please_reciever'));
//                  }
//
////                                      changeMenu(5),
//                },
//                child: new Text(
//                  AppLocalizations.of(context)
//                      .translate('bankingSend_to_button_send'),
//                  style: LikeWalletAppTheme.textStyle(context, 59,
//                      LikeWalletAppTheme.lemon, FontWeight.w100, 'font1Light'),
//                )),
//          ),
//        ));
  }

  ///-------- AnimatedPositioned UP & DOWN--------//

  _buttonBackStore() {
    return shopID == null
        ? Container()
        : Positioned(
            top: mediaQuery(context, 'height', 1500),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                GestureDetector(
                  onTap: () {
                    Navigator.of(context).pop(0);
                  },
                  child: Container(
                      width: mediaQuery(context, 'width', 930) / 2,
                      height: mediaQuery(context, 'height', 133),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: <Widget>[
                          Image.asset(
                            LikeWalletImage.icon_button_back_white,
                            height: mediaQuery(context, 'height', 133),
                          ),
                          Text(
                            'Back to Stores',
                            style: TextStyle(
                                fontFamily: 'Noto Sans',
                                fontSize: mediaQuery(context, 'height', 36),
                                color:
                                    LikeWalletAppTheme.gray.withOpacity(0.8)),
                            textAlign: TextAlign.right,
                          )
                        ],
                      )),
                ),
                Container(
                  width: mediaQuery(context, 'width', 930) / 2,
                  height: mediaQuery(context, 'height', 133),
                ),
              ],
            ),
          );
  }

  Widget _equal() {
    return Positioned(
        top: mediaQuery(context, 'height', 115),
        right: mediaQuery(context, 'width', 100),
        child: new Container(
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.5),
                spreadRadius: 0,
                blurRadius: 2,
                offset: Offset(0, 0), // changes position of shadow
              ),
            ],
          ),
          child: new CircleAvatar(
            backgroundColor: LikeWalletAppTheme.lemon,
            child: Icon(
              Icons.drag_handle,
              color: LikeWalletAppTheme.white,
              size: mediaQuery(context, 'height', 41),
            ),
          ),
          width: mediaQuery(context, 'height', 89),
          height: mediaQuery(context, 'height', 89),
          padding: const EdgeInsets.all(2.0), // borde width
        ));
  }

  /// เเสดงContainer ที่ค่าสกุลเป็นจำนวน Like
  Widget _currency() {
    return AnimatedPositioned(
        left: mediaQuery(context, 'width', 75),
        top: showcurrency
            ? mediaQuery(context, 'height', 150)
            : mediaQuery(context, 'height', 5),
        height: showcurrency
            ? mediaQuery(context, 'height', 500)
            : mediaQuery(context, 'height', 0),
        duration: Duration(milliseconds: 500),
        child: SingleChildScrollView(
            child: Container(
                child: Container(
                    padding: EdgeInsets.only(
                        left: mediaQuery(context, 'width', 40),
                        bottom: mediaQuery(context, 'height', 40)),
                    height: mediaQuery(context, 'height', 300),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(20.0),
                          bottomRight: Radius.circular(20.0)),
                      color: LikeWalletAppTheme.lemon,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.5),
                          spreadRadius: 0,
                          blurRadius: 4,
                          offset: Offset(0, 0), // changes position of shadow
                        ),
                      ],
                    ),
                    width: mediaQuery(context, 'width', 671),
                    alignment: Alignment.center,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.end,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Text(
                          AppLocalizations.of(context)!
                              .translate('bankingSend_equal'),
                          style: TextStyle(
                            color: LikeWalletAppTheme.black.withOpacity(0.5),
                            fontFamily:
                                AppLocalizations.of(context)!.translate('font'),
                            fontWeight: FontWeight.normal,
                            fontSize: mediaQuery(context, 'height', 36),
                            letterSpacing: 0.5,
                          ),
                        ),
                        Container(
                          padding: EdgeInsets.only(
                              bottom: mediaQuery(context, 'height', 20)),
                          child: Text(
                            AppLocalizations.of(context)!
                                .translate('bankingSend_to_send'),
                            style: TextStyle(
                              color: LikeWalletAppTheme.black.withOpacity(0.5),
                              fontFamily: AppLocalizations.of(context)!
                                  .translate('font'),
                              fontWeight: FontWeight.normal,
                              fontSize: mediaQuery(context, 'height', 36),
                              letterSpacing: 0.5,
                            ),
                          ),
                        ),
                        Row(
                          children: <Widget>[
                            Expanded(
                              child: Text(
                                amountValue == ''
                                    ? '0'
                                    : validateFirstNumber(amountValue) == true
                                        ? f
                                            .format((double.parse(amountValue) *
                                                rateCurrency *
                                                rate))
                                            .toString()
                                        : amountValue.substring(0, 1) == '.'
                                            ? f.format((double.parse(
                                                    '0' + amountValue) *
                                                rateCurrency *
                                                rate))
                                            : '0',
                                style: TextStyle(
                                  color:
                                      LikeWalletAppTheme.black.withOpacity(1),
                                  fontFamily: AppLocalizations.of(context)!
                                      .translate('font'),
                                  fontWeight: FontWeight.normal,
                                  fontSize: mediaQuery(context, 'height', 42),
                                  letterSpacing: 0.5,
                                ),
                              ),
                            ),
                            Container(
                              alignment: Alignment.center,
                              width: mediaQuery(context, 'width', 200),
                              child: Text(
                                'LIKE',
                                style: TextStyle(
                                  color: LikeWalletAppTheme.gray.withOpacity(1),
                                  fontFamily: AppLocalizations.of(context)!
                                      .translate('font'),
                                  fontWeight: FontWeight.normal,
                                  fontSize: mediaQuery(context, 'width', 42),
                                  letterSpacing: 0.5,
                                ),
                              ),
                            ),
                          ],
                        )
                      ],
                    )))));
  }

  /// เเสดงสกุลทั้งหมดที่สามารถเเปลงได้
  Widget _showSymbol() {
    return AnimatedPositioned(
        top: showsymbol
            ? mediaQuery(context, 'height', 800)
            : mediaQuery(context, 'height', 800),
        right: mediaQuery(context, 'width', 140),
        height: showsymbol
            ? mediaQuery(context, 'height', 800)
            : mediaQuery(context, 'height', 0),
        duration: Duration(milliseconds: 200),
        child: SingleChildScrollView(
            child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: <Widget>[
            for (var i = 0; i < symbol.length; i++)
              if (symbol[i] != Symbol)
                GestureDetector(
                    onTap: () {
                      print(symbol[i]);
                      setState(() {
                        Symbol = symbol[i];
                        _changeCurrency(Symbol);
                        showsymbol = false;
                        print(amountSend);
                        if (amountSend.text == '') {
                          showcurrency = false;
                        } else {
                          showbutton = true;
                          showcurrency = true;
                        }
                      });
                    },
                    child: Container(
                        width: mediaQuery(context, 'width', 170),
                        padding: EdgeInsets.only(
                          top: mediaQuery(context, 'width', 20),
                          bottom: mediaQuery(context, 'width', 20),
                        ),
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          border: Border(
                            top: BorderSide(
                              //                    <--- top side
                              color: LikeWalletAppTheme.gray.withOpacity(0.5),
                              width: mediaQuery(context, 'width', 3),
                            ),
                          ),
                        ),
                        child: Text(
                          symbol[i],
                          style: TextStyle(
                              fontSize: mediaQuery(context, 'height', 45),
                              color: LikeWalletAppTheme.black,
                              fontWeight: FontWeight.normal,
                              fontFamily: AppLocalizations.of(context)!
                                  .translate('font1'),
                              shadows: [
                                Shadow(
                                  blurRadius: 0,
                                  color:
                                      LikeWalletAppTheme.black.withOpacity(0.5),
                                  offset: Offset(0.0, 0.0),
                                ),
                              ]),
                        )))
          ],
        )));
  }

  Widget _fee() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: <Widget>[
        new Container(
            padding: EdgeInsets.only(
              left: mediaQuery(context, 'width', 0),
              right: mediaQuery(context, 'width', 40),
            ),
            alignment: Alignment.centerLeft,
            height: mediaQuery(context, 'height', 130),
            width: mediaQuery(context, 'width', 671),
            child: Row(
              children: <Widget>[
                Text(
                    AppLocalizations.of(context)!
                        .translate('bankingSend_to_fee'),
                    style: LikeWalletAppTheme.textStyle(context, 36,
                        LikeWalletAppTheme.gray, FontWeight.normal, 'font1')),
                Text("   " + '0',
                    textAlign: TextAlign.end,
                    style: LikeWalletAppTheme.textStyle(context, 36,
                        LikeWalletAppTheme.black, FontWeight.normal, 'font1')),
                Text(
                  "  " + AppLocalizations.of(context)!.translate('send_symbol'),
                  style: TextStyle(
                    fontFamily:
                        AppLocalizations.of(context)!.translate('font1'),
                    fontSize: mediaQuery(context, 'height', 33),
                    color: LikeWalletAppTheme.gray.withOpacity(1),
                    letterSpacing: 0.3,
                  ),
                  textAlign: TextAlign.left,
                ),
              ],
            )),
      ],
    );
  }

  /// เเสดงรายการโปรด
  Widget _favoriteForm() {
    return AnimatedPositioned(
        duration: Duration(milliseconds: 300),
        bottom: showfavorite
            ? mediaQuery(context, 'height', 0)
            : mediaQuery(context, 'height', -660),
        child: Stack(
          children: <Widget>[
            //---------Container outside--------//
            Container(
              width: MediaQuery.of(context).size.width,
              height: mediaQuery(context, 'height', 660),
              child: Align(
                  //--------Position bottomCenter--------//
                  alignment: Alignment.bottomCenter,
                  child: Stack(
                    children: <Widget>[
                      //--------Container Background--------//
                      Container(
                        width: MediaQuery.of(context).size.width,
                        height: mediaQuery(context, 'height', 600),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(25.0),
                              topRight: Radius.circular(25.0)),
                          boxShadow: [
                            new BoxShadow(
                                color: Colors.black.withOpacity(0.16),
                                offset: new Offset(0, -3),
                                blurRadius: 5.0,
                                spreadRadius: 1.0),
                          ],
                          color: Color(0xfFFFFFFF),
                        ),
                        child: new StreamBuilder(
                          stream: fireStore
                              .collection('favorites')
                              .doc(FirebaseAuth.instance.currentUser!.uid)
                              .collection('transfer')
                              .snapshots(),
                          builder: (BuildContext context,
                              AsyncSnapshot<QuerySnapshot<Map<String, dynamic>>>
                                  snapshot) {
                            if (!snapshot.hasData) return new Text('');
                            return new ListView(
                              scrollDirection: Axis.horizontal,
                              children: snapshot.data!.docs.map((document) {
                                return Container(
                                    padding: EdgeInsets.all(10),
                                    child: Stack(
                                      children: <Widget>[
                                        Column(
                                          children: <Widget>[
                                            Padding(
                                              padding: EdgeInsets.symmetric(
                                                vertical: mediaQuery(
                                                    context, 'height', 70),
                                              ),
                                            ),
                                            GestureDetector(
                                                onTap: () {
                                                  sendAddress(document
                                                      .data()['addressFavorite']
                                                      .toString());
                                                },
                                                child: document.data()[
                                                                'photoFavorite'] ==
                                                            "null" ||
                                                        document.data()[
                                                                'photoFavorite'] ==
                                                            ""
                                                    ? CircleAvatar(
                                                        backgroundColor: Colors
                                                            .black12
                                                            .withOpacity(0.1),
                                                        radius: mediaQuery(
                                                            context,
                                                            'height',
                                                            125),
                                                        backgroundImage:
                                                            AssetImage(
                                                                LikeWalletImage
                                                                    .icon_user),
                                                      )
                                                    : CircleAvatar(
                                                        radius: mediaQuery(
                                                            context,
                                                            'height',
                                                            125),
                                                        backgroundImage:
                                                            NetworkImage(document
                                                                    .data()[
                                                                'photoFavorite']),
                                                      )),
//
                                            Container(
                                                padding: EdgeInsets.symmetric(
                                                  vertical: mediaQuery(
                                                      context, 'height', 20),
                                                ),
                                                child: Text(
                                                  document
                                                      .data()['nameFavorite'],
                                                  style: LikeWalletAppTheme
                                                      .textStyle(
                                                          context,
                                                          36,
                                                          LikeWalletAppTheme
                                                              .gray1,
                                                          FontWeight.normal,
                                                          'font1'),
                                                )),
                                          ],
                                        ),
                                        if (delete == true)
                                          Positioned(
                                              top: mediaQuery(
                                                  context, 'height', 330),
                                              right: mediaQuery(
                                                  context, 'width', 0),
                                              child: GestureDetector(
                                                onTap: () {
                                                  FirebaseFirestore.instance
                                                      .collection("favorites")
                                                      .doc(uid)
                                                      .collection("transfer")
                                                      .doc(document.id)
                                                      .delete();
                                                },
                                                child: Image.asset(
                                                  LikeWalletImage.icon_delete,
                                                  height: mediaQuery(
                                                      context, 'height', 100),
                                                  width: mediaQuery(
                                                      context, 'height', 100),
                                                ),
                                              ))
                                      ],
                                    ));
                              }).toList(),
                            );
                          },
                        ),
                      )
                    ],
                  )),
            ),
            //--------Button add favorites--------//
            Positioned(
                top: MediaQuery.of(context).size.height *
                    Screen_util("height", 0),
                right: mediaQuery(context, 'width', 50),
                child: GestureDetector(
                  onTap: () {
                    AddFavortieForm(context);
                    setState(() {});
                  },
                  child: Image.asset(
                    LikeWalletImage.icon_add,
                    height: mediaQuery(context, 'height', 125),
                    width: mediaQuery(context, 'height', 125),
                  ),
                )),
            //--------Button delete favorites--------//
            Positioned(
                top: MediaQuery.of(context).size.height *
                    Screen_util("height", 0),
                right: mediaQuery(context, 'height', 200),
                child: GestureDetector(
                  onTap: () {
                    if (delete == false)
                      deleteFavorites(true);
                    else
                      deleteFavorites(false);
                  },
                  child: Image.asset(
                    LikeWalletImage.icon_delete,
                    height: mediaQuery(context, 'height', 125),
                    width: MediaQuery.of(context).size.height *
                        Screen_util("height", 125),
                  ),
                )),
            //--------Button title favorties--------//
            Positioned(
                top: MediaQuery.of(context).size.height *
                    Screen_util("height", 100),
                left: mediaQuery(context, 'width', 50),
                child: Row(
                  children: <Widget>[
                    Container(
                      width: mediaQuery(context, 'width', 20),
                      height: mediaQuery(context, 'height', 20),
                      decoration: BoxDecoration(
                          color: Color(0xffB4E60D), shape: BoxShape.circle),
                    ),
                    Text(
                      '  ' +
                          AppLocalizations.of(context)!
                              .translate('stores_favorites'),
                      style: LikeWalletAppTheme.textStyle(context, 36,
                          LikeWalletAppTheme.black, FontWeight.w100, 'font1'),
                    ),
                  ],
                )),
          ],
        ));
  }

  setSelectInput(number) {
    setState(() {
      selected = number;
    });
    print(selected);
  }

  ///
  Widget _selectInput() {
    return Container(
      height: mediaQuery(context, 'height', 115),
      width: mediaQuery(context, 'width', 935.38),
      decoration: BoxDecoration(
        color: LikeWalletAppTheme.gray6,
        boxShadow: [
          BoxShadow(
            color: LikeWalletAppTheme.black.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: Offset(0, 0), // changes position of shadow
          ),
        ],
        borderRadius: BorderRadius.circular(5.0),
      ),
      child: Stack(
        children: <Widget>[
          Row(
            children: <Widget>[
              Diagonal(
                clipHeight: mediaQuery(context, 'height', 935.38) / 15,
                axis: Axis.vertical,
                position: DiagonalPosition.TOP_RIGHT,
                child: Container(
                  width: mediaQuery(context, 'width', 498.67),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(5.0),
                        bottomLeft: Radius.circular(5.0)),
                  ),
                  child: GestureDetector(
                    onTap: () => Navigator.pushNamed(context, '/diagonal_demo'),
                    child: GestureDetector(
                      onTap: () {
                        setSelectInput('address');
                      },
                      child: Container(
                        alignment: Alignment.center,
                        height: mediaQuery(context, 'height', 115),
                        width: mediaQuery(context, 'width', 935.38) / 2,
                        child: Text(
                          AppLocalizations.of(context)!
                              .translate('bankingSend_to_address'),
                          style: TextStyle(
                            fontFamily: AppLocalizations.of(context)!
                                .translate('font1'),
                            fontSize: mediaQuery(context, 'height', 42),
                            color: const Color(0x993c3c43),
                          ),
                          textAlign: TextAlign.left,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              GestureDetector(
                  onTap: () {
                    setSelectInput('phone');
                  },
                  child: Container(
                    alignment: Alignment.center,
                    height: mediaQuery(context, 'height', 115),
                    width: mediaQuery(context, 'width', 427.69),
                    child: Text(
                      AppLocalizations.of(context)!
                          .translate('bankingSend_to_mobile_number'),
                      style: TextStyle(
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                        fontSize: mediaQuery(context, 'height', 42),
                        color: const Color(0x993c3c43),
                      ),
                      textAlign: TextAlign.left,
                    ),
                  )),
            ],
          )
        ],
      ),
    );
  }
}

class UniqueColorGenerator {
  static Random random = new Random();
  static Color getColor() {
    return Color.fromARGB(
        255, random.nextInt(255), random.nextInt(255), random.nextInt(255));
  }
}
