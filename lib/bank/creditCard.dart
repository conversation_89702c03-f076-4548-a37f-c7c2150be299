import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class creditCard extends StatefulWidget {
  _creditCard createState() => new _creditCard();
}

class _creditCard extends State<creditCard> {
  buildForPhone(Orientation orientation) {
    return Scaffold(
      backgroundColor: Color(0xFFFFFFFF),
      body: Align(
        alignment: Alignment.topCenter,
        child: Column(
          children: <Widget>[
            new Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                new Container(
                  color: Color(0xff000000),
                  padding: EdgeInsets.only(
                    top: MediaQuery.of(context).size.height * 0.05,
                  ),
                  alignment: Alignment.center,
                  height: MediaQuery.of(context).size.height * 0.15,
                  width: MediaQuery.of(context).size.width,
                  child: Row(
                    children: <Widget>[
                      Icon(
                        Icons.arrow_back,
                        color: Color(0xffB4E60D),
                      ),
                      SizedBox(
                        width: MediaQuery.of(context).size.width * 0.02,
                      ),
                      GestureDetector(
                        onTap: (() => {Navigator.pop(context)}),
                        child: Text(
                          'Checkout',
                          style: TextStyle(
                              color: Color(0xffB4E60D),
                              fontFamily: 'Roboto',
                              fontSize: 18,
                              fontWeight: FontWeight.bold),
                        ),
                      )
                    ],
                  ),
                ),
              ],
            ),
            new Row(
              children: <Widget>[
                new Container(
                    padding: EdgeInsets.only(
                        left: MediaQuery.of(context).size.width * 0.07),
                    alignment: Alignment.centerLeft,
                    height: MediaQuery.of(context).size.height * 0.15,
                    width: MediaQuery.of(context).size.width,
                    child: Text(
                      'Please select your payment option',
                      style: TextStyle(
                          color: Color(0xff000000).withOpacity(0.25),
                          fontFamily: 'Roboto',
                          fontSize: 15,
                          fontWeight: FontWeight.bold),
                    )),
              ],
            ),
            GestureDetector(
//              onTap: (() => {Navigator.pushNamed(context, '/netBank')}),
              child: Container(
                  alignment: Alignment.center,
                  height: MediaQuery.of(context).size.height * 0.1,
                  width: MediaQuery.of(context).size.width * 0.9,
                  decoration: BoxDecoration(
                    border: Border.all(
                        width: 0.3, color: Color(0xff707071).withOpacity(0.5)),
                    borderRadius: BorderRadius.all(
                        Radius.circular(10.0) //         <--- border radius here
                        ),
                    boxShadow: [
                      BoxShadow(
                        blurRadius: 2.0,
                        color: Colors.black.withOpacity(.2),
                        offset: Offset(0.0, 5.0),
                      ),
                    ],
                    color: Colors.white,
                  ),
                  child: Row(
                    children: <Widget>[
                      Container(
                        height: MediaQuery.of(context).size.height * 0.035,
                        width: MediaQuery.of(context).size.width * 0.1,
                        alignment: Alignment.centerRight,
                        padding: EdgeInsets.all(
                            MediaQuery.of(context).size.height * 0.03),
                        margin: EdgeInsets.only(
                            left: MediaQuery.of(context).size.width * 0.04),
                        decoration: BoxDecoration(
                            shape: BoxShape.circle,
//                          color: Theme.of(context).primaryColor.withAlpha(alpha),
                            border: Border.all(
                              color: Color(0xffB4E60D),
                              width: 4.0,
                            )),
                      ),
                      Container(
                        alignment: Alignment.centerLeft,
                        padding: EdgeInsets.all(
                            MediaQuery.of(context).size.height * 0.02),
                        child: new Text(
                          'Internet Banking',
                          style: TextStyle(
                              color: Color(0xff000000).withOpacity(1),
                              fontFamily: 'Proxima Nova',
                              fontWeight: FontWeight.bold,
                              fontSize: 18.0),
                        ),
                      ),
                      Image.asset(
                        'assets/image/InternetBanking.png',
                        scale: 3,
                      ),
                    ],
                  )),
            ),
          ],
        ),
      ),
    );
  }

  buildForTablet(Orientation orientation) {
    return Scaffold();
  }

  int tabMenu = 0;
  changeMenu(_number) async {
    setState(() {
      tabMenu = _number;
    });
  }

  @override
  Widget build(BuildContext context) {
    final double shortestSide = MediaQuery.of(context).size.shortestSide;
    final bool useMobileLayout = shortestSide < 600.0;
    final Orientation orientation = MediaQuery.of(context).orientation;
    return Scaffold(
      body: useMobileLayout
          ? buildForPhone(orientation)
          : buildForTablet(orientation),
    );
  }
}
