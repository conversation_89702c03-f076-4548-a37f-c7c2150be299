import 'dart:async';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/bank/buylike/confirm_buylike.dart';
import 'package:likewallet/icon/icon_home_icons.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:likewallet/model/bank/exchange_fiat.dart';
import 'package:likewallet/routes.dart';
import 'package:likewallet/screen_util.dart';
import 'dart:ui';
import 'package:flutter/rendering.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:likewallet/screen/home.dart';
import 'package:likewallet/libraryman//ethcontract.dart';
import 'package:likewallet/middleware/get_mnemonic.dart';
import 'package:likewallet/middleware/check_auth.dart';
import 'package:intl/intl.dart';
import 'package:likewallet/libraryman/address_service.dart';
import 'package:likewallet/libraryman/configuration_service.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
//use for check active API before use withdraw
import 'package:likewallet/libraryman/checkAPI.dart';
import 'package:likewallet/libraryman/setFormat.dart';
import 'package:likewallet/libraryman/serviceHTTP.dart';
import 'package:intl/intl.dart' as formatIntl;
import 'package:likewallet/quickpay/favorites/list.dart';
import 'package:likewallet/libraryman/keyboard_done_widget.dart';
import 'dart:io' show File, Platform;

import '../../animationPage.dart';

/* ----------------| RECEIVE |--------------*/
class BuyLike extends StatefulWidget {
  BuyLike(
      {
        this.selectPage,
      required this.source,
      this.shopID,
      this.scanActive,
      required this.address,
      required this.contract,
        required this.abi,
        required this.callFunction});
  final String source;
  final selectPage;
  final shopID;
  final scanActive;
  final String contract;
  final String address;
  final String abi;
  final String callFunction;
  @override
  State<StatefulWidget> createState() {
    return _BuyLike(
        selectPage: selectPage,
        source: source,
        shopID: shopID,
        scanActive: scanActive,
        address: address,
        contract: contract,
        abi: abi,
        callFunction: callFunction);
  }
}

class _BuyLike extends State<BuyLike> {
  final TextEditingController controller = new TextEditingController();
//  TextEditingController amountSend = TextEditingController();
//  TextEditingController textCash = TextEditingController();
//  TextEditingController textAccountNumber = TextEditingController();
  TextEditingController amountSend = TextEditingController();
  _BuyLike(
      {this.selectPage,
        required this.source,
      this.shopID,
      this.scanActive,
        required this.address,
        required this.contract,
        required this.abi,
        required this.callFunction});

  final selectPage;
  final String source;
  final shopID;
  final scanActive;
  final String address;
  final String contract;
  final String abi;
  final String callFunction;

  bool showfavorite = false;

  final fireStore = FirebaseFirestore.instance;
  double price = 0;
  final f = new formatIntl.NumberFormat("###,###");
  final formatCurrency = new NumberFormat.decimalPattern();
  String amountValue = '0';
  String Symbol = 'THB';
  List symbol = ['THB', 'LAK', 'VND', 'USD', 'GOLD', 'LIKE'];
  bool showsymbol = false;
  bool showbutton = false;
  StreamController<String> streamBalance = StreamController<String>();
  StreamController<String> streamBalanceLocked = StreamController<String>();
  StreamController<String> streamAvailable = StreamController<String>();
  StreamController<double> streamBalanceLock = StreamController<double>();

  GlobalKey<ScaffoldState> _scaffoldKey = new GlobalKey();
  GlobalKey globalKey = new GlobalKey();

  double rate = 100.00;
  double amount = 0.00;
  String accountNumber = '';
  String currency = 'THB';
  double fee = 0.00;

  int keyword = 1;
  String barcode = "";
  String symbol_1 = 'LIKE';
  String symbol_2 = 'LIKE';
  String balance = 'Loading..';
  double balanceLIKE = 0.00;
  String locked_balance = 'Loading..';
  double rateCurrency = 0.0;
  double balanceLIKELock = 0.00;
  String available = 'Loading..';
  String bank = '';
  late String pketh;
  late BaseETH eth;
  late GetMnemonic seed;
  late String mnemonic;
  late CheckAuth Logon;
  late  IAddressService addressService;
  late IConfigurationService configETH;
  late BaseAuth FirebaseAuth;
  bool login = false;
  late CheckAPI checkAPI;
  late SetFormat setFormat;
  TextEditingController addressText = TextEditingController();
  final TextEditingController nameFavorite = TextEditingController();
  final TextEditingController addressFavorite = TextEditingController();
  late String photoFavorite;
  late File? _imageFavorite;
  bool _autoValidate = false;
  final formKey = GlobalKey<FormState>();
  bool delete = false;

  int tabMenu = 0;
  var crossFadeView = CrossFadeState.showFirst;
  bool _saving = false;
  late  String toAddress;
  late AbstractServiceHTTP APIHttp;
  var _opacity = 1.0;
  late OverlayEntry? overlayEntry;
  String _dataString = "";
  //ร้านค้า
  List<list> _list = [];
  List group = [];
  List<list> search = [];
  late String uid;

  //kyc session
  bool kycStatus = false;
  FocusNode amountFocusNode = new FocusNode();
  FocusNode amountBuyFocusNode = new FocusNode();
  FocusNode amountCashFocusNode = new FocusNode();

  FocusNode promptPayFocusNode = new FocusNode();
  KeyboardVisibilityController keyboardVisibilityController = KeyboardVisibilityController();

  final formatBank = new NumberFormat("###-###-####");

  _changePrice(String value) {
    setState(() => price = double.parse(value) / 100);
  }

  bool validateFirstNumber(String value) {
    String pattern = r'(^[0-9]?\d)';
    RegExp regExp = new RegExp(pattern);
    if (value.length == 0) {
      print(value);
      return false;
    } else if (!regExp.hasMatch(value)) {
      print(value + ' not match2');

      return false;
    }
    print(value + ' match2');
    return true;
  }

  void showColoredToast(msg) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_LONG,
        backgroundColor: Colors.red,
        textColor: Colors.white);
  }

  //done button zone
  showOverlay(BuildContext context) {
    if (overlayEntry != null) return;
    OverlayState? overlayState = Overlay.of(context);
    overlayEntry = OverlayEntry(builder: (context) {
      return Positioned(
          bottom: MediaQuery.of(context).viewInsets.bottom,
          right: 0.0,
          left: 0.0,
          child: InputDoneView());
    });

    overlayState!.insert(overlayEntry!);
  }

  removeOverlay() {
    if (overlayEntry != null) {
      overlayEntry!.remove();
      overlayEntry = null;
    }
  }

  Future<bool> getShop() async {
    //เรียก API ร้านค้า
    var data = await APIHttp.getQuickpayShop();
    setState(() {
      _list = data[0];
      search = data[0];
      group = data[1];
    });
    return true;
  }

  void pasteAddress() async {
    ClipboardData? data = await Clipboard.getData('text/plain');
    addressText.text = data!.text!;
    toAddress = data.text!;
  }

  Future generateETH(seed, pref) async {
    setState(() {
      _dataString = configETH.getAddress();
    });
    eth.getBalance(address: _dataString).then((avaiBalance) {
      available = 'LIKE ' + f.format(avaiBalance).toString();
      setState(() {
        balanceLIKE = avaiBalance.toDouble();
      });
      streamAvailable.sink.add(available);

      eth.getBalanceLock(address: _dataString).then((balanceLock) {
        print(balanceLock);
        balance = 'LIKE ' + f.format(avaiBalance + balanceLock);
        balanceLIKELock = balanceLock.toDouble();
        locked_balance = 'LIKE ' + f.format(balanceLock).toString();
        streamBalance.sink.add(balance);
        streamBalanceLock.sink.add(balanceLIKELock);
        streamBalanceLocked.sink.add(locked_balance);
      });
    });
  }

  _changeText(_symbol) async {
    final exchangeFiat = fireStore
        .collection('exchangeFiat')
        .withConverter<ExchangeFiat>(
          fromFirestore: (snapshot, _) => ExchangeFiat.fromJson(snapshot.data()!),
          toFirestore: (model, _) => model.toJson(),
        );

     exchangeFiat.doc(_symbol == 'none' ? "THB-LIKE" : "THB-" + _symbol)
        .get()
        .then((ds) {
      if (ds.data()!.to == 'LIKE') {
        if (!mounted) return;
        setState(() {
          rateCurrency = ds.data()!.rate.toDouble();
        });
      } else {
        if (!mounted) return;
        setState(() {
          rateCurrency = 1 / ds.data()!.rate;
        });
      }
    });

    if (!mounted) return;
    setState(() {
      print(_symbol);
      symbol_1 = _symbol;
    });
  }

  _changeCurrency(_symbol) async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    _changeText(_symbol);
    pref.setString('currency', _symbol);
//    int lastIndex = allCurrency.length - 1;
//    int _index = allCurrency.indexOf(current);
//    if (_index == lastIndex) {
//      _index = 0;
//      _changeText(allCurrency[_index]);
//      pref.setString('currency', allCurrency[_index]);
//    } else {
//      _changeText(allCurrency[_index + 1]);
//      pref.setString('currency', allCurrency[_index + 1]);
//    }
  }

  changeMenu(_number) async {
    setState(() {
      tabMenu = _number;
    });
  }

  String validateNumber(String value) {
    String pattern = r'(^[0-9.]{1,}$)';
    RegExp regExp = new RegExp(pattern);
    if (value.length == 0) {
      print(value);
      return 'Please enter mobile number';
    } else if (!regExp.hasMatch(value)) {
      print(value + ' not match');
      return 'Please enter valid mobile number';
    }
    print(value + ' match');
    return 'match';
  }

  changeAmount() {
    print(amountSend.text);

//    if(Platform.isIOS){
    var valid = validateNumber(amountSend.text.replaceAll(',', ''));
    if (valid == 'match') {
      setState(() {
        amountValue = amountSend.text.replaceAll(',', '');
      });

      if (amountSend.text.length > 0 && amountSend.text.isNotEmpty) {
        setState(() {
          showbutton = true;
        });
      } else {
        setState(() {
          showsymbol = false;
          showbutton = false;
        });
      }
    } else {
//        showColoredToast('only number');
    }

//    }
  }

  @override
  void dispose() {
    // TODO: implement dispose
//    streamBalance.close();
//    streamBalanceLocked.close();
//    streamBalanceLock.close();

    streamAvailable.close();
    amountFocusNode.dispose();
    amountBuyFocusNode.dispose();
    amountCashFocusNode.dispose();
    super.dispose();
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    APIHttp = new ServiceHTTP();
//    amountSend.addListener(changeAmount);
//    textCash.addListener(updateRecived);
//    textAccountNumber.addListener(updateAccountNumber);
    amountSend.addListener(changeAmount);
    Logon = new CurCheckAuth();
    seed = new MnemonicRetrieve();
    eth = new EthContract();
    FirebaseAuth = Auth();
    checkAPI = APIChecker();
    setFormat = SetFormatString();
    if (Platform.isIOS) {
      amountFocusNode.addListener(() {
        bool hasFocus = amountFocusNode.hasFocus;
        if (hasFocus)
          showOverlay(context);
        else
          removeOverlay();
      });

      amountBuyFocusNode.addListener(() {
        bool hasFocus = amountBuyFocusNode.hasFocus;
        if (hasFocus)
          showOverlay(context);
        else
          removeOverlay();
      });
      amountCashFocusNode.addListener(() {
        bool hasFocus = amountCashFocusNode.hasFocus;
        if (hasFocus)
          showOverlay(context);
        else
          removeOverlay();
      });
      keyboardVisibilityController.onChange.listen((bool visible) {
        removeOverlay();
      });
    }

    setInit();
    if (Platform.isAndroid) {
      promptPayFocusNode.addListener(() {
        bool hasFocus = promptPayFocusNode.hasFocus;
        if (hasFocus) {
          print('dsdsds');
          showOverlay(context);
        } else
          removeOverlay();
      });
    }

    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  }

  void setPay() async {
    getShop().then((result) {
      // print(result);
      if (result) {
        // print('result getShop' + result.toString());
        setState(() {
          toAddress = _list[shopID].address.toString().trim().toString();
          addressText.text = _list[shopID].title.toString();
        });
      }
    });
  }

  setInit() async {
    print('open banking');

    SharedPreferences pref = await SharedPreferences.getInstance();
    if (!mounted) return;
    setState(() {
      kycStatus = pref.getBool('kycActive') ?? false;
    });
    currency = pref.getString('currency') ?? 'THB';

    _changeText(currency);
    APIHttp.getCurrentFee(currency: currency).then((getFee) {
      setState(() {
        fee = getFee;
      });
      print(fee);
    });

    configETH = new ConfigurationService(pref);
    addressService = new AddressService(configETH);

    login = pref.getBool('login') ?? false;
    if (login) {
      mnemonic = await configETH.getMnemonic();
      generateETH(mnemonic, pref);
    }
  }

//  updateRecived() {
//    if (double.parse(textCash.text.replaceAll(",", "")) >= balanceLIKE) {
//      setState(() {
//        amount = double.parse((balanceLIKE / rate - fee).toStringAsFixed(2));
//      });
//    } else {
//      print('here');
//      setState(() {
//        amount = double.parse(
//            (double.parse(textCash.text.replaceAll(",", "")) / rate - fee)
//                .toStringAsFixed(2));
//      });
//    }
//  }

//  updateAccountNumber() {
//    setState(() {
//      accountNumber = textAccountNumber.text;
//    });
//  }

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Scaffold(
      bottomNavigationBar: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          Container(
            height: 296.03.h,
            width: MediaQuery.of(context).size.width,
            color: Color(0xffFFFFFF),
          ),
          // Container(
          //   // color: Color(0xffF5F5F5),
          //   height: mediaQuery(context, 'height', 218.4),
          //   child: SvgPicture.string(
          //     '<svg viewBox="0.0 2121.0 1078.7 218.4" ><path transform="translate(1454.92, -1101.01)" d="M -376.*********** 3222.*********** L -944.************* 3222.0078125 L -944.************* 3222.******** L -959.************* 3222.******** C -959.************* 3222.******** -995.*********** 3222.********** -1024.************ 3230.********** C -1051.********** 3236.********* -1067.************ 3248.********** -1075.************ 3254.770263671875 L -1075.************ 3254.7666015625 C -1096.18798828125 3271.944091796875 -1132.874755859375 3292.77587890625 -1169.73974609375 3292.79443359375 C -1207.788818359375 3292.79443359375 -1239.385986328125 3271.07861328125 -1259.194946289062 3254.65380859375 C -1267.607055664062 3248.7021484375 -1283.41796875 3236.********** -1310.423950195312 3230.193603515625 C -1334.906982421875 3224.57470703125 -1365.828979492188 3222.80078125 -1380.921********* 3222.*********** L -1380.921********* 3222.0078125 L -1454.*********** 3222.*********** L -1454.*********** 3440.*********** L -376.*********** 3440.*********** L -376.*********** 3222.*********** Z" fill="#ffffff" stroke="none" stroke-width="1" stroke-miterlimit="10" stroke-linecap="butt" /></svg>',
          //     allowDrawingOutsideViewBox: true,
          //   ),
          // ),
          // Positioned(
          //   top: 0,
          //   left: 256.3.w,
          //   child: Image.asset(
          //     LikeWalletImage.button_banking,
          //     height: 103.93.h,
          //   ),
          // ),
          Positioned(
            top: 50.h,
            width: MediaQuery.of(context).size.width,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 135.w),
              margin: EdgeInsets.only(top: 55.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  InkWell(
                    onTap: () {
                      AppRoutes.makeFirst(
                          context, HomeLikewallet(selectPage: 0));
                    },
                    child: Icon(IconHome.path_43609,
                        size: mediaQuery(context, 'height', 58),
                        color: Color(0xffB3B3B4)),
                  ),
                  InkWell(
                    onTap: () {
                      AppRoutes.makeFirst(
                          context, HomeLikewallet(selectPage: 1));
                    },
                    child: Icon(IconHome.path_43608,
                        size: mediaQuery(context, 'height', 58),
                        color: Color(0xffB3B3B4)),
                  ),
                  InkWell(
                    onTap: () {
                      AppRoutes.makeFirst(
                          context, HomeLikewallet(selectPage: 2));
                    },
                    child: Icon(IconHome.group_24548,
                        size: mediaQuery(context, 'height', 58),
                        color: Color(0xffB3B3B4)),
                  ),
                  InkWell(
                    onTap: () {
                      AppRoutes.makeFirst(
                          context, HomeLikewallet(selectPage: 3));
                    },
                    child: Icon(IconHome.path_58781,
                        size: mediaQuery(context, 'height', 58),
                        color: Color(0xffB3B3B4)),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: GestureDetector(
            onTap: () {
              FocusScopeNode currentFocus = FocusScope.of(context);
              if (!currentFocus.hasPrimaryFocus) {
                currentFocus.unfocus();
              }
              setState(() {
                showsymbol = false;
              });
            },
            child: Stack(
              alignment: Alignment.topLeft,
              clipBehavior: Clip.none,
              fit: StackFit.passthrough,
              children: <Widget>[
                _buy_like(context),
                _head(),
              ],
            )),
      ),
    );
  }

  Widget _head() {
    return Stack(
      children: <Widget>[
        Container(
          color: Color(0xff141322),
          height: mediaQuery(context, 'height', 256),
          child: Container(
            padding: EdgeInsets.only(bottom: mediaQuery(context, 'height', 35)),
            alignment: Alignment.bottomCenter,
            width: MediaQuery.of(context).size.width,
            child: new Text(
              AppLocalizations.of(context)!.translate('buylike_CONFIRM'),
              style: TextStyle(
                  letterSpacing: 0.5,
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  color: Color(0xffFFFFFF).withOpacity(1),
                  fontSize: MediaQuery.of(context).size.height * 0.01794871794,
                  fontWeight: FontWeight.normal),
            ),
          ),
        ),
        Container(
          height: MediaQuery.of(context).size.height,
        ),
        Positioned(
            top: MediaQuery.of(context).size.height * 0.08608547008,
            child: backButtonLemon(context)),
      ],
    );
  }

  Widget _buy_like(context) {
    return Align(
      alignment: Alignment.center,
      child: new Container(
        padding: EdgeInsets.only(
          top: mediaQuery(context, 'height', 250),
        ),
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: BoxDecoration(
          color: Color(0xffF5F5F5),
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            stops: [0.0, 0.2, 0.5],
            colors: [
              // Colors are easy thanks to Flutter's Colors class.
              Colors.white,
              Colors.white,
              LikeWalletAppTheme.white1
            ],
          ),
        ),
        child: new Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              ///Text Amount
              Container(
                  height: mediaQuery(context, 'height', 1650),
//                      width: mediaQuery(context, 'width', 945.66),
                  child: Stack(
                    alignment: Alignment.center,
                    children: <Widget>[
                      Container(
                        margin: EdgeInsets.only(
                            top: mediaQuery(context, 'height', 96),
                            left: mediaQuery(context, 'height', 75)),
                        child: Column(
                          children: <Widget>[
                            _amountLike(),
                            SizedBox(
                              height: mediaQuery(context, 'height', 20),
                            ),
                            _exchangeRate(),
                          ],
                        ),
                      ),
                      Positioned(
                        top: mediaQuery(context, 'height', 283),
                        left: mediaQuery(context, 'width', 440),
                        child: _iconEqual(),
                      ),
                      _showSymbol(),
                      _buttonNext(),
                    ],
                  )),
            ]),
      ),
    );
  }

//  Widget _buy_confirm(context) {
//    return new Stack(
//      children: <Widget>[
//        //พื้นหลัง
//        Align(
//          alignment: Alignment.center,
//          child: new Padding(
//              padding: EdgeInsets.only(top: mediaQuery(context, 'height', 50)),
//              child: new Container(
////                height: MediaQuery.of(context).size.height / 1.3,
//                width: MediaQuery.of(context).size.width,
//                decoration: BoxDecoration(
//                  gradient: LinearGradient(
//                    begin: Alignment.topCenter,
//                    end: Alignment.bottomCenter,
//                    stops: [1],
//                    colors: [
//                      // Colors are easy thanks to Flutter's Colors class.
//                      Colors.white
//                    ],
//                  ),
//                ),
//                child: new Column(
//                  children: <Widget>[
//                    SizedBox(
//                      height: MediaQuery.of(context).size.height * 0.1,
//                    ),
//                    new Container(
//                      height: MediaQuery.of(context).size.height * 0.2,
//                      width: MediaQuery.of(context).size.width * 0.9,
//                      decoration: BoxDecoration(
//                        border:
//                            Border.all(width: 0.3, color: Color(0xff707071)),
//                        borderRadius: BorderRadius.all(Radius.circular(
//                                5.0) //         <--- border radius here
//                            ),
//                      ),
//                      child: new Column(
//                        crossAxisAlignment: CrossAxisAlignment.start,
//                        mainAxisAlignment: MainAxisAlignment.start,
//                        children: <Widget>[
//                          //หัวข้อยืนยันการเเรก
//                          _buy_confirm_title(),
//                          //ยอดซื้อ
//                          _buy_confirm_like(),
//                          //เว้นเเถว
//                          SizedBox(
//                            height: MediaQuery.of(context).size.height * 0.02,
//                          ),
//                          //ยอดรวม
//                          _buy_confirm_total(),
//                          _buy_confirm_total_line2()
//                        ],
//                      ),
//                    ),
//                    SizedBox(
//                      height: MediaQuery.of(context).size.height * 0.23,
//                    ),
//                    //ปุ่มต่อไป
//                    _buy_confirm_button()
//                  ],
//                ),
//              )),
//        ),
//        //ปุุ่มกลับ
//        Positioned(
//            top: MediaQuery.of(context).size.height * Screen_util("height", 50),
//            child: Row(
//              children: <Widget>[
//                new GestureDetector(
//                  onTap: (() => {changeMenu(0)}),
//                  child: Container(
//                      alignment: Alignment.centerRight,
//                      height: MediaQuery.of(context).size.height * 0.06,
//                      width: MediaQuery.of(context).size.width * 0.1,
//                      child: Icon(
//                        Icons.arrow_back_ios,
//                        size: MediaQuery.of(context).size.height *
//                            Screen_util("height'", 27),
//                        color: Color(0xff6C6B6D).withOpacity(0.5),
//                      )),
//                ),
//              ],
//            )),
//        //หัวข้อ
//        Positioned(
//          top: MediaQuery.of(context).size.height * Screen_util("height", 50),
//          child: new Container(
//            alignment: Alignment.center,
//            padding: EdgeInsets.only(
//                left: MediaQuery.of(context).size.width * 0.19567),
//            height: MediaQuery.of(context).size.height * 0.06,
//            width: MediaQuery.of(context).size.width * 0.8,
//            child: new Text(
//              AppLocalizations.of(context)!.translate('bankingbuy_order'),
//              style: TextStyle(
//                  color: LikeWalletAppTheme.gray1.withOpacity(0.5),
//                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
//                  fontWeight: FontWeight.bold,
//                  fontSize: mediaQuery(context, 'height', 45)),
//            ),
//          ),
//        ),
//      ],
//    );
//  }
//
//  Widget _buy_confirm_title() {
//    return new Container(
//      padding: EdgeInsets.only(left: MediaQuery.of(context).size.width * 0.07),
//      alignment: Alignment.bottomLeft,
//      height: MediaQuery.of(context).size.height * 0.05,
//      width: MediaQuery.of(context).size.width * 0.25,
//      child: Text(
//        AppLocalizations.of(context)!.translate('bankingbuy_buy'),
//        style: TextStyle(
//            color: Color(0xff6C6B6D).withOpacity(0.5),
//            fontFamily: AppLocalizations.of(context)!.translate('font1'),
//            fontWeight: FontWeight.normal,
//            fontSize:
//                MediaQuery.of(context).size.height * Screen_util("height", 36)),
//      ),
//    );
//  }
//
//  Widget _buy_confirm_like() {
//    return Row(
//      children: <Widget>[
//        new Container(
//          padding:
//              EdgeInsets.only(left: MediaQuery.of(context).size.width * 0.07),
//          alignment: Alignment.centerLeft,
//          height: MediaQuery.of(context).size.height * 0.03,
//          width: MediaQuery.of(context).size.width * 0.3,
//          child: Text(
//            formatCurrency.format(price),
//            style: TextStyle(
//                color: Color(0xff141322).withOpacity(1),
//                fontFamily: AppLocalizations.of(context)!.translate('font1'),
//                fontWeight: FontWeight.normal,
//                fontSize: MediaQuery.of(context).size.height *
//                    Screen_util("height", 45)),
//          ),
//        ),
//        new Container(
//          padding:
//              EdgeInsets.only(left: MediaQuery.of(context).size.width * 0.07),
//          alignment: Alignment.centerLeft,
//          height: MediaQuery.of(context).size.height * 0.03,
//          width: MediaQuery.of(context).size.width * 0.4,
//          child: Text(
//            AppLocalizations.of(context)!.translate('bankingbuy_likepoint'),
//            style: TextStyle(
//                color: Color(0xff141322).withOpacity(1),
//                fontFamily: AppLocalizations.of(context)!.translate('font1'),
//                fontWeight: FontWeight.normal,
//                fontSize: MediaQuery.of(context).size.height *
//                    Screen_util("height", 36)),
//          ),
//        ),
//      ],
//    );
//  }
//
//  Widget _buy_confirm_total() {
//    return new Container(
//      padding: EdgeInsets.only(left: MediaQuery.of(context).size.width * 0.07),
//      alignment: Alignment.bottomLeft,
//      height: MediaQuery.of(context).size.height * 0.03,
//      width: MediaQuery.of(context).size.width * 0.25,
//      child: Text(
//        AppLocalizations.of(context)!.translate('bankingbuy_total'),
//        style: TextStyle(
//            color: Color(0xff6C6B6D).withOpacity(0.5),
//            fontFamily: AppLocalizations.of(context)!.translate('font1'),
//            fontWeight: FontWeight.normal,
//            fontSize:
//                MediaQuery.of(context).size.height * Screen_util("height", 36)),
//      ),
//    );
//  }

//  Widget _buy_confirm_total_line2() {
//    return Row(
//      children: <Widget>[
//        new Container(
//          padding:
//              EdgeInsets.only(left: MediaQuery.of(context).size.width * 0.07),
//          alignment: Alignment.centerLeft,
//          height: MediaQuery.of(context).size.height * 0.03,
//          width: MediaQuery.of(context).size.width * 0.3,
//          child: Text(
//            formatCurrency.format(price / rate),
//            style: TextStyle(
//                color: Color(0xff141322).withOpacity(1),
//                fontFamily: AppLocalizations.of(context)!.translate('font1'),
//                fontWeight: FontWeight.normal,
//                fontSize: MediaQuery.of(context).size.height *
//                    Screen_util("height", 45)),
//          ),
//        ),
//        new Container(
//          padding:
//              EdgeInsets.only(left: MediaQuery.of(context).size.width * 0.07),
//          alignment: Alignment.centerLeft,
//          height: MediaQuery.of(context).size.height * 0.03,
//          width: MediaQuery.of(context).size.width * 0.4,
//          child: Text(
//            AppLocalizations.of(context)!.translate('bankingbuy_symbol'),
//            style: TextStyle(
//                color: Color(0xff141322).withOpacity(1),
//                fontFamily: AppLocalizations.of(context)!.translate('font1'),
//                fontWeight: FontWeight.normal,
//                fontSize: MediaQuery.of(context).size.height *
//                    Screen_util("height", 36)),
//          ),
//        ),
//      ],
//    );
//  }
//
//  Widget _buy_confirm_button() {
//    return Row(
//      mainAxisAlignment: MainAxisAlignment.center,
//      crossAxisAlignment: CrossAxisAlignment.center,
//      children: <Widget>[
//        new Container(
//          alignment: Alignment.center,
//          child: ButtonTheme(
//            minWidth:
//                MediaQuery.of(context).size.width * Screen_util("width", 930),
//            height:
//                MediaQuery.of(context).size.height * Screen_util("height", 132),
//            child: new FlatButton(
//                shape: new RoundedRectangleBorder(
//                  borderRadius: new BorderRadius.circular(8.0),
//                ),
//                disabledColor: Color(0xff141322).withOpacity(1),
//                color: Color(0xff141322).withOpacity(1),
//                onPressed: () => {
//                      changeMenu(2),
//                    },
//                child: new Text(
//                  AppLocalizations.of(context)!.translate('bankingbuy_button'),
//                  style: TextStyle(
//                      color: Color(0xffB4E60D),
//                      fontFamily:
//                          AppLocalizations.of(context)!.translate('font1'),
//                      fontSize: MediaQuery.of(context).size.height *
//                          Screen_util("height", 52),
//                      fontWeight: FontWeight.normal),
//                )),
//          ),
//        ),
//      ],
//    );
//  }
//
//  Widget _buy_payment(context) {
//    return new Stack(
//      alignment: Alignment.topLeft,
//  clipBehavior: Clip.none,
//      fit: StackFit.passthrough,
//      children: <Widget>[
//        // พื้นหลังขาว
//        Align(
//          alignment: Alignment.center,
//          child: new Padding(
//              padding: EdgeInsets.only(
//                top: MediaQuery.of(context).size.height *
//                    Screen_util("height", 50),
//              ),
//              child: new Container(
////                height: MediaQuery.of(context).size.height / 1.3,
//                width: MediaQuery.of(context).size.width,
//                decoration: BoxDecoration(
//                  gradient: LinearGradient(
//                    begin: Alignment.topCenter,
//                    end: Alignment.bottomCenter,
//                    stops: [1],
//                    colors: [
//                      // Colors are easy thanks to Flutter's Colors class.
//                      Colors.white
//                    ],
//                  ),
//                ),
//                child: new Column(
//                  children: <Widget>[
//                    SizedBox(
//                      height: MediaQuery.of(context).size.height * 0.1,
//                    ),
//                    //รายการซื้อด้วยการโอน
//                    _buy_payment_transfer(),
//                    SizedBox(
//                      height: MediaQuery.of(context).size.height * 0.03,
//                    ),
//                  ],
//                ),
//              )),
//        ),
//        //ปุ่มกลับ
//        Positioned(
//            top: MediaQuery.of(context).size.height * Screen_util("height", 50),
//            child: Row(
//              children: <Widget>[
//                new GestureDetector(
//                  onTap: (() => {changeMenu(1)}),
//                  child: Container(
//                      alignment: Alignment.centerRight,
//                      height: MediaQuery.of(context).size.height * 0.06,
//                      width: MediaQuery.of(context).size.width * 0.1,
//                      child: Icon(
//                        Icons.arrow_back_ios,
//                        size: MediaQuery.of(context).size.height *
//                            Screen_util("height'", 27),
//                        color: Color(0xff6C6B6D).withOpacity(0.5),
//                      )),
//                ),
//              ],
//            )),
//        //หัวข้อ
//        Positioned(
//          top: MediaQuery.of(context).size.height * Screen_util("height", 50),
//          child: new Container(
//            alignment: Alignment.center,
//            padding: EdgeInsets.only(
//                left: MediaQuery.of(context).size.width * 0.19567),
//            height: MediaQuery.of(context).size.height * 0.06,
//            width: MediaQuery.of(context).size.width * 0.8,
//            child: new Text(
//              AppLocalizations.of(context)!.translate('buy_payment_title'),
//              style: TextStyle(
//                  color: LikeWalletAppTheme.gray1.withOpacity(0.5),
//                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
//                  fontWeight: FontWeight.bold,
//                  fontSize: mediaQuery(context, 'height', 45)),
//            ),
//          ),
//        ),
//      ],
//    );
//  }
//
//  Widget _buy_payment_transfer() {
//    return GestureDetector(
//      onTap: (() => {
//            Navigator.push(context,
//                EnterExitRoute(exitPage: null, enterPage: PaymentBuy()))
//          }),
//      child: Container(
//          alignment: Alignment.center,
//          height: MediaQuery.of(context).size.height * 0.1,
//          width: MediaQuery.of(context).size.width * 0.9,
//          decoration: BoxDecoration(
//            border: Border.all(
//                width: 0.3, color: Color(0xff707071).withOpacity(0.5)),
//            borderRadius: BorderRadius.all(
//                Radius.circular(10.0) //         <--- border radius here
//                ),
//            boxShadow: [
//              BoxShadow(
//                blurRadius: 2.0,
//                color: Colors.black.withOpacity(.2),
//                offset: Offset(0.0, 5.0),
//              ),
//            ],
//            color: Colors.white,
//          ),
//          child: Row(
//            children: <Widget>[
//              Container(
//                height: MediaQuery.of(context).size.height * 0.035,
//                width: MediaQuery.of(context).size.width * 0.1,
//                alignment: Alignment.centerRight,
//                padding:
//                    EdgeInsets.all(MediaQuery.of(context).size.height * 0.03),
//                margin: EdgeInsets.only(
//                    left: MediaQuery.of(context).size.width * 0.04),
//                decoration: BoxDecoration(
//                    shape: BoxShape.circle,
//                    border: Border.all(
//                      color: Color(0xffB4E60D),
//                      width: 4.0,
//                    )),
//              ),
//              //ช่องทางการจ่ายเงิน
//              Container(
//                alignment: Alignment.centerLeft,
//                padding:
//                    EdgeInsets.all(MediaQuery.of(context).size.height * 0.02),
//                child: new Text(
//                  AppLocalizations.of(context)
//                      .translate('buy_payment_transfer_banking'),
//                  style: TextStyle(
//                      color: LikeWalletAppTheme.black,
//                      fontFamily:
//                          AppLocalizations.of(context)!.translate('font1'),
//                      fontWeight: FontWeight.bold,
//                      fontSize: 18.0),
//                ),
//              ),
//              Image.asset(
//                LikeWalletImage.icon_transfer,
//                scale: 3,
//              ),
//            ],
//          )),
//    );
//  }

  ///ช่องกรอกจำนวน
  Widget _amountLike() {
    return Container(
      padding: EdgeInsets.only(
        left: mediaQuery(context, 'width', 0),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: <Widget>[
          Container(
            padding: EdgeInsets.only(
              top: mediaQuery(context, 'height', 37),
              left: mediaQuery(context, 'width', 56),
            ),
            width: mediaQuery(context, 'width', 671),
            height: mediaQuery(context, 'height', 290),
            decoration: BoxDecoration(
              borderRadius:
                  BorderRadius.circular(mediaQuery(context, 'height', 16)),
              color: LikeWalletAppTheme.white,
              boxShadow: [
                BoxShadow(
                  color: const Color(0x1a000000),
                  offset: Offset(1, 1),
                  blurRadius: 7,
                  spreadRadius: 1,
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Container(
                  margin: EdgeInsets.only(
                      bottom: mediaQuery(context, 'height', 10)),
                  child: Text(
                    AppLocalizations.of(context)!.translate('buylike_amount'),
                    style: TextStyle(
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      fontSize: mediaQuery(context, 'height', 36),
                      color: LikeWalletAppTheme.gray1,
                      letterSpacing: 0.3,
                    ),
                    textAlign: TextAlign.left,
                  ),
                ),
                Container(
                  alignment: Alignment.centerLeft,
                  height: mediaQuery(context, 'height', 109),
                  child: TextFormField(
                    onTap: () {
                      setState(() {
                        showsymbol = false;
                      });
                    },
                    onChanged: (value) {
                      if (value.isEmpty) {
                        setState(() {
                          showbutton = false;
                        });
                      }
                    },
                    inputFormatters: [
                      // CurrencyTextInputFormatter(
                      //   decimalDigits: 0,
                      // )
                    ],
                    keyboardType: TextInputType.number,
                    textAlignVertical: TextAlignVertical.center,
                    textAlign: TextAlign.start,
                    style: TextStyle(
                        fontSize: mediaQuery(context, 'height', 83),
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font2'),
                        color: LikeWalletAppTheme.gray1),
                    controller: amountSend,
                    focusNode: amountFocusNode,
                    decoration: InputDecoration(
                      focusedBorder: InputBorder.none,
                      enabledBorder: InputBorder.none,
                      hintText: '0',
                      hintStyle: TextStyle(
                          fontSize: mediaQuery(context, 'height', 83),
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font2'),
                          color: LikeWalletAppTheme.gray),
                      contentPadding: EdgeInsets.only(
                        bottom: mediaQuery(context, 'height', 20),
                      ),
                    ),
                  ),
                ),
                Container(
                  margin:
                      EdgeInsets.only(top: mediaQuery(context, 'height', 10)),
                  child: Text(
                    Symbol == 'THB'
                        ? AppLocalizations.of(context)!.translate('symbol_th')
                        : Symbol == 'USD'
                            ? AppLocalizations.of(context)!
                            .translate('symbol_us')
                            : Symbol == 'LAK'
                                ? AppLocalizations.of(context)!
                            .translate('symbol_lak')
                                : Symbol == 'VND'
                                    ? AppLocalizations.of(context)!
                            .translate('symbol_vn')
                                    : Symbol == 'GOLD'
                                        ? AppLocalizations.of(context)!
                            .translate('symbol_gold')
                                        : Symbol == 'LIKE'
                                            ? AppLocalizations.of(context)!
                            .translate('symbol_like')
                                            : AppLocalizations.of(context)!
                            .translate('symbol_th'),
                    style: TextStyle(
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      fontSize: mediaQuery(context, 'height', 33),
                      color: LikeWalletAppTheme.gray1,
                      letterSpacing: 0.3,
                    ),
                    textAlign: TextAlign.left,
                  ),
                ),
              ],
            ),
          ),
//          Container(
//              padding: EdgeInsets.only(
//                left: mediaQuery(context, 'width', 32),
//              ),
//              child: _switchSymbol())
        ],
      ),
    );
  }

  ///ช่องกรอกจำนวน
  Widget _exchangeRate() {
    return Container(
      padding: EdgeInsets.only(
        left: mediaQuery(context, 'width', 0),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: <Widget>[
          Container(
            width: mediaQuery(context, 'width', 671),
            height: mediaQuery(context, 'height', 290),
            decoration: BoxDecoration(
              borderRadius:
                  BorderRadius.circular(mediaQuery(context, 'height', 16)),
              color: LikeWalletAppTheme.white,
              boxShadow: [
                BoxShadow(
                  color: const Color(0x1a000000),
                  offset: Offset(1, 1),
                  blurRadius: 7,
                  spreadRadius: 1,
                ),
              ],
            ),
            child: Stack(
//              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Positioned(
                  left: mediaQuery(context, 'width', 56),
                  top: mediaQuery(context, 'height', 43),
                  child: Container(
                    margin: EdgeInsets.only(
                        bottom: mediaQuery(context, 'height', 10)),
                    child: Text(
                      AppLocalizations.of(context)!.translate('buylike_get'),
                      style: TextStyle(
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                        fontSize: mediaQuery(context, 'height', 36),
                        color: LikeWalletAppTheme.gray1,
                        letterSpacing: 0.3,
                      ),
                      textAlign: TextAlign.left,
                    ),
                  ),
                ),
                Positioned(
                  left: mediaQuery(context, 'width', 56),
                  top: mediaQuery(context, 'height', 100),
                  child: Container(
                      alignment: Alignment.centerLeft,
                      height: mediaQuery(context, 'height', 109),
                      child: Text(
                        amountValue == ''
                            ? '0'
                            : validateFirstNumber(amountValue) == true
                                ? f
                                    .format((double.parse(amountValue) * rate))
                                    .toString()
                                : amountValue.substring(0, 1) == '.'
                                    ? f.format(
                                        (double.parse('0' + amountValue) *
                                            rate))
                                    : '0',
                        // amountValue == ''
                        //     ? '0'
                        //     : f
                        //         .format((int.parse(amountValue) *
                        //             rateCurrency *
                        //             rate))
                        //         .toString(),
                        style: TextStyle(
                            fontSize: mediaQuery(context, 'height', 83),
                            fontFamily:
                                AppLocalizations.of(context)!.translate('font2'),
                            color: LikeWalletAppTheme.gray1),
                      )),
                ),
                Positioned(
                  left: mediaQuery(context, 'width', 56),
                  bottom: mediaQuery(context, 'height', 40),
                  child: Container(
                    child: Text(
                      AppLocalizations.of(context)!
                            .translate('buylike_likepoint'),
                      style: TextStyle(
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                        fontSize: mediaQuery(context, 'height', 33),
                        color: LikeWalletAppTheme.gray1,
                        letterSpacing: 0.3,
                      ),
                      textAlign: TextAlign.left,
                    ),
                  ),
                ),
                Positioned(
                  right: mediaQuery(context, 'width', 45),
                  bottom: mediaQuery(context, 'height', 40),
                  child: Container(
                    padding: EdgeInsets.only(
                      left: mediaQuery(context, 'width', 40),
                    ),
                    child: Text(
                      'LIKE',
                      style: TextStyle(
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font2'),
                        fontSize: mediaQuery(context, 'height', 71),
                        color: LikeWalletAppTheme.black,
                        fontWeight: FontWeight.w500,
                        letterSpacing: 0.3,
                      ),
                      textAlign: TextAlign.left,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _iconEqual() {
    return Container(
        padding: EdgeInsets.only(
          left: mediaQuery(context, 'width', 0),
        ),
        child: Image.asset(
          LikeWalletImage.icon_equal,
          height: mediaQuery(context, 'height', 228),
        ));
  }

  ///เเสดงสกุลที่เลือก ตั้งค่าทเริ่มที่ THB
  Widget _switchSymbol() {
    return GestureDetector(
        onTap: () {
          setState(() {
            showsymbol = !showsymbol;
            print(showsymbol);
            showbutton = false;
          });
        },
        child: Container(
          margin: EdgeInsets.only(left: mediaQuery(context, 'width', 0)),
          height: mediaQuery(context, 'height', 161),
          width: mediaQuery(context, 'width', 270),
          child: Row(
            children: <Widget>[
              Text(
                Symbol,
                style: TextStyle(
                    fontSize: mediaQuery(context, 'height', 71),
                    color: LikeWalletAppTheme.black,
                    fontWeight: FontWeight.w700,
                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
                    shadows: [
                      Shadow(
                        blurRadius: 2,
                        color: LikeWalletAppTheme.black.withOpacity(0.5),
                        offset: Offset(0.0, 0.0),
                      ),
                    ]),
              ),
              Icon(
                Icons.keyboard_arrow_down,
                color: LikeWalletAppTheme.black,
                size: mediaQuery(context, 'height', 71),
              )
            ],
          ),
        ));
  }

  /// เเสดงสกุลทั้งหมดที่สามารถเเปลงได้
  Widget _showSymbol() {
    return AnimatedPositioned(
        top: showsymbol
            ? mediaQuery(context, 'height', 300)
            : mediaQuery(context, 'height', 300),
        right: mediaQuery(context, 'width', 140),
        height: showsymbol
            ? mediaQuery(context, 'height', 800)
            : mediaQuery(context, 'height', 0),
        duration: Duration(milliseconds: 200),
        child: SingleChildScrollView(
            child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: <Widget>[
            for (var i = 0; i < symbol.length; i++)
              if (symbol[i] != Symbol)
                GestureDetector(
                    onTap: () {
                      print(symbol[i]);
                      setState(() {
                        Symbol = symbol[i];
                        _changeCurrency(Symbol);
                        showsymbol = false;
                        print(amountSend);
                        if (amountSend.text.isEmpty) {
                        } else {
                          showbutton = true;
                        }
                      });
                    },
                    child: Container(
                        width: mediaQuery(context, 'width', 170),
                        padding: EdgeInsets.only(
                          top: mediaQuery(context, 'width', 35),
                          bottom: mediaQuery(context, 'width', 35),
                        ),
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          border: Border(
                            top: BorderSide(
                              //                    <--- top side
                              color: LikeWalletAppTheme.gray.withOpacity(0.5),
                              width: mediaQuery(context, 'width', 3),
                            ),
                          ),
                        ),
                        child: Text(
                          symbol[i],
                          style: TextStyle(
                              fontSize: mediaQuery(context, 'height', 45),
                              color: LikeWalletAppTheme.black,
                              fontWeight: FontWeight.normal,
                              fontFamily: AppLocalizations.of(context)!
                            .translate('font1'),
                              shadows: [
                                Shadow(
                                  blurRadius: 0,
                                  color:
                                      LikeWalletAppTheme.black.withOpacity(0.5),
                                  offset: Offset(0.0, 0.0),
                                ),
                              ]),
                        )))
          ],
        )));
  }

  ///ปุ่มถัดไป
  Widget _buttonNext() {
    return AnimatedPositioned(
        top: showbutton
            ? mediaQuery(context, 'height', 800)
            : mediaQuery(context, 'height', 2430),
        duration: Duration(milliseconds: 500),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            GestureDetector(
              onTap: () {
                setState(() {
                  showbutton = false;
                  amountSend.clear();
                  amountValue = '0';
                });
              },
              child: Container(
                  width: mediaQuery(context, 'width', 930) / 2,
                  height: mediaQuery(context, 'height', 133),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: <Widget>[
                      Image.asset(
                        LikeWalletImage.icon_button_cancel_white,
                        height: mediaQuery(context, 'height', 133),
                      ),
                      Text(
                        AppLocalizations.of(context)!
                            .translate('buylike_cancel'),
                        style: TextStyle(
                            fontFamily: 'Noto Sans',
                            fontSize: mediaQuery(context, 'height', 36),
                            color: LikeWalletAppTheme.gray.withOpacity(0.8)),
                        textAlign: TextAlign.right,
                      )
                    ],
                  )),
            ),
            GestureDetector(
              onTap: () {
                print(selectPage);
                if (double.parse(amountSend.text.replaceAll(',', '')) > 0 &&
                    selectPage == 'QRCode' &&
                    double.parse(amountSend.text.replaceAll(',', '')) <=
                        50000) {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) => confirmBuylike(
                              amountSend: amountSend.text,
                              selectPage: selectPage,
                              titleName: '..Loading',
                              rateCurrency:
                                  (int.parse(amountValue) * rate).toString())));
                } else if (double.parse(amountSend.text.replaceAll(',', '')) >
                        0 &&
                    selectPage == 'Upload') {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) => confirmBuylike(
                              amountSend: amountSend.text,
                              selectPage: selectPage,
                              titleName: '..Loading',
                              rateCurrency:
                                  (int.parse(amountValue) * rate).toString())));
                } else if (double.parse(amountSend.text.replaceAll(',', '')) >
                        50000 &&
                    selectPage == 'QRCode') {
                  showColoredToast(
                      AppLocalizations.of(context)!.translate('buylike_qrcode'));
                } else if (double.parse(amountSend.text.replaceAll(',', '')) ==
                    0) {
                  showColoredToast(
                      AppLocalizations.of(context)!.translate('greater_than_0'));
                }
              },
              child: Container(
                  width: mediaQuery(context, 'width', 930) / 2,
                  height: mediaQuery(context, 'height', 133),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: <Widget>[
                      Text(
                        AppLocalizations.of(context)!.translate('buylike_next'),
                        style: TextStyle(
                            fontFamily: 'Noto Sans',
                            fontSize: mediaQuery(context, 'height', 36),
                            color: LikeWalletAppTheme.gray.withOpacity(0.8)),
                        textAlign: TextAlign.right,
                      ),
                      Image.asset(
                        LikeWalletImage.icon_button_next_black,
                        height: mediaQuery(context, 'height', 133),
                      ),
                    ],
                  )),
            ),
          ],
        ));
  }
}
