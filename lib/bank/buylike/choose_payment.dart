import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/animationPage.dart';
import 'package:likewallet/bank/buylike/buylike.dart';
import 'package:likewallet/close_maintenance.dart';
import 'package:likewallet/dapp/listdapp.dart';
import 'package:likewallet/main.dart';
import 'package:likewallet/middleware/check_maintenance/check_maintenance.dart';
import 'package:likewallet/middleware/getLanguage.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as riverpod;
import 'package:likewallet/model/pageStatus.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/screen_util.dart';

class ChoosePayment extends StatefulWidget {
  ChoosePayment(
      {this.selectPage,
      required this.source,
      this.shopID,
      this.scanActive,
      required this.address,
      required this.contract,
      required this.abi,
      required this.callFunction});

//  ChoosePayment({this.rate, this.amount, this.fee, this.symbol});
//  final double rate;
//  final double amount;
//  final double fee;
//   final String symbol;
//   final String amount;
  final String source;
  final selectPage;
  final shopID;
  final scanActive;
  final String contract;
  final String address;
  final String abi;
  final String callFunction;
//  _ChoosePayment createState() =>
//      new _ChoosePayment(rate: rate, amount: amount, fee: fee, symbol: symbol);
  _ChoosePayment createState() => new _ChoosePayment(
      selectPage: selectPage,
      source: source,
      shopID: shopID,
      scanActive: scanActive,
      address: address,
      contract: contract,
      abi: abi,
      callFunction: callFunction);
}

class _ChoosePayment extends State<ChoosePayment> {
//  _ChoosePayment({this.rate, this.amount, this.fee, this.symbol});
//  final double rate;
//  final double amount;
//  final double fee;
  _ChoosePayment(
      {this.selectPage,
      required this.source,
      this.shopID,
      this.scanActive,
      required this.address,
      required this.contract,
      required this.abi,
      required this.callFunction});

  // final String symbol;
  // final String amount;
  final selectPage;
  final String source;
  final shopID;
  final scanActive;
  final String address;
  final String contract;
  final String abi;
  final String callFunction;
  bool show = false;
  late String uid;
  late String typePayment;
  late CheckAbout checkAbout;
  late OnLanguage language;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    checkAbout = OnCheckAbout();
    language = CallLanguage();
    checkFirst();
  }

  checkFirst() async {
    PageMaintenance statusPage = await checkAbout.checkTierPermission(
        tierLevel: context.read(tierLevel).state, page: 'buylike');
    if (statusPage.status == 'active') {
      //เริ่มทำงาน
      print('active');
      // setInitState();
    } else {
      //ปิด maintenance
      print('inactive');
      final lang = await language.getLanguage();
      final title = await checkAbout.selectLanguage(
          language: lang, detail: statusPage.title);
      final detail = await checkAbout.selectLanguage(
          language: lang, detail: statusPage.detail);
      final detailTime = await checkAbout.selectLanguage(
          language: lang, detail: statusPage.detail_time);
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
            builder: (context) => CloseMaintenance(
                  title: title,
                  detail: detail,
                  detailTime: detailTime,
                  url: statusPage.url,
                )),
      );
    }
  }

  buildForPhone() {
    return Scaffold(
      backgroundColor: LikeWalletAppTheme.white,
      body: GestureDetector(
        onTap: () {
          setState(() {
            FocusScopeNode currentFocus = FocusScope.of(context);
            if (!currentFocus.hasPrimaryFocus) {
              currentFocus.unfocus();
            }
          });
        },
        child: SingleChildScrollView(
          child: Column(
            // alignment: Alignment.center,
            children: <Widget>[
              Container(
                decoration: BoxDecoration(
                  color: Color(0xffF5F5F5),
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    stops: [0.0, 0.2, 0.5],
                    colors: [
                      // Colors are easy thanks to Flutter's Colors class.
                      Colors.white,
                      Colors.white,
                      LikeWalletAppTheme.white1
                    ],
                  ),
                ),
              ),
              SizedBox(
                height: mediaQuery(context, 'height', 250),
              ),
              QRCode(),
              SizedBox(height: 25),
              banking(),
              SizedBox(height: 25),
              bankingRPLC(),
              // _head(),
            ],
          ),
        ),
      ),
    );
  }

  Widget QRCode() {
    return Column(
      children: [
        GestureDetector(
          onTap: () {
            Navigator.push(
                context,
                EnterExitRoute(
                    exitPage: ChoosePayment(
                      abi: this.abi,
                      contract: this.contract,
                      address: this.address,
                      source: this.source,
                      callFunction: this.callFunction,
                    ),
                    enterPage: BuyLike(
                        selectPage: 'QRCode',
                        source: source,
                        shopID: shopID,
                        scanActive: scanActive,
                        address: address,
                        contract: contract,
                        abi: abi,
                        callFunction: callFunction)));
            // Navigator.push(
            //     context,
            //     MaterialPageRoute(
            //         builder: (context) => QRCodePromptpay(amountSend: amount)));
          },
          child: new Container(
              height: mediaQuery(context, 'height', 319),
              width: mediaQuery(context, 'width', 932),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20.0),
                color: LikeWalletAppTheme.white,
                boxShadow: [
                  BoxShadow(
                    color: LikeWalletAppTheme.black.withOpacity(0.1),
                    offset: Offset(0, 0),
                    blurRadius: 8,
                  ),
                ],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Image.asset(
                    LikeWalletImage.icon_qrcode,
                    height: mediaQuery(context, 'height', 118.65),
                    width: mediaQuery(context, 'height', 118.65),
                  ),
                  SizedBox(
                    width: mediaQuery(context, 'width', 60),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        width: mediaQuery(context, 'width', 500),
                        child: Text(
                          AppLocalizations.of(context)!
                              .translate('buylike_qrcode1'),
                          style: TextStyle(
                            fontFamily: AppLocalizations.of(context)!
                                .translate('font1'),
                            fontSize: mediaQuery(context, 'height', 45),
                            color: LikeWalletAppTheme.black,
                            letterSpacing: 0.139,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      Container(
                        width: mediaQuery(context, 'width', 600),
                        child: Text(
                          AppLocalizations.of(context)!
                              .translate('buylike_qrcode2'),
                          style: TextStyle(
                            fontFamily: AppLocalizations.of(context)!
                                .translate('font1'),
                            fontSize: mediaQuery(context, 'height', 36),
                            color: LikeWalletAppTheme.bule2_8.withOpacity(0.5),
                            letterSpacing: 0.3,
                            fontWeight: FontWeight.w100,
                          ),
                        ),
                      ),
                    ],
                  )
                ],
              )),
        ),
        _textQRCode()
      ],
    );
  }

  Widget banking() {
    return Column(
      children: [
        GestureDetector(
          onTap: () async {
            Navigator.push(
              context,
              EnterExitRoute(
                exitPage: ChoosePayment(
                  callFunction: this.callFunction,
                  contract: this.contract,
                  source: this.source,
                  address: this.address,
                  abi: this.abi,
                ),
                enterPage: BuyLike(
                  selectPage: 'Upload',
                  source: source,
                  shopID: shopID,
                  scanActive: scanActive,
                  address: address,
                  contract: contract,
                  abi: abi,
                  callFunction: callFunction,
                ),
              ),
            );
          },
          child: new Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20.0),
              color: LikeWalletAppTheme.white,
              boxShadow: [
                BoxShadow(
                  color: LikeWalletAppTheme.black.withOpacity(0.1),
                  offset: Offset(0, 0),
                  blurRadius: 8,
                ),
              ],
            ),
            height: mediaQuery(context, 'height', 319),
            width: mediaQuery(context, 'width', 932),
            alignment: Alignment.center,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  LikeWalletImage.icon_bank,
                  height: mediaQuery(context, 'height', 118.65),
                  width: mediaQuery(context, 'height', 118.65),
                ),
                SizedBox(
                  width: mediaQuery(context, 'width', 60),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      width: mediaQuery(context, 'width', 600),
                      child: Text(
                        AppLocalizations.of(context)!
                            .translate('buylike_banking1'),
                        style: TextStyle(
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font1'),
                          fontSize: mediaQuery(context, 'height', 45),
                          color: LikeWalletAppTheme.black,
                          letterSpacing: 0.139,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    Container(
                        child: Text(
                      AppLocalizations.of(context)!
                          .translate('buylike_banking2'),
                      style: TextStyle(
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                        fontSize: mediaQuery(context, 'height', 36),
                        color: LikeWalletAppTheme.bule2_8.withOpacity(0.5),
                        letterSpacing: 0.3,
                        fontWeight: FontWeight.w100,
                      ),
                    )),
                  ],
                ),
              ],
            ),
          ),
        ),
        _textBanking(),
      ],
    );
  }

  Widget bankingRPLC() {
    return Column(
      children: [
        GestureDetector(
          onTap: () async {
            final data = await FirebaseFirestore.instance
                .collection('link')
                .doc('page')
                .collection('buy_like')
                .doc('url_rplc_rofco')
                .get();
            final urlWeb = data.data()!['url'];
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => WebOpenURL(url: urlWeb)),
            );
            // Navigator.push(
            //   context,
            //   EnterExitRoute(
            //     exitPage: ChoosePayment(
            //       callFunction: this.callFunction,
            //       contract: this.contract,
            //       source: this.source,
            //       address: this.address,
            //       abi: this.abi,
            //     ),
            //     enterPage: BuyLike(
            //       selectPage: 'Upload',
            //       source: source,
            //       shopID: shopID,
            //       scanActive: scanActive,
            //       address: address,
            //       contract: contract,
            //       abi: abi,
            //       callFunction: callFunction,
            //     ),
            //   ),
            // );
          },
          child: new Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20.0),
              color: LikeWalletAppTheme.white,
              boxShadow: [
                BoxShadow(
                  color: LikeWalletAppTheme.black.withOpacity(0.1),
                  offset: Offset(0, 0),
                  blurRadius: 8,
                ),
              ],
            ),
            height: mediaQuery(context, 'height', 319),
            width: mediaQuery(context, 'width', 932),
            alignment: Alignment.center,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  LikeWalletImage.icon_bank,
                  height: mediaQuery(context, 'height', 118.65),
                  width: mediaQuery(context, 'height', 118.65),
                ),
                SizedBox(
                  width: mediaQuery(context, 'width', 60),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      width: mediaQuery(context, 'width', 600),
                      child: Text(
                        AppLocalizations.of(context)!
                                .translate('buylike_banking1') +
                            " RPLC & RAFCO",
                        style: TextStyle(
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font1'),
                          fontSize: mediaQuery(context, 'height', 45),
                          color: LikeWalletAppTheme.black,
                          letterSpacing: 0.139,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    Container(
                        child: Text(
                      AppLocalizations.of(context)!
                          .translate('buylike_banking2'),
                      style: TextStyle(
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                        fontSize: mediaQuery(context, 'height', 36),
                        color: LikeWalletAppTheme.bule2_8.withOpacity(0.5),
                        letterSpacing: 0.3,
                        fontWeight: FontWeight.w100,
                      ),
                    )),
                  ],
                ),
              ],
            ),
          ),
        ),
        _textBanking(),
      ],
    );
  }

  Widget _head() {
    return Container(
      child: Stack(
        children: <Widget>[
          Column(
            children: [Expanded(child: Container())],
          ),
          Container(
            color: LikeWalletAppTheme.bule2,
            height: mediaQuery(context, 'height', 255),
            child: Container(
              padding:
                  EdgeInsets.only(bottom: mediaQuery(context, 'height', 35)),
              alignment: Alignment.bottomCenter,
              width: MediaQuery.of(context).size.width,
              child: new Text(
                AppLocalizations.of(context)!.translate('buylike_CHOOSE'),
                style: TextStyle(
                    letterSpacing: 0.5,
                    fontFamily:
                        AppLocalizations.of(context)!.translate('font1'),
                    color: Color(0xffFFFFFF).withOpacity(1),
                    fontSize: mediaQuery(context, 'height', 45),
                    fontWeight: FontWeight.w500),
              ),
            ),
          ),
          Positioned(
              left: 0,
              top: MediaQuery.of(context).size.height * 0.08608547008,
              child: backButtonLemon(context)),
        ],
      ),
    );
  }

  Widget _textQRCode() {
    return Container(
      margin: EdgeInsets.only(top: mediaQuery(context, 'height', 30)),
      child: Text(
        AppLocalizations.of(context)!.translate('buylike_qrcode'),
        style: TextStyle(
          fontFamily: AppLocalizations.of(context)!.translate('font1'),
          fontSize: mediaQuery(context, 'height', 36),
          color: LikeWalletAppTheme.gray4.withOpacity(0.5),
          letterSpacing: 0.3,
          fontWeight: FontWeight.normal,
        ),
      ),
    );
  }

  Widget _textBanking() {
    return Container(
      margin: EdgeInsets.only(top: mediaQuery(context, 'height', 30)),
      child: Text(
        AppLocalizations.of(context)!.translate('buylike_banking'),
        style: TextStyle(
          fontFamily: AppLocalizations.of(context)!.translate('font1'),
          fontSize: mediaQuery(context, 'height', 36),
          color: LikeWalletAppTheme.gray4.withOpacity(0.5),
          letterSpacing: 0.3,
          fontWeight: FontWeight.normal,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final double shortestSide = MediaQuery.of(context).size.shortestSide;
    final bool useMobileLayout = shortestSide < 600.0;
    final Orientation orientation = MediaQuery.of(context).orientation;
    return Scaffold(
      // bottomNavigationBar: NavigationBar(),
      body: buildForPhone(),
    );
  }
}
