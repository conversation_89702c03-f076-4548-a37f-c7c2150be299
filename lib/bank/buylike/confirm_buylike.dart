import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import 'package:flutter/services.dart';

import 'package:fluttertoast/fluttertoast.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/bank/buylike/bank_upload_receipt.dart';
import 'package:likewallet/bank/buylike/qrcode_promptpay.dart';
import 'package:likewallet/bank/navigation_bar/navigation_bar_white.dart';
import 'package:likewallet/model/bank/users.dart';
import 'package:likewallet/screen/NavigationBar.dart';
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:likewallet/screen_util.dart';
import 'package:dio/dio.dart';
import 'package:likewallet/app_config.dart';
import 'package:likewallet/bank/buylike/choose_payment.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:likewallet/bank/bill_transection.dart';
import 'package:likewallet/middleware/get_mnemonic.dart';
import 'package:likewallet/libraryman//ethcontract.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:likewallet/middleware/check_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:likewallet/libraryman/configuration_service.dart';
import 'package:likewallet/libraryman/address_service.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:web3dart/web3dart.dart';
import 'package:intl/intl.dart' as formatIntl;

class confirmBuylike extends StatefulWidget {
  confirmBuylike({
    required this.amountSend,
    required this.selectPage,
    required this.titleName,
    required this.rateCurrency,
  });

  final String amountSend;
  final String selectPage;
  final String titleName;
  final String rateCurrency;

  _confirmBuylike createState() => new _confirmBuylike(
        amount: amountSend,
        selectPage: selectPage,
        titleName: titleName,
        rateCurrency: rateCurrency,
      );
}

class _confirmBuylike extends State<confirmBuylike> {
  _confirmBuylike({
    required this.selectPage,
    required this.amount,
    required this.titleName,
    required this.rateCurrency,
  });

  final String amount;
  final String selectPage;
  // final String nameTO;
  final String titleName;
  final String rateCurrency;
  double rate = 100;
  late CheckAuth Logon;
  late GetMnemonic seed;
  late String firstTO;
  String destName = "no";
  String fromName = "no";

  String lastTO = 'null';
  String nameFrom = '00000000';
  var fee = '0 LIKE';
  late String mnemonic;
  late String pketh;
  late BaseETH eth;
  bool _saving = false;
  bool _logo = false;
  late String noteMessage;
  late BaseAuth auth;
  late IAddressService addressService;
  late IConfigurationService configETH;
  TextEditingController noteController = TextEditingController();
  Dio dio = new Dio();

  bool selected = false;

  final f = new formatIntl.NumberFormat("###,###");

  @override
  void initState() {
    super.initState();

    setState(() {
      _saving = true;
    });
    auth = Auth();
    Logon = new CurCheckAuth();
    seed = new MnemonicRetrieve();
    eth = new EthContract();
    noteController.addListener(setNote);
    setInit();
  }

  setNote() {
    noteMessage = noteController.text;
  }

  void showShortToast(msg) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_SHORT,
        backgroundColor: Colors.orange,
        textColor: Colors.white);
  }

  searchName() async {
    auth.getCurrentUser().then((decodeToken) {
      // print(decodeToken);

      FirebaseFirestore.instance
          .collection('users')
          .doc(decodeToken!.uid)
          .get()
          .then((DocumentSnapshot<Map<String, dynamic>> ds) {
        setState(() {
          fromName = ds.data()!['firstName'] + " " + ds.data()!['lastName'];
          _saving = false;
        });
      });
    });
  }

  setInit() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    configETH = new ConfigurationService(pref);
    addressService = new AddressService(configETH);
    String address = configETH.getAddress();

    searchName();

    setState(() {
      nameFrom = address;
    });
  }

  void signTransaction() async {
    print(selectPage);
    if (selectPage == 'QRCode') {
      Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) => QRCodePromptpay(
                    amountSend: amount,
                    titleName: '',
                    rateCurrency: 0,
                  )));
    }
    if (selectPage == 'Upload') {
      Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) => BankUploadReceipt(
                  amountSend: amount, titleName: '', rateCurrency: 0.0)));
    }
//     Navigator.push(
//         context,
//         MaterialPageRoute(
//             builder: (context) => ChoosePayment(
//                 amount: amount,
// //                amountSend: amountSend.text,
// //                titleName: 'oil',
//                 symbol: 'THB')));
//    eth.getBalance(address: configETH.getAddress()).then((avaiBalance) {
//      if ((avaiBalance >= double.parse(amount) * rateCurrency * rate)) {
//        Logon.checkLogin().then((login) {
//          seed.getMnemonic().then((value) {
//            mnemonic = value.split(":")[0];
//            pketh = value.split(":")[1];
//
////        eth.sendTransaction(pk: pketh, to: nameTO.trim(), value: (double.parse(amount)*rateCurrency*rate).toString()).then((tx) {
//            if (contract == 'no') {
//              eth
//                  .transferMessage(
//                      pk: pketh,
//                      to: nameTO.trim(),
//                      value: (double.parse(amount) * rateCurrency * rate)
//                          .toString(),
//                      message: noteController.text.toString())
//                  .then((tx) {
//                print(tx);
//                setState(() {
//                  _saving = false;
//                  _logo = true;
//                });
//                setState(() {
//                  changeMenu(1);
//                });
//              });
//            } else if (contract.length == 42 &&
//                callFunction != 'no' &&
//                abi != 'no') {
//              eth
//                  .dynamicSendContract(
//                      pk: pketh,
//                      to: nameTO.trim(),
//                      value: (double.parse(amount) * rateCurrency * rate)
//                          .toString(),
//                      message: noteController.text.toString(),
//                      callFunction: callFunction,
//                      abi: abi,
//                      contractAddress: contract)
//                  .then((tx) {
//                print(tx);
//                setState(() {
//                  _saving = false;
//                  _logo = true;
//                });
//                setState(() {
//                  changeMenu(1);
//                });
//              });
//            }
//          });
//        });
//      } else {
//        setState(() {
//          _saving = false;
//        });
//        showShortToast(AppLocalizations.of(context)!.translate('not_enough') +
//            AppLocalizations.of(context)!.translate('not_enough_tail') +
//            " " +
//            (avaiBalance).toString() +
//            " LIKE");
//      }
//    });
  }

  _sendTransaction() async {
    //Simulate a service call
    print('submitting to backend...');
    setState(() {
//      _saving = true;
    });
    signTransaction();
  }

  buildForPhone(Orientation orientation) {
    return Scaffold(
      body: ModalProgressHUD(
        opacity: 0.1,
        child: GestureDetector(
          onTap: () {
            FocusScopeNode currentFocus = FocusScope.of(context);

            if (!currentFocus.hasPrimaryFocus) {
              currentFocus.unfocus();
            }
          },
          child: Stack(
            children: <Widget>[
              Column(
                children: <Widget>[
                  Container(
                    color: Color(0xff141322),
                    height: MediaQuery.of(context).size.height * 0.10945299145,
                    child: Container(
                      padding: EdgeInsets.only(
                          bottom: mediaQuery(context, 'height', 35)),
                      alignment: Alignment.bottomCenter,
                      width: MediaQuery.of(context).size.width,
                      child: new Text(
                        AppLocalizations.of(context)!
                            .translate('buylike_CONFIRM'),
                        style: TextStyle(
                            letterSpacing: 0.5,
                            fontFamily: AppLocalizations.of(context)!
                                .translate('font1'),
                            color: Color(0xffFFFFFF).withOpacity(1),
                            fontSize: MediaQuery.of(context).size.height *
                                0.01794871794,
                            fontWeight: FontWeight.normal),
                      ),
                    ),
                  ),
                  Expanded(
                    child: Container(
                      height: MediaQuery.of(context).size.height,
                    ),
                  ),
                ],
              ),
              Positioned(
                  top: MediaQuery.of(context).size.height * 0.08608547008,
                  child: backButtonLemon(context)),
              Positioned(
                top: mediaQuery(context, 'height', 1003),
                right: mediaQuery(context, 'width', 98),
                child: new GestureDetector(
                  onTap: () {
                    _sendTransaction();
                  },
                  child: Container(
                    alignment: Alignment.center,

                    height: MediaQuery.of(context).size.height *
                        0.10854700854, // height of the button
                    width:
                        MediaQuery.of(context).size.height * 0.10854700854, //
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Color(0xff2B3038).withOpacity(1),
                      boxShadow: [
                        BoxShadow(
                          spreadRadius: 0,
                          blurRadius: 10,
                          color: LikeWalletAppTheme.black.withOpacity(0.3),
                          offset: Offset(
                            0.0,
                            5.0,
                          ),
                        ),
                      ],
                    ),
                    child: new Text(
                      AppLocalizations.of(context)!
                          .translate('bankingTran_button'),
                      style: TextStyle(
                          color: Color(0xffB4E60D),
                          letterSpacing: 1,
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font1'),
                          fontWeight: FontWeight.normal,
                          fontSize: mediaQuery(context, 'height', 39)),
                    ),
                  ),
                ),
              ),

              ///NAME
              _title(
                  427,
                  AppLocalizations.of(context)!.translate('buylike_Name'),
                  fromName,
                  ''),

              ///YOU WILL GET
              _title(
                  620,
                  AppLocalizations.of(context)!.translate('buylike_GET'),
                  f.format(double.parse(rateCurrency)).toString(),
                  'LIKE'),

              ///AMOUNT TO BE PAID
              _title(
                  818,
                  AppLocalizations.of(context)!.translate('buylike_PAID'),
                  amount.toString(),
                  'BAHT')
            ],
          ),
        ),
        inAsyncCall: _saving,
        progressIndicator: CustomLoading(),
//              color: Color(0xff707071),
//              opacity: 0.9,
//              progressIndicator: _Progress()
      ),
    );
  }

  Widget _title(double height, title, value, symbol) {
    return

        ///NAME
        Positioned(
            top: mediaQuery(context, 'height', height),
            left: mediaQuery(context, 'width', 214),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  title,
                  style: TextStyle(
                    letterSpacing: 0.3,
                    fontWeight: FontWeight.normal,
                    fontSize: mediaQuery(context, 'height', 33),
                    fontFamily:
                        AppLocalizations.of(context)!.translate('font1'),
                    color: LikeWalletAppTheme.gray.withOpacity(0.3),
                  ),
                ),
                Text(
                  value + " " + symbol,
                  style: TextStyle(
                    letterSpacing: 0.3,
                    fontSize: mediaQuery(context, 'height', 48),
                    fontWeight: FontWeight.normal,
                    fontFamily:
                        AppLocalizations.of(context)!.translate('font1'),
                    color: LikeWalletAppTheme.gray.withOpacity(0.6),
                  ),
                ),
              ],
            ));
  }

  int tabMenu = 0;
  changeMenu(_number) async {
    setState(() {
      tabMenu = _number;
    });
  }

  @override
  Widget build(BuildContext context) {
    final double shortestSide = MediaQuery.of(context).size.shortestSide;
    final bool useMobileLayout = shortestSide < 600.0;
    final Orientation orientation = MediaQuery.of(context).orientation;
    return Scaffold(
      bottomNavigationBar: NavigationBarWhite(),
      body: useMobileLayout
          ? buildForPhone(orientation)
          : buildForPhone(orientation),
    );
  }

  _loadingView() {
    setState(() {
      _saving = true;
    });
    //Simulate a service call
    print('submitting to backend...');
    new Future.delayed(new Duration(seconds: 2), () {
      setState(() {
        changeMenu(1);
      });
    });
  }

  Widget _Progress() {
    return Container(
        alignment: Alignment.bottomCenter,
        padding: EdgeInsets.only(bottom: MediaQuery.of(context).size.width / 3),
        child: Image.asset(
          'assets/image/success.png',
          scale: 3.5,
        ));
  }
}
