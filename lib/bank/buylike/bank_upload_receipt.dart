import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/bank/navigation_bar/navigation_bar_white.dart';
import 'package:likewallet/libraryman/address_service.dart';
import 'package:likewallet/libraryman/configuration_service.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:likewallet/screen/NavigationBar.dart';
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../Theme.dart';
import '../../ImageTheme.dart';
import '../../screen_util.dart';
import 'dart:async';
import 'dart:typed_data';
import 'dart:convert';
import 'dart:io';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:ui' as ui;
import 'package:flutter/rendering.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:flutter/services.dart';
import 'package:likewallet/bank/completeTX.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:likewallet/app_config.dart';
import 'package:http/http.dart' as http;

class BankUploadReceipt extends StatefulWidget {
  BankUploadReceipt({
    required this.amountSend,
    required this.titleName,
    required this.rateCurrency,
  });

  final String amountSend;
  final String titleName;
  final double rateCurrency;

  _BankUploadReceipt createState() => new _BankUploadReceipt(
        amount: amountSend,
        titleName: titleName,
        rateCurrency: rateCurrency,
        nameTO: '',
      );
}

class _BankUploadReceipt extends State<BankUploadReceipt>
    with TickerProviderStateMixin {
  _BankUploadReceipt({
    required this.nameTO,
    required this.amount,
    required this.titleName,
    required this.rateCurrency,
  });
  bool statusUpload = false;
  final String amount;
  final String nameTO;
  final String titleName;
  late BaseAuth auth;

  FirebaseFirestore fireStore = FirebaseFirestore.instance;
  final double rateCurrency;
  GlobalKey _globalKey = new GlobalKey();
  late Uint8List imageInMemory;
  final ImagePicker _picker = ImagePicker();
  late SharedPreferences pref;
  late IAddressService addressService;
  late IConfigurationService configETH;
  bool _saving = false;

  String nameAcc = '..loading';
  String numAcc = '..loading';
  String bankAcc = '..loading';
  @override
  void initState() {
    super.initState();
    auth = Auth();
    setInit();
    setState(() {});
  }

  setInit() async {
    fireStore.collection('accountBuyLike').doc('bank').get().then((value) {
      setState(() {
        nameAcc = value.data()?["name"];
        numAcc = value.data()?["number"];
        bankAcc = value.data()?["bank"];
      });
    });
    pref = await SharedPreferences.getInstance();
    configETH = new ConfigurationService(pref);
  }

  buildForPhone() {
    return Scaffold(
      // resizeToAvoidBottomPadding: false,
      resizeToAvoidBottomInset: false,
      body: GestureDetector(
        onTap: () {
          FocusScopeNode currentFocus = FocusScope.of(context);
          if (!currentFocus.hasPrimaryFocus) {
            currentFocus.unfocus();
          }
        },
        child: Stack(
          alignment: Alignment.center,
          children: <Widget>[
            ///Background
            BG(),

            /// Buttton Back Page
            _buttonBackPage(),

            ///Text Head PromptPay
            _textHead(),

            ///Title Line 1
            _titleLine1(),

            ///Name
            _title(
                573,
                AppLocalizations.of(context)!.translate('buylike_bank3'),
                nameAcc),

            ///Name
            _title(
                759,
                AppLocalizations.of(context)!.translate('buylike_bank4'),
                bankAcc),

            ///Name
            _title(
                945,
                AppLocalizations.of(context)!.translate('buylike_bank5'),
                numAcc),

            ///Button Upload
            _buttonUpload(),

            ///Button Done
            statusUpload ? _buttonDone() : Container(),
//            ///Button OK
//            _buttonOK()
          ],
        ),
      ),
    );
  }

  Widget BG() {
    return Column(
      children: <Widget>[
        Container(
          color: Color(0xff141322),
          height: MediaQuery.of(context).size.height * 0.***********,
        ),
//        Expanded(
//          child: Container(
//            height: MediaQuery.of(context).size.height,
//          ),
//        ),
      ],
    );
  }

  Widget _textHead() {
    return Positioned(
      top: MediaQuery.of(context).size.height * 0.***********,
      child: Container(
        alignment: Alignment.center,
        width: MediaQuery.of(context).size.width,
        child: new Text(
          AppLocalizations.of(context)!.translate('buylike_bank1'),
          style: TextStyle(
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              color: Color(0xffFFFFFF).withOpacity(1),
              fontSize: MediaQuery.of(context).size.height * 0.***********,
              fontWeight: FontWeight.bold),
        ),
      ),
    );
  }

  Widget _titleLine1() {
    return Positioned(
      top: mediaQuery(context, 'height', 392),
      child: Text(
        AppLocalizations.of(context)!.translate('buylike_bank2'),
        style: TextStyle(
          letterSpacing: 0,
          fontWeight: FontWeight.w700,
          fontSize: mediaQuery(context, 'height', 42),
          fontFamily: AppLocalizations.of(context)!.translate('font1'),
          color: LikeWalletAppTheme.gray4.withOpacity(1),
        ),
      ),
    );
  }

  Widget _buttonDone() {
    return Positioned(
      top: mediaQuery(context, 'height', 1573),
      child: new GestureDetector(
        onTap: () {
          Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) => CompleteTX(
                      title: AppLocalizations.of(context)!
                          .translate('buylike_completed3'),
                      detail: AppLocalizations.of(context)!
                          .translate('buylike_completed4'),
                      back: '/home')));
        },
        child: Container(
          alignment: Alignment.center,
          height: mediaQuery(context, 'height', 182),
          width: mediaQuery(context, 'height', 182),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Color(0xff2B3038).withOpacity(1),
            boxShadow: [
              BoxShadow(
                color: LikeWalletAppTheme.gray.withOpacity(0.5),
                offset: Offset(0, 1),
                spreadRadius: 1,
                blurRadius: 5,
              ),
            ],
          ),
          child: new Text(
            AppLocalizations.of(context)!.translate('buylike_bank_done'),
            style: TextStyle(
                color: Color(0xffB4E60D),
                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                fontWeight: FontWeight.normal,
                fontSize: MediaQuery.of(context).size.height * 0.************),
          ),
        ),
      ),
    );
  }

  Widget _title(double height, title, detail) {
    return Positioned(
      top: mediaQuery(context, 'height', height),
      child: Column(
        children: <Widget>[
          Text(
            title,
            style: TextStyle(
              letterSpacing: 0,
              fontWeight: FontWeight.w500,
              fontSize: mediaQuery(context, 'height', 33),
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              color: LikeWalletAppTheme.gray.withOpacity(0.3),
            ),
          ),
          Text(
            detail,
            style: TextStyle(
              letterSpacing: 0,
              fontWeight: FontWeight.w500,
              fontSize: mediaQuery(context, 'height', 48),
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              color: LikeWalletAppTheme.gray.withOpacity(1),
            ),
          ),
        ],
      ),
    );
  }

  Widget _newQRcode() {
    return Positioned(
      top: mediaQuery(context, 'height', 1050),
      child: Row(
        children: <Widget>[
          Text(
            'Can’t do it in time? Send me',
            style: TextStyle(
              letterSpacing: 0.3,
              fontWeight: FontWeight.normal,
              fontSize: mediaQuery(context, 'height', 36),
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              color: LikeWalletAppTheme.gray.withOpacity(0.3),
            ),
          ),
          SizedBox(width: mediaQuery(context, 'width', 20)),
          Container(
            alignment: Alignment.center,
            width: mediaQuery(context, 'width', 250),
            height: mediaQuery(context, 'height', 86),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(43.0),
              border: Border.all(
                  width: mediaQuery(context, 'width', 3),
                  color: LikeWalletAppTheme.gray4.withOpacity(0.3)),
            ),
            child: Text(
              'New Code',
              style: TextStyle(
                letterSpacing: 0.3,
                fontWeight: FontWeight.normal,
                fontSize: mediaQuery(context, 'height', 36),
                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                color: LikeWalletAppTheme.gray4.withOpacity(0.3),
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget _buttonUpload() {
    return Positioned(
      top: mediaQuery(context, 'height', 1235),
      child: Container(
          width: mediaQuery(context, 'width', 930),
          height: mediaQuery(context, 'height', 261),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(131.0),
            color: LikeWalletAppTheme.white,
            boxShadow: [
              BoxShadow(
                color: LikeWalletAppTheme.gray.withOpacity(0.3),
                offset: Offset(0, 6),
                blurRadius: 10,
              ),
            ],
          ),
          child: Stack(
            children: <Widget>[
              Container(
                  alignment: Alignment.center,
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: <Widget>[
                      Container(
                        margin: EdgeInsets.only(
                          left: mediaQuery(context, 'width', 53),
                        ),
                        child: Text(
                          statusUpload
                              ? AppLocalizations.of(context)!
                                  .translate('buylike_bank7')
                              : AppLocalizations.of(context)!
                                  .translate('buylike_bank6'),
                          style: TextStyle(
                            letterSpacing: 0,
                            fontWeight: FontWeight.bold,
                            fontSize: mediaQuery(context, 'height', 42),
                            fontFamily: AppLocalizations.of(context)!
                                .translate('font1'),
                            color: LikeWalletAppTheme.gray.withOpacity(1),
                          ),
                        ),
                      ),
                      statusUpload ? _buttonBack() : Container(),
                      _buttonOK(),
                    ],
                  )),
            ],
          )),
    );
  }

  Widget _buttonBackPage() {
    return Positioned(
      top: mediaQuery(context, 'height', 207),
      left: 0,
      child: GestureDetector(
        onTap: () => {Navigator.of(context).pop()},
        child: Container(
          decoration: BoxDecoration(
            color: Color(0xffB4E60D),
            borderRadius: new BorderRadius.only(
                bottomRight: Radius.circular(40.0),
                topRight: Radius.circular(40.0)),
          ),
          height: MediaQuery.of(context).size.height * 0.04797863247,
          width: MediaQuery.of(context).size.width * 0.18317592592,
          child: Icon(
            Icons.arrow_back_ios,
//                        color: Colors.blue,
            size: MediaQuery.of(context).size.height * 0.0190042735,
          ),
        ),
      ),
    );
  }

  Widget _buttonBack() {
    return GestureDetector(
      onTap: () {
        setState(() {
          statusUpload = false;
        });
      },
      child: Container(
        alignment: Alignment.centerRight,
        width: mediaQuery(context, 'width', 129),
        child: Image.asset(
          LikeWalletImage.icon_back,
          height: mediaQuery(context, 'height', 28),
        ),
      ),
    );
  }

  Future<List<String>> upload(File file) async {
    if (file == null) return ['false'];
    String base64Image = base64Encode(file.readAsBytesSync());
    String fileName = file.path.split("/").last;
    var url = Uri.https(env.apiCheck, 'uploadImageBuy');
    final response = await http.post(url, body: {
      "image": base64Image,
      "name": fileName,
    });
    final body = json.decode(response.body);

    print(body["result"]);
    print(body["result"]["id"]);
    print(body["result"]["url"]);
    return [body["result"]["id"], body["result"]["url"]];
  }

  Future<bool> saveSlip(String pathPic) async {
    User? user = FirebaseAuth.instance.currentUser;
    var url = Uri.https(env.apiUrl, '/buyLikepoint');

    print(amount.toString());
    // print(await user!.getIdToken());
    print(pathPic);
    print(user!.phoneNumber);
    print(configETH.getAddress());
    var response = await http.post(url, body: {
      '_token': await user.getIdToken(),
      'paymentMethod': 'bank',
      'bankName': 'Likewallet',
      'accountNumber': 'Likewallet',
      'baht': amount.toString(),
      'slip': pathPic,
      'phoneNumber': user.phoneNumber == null ? 'no_phone' : user.phoneNumber,
      'address': configETH.getAddress()
    });
    print(response);
    var body = json.decode(response.body);
    print(body["statusCode"]);
    int statusCode = body["statusCode"];
    if (statusCode == 200) {
      print('save');
      setState(() {
        _saving = false;
        print(_saving);
      });
      return true;
    } else {
      setState(() {
        _saving = false;
      });
      return false;
    }
  }

  Widget _buttonOK() {
    return new GestureDetector(
        onTap: () {
          getImage().then((_imageFile) async {
            upload(_imageFile).then((thenUpload) {
              saveSlip(thenUpload[1]).then((success) async {
                uploadImage(_imageFile);
              });
            });
          });
        },
        child: statusUpload
            ? Container(
                margin: EdgeInsets.only(
                  right: mediaQuery(context, 'width', 53),
                ),
                alignment: Alignment.center,
                height: mediaQuery(context, 'height', 160),
                width: mediaQuery(context, 'height', 160),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: LikeWalletAppTheme.lemon1,
                  // boxShadow: [
                  //   BoxShadow(
                  //     color: LikeWalletAppTheme.gray.withOpacity(0.3),
                  //     offset: Offset(0, 1),
                  //     spreadRadius: 0,
                  //     blurRadius: 3,
                  //   ),
                  // ],
                ),
                child: Image.asset(
                  LikeWalletImage.icon_success,
                  height: mediaQuery(context, 'height', 51.17),
                ))
            : Container(
                margin: EdgeInsets.only(
                  right: mediaQuery(context, 'width', 53),
                ),
                alignment: Alignment.center,
                height: mediaQuery(context, 'height', 160),
                width: mediaQuery(context, 'height', 160),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: LikeWalletAppTheme.white,
                  boxShadow: [
                    BoxShadow(
                      color: LikeWalletAppTheme.gray.withOpacity(0.3),
                      offset: Offset(0, 1),
                      spreadRadius: 0,
                      blurRadius: 3,
                    ),
                  ],
                ),
                child: Image.asset(
                  LikeWalletImage.contact_us_photo,
                  height: mediaQuery(context, 'height', 65),
                )));
  }

  Future<File> getImage() async {
    var image = await _picker.pickImage(
        source: ImageSource.gallery, maxHeight: 600, maxWidth: 600);
    return File(image!.path);
  }

  void uploadImage(image) async {
    //API UPLOAD PHOTO
    if (image != null) {
      setState(() {
        statusUpload = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final double shortestSide = MediaQuery.of(context).size.shortestSide;
    final bool useMobileLayout = shortestSide < 600.0;
    final Orientation orientation = MediaQuery.of(context).orientation;
    return ModalProgressHUD(
        opacity: 0.1,
        inAsyncCall: _saving,
        progressIndicator: CustomLoading(),
        child: Scaffold(
            bottomNavigationBar: NavigationBarWhite(), body: buildForPhone()));
  }
}
