import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/bank/navigation_bar/navigation_bar_white.dart';
import 'package:likewallet/model/bank/users.dart';
import 'package:likewallet/screen/NavigationBar.dart';
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/libraryman/app_local.dart';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/libraryman/address_service.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:likewallet/libraryman/configuration_service.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:likewallet/screen/NavigationBar.dart';
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../Theme.dart';
import '../../ImageTheme.dart';
import '../../screen_util.dart';
import 'dart:async';
import 'dart:typed_data';
import 'dart:convert';
import 'dart:io';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:ui' as ui;
import 'package:flutter/rendering.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:flutter/services.dart';
import 'package:likewallet/bank/completeTX.dart';
import 'package:likewallet/app_config.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:intl/intl.dart' as formatIntl;

class QRCodePromptpay extends StatefulWidget {
  QRCodePromptpay({
    required this.amountSend,
    required this.titleName,
    required this.rateCurrency,
  });

  final String amountSend;
  final String titleName;
  final double rateCurrency;

  _QRCodePromptpay createState() => new _QRCodePromptpay(
        amount: amountSend,
        titleName: titleName,
        rateCurrency: rateCurrency,
      );
}

class _QRCodePromptpay extends State<QRCodePromptpay>
    with TickerProviderStateMixin {
  _QRCodePromptpay({
    required this.amount,
    required this.titleName,
    required this.rateCurrency,
  });

  GlobalKey globalKey = new GlobalKey();

  bool statusTranfer = false;
  final String amount;
  final String titleName;
  final double rateCurrency;
  GlobalKey _globalKey = new GlobalKey();
  late Uint8List imageInMemory;

  late BaseAuth FirebaseAuth;
  Dio dio = new Dio();
  bool _saving = false;
  late IConfigurationService configETH;
  late IAddressService addressService;
  late String mnemonic;
  String addr = "no";
  String qrCode = "";
  String requestDt = "";
  late AnimationController controller;
  late Timer timer;
  int countLoop = 0;
  late String partnerTxnUid;
  late String origPartnerTxnUid;
  @override
  void dispose() {
    controller.dispose();
    timer.cancel();
    super.dispose();
  }

//  void _listenForPermissionStatus() async {
//    final status = await _permission.status;
//    setState(() => _permissionStatus = status);
//
//  }
  @override
  void initState() {
    super.initState();
//    _listenForPermissionStatus();

    controller = AnimationController(
      vsync: this,
      duration: Duration(seconds: 10),
    );
    Future.delayed(Duration.zero, () {
      setState(() {
        _saving = true;
      });
      FirebaseAuth = Auth();
      generateQR();
    });
  }

  int get timerOut {
    return (controller.duration! * controller.value).inMilliseconds;
  }

  String get timerString {
    Duration duration = controller.duration! * controller.value;
    return '${(duration.inMinutes % 60).toString().padLeft(2, '0')}:${(duration.inSeconds % 60).toString().padLeft(2, '0')}';
  }

  inquiry() async {
    FirebaseAuth.getTokenFirebase().then((token) {
      FirebaseAuth.getCurrentUser().then((snapshot) {
        FirebaseFirestore.instance
            .collection('users')
            .doc(snapshot!.uid)
            .get()
            .then((DocumentSnapshot ds) async {
          Response response =
              await dio.post('https://' + env.apiUrl + "/inquiryBuy", data: {
            "_token": token,
            "partnerTxnUid": partnerTxnUid,
            "origPartnerTxnUid": origPartnerTxnUid
          });
          // print(response.data);
          if (response.data["result"]["statusCode"] == 200) {
            print(response.data["tx"]);
            print(response.data["body"]);
            setState(() {
              statusTranfer = !statusTranfer;
            });
            countLoop = 1;
            timer.cancel();
            new Future.delayed(new Duration(seconds: 3), () {
              Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => CompleteTX(
                          title: AppLocalizations.of(context)!
                              .translate('buylike_completed1'),
                          detail: AppLocalizations.of(context)!
                              .translate('buylike_completed2'),
                          back: '/home')));
            });
          }
        });
      });
    });
  }

  generateQR() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    configETH = new ConfigurationService(pref);
    addressService = new AddressService(configETH);
    mnemonic = await configETH.getMnemonic();
    addr = configETH.getAddress();

    if (addr != "no")
      setState(() {
        FirebaseAuth.getTokenFirebase().then((token) {
          FirebaseAuth.getCurrentUser().then((snapshot) {
            final users = FirebaseFirestore.instance
                .collection('users')
                .doc(snapshot!.uid)
                .get()
                .then((ds) async {
              Response response = await dio
                  .post('https://' + env.apiUrl + "/createOrderBuy", data: {
                "_token": token,
                "walletNumber": ds.data()!['wallet_number'] == "1"
                    ? "0"
                    : ds.data()!['wallet_number'],
                "address": addr,
                "amount": amount.replaceAll(",", "")
              });
              print(response.data["result"]);
              if (response.data["statusCode"] == 200) {
                print(response.data["result"]);

                var parsedDate = DateTime.parse(
                    response.data["result"][0]["requestDt"].toString());
                int startTime = parsedDate.millisecondsSinceEpoch;
                final diffTime =
                    ((startTime / 1000) + 600) - (startTime / 1000);

                print(diffTime);
                int valueSend = diffTime.toString().indexOf('.');
                controller = AnimationController(
                  vsync: this,
                  duration: Duration(
                      seconds: int.parse(diffTime
                                  .toString()
                                  .substring(0, valueSend)) <=
                              0.0
                          ? 0
                          : int.parse(
                              diffTime.toString().substring(0, valueSend))),
                );
                {
                  if (controller.isAnimating)
                    controller.stop();
                  else {
                    controller.reverse(
                        from: controller.value == 0.0 ? 1.0 : controller.value);
                  }
                }
                ;

                setState(() {
                  partnerTxnUid =
                      response.data["result"][0]["partnerTxnUid"].toString();
                  origPartnerTxnUid =
                      response.data["result"][0]["partnerTxnUid"].toString();
                  print(origPartnerTxnUid);
                  qrCode = response.data["result"][0]["qrcode"].toString();
                  requestDt =
                      response.data["result"][0]["requestDt"].toString();

                  _saving = false;
                });
                timer = Timer.periodic(new Duration(seconds: 5), (timerx) {
                  if (countLoop == 0) {
                    if (timerOut <= 0) {
                      countLoop = 1;
                      timer.cancel();
                    }
                    inquiry();
                  }
                });
              } else {
                //dismiss
                _saving = false;
                //back
              }
            });
          });
        });
      });
  }

  buildForPhone() {
    return GestureDetector(
      onTap: () {
        FocusScopeNode currentFocus = FocusScope.of(context);

        if (!currentFocus.hasPrimaryFocus) {
          currentFocus.unfocus();
        }
      },
      child: Scaffold(
        // resizeToAvoidBottomPadding: false,
        // resizeToAvoidBottomInset: false,
        body: Stack(
          // alignment: Alignment.center,
          children: <Widget>[
            SingleChildScrollView(
              child: Column(
                children: [
                  SizedBox(
                    height: 350.sp,
                  ),

                  ///Title Line 1
                  _titleLine1(),
                  SizedBox(
                    height: 106.sp,
                  ),

                  ///View QRCode
                  _QRCode(),
                  SizedBox(
                    height: 92.sp,
                  ),

                  ///Button New Qrcode
                  _newQRcode(),
                  SizedBox(
                    height: 106.h,
                  ),

                  ///Button Save Qrcode
                  _buttonSave(),
                  SizedBox(
                    height: 40.h,
                  ),

                  ///Button Ststus
                  statusTranfer ? _buttonStstus() : Container(),
                  SizedBox(
                    height: 106.h,
                  ),
                ],
              ),
            ),

            ///Background
            BG(),

            /// Buttton Back Page
            _buttonBackPage(),
//            ///Button OK
//            _buttonOK()
          ],
        ),
      ),
    );
  }

  Widget BG() {
    return Column(
      children: <Widget>[
        Container(
          color: Color(0xff141322),
          height: MediaQuery.of(context).size.height * 0.10945299145,
          child: Container(
            padding: EdgeInsets.only(bottom: mediaQuery(context, 'height', 35)),
            alignment: Alignment.bottomCenter,
            width: MediaQuery.of(context).size.width,
            child: new Text(
              AppLocalizations.of(context)!.translate('buylike_promptpay4'),
              style: TextStyle(
                  letterSpacing: 0.5,
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  color: Color(0xffFFFFFF).withOpacity(1),
                  fontSize: MediaQuery.of(context).size.height * 0.01794871794,
                  fontWeight: FontWeight.normal),
            ),
          ),
        ),
        Expanded(
          child: Container(
            height: MediaQuery.of(context).size.height,
          ),
        ),
      ],
    );
  }

  Widget _titleLine1() {
    return Container(
      width: 880.sp,
      child: AnimatedBuilder(
        animation: controller,
        builder: (context, child) {
          return controller.isAnimating
              ? Text(
                  AppLocalizations.of(context)!
                          .translate('buylike_promptpay1') +
                      " " +
                      timerString +
                      " " +
                      AppLocalizations.of(context)!
                          .translate('buylike_promptpay1_2'),
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    letterSpacing: 0,
                    fontWeight: FontWeight.w500,
                    fontSize: mediaQuery(context, 'height', 42),
                    fontFamily:
                        AppLocalizations.of(context)!.translate('font1'),
                    color: Color(0xff3C3C43),
                  ),
                )
              : Text(
                  AppLocalizations.of(context)!
                          .translate('buylike_promptpay1') +
                      " " +
                      timerString +
                      " " +
                      AppLocalizations.of(context)!
                          .translate('buylike_promptpay1_2'),
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    letterSpacing: 0,
                    fontWeight: FontWeight.w500,
                    fontSize: mediaQuery(context, 'height', 42),
                    fontFamily:
                        AppLocalizations.of(context)!.translate('font1'),
                    color: LikeWalletAppTheme.gray.withOpacity(1),
                  ),
                );
        },
      ),
    );
  }

  Widget _QRCode() {
    return Stack(
      alignment: Alignment.center,
      children: [
        Container(
          height: 650.h,
          width: 650.h,
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                  color: Colors.black.withOpacity(0.16),
                  offset: Offset(0, 35.h),
                  blurRadius: 75.h,
                  spreadRadius: 0),
            ],
          ),
        ),
        Container(
          height: 650.h,
          width: 650.h,
          child: RepaintBoundary(
            key: _globalKey,
            child: QrImageView(
              // size: mediaQuery(context, "height", 500.5),
              embeddedImage: AssetImage(LikeWalletImage.icon_buylike),
              embeddedImageStyle: QrEmbeddedImageStyle(
                size: Size(107.sp, 107.sp),
              ),
              data: qrCode,
              version: QrVersions.auto,
              backgroundColor: LikeWalletAppTheme.white,
              // errorCorrectionLevel: QrErrorCorrectLevel.Q,
              gapless: false,
            ),
          ),
        ),
      ],
    );
  }

  Widget _newQRcode() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: <Widget>[
        Text(
          AppLocalizations.of(context)!.translate('buylike_promptpay2'),
          style: TextStyle(
            letterSpacing: 0.3,
            fontWeight: FontWeight.normal,
            fontSize: mediaQuery(context, 'height', 36),
            fontFamily: AppLocalizations.of(context)!.translate('font1'),
            color: LikeWalletAppTheme.gray.withOpacity(0.3),
          ),
        ),
        SizedBox(width: mediaQuery(context, 'width', 20)),
        GestureDetector(
          onTap: () {
            setState(() {
              _saving = true;
            });
            generateQR();
          },
          child: Container(
            alignment: Alignment.center,
            width: mediaQuery(context, 'width', 250),
            height: mediaQuery(context, 'height', 86),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(43.0),
              border: Border.all(
                  width: mediaQuery(context, 'width', 3),
                  color: LikeWalletAppTheme.gray4.withOpacity(0.1)),
            ),
            child: Text(
              AppLocalizations.of(context)!.translate('buylike_promptpay3'),
              style: TextStyle(
                letterSpacing: 0.3,
                fontWeight: FontWeight.normal,
                fontSize: mediaQuery(context, 'height', 36),
                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                color: LikeWalletAppTheme.gray4.withOpacity(0.7),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buttonSave() {
    return Container(
      width: mediaQuery(context, 'width', 930),
      height: mediaQuery(context, 'height', 261),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(131.0),
        color: LikeWalletAppTheme.white,
        boxShadow: [
          BoxShadow(
            color: LikeWalletAppTheme.gray.withOpacity(0.2),
            offset: Offset(0, 2),
            blurRadius: 5,
          ),
        ],
      ),
      child: Stack(
        children: <Widget>[
          Container(
              padding: EdgeInsets.only(
                left: mediaQuery(context, 'width', 86),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Text(
                            AppLocalizations.of(context)!
                                .translate('buylike_promptpay4'),
                            style: TextStyle(
                              letterSpacing: 0,
                              fontWeight: FontWeight.bold,
                              fontSize: mediaQuery(context, 'height', 42),
                              fontFamily: AppLocalizations.of(context)!
                                  .translate('font1'),
                              color: LikeWalletAppTheme.gray.withOpacity(1),
                            ),
                          ),
                          SizedBox(
                            width: mediaQuery(context, 'width', 20),
                          ),
                          Text(
                            AppLocalizations.of(context)!
                                .translate('buylike_promptpay5'),
                            style: TextStyle(
                              letterSpacing: 0,
                              fontWeight: FontWeight.normal,
                              fontSize: mediaQuery(context, 'height', 42),
                              fontFamily: AppLocalizations.of(context)!
                                  .translate('font1'),
                              color: LikeWalletAppTheme.gray.withOpacity(0.3),
                            ),
                          ),
                        ],
                      ),
                      Text(
                        AppLocalizations.of(context)!
                            .translate('buylike_promptpay6'),
                        style: TextStyle(
                          letterSpacing: 0,
                          fontWeight: FontWeight.normal,
                          fontSize: mediaQuery(context, 'height', 42),
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font1'),
                          color: LikeWalletAppTheme.gray.withOpacity(0.3),
                        ),
                      ),
                    ],
                  ),
                  _save(),
                ],
              )),
        ],
      ),
    );
  }

  Widget _buttonStstus() {
    return Positioned(
      top: mediaQuery(context, 'height', 1512),
      child: Container(
          width: mediaQuery(context, 'width', 930),
          height: mediaQuery(context, 'height', 261),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(131.0),
            color: LikeWalletAppTheme.white,
            boxShadow: [
              BoxShadow(
                color: LikeWalletAppTheme.gray.withOpacity(0.3),
                offset: Offset(0, 6),
                blurRadius: 10,
              ),
            ],
          ),
          child: Stack(
            children: <Widget>[
              Container(
                  alignment: Alignment.center,
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: <Widget>[
                      _buttonSuccess(),
                      SizedBox(
                        width: mediaQuery(context, 'width', 30),
                      ),
                      Text(
                        AppLocalizations.of(context)!
                            .translate('buylike_promptpay7'),
                        style: TextStyle(
                          letterSpacing: 0,
                          fontWeight: FontWeight.bold,
                          fontSize: mediaQuery(context, 'height', 42),
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font1'),
                          color: LikeWalletAppTheme.gray.withOpacity(1),
                        ),
                      ),
                      // _buttonOK(),
                    ],
                  )),
            ],
          )),
    );
  }

  Widget _buttonBackPage() {
    return Positioned(
        top: mediaQuery(context, 'height', 207),
        left: 0,
        child: backButtonLemon(context));
  }

  Widget _save() {
    return new GestureDetector(
      onTap: () {
        _capturePng().then((value) {
          FadeSaveReceipt(context,
              AppLocalizations.of(context)!.translate('buylike_notify'), this);
        });

        /// Test of transaction status successfully
//        setState(() {
//          statusTranfer = !statusTranfer;
//        });
      },
      child: Container(
          margin: EdgeInsets.only(
            right: mediaQuery(context, 'width', 53),
          ),
          alignment: Alignment.center,
          height: mediaQuery(context, 'height', 160),
          width: mediaQuery(context, 'height', 160),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: LikeWalletAppTheme.white,
            boxShadow: [
              BoxShadow(
                color: LikeWalletAppTheme.gray.withOpacity(0.1),
                offset: Offset(1, 3),
                spreadRadius: 0,
                blurRadius: 3,
              ),
            ],
          ),
          child: Image.asset(
            LikeWalletImage.icon_save_alt,
            height: mediaQuery(context, 'height', 63.85),
          )),
    );
  }

  Widget _buttonSuccess() {
    return new GestureDetector(
      onTap: () {},
      child: Container(
        margin: EdgeInsets.only(
          left: mediaQuery(context, 'width', 53),
        ),
        alignment: Alignment.center,
        height: mediaQuery(context, 'height', 160),
        width: mediaQuery(context, 'height', 160),
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: LikeWalletAppTheme.lemon1,
          boxShadow: [
            BoxShadow(
              color: LikeWalletAppTheme.gray.withOpacity(0.3),
              offset: Offset(0, 1),
              spreadRadius: 0,
              blurRadius: 3,
            ),
          ],
        ),
        child: Image.asset(LikeWalletImage.icon_success,
            height: mediaQuery(context, "height", 51.17)),
      ),
    );
  }

  Widget _buttonOK() {
    return new GestureDetector(
      onTap: () {
        Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => CompleteTX(
                    title: AppLocalizations.of(context)!
                        .translate('buylike_completed1'),
                    detail: AppLocalizations.of(context)!
                        .translate('buylike_completed2'),
                    back: '/home')));
      },
      child: Container(
        margin: EdgeInsets.only(
          right: mediaQuery(context, 'width', 53),
        ),
        alignment: Alignment.center,
        height: mediaQuery(context, 'height', 160),
        width: mediaQuery(context, 'height', 160),
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: LikeWalletAppTheme.white,
          boxShadow: [
            BoxShadow(
              color: LikeWalletAppTheme.gray.withOpacity(0.3),
              offset: Offset(0, 1),
              spreadRadius: 0,
              blurRadius: 3,
            ),
          ],
        ),
        child: Text(
          AppLocalizations.of(context)!.translate('buylike_promptpay8'),
          style: TextStyle(
            letterSpacing: 0,
            fontWeight: FontWeight.bold,
            fontSize: mediaQuery(context, 'height', 42),
            fontFamily: AppLocalizations.of(context)!.translate('font1'),
            color: LikeWalletAppTheme.gray.withOpacity(1),
          ),
        ),
      ),
    );
  }

  Future<Uint8List> _capturePng() async {
    print('inside');
    RenderRepaintBoundary boundary =
        _globalKey.currentContext!.findRenderObject() as RenderRepaintBoundary;
    ui.Image image = await boundary.toImage(pixelRatio: 10.0);
    ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    Uint8List pngBytes = byteData!.buffer.asUint8List();
    String bs64 = base64Encode(pngBytes);
    if (Platform.isAndroid) {
      bool statusPermision = await Permission.photos.isGranted;
      if (statusPermision != true) {
        await Permission.manageExternalStorage.request(); await Permission.photos.request(); 
      }
    } else {
      bool statusPermision = await Permission.photos.isGranted;
      if (statusPermision != true) {
        await Permission.manageExternalStorage.request(); await Permission.photos.request(); 
      }
    }

    final result = await ImageGallerySaver.saveImage(
        Uint8List.fromList(pngBytes.buffer.asInt8List()));
    print(result);
    print('png done');
    FadeSaveReceipt(context,
        AppLocalizations.of(context)!.translate('fade_save_receipt'), this);
//    _snackSample(context);
    setState(() {
      imageInMemory = pngBytes;
    });

    return pngBytes;
  }

  @override
  Widget build(BuildContext context) {
    screenUtil(context);
    return ModalProgressHUD(
        opacity: 0.1,
        progressIndicator: CustomLoading(),
        inAsyncCall: _saving,
        child: Scaffold(
            bottomNavigationBar: NavigationBarWhite(), body: buildForPhone()));
  }
}
