import 'package:flutter/material.dart';
import 'package:likewallet/bank/favorites/BankFavorites.dart';
import 'package:likewallet/bank/favorites/TransferFavorites.dart';

class Favorites extends StatefulWidget {
  _Favorites createState() => new _Favorites();
}

class _Favorites extends State<Favorites> {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      home: DefaultTabController(
        length: 2,
        child: Scaffold(
          appBar: AppBar(
            backgroundColor: Color(0xff000000),
            bottom: TabBar(
              indicatorColor: Colors.cyan,
              tabs: [
                Text('โอนไลท์'),
                Text('บัญชีธนาคาร'),
              ],
            ),
            leading: Icon(Icons.arrow_back_ios),
            title: Text(
              'รายการโปรด',
              style: TextStyle(),
            ),
          ),
          body: TabBarView(
            children: [
              TransferFavorites(),
              BankFavorites(),
            ],
          ),
        ),
      ),
    );
  }
}
