import 'package:flutter/material.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:convert';
import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class TransferFavorites extends StatefulWidget {
  @override
  _TransferFavorites createState() => new _TransferFavorites();
}

class _TransferFavorites extends State<TransferFavorites> {
  late BaseAuth auth;
  final fireStore = FirebaseFirestore.instance;
  late String uid;
  late String title;

  List<String> favorite = <String>[];
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    auth = new Auth();
    _loadList();
  }

  @override
  Widget build(BuildContext context) {
    return new StreamBuilder(
      stream: fireStore
          .collection('favorites')
          .doc(uid)
          .collection('transfer')
          .snapshots(),
      builder: (BuildContext context, AsyncSnapshot<QuerySnapshot<Map<String, dynamic>>> snapshot) {
        if (!snapshot.hasData) return new Text('Loading...');
        return new ListView(
          children: snapshot.data!.docs.map((document) {
            return new ListTile(
              title: new Text(document.data()!['addressFavorite']),
              subtitle: new Text(document.data()!['photoFavorite']),
            );
          }).toList(),
        );
      },
    );
  }

  void _loadList() async {
    auth.getCurrentUser().then((decodeToken) {
      // print(decodeToken);
      setState(() {
        uid = decodeToken!.uid;
      });

      print(uid);
//      fireStore
//          .collection('favorites')
//          .document(decodeToken.uid)
//          .collection('transfer')
//          .getDocuments()
//          .then((value) {
//        print(value['title']);
////        value.documents.forEach((doc) {
////          print(doc.data);
////          setState(() {
////
////          });
////        });
//      });
//    });
    });
  }
}
