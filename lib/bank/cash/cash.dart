import 'dart:convert';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';

import 'package:flutter_svg/flutter_svg.dart';

import 'package:image_picker/image_picker.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/animationPage.dart';
import 'package:likewallet/close_maintenance.dart';
import 'package:likewallet/device_utils.dart';
import 'package:likewallet/kyc/kyc.dart';
import 'package:likewallet/kycSumSub/kycSumSub.dart';

import 'package:likewallet/libraryman/app_local.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as riverpod;
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'dart:async';
import 'package:flutter/services.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:likewallet/libraryman/maintenance.dart';
import 'package:likewallet/libraryman/open_web_notitle.dart';
import 'package:likewallet/main.dart';
import 'package:likewallet/middleware/callFireStore.dart';
import 'package:likewallet/middleware/check_maintenance/check_maintenance.dart';
import 'package:likewallet/middleware/getLanguage.dart';
import 'package:likewallet/model/bank/exchange_fiat.dart';
import 'package:likewallet/model/pageStatus.dart';
import 'package:likewallet/model/sumSubToken.dart';
import 'package:likewallet/routes.dart';
import 'package:likewallet/screen_util.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:likewallet/screen/home.dart';
import 'dart:ui';
import 'package:flutter/rendering.dart';
import 'package:likewallet/libraryman//ethcontract.dart';
import 'package:likewallet/middleware/get_mnemonic.dart';
import 'package:likewallet/middleware/check_auth.dart';
import 'package:intl/intl.dart';
import 'package:likewallet/libraryman/address_service.dart';
import 'package:likewallet/libraryman/configuration_service.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:http/http.dart' as http;
import 'package:likewallet/app_config.dart';
import 'package:likewallet/bank/completeTX.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
//use for check active API before use withdraw
import 'package:likewallet/libraryman/checkAPI.dart';
import 'package:likewallet/libraryman/setFormat.dart';
import 'package:likewallet/bank/choiceTopay.dart';
import 'package:likewallet/libraryman/serviceHTTP.dart';
import 'package:intl/intl.dart' as formatIntl;
import 'package:likewallet/quickpay/favorites/list.dart';
import 'package:likewallet/libraryman/keyboard_done_widget.dart';
import 'dart:io' show File, Platform;
import 'package:likewallet/middleware/callHttp.dart';

/* ----------------| RECEIVE |--------------*/
class CASH extends StatefulWidget {
  CASH(
      {this.selectPage,
      required this.source,
      this.shopID,
      this.scanActive,
      required this.address,
      required this.contract,
      required this.abi,
      required this.callFunction});

  final String source;
  final selectPage;
  final shopID;
  final scanActive;
  final String contract;
  final String address;
  final String abi;
  final String callFunction;
  _CASH createState() => new _CASH(
      selectPage: selectPage,
      source: source,
      shopID: shopID,
      scanActive: scanActive,
      address: address,
      contract: contract,
      abi: abi,
      callFunction: callFunction);
}

class _CASH extends State<CASH> {
  _CASH(
      {this.selectPage,
      required this.source,
      this.shopID,
      this.scanActive,
      required this.address,
      required this.contract,
      required this.abi,
      required this.callFunction});
  final selectPage;
  final String source;
  final shopID;
  final scanActive;
  final String address;
  final String contract;
  final String abi;
  final String callFunction;
  GlobalKey<ScaffoldState> _scaffoldKey = new GlobalKey();
  GlobalKey globalKey = new GlobalKey();
  GlobalKey builderKey = new GlobalKey();
  final TextEditingController controller = new TextEditingController();
  TextEditingController amountSend = TextEditingController();
  TextEditingController textCash = TextEditingController();
  TextEditingController textAccountNumber = TextEditingController();
  bool showfavorite = false;
  final fireStore = FirebaseFirestore.instance;
  double price = 0;
  bool _loading = false;
  final f = new formatIntl.NumberFormat("###,###");
  final formatCurrency = new NumberFormat.decimalPattern();
  late FocusScopeNode currentFocus;
  StreamController<String> streamBalance = StreamController<String>();
  StreamController<String> streamBalanceLocked = StreamController<String>();
  StreamController<String> streamAvailable = StreamController<String>();
  StreamController<double> streamBalanceLock = StreamController<double>();
  late CarouselSlider carouselSlider;
  final CarouselController carouse_controller = CarouselController();
  String Symbol = 'THB';
  String _total_pay = '300.00';
  double rate = 100.00;
  double amount = 0.00;
  String accountNumber = '';
  double totalSell = 0.00;
  bool showsymbol = false;
  List symbol = [
    'USD',
    'LAK',
    'THB',
  ];
  String currency = 'THB';
  double fee = 0;
  late String _inputErrorText;
  int keyword = 1;
  String barcode = "";
  String symbol_1 = 'LIKE';
  String symbol_2 = 'LIKE';
  String balance = 'Loading..';
  double balanceLIKE = 0.00;
  String locked_balance = 'Loading..';
  double rateCurrency = 0.00;
  double balanceLIKELock = 0.00;
  String available = 'Loading..';
  String bank = '';
  late String pketh;
  late BaseETH eth;
  late GetMnemonic seed;
  late String mnemonic;
  late CheckAuth Logon;
  late IAddressService addressService;
  late IConfigurationService configETH;
  late BaseAuth FirebaseAuth;
  late OnCallFireStore onFireStore;
  bool login = false;
  late CheckAPI checkAPI;
  late SetFormat setFormat;
  TextEditingController addressText = TextEditingController();
  final TextEditingController nameFavorite = TextEditingController();
  final TextEditingController addressFavorite = TextEditingController();
  late String photoFavorite;
  final formKey = GlobalKey<FormState>();
  bool delete = false;

  int tabMenu = 0;
  var crossFadeView = CrossFadeState.showFirst;
  bool _saving = false;
  late String toAddress;
  late AbstractServiceHTTP APIHttp;
  var _opacity = 1.0;
  late OverlayEntry? overlayEntry;
  String _dataString = "";
  //ร้านค้า
  List<list> _list = [];
  List group = [];
  List<list> search = [];
  late String uid;
  int _current = 0;
  List imgList = [
    LikeWalletImage.icon_us,
    LikeWalletImage.icon_laos,
    LikeWalletImage.icon_th
  ];
  List symbolList = [
    'USD',
    'LAK',
    'THB',
  ];
  List<dynamic> RateList = [];
  //kyc session
  bool kycStatus = false;
  bool kycStatusSumSub = false;
  FocusNode amountFocusNode = new FocusNode();
  FocusNode amountBuyFocusNode = new FocusNode();
  FocusNode amountCashFocusNode = new FocusNode();
  final formatBank = new NumberFormat("###-###-####");
  late CheckAbout checkAbout;
  late OnLanguage language;
  late CallHttp onHttp;
  KeyboardVisibilityController keyboardVisibilityController = KeyboardVisibilityController();

  _changePrice(String value) {
    setState(() => price = double.parse(value));
  }

  //done button zone
  showOverlay(BuildContext context) {
    if (overlayEntry != null) return;
    OverlayState? overlayState = Overlay.of(context);
    overlayEntry = OverlayEntry(builder: (context) {
      return Positioned(
          bottom: MediaQuery.of(context).viewInsets.bottom,
          right: 0.0,
          left: 0.0,
          child: InputDoneView());
    });

    overlayState!.insert(overlayEntry!);
  }

  removeOverlay() {
    if (overlayEntry != null) {
      overlayEntry!.remove();
      overlayEntry = null;
    }
  }

  Future<bool> getShop() async {
    //เรียก API ร้านค้า
    var data = await APIHttp.getQuickpayShop();
    setState(() {
      _list = data[0];
      search = data[0];
      group = data[1];
    });
    return true;
  }

  void pasteAddress() async {
    ClipboardData? data = await Clipboard.getData('text/plain');
    addressText.text = data!.text!;
    toAddress = data.text!;
  }

  Future generateETH(seed, pref) async {
    setState(() {
      _dataString = configETH.getAddress();
    });
    eth.getBalance(address: _dataString).then((avaiBalance) {
      available = 'LIKE ' + f.format(avaiBalance).toString();
      setState(() {
        balanceLIKE = avaiBalance.toDouble();
      });
      streamAvailable.sink.add(available);

      eth.getBalanceLock(address: _dataString).then((balanceLock) {
        print(balanceLock);
        balance = 'LIKE ' + f.format(avaiBalance + balanceLock);
        balanceLIKELock = balanceLock.toDouble();
        locked_balance = 'LIKE ' + f.format(balanceLock).toString();
        streamBalance.sink.add(balance);
        streamBalanceLock.sink.add(balanceLIKELock);
        streamBalanceLocked.sink.add(locked_balance);
      });
    });
  }

  _changeText(_symbol) {
    //เปลี่ยนสกุลเงิน
    print('symbol Currency : ' + _symbol);

    final exchangeFiat =
        fireStore.collection('exchangeFiat').withConverter<ExchangeFiat>(
              fromFirestore: (snapshot, _) =>
                  ExchangeFiat.fromJson(snapshot.data()!),
              toFirestore: (model, _) => model.toJson(),
            );
    exchangeFiat
        .doc(_symbol == 'none' ? "THB-THB" : "THB-" + _symbol)
        .get()
        .then((ds) {
      // print(ds.data()!.rate.toDouble());
      if (ds.data()!.to == 'LIKE') {
        setState(() {
          rateCurrency = ds.data()!.rate.toDouble();
          print('rateCurrency ds[LIKE]: ' + rateCurrency.toString());
        });
      } else {
        if (!mounted) return;
        setState(() {
          rateCurrency = ds.data()!.rate.toDouble();
          print('rateCurrency ds[' + _symbol + ']: ' + rateCurrency.toString());
        });
      }
      APIHttp.getCurrentFee(currency: _symbol == 'none' ? 'THB' : _symbol)
          .then((getFee) {
        setState(() {
          fee = getFee;
        });
        print("fee$fee");
      });
      updateRecived();
    });
    setState(() {
      print(_symbol);
      symbol_1 = _symbol;
    });
  }

  changeMenu(_number) async {
    setState(() {
      tabMenu = _number;
    });
  }

  _changeCurrency() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    currency = pref.getString('currency') ?? 'THB';
    pref.setString('currency', currency);
    _changeText(currency);
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    checkAbout = OnCheckAbout();
    language = CallLanguage();
    onFireStore = CallFireStore();
    onHttp = OnCallHttp();
    setState(() => _loading = true);
    checkFirst();
  }

  checkFirst() async {
    PageMaintenance statusPage = await checkAbout.checkTierPermission(
        tierLevel: context.read(tierLevel).state, page: 'cash');
    if (statusPage.status == 'active') {
      //เริ่มทำงาน
      print('active');

      setInitState();
    } else {
      //ปิด maintenance
      print('inactive');
      final lang = await language.getLanguage();
      final title = await checkAbout.selectLanguage(
          language: lang, detail: statusPage.title);
      final detail = await checkAbout.selectLanguage(
          language: lang, detail: statusPage.detail);
      final detailTime = await checkAbout.selectLanguage(
          language: lang, detail: statusPage.detail_time);
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
            builder: (context) => CloseMaintenance(
                  title: title,
                  detail: detail,
                  detailTime: detailTime,
                  url: statusPage.url,
                )),
      );
    }
  }

  setInitState() {
    Future.delayed(Duration.zero, () {
      _changeText('none');
      var thirdMap = {};
      var setData = {};
      fireStore.collection('exchangeFiat').get().then((ds) {
        ds.docs.forEach((element) {
          Map<String, dynamic> docs = element.data();
          if (symbolList.contains(docs["to"])) {
            setData = {docs["to"]: docs["rate"]};
          }
          //รวม json เข้าด้วยกัน
          thirdMap.addAll(setData);
        });
        //นำมาใส่ใน RateList
        RateList.add(thirdMap);
      });
      APIHttp = new ServiceHTTP();
      amountSend.addListener(changeAmount);
      textCash.addListener(updateRecived);
      textAccountNumber.addListener(updateAccountNumber);
      Logon = new CurCheckAuth();
      seed = new MnemonicRetrieve();
      eth = new EthContract();
      FirebaseAuth = Auth();
      checkAPI = APIChecker();
      setFormat = SetFormatString();

      if (Platform.isIOS) {
        amountFocusNode.addListener(() {
          bool hasFocus = amountFocusNode.hasFocus;
          if (hasFocus)
            showOverlay(context);
          else
            removeOverlay();
        });

        amountBuyFocusNode.addListener(() {
          bool hasFocus = amountBuyFocusNode.hasFocus;
          if (hasFocus)
            showOverlay(context);
          else
            removeOverlay();
        });
        amountCashFocusNode.addListener(() {
          bool hasFocus = amountCashFocusNode.hasFocus;
          if (hasFocus)
            showOverlay(context);
          else
            removeOverlay();
        });
        keyboardVisibilityController.onChange.listen((bool visible) {
          removeOverlay();
        });
      }

      if (scanActive == 'active') {
        setState(() {
          addressText.text = address;
          toAddress = address;
        });
      }
      if (source == 'favorite') {
        setPay();

        print('call favorite');
      }
      setInit();
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);
    });
  }

  @override
  void dispose() {
    // TODO: implement dispose
    streamBalance.close();
    streamBalanceLocked.close();
    streamBalanceLock.close();

    addressText.dispose();
    nameFavorite.dispose();
    addressFavorite.dispose();
    controller.dispose();
    amountSend.dispose();
    textCash.dispose();
    textAccountNumber.dispose();
    streamAvailable.close();
    amountFocusNode.dispose();
    amountBuyFocusNode.dispose();
    amountCashFocusNode.dispose();
//    currentFocus.dispose();

    super.dispose();
  }

  void setPay() async {
    getShop().then((result) {
      // print(result);
      if (result) {
        print('result getShop' + result.toString());
        setState(() {
          toAddress = _list[shopID].address.toString().trim().toString();
          addressText.text = _list[shopID].title.toString();
        });
      }
    });
  }

  Future<bool> callStatusKYCSumSub() async {
    bool sumSubStatus = false;
    User? user = await FirebaseAuth.getCurrentUser();
    StatusSumSub res = await onHttp.callStatusSumSub(
        url: env.apiSumSub + 'status',
        jsonMap: {"uid": user!.uid, "phone": user.phoneNumber});
    if (res.statusCode == 200) {
      if (res.status == 'completed') {
        if (res.result == 'GREEN') {
          sumSubStatus = true;
        }
        if (res.result == 'RED') {
          sumSubStatus = false;
        }
      } else {
        sumSubStatus = false;
      }
    } else {
      sumSubStatus = false;
      // Navigator.of(context).pop();
      // showColoredToast(AppLocalizations.of(context)!.translate('save_err'));
    }
    return sumSubStatus;
  }

  setInit() async {
    print('open banking');
    SharedPreferences pref = await SharedPreferences.getInstance();
    if (!mounted) return;
    //ส่วนนี้สถานะจะมาจากการเช็ค KYC ตัวเก่า ที่เป็น TRUE RO FALSE
    kycStatus = pref.getBool('kycActive') ?? false;
    print("KYC OLD $kycStatus");
    //ถ้า KYC เก่าอันเก่าเเล้วสามารถใช้งาน Cash out ได้
    if (kycStatus) {
      setState(() => _loading = false);
      print('สามารถใช้งานได้เลย');
    } else {
      kycStatusSumSub = await callStatusKYCSumSub();
      print("KYC SumSub $kycStatusSumSub");
      if (kycStatusSumSub) {
        setState(() => _loading = false);
        print('สามารถใช้งานได้เลย');
      } else {
        setState(() => _loading = false);
        Future.delayed(Duration(milliseconds: 0)).then((value) async {
          var user = await FirebaseAuth.getCurrentUser();
          bool kyc = await onFireStore.getPermissionPage(
              tier: context.read(tierLevel).state, page: 'kyc');
          bool kycSumSub = await onFireStore.getPermissionPage(
              tier: context.read(tierLevel).state, page: 'kycSumSub');
          //call KYC Normal
          if (kyc && !kycSumSub) {
            // Navigator.pushNamed(context, '/kyc');
            Navigator.pushReplacement(
                context,
                ScaleRoute(
                  page: KYC(
                    frontPhoto: 'none',
                    backPhoto: 'none',
                    selfiePhoto: 'none',
                    title: '',
                    id: '',
                  ),
                )).then((value) => Navigator.of(context).pop());
            //call KYC SumSub
          } else if (!kyc && kycSumSub) {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(
                  builder: (context) => KYCSumSub(
                      uid: user!.uid, phone: user.phoneNumber.toString())),
            ).then((value) => Navigator.of(context).pop());
          }
        });
      }
    }

    // currency = pref.getString('currency') ?? 'THB';
    // APIHttp.getCurrentFee(currency: currency).then((getFee) {
    //   setState(() {
    //     fee = getFee;
    //   });
    //   print(fee);
    // });

    configETH = new ConfigurationService(pref);
    addressService = new AddressService(configETH);

    login = pref.getBool('login') ?? false;
    if (login) {
      mnemonic = await configETH.getMnemonic();
      generateETH(mnemonic, pref);
    }
  }

  changeAmount() {
    print(amountSend.text);
  }

  updateRecived() async {
    double balanceCheck = textCash.text == ''
        ? 0.0
        : double.parse(textCash.text.replaceAll(",", ""));

//     if (balanceCheck >= balanceLIKE) {
// //       setState(() {
// // //        amount = double.parse((balanceLIKE / rate - fee).toStringAsFixed(2));
// //         amount = double.parse(
// //             (balanceLIKE * rateCurrency / rate - fee).toStringAsFixed(2));
// //       });
//     } else {
//       print('here');
//       setState(() {
//         print('rateCurrency ' + rateCurrency.toString());
//         amount = double.parse(
//             (balanceCheck * rateCurrency / rate - fee).toStringAsFixed(2));
//       });
//     }
    print("rate" + '${rate.toDouble() - fee}');
    amount = double.parse((balanceCheck * rateCurrency / rate.toDouble() - fee)
        .toStringAsFixed(2));
    print(amount);
  }

  updateAccountNumber() {
    setState(() {
      accountNumber = textAccountNumber.text;
    });
  }

  _onWillPop() async {
    return Navigator.pushAndRemoveUntil(
      context,
      MaterialPageRoute(builder: (context) => HomeLikewallet()),
      ModalRoute.withName('/home'),
    );
  }

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return ModalProgressHUD(
        inAsyncCall: _loading,
        opacity: 0.3,
        progressIndicator: CustomLoading(),
        child: SingleChildScrollView(
            key: globalKey,
            child: GestureDetector(
                onTap: () {
                  DeviceUtils.hideKeyboard(context);
                  currentFocus = FocusScope.of(context);
                },
                child: Stack(
                  children: <Widget>[
                    _CASH_Bath(context),
                  ],
                ))));
  }

  Widget _CASH_Bath(context) {
    return Align(
      alignment: Alignment.center,
      child: new Container(
        padding: EdgeInsets.only(
          top: mediaQuery(context, 'height', 200),
        ),
        height: mediaQuery(context, 'height', 1650),
        width: MediaQuery.of(context).size.width,
        decoration: BoxDecoration(
          color: Color(0xffF5F5F5),
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            stops: [0.0, 0.2, 0.5],
            colors: [
              // Colors are easy thanks to Flutter's Colors class.
              Colors.white,
              Colors.white,
              LikeWalletAppTheme.white1
            ],
          ),
        ),
        child: new Column(
          children: <Widget>[
            Container(
                height: mediaQuery(context, 'height', 900),
                width: mediaQuery(context, 'width', 945.66),
                child: Stack(
                  children: <Widget>[
                    Positioned(
                      top: mediaQuery(context, 'height', 30),
                      left: mediaQuery(context, 'width', 50),
                      child: _maxnumim(),
                    ),
                    Positioned(
                      top: mediaQuery(context, 'height', 96),
                      left: mediaQuery(context, 'width', 20),
                      child: _cashLike(),
                    ),
                    Positioned(
                      top: mediaQuery(context, 'height', 406),
                      left: mediaQuery(context, 'width', 20),
                      child: _amount(),
                    ),
                    Positioned(
                      top: mediaQuery(context, 'height', 291),
                      left: mediaQuery(context, 'width', 440),
                      child: _iconArrow(),
                    ),
                    Positioned(
                      bottom: mediaQuery(context, 'height', 0),
                      left: mediaQuery(context, 'width', 20),
                      child: _fee(),
                    ),
                    _showSymbol()
                  ],
                )),
            SizedBox(
              height: mediaQuery(context, 'height', 250),
            ),
            textCash.text == ""
                ? Container(
                    height: mediaQuery(context, 'height', 150),
                  )
                : _button(),
            Container(child: _sliderExchangeRate(context))
//                new Row(
//                    crossAxisAlignment: CrossAxisAlignment.start,
//                    children: <Widget>[_inputAmount(), _switch()]),
//                //เว้นเเถว

//                _fee(),
//                SizedBox(
//                  height: MediaQuery.of(context).size.height * 0.05,
//                ),
//                _button()
          ],
        ),
      ),
    );
  }

  Widget _cashLike() {
    return Container(
      padding: EdgeInsets.only(
        left: mediaQuery(context, 'width', 0),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: <Widget>[
          Container(
            padding: EdgeInsets.only(
              top: mediaQuery(context, 'height', 37),
              left: mediaQuery(context, 'width', 56),
            ),
            width: mediaQuery(context, 'width', 671),
            height: mediaQuery(context, 'height', 290),
            decoration: BoxDecoration(
              borderRadius:
                  BorderRadius.circular(mediaQuery(context, 'height', 16)),
              color: LikeWalletAppTheme.white,
              boxShadow: [
                BoxShadow(
                  color: const Color(0x1a000000),
                  offset: Offset(1, 1),
                  blurRadius: 7,
                  spreadRadius: 1,
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Container(
                  margin: EdgeInsets.only(
                      bottom: mediaQuery(context, 'height', 10)),
                  child: Text(
                    AppLocalizations.of(context)!.translate('cash_exchange'),
                    style: TextStyle(
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      fontSize: mediaQuery(context, 'height', 36),
                      color: LikeWalletAppTheme.gray1,
                      letterSpacing: 0.3,
                    ),
                    textAlign: TextAlign.left,
                  ),
                ),
                Container(
                  alignment: Alignment.centerLeft,
                  height: mediaQuery(context, 'height', 109),
                  child: TextFormField(
                    textAlignVertical: TextAlignVertical.center,
                    textAlign: TextAlign.start,
                    inputFormatters: <TextInputFormatter>[
                      FilteringTextInputFormatter.digitsOnly
                    ],
                    style: TextStyle(
                        fontSize: mediaQuery(context, 'height', 83),
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font2'),
                        color: LikeWalletAppTheme.gray1),
                    keyboardType: TextInputType.number,
                    controller: textCash,
                    focusNode: amountCashFocusNode,
                    decoration: InputDecoration(
                      focusedBorder: InputBorder.none,
                      enabledBorder: InputBorder.none,
                      hintText: '0',
                      hintStyle: TextStyle(
                          fontSize: mediaQuery(context, 'height', 83),
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font2'),
                          color: LikeWalletAppTheme.gray),
                      contentPadding: EdgeInsets.only(
                        bottom: mediaQuery(context, 'height', 20),
                      ),
                    ),
                  ),
                ),
                Container(
                  margin:
                      EdgeInsets.only(top: mediaQuery(context, 'height', 10)),
                  child: Text(
                    AppLocalizations.of(context)!.translate('cash_likepoint'),
                    style: TextStyle(
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      fontSize: mediaQuery(context, 'height', 33),
                      color: LikeWalletAppTheme.gray1,
                      letterSpacing: 0.3,
                    ),
                    textAlign: TextAlign.left,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.only(
              left: mediaQuery(context, 'width', 40),
            ),
            child: Text(
              'LIKE',
              style: TextStyle(
                fontFamily: AppLocalizations.of(context)!.translate('font2'),
                fontSize: mediaQuery(context, 'height', 71),
                color: LikeWalletAppTheme.black,
                fontWeight: FontWeight.w500,
                letterSpacing: 0.3,
              ),
              textAlign: TextAlign.left,
            ),
          )
        ],
      ),
    );
  }

  Widget _amount() {
    return Container(
      padding: EdgeInsets.only(
        left: mediaQuery(context, 'width', 0),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: <Widget>[
          Container(
            padding: EdgeInsets.only(
              top: mediaQuery(context, 'height', 37),
              left: mediaQuery(context, 'width', 56),
            ),
            width: mediaQuery(context, 'width', 671),
            height: mediaQuery(context, 'height', 290),
            decoration: BoxDecoration(
              borderRadius:
                  BorderRadius.circular(mediaQuery(context, 'height', 16)),
              color: LikeWalletAppTheme.white,
              boxShadow: [
                BoxShadow(
                  color: const Color(0x1a000000),
                  offset: Offset(0, 1),
                  blurRadius: 4,
                  spreadRadius: 2,
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Container(
                  margin: EdgeInsets.only(
                      bottom: mediaQuery(context, 'height', 10)),
                  child: Text(
                    AppLocalizations.of(context)!.translate('cash_get'),
                    style: TextStyle(
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      fontSize: mediaQuery(context, 'height', 36),
                      color: LikeWalletAppTheme.gray1,
                      letterSpacing: 0.3,
                    ),
                    textAlign: TextAlign.left,
                  ),
                ),
                Text(
                  amount.toString(),
                  style: TextStyle(
                      fontSize: mediaQuery(context, 'height', 83),
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font2'),
                      color: LikeWalletAppTheme.gray1),
                  textAlign: TextAlign.left,
                ),
                Container(
                  margin:
                      EdgeInsets.only(top: mediaQuery(context, 'height', 10)),
                  child: Text(
                    Symbol == 'THB'
                        ? AppLocalizations.of(context)!.translate('symbol_th')
                        : Symbol == 'USD'
                            ? AppLocalizations.of(context)!
                                .translate('symbol_us')
                            : Symbol == 'LAK'
                                ? AppLocalizations.of(context)!
                                    .translate('symbol_lak')
                                : AppLocalizations.of(context)!
                                    .translate('symbol_th'),
                    style: TextStyle(
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      fontSize: mediaQuery(context, 'height', 33),
                      color: LikeWalletAppTheme.gray1,
                      letterSpacing: 0.3,
                    ),
                    textAlign: TextAlign.left,
                  ),
                ),
              ],
            ),
          ),
          Container(
              padding: EdgeInsets.only(
                left: mediaQuery(context, 'width', 40),
              ),
              child: _switchSymbol())
        ],
      ),
    );
  }

  Widget _iconArrow() {
    return Container(
        padding: EdgeInsets.only(
          left: mediaQuery(context, 'width', 0),
        ),
        child: Image.asset(
          LikeWalletImage.arrow_blue_dropdown,
          height: mediaQuery(context, 'height', 228),
        ));
  }

  /// เเสดงสกุลทั้งหมดที่สามารถเเปลงได้
  Widget _showSymbol() {
    return AnimatedPositioned(
        top: showsymbol
            ? mediaQuery(context, 'height', 600)
            : mediaQuery(context, 'height', 600),
        right: mediaQuery(context, 'width', 40),
        height: showsymbol
            ? mediaQuery(context, 'height', 800)
            : mediaQuery(context, 'height', 0),
        duration: Duration(milliseconds: 200),
        child: SingleChildScrollView(
            child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: <Widget>[
            for (var i = 0; i < symbol.length; i++)
              if (symbol[i] != Symbol)
                GestureDetector(
                    onTap: () {
                      print(symbol[i]);
                      setState(() {
                        print(symbol[i]);
                        Symbol = symbol[i];
                        _changeText(symbol[i]);

                        showsymbol = false;
                        print(amountSend);
                      });
                    },
                    child: Container(
                        width: mediaQuery(context, 'width', 170),
                        padding: EdgeInsets.only(
                          top: mediaQuery(context, 'width', 35),
                          bottom: mediaQuery(context, 'width', 35),
                        ),
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          border: Border(
                            top: BorderSide(
                              //                    <--- top side
                              color: LikeWalletAppTheme.gray.withOpacity(0.5),
                              width: mediaQuery(context, 'width', 3),
                            ),
                          ),
                        ),
                        child: Text(
                          symbol[i],
                          style: TextStyle(
                              fontSize: mediaQuery(context, 'height', 45),
                              color: LikeWalletAppTheme.black,
                              fontWeight: FontWeight.normal,
                              fontFamily: AppLocalizations.of(context)!
                                  .translate('font1'),
                              shadows: [
                                Shadow(
                                  blurRadius: 0,
                                  color:
                                      LikeWalletAppTheme.black.withOpacity(0.5),
                                  offset: Offset(0.0, 0.0),
                                ),
                              ]),
                        )))
          ],
        )));
  }

  ///เเสดงสกุลที่เลือก ตั้งค่าทเริ่มที่ THB
  Widget _switchSymbol() {
    return GestureDetector(
        onTap: () {
          setState(() {
            showsymbol = !showsymbol;
            print(showsymbol);
          });
        },
        child: Container(
          margin: EdgeInsets.only(left: mediaQuery(context, 'width', 0)),
          height: mediaQuery(context, 'height', 161),
          width: mediaQuery(context, 'width', 270),
          child: Row(
            children: <Widget>[
              Text(
                Symbol,
                style: TextStyle(
                    fontSize: mediaQuery(context, 'height', 71),
                    color: LikeWalletAppTheme.black,
                    fontWeight: FontWeight.w700,
                    fontFamily:
                        AppLocalizations.of(context)!.translate('font1'),
                    shadows: [
                      Shadow(
                        blurRadius: 7,
                        color: LikeWalletAppTheme.gray.withOpacity(0.2),
                        offset: Offset(0.0, 0.0),
                      ),
                    ]),
              ),
              SizedBox(
                width: mediaQuery(context, 'width', 20),
              ),
              SvgPicture.asset(
                LikeWalletImage.icon_dropdown,
                fit: BoxFit.contain,
                height: mediaQuery(context, 'height', 23.47),
                width: mediaQuery(context, 'width', 23.64),
              ),
            ],
          ),
        ));
  }

  Widget _inputAmount() {
    return Column(children: <Widget>[
      new Row(
        children: <Widget>[
          new Container(
            padding: EdgeInsets.only(left: mediaQuery(context, 'width', 75)),
            child: new Column(
              children: <Widget>[
                new Container(
                  decoration: BoxDecoration(
                    border:
                        Border.all(width: 0.5, color: LikeWalletAppTheme.gray),
                    borderRadius: new BorderRadius.circular(10.0),
                    color: Colors.white,
                    boxShadow: [
                      BoxShadow(
                        spreadRadius: 0,
                        blurRadius: 2,
                        color: LikeWalletAppTheme.gray.withOpacity(0.5),
                        offset: Offset(
                          0.0,
                          1.0,
                        ),
                      ),
                    ],
                  ),
                  alignment: Alignment.centerLeft,
                  height: mediaQuery(context, 'height', 160.68),
                  width: mediaQuery(context, 'width', 732.71),
                  child: TextFormField(
                    textAlign: TextAlign.left,
                    style: TextStyle(
                        fontSize: mediaQuery(context, 'height', 83),
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font2'),
                        color: LikeWalletAppTheme.black),
                    keyboardType: TextInputType.number,
                    controller: textCash,
                    focusNode: amountCashFocusNode,
                    decoration: InputDecoration(
                      focusedBorder: InputBorder.none,
                      enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                              width: 0.1,
                              color: LikeWalletAppTheme.gray.withOpacity(0.0))),
                      border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10.0)),
                      hintText: '0',
                      hintStyle: TextStyle(
                          fontSize: mediaQuery(context, 'height', 83),
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font2'),
                          color: LikeWalletAppTheme.gray),
                      contentPadding:
                          EdgeInsets.fromLTRB(20.0, 10.0, 20.0, 10.0),
                    ),
                  ),
                ),
                new Container(
                    height: mediaQuery(context, 'height', 100),
                    width: mediaQuery(context, 'width', 732.71),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        Text(
                          AppLocalizations.of(context)!
                              .translate('bankingcash_about'),
                          style: TextStyle(
                            color: LikeWalletAppTheme.gray2.withOpacity(0.5),
                            fontFamily: AppLocalizations.of(context)!
                                .translate('font1'),
                            fontWeight: FontWeight.normal,
                            fontSize: mediaQuery(context, 'height', 36),
                          ),
                        ),
                      ],
                    )),
              ],
            ),
          ),
        ],
      ),
    ]);
  }

  Widget _switch() {
    return
//      Column(children: <Widget>[
//      new AnimatedSwitcher(
//        duration: const Duration(milliseconds: 200),
//        transitionBuilder: (Widget child, Animation<double> animation) {
//          return ScaleTransition(child: child, scale: animation);
//        },
//        child:
        Container(
      alignment: Alignment.center,
      height: mediaQuery(context, 'height', 160.68),
      width: mediaQuery(context, 'width', 200),
      child: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          _changeCurrency();
        },
        child: AnimatedOpacity(
          duration: Duration(seconds: 1),
          opacity: _opacity,
          child: new Text(
            symbol_2,
            style: TextStyle(
              color: LikeWalletAppTheme.black.withOpacity(1),
              fontFamily: 'Proxima Nova',
              fontWeight: FontWeight.normal,
              fontSize: mediaQuery(context, 'height', 70),
            ),
          ),
        ),
      ),
    );
//      ),
//          new Container(
//            alignment: Alignment.center,
//            height: mediaQuery(context, 'height', 90),
//            width: mediaQuery(context, 'width', 200),
//            child: new Image.asset(
//              LikeWalletImage.icon_down_arrow,
//              height: mediaQuery(context, 'height', 70),
//            ),
//          ),
//          new Container(
//            alignment: Alignment.center,
//            height: mediaQuery(context, 'height', 100),
//            width: mediaQuery(context, 'width', 200),
//            child: new Text(
//              symbol_1,
//              style: TextStyle(
//                color: LikeWalletAppTheme.gray2.withOpacity(0.5),
//                fontFamily: 'Proxima Nova',
//                fontWeight: FontWeight.normal,
//                fontSize: mediaQuery(context, 'height', 70),
//              ),
//            ),
//          ),
//    ]);
  }

//  Widget _amount() {
//    return Row(
//      mainAxisAlignment: MainAxisAlignment.start,
//      crossAxisAlignment: CrossAxisAlignment.start,
//      children: <Widget>[
//        new Container(
//          padding: EdgeInsets.only(
//            left: mediaQuery(context, 'width', 75),
//          ),
//          alignment: Alignment.centerLeft,
//          height: mediaQuery(context, 'height', 105),
//          width: mediaQuery(context, 'width', 540),
//          child: Text(
//            AppLocalizations.of(context)!.translate('bankingcash_get'),
//            style: TextStyle(
//                color: Color(0xff6C6B6D).withOpacity(0.5),
//                fontFamily: AppLocalizations.of(context)!.translate('font1'),
//                fontWeight: FontWeight.normal,
//                fontSize: MediaQuery.of(context).size.height * 0.***********),
//          ),
//        ),
//        new Container(
//          alignment: Alignment.centerRight,
//          height: mediaQuery(context, 'height', 105),
//          width: mediaQuery(context, 'width', 270),
//          child: Text(
//            amount.toString(),
//            style: TextStyle(
//                color: LikeWalletAppTheme.black.withOpacity(1),
//                fontFamily: 'Roboto',
//                fontWeight: FontWeight.normal,
//                fontSize: mediaQuery(context, 'height', 84)),
//          ),
//        ),
//        new Container(
//          alignment: Alignment.bottomLeft,
//          height: mediaQuery(context, 'height', 93),
//          width: mediaQuery(context, 'width', 270),
//          child: Text(
//            AppLocalizations.of(context)!.translate('bankingcash_symbol'),
//            style: TextStyle(
//                color: LikeWalletAppTheme.black.withOpacity(1),
//                fontFamily: AppLocalizations.of(context)!.translate('font1'),
//                fontWeight: FontWeight.normal,
//                fontSize: mediaQuery(context, 'height', 36)),
//          ),
//        ),
//      ],
//    );
//  }

  Widget _fee() {
    return Container(
      height: mediaQuery(context, 'height', 50),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          new Container(
            margin: EdgeInsets.only(
              right: mediaQuery(context, 'width', 80),
            ),
            alignment: Alignment.centerLeft,
            child: Text(
              AppLocalizations.of(context)!.translate('bankingcash_fee'),
              style: TextStyle(
                  color: LikeWalletAppTheme.gray,
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  fontWeight: FontWeight.normal,
                  fontSize: mediaQuery(context, 'height', 38)),
            ),
          ),
          Container(
            margin: EdgeInsets.only(
              right: mediaQuery(context, 'width', 20),
            ),
            child: Text(
              fee.toString(),
              style: TextStyle(
                  color: LikeWalletAppTheme.black,
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  fontWeight: FontWeight.normal,
                  fontSize: mediaQuery(context, 'height', 38)),
            ),
          ),
          Container(
            margin: EdgeInsets.only(
              right: mediaQuery(context, 'width', 20),
            ),
            child: Text(
                Symbol == 'THB'
                    ? AppLocalizations.of(context)!.translate('symbol_th')
                    : Symbol == 'USD'
                        ? AppLocalizations.of(context)!.translate('symbol_us')
                        : Symbol == 'LAK'
                            ? AppLocalizations.of(context)!
                                .translate('symbol_lak')
                            : AppLocalizations.of(context)!
                                .translate('symbol_th'),
                style: TextStyle(
                  color: LikeWalletAppTheme.gray,
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  fontWeight: FontWeight.normal,
                  fontSize: mediaQuery(context, 'height', 38),
                )),
          ),
        ],
      ),
    );
  }

  Widget _maxnumim() {
    return Container(
      height: mediaQuery(context, 'height', 50),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          new Container(
            margin: EdgeInsets.only(
              right: mediaQuery(context, 'width', 30),
            ),
            alignment: Alignment.centerLeft,
            child: Text(
              AppLocalizations.of(context)!.translate('bankingcash_minimum'),
              style: TextStyle(
                  color: LikeWalletAppTheme.bule1,
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  fontWeight: FontWeight.normal,
                  fontSize: mediaQuery(context, 'height', 38)),
            ),
          ),
          Container(
            margin: EdgeInsets.only(
              right: mediaQuery(context, 'width', 20),
            ),
            child: Text(
              "1,000",
              style: TextStyle(
                  color: LikeWalletAppTheme.bule1,
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  fontWeight: FontWeight.normal,
                  fontSize: mediaQuery(context, 'height', 38)),
            ),
          ),
          Container(
            margin: EdgeInsets.only(
              right: mediaQuery(context, 'width', 20),
            ),
            child: Text(AppLocalizations.of(context)!.translate('lock_symbol'),
                style: TextStyle(
                  color: LikeWalletAppTheme.bule1,
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  fontWeight: FontWeight.normal,
                  fontSize: mediaQuery(context, 'height', 38),
                )),
          ),
        ],
      ),
    );
  }

//
  Widget _sliderExchangeRate(context) {
    return Container(
        height: mediaQuery(context, 'height', 150),
        width: MediaQuery.of(context).size.width,
        child: CarouselSlider(
          options: CarouselOptions(
            scrollPhysics: NeverScrollableScrollPhysics(),
            height: mediaQuery(context, 'height', 150),
            initialPage: 0,
            enlargeCenterPage: true,
            autoPlay: true,
            reverse: false,
            enableInfiniteScroll: true,
            autoPlayCurve: Curves.fastOutSlowIn,
            autoPlayInterval: Duration(seconds: 2),
            autoPlayAnimationDuration: Duration(milliseconds: 800),
            scrollDirection: Axis.horizontal,
            onPageChanged: (index, reason) {
              if (!mounted) return;
              setState(() {
                _current = index;
              });
            },
          ),
          items: imgList.asMap().entries.map((imgUrl) {
            return Builder(
              builder: (BuildContext context) {
                return Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    Container(
                      height: mediaQuery(context, 'height', 75),
                      width: mediaQuery(context, 'height', 75),
                      margin: EdgeInsets.symmetric(horizontal: 10.0),
                      child: Image.asset(
                        imgUrl.value,
                        fit: BoxFit.fill,
                      ),
                    ),
                    Text(
                      RateList.isEmpty
                          ? '0'
                          : double.parse((1 *
                                      RateList[0][symbolList[imgUrl.key]] /
                                      rate)
                                  .toStringAsFixed(5))
                              .toString(),
                      style: TextStyle(
                          color: LikeWalletAppTheme.bule1,
                          fontFamily: 'Roboto',
                          fontWeight: FontWeight.normal,
                          fontSize: mediaQuery(context, 'height', 36),
                          letterSpacing: 1),
                    ),
                  ],
                );
              },
            );
          }).toList(),
        ));
//    text /100 * ds["rate"];
  }

  Widget _button() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: <Widget>[
        GestureDetector(
          onTap: () {
            setState(() {
              textCash.clear();
            });
          },
          child: Container(
              width: mediaQuery(context, 'width', 930) / 2,
              height: mediaQuery(context, 'height', 150),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: <Widget>[
                  Image.asset(
                    LikeWalletImage.icon_button_cancel_white,
                  ),
                  Text(
                    AppLocalizations.of(context)!
                        .translate('add_account_cancel'),
                    style: TextStyle(
                        fontFamily: 'Noto Sans',
                        fontSize: mediaQuery(context, 'height', 36),
                        color: LikeWalletAppTheme.gray.withOpacity(0.8)),
                    textAlign: TextAlign.right,
                  )
                ],
              )),
        ),
        GestureDetector(
          onTap: () {
//            Navigator.pushNamed(context, '/choiceTopay',
//                arguments: {'amount': amount, 'fee': fee, 'rate': rate});
//             print(textCash.text);
//             print(double.parse((balanceLIKE).toStringAsFixed(2)));
//             print(amount);
            if (double.parse(textCash.text) > balanceLIKE) {
              showColoredToast(AppLocalizations.of(context)!
                  .translate('cash_unlimited_amount'));
            } else {
              if (amount < 1 && Symbol == 'USD')
                showColoredToast(AppLocalizations.of(context)!
                    .translate('minimum_withdrawUSD'));
              else if (amount < 10 && Symbol == 'THB')
                showColoredToast(AppLocalizations.of(context)!
                    .translate('minimum_withdrawTHB'));
              else if (amount < 10 && Symbol == 'LAK')
                showColoredToast(AppLocalizations.of(context)!
                    .translate('minimum_withdrawLAK'));
              else {
                double balanceCheck = textCash.text == ''
                    ? 0.0
                    : double.parse(textCash.text.replaceAll(",", ""));

                if (balanceCheck >= balanceLIKE) {
                  setState(() {
                    totalSell = balanceLIKE;
                  });
                } else {
                  setState(() {
                    totalSell = balanceCheck;
                  });
                }
                Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => ChoiceTopay(
                          amount: amount,
                          fee: fee,
                          rate: rate,
                          symbol: Symbol,
                          totalSell: totalSell)),
                );
              }
            }
          },
          child: Container(
              width: mediaQuery(context, 'width', 930) / 2,
              height: mediaQuery(context, 'height', 150),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: <Widget>[
                  Text(
                    AppLocalizations.of(context)!
                        .translate('bankingbuy_button'),
                    style: TextStyle(
                        fontFamily: 'Noto Sans',
                        fontSize: mediaQuery(context, 'height', 36),
                        color: LikeWalletAppTheme.gray.withOpacity(0.8)),
                    textAlign: TextAlign.right,
                  ),
                  Image.asset(
                    LikeWalletImage.icon_button_next_black,
                  ),
                ],
              )),
        ),
      ],
    );
  }
}
