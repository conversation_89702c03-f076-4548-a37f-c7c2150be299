import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:likewallet/bank/bill_transection.dart';
import 'package:likewallet/screen/NavigationBar.dart' as nav;
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:likewallet/libraryman/app_local.dart';

import 'package:likewallet/libraryman/setFormat.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:likewallet/libraryman//ethcontract.dart';
import 'package:likewallet/middleware/get_mnemonic.dart';
import 'package:likewallet/middleware/check_auth.dart';
import 'package:likewallet/libraryman/address_service.dart';
import 'package:likewallet/libraryman/configuration_service.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:http/http.dart' as http;
import 'package:likewallet/app_config.dart';
import 'package:likewallet/bank/completeTX.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
//use for check active API before use withdraw
import 'package:likewallet/libraryman/checkAPI.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:likewallet/libraryman/setFormat.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:likewallet/bank/choiceTopay.dart';
import 'package:likewallet/screen/home.dart';
import 'package:likewallet/libraryman/serviceHTTP.dart';

class ConfirmCash extends StatefulWidget {
  ConfirmCash(
      {required this.rate,
      required this.amount,
      required this.fee,
      required this.accountNumber,
      required this.typePay});
  final double rate;
  final double amount;
  final double fee;
  final String accountNumber;
  final String typePay;

  _ConfirmCash createState() => new _ConfirmCash(
      rate: rate,
      amount: amount,
      fee: fee,
      accountNumber: accountNumber,
      typePay: typePay);
}

class _ConfirmCash extends State<ConfirmCash> {
  _ConfirmCash(
      {required this.rate,
      required this.amount,
      required this.fee,
      required this.accountNumber,
      required this.typePay});
  final double rate;
  final double amount;
  final double fee;
  final String accountNumber;
  final String typePay;
  TextEditingController note = TextEditingController();
  late String pketh;
  late BaseETH eth;
  late GetMnemonic seed;
  late String mnemonic;
  late CheckAuth Logon;
  late IAddressService addressService;
  late IConfigurationService configETH;
  late BaseAuth FirebaseAuth;
  late CheckAPI checkAPI;
  late SetFormat setFormat;

  late AbstractServiceHTTP APIHttp;
  bool _saving = false;
  void showColoredToast(msg) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_LONG,
        backgroundColor: Colors.red,
        textColor: Colors.white);
  }

  void showShortToast(msg) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_SHORT,
        backgroundColor: Colors.cyan,
        textColor: Colors.white);
  }

  Future<String> signTransaction() async {
    String tx;
    String value = await seed.getMnemonic();
    String addressSell = await APIHttp.getSellAddress(bank: typePay);
    mnemonic = value.split(":")[0];
    pketh = value.split(":")[1];
    tx = await eth.sendTransaction(
        pk: pketh,
        to: addressSell,
        value: ((amount * rate) + (fee * rate)).toString());

    return tx;
  }

  requestPayout() {
    setState(() {
      _saving = true;
      print(_saving);
    });
    checkAPI.checkActive().then((statusAPI) {
      if (statusAPI == true) {
        signTransaction().then((tx) {
          print(tx);
          FirebaseFirestore.instance
              .collection('logs')
              .doc('withdrawAPP')
              .collection('newlikewallet')
              .doc()
              .set({'tx': tx, 'owner': accountNumber.toString()});
          var url = Uri.https(env.apiUrl, '/sellLikepoint');
          FirebaseAuth.getTokenFirebase().then((_token) async {
            var response = await http.post(url, body: {
              '_token': _token,
              'paymentMethod': 'bank',
              'bankName': typePay.toString(),
              'accountNumber': accountNumber.toString(),
              'tx': tx.toString(),
              'amount': ((amount * rate) + (fee * rate)).toString(),
              'baht': amount.toString(),
              'fee': fee.toString(),
              'note': note.text != null ? note.text.toString() : 'no note'
            });
            print(response);
            var body = json.decode(response.body);
            print(body["statusCode"]);
            int statusCode = body["statusCode"];
            if (statusCode == 200) {
              setState(() {
                _saving = false;
                print(_saving);
              });

              Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => CompleteTX(
                      title: AppLocalizations.of(context)!
                          .translate('promptpay_withdraw'),
                      detail: AppLocalizations.of(context)!
                          .translate('promptpay_details'),
                      buttonText:
                          AppLocalizations.of(context)!.translate('backtohome'),
//                      back: HomeLikewallet(),
                    ),
                  ));
//                  (Route<dynamic> route) => false);
            } else {
//show error and contact support
              print('error');
            }
          });
        });
      } else {
        setState(() {
          _saving = false;
        });
        showColoredToast('Service not available in this time.');
      }
    });
  }

  @override
  void initState() {
// TODO: implement initState
    super.initState();
    APIHttp = ServiceHTTP();
    setFormat = SetFormatString();
    Logon = new CurCheckAuth();
    seed = new MnemonicRetrieve();
    eth = new EthContract();
    FirebaseAuth = Auth();
    checkAPI = APIChecker();
    setFormat = SetFormatString();
//    setState(() {
//      _saving = true;
//    });
//    setInit();
  }

  int tabMenu = 0;
  changeMenu(_number) async {
    setState(() {
      tabMenu = _number;
    });
  }

  buildForPhone(Orientation orientation) {
    return Scaffold(
      bottomNavigationBar: nav.NavigationBar(),
      resizeToAvoidBottomInset: false,
      // resizeToAvoidBottomPadding: false,
      backgroundColor: Color(0xFFFFFFFF),
      body: SingleChildScrollView(
        child: Stack(
//        alignment: Alignment.center,
          children: <Widget>[
            Column(
              children: <Widget>[
                Container(
                  color: Color(0xff141322),
                  height: MediaQuery.of(context).size.height * 0.10945299145,
                ),
                Container(
                  height: MediaQuery.of(context).size.height * 0.55476923076,
                  decoration: BoxDecoration(
                    color: Color(0xffFFFFFF),
                    boxShadow: [
                      BoxShadow(
                        spreadRadius: 0,
                        blurRadius: 4,
                        color: Color(0xff707071).withOpacity(0.5),
                        offset: Offset(
                          0.0,
                          2.0,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  height: MediaQuery.of(context).size.height * 0.1,
                ),
              ],
            ),
            Positioned(
              top: MediaQuery.of(context).size.height * 0.08608547008,
              child: GestureDetector(
                onTap: () => {Navigator.of(context).pop()},
                child: Container(
                  decoration: BoxDecoration(
                    color: Color(0xffB4E60D),
                    borderRadius: new BorderRadius.only(
                        bottomRight: Radius.circular(40.0),
                        topRight: Radius.circular(40.0)),
                  ),
                  height: MediaQuery.of(context).size.height * 0.04797863247,
                  width: MediaQuery.of(context).size.width * 0.18317592592,
                  child: Icon(
                    Icons.arrow_back_ios,
//                        color: Colors.blue,
                    size: MediaQuery.of(context).size.height * 0.0190042735,
                  ),
                ),
              ),
            ),
            Positioned(
              top: MediaQuery.of(context).size.height * 0.08034188034,
              child: Container(
                alignment: Alignment.center,
                width: MediaQuery.of(context).size.width,
                child: new Text(
                  AppLocalizations.of(context)!.translate('comfirmcash_title'),
                  style: TextStyle(
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      color: Color(0xffFFFFFF).withOpacity(1),
                      fontSize:
                          MediaQuery.of(context).size.height * 0.01794871794,
                      fontWeight: FontWeight.bold),
                ),
              ),
            ),
            Positioned(
              top: MediaQuery.of(context).size.height * 0.19294871794,
              left: MediaQuery.of(context).size.width * 0.***********,
              child: Container(
                child: new Text(
                  AppLocalizations.of(context)!.translate('comfirmcash_from'),
                  style: TextStyle(
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      color: Color(0xff6C6B6D).withOpacity(0.5),
                      fontSize:
                          MediaQuery.of(context).size.height * 0.01111111111,
                      fontWeight: FontWeight.bold),
                ),
              ),
            ),
            Positioned(
              top: MediaQuery.of(context).size.height * 0.18803418803,
              left: MediaQuery.of(context).size.width * 0.34166666666,
              child: Container(
                alignment: Alignment.topLeft,

//              child: Column(
//                crossAxisAlignment: CrossAxisAlignment.start,
//                children: <Widget>[
//                  Text(
//                    'สุวดี ผ่องโสภา',
//                    style: TextStyle(
//                        color: Color(0xff707071),
//                        fontFamily:
//                            AppLocalizations.of(context)!.translate('font1'),
//                        fontWeight: FontWeight.normal,
//                        fontSize:
//                            MediaQuery.of(context).size.height * 0.***********),
//                  ),
//                  Text(
//                    '32,000' +
//                        AppLocalizations.of(context)
//                            .translate('comfirmcash_symbol'),
//                    style: TextStyle(
//                        color: Color(0xff707071),
//                        fontFamily:
//                            AppLocalizations.of(context)!.translate('font1'),
//                        fontWeight: FontWeight.normal,
//                        fontSize:
//                            MediaQuery.of(context).size.height * 0.***********),
//                  ),
//                ],

                child: Text(
                  amount.toString() + ' BAHT',
                  style: TextStyle(
                      color: Color(0xff707071),
                      fontFamily: 'Proxima Nova',
                      fontWeight: FontWeight.normal,
                      fontSize:
                          MediaQuery.of(context).size.height * 0.***********),
                ),
              ),
            ),
            Positioned(
              top: MediaQuery.of(context).size.height * 0.24369230769,
              left: MediaQuery.of(context).size.width * 0.34074074074,
              child: new Container(
                child: Image.asset(
                  'assets/image/down_arrow.png',
                  height: MediaQuery.of(context).size.height * 0.04786324786,
                ),
              ),
            ),
            Positioned(
              top: MediaQuery.of(context).size.height * 0.33974358974,
              left: MediaQuery.of(context).size.width * 0.***********,
              child: Container(
                child: new Text(
                  AppLocalizations.of(context)!.translate('comfirmcash_to'),
                  style: TextStyle(
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      color: Color(0xff6C6B6D).withOpacity(0.5),
                      fontSize:
                          MediaQuery.of(context).size.height * 0.01111111111,
                      fontWeight: FontWeight.bold),
                ),
              ),
            ),
            Positioned(
              top: MediaQuery.of(context).size.height * 0.31794871794,
              left: MediaQuery.of(context).size.width * 0.34166666666,
              child: Container(
                alignment: Alignment.topLeft,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      typePay.toString(),
                      style: TextStyle(
                          color: Color(0xff707071),
                          fontFamily: 'Proxima Nova',
                          fontWeight: FontWeight.normal,
                          fontSize: MediaQuery.of(context).size.height *
                              0.***********),
                    ),
                    Text(
                      typePay == 'promptpay'
                          ? setFormat.formatMobile(accountNumber)
                          : typePay == 'truemoney'
                              ? setFormat.formatMobile(accountNumber)
                              : setFormat.formatBank(accountNumber),
                      style: TextStyle(
                          color: Color(0xff707071),
                          fontFamily: 'Proxima Nova',
                          fontWeight: FontWeight.normal,
                          fontSize: MediaQuery.of(context).size.height *
                              0.***********),
                    ),
                  ],
                ),
              ),
            ),
            Positioned(
              top: MediaQuery.of(context).size.height * 0.***********,
              left: MediaQuery.of(context).size.width * 0.***********,
              child: Container(
                child: new Text(
                  AppLocalizations.of(context)!.translate('comfirmcash_fee'),
                  style: TextStyle(
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      color: Color(0xff6C6B6D).withOpacity(0.5),
                      fontSize:
                          MediaQuery.of(context).size.height * 0.01111111111,
                      fontWeight: FontWeight.bold),
                ),
              ),
            ),
            Positioned(
              top: MediaQuery.of(context).size.height * 0.38333333333,
              left: MediaQuery.of(context).size.width * 0.34166666666,
              child: Container(
                alignment: Alignment.topLeft,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      fee.toString() +
                          AppLocalizations.of(context)!
                              .translate('comfirmcash_symbol'),
                      style: TextStyle(
                          color: Color(0xff707071),
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font1'),
                          fontWeight: FontWeight.normal,
                          fontSize: MediaQuery.of(context).size.height *
                              0.***********),
                    ),
                  ],
                ),
              ),
            ),
            Positioned(
              top: MediaQuery.of(context).size.height * 0.42649572649,
              left: MediaQuery.of(context).size.width * 0.34166666666,
              child: Container(
//                      color: Color(0xffB4E60D),
                child: Row(
                  children: <Widget>[
                    Container(
                      height:
                          MediaQuery.of(context).size.height * 0.01352991452,
                      width: MediaQuery.of(context).size.width * 0.02931481481,
                      color: Color(0xffB4E60D),
                    ),
                    SizedBox(
                      width: MediaQuery.of(context).size.width * 0.03,
                    ),
                    Text(
                      AppLocalizations.of(context)!
                          .translate('comfirmcash_note'),
                      style: TextStyle(
                          color: Color(0xff707071),
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font1'),
                          fontWeight: FontWeight.normal,
                          fontSize: MediaQuery.of(context).size.height *
                              0.***********),
                    )
                  ],
                ),
              ),
            ),
            Positioned(
              top: MediaQuery.of(context).size.height * 0.45539743589,
              left: MediaQuery.of(context).size.width * 0.24167592592,
              child: new Container(
                decoration: BoxDecoration(
                  borderRadius:
                      new BorderRadius.only(topLeft: Radius.circular(50.0)),
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      spreadRadius: 0,
                      blurRadius: 4,
                      color: Color(0xff707071).withOpacity(0.5),
                      offset: Offset(
                        0.0,
                        1.0,
                      ),
                    ),
                  ],
                  //shape: BoxShape.rectangle,
//                  border: Border.all(10.0),
                ),
                height: MediaQuery.of(context).size.height * 0.20874358974,
                width: MediaQuery.of(context).size.width * 0.75833333333,
                child: TextFormField(
                  controller: note,
                  style: TextStyle(
                    fontSize:
                        MediaQuery.of(context).size.height * 0.02371794871,
                    color: Color(0xff707071),
                    fontFamily:
                        AppLocalizations.of(context)!.translate('font1'),
                  ),
                  maxLines: 5,
                  maxLength: 88,
                  keyboardType: TextInputType.text,
                  decoration: InputDecoration(
                    counterText: '',
                    focusedBorder: InputBorder.none,
//                            enabledBorder: InputBorder.none,
                    border: new OutlineInputBorder(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(50.0),
                      ),
                      borderSide: BorderSide.none,
                    ),
                    hintText: AppLocalizations.of(context)!
                        .translate('comfirmcash_note_hint'),
                    hintStyle: TextStyle(
                      color: Color(0xff707071),
                      fontSize:
                          MediaQuery.of(context).size.height * 0.02371794871,
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                    ),
                    fillColor: Colors.white,
                    contentPadding: EdgeInsets.fromLTRB(30.0, 60.0, 30.0, 10.0),
                  ),
                ),
//                        ),
              ),
            ),
            Positioned(
              top: MediaQuery.of(context).size.height * 0.60982905982,
              left: MediaQuery.of(context).size.width * 0.68407407407,
              child: new GestureDetector(
                onTap: () {
//                billTransection();

                  requestPayout();
                },
                child: ClipOval(
                  child: Container(
                    alignment: Alignment.center,
                    color: Color(0xff2B3038).withOpacity(1),
                    height: MediaQuery.of(context).size.height *
                        0.10854700854, // height of the button
                    width: MediaQuery.of(context).size.height *
                        0.10854700854, // width of the button
                    child: new Text(
                      AppLocalizations.of(context)!
                          .translate('comfirmcash_button'),
                      style: TextStyle(
                          color: Color(0xffB4E60D),
                          fontFamily: 'Proxima Nova',
                          fontWeight: FontWeight.normal,
                          fontSize: MediaQuery.of(context).size.height *
                              0.018194017094),
                    ),
                  ),
                ),
              ),
            ),
//          Positioned(
//            top: MediaQuery.of(context).size.height * 0.25,
//            left: MediaQuery.of(context).size.width * 0.07,
//            right: MediaQuery.of(context).size.width * 0.07,
//            child: new Container(
////              alignment: Alignment.center,
//              child: Row(
//                mainAxisAlignment: MainAxisAlignment.center,
//                crossAxisAlignment: CrossAxisAlignment.start,
//                children: <Widget>[
//                  new Container(
////                    color: Colors.amber,
//                    padding: EdgeInsets.only(
//                        left: MediaQuery.of(context).size.width * 0),
//                    alignment: Alignment.bottomLeft,
//                    height: MediaQuery.of(context).size.height * 0.045,
//                    width: MediaQuery.of(context).size.width * 0.45,
//                    child: Text(
//                      'FEE',
//                      style: TextStyle(
//                          color: Color(0xff000000).withOpacity(1),
//                          fontFamily: 'Proxima Nova',
//                          fontWeight: FontWeight.normal,
//                          fontSize: 13.0),
//                    ),
//                  ),
//                  new Container(
////                    color: Colors.blue,
//                    alignment: Alignment.bottomLeft,
//                    height: MediaQuery.of(context).size.height * 0.05,
//                    width: MediaQuery.of(context).size.width * 0.25,
////                          color: Colors.green,
//                    child: Text(
//                      fee.toString(),
//                      style: TextStyle(
//                          color: Color(0xff000000).withOpacity(1),
//                          fontFamily: 'Roboto',
//                          fontWeight: FontWeight.normal,
//                          fontSize: 28.0),
//                    ),
//                  ),
//                  new Container(
////                    color: Colors.blueGrey,
//                    alignment: Alignment.bottomLeft,
//                    height: MediaQuery.of(context).size.height * 0.045,
//                    width: MediaQuery.of(context).size.width * 0.12,
//                    child: Text(
//                      '  BAHT',
//                      style: TextStyle(
//                          color: Color(0xff000000).withOpacity(1),
//                          fontFamily: 'Roboto',
//                          fontWeight: FontWeight.normal,
//                          fontSize: 13.0),
//                    ),
//                  ),
//                ],
//              ),
//            ),
//          ),
//          Positioned(
//            top: MediaQuery.of(context).size.height * 0.3,
//            left: MediaQuery.of(context).size.width * 0.07,
//            right: MediaQuery.of(context).size.width * 0.07,
//            child: new Container(
////              alignment: Alignment.center,
//              child: Row(
//                mainAxisAlignment: MainAxisAlignment.center,
//                crossAxisAlignment: CrossAxisAlignment.start,
//                children: <Widget>[
//                  new Container(
////                    color: Colors.amber,
//                    padding: EdgeInsets.only(
//                        left: MediaQuery.of(context).size.width * 0),
//                    alignment: Alignment.bottomLeft,
//                    height: MediaQuery.of(context).size.height * 0.045,
//                    width: MediaQuery.of(context).size.width * 0.45,
//                    child: Text(
//                      'ACCOUNT NUMBER',
//                      style: TextStyle(
//                          color: Color(0xff000000).withOpacity(1),
//                          fontFamily: 'Proxima Nova',
//                          fontWeight: FontWeight.normal,
//                          fontSize: 13.0),
//                    ),
//                  ),
//                  new Container(
////                    color: Colors.blue,
//                    alignment: Alignment.bottomLeft,
//                    height: MediaQuery.of(context).size.height * 0.05,
//                    width: MediaQuery.of(context).size.width * 0.35,
////                          color: Colors.green,
//                    child: Text(
//                      typePay == 'promptpay'
//                          ? setFormat.formatMobile(accountNumber)
//                          : typePay == 'truemoney'
//                              ? setFormat.formatMobile(accountNumber)
//                              : setFormat.formatBank(accountNumber),
//                      style: TextStyle(
//                          color: Color(0xff000000).withOpacity(1),
//                          fontFamily: 'Roboto',
//                          fontWeight: FontWeight.normal,
//                          fontSize: 18.0),
//                    ),
//                  ),
//                ],
//              ),
//            ),
//          ),
//          Positioned(
//            top: MediaQuery.of(context).size.height * 0.35,
//            left: MediaQuery.of(context).size.width * 0.07,
//            right: MediaQuery.of(context).size.width * 0.07,
//            child: new Container(
////              alignment: Alignment.center,
//              child: Row(
//                mainAxisAlignment: MainAxisAlignment.center,
//                crossAxisAlignment: CrossAxisAlignment.start,
//                children: <Widget>[
//                  new Container(
////                    color: Colors.amber,
//                    padding: EdgeInsets.only(
//                        left: MediaQuery.of(context).size.width * 0),
//                    alignment: Alignment.bottomLeft,
//                    height: MediaQuery.of(context).size.height * 0.045,
//                    width: MediaQuery.of(context).size.width * 0.45,
//                    child: Text(
//                      'TYPE PAY',
//                      style: TextStyle(
//                          color: Color(0xff000000).withOpacity(1),
//                          fontFamily: 'Proxima Nova',
//                          fontWeight: FontWeight.normal,
//                          fontSize: 13.0),
//                    ),
//                  ),
//                  new Container(
////                    color: Colors.blue,
//                    alignment: Alignment.bottomLeft,
//                    height: MediaQuery.of(context).size.height * 0.05,
//                    width: MediaQuery.of(context).size.width * 0.35,
////                          color: Colors.green,
//                    child: Text(
//                      typePay,
//                      style: TextStyle(
//                          color: Color(0xff000000).withOpacity(1),
//                          fontFamily: 'Roboto',
//                          fontWeight: FontWeight.normal,
//                          fontSize: 16.0),
//                    ),
//                  ),
//                ],
//              ),
//            ),
//          ),
//          Positioned(
//            top: MediaQuery.of(context).size.height * 0.47,
//            left: MediaQuery.of(context).size.width * 0.07,
//            right: MediaQuery.of(context).size.width * 0.025,
//            child: new Container(
//              alignment: Alignment.centerLeft,
//              child: new Text(
//                'WILL BE DEPOSIT INTO ACCOUNT',
//                textAlign: TextAlign.center,
//                style: TextStyle(
//                    fontFamily: 'Proxima Nova',
//                    color: Color(0xff000000),
//                    fontSize: 14,
//                    fontWeight: FontWeight.normal),
//              ),
//            ),
//          ),
//          Positioned(
//            top: MediaQuery.of(context).size.height * 0.5,
//            left: MediaQuery.of(context).size.width * 0.07,
//            right: MediaQuery.of(context).size.width * 0.025,
//            child: new Container(
//                alignment: Alignment.centerLeft,
//                child: Row(
//                  children: <Widget>[
//                    new Text(
//                      typePay == 'promptpay'
//                          ? setFormat.formatMobile(accountNumber)
//                          : typePay == 'truemoney'
//                              ? setFormat.formatMobile(accountNumber)
//                              : setFormat.formatBank(accountNumber),
//                      textAlign: TextAlign.center,
//                      style: TextStyle(
//                          fontFamily: 'Proxima Nova',
//                          color: Color(0xffACE000),
//                          fontSize: 14,
//                          fontWeight: FontWeight.normal),
//                    ),
////                new Text(
////                  'WITHIN 24 HOURS ',
////                  textAlign: TextAlign.center,
////                  style: TextStyle(
////                      fontFamily: 'Proxima Nova',
////                      color: Color(0xff000000),
////                      fontSize: 14,
////                      fontWeight: FontWeight.normal),
////                ),
//                  ],
//                )),
//          ),
//          Positioned(
//            top: MediaQuery.of(context).size.height * 0.55,
//            left: MediaQuery.of(context).size.width * 0.07,
//            right: MediaQuery.of(context).size.width * 0.025,
//            child: new Container(
//              alignment: Alignment.centerLeft,
//              child: new Text(
//                'NOTE',
//                style: TextStyle(
//                    color: Color(0xff707071).withOpacity(0.5),
//                    fontFamily: 'Proxima Nova',
//                    fontWeight: FontWeight.normal,
//                    fontSize: 13.0),
//              ),
//            ),
//          ),
//          Positioned(
//            top: MediaQuery.of(context).size.height * 0.58,
//            left: MediaQuery.of(context).size.width * 0.07,
//            right: MediaQuery.of(context).size.width * 0.07,
//            child: new Container(
//              alignment: Alignment.centerLeft,
//              child: new Container(
////                padding: EdgeInsets.only(
////                    left: MediaQuery.of(context).size.height *
////                        0.05),
//                height: MediaQuery.of(context).size.height * 0.12,
//                width: MediaQuery.of(context).size.width * 0.9,
//                child: Material(
//                  color: Colors.white,
//                  elevation: 3.0,
//                  shadowColor: Colors.black,
//                  child: TextFormField(
//                    controller: note,
//                    keyboardType: TextInputType.text,
//                    maxLines: 4,
//                    style: TextStyle(fontSize: 18),
////                              obscureText: true,
//                    autofocus: false,
//                    decoration: InputDecoration(
//                      hintText: '',
//                      hintStyle: TextStyle(
//                          fontSize: 18,
//                          color: Color(0xff6C6B6D).withOpacity(0.5)),
//                      fillColor: Colors.white,
//                      filled: true,
//                      contentPadding:
//                          EdgeInsets.fromLTRB(20.0, 10.0, 20.0, 10.0),
//                      enabledBorder: OutlineInputBorder(
//                          borderRadius: BorderRadius.circular(15.0),
//                          borderSide:
//                              BorderSide(color: Colors.white, width: 3.0)),
//                    ),
//                  ),
//                ),
//              ),
//            ),
//          ),
//          Positioned(
//            top: MediaQuery.of(context).size.height * 0.8,
//            left: MediaQuery.of(context).size.width * 0.07,
//            right: MediaQuery.of(context).size.width * 0.07,
//            child: new Container(
//              alignment: Alignment.centerLeft,
//              child: new Container(
//                alignment: Alignment.center,
//                height: MediaQuery.of(context).size.height * 0.07,
//                width: MediaQuery.of(context).size.width * 0.9,
//                child: ButtonTheme(
//                  minWidth: MediaQuery.of(context).size.width / 1.2,
//                  height: MediaQuery.of(context).size.height * 0.07,
//                  child: new FlatButton(
//                      onPressed: () {
////                      Navigator.of(context).pushNamed('/billTransection');
//
//                        requestPayout();
//                      },
//                      shape: new RoundedRectangleBorder(
//                        borderRadius: new BorderRadius.circular(8.0),
//                      ),
//                      disabledColor: Color(0xff150e23).withOpacity(0.8),
//                      color: Color(0xff150e23).withOpacity(0.8),
////                      onPressed: () {_sendTransaction();},
//                      child: new Text(
//                        'Confirm',
//                        style: TextStyle(
//                            color: Color(0xffB4E60D),
//                            fontSize: 18,
//                            fontWeight: FontWeight.normal),
//                      )),
//                ),
//              ),
//            ),
//          ),
          ],
        ),
      ),
    );
  }

  buildForTablet(Orientation orientation) {
    return Scaffold();
  }

  @override
  Widget build(BuildContext context) {
    final double shortestSide = MediaQuery.of(context).size.shortestSide;
    final bool useMobileLayout = shortestSide < 600.0;
    final Orientation orientation = MediaQuery.of(context).orientation;
    return ModalProgressHUD(
      opacity: 0.1,
      child: Scaffold(
        body: useMobileLayout
            ? buildForPhone(orientation)
            : buildForPhone(orientation),
      ),
      inAsyncCall: _saving,
      progressIndicator: CustomLoading(),
    );
  }

  _loadingView() {
    setState(() {
      _saving = true;
    });
//Simulate a service call
    print('submitting to backend...');
    new Future.delayed(new Duration(seconds: 2), () {
      setState(() {
        changeMenu(1);
      });
    });
  }

  Widget _Progress() {
    return Container(
        alignment: Alignment.bottomCenter,
        padding: EdgeInsets.only(bottom: MediaQuery.of(context).size.width / 3),
        child: Image.asset(
          'assets/image/success.png',
          scale: 3.5,
        ));
  }
}
