import 'package:flutter/material.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/screen_util.dart';

class CropImage extends StatefulWidget {
  _CropImage createState() => new _CropImage();
}

class _CropImage extends State<CropImage> {
  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Scaffold(
      body: Stack(
        children: <Widget>[_title(), _screenCropImage()],
      ),
    );
  }

  Widget _title() {
    return Text(
      'ปรับภาพถ่าย',
      style: TextStyle(
          letterSpacing: 1,
          color: Color(0xff707071),
          fontFamily: AppLocalizations.of(context)!.translate('font1'),
          fontSize:
              MediaQuery.of(context).size.height * Screen_util('height', 56),
          fontWeight: FontWeight.normal),
    );
  }

  Widget _screenCropImage() {
    return Container(
      color: LikeWalletAppTheme.gray,
      width: mediaQuery(context, 'width', 1080),
      height: mediaQuery(context, 'height', 780),
    );
  }
}
