import 'dart:convert';
import 'dart:io';
import 'dart:ui';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/bank/add_banking/listBanking.dart';
import 'package:likewallet/bank/navigation_bar/navigation_bar_white.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:likewallet/libraryman/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import 'package:likewallet/libraryman/keyboard_done_widget.dart';
import 'package:likewallet/middleware/getLanguage.dart';

import 'package:likewallet/screen/NavigationBar.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/bank/netBanking.dart';
import 'package:likewallet/screen/NavigationBar.dart';
import 'package:likewallet/bank/promptpay.dart';
import 'package:likewallet/bank/trueMoney.dart';
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/bank/confirmCash.dart';
import 'package:likewallet/setmodel/cash_out_model.dart';
import 'package:likewallet/tabslide/logout.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ChoiceTopay extends StatefulWidget {
  ChoiceTopay(
      {required this.rate,
      required this.amount,
      required this.fee,
      required this.symbol,
      required this.totalSell});
  final double rate;
  final double amount;
  final double fee;
  final String symbol;
  final double totalSell;

  _ChoiceTopay createState() => new _ChoiceTopay(
      rate: rate,
      amount: amount,
      fee: fee,
      symbol: symbol,
      totalSell: totalSell);
}

class _ChoiceTopay extends State<ChoiceTopay> {
  _ChoiceTopay(
      {required this.rate,
      required this.amount,
      required this.fee,
      required this.symbol,
      required this.totalSell});
  final double rate;
  final double totalSell;
  final double amount;
  final double fee;
  final String symbol;
  bool show = false;
  final fireStore = FirebaseFirestore.instance;
  final FirebaseAuth firebaseAuth = FirebaseAuth.instance;
  late BaseAuth auth;
  String? uid;
  late String typePayment;
  bool promptpay = false;
  bool truemoney = false;
  late OverlayEntry? overlayEntry;
  FocusNode promptPayFocusNode = new FocusNode();
  final TextEditingController nameAccount = TextEditingController();
  final TextEditingController numberPromptpay = TextEditingController();
  final TextEditingController numberTruemoney = TextEditingController();
  CashRateText? cashRateText;
  late OnLanguage onLanguage;
  String cashTimeout = '';

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    auth = Auth();
    onLanguage = CallLanguage();
    getUID();
    if (Platform.isIOS) {
      promptPayFocusNode.addListener(() {
        bool hasFocus = promptPayFocusNode.hasFocus;
        if (hasFocus)
          showOverlay(context);
        else
          removeOverlay();
      });
    }
  }

  //done button zone
  showOverlay(BuildContext context) {
    if (overlayEntry != null) return;
    OverlayState? overlayState = Overlay.of(context);
    overlayEntry = OverlayEntry(builder: (context) {
      return Positioned(
          bottom: MediaQuery.of(context).viewInsets.bottom,
          right: 0.0,
          left: 0.0,
          child: Container(
            width: double.infinity,
            color: Colors.white,
            child: Align(
              alignment: Alignment.topRight,
              child: Padding(
                padding: const EdgeInsets.only(top: 4.0, bottom: 4.0),
                child: CupertinoButton(
                  padding: EdgeInsets.only(right: 24.0, top: 8.0, bottom: 8.0),
                  onPressed: () async {
                    FocusScope.of(context).requestFocus(new FocusNode());
                    onSubmit(numberPromptpay.text);
                  },
                  child: Text("Done",
                      style: TextStyle(
                          color: Colors.grey, fontWeight: FontWeight.bold)),
                ),
              ),
            ),
          ));
    });

    overlayState!.insert(overlayEntry!);
  }

  removeOverlay() {
    if (overlayEntry != null) {
      overlayEntry!.remove();
      overlayEntry = null;
    }
  }

  @override
  void dispose() {
    // TODO: implement dispose
    promptPayFocusNode.dispose();
    super.dispose();
  }

  getUID() async {
    User? user = firebaseAuth.currentUser;
    var lang = await onLanguage.getLanguage();
    setState(() {
      uid = user!.uid;
    });
    // await getRateCashOut(lang);
  }

  changePayment(value) {
    setState(() {
      typePayment = value;
    });
  }

  Future getRateCashOut(lang) async {
    print(lang);
    await FirebaseFirestore.instance
        .collection('allText')
        .doc('cashText')
        .get()
        .then((value) {
      setState(() {
        cashRateText = CashRateText.fromJson(value.data());
      });
    });
    await FirebaseFirestore.instance
        .collection('allText')
        .doc('cash')
        .collection(lang)
        .doc('chooseMethod')
        .get()
        .then((value) {
      setState(() => cashTimeout = value.data()!['cash_timeout']);
      print(cashTimeout);
    });
  }

  onSubmit(value) {
    String pattern = r'(^(?:[0]9)?[0-9]{10,}$)';
    RegExp regex = new RegExp(pattern);
    if (value.length == 0) {
      showShortToast('กรุณากรอกข้อมูลให้ครบ', Colors.cyan);
      return 'Please enter mobile number';
    } else if (!regex.hasMatch(value)) {
      print('not match');
      showShortToast(
          'กรุณาใส่เบอร์โทรหรือ รหัสบัตรประชาชน ตัวอย่าง 0808349123 หรือ 1229900468722',
          Colors.red);
      return 'Please enter valid mobile number';
    } else if (regex.hasMatch(value)) {
      value.length < 10
          ? showColoredToast(
              AppLocalizations.of(context)!.translate('check_promptpay'))
          : Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) => ConfirmCash(
                      amount: amount,
                      totalSell: totalSell,
                      fee: fee,
                      rate: rate,
                      nameAccount: '',
                      accountNumber: numberPromptpay.text.replaceAll('-', ''),
                      typePay: 'promptpay',
                      symbol: symbol)),
            );
    }
  }

  buildForPhone(Orientation orientation) {
    return Scaffold(
//      bottomNavigationBar: NavigationBar(),
      backgroundColor: LikeWalletAppTheme.white,
      body: GestureDetector(
        onTap: () {
          setState(() {
            FocusScopeNode currentFocus = FocusScope.of(context);

            if (!currentFocus.hasPrimaryFocus) {
              currentFocus.unfocus();
            }
            setState(() {
              promptpay = false;
              truemoney = false;
              show = false;
            });
          });
        },
        child: Stack(
          alignment: Alignment.topCenter,
          children: <Widget>[
            Container(
              height: MediaQuery.of(context).size.height,
              decoration: BoxDecoration(
                color: Color(0xffF5F5F5),
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  stops: [0.0, 0.2, 0.5],
                  colors: [
                    // Colors are easy thanks to Flutter's Colors class.
                    Colors.white,
                    Colors.white,
                    LikeWalletAppTheme.white1
                  ],
                ),
              ),
            ),
            SingleChildScrollView(
              child: Container(
                height: mediaQuery(context, 'height', 2000),
                child: Stack(alignment: Alignment.topCenter, children: <Widget>[
                  if (symbol == 'THB' || symbol == 'LAK' || symbol == 'USD')
                    banking(),
                  // if (symbol == 'THB') promptPay(),
                  text()
                  // if (symbol == 'THB') trueMoney(),
                ]),
              ),
            ),
            Container(
              alignment: Alignment.bottomCenter,
              padding:
                  EdgeInsets.only(bottom: mediaQuery(context, 'height', 35)),
              color: LikeWalletAppTheme.bule2,
              height: mediaQuery(context, 'height', 255),
              width: MediaQuery.of(context).size.height,
              child: new Text(
                AppLocalizations.of(context)!.translate('choose_title'),
                style: TextStyle(
                    letterSpacing: 0.5,
                    fontFamily:
                        AppLocalizations.of(context)!.translate('font1'),
                    color: Color(0xffFFFFFF).withOpacity(1),
                    fontSize: mediaQuery(context, 'height', 45),
                    fontWeight: FontWeight.w500),
              ),
            ),
            // Positioned(
            //   top: MediaQuery.of(context).size.height * 0.08034188034,
            //   child: Container(
            //     alignment: Alignment.center,
            //     width: MediaQuery.of(context).size.width,
            //     chil
            //   ),
            // ),
            Positioned(
              left: 0,
              top: MediaQuery.of(context).size.height * 0.08608547008,
              child: GestureDetector(
                onTap: () => {Navigator.of(context).pop()},
                child: Container(
                  decoration: BoxDecoration(
                    color: Color(0xffB4E60D),
                    borderRadius: new BorderRadius.only(
                        bottomRight: Radius.circular(40.0),
                        topRight: Radius.circular(40.0)),
                  ),
                  height: MediaQuery.of(context).size.height * 0.***********,
                  width: MediaQuery.of(context).size.width * 0.***********,
                  child: Icon(
                    Icons.arrow_back_ios,
//                        color: Colors.blue,
                    size: MediaQuery.of(context).size.height * 0.**********,
                  ),
                ),
              ),
            ),
            _addAccountBanking(context)
          ],
        ),
      ),
    );
  }

  Widget banking() {
    return Positioned(
      top: mediaQuery(context, 'height', 415),
      child: GestureDetector(
        onTap: () {
          setState(() {
            show = true;
            promptpay = false;
            truemoney = false;
          });
//                  Navigator.pushNamed(
//                      context,
//                      '/netBank',
//                    arguments: {
//                        'amount':amount,
//                        'fee': fee,
//                        'rate': rate
//                    }
//                  );
//                  print(amount);
//                  Navigator.push(
//                    context,
//                    MaterialPageRoute(
//                        builder: (context) => NetBanking(
//                              amount: amount,
//                              fee: fee,
//                              rate: rate,
//                            )),
//                  );
        },
        child: new Container(
            height: mediaQuery(context, 'height', 319),
            width: mediaQuery(context, 'width', 932),
            alignment: Alignment.center,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20.0),
              color: LikeWalletAppTheme.white,
              boxShadow: [
                BoxShadow(
                  color: LikeWalletAppTheme.black.withOpacity(0.1),
                  offset: Offset(0, 0),
                  blurRadius: 8,
                ),
              ],
            ),
            child: Text(
              AppLocalizations.of(context)!.translate('choose_bank'),
              textAlign: TextAlign.center,
              style: TextStyle(
                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                fontSize: mediaQuery(context, 'height', 45),
                color: LikeWalletAppTheme.black,
                letterSpacing: 0.139,
                fontWeight: FontWeight.w600,
              ),
            )),
      ),
    );
  }

  Widget promptPay() {
    return promptpay
        ? Positioned(
            top: mediaQuery(context, 'height', 761),
            width: mediaQuery(context, 'width', 743),
            child: GestureDetector(
                onTap: () {
                  setState(() {
                    promptpay = false;
                  });
                },
                child: Column(
                  children: <Widget>[
                    Container(
                        height: mediaQuery(context, 'height', 319),
                        width: mediaQuery(context, 'width', 932),
                        alignment: Alignment.center,
                        child: Image.asset(
                          LikeWalletImage.icon_promptpay,
                          height: mediaQuery(context, 'height', 174.24),
                          width: mediaQuery(context, 'width', 229.92),
                        )),
                    Container(
                      padding: EdgeInsets.only(
                        top: mediaQuery(context, 'height', 0),
                      ),
                      child: TextFormField(
                        controller: numberPromptpay,
                        focusNode: promptPayFocusNode,
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          LengthLimitingTextInputFormatter(13),
                        ],
                        onFieldSubmitted: (value) {
                          onSubmit(value);
                        },
                        style: TextStyle(
                          color: LikeWalletAppTheme.gray1,
                          fontSize: MediaQuery.of(context).size.height *
                              Screen_util('height', 42),
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font1'),
                        ),
                        decoration: InputDecoration(
                            hintText: AppLocalizations.of(context)!
                                .translate('choose_promptpay'),
                            helperStyle: TextStyle(
                                color: LikeWalletAppTheme.gray,
                                fontFamily: AppLocalizations.of(context)!
                                    .translate('font1'),
                                fontSize: MediaQuery.of(context).size.height *
                                    Screen_util('height', 34),
                                fontWeight: FontWeight.normal),
                            enabledBorder: UnderlineInputBorder(
                                borderSide: BorderSide(
                                    color: LikeWalletAppTheme.gray
                                        .withOpacity(0.5))),
                            contentPadding: EdgeInsets.only(
                              top: mediaQuery(context, 'height', 40),
                            ),
                            focusedBorder: UnderlineInputBorder(
                                borderSide: BorderSide(
                                    color: LikeWalletAppTheme.gray
                                        .withOpacity(0.5)))),
                      ),
                    ),
                  ],
                )),
          )
        : Positioned(
            top: mediaQuery(context, 'height', 761),
            width: mediaQuery(context, 'width', 932),
            child: GestureDetector(
              onTap: () async {
                setState(() {
                  promptpay = true;
                  truemoney = false;
                  show = false;
                });
              },
              child: new Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20.0),
                    color: LikeWalletAppTheme.white,
                    boxShadow: [
                      BoxShadow(
                        color: LikeWalletAppTheme.black.withOpacity(0.1),
                        offset: Offset(0, 0),
                        blurRadius: 8,
                      ),
                    ],
                  ),
                  height: mediaQuery(context, 'height', 319),
                  width: mediaQuery(context, 'width', 932),
                  alignment: Alignment.center,
                  child: Image.asset(
                    LikeWalletImage.icon_promptpay,
                    height: mediaQuery(context, 'height', 174.24),
                    width: mediaQuery(context, 'width', 229.92),
                  )),
            ),
          );
  }

  Widget text() {
    return Positioned(
      top: promptpay
          ? mediaQuery(context, 'height', 823)
          : mediaQuery(context, 'height', 823),
      width: mediaQuery(context, 'width', 930),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            width: 879.0.w,
            height: 1209.0.h,
            child: Text.rich(
              TextSpan(
                style: TextStyle(
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  fontSize: 42.h,
                  color: const Color(0xff707a8a),
                  letterSpacing: 1.26.w,
                ),
                children: [
                  TextSpan(
                    text: AppLocalizations.of(context)!.translate('cash_text1'),
                    style: TextStyle(
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  TextSpan(
                    text: AppLocalizations.of(context)!.translate('cash_text2'),
                    style: TextStyle(
                      fontSize: 36.h,
                      letterSpacing: 1.08.w,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  TextSpan(text: '\n'),
                  TextSpan(text: '\n'),
                  TextSpan(
                    text: AppLocalizations.of(context)!.translate('cash_text3'),
                    style: TextStyle(
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  TextSpan(
                    text: AppLocalizations.of(context)!.translate('cash_text4'),
                    style: TextStyle(
                      fontSize: 36.h,
                      letterSpacing: 1.08.w,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  TextSpan(text: '\n'),
                  TextSpan(text: '\n'),
                  TextSpan(
                    text: AppLocalizations.of(context)!.translate('cash_text5'),
                    style: TextStyle(
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  TextSpan(
                    text: AppLocalizations.of(context)!.translate('cash_text6'),
                    style: TextStyle(
                      fontSize: 36.h,
                      letterSpacing: 1.08.w,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  TextSpan(text: '\n'),
                  TextSpan(text: '\n'),
                  TextSpan(
                    text: AppLocalizations.of(context)!.translate('cash_text7'),
                    style: TextStyle(
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  TextSpan(
                    text: AppLocalizations.of(context)!.translate('cash_text8'),
                    style: TextStyle(
                      fontSize: 36.h,
                      letterSpacing: 1.08.w,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  TextSpan(text: '\n'),
                  TextSpan(text: '\n'),
                  TextSpan(
                    text: AppLocalizations.of(context)!.translate('cash_text9'),
                    style: TextStyle(
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  TextSpan(
                    text: AppLocalizations.of(context)!.translate('cash_text10'),
                    style: TextStyle(
                      fontSize: 36.h,
                      letterSpacing: 1.08.w,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  TextSpan(text: '\n'),
                  TextSpan(text: '\n'),
                  TextSpan(
                    text:
                        AppLocalizations.of(context)!.translate('cash_text11'),
                    style: TextStyle(
                      fontSize: 36.h,
                      color: const Color(0xffff0000),
                      letterSpacing: 1.08.w,
                    ),
                  ),
                  TextSpan(
                    text:
                    AppLocalizations.of(context)!.translate('cash_text12'),
                    style: TextStyle(
                      fontSize: 36.h,
                      color: const Color(0xffff0000),
                      letterSpacing: 1.08.w,
                    ),
                  ),
                ],
              ),
              textHeightBehavior:
                  TextHeightBehavior(applyHeightToFirstAscent: false),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget trueMoney() {
    return truemoney
        ? Positioned(
            top: truemoney
                ? mediaQuery(context, 'height', 1107)
                : mediaQuery(context, 'height', 1452),
            width: mediaQuery(context, 'width', 743),
            child: GestureDetector(
                onTap: () {
                  setState(() {
                    truemoney = false;
                  });
//                Navigator.pushNamed(context, '/promptpay');
//                  Navigator.push(
//                    context,
//                    MaterialPageRoute(
//                        builder: (context) => PromptPay(
//                              amount: amount,
//                              fee: fee,
//                              rate: rate,
//                            )),
//                  );
                },
                child: Column(
                  children: <Widget>[
                    Container(
                        height: mediaQuery(context, 'height', 319),
                        width: mediaQuery(context, 'width', 932),
                        alignment: Alignment.center,
                        child: Image.asset(
                          LikeWalletImage.icon_truemoney,
                          height: mediaQuery(context, 'height', 200.24),
                          width: mediaQuery(context, 'width', 229.92),
                        )),
                    Container(
                      padding: EdgeInsets.only(
                        top: mediaQuery(context, 'height', 0),
                      ),
                      child: TextFormField(
                        controller: numberTruemoney,
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          LengthLimitingTextInputFormatter(10),
                        ],
                        onFieldSubmitted: (value) {
                          String pattern = r'(^(?:[0]9)?[0-9]{10,}$)';
                          RegExp regex = new RegExp(pattern);
                          if (value.length == 0) {
                            showShortToast(
                                'กรุณากรอกข้อมูลให้ครบ', Colors.cyan);
                            // return 'Please enter mobile number';
                          } else if (!regex.hasMatch(value)) {
                            print('not match');
                            showShortToast(
                                'กรุณาใส่เบอร์โทร ตัวอย่าง 0808349123',
                                Colors.red);
                            // return 'Please enter valid mobile number';
                          } else if (regex.hasMatch(value)) {
                            value.length < 10
                                ? showColoredToast(AppLocalizations.of(context)!
                                    .translate('check_truemoney'))
                                : Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) => ConfirmCash(
                                            amount: amount,
                                            totalSell: totalSell,
                                            fee: fee,
                                            rate: rate,
                                            nameAccount: '',
                                            accountNumber: numberTruemoney.text,
                                            typePay: 'truemoney',
                                            symbol: symbol)),
                                  );
                          }
                        },
                        style: TextStyle(
                            color: LikeWalletAppTheme.gray1,
                            fontSize: MediaQuery.of(context).size.height *
                                Screen_util('height', 42),
                            fontFamily: AppLocalizations.of(context)!
                                .translate('font1')),
                        decoration: InputDecoration(
                            hintText: AppLocalizations.of(context)!
                                .translate('choose_truemoney'),
                            helperStyle: TextStyle(
                                color: LikeWalletAppTheme.gray,
                                fontFamily: AppLocalizations.of(context)!
                                    .translate('font1'),
                                fontSize: MediaQuery.of(context).size.height *
                                    Screen_util('height', 34),
                                fontWeight: FontWeight.normal),
                            enabledBorder: UnderlineInputBorder(
                                borderSide: BorderSide(
                                    color: LikeWalletAppTheme.gray
                                        .withOpacity(0.5))),
                            contentPadding: EdgeInsets.only(
                              top: mediaQuery(context, 'height', 40),
                            ),
                            focusedBorder: UnderlineInputBorder(
                                borderSide: BorderSide(
                                    color: LikeWalletAppTheme.gray
                                        .withOpacity(0.5)))),
                      ),
                    ),
                  ],
                )),
          )
        : Positioned(
            top: promptpay
                ? mediaQuery(context, 'height', 1452)
                : mediaQuery(context, 'height', 1107),
            height: mediaQuery(context, 'height', 319),
            width: mediaQuery(context, 'width', 932),
            child: GestureDetector(
              onTap: () async {
                setState(() {
                  promptpay = false;
                  truemoney = true;
                  show = false;
                });
              },
              child: new Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20.0),
                    color: LikeWalletAppTheme.white,
                    boxShadow: [
                      BoxShadow(
                        color: LikeWalletAppTheme.black.withOpacity(0.1),
                        offset: Offset(0, 0),
                        blurRadius: 8,
                      ),
                    ],
                  ),
                  alignment: Alignment.center,
                  child: Image.asset(
                    LikeWalletImage.icon_truemoney,
                    height: mediaQuery(context, 'height', 174.24),
                    width: mediaQuery(context, 'width', 229.92),
                  )),
            ),
          );
  }

  Widget _addAccountBanking(context) {
    return AnimatedPositioned(
        top: show ? 0 : mediaQuery(context, 'height', -2430),
        duration: Duration(milliseconds: 300),
        child: GestureDetector(
            onTap: () {
              FocusScopeNode currentFocus = FocusScope.of(context);
              if (!currentFocus.hasPrimaryFocus) {
                currentFocus.unfocus();
              }
            },
            child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                      bottomRight: Radius.circular(37.0),
                      bottomLeft: Radius.circular(37.0)),
                  color: LikeWalletAppTheme.white,
                ),
                child: AnimatedContainer(
                  duration: Duration(milliseconds: 300),
                  alignment: Alignment.topCenter,
                  width: MediaQuery.of(context).size.width,
//                  height: show
//                      ? mediaQuery(context, 'height', 350) * 2.5
//                      : mediaQuery(context, 'height', 0),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      stops: [0.4, 0.6, 0.7, 0.8, 0.85],
                      colors: [
                        // Colors are easy thanks to Flutter's Colors class.
                        LikeWalletAppTheme.bule2.withOpacity(1),
                        LikeWalletAppTheme.bule2.withOpacity(0.94),
                        LikeWalletAppTheme.bule2.withOpacity(0.92),
                        LikeWalletAppTheme.bule2.withOpacity(0.9),
                        LikeWalletAppTheme.bule2.withOpacity(0.9),
                      ],
                    ),
                    borderRadius: BorderRadius.only(
                        bottomRight: Radius.circular(35.0),
                        bottomLeft: Radius.circular(35.0)),
                    boxShadow: [
                      new BoxShadow(
                          color: Colors.black.withOpacity(0.16),
                          offset: new Offset(0, 3),
                          blurRadius: 3.0,
                          spreadRadius: 0.0),
                    ],
                    color: LikeWalletAppTheme.white,
                  ),
                  child: SingleChildScrollView(
                      child: Column(
                    children: <Widget>[
                      _head(),
                      _listAccount(),
                      _buttonAddAccount(),
                    ],
                  )),
                ))));
  }

  Widget _head() {
    return Container(
        margin: EdgeInsets.only(
          top: mediaQuery(context, 'height', 200),
          bottom: mediaQuery(context, 'height', 155),
        ),
        child: Text(
          AppLocalizations.of(context)!.translate('choose_bank_account'),
          style: TextStyle(
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              color: Color(0xffFFFFFF).withOpacity(1),
              fontSize: MediaQuery.of(context).size.height * 0.***********,
              fontWeight: FontWeight.w500),
        ));
  }

  Widget _listAccount() {
    return uid != null
        ? StreamBuilder(
            stream: fireStore
                .collection('kycCashOut')
                .doc('bookBank')
                .collection(FirebaseAuth.instance.currentUser!.uid)
                .snapshots(),
            builder: (BuildContext context,
                AsyncSnapshot<QuerySnapshot<Map<String, dynamic>>> snapshot) {
              if (snapshot.hasError)
                return new Text('Error: ${snapshot.error}');
              switch (snapshot.connectionState) {
                case ConnectionState.waiting:
                  return Container();
                default:
                  return new Column(
                    children: snapshot.data!.docs
                        .map((DocumentSnapshot<Map<String, dynamic>> document) {
                      print('status is : ' + document['status']);
                      return GestureDetector(
                          onTap: () {
                            document['status'] == 'pending'
                                ? null
                                : document['status'] == 'verify'
                                    ? Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                            builder: (context) => ConfirmCash(
                                                amount: amount,
                                                totalSell: totalSell,
                                                fee: fee,
                                                rate: rate,
                                                nameAccount:
                                                    document['nameBookBank'],
                                                accountNumber:
                                                    document['numberBookBank']
                                                        .toString(),
                                                typePay: document['nameBank']
                                                    .toString(),
                                                symbol: symbol)),
                                      )
                                    : document['status'] == 'reject'
                                        ? Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                                builder: (context) =>
                                                    ListBanking(
                                                        symbol: symbol)),
                                          )
                                        : null;
                          },
                          child: Container(
                              padding: EdgeInsets.only(
                                left: mediaQuery(context, 'width', 100),
                                bottom: mediaQuery(context, 'height', 70),
                              ),
                              child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: <Widget>[
                                    Image.network(
                                      document['imageBank'],
                                      height:
                                          mediaQuery(context, 'height', 126),
                                    ),
                                    Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: <Widget>[
                                        SizedBox(
                                            width: mediaQuery(
                                                context, 'width', 25)),
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: <Widget>[
                                            Text(
                                              document['nameBookBank'],
                                              style: TextStyle(
                                                  fontFamily:
                                                      AppLocalizations.of(
                                                              context)!
                                                          .translate('font1'),
                                                  color:
                                                      LikeWalletAppTheme.gray1,
                                                  fontSize: mediaQuery(
                                                      context, 'heigth', 42),
                                                  fontWeight: FontWeight.w100),
                                            ),
                                            new Text(
                                              document['numberBookBank']
                                                      .toString()
                                                      .substring(0, 3) +
                                                  "-" +
                                                  document['numberBookBank']
                                                      .toString()
                                                      .substring(3, 4) +
                                                  "-" +
                                                  document['numberBookBank']
                                                      .toString()
                                                      .substring(4, 9) +
                                                  "-" +
                                                  document['numberBookBank']
                                                      .toString()
                                                      .substring(9, 10),
                                              style: TextStyle(
                                                  fontFamily:
                                                      AppLocalizations.of(
                                                              context)!
                                                          .translate('font1'),
                                                  color:
                                                      LikeWalletAppTheme.gray1,
                                                  fontSize: mediaQuery(
                                                      context, 'heigth', 42),
                                                  fontWeight: FontWeight.w100),
                                            ),
                                          ],
                                        ),
                                        Expanded(child: Container()),
                                        Container(
                                          alignment: Alignment.center,
                                          width: mediaQuery(
                                              context, 'height', 281),
                                          height:
                                              mediaQuery(context, 'height', 80),
                                          decoration: new BoxDecoration(
                                            borderRadius: BorderRadius.all(
                                              Radius.circular(100.0),
                                            ),
                                            color: LikeWalletAppTheme.gray
                                                .withOpacity(0.4),
                                          ),
                                          child: Text(
                                            document['status'] == 'pending'
                                                ? AppLocalizations.of(context)!
                                                    .translate(
                                                        'add_account_alert_status_pending')
                                                : document['status'] == 'verify'
                                                    ? AppLocalizations.of(
                                                            context)!
                                                        .translate(
                                                            'add_account_alert_status_verify')
                                                    : document['status'] ==
                                                            'reject'
                                                        ? AppLocalizations.of(
                                                                context)!
                                                            .translate(
                                                                'add_account_alert_status_reject')
                                                        : "",
                                            style: TextStyle(
                                                fontFamily: AppLocalizations.of(
                                                        context)!
                                                    .translate('font1'),
                                                color: document['status'] ==
                                                        'pending'
                                                    ? Colors.yellow
                                                    : document['status'] ==
                                                            'verify'
                                                        ? LikeWalletAppTheme
                                                            .bule1_3
                                                        : document['status'] ==
                                                                'reject'
                                                            ? LikeWalletAppTheme
                                                                .red
                                                            : LikeWalletAppTheme
                                                                .bule1_3,
                                                fontSize: mediaQuery(
                                                    context, 'heigth', 30),
                                                fontWeight: FontWeight.w100),
                                          ),
                                        ),
                                        GestureDetector(
                                          onTap: () async {
                                            print('hello minus');
                                            await RemoveDialog(
                                                context,
                                                document['numberBookBank']
                                                    .toString());
                                          },
                                          child: Container(
                                            margin: EdgeInsets.only(
                                                right: mediaQuery(
                                                    context, 'width', 75),
                                                bottom: mediaQuery(
                                                    context, 'width', 0)),
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.end,
                                              children: <Widget>[
                                                SizedBox(
                                                  width: mediaQuery(
                                                      context, 'width', 37),
                                                ),
                                                Container(
                                                    width: mediaQuery(
                                                        context, 'height', 80),
                                                    height: mediaQuery(
                                                        context, 'height', 80),
                                                    decoration:
                                                        new BoxDecoration(
                                                      color: LikeWalletAppTheme
                                                          .gray
                                                          .withOpacity(0.4),
                                                      shape: BoxShape.circle,
                                                    ),
                                                    child: Icon(
                                                      Icons.close,
                                                      color: Colors.white
                                                          .withOpacity(0.4),
                                                      size: mediaQuery(context,
                                                          'height', 40),
                                                    ))
                                              ],
                                            ),
                                          ),
                                        )
                                      ],
                                    ),
                                  ])));
                    }).toList(),
                  );
              }
            },
          )
        : Container();
  }

  Future removeBankAccount(documentID) async {
    var decodeToken = await auth.getCurrentUser();
    // print(decodeToken);
    uid = decodeToken!.uid;
    print(uid);
    fireStore
        .collection('kycCashOut')
        .doc('bookBank')
        .collection(FirebaseAuth.instance.currentUser!.uid)
        .doc(documentID)
        .delete();
    return true;
  }

  RemoveDialog(context, documentID) {
    Dialog simpleDialog = Dialog(
      elevation: 500,
      backgroundColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(30.0),
      ),
      child: Container(
        height: mediaQuery(context, 'height', 554.63),
        width: mediaQuery(context, 'width', 929.64),
        color: Colors.transparent,
        margin: EdgeInsets.only(bottom: mediaQuery(context, 'height', 600)),
        child: new ClipRect(
          child: new BackdropFilter(
            filter: new ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
            child: Container(
              decoration: BoxDecoration(
                color: LikeWalletAppTheme.white.withOpacity(0.6),
                borderRadius: BorderRadius.all(Radius.circular(20.0)),
              ),
              height: mediaQuery(context, 'height', 554.63),
              width: mediaQuery(context, 'width', 929.64),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  Text(
                    AppLocalizations.of(context)!.translate('remove_bank'),
                    style: TextStyle(
                      letterSpacing: 0.3,
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      color: LikeWalletAppTheme.black.withOpacity(1),
                      fontSize: mediaQuery(context, "height", 56),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(
                        bottom: mediaQuery(context, 'height', 80)),
                    width: mediaQuery(context, 'width', 777.62),
                    child: Text(
                      AppLocalizations.of(context)!
                          .translate('remove_bank_detail'),
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        letterSpacing: 0.3,
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                        color: LikeWalletAppTheme.black.withOpacity(1),
                        fontSize: mediaQuery(context, "height", 42),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Container(
                      width: mediaQuery(context, 'width', 777.62),
                      decoration: BoxDecoration(
                        border: Border(
                          top: BorderSide(
                            //                   <--- left side
                            color: LikeWalletAppTheme.black.withOpacity(0.4),
                            width: mediaQuery(context, 'width', 1),
                          ),
                        ),
                      ),
                      child: Row(
                        children: <Widget>[
                          GestureDetector(
                            onTap: () async {
                              removeBankAccount(documentID).then((value) {
                                print('remove it');
                                Navigator.of(context).pop();
                              });
                            },
                            child: Container(
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                border: Border(
                                  right: BorderSide(
                                    //                   <--- left side
                                    color: LikeWalletAppTheme.black
                                        .withOpacity(0.4),
                                    width: mediaQuery(context, 'width', 1),
                                  ),
                                ),
                              ),
                              height: mediaQuery(context, 'height', 127.66),
                              width: mediaQuery(context, 'width', 777.62) / 2,
                              child: Text(
                                AppLocalizations.of(context)!
                                    .translate('logout_yes'),
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  letterSpacing: 0.3,
                                  fontFamily: AppLocalizations.of(context)!
                                      .translate('font1'),
                                  color:
                                      LikeWalletAppTheme.bule1_7.withOpacity(1),
                                  fontSize: mediaQuery(context, "height", 52),
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                          GestureDetector(
                            onTap: () {
                              Navigator.of(context).pop();
                            },
                            child: Container(
                              alignment: Alignment.center,
                              height: mediaQuery(context, 'height', 127.66),
                              width: mediaQuery(context, 'width', 777.62) / 2,
                              child: Text(
                                AppLocalizations.of(context)!
                                    .translate('logout_no'),
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  letterSpacing: 0.3,
                                  fontFamily: AppLocalizations.of(context)!
                                      .translate('font1'),
                                  color:
                                      LikeWalletAppTheme.bule1_7.withOpacity(1),
                                  fontSize: mediaQuery(context, "height", 52),
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                        ],
                      )),
                ],
              ),
            ),
          ),
        ),
      ),
    );
    showDialog(
        context: context, builder: (BuildContext context) => simpleDialog);
  }

  Widget _buttonAddAccount() {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => ListBanking(symbol: symbol)),
        );
      },
      child: Container(
          margin: EdgeInsets.only(
              right: mediaQuery(context, 'width', 50),
              bottom: mediaQuery(context, 'width', 50)),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: <Widget>[
              Text(
                AppLocalizations.of(context)!.translate('add_bank_account'),
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  fontSize: mediaQuery(context, 'height', 39),
                  color: LikeWalletAppTheme.white.withOpacity(0.7),
                  letterSpacing: 0.139,
                  fontWeight: FontWeight.w100,
                ),
              ),
              SizedBox(
                width: mediaQuery(context, 'width', 20),
              ),
              Image.asset(
                LikeWalletImage.plus,
                height: mediaQuery(context, 'height', 103),
                width: mediaQuery(context, 'width', 103),
              ),
            ],
          )),
    );
  }

  @override
  Widget build(BuildContext context) {
    final double shortestSide = MediaQuery.of(context).size.shortestSide;
    final bool useMobileLayout = shortestSide < 600.0;
    final Orientation orientation = MediaQuery.of(context).orientation;
    return Scaffold(
      bottomNavigationBar: NavigationBarWhite(),
      body: useMobileLayout
          ? buildForPhone(orientation)
          : buildForPhone(orientation),
    );
  }
}
