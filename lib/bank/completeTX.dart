import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/bank/banking.dart';
import 'package:likewallet/bank/navigation_bar/navigation_bar_white.dart';
import 'package:likewallet/kycSumSub/kycSumSub.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/model/sumSubToken.dart';
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/screen/NavigationBar.dart';
import 'package:likewallet/bank/banking.dart';
import 'package:likewallet/middleware/callHttp.dart';
import 'package:likewallet/app_config.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CompleteTX extends StatefulWidget {
  CompleteTX(
      {this.uid,
      this.phone,
      this.statusPage,
      this.title,
      this.detail,
      this.buttonText,
      this.back});
  final uid;
  final phone;
  final statusPage;
  final title;
  final detail;
  final buttonText;
  final back;

  _CompleteTX createState() => new _CompleteTX(
      statusPage: statusPage,
      title: title,
      detail: detail,
      buttonText: buttonText,
      back: back);
}

class _CompleteTX extends State<CompleteTX> {
  _CompleteTX(
      {this.uid,
      this.phone,
      this.statusPage,
      this.title,
      this.detail,
      this.buttonText,
      this.back});
  final uid;
  final phone;
  final statusPage;
  final title;
  final detail;
  final buttonText;
  final back;
  late CallHttp http;
  bool _loading = false;
  @override
  void initState() {
    super.initState();
    http = OnCallHttp();
  }

  // callSumSub() async {
  //   setState(() => _loading = true);
  //   SumSubToken res = await http.getSumSubAccessToken(
  //       url: env.apiSumSub + 'create_token',
  //       jsonMap: {
  //         "uid": 'xQdzj7micUSVdiW9LTtmtiEmI9q1',
  //         "phone": "+66994894210"
  //       });
  //   launchSNSMobileSDK(token: res.token.toString())
  //       .then((value) => Navigator.of(context).pop());
  // }

  buildForPhone() {
    return ModalProgressHUD(
      inAsyncCall: _loading,
      opacity: 0.1,
      progressIndicator: Center(
        child: SpinKitFadingCircle(
          color: LikeWalletAppTheme.bule1,
          size: 200.h,
        ),
      ),
      child: Scaffold(
//      backgroundColor: Color(0xFFFFFFFF),
        body: Stack(
          alignment: Alignment.center,
          children: <Widget>[
            Positioned(
              top: mediaQuery(context, 'height', 390),
              child: new Container(
                alignment: Alignment.center,
                width: mediaQuery(context, 'height', 283),
                child: Image.asset(
                  statusPage == 'RED'
                      ? LikeWalletImage.network_error
                      : 'assets/image/complete.png',
                ),
              ),
            ),
            Positioned(
              top: mediaQuery(context, 'height', 733),
              child: new Container(
                alignment: Alignment.center,
                width: mediaQuery(context, 'height', 701),
                child: new Text(
                  title,
                  style: TextStyle(
                    letterSpacing: 0.3,
                    color: Color(0xff000000).withOpacity(1),
                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
                    fontWeight: FontWeight.bold,
                    fontSize: mediaQuery(context, 'height', 49),
                  ),
                ),
              ),
            ),
            Positioned(
              top: mediaQuery(context, 'height', 853),
              child: new Container(
                alignment: Alignment.center,
                width: mediaQuery(context, 'width', 800),
                child: new Text(
                  detail,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    letterSpacing: 0.3,
                    color: LikeWalletAppTheme.gray,
                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
                    fontWeight: FontWeight.normal,
                    fontSize: mediaQuery(context, 'height', 42),
                  ),
                ),
              ),
            ),
            statusPage == 'RED'
                ? Positioned(
                    top: mediaQuery(context, 'height', 1200),
                    child: GestureDetector(
                      // onTap: () => callSumSub(),
                      child: Container(
                        alignment: Alignment.center,

                        height: MediaQuery.of(context).size.height *
                            0.10854700854, // height of the button
                        width: MediaQuery.of(context).size.height *
                            0.10854700854, // width of the button
                        child: new Text(
                          AppLocalizations.of(context)!
                            .translate('kyc_reject_again'),
                          style: TextStyle(
                              color: Color(0xffB4E60D),
                              fontFamily: AppLocalizations.of(context)!
                            .translate('font1'),
                              fontWeight: FontWeight.normal,
                              fontSize: MediaQuery.of(context).size.height *
                                  0.018194017094),
                        ),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Color(0xff2B3038).withOpacity(1),
                          boxShadow: [
                            BoxShadow(
                              spreadRadius: 5,
                              blurRadius: 14,
                              color: Colors.black.withOpacity(0.16),
                              offset: Offset(
                                6.0,
                                8.0,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  )
                : Container(),
            Container(
              alignment: Alignment.bottomCenter,
              height: MediaQuery.of(context).size.height,
              child: NavigationBarWhite(),
            ),
          ],
        ),
      ),
    );
  }

  int tabMenu = 0;
  changeMenu(_number) async {
    setState(() {
      tabMenu = _number;
    });
  }

  @override
  Widget build(BuildContext context) {
    return new WillPopScope(
        onWillPop: () async => false, child: Scaffold(body: buildForPhone()));
  }
}
