import 'dart:io';
import 'package:likewallet/Theme.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:likewallet/bank/banking.dart';
import 'package:likewallet/screen/home.dart';
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/screen/navigationbar/contact_us.dart';
import 'package:likewallet/routes.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:likewallet/icon/icon_home_icons.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class NavigationBarWhite extends StatefulWidget {
  _NavigationBarWhite createState() => new _NavigationBarWhite();
}

class _NavigationBarWhite extends State<NavigationBarWhite> {
  int _selectPage = 0;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  int curIndex = 0;
  Widget build(BuildContext context) {
    return SizedBox(
      child: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          Container(
            height: 296.03.h,
            width: MediaQuery.of(context).size.width,
            color: Color(0xffFFFFFF),
          ),
          // Container(
          //   // color: Color(0xffF5F5F5),
          //   height: mediaQuery(context, 'height', 218.4),
          //   child: SvgPicture.string(
          //     '<svg viewBox="0.0 2121.0 1078.7 218.4" ><path transform="translate(1454.92, -1101.01)" d="M -376.*********** 3222.*********** L -944.921*********5 3222.0078125 L -944.921*********5 3222.01171875 L -959.7789916992188 3222.01171875 C -959.7789916992188 3222.01171875 -995.64599609375 3222.3935546875 -1024.869018554688 3230.3291015625 C -1051.8759765625 3236.529296875 -1067.806030273438 3248.9716796875 -1075.692993164062 3254.770263671875 L -1075.692993164062 3254.7666015625 C -1096.18798828125 3271.944091796875 -1132.874755859375 3292.77587890625 -1169.73974609375 3292.79443359375 C -1207.788818359375 3292.79443359375 -1239.385986328125 3271.07861328125 -1259.194946289062 3254.65380859375 C -1267.607055664062 3248.7021484375 -1283.41796875 3236.3935546875 -1310.423950195312 3230.193603515625 C -1334.906982421875 3224.57470703125 -1365.828979492188 3222.80078125 -1380.921********* 3222.*********** L -1380.921********* 3222.0078125 L -1454.*********** 3222.*********** L -1454.*********** 3440.*********** L -376.*********** 3440.*********** L -376.*********** 3222.*********** Z" fill="#ffffff" stroke="none" stroke-width="1" stroke-miterlimit="10" stroke-linecap="butt" /></svg>',
          //     allowDrawingOutsideViewBox: true,
          //   ),
          // ),
          // Positioned(
          //   top: 0,
          //   left: 256.3.w,
          //   child: Image.asset(
          //     LikeWalletImage.button_banking,
          //     height: 103.93.h,
          //   ),
          // ),
          Positioned(
            top: 50.h,
            width: MediaQuery.of(context).size.width,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 135.w),
              margin: EdgeInsets.only(top: 55.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  InkWell(
                    onTap: () {
                      AppRoutes.makeFirst(
                          context, HomeLikewallet(selectPage: 0));
                    },
                    child: Icon(IconHome.path_43609,
                        size: mediaQuery(context, 'height', 58),
                        color: Color(0xffB3B3B4)),
                  ),
                  InkWell(
                    onTap: () {
                      AppRoutes.makeFirst(
                          context, HomeLikewallet(selectPage: 1));
                    },
                    child: Icon(IconHome.path_43608,
                        size: mediaQuery(context, 'height', 58),
                        color: Color(0xffB3B3B4)),
                  ),
                  InkWell(
                    onTap: () {
                      AppRoutes.makeFirst(
                          context, HomeLikewallet(selectPage: 2));
                    },
                    child: Icon(IconHome.group_24548,
                        size: mediaQuery(context, 'height', 58),
                        color: Color(0xffB3B3B4)),
                  ),
                  InkWell(
                    onTap: () {
                      AppRoutes.makeFirst(
                          context, HomeLikewallet(selectPage: 3));
                    },
                    child: Icon(IconHome.path_58781,
                        size: mediaQuery(context, 'height', 58),
                        color: Color(0xffB3B3B4)),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
