import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'dart:ui' as ui;
import 'package:likewallet/libraryman/app_local.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:likewallet/libraryman/app_local.dart';

class LoadingSendLike extends StatefulWidget {
  // ignore: prefer_const_constructors_in_immutables
  LoadingSendLike();

  @override
  _LoadingSendLike createState() => _LoadingSendLike();
}

class _LoadingSendLike extends State<LoadingSendLike>
    with SingleTickerProviderStateMixin {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Column(
        children: [
          Expanded(
            child: Container(
              height: 875.h,
              alignment: Alignment.center,
              child: Stack(
                alignment: Alignment.topCenter,
                children: <Widget>[
                  loadingHeader(),
                  loadingBody(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget loadingHeader() => Container(
        height: 764.h,
        width: 730.w,
        margin: EdgeInsets.only(top: 111.h),
        padding: EdgeInsets.only(top: 111.h),
        alignment: Alignment.center,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(50.0.h),
          gradient: LinearGradient(
            begin: Alignment(-1.0, 1.0),
            end: Alignment(1.0, -1.0),
            colors: [const Color(0xf230f3ca), const Color(0xf224c6e4)],
            stops: [0.0, 1.0],
          ),
          // boxShadow: [
          //   BoxShadow(
          //     color: const Color(0x27000000),
          //     offset: Offset(0, 45.h),
          //     blurRadius: 65.h,
          //   ),
          // ],
        ),
        child: Text.rich(
          TextSpan(
            style: TextStyle(
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              fontSize: 41.h,
              color: const Color(0xffffffff),
              letterSpacing: 2.1.w,
            ),
            children: [
              TextSpan(
                text: AppLocalizations.of(context)!
                    .translate('loading_send_text1'),
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                ),
              ),
              TextSpan(
                text: AppLocalizations.of(context)!
                    .translate('loading_send_text2'),
              ),
            ],
          ),
          textHeightBehavior:
              TextHeightBehavior(applyHeightToFirstAscent: false),
          textAlign: TextAlign.center,
        ),
      );

  Widget loadingBody() => Container(
        width: 554.0.w,
        height: 221.0.h,
        child: ClipRect(
          child: BackdropFilter(
            filter: ui.ImageFilter.blur(sigmaX: 30.0.h, sigmaY: 30.0.h),
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.all(
                  Radius.circular(111),
                ),
                color: Colors.white.withOpacity(0.5),
              ),
              child: SpinKitFadingCircle(
                color: Colors.black,
                size: 124.29.h,
              ),
            ),
          ),
        ),
      );
}
