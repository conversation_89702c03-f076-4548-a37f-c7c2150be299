import 'package:cached_network_image/cached_network_image.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:date_format/date_format.dart';
import 'package:delayed_display/delayed_display.dart';
// import 'package:esys_flutter_share/esys_flutter_share.dart';
import 'package:flare_flutter/flare_actor.dart';

import 'package:flutter/cupertino.dart';
import 'dart:async';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

import 'package:image_picker/image_picker.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:likewallet/libraryman/custom_loading.dart';

import 'package:likewallet/screen_util.dart';
import 'dart:convert';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'package:flutter/services.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:likewallet/middleware/get_mnemonic.dart';
import 'package:likewallet/libraryman//ethcontract.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:likewallet/middleware/check_auth.dart';
import 'package:share/share.dart';
//import 'package:permission/permission.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:likewallet/libraryman/configuration_service.dart';
import 'package:likewallet/libraryman/address_service.dart';
import 'package:intl/intl.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:intl/intl.dart' as formatIntl;

import 'package:http/http.dart' as http;
import 'package:likewallet/app_config.dart';
import 'dart:convert';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:likewallet/Theme.dart';

const directoryName = 'receiptLike';

class billTransection extends StatefulWidget {
  billTransection(
      {required this.addressText,
      required this.amountSend,
      this.noteMessage,
      required this.titleName,
      required this.rateCurrency,
      required this.contract,
      required this.fromName,
      required this.destName,
      required this.isVending});

//  billTransection();
  final String amountSend;
  final String addressText;
  final String? noteMessage;
  final String titleName;
  final double rateCurrency;
  final String contract;
  final String fromName;
  final String destName;
  final bool isVending;

  _billTransection createState() => new _billTransection(
      nameTO: addressText,
      amount: amountSend,
      noteMessage: noteMessage,
      titleName: titleName,
      rateCurrency: rateCurrency,
      contract: contract,
      fromName: fromName,
      destName: destName,
      isVending: isVending);
}

class _billTransection extends State<billTransection>
    with WidgetsBindingObserver, TickerProviderStateMixin {
  _billTransection(
      {required this.nameTO,
      required this.amount,
      this.noteMessage,
      required this.titleName,
      required this.rateCurrency,
      required this.contract,
      required this.fromName,
      required this.destName,
      required this.isVending});

//  _billTransection();
  final String amount;
  late String nameTO;
  final String? noteMessage;
  final String titleName;
  final double rateCurrency;
  final String contract;
  final String fromName;
  final String destName;
  final format = new DateFormat('dd-MM-yyyy HH:MM');
  final bool isVending;

  late String ShowDate = 'null';
  late String ShowMonth = 'null';
  late String ShowYear = 'null';
  String timethai = 'เวลา';
  late String time = 'no';
  late String timeA = 'no';

  final f = new formatIntl.NumberFormat("###,###.##");

  double rate = 100;
  late CheckAuth Logon;
  late GetMnemonic seed;
  late BaseETH eth;
  late String mnemonic;
  late String pketh;
  GlobalKey _globalKey = new GlobalKey();
  bool inside = false;
  late Uint8List imageInMemory;
  late IAddressService addressService;
  late IConfigurationService configETH;
  late String formattedDate;
  late FocusNode myFocusNode;
//  TextEditingController addressText = TextEditingController();
  final TextEditingController nameFavorite = TextEditingController();
  TextEditingController addressFavorite = TextEditingController();
  late String photoFavorite;
  late File? _imageFavorite;

  final fireStore = FirebaseFirestore.instance;

  bool _saveSlip = true;

  final ImagePicker _picker = ImagePicker();

  bool _saving = false;
  late String uid;
  late BaseAuth FirebaseAuth;
  final formKey = GlobalKey<FormState>();
  bool _autoValidate = false;
  String image = 'assets/image/receive/receipt.png';

  @override
  void initState() {
    super.initState();
    myFocusNode = FocusNode();
    Logon = new CurCheckAuth();
    seed = new MnemonicRetrieve();
    eth = new EthContract();
    convertDate();
    FirebaseAuth = Auth();
    setState(() {
      addressFavorite.text = nameTO;
    });
    print('Message ' + noteMessage!);
    getFavorites();
    setInit();
  }

  convertDate() async {
    DateTime now = DateTime.now();

    formattedDate = DateFormat('dd-MM-yyyy – kk:mm').format(now);
    print(formattedDate);
    var month = formattedDate.substring(3, 5);
    var year = formattedDate.substring(6, 10);
    var day = formattedDate.substring(0, 2);
    print(month);
    print(year);
    print(day);
    var count = int.parse(month);
    List<String> mount12_th = [
      'มกราคม',
      'กุมภาพันธ์',
      'มีนาคม',
      'เมษายน',
      'พฤษภาคม',
      'มิถุนายน',
      'กรกฎาคม',
      'สิงหาคม',
      'กันยายน',
      'ตุลาคม',
      'พฤศจิกายน',
      'ธันวาคม',
    ];
    List<String> mount12_en = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    var prefs = await SharedPreferences.getInstance();
    var language_code = await prefs.getString('language_code');
    print(language_code);
    if (language_code == 'th') {
      setState(() {
        ShowDate = day;
        ShowMonth = mount12_th[count - 1];
        ShowYear = (int.parse(year) + 543).toString();
        timethai = 'เวลา';
        formattedDate = DateFormat('dd-MM-yyyy – kk:mm').format(now);
        timeA = formattedDate.substring(13, 18) + " น.";
      });
    } else {
      setState(() {
        ShowDate = day;
        ShowMonth = mount12_en[count - 1];
        timethai = '';
        ShowYear = year;
        formattedDate = DateFormat('hh:mm a').format(now);
        timeA = formattedDate.substring(0, 8);
      });
    }
  }

  setInit() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    setState(() {
      image = pref.getString('receipt') ?? '';
      print("image $image");
    });
    configETH = new ConfigurationService(pref);
    addressService = new AddressService(configETH);
    String address = configETH.getAddress();
    setState(() {
      nameFrom = address;
    });
    bool slipAuto = pref.getBool('saveSlip') ?? true;
    if (image != '') {
      if (slipAuto) {
        Future.delayed(const Duration(seconds: 5), () {
          _capturePng();
        });
      }
    }
  }

  buildForPhone(Orientation orientation) {
    return Scaffold(
      backgroundColor: Color(0xFFFFFFFF),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          InkWell(
            onTap: () {
              Navigator.pushReplacementNamed(context, '/bank');
              // Navigator.popAndPushNamed(context, '/home');
            },
            child: Container(
              alignment: Alignment.bottomCenter,
              width: MediaQuery.of(context).size.width,
              height: 256.0.h,
              padding: EdgeInsets.only(bottom: 47.h),
              decoration: BoxDecoration(
                color: const Color(0xff141322),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(
                    LikeWalletImage.button_back,
                    height: mediaQuery(context, 'height', 40.4),
                    color: Colors.white,
                  ),
                  SizedBox(
                    width: 20.w,
                  ),
                  Text(
                    AppLocalizations.of(context)!.translate('bill_back'),
                    style: TextStyle(
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      fontSize: 39.h,
                      color: const Color(0x99ffffff),
                      letterSpacing: 3.9000000000000004.w,
                    ),
                    textAlign: TextAlign.left,
                  ),
                ],
              ),
            ),
          ),
          SizedBox(
            height: 48.h,
          ),
          DelayedDisplay(
            slidingBeginOffset: const Offset(0.0, 0.0),
            delay: Duration(milliseconds: 1300),
            fadingDuration: const Duration(milliseconds: 500),
            child: Container(
              // width: 800.0.w,
              height: 1443.0.h,
              decoration: BoxDecoration(
                // color: Colors.red,
                boxShadow: [
                  BoxShadow(
                    color: const Color(0x29000000),
                    offset: Offset(12.w, 35.h),
                    blurRadius: 45.h,
                  ),
                ],
                // color: Colors.white,
                borderRadius: BorderRadius.circular(21.0.w),
              ),
              child: coverReceipt(),
              // child: RepaintBoundary(
              //     key: _globalKey,
              //     child: Stack(
              //       children: [
              //         image == 'null'
              //             ? Container()
              //             : CachedNetworkImage(
              //                 height: 1443.0.h,
              //                 placeholder: (context, url) => Container(),
              //                 // placeholder: (context, url) => Container(
              //                 //   height: 1443.0.h,
              //                 //   child:
              //                 // ),
              //                 fit: BoxFit.fitWidth,
              //                 imageUrl: image,
              //               ),
              //         image == ''
              //             ? Container()
              //             : Positioned(
              //                 top: 20.h,
              //                 left: 100.w,
              //                 child: DelayedDisplay(
              //                   slidingBeginOffset: const Offset(0.0, 0.0),
              //                   fadingDuration:
              //                       const Duration(milliseconds: 800),
              //                   child: receipt(context),
              //                 ),
              //               ),
              //       ],
              //     )),
            ),
          ),
          SizedBox(
            height: 51.h,
          ),
          controlButton(context),
        ],
      ),
    );
  }

  Widget coverReceipt() {
    return Column(
      children: [
        RepaintBoundary(
          key: _globalKey,
          child: Container(
            height: 1443.sp,
            width: 961.sp,
            decoration: BoxDecoration(
                image: DecorationImage(
                    image: AssetImage(
              'assets/image/receive/receipt.png',
            ))),
            child: Padding(
              padding: EdgeInsets.only(left: 140.sp),
              child: receipt(context),
            ),
          ),
        ),
      ],
    );
  }

  buildForTablet(Orientation orientation) {
    return Scaffold();
  }

  String nameFrom = '00000000';
  var fee = '0 LIKE';

  @override
  Widget build(BuildContext context) {
    final double shortestSide = MediaQuery.of(context).size.shortestSide;
    final bool useMobileLayout = shortestSide < 600.0;
    final Orientation orientation = MediaQuery.of(context).orientation;
    return ModalProgressHUD(
      opacity: 0.3,
      child: Scaffold(
        body: useMobileLayout
            ? buildForPhone(orientation)
            : buildForPhone(orientation),
      ),
      inAsyncCall: _saving,
      progressIndicator: CustomLoading(),
    );
  }

  receipt(context) {
    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: mediaQuery(context, 'height', 131),
          ),
          Container(
            child: new Text(
              AppLocalizations.of(context)!.translate('receipt_from'),
              style: TextStyle(
                  letterSpacing: 1,
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  color: Colors.black,
                  fontSize: mediaQuery(context, 'height', 36),
                  fontWeight: FontWeight.normal),
            ),
          ),
          Container(
            child: new Text(
              fromName != 'no'
                  ? fromName
                  : nameFrom.substring(0, 5) +
                      '...' +
                      nameFrom.substring(nameFrom.length - 5),
              style: TextStyle(
                  color: Colors.white,
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  fontWeight: FontWeight.normal,
                  fontSize: mediaQuery(context, 'height', 50)),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          SizedBox(
            height: mediaQuery(context, 'height', 54),
          ),
          Container(
            // width: mediaQuery(context, 'width', 190),
            child: new Text(
              AppLocalizations.of(context)!.translate('receipt_to'),
              style: TextStyle(
                  letterSpacing: 1,
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  color: Colors.black,
                  fontSize: mediaQuery(context, 'height', 39),
                  fontWeight: FontWeight.normal),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Container(
            child: Text(
              destName != 'no'
                  ? destName
                  : titleName.substring(0, 1) != '0'
                      ? titleName
                      : nameTO.substring(0, 5) +
                          '...' +
                          nameTO.substring(nameTO.length - 5),
              style: TextStyle(
                  color: Colors.white,
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  fontWeight: FontWeight.normal,
                  fontSize: mediaQuery(context, 'height', 50)),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          SizedBox(
            height: mediaQuery(context, 'height', 54),
          ),
          Container(
            // width: mediaQuery(context, 'width', 190),
            child: new Text(
              AppLocalizations.of(context)!.translate('receipt_amount'),
              style: TextStyle(
                  letterSpacing: 1,
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  color: Colors.black,
                  fontSize: mediaQuery(context, 'height', 39),
                  fontWeight: FontWeight.normal),
            ),
          ),
          Container(
            child: isVending == true
                ? Text(
                    f.format(double.parse(amount)) + ' LIKE',
                    style: TextStyle(
                        color: Colors.white,
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                        fontWeight: FontWeight.normal,
                        fontSize: mediaQuery(context, 'height', 50)),
                  )
                : Text(
                    f.format(double.parse(amount) * rateCurrency * rate) +
                        ' LIKE',
                    style: TextStyle(
                        color: Colors.white,
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                        fontWeight: FontWeight.normal,
                        fontSize: mediaQuery(context, 'height', 50)),
                  ),
          ),
          SizedBox(
            height: mediaQuery(context, 'height', 54),
          ),
          Container(
            // width: mediaQuery(context, 'width', 190),
            child: new Text(
              AppLocalizations.of(context)!.translate('receipt_fee'),
              style: TextStyle(
                  letterSpacing: 1,
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  color: Colors.black,
                  fontSize: mediaQuery(context, 'height', 39),
                  fontWeight: FontWeight.normal),
            ),
          ),
          Container(
            child: Text(
              fee,
              style: TextStyle(
                  color: Colors.white,
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  fontWeight: FontWeight.normal,
                  fontSize: mediaQuery(context, 'height', 50)),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          SizedBox(
            height: mediaQuery(context, 'height', 54),
          ),
          Container(
            width: mediaQuery(context, 'width', 190),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                child: Text(
                  AppLocalizations.of(context)!.translate('receipt_note'),
                  style: TextStyle(
                    color: Colors.black,
                    fontFamily:
                        AppLocalizations.of(context)!.translate('font1'),
                    fontWeight: FontWeight.normal,
                    fontSize: mediaQuery(context, 'height', 39),
                  ),
                ),
              ),
              SizedBox(
                height: mediaQuery(context, 'height', 10),
              ),
              new Container(
                width: MediaQuery.of(context).size.width *
                    Screen_util("width", 558.72),
                child: Text(
                  noteMessage ?? '',
                  style: TextStyle(
                    color: Colors.white,
                    letterSpacing: 0.5,
                    fontFamily:
                        AppLocalizations.of(context)!.translate('font1'),
                    fontWeight: FontWeight.normal,
                    fontSize: MediaQuery.of(context).size.height *
                        Screen_util("height", 34),
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          Text(
            AppLocalizations.of(context)!.translate('receipt_title'),
            textAlign: TextAlign.start,
            style: TextStyle(
              color: Colors.black,
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              fontWeight: FontWeight.normal,
              fontSize: mediaQuery(context, 'height', 31),
            ),
          ),
          Text(
            ShowDate == null ? '' : ShowDate + " " + ShowMonth + " " + ShowYear,
            textAlign: TextAlign.start,
            style: TextStyle(
              color: Colors.black,
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              fontWeight: FontWeight.normal,
              fontSize: mediaQuery(context, 'height', 31),
            ),
          ),
          Text(
            timeA == '' ? '' : timeA,
            textAlign: TextAlign.start,
            style: TextStyle(
              color: Colors.black,
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              fontWeight: FontWeight.normal,
              fontSize: mediaQuery(context, 'height', 31),
            ),
          ),
        ],
      ),
    );
  }

  Widget controlButton(context) {
    return Container(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          //-----------button add favorite------------//
          contract == 'no'
              ? Column(
                  children: <Widget>[
                    new GestureDetector(
                      onTap: (() {
                        AddFavortieForm(context);
                        print(nameTO);
                        print(uid);
                        getFavorites();
                      }),
                      child: Container(
                        margin: EdgeInsets.only(
                            top: mediaQuery(context, 'height', 0)),
                        alignment: Alignment.center,
                        child: Image.asset(
                          'assets/image/favorite_white.png',
                          height: MediaQuery.of(context).size.height *
                              Screen_util("height", 119.7),
                        ),
                      ),
                    ),
                    new Container(
                      margin: EdgeInsets.only(
                          top: mediaQuery(context, 'height', 20)),
                      alignment: Alignment.center,
                      width: MediaQuery.of(context).size.width * 0.12,
                      child: new Text(
                        AppLocalizations.of(context)!
                            .translate('receipt_favorite'),
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Color(0xff707071),
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font1'),
                          fontWeight: FontWeight.normal,
                          fontSize: MediaQuery.of(context).size.height *
                              Screen_util("height", 36),
                        ),
                      ),
                    ),
                  ],
                )
              : Container(),

          contract == 'no'
              ? SizedBox(
                  width: MediaQuery.of(context).size.width * 0.06,
                )
              : Container(),
          //-----------button share------------//
          Column(children: <Widget>[
            GestureDetector(
              onTap: () async {
                try {
                  setState(() => _saving = true);
                  RenderRepaintBoundary boundary = _globalKey.currentContext!
                      .findRenderObject() as RenderRepaintBoundary;
                  ui.Image image = await boundary.toImage(pixelRatio: 5.0);
                  ByteData? byteData =
                      await image.toByteData(format: ui.ImageByteFormat.png);
                  Uint8List pngBytes = byteData!.buffer.asUint8List();
                  final tempDir = await getTemporaryDirectory();
                  final file =
                      await new File('${tempDir.path}/screenshot.png').create();
                  await file.writeAsBytes(pngBytes);
                  final RenderBox box = context.findRenderObject();
                  Share.shareFiles(['${tempDir.path}/screenshot.png'],
                      subject: 'Share ScreenShot',
                      // text: 'Hello, check your share files!',
                      sharePositionOrigin:
                          box.localToGlobal(Offset.zero) & box.size);
                  final channel =
                      const MethodChannel('channel:me.shopqr.share/share');
                  channel.invokeMethod('shareFile', 'image.png');
                  setState(() => _saving = false);
                } catch (e) {
                  setState(() => _saving = false);
                  print(e.toString());
                }
              },
              child: new Container(
                margin: EdgeInsets.only(top: mediaQuery(context, 'height', 0)),
                alignment: Alignment.topCenter,
                child: Image.asset(
                  'assets/image/Share_receipt.png',
                  height: MediaQuery.of(context).size.height *
                      Screen_util("height", 119.7),
                ),
              ),
            ),
            new Container(
              margin: EdgeInsets.only(top: mediaQuery(context, 'height', 20)),
              alignment: Alignment.center,
              width: MediaQuery.of(context).size.width * 0.1,
              child: new Text(
                AppLocalizations.of(context)!.translate('receipt_share'),
                style: TextStyle(
                  color: LikeWalletAppTheme.gray,
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  fontWeight: FontWeight.normal,
                  fontSize: MediaQuery.of(context).size.height *
                      Screen_util("height", 36),
                ),
              ),
            ),
          ]),
          SizedBox(
            width: MediaQuery.of(context).size.width * 0.06,
          ),
          //-----------button save------------//
          Column(children: <Widget>[
            GestureDetector(
              onTap: _capturePng,
              child: new Container(
                margin: EdgeInsets.only(top: mediaQuery(context, 'height', 0)),
                alignment: Alignment.topCenter,
                child: Image.asset(
                  'assets/image/Save_receipt.png',
                  height: MediaQuery.of(context).size.height *
                      Screen_util("height", 119.7),
                ),
              ),
            ),
            new Container(
              margin: EdgeInsets.only(top: mediaQuery(context, 'height', 20)),
              alignment: Alignment.center,
              width: MediaQuery.of(context).size.width * 0.1,
              child: new Text(
                AppLocalizations.of(context)!.translate('receipt_save'),
                style: TextStyle(
                  color: Color(0xff707071),
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  fontWeight: FontWeight.normal,
                  fontSize: MediaQuery.of(context).size.height *
                      Screen_util("height", 36),
                ),
              ),
            ),
          ]),
        ],
      ),
    );
  }

  Future<Uint8List> _capturePng() async {
    print('inside');
    setState(() {
      _saving = true;
      inside = true;
    });

    RenderRepaintBoundary boundary =
        _globalKey.currentContext!.findRenderObject() as RenderRepaintBoundary;
    ui.Image image = await boundary.toImage(pixelRatio: 5.0);
    ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    Uint8List pngBytes = byteData!.buffer.asUint8List();
    String bs64 = base64Encode(pngBytes);
//      print(pngBytes);
//      print(bs64);
//      if (!(await checkPermission())) await requestPermission();
    if (Platform.isAndroid) {
      bool statusPermision = await Permission.photos.isGranted;
      if (statusPermision != true) {
        await Permission.manageExternalStorage.request(); await Permission.photos.request(); 
      }
    } else {
      bool statusPermision = await Permission.photos.isGranted;
      if (statusPermision != true) {
        await Permission.manageExternalStorage.request(); await Permission.photos.request(); 
      }
    }

    // Use plugin [path_provider] to export image to storage
    final result = await ImageGallerySaver.saveImage(
        Uint8List.fromList(pngBytes.buffer.asInt8List()));
    print(result);
    print('png done');
    FadeSaveReceipt(context,
        AppLocalizations.of(context)!.translate('fade_save_receipt'), this);
//    _snackSample(context);
    setState(() {
      imageInMemory = pngBytes;
      inside = false;
      _saving = false;
    });

    return pngBytes;
  }

//  String formattedDate() {
//    DateTime dateTime = DateTime.now();
//    String dateTimeString = 'Signature_' +
//        dateTime.year.toString() +
//        dateTime.month.toString() +
//        dateTime.day.toString() +
//        dateTime.hour.toString() +
//        ':' +
//        dateTime.minute.toString() +
//        ':' +
//        dateTime.second.toString() +
//        ':' +
//        dateTime.millisecond.toString() +
//        ':' +
//        dateTime.microsecond.toString();
//    return dateTimeString;
//  }

  //from input favorite
  AddFavortieForm(BuildContext context) async {
    return showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
              backgroundColor: Colors.white.withOpacity(0.9),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(30.0),
              ),
//
//            title: Text('TextField in Dialog'),
              content: SingleChildScrollView(
                  child: Container(
                      padding: EdgeInsets.all(0),
                      height: MediaQuery.of(context).size.height *
                          Screen_util('height', 1194),
                      width: MediaQuery.of(context).size.width *
                          Screen_util('width', 934),
                      child: Form(
                        key: formKey,
                        // autovalidate: _autoValidate,
                        child: Column(children: <Widget>[
                          Row(children: <Widget>[
                            Container(
                              child: Image.asset(
                                'assets/image/favorite_white.png',
                                scale: 2.5,
                              ),
                            ),
                            Container(
                                child: new Text(
                              ' ' +
                                  AppLocalizations.of(context)!
                                      .translate('favorite_title'),
                              style: TextStyle(
                                  color: Color(0xff707071),
                                  fontFamily: AppLocalizations.of(context)!
                                      .translate('font1'),
                                  fontSize: MediaQuery.of(context).size.height *
                                      Screen_util('height', 33),
                                  fontWeight: FontWeight.normal),
                            )),
                          ]),
                          Container(
                              height: MediaQuery.of(context).size.height *
                                  Screen_util('height', 300),
                              width: MediaQuery.of(context).size.width *
                                  Screen_util('width', 300),
//                      color: Colors.cyanAccent,
                              padding: EdgeInsets.all(
                                  MediaQuery.of(context).size.height *
                                      Screen_util('height', 20)),
                              child: Stack(
                                children: <Widget>[
                                  Container(
                                      color: Colors.transparent,
                                      child: _imageFavorite != null
                                          ? new CircleAvatar(
                                              backgroundImage: new FileImage(
                                                  _imageFavorite!),
                                              radius: 150,
                                            )
                                          : new CircleAvatar(
                                              backgroundColor:
                                                  Colors.transparent,
                                              backgroundImage: new AssetImage(
                                                  'assets/image/favorite_photo.png'),
                                              radius: 150,
                                            )),
                                  GestureDetector(
                                      onTap: () {
                                        preview();
                                      },
                                      child: Container(
                                        padding: EdgeInsets.only(
                                            right: MediaQuery.of(context)
                                                    .size
                                                    .width *
                                                Screen_util('width', 10),
                                            bottom: MediaQuery.of(context)
                                                    .size
                                                    .height *
                                                Screen_util('height', 10)),
                                        alignment: Alignment.bottomRight,
                                        child: Image.asset(
                                          'assets/image/edit_favorite_photo.png',
                                          scale: 3.5,
                                        ),
                                      )),
                                ],
                              )),
                          Container(
                            padding: EdgeInsets.all(
                                MediaQuery.of(context).size.height *
                                    Screen_util('height', 20)),
                            child: TextFormField(
                              controller: nameFavorite,
                              focusNode: myFocusNode,
                              style: TextStyle(
                                  color: Color(0xff707071),
                                  fontFamily: AppLocalizations.of(context)!
                                      .translate('font1'),
                                  fontSize: MediaQuery.of(context).size.height *
                                      Screen_util('height', 34),
                                  fontWeight: FontWeight.normal),
                              validator: (val) => val!.length < 1
                                  ? 'กรุณาใส่ชื่อรายการโปรด'
                                  : null,
                              decoration: InputDecoration(
                                labelText: AppLocalizations.of(context)!
                                    .translate('favorite_name'),
                                labelStyle: TextStyle(
                                    color: Color(0xff707071),
                                    fontFamily: AppLocalizations.of(context)!
                                        .translate('font1'),
                                    fontSize:
                                        MediaQuery.of(context).size.height *
                                            Screen_util('height', 34),
                                    fontWeight: FontWeight.normal),
                              ),
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.all(
                                MediaQuery.of(context).size.height *
                                    Screen_util('height', 20)),
                            child: TextFormField(
                              controller: addressFavorite,
                              style: TextStyle(
                                  color: Color(0xff707071),
                                  fontFamily: AppLocalizations.of(context)!
                                      .translate('font1'),
                                  fontSize: MediaQuery.of(context).size.height *
                                      Screen_util('height', 32),
                                  fontWeight: FontWeight.normal),
                              readOnly: true,
                              decoration: InputDecoration(
                                contentPadding:
                                    const EdgeInsets.fromLTRB(0, 20, 0, 0),
                                labelText: AppLocalizations.of(context)!
                                    .translate('favorite_address'),
                                labelStyle: TextStyle(
                                    color: Color(0xff707071),
                                    fontFamily: AppLocalizations.of(context)!
                                        .translate('font1'),
                                    fontSize:
                                        MediaQuery.of(context).size.height *
                                            Screen_util('height', 34),
                                    fontWeight: FontWeight.normal),
                              ),
                            ),
                          ),
                          SizedBox(
                            height: MediaQuery.of(context).size.height *
                                Screen_util("height", 50),
                          ),
                          GestureDetector(
                              onTap: () {
//                                print(_imageFavorite.path);
                                _validateInputs(_imageFavorite);
                              },
                              child: Container(
                                  alignment: Alignment.center,
                                  height: MediaQuery.of(context).size.height *
                                      Screen_util("height", 223),
                                  width: MediaQuery.of(context).size.height *
                                      Screen_util("height", 223),
                                  decoration: new BoxDecoration(
                                    color: Color(0xff2B3038),
                                    shape: BoxShape.circle,
                                  ),
                                  child: Stack(
                                    alignment: Alignment.center,
                                    children: <Widget>[
                                      Text(
                                          AppLocalizations.of(context)!
                                              .translate('favorite_ok'),
//                        textAlign: TextAlign.center,
                                          style: TextStyle(
                                            fontFamily:
                                                AppLocalizations.of(context)!
                                                    .translate('font1'),
                                            color: Color(0xffB4E60D),
                                            fontWeight: FontWeight.bold,
                                            fontSize: MediaQuery.of(context)
                                                    .size
                                                    .height *
                                                Screen_util("height", 35),
                                          ))
                                    ],
                                  )))
                        ]),
                      ))));
        });
  }

  void _validateInputs(_imageFavorite) {
    if (formKey.currentState!.validate()) {
//    If all data are correct then save data to out variables
      if (_imageFavorite != null) {
        _saving = true;
        upload(_imageFavorite);
        Navigator.of(context).pop();
      } else {
        _saving = true;
        Navigator.of(context).pop();
        addFavorite();
      }
      formKey.currentState!.save;
    } else {
//    If all data are not valid then start auto validation.
      setState(() {
        _autoValidate = true;
      });
    }
  }

  upload(File file) async {
    if (file == null) return ['false'];
    String base64Image = base64Encode(file.readAsBytesSync());
    String fileName = file.path.split("/").last;
    //man disable
    var url = Uri.https(env.apiCheck, '/uploadBinary');
    final response = await http.post(url, body: {
      "image": base64Image,
      "name": fileName,
    });
    print(response.statusCode);
    final body = json.decode(response.body);

    print(body["result"]);
    print(body["result"]["id"]);
    print(body["result"]["url"]);
    setState(() {
      photoFavorite = body["result"]["url"];
    });
    await addFavorite();
//    return [body["result"]["url"]];
  }

// show photo
  Future preview() async {
    var image = await _picker.getImage(source: ImageSource.gallery);

    setState(() {
      _imageFavorite = File(image!.path);
    });
    print(_imageFavorite!.path);
    myFocusNode.requestFocus();
//    Navigator.of(context).pop();

//    await AddFavortieForm(context);
  }

//get uid
  getFavorites() async {
    //เรียก API รายการโปรด
    FirebaseAuth.getCurrentUser().then((decodeToken) {
      // print(decodeToken);
      setState(() {
        uid = decodeToken!.uid;
      });
      print(uid);
    });
  }

//firebase add favorite
  Future addFavorite() async {
    fireStore
        .collection('favorites')
        .doc(uid)
        .collection('transfer')
        .doc()
        .set({
      'nameFavorite': nameFavorite.text,
      'addressFavorite': addressFavorite.text,
      'photoFavorite': photoFavorite.toString(),
    });
    nameFavorite.clear();
    addressFavorite.clear();
    // _imageFavorite = null;
    _saving = false;
    _SuccessAddFavorite(context);
    Future.delayed(const Duration(milliseconds: 3000), () {
      Navigator.of(context).pop();
    });
  }

  Widget? _SuccessAddFavorite(context) {
    Dialog simpleDialog = Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      backgroundColor: Colors.transparent,
      elevation: 300,
      child: Container(
          color: Colors.transparent,
          height: 150,
          width: 150,
          child: FlareActor(
            "assets/animation/success.flr",
            alignment: Alignment.center,
            fit: BoxFit.contain,
            animation: "Untitled",
          )),
    );
    showDialog(
        context: context, builder: (BuildContext context) => simpleDialog);
  }

  Widget? _snackSample(context) {
    ScaffoldMessenger.of(context).showSnackBar(new SnackBar(
      content: const Text('Save receipt success!'),
      backgroundColor: Colors.lightGreen,
    ));
  }

//  requestPermission() async {
//    final res = await Permission.getPermissionsStatus([PermissionName.Calendar, PermissionName.Camera, PermissionName.Storage]);
//
//    print("permission request result is " + res.toString());
//  }
//
//  checkPermission() async {
//    final result = await Permission.getPermissionsStatus([PermissionName.Storage]);
//
//    return result;
//  }
//
//  getPermissionStatus() async {
//
//  }
}
