// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
//
// import 'package:likewallet/screen/NavigationBar.dart';
//
// import 'package:likewallet/bank/confirmCash.dart';
// import 'package:pattern_formatter/pattern_formatter.dart';
//
// class trueMoney extends StatefulWidget {
//   trueMoney({required this.rate, required this.amount, required this.fee});
//   final double rate;
//   final double amount;
//   final double fee;
//   _trueMoney createState() => new _trueMoney(rate: rate, amount: amount, fee: fee);
// }
//
// class _trueMoney extends State<trueMoney> {
//   _trueMoney({required this.rate, required this.amount, required this.fee});
//   final double rate;
//   final double amount;
//   final double fee;
//   String bank = 'truemoney';
//   buildForPhone(Orientation orientation) {
//     return Scaffold(
//       bottomNavigationBar: NavigationBar(),
//       backgroundColor: Color(0xFFFFFFFF),
//       body: Stack(
//         alignment: Alignment.center,
//         children: <Widget>[
//           Positioned(
//             top: MediaQuery.of(context).size.height * 0.1,
//             left: MediaQuery.of(context).size.width * 0.025,
//             right: MediaQuery.of(context).size.width * 0.025,
//             child: new Container(
//                 alignment: Alignment.center,
//                 child:  Image.asset('assets/image/truemoneywallet.png',scale: 2.5,)),
//           ),
//           Positioned(
//             top: MediaQuery.of(context).size.height * 0.35,
//             left: MediaQuery.of(context).size.width * 0.025,
//             right: MediaQuery.of(context).size.width * 0.025,
//             child: new Container(
//               alignment: Alignment.center,
//               child:  new Container(
// //              color:Colors.blue,
//                 height: MediaQuery.of(context).size.height * 0.07,
//                 width: MediaQuery.of(context).size.width * 0.9,
//                 alignment: Alignment.center,
//                 child: new Container(
//                   decoration: BoxDecoration(
//                     border: Border.all(width:0.5,color: Color(0xff707071) ),
//                     borderRadius: new BorderRadius.circular(10.0),
//                     color: Colors.white,
//                     boxShadow: [
//                       BoxShadow(
//                         spreadRadius: 0,
//                         blurRadius: 2,
//                         color: Color(0xff707071).withOpacity(0.5),
//                         offset: Offset(
//                           0.0,
//                           1.0,
//                         ),
//                       ),
//                     ],
//                     //shape: BoxShape.rectangle,
//                     //                  border: Border.all(10.0),
//                   ),
//                   alignment: Alignment.centerLeft,
//                   padding: EdgeInsets.only(
//                       left: MediaQuery.of(context).size.width * 0.0),
//                   height: MediaQuery.of(context).size.height * 0.07,
//                   width: MediaQuery.of(context).size.width * 0.9,
//                   child: TextFormField(
//                     onFieldSubmitted: (term){
//
// //                      Navigator.pushNamed(context, '/confirmCash');
//                       Navigator.push(
//                         context,
//                         MaterialPageRoute(builder: (context) => ConfirmCash(
//                             amount: amount,
//                             fee: fee,
//                             rate: rate,
//                             accountNumber: term,
//                             typePay: bank,
//                             totalSell: 0.0,
//                             symbol: '',
//                             nameAccount: '',
//                         )),
//                       );
//                     },
//                     keyboardType: TextInputType.number,
//                     decoration: InputDecoration(
//                       enabledBorder: OutlineInputBorder(
//                           borderSide:
//                           BorderSide(width: 0.1, color: Color(0xff707071).withOpacity(0.0))),
//                       border: OutlineInputBorder(
//                           borderRadius: BorderRadius.circular(10.0)),
//                       hintText: 'Enter payee mobile number',
//                       prefix: Container(
//                         width: MediaQuery.of(context).size.height * 0.0,
//                         height: MediaQuery.of(context).size.height * 0.03,
//                         margin: const EdgeInsets.only(right: 3.0),
//                         padding: const EdgeInsets.only(top: 2.0),
//                       ),),),),),),),
//
//
//         ],
//       ),
//     );
//   }
//
//   buildForTablet(Orientation orientation) {
//     return Scaffold();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     final double shortestSide = MediaQuery.of(context).size.shortestSide;
//     final bool useMobileLayout = shortestSide < 600.0;
//     final Orientation orientation = MediaQuery.of(context).orientation;
//     return Scaffold(
//       body: useMobileLayout
//           ? buildForPhone(orientation)
//           : buildForTablet(orientation),
//     );
//   }
//
// }
