import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:likewallet/bank/completeTranslate.dart';
import 'package:likewallet/libraryman/configuration_service.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:likewallet/libraryman/serviceHTTP.dart';
import 'package:likewallet/screen/home.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ThaiQR extends StatefulWidget {
  ThaiQR({required this.rate, required this.amount, required this.fee});
  final double rate;
  final double amount;
  final double fee;
  _ThaiQR createState() =>
      new _ThaiQR(rate: rate, amount: amount, fee: fee);
}

class _ThaiQR extends State<ThaiQR> with TickerProviderStateMixin {
  _ThaiQR({required this.rate, required this.amount, required this.fee});
  final double rate;
  final double amount;
  final double fee;
  bool _saving = false;
  GlobalKey<ScaffoldState> _scaffoldKey = new GlobalKey();
  GlobalKey globalKey = new GlobalKey();
  String _dataString = "";
  late AbstractServiceHTTP callAPI;
  int keyword = 1;
  String bank = "";
  late IConfigurationService configETH;
  late BaseAuth auth;
  late AnimationController controller;
  late Timer _timer;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    setState(() {
      controller = AnimationController(
        vsync: this,
        duration: Duration(seconds: 580),
      );
    });

    setState(() {
      _saving = true;
    });
    auth = Auth();
    callAPI = ServiceHTTP();
    createOrder();

  }
  @override
  void dispose() {
    controller.dispose();
    super.dispose();
    _timer.cancel();
  }

  String get timerString {
    Duration duration = controller.duration! * controller.value;
    return '${(duration.inHours % 60).toString().padLeft(2, '0')}:${(duration.inMinutes % 60).toString().padLeft(2, '0')}:${(duration.inSeconds % 60).toString().padLeft(2, '0')}';
  }

  Future createOrder() async {
    SharedPreferences pref = await SharedPreferences.getInstance();

    configETH = new ConfigurationService(pref);

    auth.getTokenFirebase().then((_token) {
//      print(_token);
    callAPI.createOrderPromptpay(_token, '0', configETH.getAddress(), amount/rate).then((qrData) {

      if(qrData != null){
        setState(() {
          _dataString = qrData[0].qrcode;
          print(qrData[0].qrcode);
        });
        if (controller.isAnimating)
          controller.stop();
        else {
          controller.reverse(
              from: controller.value == 0.0 ? 1.0 : controller.value);
        }

        // runs every 1 second
        _timer = new Timer.periodic(
            new Duration(seconds: 5), (Timer timer)  {
              callAPI.penddingPromptpayBuy(
                  merchantId: qrData[0].merchantId,
                  origPartnerTxnUid: qrData[0].QRpartnerTxnUid,
                  partnerTxnUid: qrData[0].partnerTxnUid,
                  qrType: qrData[0].qrType,
                  requestDt: qrData[0].requestDt,
                  terminalId: qrData[0].terminalId,
                  token: _token
              ).then((paid){
//                print(paid);
                if(paid == 'PAID')
                {
                  timer.cancel();
                  setState(() {
                    //successes paid
                  });
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => CompleteTranslate(
                          title: 'promotpay_buy',
                        back: HomeLikewallet(),
                        buttonText: 'promtpay_thank',
                        detail: 'promptpay_buy_success',
                      )
                    ),
                  );
                }else {
                  print(paid);
                }

              });
        });

        setState(() {
          _saving = false;
        });

      }
    });
    });
  }
  buildForPhone(Orientation orientation) {
    return Scaffold(
//      bottomNavigationBar: NavigationBar(),
      backgroundColor: Color(0xFFFFFFFF),
      body: Stack(
//        alignment: Alignment.center,
        children: <Widget>[
          Column(
            children: <Widget>[
              Container(
                color: Color(0xff141322),
                height: MediaQuery.of(context).size.height * 0.10945299145,
              ),
              Expanded(
                child: Container(
                  height: MediaQuery.of(context).size.height,
                  decoration: BoxDecoration(
                    color: Color(0xffFFFFFF),
                    boxShadow: [
                      BoxShadow(
                        spreadRadius: 0,
                        blurRadius: 4,
                        color: Color(0xff707071).withOpacity(0.5),
                        offset: Offset(
                          0.0,
                          2.0,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          Positioned(
            top: MediaQuery.of(context).size.height * 0.08034188034,
            child: Container(
              alignment: Alignment.center,
              width: MediaQuery.of(context).size.width,
              child: new Text(
                'Promptpay',
                style: TextStyle(
                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
                    color: Color(0xffFFFFFF).withOpacity(1),
                    fontSize:
                    MediaQuery.of(context).size.height * 0.01794871794,
                    fontWeight: FontWeight.bold),
              ),
            ),
          ),
          Positioned(
            top: MediaQuery.of(context).size.height * 0.08608547008,
            child: GestureDetector(
              onTap: () => {Navigator.of(context).pop()},
              child: Container(
                decoration: BoxDecoration(
                  color: Color(0xffB4E60D),
                  borderRadius: new BorderRadius.only(
                      bottomRight: Radius.circular(40.0),
                      topRight: Radius.circular(40.0)),
                ),
                height: MediaQuery.of(context).size.height * 0.04797863247,
                width: MediaQuery.of(context).size.width * 0.18317592592,
                child: Icon(
                  Icons.arrow_back_ios,
//                        color: Colors.blue,
                  size: MediaQuery.of(context).size.height * 0.0190042735,
                ),
              ),
            ),
          ),
          Positioned(

            left: MediaQuery.of(context).size.width *0.25,
            child: new Container(
//              color:Colors.blue,
//              height: MediaQuery.of(context).size.height * 0.1,
//              width: MediaQuery.of(context).size.height * 0.2,
              height:  MediaQuery.of(context).size.height ,
              width: MediaQuery.of(context).size.width,
              alignment: Alignment.center,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: <Widget>[
                  new Container(
                    margin: EdgeInsets.all(
                        MediaQuery.of(context).size.height * 0.00),
//                    alignment: Alignment.center,
//                    height: MediaQuery.of(context).size.height * 0.2,
//                    width: MediaQuery.of(context).size.width * 0.2,
                    width: MediaQuery.of(context).size.width,
                    child: RepaintBoundary(
                      key: globalKey,
                      child: QrImageView(
                        backgroundColor: Colors.white,
                        data: _dataString,
                        size: MediaQuery.of(context).size.height *
                            0.25308547008,

                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          Positioned(
            top: MediaQuery.of(context).size.height * 0.15,
            left: MediaQuery.of(context).size.width *0.02,
            child: new Container(
//              color:Colors.blue,
//              height: MediaQuery.of(context).size.height * 0.1,
//              width: MediaQuery.of(context).size.height * 0.2,
              height:  MediaQuery.of(context).size.height ,
              width: MediaQuery.of(context).size.width,
              alignment: Alignment.center,
              child:  AnimatedBuilder(
                animation: controller,
                builder: (context, child) {
                  return Text(
                    timerString,
                    textAlign: TextAlign.right,
                    style: TextStyle(
                        fontFamily:
                        AppLocalizations.of(context)!
                            .translate('font2'),
                        color: Colors.black,
                        fontSize: MediaQuery.of(context)
                            .size
                            .height *
                            0.02564102564,
                        fontWeight: FontWeight.normal),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  buildForTablet(Orientation orientation) {
    return Scaffold();
  }

  @override
  Widget build(BuildContext context) {
    final double shortestSide = MediaQuery.of(context).size.shortestSide;
    final bool useMobileLayout = shortestSide < 600.0;
    final Orientation orientation = MediaQuery.of(context).orientation;
    return ModalProgressHUD(
      opacity: 0.1,
      inAsyncCall: _saving,
      progressIndicator: CustomLoading(),
      child: Scaffold(
        body: useMobileLayout
            ? buildForPhone(orientation)
            : buildForPhone(orientation),
      ),
    );
  }
}
