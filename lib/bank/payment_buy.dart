import 'dart:convert';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:likewallet/animationPage.dart';
import 'package:likewallet/bank/completeTX.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:likewallet/screen/home.dart';
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:likewallet/libraryman/keyboard_done_widget.dart';
import 'dart:io' show Platform;
import 'package:likewallet/app_config.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:likewallet/libraryman/auth.dart';

import 'package:likewallet/libraryman/configuration_service.dart';
import 'package:likewallet/libraryman/address_service.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PaymentBuy extends StatefulWidget {
  PaymentBuy({required this.rate, required this.amount, required this.fee});
  final double rate;
  final double amount;
  final double fee;
  _PaymentBuy createState() =>
      new _PaymentBuy(rate: rate, amount: amount, fee: fee);
}

class _PaymentBuy extends State<PaymentBuy> {
  _PaymentBuy({required this.rate, required this.amount, required this.fee});
  final double rate;
  final double amount;
  final double fee;
  late File _imageFile;
  late SharedPreferences pref;
  late IAddressService addressService;
  late IConfigurationService configETH;

  final ImagePicker _picker = ImagePicker();

  late BaseAuth auth;
  FirebaseFirestore fireStore = FirebaseFirestore.instance;
  FocusNode doneFocusNode = new FocusNode();
  FocusNode doneFocusNodeAmount = new FocusNode();
  late OverlayEntry? overlayEntry;

  TextEditingController accountNumber = TextEditingController();
  TextEditingController photo = TextEditingController();
  TextEditingController amountBuy = TextEditingController();
  KeyboardVisibilityController keyboardVisibilityController = KeyboardVisibilityController();
  int keyword = 1;
  String bank = "";
  bool _bangkok = false;
  bool _ayudya = false;
  bool _kbank = false;
  bool _krungthai = false;
  bool _tmb = false;
  bool _saving = false;

  _changeBank(_choice) {
    setState(() {
      keyword = _choice;
      if (keyword == 1) {
        bank = AppLocalizations.of(context)!.translate('netbank_bangkok');
      }
      if (keyword == 2) {
        bank = AppLocalizations.of(context)!.translate('netbank_krungsi');
      }
      if (keyword == 3) {
        bank = AppLocalizations.of(context)!.translate('netbank_kasikorn');
      }
      if (keyword == 4) {
        bank = AppLocalizations.of(context)!.translate('netbank_krungthai');
      }
      if (keyword == 5) {
        bank = AppLocalizations.of(context)!.translate('netbank_tmb');
      }
    });
  }

  void showColoredToast(msg, colors) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_LONG,
        backgroundColor: colors,
        textColor: Colors.white);
  }

  Future<List<String>> upload(File file) async {

    if (file == null) return ['false'];
    String base64Image = base64Encode(file.readAsBytesSync());
    String fileName = file.path.split("/").last;
    var url = Uri.https(env.apiCheck, '/uploadImageBuy');
    final response = await http.post(url, body: {
      "image": base64Image,
      "name": fileName,
    });
    print(response.statusCode);
    final body = json.decode(response.body);

    print(body["result"]);
    print(body["result"]["id"]);
    print(body["result"]["url"]);
    return [body["result"]["id"], body["result"]["url"]];
  }

  Future<bool> saveSlip(String pathPic) async {
    User? user = await auth.getCurrentUser();
    var url = Uri.https(env.apiUrl, '/buyLikepoint');
    var response = await http.post(url, body: {
      '_token': await auth.getTokenFirebase(),
      'paymentMethod': 'bank',
      'bankName': bank,
      'accountNumber': accountNumber.text.toString(),
      'baht': amountBuy.text.toString(),
      'slip': pathPic,
      'phoneNumber': user!.phoneNumber,
      'address': configETH.getAddress()
    });
    // print(response);
    var body = json.decode(response.body);
    print(body["statusCode"]);
    int statusCode = body["statusCode"];
    if (statusCode == 200) {
      setState(() {
        _saving = false;
        print(_saving);
      });
      return true;
    } else {
      setState(() {
        _saving = false;
      });
      return false;
    }
  }

  setInit() async {
    pref = await SharedPreferences.getInstance();
    configETH = new ConfigurationService(pref);
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    auth = Auth();
    setInit();
    //keyboard show done
    if (Platform.isIOS) {
      doneFocusNode.addListener(() {
        bool hasFocus = doneFocusNode.hasFocus;
        if (hasFocus)
          showOverlay(context);
        else
          removeOverlay();
      });
      doneFocusNodeAmount.addListener(() {
        bool hasFocus = doneFocusNodeAmount.hasFocus;
        if (hasFocus)
          showOverlay(context);
        else
          removeOverlay();
      });
      keyboardVisibilityController.onChange.listen((bool visible) {
        removeOverlay();
      });
    }
  }

  //done button zone
  showOverlay(BuildContext context) {
    if (overlayEntry != null) return;
    OverlayState? overlayState = Overlay.of(context);
    overlayEntry = OverlayEntry(builder: (context) {
      return Positioned(
          bottom: MediaQuery.of(context).viewInsets.bottom,
          right: 0.0,
          left: 0.0,
          child: InputDoneView());
    });

    overlayState!.insert(overlayEntry!);
  }

  removeOverlay() {
    if (overlayEntry != null) {
      overlayEntry!.remove();
      overlayEntry = null;
    }
  }

  @override
  void dispose() {
    doneFocusNode.dispose();
    doneFocusNodeAmount.dispose();
    super.dispose();
  }

  buildForPhone(Orientation orientation) {
    return Scaffold(
//      bottomNavigationBar: NavigationBar(),
      backgroundColor: Color(0xFFFFFFFF),
      body: Stack(
        alignment: Alignment.center,
        children: <Widget>[
          Column(
            children: <Widget>[
              Container(
                color: Color(0xff141322),
                height: MediaQuery.of(context).size.height * 0.10945299145,
              ),
              Expanded(
                child: Container(
                  height: MediaQuery.of(context).size.height,
                  decoration: BoxDecoration(
                    color: Color(0xffFFFFFF),
                    boxShadow: [
                      BoxShadow(
                        spreadRadius: 0,
                        blurRadius: 4,
                        color: Color(0xff707071).withOpacity(0.5),
                        offset: Offset(
                          0.0,
                          2.0,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          Positioned(
            top: MediaQuery.of(context).size.height * 0.***********,
            child: Container(
              alignment: Alignment.center,
              width: MediaQuery.of(context).size.width,
              child: new Text(
                AppLocalizations.of(context)!.translate('netbank_title'),
                style: TextStyle(
                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
                    color: Color(0xffFFFFFF).withOpacity(1),
                    fontSize:
                        MediaQuery.of(context).size.height * 0.***********,
                    fontWeight: FontWeight.bold),
              ),
            ),
          ),
          Positioned(
            top: MediaQuery.of(context).size.height * Screen_util("width", 90),
            left: MediaQuery.of(context).size.width * Screen_util("width", 0),
            child: GestureDetector(
              onTap: () => {Navigator.of(context).pop()},
              child: Container(
                decoration: BoxDecoration(
                  color: Color(0xffB4E60D),
                  borderRadius: new BorderRadius.only(
                      bottomRight: Radius.circular(40.0),
                      topRight: Radius.circular(40.0)),
                ),
                height: MediaQuery.of(context).size.height * 0.04797863247,
                width: MediaQuery.of(context).size.width * 0.18317592592,
                child: Icon(
                  Icons.arrow_back_ios,
//                        color: Colors.blue,
                  size: MediaQuery.of(context).size.height * 0.0190042735,
                ),
              ),
            ),
          ),
          Positioned(
            top: MediaQuery.of(context).size.height * 0.17969230769,
            left: MediaQuery.of(context).size.width * 0.2,
            right: MediaQuery.of(context).size.width * 0.2,
            child: new Container(
//              color:Colors.blue,
              height: MediaQuery.of(context).size.height * 0.1,
              width: MediaQuery.of(context).size.height * 0.2,
              alignment: Alignment.center,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: <Widget>[
                  new Container(
                    margin: EdgeInsets.all(
                        MediaQuery.of(context).size.height * 0.00),
                    alignment: Alignment.center,
                    height: MediaQuery.of(context).size.height * 0.1,
                    width: MediaQuery.of(context).size.width * 0.2,
                    child: GestureDetector(
                        onTap: () {
                          _changeBank(1);
                          setState(() {
                            _bangkok = true;
                            if (_bangkok == true) {
                              _ayudya = false;
                              _kbank = false;
                              _krungthai = false;
                              _tmb = false;
                            }
                          });
                        },
                        child: AnimatedOpacity(
                          duration: const Duration(milliseconds: 100),
                          opacity: _bangkok ? 1 : 0.5,
                          child: Image.asset(
                            'assets/image/Bangkok.png',
                            height: MediaQuery.of(context).size.height *
                                0.*********,
//                                  color: Color(0xff6C6B6D).withOpacity(0.5),
                          ),
                          // ..
                        )),
                  ),
                  new Container(
                    margin: EdgeInsets.all(
                        MediaQuery.of(context).size.height * 0.00),
                    alignment: Alignment.center,
                    height: MediaQuery.of(context).size.height * 0.2,
                    width: MediaQuery.of(context).size.width * 0.2,
                    child: GestureDetector(
                        onTap: () {
                          _changeBank(2);
                          setState(() {
                            _ayudya = true;
                            if (_ayudya == true) {
                              _bangkok = false;
                              _kbank = false;
                              _krungthai = false;
                              _tmb = false;
                            }
                          });
                        },
                        child: AnimatedOpacity(
                          duration: const Duration(milliseconds: 100),
                          opacity: _ayudya ? 1 : 0.5,
                          child: Image.asset(
                            'assets/image/Ayudya.png',
                            height: MediaQuery.of(context).size.height *
                                0.*********,
//                                  color: Color(0xff6C6B6D).withOpacity(0.5),
                          ),
                          // ..
                        )),
                  ),
                  new Container(
                    margin: EdgeInsets.all(
                        MediaQuery.of(context).size.height * 0.00),
                    alignment: Alignment.center,
                    height: MediaQuery.of(context).size.height * 0.2,
                    width: MediaQuery.of(context).size.width * 0.2,
                    child: GestureDetector(
                        onTap: () {
                          _changeBank(3);
                          setState(() {
                            _kbank = true;
                            if (_kbank == true) {
                              _bangkok = false;
                              _ayudya = false;
                              _krungthai = false;
                              _tmb = false;
                            }
                          });
                        },
                        child: AnimatedOpacity(
                          duration: const Duration(milliseconds: 100),
                          opacity: _kbank ? 1 : 0.5,
                          child: Image.asset(
                            'assets/image/KBank.png',
                            height: MediaQuery.of(context).size.height *
                                0.*********,
//                                  color: Color(0xff6C6B6D).withOpacity(0.5),
                          ),
                          // ..
                        )),
                  ),
//                        new Container(
//                          margin: EdgeInsets.all(
//                              MediaQuery.of(context).size.height * 0.005),
//                          alignment: Alignment.center,
//                          height: MediaQuery.of(context).size.height * 0.1,
//                          width: MediaQuery.of(context).size.width * 0.16,
//                          child: GestureDetector(
//                              onTap: () {
//                                _changeBank(4);
//                                setState(() {
//                                  _krungthai = true;
//                                  if (_krungthai == true) {
//                                    _bangkok = false;
//                                    _ayudya = false;
//                                    _kbank = false;
//                                    _tmb = false;
//                                  }
//                                });
//                              },
//                              child: AnimatedOpacity(
//                                duration: const Duration(milliseconds: 100),
//                                opacity: _krungthai ? 1 : 0.5,
//                                child: Image.asset(
//                                  'assets/image/Krungthai.png',
//                                  scale: 2,
////                                  color: Color(0xff6C6B6D).withOpacity(0.5),
//                                ),
//                                // ..
//                              )),
//                        ),
//                        new Container(
//                          margin: EdgeInsets.all(
//                              MediaQuery.of(context).size.height * 0.005),
//                          alignment: Alignment.center,
//                          height: MediaQuery.of(context).size.height * 0.1,
//                          width: MediaQuery.of(context).size.width * 0.16,
//                          child: GestureDetector(
//                              onTap: () {
//                                _changeBank(5);
//                                setState(() {
//                                  _tmb = true;
//                                  if (_tmb == true) {
//                                    _bangkok = false;
//                                    _ayudya = false;
//                                    _kbank = false;
//                                    _krungthai = false;
//                                  }
//                                });
//                              },
//                              child: AnimatedOpacity(
//                                duration: const Duration(milliseconds: 100),
//                                opacity: _tmb ? 1 : 0.5,
//                                child: Image.asset(
//                                  'assets/image/TMB.png',
//                                  scale: 2,
////                                  color: Color(0xff6C6B6D).withOpacity(0.5),
//                                ),
//                                // ..
//                              )),
//                        ),
                ],
              ),
            ),
          ),
          Positioned(
            top: MediaQuery.of(context).size.height * 0.***********,
            left: MediaQuery.of(context).size.width * 0.2,
            right: MediaQuery.of(context).size.width * 0.2,
            child: new Container(
//              color:Colors.blue,
              height: MediaQuery.of(context).size.height * 0.1,
              width: MediaQuery.of(context).size.height * 0.2,
              alignment: Alignment.center,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: <Widget>[
                  new Container(
                    margin: EdgeInsets.all(
                        MediaQuery.of(context).size.height * 0.00),
                    alignment: Alignment.center,
                    height: MediaQuery.of(context).size.height * 0.2,
                    width: MediaQuery.of(context).size.width * 0.2,
                    child: GestureDetector(
                        onTap: () {
                          _changeBank(4);
                          setState(() {
                            _krungthai = true;
                            if (_krungthai == true) {
                              _bangkok = false;
                              _ayudya = false;
                              _kbank = false;
                              _tmb = false;
                            }
                          });
                        },
                        child: AnimatedOpacity(
                          duration: const Duration(milliseconds: 100),
                          opacity: _krungthai ? 1 : 0.5,
                          child: Image.asset(
                            'assets/image/Krungthai.png',
                            height: MediaQuery.of(context).size.height *
                                0.*********,
//                                  color: Color(0xff6C6B6D).withOpacity(0.5),
                          ),
                          // ..
                        )),
                  ),
                  new Container(
                    margin: EdgeInsets.all(
                        MediaQuery.of(context).size.height * 0.00),
                    alignment: Alignment.center,
                    height: MediaQuery.of(context).size.height * 0.2,
                    width: MediaQuery.of(context).size.width * 0.2,
                    child: GestureDetector(
                        onTap: () {
                          _changeBank(5);
                          setState(() {
                            _tmb = true;
                            if (_tmb == true) {
                              _bangkok = false;
                              _ayudya = false;
                              _kbank = false;
                              _krungthai = false;
                            }
                          });
                        },
                        child: AnimatedOpacity(
                          duration: const Duration(milliseconds: 100),
                          opacity: _tmb ? 1 : 0.5,
                          child: Image.asset(
                            'assets/image/TMB.png',
                            height: MediaQuery.of(context).size.height *
                                0.*********,
//                                  color: Color(0xff6C6B6D).withOpacity(0.5),
                          ),
                          // ..
                        )),
                  ),
                ],
              ),
            ),
          ),
          Positioned(
            top: MediaQuery.of(context).size.height * 0.***********,
            left: MediaQuery.of(context).size.width * 0.15,
            right: MediaQuery.of(context).size.width * 0.15,
            child: new Container(
//              color:Colors.blue,
              height: MediaQuery.of(context).size.height * 0.1,
              width: MediaQuery.of(context).size.width * 0.2,
              alignment: Alignment.topCenter,
              child: new Container(
                alignment: Alignment.bottomCenter,
                height: MediaQuery.of(context).size.height * 0.03,
                width: MediaQuery.of(context).size.width * 0.9,
                child: Text(
                  bank,
                  style: TextStyle(
                      color: Color(0xff141322).withOpacity(1),
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      fontWeight: FontWeight.normal,
                      fontSize:
                          MediaQuery.of(context).size.height * 0.***********),
                ),
              ),
            ),
          ),
          Positioned(
            top: MediaQuery.of(context).size.height *
                Screen_util("height", 1100),
            child: new Container(
//              color:Colors.blue,
              height: MediaQuery.of(context).size.height * 0.07,
              width: MediaQuery.of(context).size.width * 0.9,
              alignment: Alignment.center,
              child: new Container(
                decoration: BoxDecoration(
                  border: Border.all(width: 0.5, color: Color(0xff707071)),
                  borderRadius: new BorderRadius.circular(10.0),
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      spreadRadius: 0,
                      blurRadius: 2,
                      color: Color(0xff707071).withOpacity(0.5),
                      offset: Offset(
                        0.0,
                        1.0,
                      ),
                    ),
                  ],
                  //shape: BoxShape.rectangle,
//                  border: Border.all(10.0),
                ),
                alignment: Alignment.centerLeft,
                padding: EdgeInsets.only(
                    left: MediaQuery.of(context).size.width * 0.0),
                height: MediaQuery.of(context).size.height * 0.***********,
                width: MediaQuery.of(context).size.width * 0.***********,
                child: TextFormField(
                  controller: accountNumber,
//                  onFieldSubmitted: (term) {
////                    Navigator.pushNamed(context, '/confirmCash');
//                    print(term);
//                    Navigator.push(
//                      context,
//                      MaterialPageRoute(
//                          builder: (context) => ConfirmCash(
//                              amount: amount,
//                              fee: fee,
//                              rate: rate,
//                              accountNumber: term,
//                              typePay: bank)),
//                    );
//                  },
                  style: TextStyle(
                      color: Color(0xff707071),
                      fontSize:
                          MediaQuery.of(context).size.height * 0.***********,
                      fontFamily: 'Proxima Nova'),
                  keyboardType: TextInputType.number,
                  focusNode: doneFocusNode,
                  decoration: InputDecoration(
                    focusedBorder: InputBorder.none,
                    enabledBorder: OutlineInputBorder(
                        borderSide: BorderSide(
                            width: 0.1,
                            color: Color(0xff707071).withOpacity(0.0))),
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10.0)),
                    hintText: AppLocalizations.of(context)!
                            .translate('netbank_account'),
                    hintStyle: TextStyle(
                        color: Color(0xff707071),
                        fontSize:
                            MediaQuery.of(context).size.height * 0.***********,
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1')),
                    contentPadding: EdgeInsets.fromLTRB(20.0, 10.0, 20.0, 10.0),
                  ),
                ),
              ),
            ),
          ),
          Positioned(
            top: MediaQuery.of(context).size.height *
                Screen_util("height", 1300),
            child: new Container(
//              color:Colors.blue,
              height: MediaQuery.of(context).size.height * 0.07,
              width: MediaQuery.of(context).size.width * 0.9,
              alignment: Alignment.center,
              child: new Container(
                decoration: BoxDecoration(
                  border: Border.all(width: 0.5, color: Color(0xff707071)),
                  borderRadius: new BorderRadius.circular(10.0),
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      spreadRadius: 0,
                      blurRadius: 2,
                      color: Color(0xff707071).withOpacity(0.5),
                      offset: Offset(
                        0.0,
                        1.0,
                      ),
                    ),
                  ],
                  //shape: BoxShape.rectangle,
//                  border: Border.all(10.0),
                ),
                alignment: Alignment.centerLeft,
                padding: EdgeInsets.only(
                    left: MediaQuery.of(context).size.width * 0.0),
                height: MediaQuery.of(context).size.height * 0.***********,
                width: MediaQuery.of(context).size.width * 0.***********,
                child: TextFormField(
                  controller: photo,
                  style: TextStyle(
                      color: Color(0xff707071),
                      fontSize:
                          MediaQuery.of(context).size.height * 0.***********,
                      fontFamily: 'Proxima Nova'),
                  keyboardType: TextInputType.text,
                  readOnly: true,
                  decoration: InputDecoration(
                    focusedBorder: InputBorder.none,
                    enabledBorder: OutlineInputBorder(
                        borderSide: BorderSide(
                            width: 0.1,
                            color: Color(0xff707071).withOpacity(0.0))),
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10.0)),
                    hintText: AppLocalizations.of(context)!
                            .translate('netbank_upload'),
                    hintStyle: TextStyle(
                        color: Color(0xff707071),
                        fontSize:
                            MediaQuery.of(context).size.height * 0.***********,
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1')),
                    contentPadding: EdgeInsets.fromLTRB(20.0, 10.0, 20.0, 10.0),
                    suffixIcon: GestureDetector(
                      onTap: () => captureImage(ImageSource.gallery),
                      child: Icon(
                        Icons.add_a_photo,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
          Positioned(
            top: MediaQuery.of(context).size.height *
                Screen_util("height", 1500),
            child: new Container(
//              color:Colors.blue,
              height: MediaQuery.of(context).size.height * 0.07,
              width: MediaQuery.of(context).size.width * 0.9,
              alignment: Alignment.center,
              child: new Container(
                decoration: BoxDecoration(
                  border: Border.all(width: 0.5, color: Color(0xff707071)),
                  borderRadius: new BorderRadius.circular(10.0),
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      spreadRadius: 0,
                      blurRadius: 2,
                      color: Color(0xff707071).withOpacity(0.5),
                      offset: Offset(
                        0.0,
                        1.0,
                      ),
                    ),
                  ],
                  //shape: BoxShape.rectangle,
//                  border: Border.all(10.0),
                ),
                alignment: Alignment.centerLeft,
                padding: EdgeInsets.only(
                    left: MediaQuery.of(context).size.width * 0.0),
                height: MediaQuery.of(context).size.height * 0.***********,
                width: MediaQuery.of(context).size.width * 0.***********,
                child: TextFormField(
                  controller: amountBuy,
                  style: TextStyle(
                      color: Color(0xff707071),
                      fontSize:
                          MediaQuery.of(context).size.height * 0.***********,
                      fontFamily: 'Proxima Nova'),
                  focusNode: doneFocusNodeAmount,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    focusedBorder: InputBorder.none,
                    enabledBorder: OutlineInputBorder(
                        borderSide: BorderSide(
                            width: 0.1,
                            color: Color(0xff707071).withOpacity(0.0))),
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10.0)),
                    hintText: AppLocalizations.of(context)!
                            .translate('netbank_number'),
                    hintStyle: TextStyle(
                        color: Color(0xff707071),
                        fontSize:
                            MediaQuery.of(context).size.height * 0.***********,
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1')),
                    contentPadding: EdgeInsets.fromLTRB(20.0, 10.0, 20.0, 10.0),
                  ),
                ),
              ),
            ),
          ),
          Positioned(
            top: MediaQuery.of(context).size.height *
                Screen_util("height", 1900),
            child: new Container(
              alignment: Alignment.center,
              height: MediaQuery.of(context).size.height * 0.***********,
              width: MediaQuery.of(context).size.width * 0.***********,
              child: ButtonTheme(
                minWidth: MediaQuery.of(context).size.width * 0.***********,
                height: MediaQuery.of(context).size.height * 0.***********,
                child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      shape: RoundedRectangleBorder(
                        borderRadius:  BorderRadius.circular(8.0),
                      ),
                      disabledBackgroundColor: Color(0xff141322).withOpacity(1),
                      backgroundColor: Color(0xff141322).withOpacity(1),
                    ),
                    onPressed: () {
                      setState(() {
                        _saving = true;
                      });
                      if (amountBuy.text != '' &&
                          accountNumber.text != '' &&
                          bank != '' &&
                          photo.text != '') {
                        print('here');
                        upload(_imageFile).then((thenUpload) {
                          saveSlip(thenUpload[1]).then((success) {
                            if (success) {
                              setState(() {
                                _saving = false;
                              });
                              Navigator.push(
                                  context,
                                  EnterExitRoute(
                                      exitPage: PaymentBuy(rate: this.rate, fee: this.fee, amount: this.amount,),
                                      enterPage: CompleteTX(
                                        title: AppLocalizations.of(context)!
                                            .translate(
                                            'payment_buy_title_success'),
                                        detail: AppLocalizations.of(context)!
                                            .translate(
                                            'payment_buy_detail_success'),
                                        buttonText: AppLocalizations.of(context)!
                                            .translate('promtpay_thank'),
                                        back: HomeLikewallet(),
                                      )));
                            } else {
                              showColoredToast(
                                  AppLocalizations.of(context)!
                                      .translate('save_err'),
                                  Colors.red);
                            }
                          });
                        });
                      }
                    },
                    child: new Text(
                      AppLocalizations.of(context)!
                          .translate('netbank_button_next'),
                      style: TextStyle(
                          color: Color(0xffB4E60D),
                          fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                          fontSize: MediaQuery.of(context).size.height *
                              0.***********,
                          fontWeight: FontWeight.normal),
                    )),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> captureImage(ImageSource imageSource) async {
    try {
      final imageFile = await _picker.getImage(source: imageSource);

      setState(() {
        photo.text = imageFile!.path.substring(0, 20) + '....';
        _imageFile = File(imageFile.path);
      });
    } catch (e) {
      print(e);
    }
  }

  Widget? _buildActionButton({required Key key, required String text, required Function()? onPressed}) {
    return Expanded(
      child: TextButton(
          key: key,
          child: Text(text, style: TextStyle(fontSize: 20.0)),
          style: TextButton.styleFrom (
            shape: RoundedRectangleBorder(),
            backgroundColor: Colors.blueAccent,
            primary: Colors.white,
          ),
         onPressed: onPressed,),
    );
  }

  @override
  Widget build(BuildContext context) {
    final double shortestSide = MediaQuery.of(context).size.shortestSide;
    final bool useMobileLayout = shortestSide < 600.0;
    final Orientation orientation = MediaQuery.of(context).orientation;
    return ModalProgressHUD(
        opacity: 0.1,
        inAsyncCall: _saving,
        progressIndicator: CustomLoading(),
        child: Scaffold(
          body: useMobileLayout
              ? buildForPhone(orientation)
              : buildForPhone(orientation),
        ));
  }
}
