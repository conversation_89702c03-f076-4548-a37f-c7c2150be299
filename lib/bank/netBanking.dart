import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:likewallet/libraryman/app_local.dart';

import 'package:likewallet/bank/confirmCash.dart';
import 'package:likewallet/libraryman/keyboard_done_widget.dart';
import 'dart:io' show Platform;

class NetBanking extends StatefulWidget {
  NetBanking({required this.rate, required this.amount, required this.fee});
  final double rate;
  final double amount;
  final double fee;
  _NetBanking createState() =>
      new _NetBanking(rate: rate, amount: amount, fee: fee);
}

class _NetBanking extends State<NetBanking> {
  _NetBanking({required this.rate, required this.amount, required this.fee});
  final double rate;
  final double amount;
  final double fee;
  TextEditingController accountNumber = TextEditingController();
  FocusNode doneFocusNode = new FocusNode();
  OverlayEntry? overlayEntry;
  KeyboardVisibilityController keyboardVisibilityController = KeyboardVisibilityController();
  int keyword = 1;
  String bank = "";
  bool _bangkok = false;
  bool _ayudya = false;
  bool _kbank = false;
  bool _krungthai = false;
  bool _tmb = false;
  _changeBank(_choice) {
    setState(() {
      keyword = _choice;
      if (keyword == 1) {
        bank = 'BANGKOK-BANK';
      }
      if (keyword == 2) {
        bank = 'KRUNGSRI-BANK';
      }
      if (keyword == 3) {
        bank = 'KASIKORN-BANK';
      }
      if (keyword == 4) {
        bank = 'KRUNGTHAI-BANK';
      }
      if (keyword == 5) {
        bank = 'TMB-BANK';
      }
    });
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    //keyboard show done
    if (Platform.isIOS) {
      doneFocusNode.addListener(() {
        bool hasFocus = doneFocusNode.hasFocus;
        if (hasFocus)
          showOverlay(context);
        else
          removeOverlay();
      });

      keyboardVisibilityController.onChange.listen((bool visible) {
        removeOverlay();
        Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) => ConfirmCash(
                amount: amount,
                fee: fee,
                rate: rate,
                accountNumber: accountNumber.text,
                typePay: bank, nameAccount: '', totalSell: 0.0, symbol: '',)),
        );
      });
    }
  }

  //done button zone
  showOverlay(BuildContext context) {
    if (overlayEntry != null) return;
    OverlayState? overlayState = Overlay.of(context);
    overlayEntry = OverlayEntry(builder: (context) {
      return Positioned(
          bottom: MediaQuery.of(context).viewInsets.bottom,
          right: 0.0,
          left: 0.0,
          child: InputDoneView());
    });

    overlayState!.insert(overlayEntry!);
  }

  removeOverlay() {
    if (overlayEntry != null) {
      overlayEntry!.remove();
      overlayEntry = null;
    }
  }

  @override
  void dispose() {
    doneFocusNode.dispose();
    super.dispose();
  }

  buildForPhone() {
    return Scaffold(
//      bottomNavigationBar: NavigationBar(),
      backgroundColor: Color(0xFFFFFFFF),
      body: Stack(
//        alignment: Alignment.center,
        children: <Widget>[
          Column(
            children: <Widget>[
              Container(
                color: Color(0xff141322),
                height: MediaQuery.of(context).size.height * 0.10945299145,
              ),
              Expanded(
                child: Container(
                  height: MediaQuery.of(context).size.height,
                  decoration: BoxDecoration(
                    color: Color(0xffFFFFFF),
                    boxShadow: [
                      BoxShadow(
                        spreadRadius: 0,
                        blurRadius: 4,
                        color: Color(0xff707071).withOpacity(0.5),
                        offset: Offset(
                          0.0,
                          2.0,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          Positioned(
            top: MediaQuery.of(context).size.height * 0.***********,
            child: Container(
              alignment: Alignment.center,
              width: MediaQuery.of(context).size.width,
              child: new Text(
                AppLocalizations.of(context)!.translate('netbank_title'),
                style: TextStyle(
                    fontFamily: AppLocalizations.of(context)!.translate('font1'),
                    color: Color(0xffFFFFFF).withOpacity(1),
                    fontSize:
                        MediaQuery.of(context).size.height * 0.***********,
                    fontWeight: FontWeight.bold),
              ),
            ),
          ),
          Positioned(
            top: MediaQuery.of(context).size.height * 0.***********,
            child: GestureDetector(
              onTap: () => {Navigator.of(context).pop()},
              child: Container(
                decoration: BoxDecoration(
                  color: Color(0xffB4E60D),
                  borderRadius: new BorderRadius.only(
                      bottomRight: Radius.circular(40.0),
                      topRight: Radius.circular(40.0)),
                ),
                height: MediaQuery.of(context).size.height * 0.***********,
                width: MediaQuery.of(context).size.width * 0.18317592592,
                child: Icon(
                  Icons.arrow_back_ios,
//                        color: Colors.blue,
                  size: MediaQuery.of(context).size.height * 0.0190042735,
                ),
              ),
            ),
          ),
          Positioned(
            top: MediaQuery.of(context).size.height * 0.17969230769,
            left: MediaQuery.of(context).size.width * 0.2,
            right: MediaQuery.of(context).size.width * 0.2,
            child: new Container(
//              color:Colors.blue,
              height: MediaQuery.of(context).size.height * 0.1,
              width: MediaQuery.of(context).size.height * 0.2,
              alignment: Alignment.center,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: <Widget>[
                  new Container(
                    margin: EdgeInsets.all(
                        MediaQuery.of(context).size.height * 0.00),
                    alignment: Alignment.center,
                    height: MediaQuery.of(context).size.height * 0.1,
                    width: MediaQuery.of(context).size.width * 0.2,
                    child: GestureDetector(
                        onTap: () {
                          _changeBank(1);
                          setState(() {
                            _bangkok = true;
                            if (_bangkok == true) {
                              _ayudya = false;
                              _kbank = false;
                              _krungthai = false;
                              _tmb = false;
                            }
                          });
                        },
                        child: AnimatedOpacity(
                          duration: const Duration(milliseconds: 100),
                          opacity: _bangkok ? 1 : 0.5,
                          child: Image.asset(
                            'assets/image/Bangkok.png',
                            height: MediaQuery.of(context).size.height *
                                0.*********,
//                                  color: Color(0xff6C6B6D).withOpacity(0.5),
                          ),
                          // ..
                        )),
                  ),
                  new Container(
                    margin: EdgeInsets.all(
                        MediaQuery.of(context).size.height * 0.00),
                    alignment: Alignment.center,
                    height: MediaQuery.of(context).size.height * 0.2,
                    width: MediaQuery.of(context).size.width * 0.2,
                    child: GestureDetector(
                        onTap: () {
                          _changeBank(2);
                          setState(() {
                            _ayudya = true;
                            if (_ayudya == true) {
                              _bangkok = false;
                              _kbank = false;
                              _krungthai = false;
                              _tmb = false;
                            }
                          });
                        },
                        child: AnimatedOpacity(
                          duration: const Duration(milliseconds: 100),
                          opacity: _ayudya ? 1 : 0.5,
                          child: Image.asset(
                            'assets/image/Ayudya.png',
                            height: MediaQuery.of(context).size.height *
                                0.*********,
//                                  color: Color(0xff6C6B6D).withOpacity(0.5),
                          ),
                          // ..
                        )),
                  ),
                  new Container(
                    margin: EdgeInsets.all(
                        MediaQuery.of(context).size.height * 0.00),
                    alignment: Alignment.center,
                    height: MediaQuery.of(context).size.height * 0.2,
                    width: MediaQuery.of(context).size.width * 0.2,
                    child: GestureDetector(
                        onTap: () {
                          _changeBank(3);
                          setState(() {
                            _kbank = true;
                            if (_kbank == true) {
                              _bangkok = false;
                              _ayudya = false;
                              _krungthai = false;
                              _tmb = false;
                            }
                          });
                        },
                        child: AnimatedOpacity(
                          duration: const Duration(milliseconds: 100),
                          opacity: _kbank ? 1 : 0.5,
                          child: Image.asset(
                            'assets/image/KBank.png',
                            height: MediaQuery.of(context).size.height *
                                0.*********,
//                                  color: Color(0xff6C6B6D).withOpacity(0.5),
                          ),
                          // ..
                        )),
                  ),
//                        new Container(
//                          margin: EdgeInsets.all(
//                              MediaQuery.of(context).size.height * 0.005),
//                          alignment: Alignment.center,
//                          height: MediaQuery.of(context).size.height * 0.1,
//                          width: MediaQuery.of(context).size.width * 0.16,
//                          child: GestureDetector(
//                              onTap: () {
//                                _changeBank(4);
//                                setState(() {
//                                  _krungthai = true;
//                                  if (_krungthai == true) {
//                                    _bangkok = false;
//                                    _ayudya = false;
//                                    _kbank = false;
//                                    _tmb = false;
//                                  }
//                                });
//                              },
//                              child: AnimatedOpacity(
//                                duration: const Duration(milliseconds: 100),
//                                opacity: _krungthai ? 1 : 0.5,
//                                child: Image.asset(
//                                  'assets/image/Krungthai.png',
//                                  scale: 2,
////                                  color: Color(0xff6C6B6D).withOpacity(0.5),
//                                ),
//                                // ..
//                              )),
//                        ),
//                        new Container(
//                          margin: EdgeInsets.all(
//                              MediaQuery.of(context).size.height * 0.005),
//                          alignment: Alignment.center,
//                          height: MediaQuery.of(context).size.height * 0.1,
//                          width: MediaQuery.of(context).size.width * 0.16,
//                          child: GestureDetector(
//                              onTap: () {
//                                _changeBank(5);
//                                setState(() {
//                                  _tmb = true;
//                                  if (_tmb == true) {
//                                    _bangkok = false;
//                                    _ayudya = false;
//                                    _kbank = false;
//                                    _krungthai = false;
//                                  }
//                                });
//                              },
//                              child: AnimatedOpacity(
//                                duration: const Duration(milliseconds: 100),
//                                opacity: _tmb ? 1 : 0.5,
//                                child: Image.asset(
//                                  'assets/image/TMB.png',
//                                  scale: 2,
////                                  color: Color(0xff6C6B6D).withOpacity(0.5),
//                                ),
//                                // ..
//                              )),
//                        ),
                ],
              ),
            ),
          ),
          Positioned(
            top: MediaQuery.of(context).size.height * 0.***********,
            left: MediaQuery.of(context).size.width * 0.2,
            right: MediaQuery.of(context).size.width * 0.2,
            child: new Container(
//              color:Colors.blue,
              height: MediaQuery.of(context).size.height * 0.1,
              width: MediaQuery.of(context).size.height * 0.2,
              alignment: Alignment.center,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: <Widget>[
                  new Container(
                    margin: EdgeInsets.all(
                        MediaQuery.of(context).size.height * 0.00),
                    alignment: Alignment.center,
                    height: MediaQuery.of(context).size.height * 0.2,
                    width: MediaQuery.of(context).size.width * 0.2,
                    child: GestureDetector(
                        onTap: () {
                          _changeBank(4);
                          setState(() {
                            _krungthai = true;
                            if (_krungthai == true) {
                              _bangkok = false;
                              _ayudya = false;
                              _kbank = false;
                              _tmb = false;
                            }
                          });
                        },
                        child: AnimatedOpacity(
                          duration: const Duration(milliseconds: 100),
                          opacity: _krungthai ? 1 : 0.5,
                          child: Image.asset(
                            'assets/image/Krungthai.png',
                            height: MediaQuery.of(context).size.height *
                                0.*********,
//                                  color: Color(0xff6C6B6D).withOpacity(0.5),
                          ),
                          // ..
                        )),
                  ),
                  new Container(
                    margin: EdgeInsets.all(
                        MediaQuery.of(context).size.height * 0.00),
                    alignment: Alignment.center,
                    height: MediaQuery.of(context).size.height * 0.2,
                    width: MediaQuery.of(context).size.width * 0.2,
                    child: GestureDetector(
                        onTap: () {
                          _changeBank(5);
                          setState(() {
                            _tmb = true;
                            if (_tmb == true) {
                              _bangkok = false;
                              _ayudya = false;
                              _kbank = false;
                              _krungthai = false;
                            }
                          });
                        },
                        child: AnimatedOpacity(
                          duration: const Duration(milliseconds: 100),
                          opacity: _tmb ? 1 : 0.5,
                          child: Image.asset(
                            'assets/image/TMB.png',
                            height: MediaQuery.of(context).size.height *
                                0.*********,
//                                  color: Color(0xff6C6B6D).withOpacity(0.5),
                          ),
                          // ..
                        )),
                  ),
                ],
              ),
            ),
          ),
          Positioned(
            top: MediaQuery.of(context).size.height * 0.***********,
            left: MediaQuery.of(context).size.width * 0.15,
            right: MediaQuery.of(context).size.width * 0.15,
            child: new Container(
//              color:Colors.blue,
              height: MediaQuery.of(context).size.height * 0.1,
              width: MediaQuery.of(context).size.width * 0.2,
              alignment: Alignment.topCenter,
              child: new Container(
                alignment: Alignment.bottomCenter,
                height: MediaQuery.of(context).size.height * 0.03,
                width: MediaQuery.of(context).size.width * 0.9,
                child: Text(
                  bank,
                  style: TextStyle(
                      color: Color(0xff000000).withOpacity(1),
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                      fontWeight: FontWeight.normal,
                      fontSize:
                          MediaQuery.of(context).size.height * 0.***********),
                ),
              ),
            ),
          ),
          Positioned(
            top: MediaQuery.of(context).size.height * 0.***********,
            left: MediaQuery.of(context).size.width * 0.07,
            right: MediaQuery.of(context).size.width * 0.07,
            child: new Container(
//              color:Colors.blue,
              height: MediaQuery.of(context).size.height * 0.07,
              width: MediaQuery.of(context).size.width * 0.9,
              alignment: Alignment.center,
              child: new Container(
                decoration: BoxDecoration(
                  border: Border.all(width: 0.5, color: Color(0xff707071)),
                  borderRadius: new BorderRadius.circular(10.0),
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      spreadRadius: 0,
                      blurRadius: 2,
                      color: Color(0xff707071).withOpacity(0.5),
                      offset: Offset(
                        0.0,
                        1.0,
                      ),
                    ),
                  ],
                  //shape: BoxShape.rectangle,
//                  border: Border.all(10.0),
                ),
                alignment: Alignment.centerLeft,
                padding: EdgeInsets.only(
                    left: MediaQuery.of(context).size.width * 0.0),
                height: MediaQuery.of(context).size.height * 0.***********,
                width: MediaQuery.of(context).size.width * 0.***********,
                child: TextFormField(
                  controller: accountNumber,
                  onFieldSubmitted: (term) {
//                    Navigator.pushNamed(context, '/confirmCash');
                    print(term);
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) => ConfirmCash(
                              amount: amount,
                              fee: fee,
                              rate: rate,
                              accountNumber: term,
                              typePay: bank, totalSell: 0.0, symbol: '',nameAccount: '',)),
                    );
                  },
                  style: TextStyle(
                      color: Color(0xff707071),
                      fontSize:
                          MediaQuery.of(context).size.height * 0.***********,
                      fontFamily: 'Proxima Nova'),
                  keyboardType: TextInputType.number,
                  focusNode: doneFocusNode,
                  decoration: InputDecoration(
                    focusedBorder: InputBorder.none,
                    enabledBorder: OutlineInputBorder(
                        borderSide: BorderSide(
                            width: 0.1,
                            color: Color(0xff707071).withOpacity(0.0))),
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10.0)),
                    hintText: AppLocalizations.of(context)!
                            .translate('netbank_account'),
                    hintStyle: TextStyle(
                        color: Color(0xff707071),
                        fontSize:
                            MediaQuery.of(context).size.height * 0.***********,
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1')),
                    contentPadding: EdgeInsets.fromLTRB(20.0, 10.0, 20.0, 10.0),
                  ),
                ),
              ),
            ),
          ),
          Positioned(
            top: MediaQuery.of(context).size.height * 0.***********,
            left: MediaQuery.of(context).size.width * 0.07,
            right: MediaQuery.of(context).size.width * 0.07,
            child: new Container(
              alignment: Alignment.center,
              height: MediaQuery.of(context).size.height * 0.***********,
              width: MediaQuery.of(context).size.width * 0.***********,
              child: ButtonTheme(
                minWidth: MediaQuery.of(context).size.width * 0.***********,
                height: MediaQuery.of(context).size.height * 0.***********,
                child: TextButton(
                  style: ButtonStyle(
                    shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                      RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                    ),
                    backgroundColor: MaterialStateProperty.resolveWith<Color>(
                          (Set<MaterialState> states) {
                        if (states.contains(MaterialState.disabled)) {
                          return Color(0xff150e23).withOpacity(0.8); // Disabled color
                        }
                        return Color(0xff150e23).withOpacity(0.8); // Regular color
                      },
                    ),
                  ),
                  onPressed: () => (term) {
                    print(term);
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => ConfirmCash(
                          amount: amount,
                          fee: fee,
                          rate: rate,
                          accountNumber: term,
                          typePay: bank,
                          nameAccount: '',
                          symbol: '',
                          totalSell: 0.0,
                        ),
                      ),
                    );
                  },
                  child: Text(
                    AppLocalizations.of(context)!.translate('netbank_button_next'),
                    style: TextStyle(
                      color: Color(0xffB4E60D),
                      fontFamily: AppLocalizations.of(context)!.translate('font1'),
                      fontSize: MediaQuery.of(context).size.height * 0.***********,
                      fontWeight: FontWeight.normal,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(body: buildForPhone());
  }
}
