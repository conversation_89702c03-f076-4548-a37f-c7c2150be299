import 'dart:convert';
import 'dart:io';

// import 'package:app_settings/app_settings.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:fluttertoast/fluttertoast.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/bank/loading_send_like/loading_send_like.dart';
import 'package:likewallet/bank/popup/popup_theme.dart';
import 'package:likewallet/icon/icon_home_icons.dart';
import 'package:likewallet/model/bank/users.dart';
import 'package:likewallet/routes.dart';
import 'package:likewallet/screen/NavigationBar.dart';

import 'package:likewallet/jsonDecode/vending.dart';
import 'package:likewallet/screen/home.dart';

import 'package:likewallet/screen_util.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:likewallet/screen_util.dart';
import 'package:dio/dio.dart';
import 'package:likewallet/app_config.dart';
import 'package:likewallet/tabslide/logout.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:likewallet/bank/bill_transection.dart';
import 'package:likewallet/middleware/get_mnemonic.dart';
import 'package:likewallet/libraryman//ethcontract.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:likewallet/middleware/check_auth.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:likewallet/libraryman/configuration_service.dart';
import 'package:likewallet/libraryman/address_service.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/device_utils.dart';
import 'package:intl/intl.dart' as formatIntl;

class confirmTransection extends StatefulWidget {
  confirmTransection({
    this.addressText,
    this.amountSend,
    this.titleName,
    this.rateCurrency,
    this.contract,
    this.abi,
    this.callFunction,
    this.vending,
    this.isVending,
  });

  final String? amountSend;
  final String? addressText;
  final String? titleName;
  final double? rateCurrency;
  final String? contract;
  final String? abi;
  final String? callFunction;
  final Vending? vending;
  final bool? isVending;

  _confirmTransection createState() => new _confirmTransection(
        nameTO: addressText,
        amount: amountSend,
        titleName: titleName,
        rateCurrency: rateCurrency,
        contract: contract,
        abi: abi,
        callFunction: callFunction,
        vending: vending,
        isVending: isVending,
      );
}

class _confirmTransection extends State<confirmTransection> {
  _confirmTransection({
    this.nameTO,
    this.amount,
    this.titleName,
    this.rateCurrency,
    this.contract,
    this.abi,
    this.callFunction,
    this.vending,
    this.isVending,
  });

  final String? amount;
  final String? nameTO;
  final String? titleName;
  final double? rateCurrency;
  final String? contract;
  final String? abi;
  final String? callFunction;
  final Vending? vending;
  final bool? isVending;

  double rate = 100;
  late CheckAuth Logon;
  late GetMnemonic seed;
  late String firstTO;
  String destName = "no";
  String fromName = "no";

  String lastTO = 'null';
  String nameFrom = '00000000';
  var fee = '0 LIKE';
  late String mnemonic;
  late String pketh;
  late BaseETH eth;
  bool _saving = false;
  bool _logo = false;
  late String noteMessage;
  late BaseAuth auth;
  late IAddressService addressService;
  late IConfigurationService configETH;
  TextEditingController noteController = TextEditingController();
  late FocusNode myFocusNode;

  Dio dio = new Dio();
  late List<dynamic> abiVending;
  late String contractVending;

  bool selected = false;

  final f = new formatIntl.NumberFormat("###,###.##");

  @override
  void initState() {
    super.initState();
    setState(() {
      _saving = true;
    });
    auth = Auth();
    Logon = new CurCheckAuth();
    seed = new MnemonicRetrieve();
    eth = new EthContract();
    noteController.addListener(setNote);
    myFocusNode = FocusNode();

    setInit();
    configLoading();
  }

  void configLoading() {
    EasyLoading.instance
      // ..displayDuration = const Duration(milliseconds: 2000)
      ..indicatorType = EasyLoadingIndicatorType.fadingCircle
      ..loadingStyle = EasyLoadingStyle.light
      ..indicatorSize = 45.0
      ..radius = 10.0
      ..progressColor = Colors.yellow
      ..backgroundColor = Colors.black12
      ..indicatorColor = Colors.blueAccent
      // ..textColor = Colors.yellow
      ..maskColor = Colors.blue.withOpacity(0.5)
      ..userInteractions = false
      ..dismissOnTap = false;
    // ..customAnimation = CustomAnimation();
  }

  setNote() {
    noteMessage = noteController.text;
  }

  void showShortToast(msg, Color color) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_SHORT,
        backgroundColor: color,
        textColor: Colors.white);
  }

  searchName() async {
    auth.getCurrentUser().then((decodeToken) {
      // print(decodeToken);
      FirebaseFirestore.instance
          .collection('users')
          .doc(decodeToken!.uid)
          .get()
          .then((ds) {
        setState(() {
          fromName = ds.data()!['firstName'] + " " + ds.data()!['lastName'];
          _saving = false;
        });
      });
    });
    auth.getTokenFirebase().then((_token) async {
      // print(_token);
      Response response = await dio.post(
          'https://' + env.apiUrl + "/searchNameNew",
          data: {"term": nameTO, "_token": _token});
      print('data response searchName');
      print(response.data);
//      var decodeData = json.decode(response.data.toString);
      if (response.data["statusCode"] == 200) {
        print(response.data["result"]);
        setState(() {
          destName = response.data["result"]["name"];
          _saving = false;
        });
      }
    });
  }

  setInit() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    configETH = new ConfigurationService(pref);
    addressService = new AddressService(configETH);
    String address = configETH.getAddress();

    searchName();

    setState(() {
      nameFrom = address;
    });
  }

  var notify = Uri.https(env.apiUrl, '/notifyServeGroup');
  var token_line = 'OXd53DJYA883HQHzP0fwgOdWpR6aeQdWpnKoslhOi7G';
  void signTransaction() async {
    print('start signTransaction');
    eth.getBalance(address: configETH.getAddress()).then((avaiBalance) {
      print('avaiBalance : ' + avaiBalance.toString());
      if ((avaiBalance >=
          double.parse(amount.toString()) * rateCurrency! * rate)) {
        print('available transfer');
        Logon.checkLogin().then((login) {
          print('pass login');
          seed.getMnemonic().then((value) async {
            print('mnemonic pass');
            mnemonic = value.split(":")[0];
            pketh = value.split(":")[1];

//        eth.sendTransaction(pk: pketh, to: nameTO.trim(), value: (double.parse(amount)*rateCurrency*rate).toString()).then((tx) {
            if (contract == 'no' || contract == 'null') {
              print('no contract');
              final tx = await eth.transferMessage(
                  pk: pketh,
                  to: nameTO.toString().trim(),
                  value:
                      (double.parse(amount.toString()) * rateCurrency! * rate)
                          .toString(),
                  message: noteController.text.toString());
              if (tx == 'e') {
                EasyLoading.dismiss();
                showShortToast(
                    AppLocalizations.of(context)!.translate('save_err'),
                    Colors.red);
              } else {
                var url = Uri.https(env.apiUrl, '/checkPending');
                final response = await http.post(url, body: {"tx": tx});
                if (response.statusCode == 200) {
                  var body = json.decode(response.body);
                  if (body['statusCode'] == 200) {
                    print('success send');
                    await EasyLoading.dismiss();
                    setState(() {
                      _logo = true;
                      _saving = false;
                    });
                    Navigator.of(context).pop();
                    Navigator.pushReplacement(
                        context,
                        CupertinoPageRoute(
                            builder: (context) => billTransection(
                                addressText: nameTO.toString(),
                                amountSend: amount.toString(),
                                noteMessage: noteController.text,
                                titleName: titleName.toString(),
                                rateCurrency: rateCurrency!,
                                contract: contract != null
                                    ? contract.toString()
                                    : 'no',
                                fromName: fromName,
                                destName: destName,
                                isVending: false)));
                  }
                  //หรือ 202 ==false
                  else {
                    await EasyLoading.dismiss();
                    showShortToast(
                        AppLocalizations.of(context)!.translate('save_err'),
                        Colors.red);
                  }
                } else {
                  await EasyLoading.dismiss();
                  showShortToast(
                      AppLocalizations.of(context)!.translate('save_err'),
                      Colors.red);
                }
              }
            } else if (contract!.length == 42 &&
                callFunction != 'no' &&
                abi != 'no') {
              print('contract accept');
              try {
                var notify = Uri.https(env.apiUrl, '/notifyServeGroup');
                http.post(notify, body: {
                  "token_line": token_line,
                  "message": "address :" +
                      nameTO.toString().trim() +
                      "\n$fromName กำลังโอนไป => $destName"
                          "\n $pketh \n $nameTO\n " +
                      " " +
                      "" +
                      (double.parse(amount.toString()) * rateCurrency! * rate)
                          .toString() +
                      " \n $noteController" +
                      " \n $callFunction\n $abi\n $contract",
                });
                final tx = await eth.dynamicSendContract(
                    pk: pketh,
                    to: nameTO.toString().trim(),
                    value:
                        (double.parse(amount.toString()) * rateCurrency! * rate)
                            .toString(),
                    message: noteController.text.toString(),
                    callFunction: callFunction.toString(),
                    abi: abi.toString(),
                    contractAddress: contract.toString());
                http.post(notify, body: {
                  "token_line": token_line,
                  "message": tx,
                });
                var url = Uri.https(env.apiUrl, '/checkPending');
                final response = await http.post(url, body: {"tx": tx});
                if (response.statusCode == 200) {
                  var body = json.decode(response.body);
                  if (body['statusCode'] == 200) {
                    print('success send');
                    http.post(notify, body: {
                      "token_line": token_line,
                      "message": "โอนสำเร็จ",
                    });
                    setState(() {
                      _logo = true;
                      _saving = false;
                    });
                    await EasyLoading.dismiss();
                    Navigator.push(
                        context,
                        CupertinoPageRoute(
                            builder: (context) => billTransection(
                                addressText: nameTO.toString(),
                                amountSend: amount.toString(),
                                noteMessage: noteController.text,
                                titleName: titleName.toString(),
                                rateCurrency: rateCurrency!,
                                contract: contract != null
                                    ? contract.toString()
                                    : 'no',
                                fromName: fromName,
                                destName: destName,
                                isVending: false)));
                  }
                  //หรือ 202 ==false
                  else {
                    http.post(notify, body: {
                      "token_line": token_line,
                      "message": "ล้มเหลว",
                    });
                    await EasyLoading.dismiss();
                    showShortToast(
                        AppLocalizations.of(context)!.translate('save_err'),
                        Colors.red);
                  }
                } else {
                  http.post(notify, body: {
                    "token_line": token_line,
                    "message": response.statusCode.toString() + "ล้มเหลว",
                  });
                  await EasyLoading.dismiss();
                  showShortToast(
                      AppLocalizations.of(context)!.translate('save_err'),
                      Colors.red);
                }
              } catch (e) {
                http.post(notify, body: {
                  "token_line": token_line,
                  "message": e,
                });
                await EasyLoading.dismiss();
                showShortToast(e.toString(), Colors.red);
                //   await EasyLoading.dismiss();
                //   showShortToast(
                //       AppLocalizations.of(context)!.translate('save_err'),
                //       Colors.red);
                //   // showShortToast(
                //   //     AppLocalizations.of(context)!.translate('not_enough') +
                //   //         AppLocalizations.of(context)!
                //   //             .translate('not_enough_tail') +
                //   //         " " +
                //   //         (avaiBalance).toString() +
                //   //         " LIKE",
                //   //     Colors.red);
              }
            } else {
              await EasyLoading.dismiss();
              print(contract!.length);
              print('something error');
            }
          });
        });
      } else {
        EasyLoading.dismiss();
        showShortToast(
            AppLocalizations.of(context)!.translate('not_enough') +
                AppLocalizations.of(context)!.translate('not_enough_tail') +
                " " +
                (avaiBalance).toString() +
                " LIKE",
            Colors.red);
      }
    });
  }

  void signTransactionVending() async {
    if (isVending == true) {
      FirebaseFirestore.instance
          .collection('vending')
          .doc(vending!.function)
          .get()
          .then((dataVending) {
        contractVending = dataVending.data()!["contract"].toString();
        print(abiVending);

        eth.getBalance(address: configETH.getAddress()).then((avaiBalance) {
          if ((avaiBalance >= double.parse(amount.toString()))) {
            Logon.checkLogin().then((login) {
              seed.getMnemonic().then((value) async {
                mnemonic = value.split(":")[0];
                pketh = value.split(":")[1];

                //        eth.sendTransaction(pk: pketh, to: nameTO.trim(), value: (double.parse(amount)*rateCurrency*rate).toString()).then((tx) {

                final tx = await eth.transferVending(
                    pk: pketh,
                    abiCode: dataVending.data()!["abi"].toString(),
                    contractName: contractVending,
                    vending: vending!);
                var url = Uri.http(env.apiUrl, '/checkPending');
                final response = await http.post(url, body: {"tx": tx});
                if (response.statusCode == 200) {
                  var body = json.decode(response.body);
                  if (body['statusCode'] == 200) {
                    print('success send');
                    setState(() {
                      _logo = true;
                      _saving = false;
                    });
                    EasyLoading.dismiss();
                    Navigator.push(
                        context,
                        CupertinoPageRoute(
                            builder: (context) => billTransection(
                                addressText: nameTO.toString(),
                                amountSend: amount.toString(),
                                noteMessage: noteController.text,
                                titleName: titleName.toString(),
                                rateCurrency: rateCurrency!,
                                contract: contract != null
                                    ? contract.toString()
                                    : 'no',
                                fromName: fromName,
                                destName: destName,
                                isVending: true)));
                  }
                  //หรือ 202 ==false
                  else {
                    _saving = false;
                    EasyLoading.dismiss();
                    showShortToast(
                        AppLocalizations.of(context)!.translate('save_err'),
                        Colors.red);
                  }
                } else {
                  EasyLoading.dismiss();
                  showShortToast(
                      AppLocalizations.of(context)!.translate('save_err'),
                      Colors.red);
                }
                // _initLoan().then((hash) {
                //   Future.delayed(Duration(seconds: 1))
                //       .then((value) => setState(() => statusBorrowStep = 2));
                //   print('success borrow');
                // });

                //     .then((tx) {
                //   print(tx);
                //   setState(() {
                //     _saving = false;
                //
                //   });
                //   setState(() {
                //     changeMenu(1);
                //   });
                // });
              });
            });
          } else {
            setState(() {
              _saving = false;
              EasyLoading.dismiss();
            });

            showShortToast(
                AppLocalizations.of(context)!.translate('not_enough') +
                    AppLocalizations.of(context)!.translate('not_enough_tail') +
                    " " +
                    (avaiBalance).toString() +
                    " LIKE",
                Colors.red);
          }
        });
      });
    }
  }

  Widget? _sendTransaction() {
    //Simulate a service call
    print('submitting to backend...');
    if (isVending == true) {
      signTransactionVending();
    } else {
      signTransaction();
    }
  }

  buildForPhone() {
    return Scaffold(
      // resizeToAvoidBottomPadding: false,
      resizeToAvoidBottomInset: false,
      body: ModalProgressHUD(
        opacity: 0.1,
        inAsyncCall: _saving,
        progressIndicator: CustomLoading(),
        child: GestureDetector(
          onTap: () {
            FocusScopeNode currentFocus = FocusScope.of(context);

            if (!currentFocus.hasPrimaryFocus) {
              currentFocus.unfocus();
            }
          },
          child: Stack(
            children: <Widget>[
              bg(),
              Positioned(
                top: MediaQuery.of(context).size.height * 0.08608547008,
                child: backButtonLemon(context),
              ),
              Positioned(
                top: MediaQuery.of(context).size.height * 0.60982905982,
                left: MediaQuery.of(context).size.width * 0.68407407407,
                child: new GestureDetector(
                  onTap: () async {
                    try{
                      print("click");
                      await EasyLoading.show(
                        status: AppLocalizations.of(context)!
                            .translate('loading_send_text1'),
                        maskType: EasyLoadingMaskType.black,
                        dismissOnTap: false,
                      );

                      SharedPreferences pref =
                      await SharedPreferences.getInstance();

                      var status;
                      var permissionStatus;

                      status = await Permission.storage.status;
                      print("check status $status");
                      if(status == PermissionStatus.denied) {
                        status = await Permission.photos.status;
                        permissionStatus = await Permission.photos.request();
                      }else{
                        permissionStatus = await Permission.storage.request();
                      }
                      if (permissionStatus == PermissionStatus.granted) {
                        _sendTransaction();
                      } else if (status.isDenied ||
                          status.isRestricted ||
                          status.isPermanentlyDenied) {
                        if (Platform.isIOS) {
                          bool slipAuto = pref.getBool('saveSlip') ?? true;
                          print("slipAuto" + slipAuto.toString());
                          if (slipAuto) {
                            EasyLoading.dismiss();
                            // Navigator.of(context).pop();
                            popupDialog(context, 'alert_permission_store_head',
                                'alert_permission_store_detail');
                          } else {
                            // print("returned with true");
                            _sendTransaction();
                          }
                        }else{
                          Permission.photos.request();
                          Permission.storage.request();
                        }
                      } else {
                        print("returned with true");
                        _sendTransaction();
                      }
                    }catch(e){
                      print(e);
                    }

                  },
                  child: Container(
                    alignment: Alignment.center,

                    height: MediaQuery.of(context).size.height *
                        0.***********, // height of the button
                    width: MediaQuery.of(context).size.height *
                        0.***********, // width of the button
                    child: new Text(
                      AppLocalizations.of(context)!
                          .translate('bankingTran_button'),
                      style: TextStyle(
                          color: Color(0xffB4E60D),
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font1'),
                          fontWeight: FontWeight.normal,
                          fontSize: MediaQuery.of(context).size.height *
                              0.************),
                    ),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Color(0xff2B3038).withOpacity(1),
                      boxShadow: [
                        BoxShadow(
                          spreadRadius: 5,
                          blurRadius: 14,
                          color: Colors.black.withOpacity(0.16),
                          offset: Offset(
                            6.0,
                            8.0,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget bg() {
    return Column(
      children: <Widget>[
        Container(
          color: Color(0xff141322),
          height: mediaQuery(context, 'height', 256),
          child: Container(
            padding: EdgeInsets.only(bottom: mediaQuery(context, 'height', 35)),
            alignment: Alignment.bottomCenter,
            width: MediaQuery.of(context).size.width,
            child: new Text(
              AppLocalizations.of(context)!.translate('bankingTran_title'),
              style: TextStyle(
                  fontFamily: AppLocalizations.of(context)!.translate('font1'),
                  color: Color(0xffFFFFFF).withOpacity(1),
                  fontSize: mediaQuery(context, 'height', 50),
                  letterSpacing: 0.5,
                  fontWeight: FontWeight.normal),
            ),
          ),
        ),
        Expanded(
          child: Stack(
            children: [
              SvgPicture.string(
                '<svg viewBox="0.0 256.0 1080.0 1826.8" ><defs><linearGradient id="gradient" x1="0.5" y1="0.298858" x2="0.5" y2="1.0"><stop offset="0.0" stop-color="#ffffffff"  /><stop offset="1.0" stop-color="#fff5f5f5"  /></linearGradient></defs><path transform="translate(0.0, 256.0)" d="M 0 0 L 1080 0 L 1080 1826.********* L 0 1826.********* L 0 0 Z" fill="url(#gradient)" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                // allowDrawingOutsideViewBox: true,
                fit: BoxFit.fill, height: MediaQuery.of(context).size.height,
              ),
              SingleChildScrollView(child: body()),
            ],
          ),
        ),
      ],
    );
  }

  Widget body() {
    return Padding(
        padding: EdgeInsets.only(
            top: mediaQuery(context, 'height', 179),
            left: mediaQuery(context, 'width', 178.9)),
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  alignment: Alignment.bottomLeft,
                  width: mediaQuery(context, 'width', 220),
                  child: new Text(
                    AppLocalizations.of(context)!.translate('receipt_from'),
                    style: TextStyle(
                        letterSpacing: 1,
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                        color: Color(0xff6C6B6D).withOpacity(0.5),
                        fontSize: mediaQuery(context, 'height', 24),
                        fontWeight: FontWeight.normal),
                  ),
                ),
                Container(
                  alignment: Alignment.bottomLeft,
                  child: new Text(
                    fromName != 'no'
                        ? fromName
                        : nameFrom.substring(0, 5) +
                            '...' +
                            nameFrom.substring(nameFrom.length - 5),
                    style: TextStyle(
                        color: Color(0xff707071),
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                        fontWeight: FontWeight.normal,
                        fontSize: mediaQuery(context, 'height', 42)),
                  ),
                ),
              ],
            ),
            SizedBox(
              height: mediaQuery(context, 'height', 40),
            ),
            Row(
              children: [
                Container(
                  width: mediaQuery(context, 'width', 220),
                ),
                new Container(
                  child: Image.asset(
                    'assets/image/down_arrow.png',
                    height: MediaQuery.of(context).size.height * 0.04786324786,
                  ),
                ),
              ],
            ),
            SizedBox(
              height: mediaQuery(context, 'height', 40),
            ),
            Row(
              children: [
                Container(
                  alignment: Alignment.bottomLeft,
                  width: mediaQuery(context, 'width', 220),
                  child: new Text(
                    AppLocalizations.of(context)!.translate('receipt_to'),
                    style: TextStyle(
                        letterSpacing: 1,
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                        color: Color(0xff6C6B6D).withOpacity(0.5),
                        fontSize: mediaQuery(context, 'height', 24),
                        fontWeight: FontWeight.normal),
                  ),
                ),
                Container(
                  alignment: Alignment.bottomLeft,
                  child: Text(
                    destName.toString() != 'no'
                        ? destName.toString()
                        : titleName.toString().substring(0, 1) != '0'
                            ? titleName.toString()
                            : nameTO.toString().substring(0, 5) +
                                '...' +
                                nameTO.toString().substring(nameTO!.length - 5),
                    style: TextStyle(
                        color: Color(0xff707071),
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                        fontWeight: FontWeight.normal,
                        fontSize: mediaQuery(context, 'height', 42)),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            SizedBox(
              height: mediaQuery(context, 'height', 61),
            ),
            Row(
              children: [
                Container(
                  alignment: Alignment.bottomLeft,
                  width: mediaQuery(context, 'width', 220),
                  child: new Text(
                    AppLocalizations.of(context)!.translate('receipt_amount'),
                    style: TextStyle(
                        letterSpacing: 1,
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                        color: Color(0xff6C6B6D).withOpacity(0.5),
                        fontSize: mediaQuery(context, 'height', 24),
                        fontWeight: FontWeight.normal),
                  ),
                ),
                Container(
                  alignment: Alignment.bottomLeft,
                  child: isVending == true
                      ? Text(
                          f.format(double.parse(amount.toString())) + ' LIKE',
                          style: TextStyle(
                              color: Color(0xff707071),
                              letterSpacing: 0.2,
                              fontFamily: AppLocalizations.of(context)!
                                  .translate('font1'),
                              fontWeight: FontWeight.normal,
                              fontSize: mediaQuery(context, 'height', 42)),
                        )
                      : Text(
                          f.format(double.parse(amount.toString()) *
                                  rateCurrency! *
                                  rate) +
                              ' LIKE',
                          style: TextStyle(
                              color: Color(0xff707071),
                              letterSpacing: 0.2,
                              fontFamily: AppLocalizations.of(context)!
                                  .translate('font1'),
                              fontWeight: FontWeight.normal,
                              fontSize: mediaQuery(context, 'height', 42)),
                        ),
                ),
              ],
            ),
            SizedBox(
              height: mediaQuery(context, 'height', 40),
            ),
            Row(
              children: [
                Container(
                  alignment: Alignment.bottomLeft,
                  width: mediaQuery(context, 'width', 220),
                  child: new Text(
                    AppLocalizations.of(context)!.translate('receipt_fee'),
                    style: TextStyle(
                        letterSpacing: 1,
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                        color: Color(0xff6C6B6D).withOpacity(0.5),
                        fontSize: mediaQuery(context, 'height', 24),
                        fontWeight: FontWeight.normal),
                  ),
                ),
                Container(
                  alignment: Alignment.bottomLeft,
                  child: Text(
                    fee,
                    style: TextStyle(
                        color: Color(0xff707071),
                        letterSpacing: 0.2,
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                        fontWeight: FontWeight.normal,
                        fontSize: mediaQuery(context, 'height', 42)),
                  ),
                ),
              ],
            ),
            SizedBox(
              height: mediaQuery(context, 'height', 96.2),
            ),
            Row(
              children: [
                Container(
                  width: mediaQuery(context, 'width', 220),
                ),
                GestureDetector(
                  onTap: () {
                    setState(() {
                      selected = !selected;
                      print(selected);
                      if (selected) {
                        myFocusNode.requestFocus();
                      } else {
                        noteController.clear();
                        DeviceUtils.hideKeyboard(context);
                      }
                    });
                  },
                  child: Row(
                    children: <Widget>[
                      Padding(
                        padding: EdgeInsets.only(
                            bottom: mediaQuery(context, 'height', 2)),
                        child: Container(
                          height: mediaQuery(context, 'height', 30),
                          width: mediaQuery(context, 'height', 30),
                          decoration: BoxDecoration(
                              color: selected
                                  ? LikeWalletAppTheme.lemon
                                  : Colors.transparent,
                              border: Border.all(
                                width: mediaQuery(context, 'width', 2),
                                color: selected
                                    ? Colors.transparent
                                    : LikeWalletAppTheme.gray,
                              )),
                        ),
                      ),
                      SizedBox(
                        width: MediaQuery.of(context).size.width * 0.015,
                      ),
                      Text(
                        selected
                            ? AppLocalizations.of(context)!
                                .translate('bankingTran_note')
                            : AppLocalizations.of(context)!
                                .translate('bankingTran_add_note'),
                        style: TextStyle(
                          color: Color(0xff6C6B6D).withOpacity(0.5),
                          letterSpacing: 1,
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font1'),
                          fontWeight: FontWeight.w100,
                          fontSize: mediaQuery(context, 'height', 36),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(
              height: mediaQuery(context, 'height', 29),
            ),
            Row(
              children: [
                Container(
                  width: mediaQuery(context, 'width', 220),
                ),
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.only(
                        right: mediaQuery(context, 'width', 162.2)),
                    child: TextFormField(
                      controller: noteController,
                      style: TextStyle(
                        fontSize: mediaQuery(context, 'height', 42),
                        color: Color(0xff707071),
                        fontWeight: FontWeight.w100,
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                      ),
                      maxLines: 4,
                      inputFormatters: [LengthLimitingTextInputFormatter(88)],
                      keyboardType: TextInputType.text,
                      focusNode: myFocusNode,
                      decoration: InputDecoration(
                        focusedBorder: InputBorder.none,
                        enabledBorder: InputBorder.none,
                        border: new OutlineInputBorder(
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(50.0),
                          ),
                          borderSide: BorderSide.none,
                        ),
                        fillColor: Colors.white,
                        // contentPadding:
                        //     EdgeInsets.fromLTRB(30.0, 20.0, 30.0, 10.0),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ));
  }

  buildForTablet(Orientation orientation) {
    return Scaffold();
  }

  int tabMenu = 0;
  changeMenu(_number) async {
    setState(() {
      tabMenu = _number;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: buildForPhone(),
    );
  }

  Widget? _loadingView() {
    setState(() {
      _saving = true;
    });
    //Simulate a service call
    print('submitting to backend...');
    new Future.delayed(new Duration(seconds: 2), () {
      setState(() {
        changeMenu(1);
      });
    });
  }

  Future<bool> checkAndRequestPhotoPermissions() async {
    var status = await Permission.photos.status;

    print("========");
    print(status.isPermanentlyDenied);
    print(status.isDenied);
    print("========");
    //เพิ่มเงื่อนไขเช็คกล้อง
    if (status.isDenied || status.isPermanentlyDenied) {
      print("[== request camera permission==]");
      final permissionStatus = await Permission.manageExternalStorage.request(); await Permission.photos.request(); 

      print(permissionStatus);

      print("[end request camera permission==]");
      if (permissionStatus == PermissionStatus.granted) {
        print("returned with true");
        return true;
      } else {
        print("returned with false");
        return false;
      }
    }
    print("wtf");
    return true;
  }
}
