import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/screen/NavigationBar.dart' as nav;
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/libraryman/app_local.dart';

import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/screen_util.dart';
import 'dart:async';
import 'dart:typed_data';
import 'dart:convert';
import 'dart:io';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:ui' as ui;
import 'package:flutter/rendering.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:flutter/services.dart';
import 'package:likewallet/bank/completeTX.dart';

class QRCodePromptpay extends StatefulWidget {
  QRCodePromptpay({
    required this.amountSend,
    required this.titleName,
    required this.rateCurrency,
  });

  final String amountSend;
  final String titleName;
  final double rateCurrency;

  _QRCodePromptpay createState() => new _QRCodePromptpay(
        amount: amountSend,
        titleName: titleName,
        rateCurrency: rateCurrency,
      );
}

class _QRCodePromptpay extends State<QRCodePromptpay>
    with TickerProviderStateMixin {
  _QRCodePromptpay({
    required this.amount,
    required this.titleName,
    required this.rateCurrency,
  });
  bool statusTranfer = false;
  final String amount;
  final String titleName;
  final double rateCurrency;
  GlobalKey _globalKey = new GlobalKey();
  late Uint8List imageInMemory;
  @override
  void initState() {
    super.initState();
    setState(() {});
  }

  buildForPhone() {
    return Scaffold(
      // resizeToAvoidBottomPadding: false,
      resizeToAvoidBottomInset: false,
      body: GestureDetector(
        onTap: () {
          FocusScopeNode currentFocus = FocusScope.of(context);

          if (!currentFocus.hasPrimaryFocus) {
            currentFocus.unfocus();
          }
        },
        child: Stack(
          alignment: Alignment.center,
          children: <Widget>[
            ///Background
            BG(),

            /// Buttton Back Page
            _buttonBackPage(),

            ///Text Head PromptPay
            _textHead(),

            ///Title Line 1
            _titleLine1(),

            ///View QRCode
            _QRCode(),

            ///Button New Qrcode
            _newQRcode(),

            ///Button Save Qrcode
            _buttonSave(),

            ///Button Ststus
            statusTranfer ? _buttonStstus() : Container()
//            ///Button OK
//            _buttonOK()
          ],
        ),
      ),
    );
  }

  Widget BG() {
    return Column(
      children: <Widget>[
        Container(
          color: Color(0xff141322),
          height: MediaQuery.of(context).size.height * 0.***********,
        ),
        Expanded(
          child: Container(
            height: MediaQuery.of(context).size.height,
          ),
        ),
      ],
    );
  }

  Widget _textHead() {
    return Positioned(
      top: MediaQuery.of(context).size.height * 0.***********,
      child: Container(
        alignment: Alignment.center,
        width: MediaQuery.of(context).size.width,
        child: new Text(
          AppLocalizations.of(context)!.translate('bankingTran_title'),
          style: TextStyle(
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              color: Color(0xffFFFFFF).withOpacity(1),
              fontSize: MediaQuery.of(context).size.height * 0.***********,
              fontWeight: FontWeight.bold),
        ),
      ),
    );
  }

  Widget _titleLine1() {
    return Positioned(
      top: mediaQuery(context, 'height', 392),
      child: Text(
        AppLocalizations.of(context)!.translate('buylike_promptpay1'),
        style: TextStyle(
          letterSpacing: 0,
          fontWeight: FontWeight.w500,
          fontSize: mediaQuery(context, 'height', 42),
          fontFamily: AppLocalizations.of(context)!.translate('font1'),
          color: LikeWalletAppTheme.gray.withOpacity(1),
        ),
      ),
    );
  }

  Widget _QRCode() {
    return Positioned(
      top: mediaQuery(context, 'height', 517),
      child: RepaintBoundary(
          key: _globalKey,
          child: Container(
              padding: EdgeInsets.all(5.0),
              height: mediaQuery(context, 'height', 449),
              width: mediaQuery(context, 'width', 449),
              decoration: BoxDecoration(color: Colors.white, boxShadow: [
                BoxShadow(
                  spreadRadius: 1.0,
                  blurRadius: 7.0,
                  color: Color.fromRGBO(0, 0, 0, 0.2),
                  offset: Offset(0, 0),
                )
              ]),
              child: Image.network(
                  'https://www.siamvip.com/Feature/QRCode/QR.gif'))),
    );
  }

  Widget _newQRcode() {
    return Positioned(
      top: mediaQuery(context, 'height', 1050),
      child: Row(
        children: <Widget>[
          Text(
            AppLocalizations.of(context)!.translate('buylike_promptpay2'),
            style: TextStyle(
              letterSpacing: 0.3,
              fontWeight: FontWeight.normal,
              fontSize: mediaQuery(context, 'height', 36),
              fontFamily: AppLocalizations.of(context)!.translate('font1'),
              color: LikeWalletAppTheme.gray.withOpacity(0.3),
            ),
          ),
          SizedBox(width: mediaQuery(context, 'width', 20)),
          Container(
            alignment: Alignment.center,
            width: mediaQuery(context, 'width', 250),
            height: mediaQuery(context, 'height', 86),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(43.0),
              border: Border.all(
                  width: mediaQuery(context, 'width', 3),
                  color: LikeWalletAppTheme.gray4.withOpacity(0.3)),
            ),
            child: Text(
              AppLocalizations.of(context)!.translate('buylike_promptpay3'),
              style: TextStyle(
                letterSpacing: 0.3,
                fontWeight: FontWeight.normal,
                fontSize: mediaQuery(context, 'height', 36),
                fontFamily: AppLocalizations.of(context)!.translate('font1'),
                color: LikeWalletAppTheme.gray4.withOpacity(0.3),
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget _buttonSave() {
    return Positioned(
      top: mediaQuery(context, 'height', 1226),
      child: Container(
          width: mediaQuery(context, 'width', 930),
          height: mediaQuery(context, 'height', 261),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(131.0),
            color: LikeWalletAppTheme.white,
            boxShadow: [
              BoxShadow(
                color: LikeWalletAppTheme.gray.withOpacity(0.3),
                offset: Offset(0, 6),
                blurRadius: 10,
              ),
            ],
          ),
          child: Stack(
            children: <Widget>[
              Container(
                  padding: EdgeInsets.only(
                    left: mediaQuery(context, 'width', 86),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: <Widget>[
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: <Widget>[
                              Text(
                                AppLocalizations.of(context)!
                                    .translate('buylike_promptpay4'),
                                style: TextStyle(
                                  letterSpacing: 0,
                                  fontWeight: FontWeight.bold,
                                  fontSize: mediaQuery(context, 'height', 42),
                                  fontFamily: AppLocalizations.of(context)!
                                      .translate('font1'),
                                  color: LikeWalletAppTheme.gray.withOpacity(1),
                                ),
                              ),
                              SizedBox(
                                width: mediaQuery(context, 'width', 20),
                              ),
                              Text(
                                AppLocalizations.of(context)!
                                    .translate('buylike_promptpay5'),
                                style: TextStyle(
                                  letterSpacing: 0,
                                  fontWeight: FontWeight.normal,
                                  fontSize: mediaQuery(context, 'height', 42),
                                  fontFamily: AppLocalizations.of(context)!
                                      .translate('font1'),
                                  color:
                                      LikeWalletAppTheme.gray.withOpacity(0.3),
                                ),
                              ),
                            ],
                          ),
                          Text(
                            AppLocalizations.of(context)!
                                .translate('buylike_promptpay6'),
                            style: TextStyle(
                              letterSpacing: 0,
                              fontWeight: FontWeight.normal,
                              fontSize: mediaQuery(context, 'height', 42),
                              fontFamily: AppLocalizations.of(context)!
                                  .translate('font1'),
                              color: LikeWalletAppTheme.gray.withOpacity(0.3),
                            ),
                          ),
                        ],
                      ),
                      _save(),
                    ],
                  )),
            ],
          )),
    );
  }

  Widget _buttonStstus() {
    return Positioned(
      top: mediaQuery(context, 'height', 1512),
      child: Container(
          width: mediaQuery(context, 'width', 930),
          height: mediaQuery(context, 'height', 261),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(131.0),
            color: LikeWalletAppTheme.white,
            boxShadow: [
              BoxShadow(
                color: LikeWalletAppTheme.gray.withOpacity(0.3),
                offset: Offset(0, 6),
                blurRadius: 10,
              ),
            ],
          ),
          child: Stack(
            children: <Widget>[
              Container(
                  alignment: Alignment.center,
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: <Widget>[
                      _buttonSuccess(),
                      Text(
                        AppLocalizations.of(context)!
                            .translate('buylike_promptpay7'),
                        style: TextStyle(
                          letterSpacing: 0,
                          fontWeight: FontWeight.bold,
                          fontSize: mediaQuery(context, 'height', 42),
                          fontFamily:
                              AppLocalizations.of(context)!.translate('font1'),
                          color: LikeWalletAppTheme.gray.withOpacity(1),
                        ),
                      ),
                      _buttonOK(),
                    ],
                  )),
            ],
          )),
    );
  }

  Widget _buttonBackPage() {
    return Positioned(
      top: mediaQuery(context, 'height', 207),
      left: 0,
      child: GestureDetector(
        onTap: () => {Navigator.of(context).pop()},
        child: Container(
          decoration: BoxDecoration(
            color: Color(0xffB4E60D),
            borderRadius: new BorderRadius.only(
                bottomRight: Radius.circular(40.0),
                topRight: Radius.circular(40.0)),
          ),
          height: MediaQuery.of(context).size.height * 0.04797863247,
          width: MediaQuery.of(context).size.width * 0.18317592592,
          child: Icon(
            Icons.arrow_back_ios,
//                        color: Colors.blue,
            size: MediaQuery.of(context).size.height * 0.0190042735,
          ),
        ),
      ),
    );
  }

  Widget _save() {
    return new GestureDetector(
      onTap: () {
        _capturePng().then((value) {
          FadeSaveReceipt(context,
              AppLocalizations.of(context)!.translate('buylike_notify'), this);
        });

        /// Test of transaction status successfully
        setState(() {
          statusTranfer = !statusTranfer;
        });
      },
      child: Container(
          margin: EdgeInsets.only(
            right: mediaQuery(context, 'width', 53),
          ),
          alignment: Alignment.center,
          height: mediaQuery(context, 'height', 160),
          width: mediaQuery(context, 'height', 160),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: LikeWalletAppTheme.white,
            boxShadow: [
              BoxShadow(
                color: LikeWalletAppTheme.gray.withOpacity(0.3),
                offset: Offset(0, 1),
                spreadRadius: 0,
                blurRadius: 3,
              ),
            ],
          ),
          child: Icon(Icons.save_alt)),
    );
  }

  Widget _buttonSuccess() {
    return new GestureDetector(
      onTap: () {},
      child: Container(
        margin: EdgeInsets.only(
          left: mediaQuery(context, 'width', 53),
        ),
        alignment: Alignment.center,
        height: mediaQuery(context, 'height', 160),
        width: mediaQuery(context, 'height', 160),
        decoration: BoxDecoration(
          shape: BoxShape.circle,
//          color: LikeWalletAppTheme.lemon,
          color: LikeWalletAppTheme.bule1_2,
          boxShadow: [
            BoxShadow(
              color: LikeWalletAppTheme.gray.withOpacity(0.3),
              offset: Offset(0, 1),
              spreadRadius: 0,
              blurRadius: 3,
            ),
          ],
        ),
        child: Image.asset(LikeWalletImage.icon_success_white,
//    child: Image.asset(LikeWalletImage.icon_success,
            height: mediaQuery(context, "height", 51.17)),
      ),
    );
  }

  Widget _buttonOK() {
    return new GestureDetector(
      onTap: () {
        Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => CompleteTX(
                    title: AppLocalizations.of(context)!
                        .translate('buylike_completed1'),
                    detail: AppLocalizations.of(context)!
                        .translate('buylike_completed2'),
                    back: '/home')));
      },
      child: Container(
        margin: EdgeInsets.only(
          right: mediaQuery(context, 'width', 53),
        ),
        alignment: Alignment.center,
        height: mediaQuery(context, 'height', 160),
        width: mediaQuery(context, 'height', 160),
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: LikeWalletAppTheme.white,
          boxShadow: [
            BoxShadow(
              color: LikeWalletAppTheme.gray.withOpacity(0.3),
              offset: Offset(0, 1),
              spreadRadius: 0,
              blurRadius: 3,
            ),
          ],
        ),
        child: Text(
          AppLocalizations.of(context)!.translate('buylike_promptpay8'),
          style: TextStyle(
            letterSpacing: 0,
            fontWeight: FontWeight.bold,
            fontSize: mediaQuery(context, 'height', 42),
            fontFamily: AppLocalizations.of(context)!.translate('font1'),
            color: LikeWalletAppTheme.gray.withOpacity(1),
          ),
        ),
      ),
    );
  }

  Future<Uint8List> _capturePng() async {
    print('inside');
    RenderRepaintBoundary boundary =
        _globalKey.currentContext!.findRenderObject() as RenderRepaintBoundary;
    ui.Image image = await boundary.toImage(pixelRatio: 3.0);
    ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    Uint8List pngBytes = byteData!.buffer.asUint8List();
    String bs64 = base64Encode(pngBytes);
    if (Platform.isAndroid) {
      bool statusPermision = await Permission.photos.isGranted;
      if (statusPermision != true) {
        await Permission.manageExternalStorage.request(); await Permission.photos.request(); 
      }
    } else {
      bool statusPermision = await Permission.photos.isGranted;
      if (statusPermision != true) {
        await Permission.manageExternalStorage.request(); await Permission.photos.request(); 
      }
    }

    final result = await ImageGallerySaver.saveImage(
        Uint8List.fromList(pngBytes.buffer.asInt8List()));
    print(result);
    print('png done');
    FadeSaveReceipt(context,
        AppLocalizations.of(context)!.translate('fade_save_receipt'), this);
//    _snackSample(context);
    setState(() {
      imageInMemory = pngBytes;
    });

    return pngBytes;
  }

  @override
  Widget build(BuildContext context) {
    final double shortestSide = MediaQuery.of(context).size.shortestSide;
    final bool useMobileLayout = shortestSide < 600.0;
    final Orientation orientation = MediaQuery.of(context).orientation;
    return Scaffold(
        bottomNavigationBar: nav.NavigationBar(), body: buildForPhone());
  }
}
