import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:open_store/open_store.dart';
import 'package:url_launcher/url_launcher.dart';

//********************************************* claim **************************************

openApp(
    {required String androidAppId,
    required String iOSAppId,
    required String androidURl,
    required String path}) async {
  String os = Platform.operatingSystem;
  if (os == "ios") {
    print("Container clicked");
    try {
      await launch(path);
    } on PlatformException catch (e) {
      OpenStore.instance.open(
        appStoreId: iOSAppId, // AppStore id of your app
        androidAppBundleId: androidAppId, // Android app bundle package name
      );
    } finally {
      await launch(path);
      OpenStore.instance.open(
        appStoreId: iOSAppId, // AppStore id of your app
        androidAppBundleId: androidAppId, // Android app bundle package name
      );
//        launch("https://apps.apple.com/us/app/1507877291");
    }
  } else if (os == "android") {
    try {
      // await AppCheck.launchApp(androidAppId);
    } catch (e) {
      String url = androidURl;
      if (await canLaunch(url)) {
        await launch(url);
      } else {
        throw 'Could not launch $url';
      }
    }
  } else {
    Fluttertoast.showToast(
        msg: "device not support",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.TOP,
        backgroundColor: Colors.red,
        textColor: Colors.black,
        fontSize: 16.0);
  }
}

searchACTLib(id_activity) async {
  String url =
      'https://1xpf4khsld.execute-api.ap-southeast-1.amazonaws.com/latest';
  Map map = {"menu": "searchACT", "id_activity": id_activity.toString()};

  final response = await apiRequest(url, map);
  return json.decode(response);
}

saveACTLib(
    email,
    ID_LDX,
    phone_firebase,
    f_name,
    l_name,
    id_activity,
    titile,
    amount,
    type_bu,
    type_bu_gr,
    use_bu,
    use_bu_gr,
    use_bag_name,
    isAutoClaim,
    isGojoy,
    pool_percent,
    pool,
    gojoy_id_activity,
    gojoy_name_activity) async {
  String url =
      'https://1xpf4khsld.execute-api.ap-southeast-1.amazonaws.com/latest';
  Map map = {
    "menu": "insertactivityGojoy",
    "update_user": email,
    "create_user": email,
    "id_activity": id_activity,
    "name_activity": titile,
    "amount": amount.toString(),
    "id_ldx": ID_LDX,
    "tel_ldx": phone_firebase,
    "name_ldx": f_name,
    "lname_ldx": l_name,
    "type_bu": type_bu,
    "type_bu_gr": type_bu_gr,
    "use_bu": use_bu,
    "use_bu_gr": use_bu_gr,
    "use_bag_name": use_bag_name,
    "isAutoClaim": isAutoClaim.toString(),
    "isGojoy": isGojoy.toString(),
    "pool_percent": pool_percent,
    "pool": pool,
    "gojoy_id_activity": gojoy_id_activity,
    "gojoy_name_activity": gojoy_name_activity
  };

  final response = await apiRequest(url, map);
  return json.decode(response);
}

claimlikeLib(running, act_id, name_activity, fullname, mobile, id_ldx, amount,
    AddressLikepoint) async {
  String url =
      'https://1xpf4khsld.execute-api.ap-southeast-1.amazonaws.com/latest';
  Map map = {
    "menu": "tranferclaimlike",
    "running": running.toString(),
    "act_id": act_id.toString(),
    "actName": name_activity.toString(),
    "fullname": fullname.toString(),
    "mobile": mobile.toString(),
    "id_ldx": id_ldx.toString(),
    "address": AddressLikepoint,
    "amount": amount
  };

  final response = await apiRequest(url, map);
  return json.decode(response);
}

claimlikegojoyLib(running, AddressLikepoint) async {
  String url =
      'https://1xpf4khsld.execute-api.ap-southeast-1.amazonaws.com/latest';
  Map map = {
    "menu": "tranferclaimlikeGojoy",
    "running": running.toString(),
    "address": AddressLikepoint.toString()
  };

  final response = await apiRequest(url, map);
  return json.decode(response);
}

updateActivityManualLib(running, ID_LDX) async {
  String url =
      'https://1xpf4khsld.execute-api.ap-southeast-1.amazonaws.com/latest';
  Map map = {
    "menu": "UpdateTransferActManual",
    "running": running.toString(),
    "id_ldx": ID_LDX
  };

  final response = await apiRequest(url, map);
  return json.decode(response);
}

//********************************************* claim **************************************

//********************************************* claim new **************************************
insertactivityNew(
    user_transfer,
    select_key,
    idmember,
    fullname,
    name,
    lname,
    bu,
    point,
    status,
    id_activity,
    name_activity,
    id_ldx,
    tel_ldx,
    fullname_ldx,
    name_ldx,
    lname_ldx,
    address,
    status_copy,
    status_plus,
    note_plus,
    type_bu,
    type_bu_gr,
    use_bu,
    use_bu_gr,
    use_bag_name,
    status_check,
    isAutoClaim,
    isGojoy,
    pool_percent,
    pool,
    gojoy_id_activity,
    gojoy_name_activity,
    checkCancel,
    type_bag_bu,
    bag_bu,
    active,
    fromAddress,
    pay_bu_activity,
    pay_bu_member,
    tax_per,
    tax_point,
    sumall,
    day_n,
    expire_date_tranfer,
    pool_address) async {
  var gojoy_point_pool = double.parse(point) * double.parse(pool_percent);
  var gojoy_point_user = double.parse(point) - gojoy_point_pool;
  String url =
      'https://1xpf4khsld.execute-api.ap-southeast-1.amazonaws.com/latest';
  Map map = {
    "menu": "insertactivityNewV2",
    "user_transfer": user_transfer.toString(),
    "data": {
      "select_key": select_key.toString(),
      "idmember": idmember.toString(),
      "fullname": fullname.toString(),
      "name": name.toString(),
      "lname": lname.toString(),
      "bu": bu.toString(),
      "point": double.parse(point),
      "status": status.toString(),
      "id_activity": int.parse(id_activity),
      "name_activity": name_activity.toString(),
      "id_ldx": id_ldx.toString(),
      "tel_ldx": tel_ldx.toString(),
      "fullname_ldx": fullname_ldx.toString(),
      "name_ldx": name_ldx.toString(),
      "lname_ldx": lname_ldx.toString(),
      "address": address.toString(),
      "status_copy": status_copy.toString(),
      "status_plus": status_plus.toString(),
      "note_plus": note_plus.toString(),
      "type_bu": type_bu.toString(),
      "type_bu_gr": type_bu_gr.toString(),
      "use_bu": use_bu.toString(),
      "use_bu_gr": use_bu_gr.toString(),
      "use_bag_name": use_bag_name.toString(),
      "status_check": status_check.toString(),
      "isAutoClaim": isAutoClaim.toString(),
      "isGojoy": isGojoy.toString(),
      "pool_percent": double.parse(pool_percent),
      "pool": pool.toString(),
      "gojoy_point_user": gojoy_point_user,
      "gojoy_point_pool": gojoy_point_pool,
      "gojoy_id_activity": int.parse(gojoy_id_activity),
      "gojoy_name_activity": gojoy_name_activity.toString(),
      "checkCancel": checkCancel.toString(),
      "type_bag_bu": type_bag_bu.toString(),
      "bag_bu": bag_bu.toString(),
      "active": active.toString(),
      "fromAddress": fromAddress.toString(),
      "pay_bu_activity": pay_bu_activity.toString(),
      "pay_bu_member": pay_bu_member.toString(),
      "tax_per": double.parse(tax_per),
      "tax_point": double.parse(tax_point),
      "sumall": double.parse(sumall),
      "day_n": day_n.toString(),
      "expire_date_tranfer": expire_date_tranfer.toString(),
      "pool_address": pool_address.toString()
    }
  };

  final response = await apiRequest(url, map);
  return json.decode(response);
}

claimlikeNew(user_transfer, data) async {
  String url =
      'https://1xpf4khsld.execute-api.ap-southeast-1.amazonaws.com/latest';
  Map map = {
    "menu": "tranferclaimlikeNewV2",
    "user_transfer": user_transfer.toString(),
    "data": data
  };

  final response = await apiRequest(url, map);
  return json.decode(response);
}
//********************************************* claim new **************************************

//********************************************* claim lock **************************************
insertactivitylock(
    user_transfer,
    select_key,
    idmember,
    fullname,
    name,
    lname,
    bu,
    point,
    status,
    id_activity,
    name_activity,
    id_ldx,
    tel_ldx,
    fullname_ldx,
    name_ldx,
    lname_ldx,
    address,
    status_copy,
    status_plus,
    note_plus,
    type_bu,
    type_bu_gr,
    use_bu,
    use_bu_gr,
    use_bag_name,
    status_check,
    isAutoClaim,
    isGojoy,
    pool_percent,
    pool,
    gojoy_id_activity,
    gojoy_name_activity,
    checkCancel,
    type_bag_bu,
    bag_bu,
    active,
    fromAddress,
    pay_bu_activity,
    pay_bu_member,
    tax_per,
    tax_point,
    sumall,
    day_n,
    expire_date_tranfer,
    pool_address,
    date_lock) async {
  var gojoy_point_pool = double.parse(point) * double.parse(pool_percent);
  var gojoy_point_user = double.parse(point) - gojoy_point_pool;
  String url =
      'https://1xpf4khsld.execute-api.ap-southeast-1.amazonaws.com/latest';
  Map map = {
    "menu": "insertactivityLock",
    "user_transfer": user_transfer.toString(),
    "data": {
      "select_key": select_key.toString(),
      "idmember": idmember.toString(),
      "fullname": fullname.toString(),
      "name": name.toString(),
      "lname": lname.toString(),
      "bu": bu.toString(),
      "point": double.parse(point),
      "status": status.toString(),
      "id_activity": int.parse(id_activity),
      "name_activity": name_activity.toString(),
      "id_ldx": id_ldx.toString(),
      "tel_ldx": tel_ldx.toString(),
      "fullname_ldx": fullname_ldx.toString(),
      "name_ldx": name_ldx.toString(),
      "lname_ldx": lname_ldx.toString(),
      "address": address.toString(),
      "status_copy": status_copy.toString(),
      "status_plus": status_plus.toString(),
      "note_plus": note_plus.toString(),
      "type_bu": type_bu.toString(),
      "type_bu_gr": type_bu_gr.toString(),
      "use_bu": use_bu.toString(),
      "use_bu_gr": use_bu_gr.toString(),
      "use_bag_name": use_bag_name.toString(),
      "status_check": status_check.toString(),
      "isAutoClaim": isAutoClaim.toString(),
      "isGojoy": isGojoy.toString(),
      "pool_percent": double.parse(pool_percent),
      "pool": pool.toString(),
      "gojoy_point_user": gojoy_point_user,
      "gojoy_point_pool": gojoy_point_pool,
      "gojoy_id_activity": gojoy_id_activity.toString(),
      "gojoy_name_activity": gojoy_name_activity.toString(),
      "checkCancel": checkCancel.toString(),
      "type_bag_bu": type_bag_bu.toString(),
      "bag_bu": bag_bu.toString(),
      "active": active.toString(),
      "fromAddress": fromAddress.toString(),
      "pay_bu_activity": pay_bu_activity.toString(),
      "pay_bu_member": pay_bu_member.toString(),
      "tax_per": double.parse(tax_per),
      "tax_point": double.parse(tax_point),
      "sumall": double.parse(sumall),
      "day_n": day_n.toString(),
      "expire_date_tranfer": expire_date_tranfer.toString(),
      "pool_address": pool_address.toString(),
      "day_lock": int.parse(date_lock.toString()),
      "time_unlock": "",
      "time_convert": (int.parse(date_lock.toString()) * 86400).toString()
    }
  };

  final response = await apiRequest(url, map);
  return json.decode(response);
}

claimlikeLock(user_transfer, data) async {
  String url =
      'https://1xpf4khsld.execute-api.ap-southeast-1.amazonaws.com/latest';
  Map map = {
    "menu": "tranferclaimlikeLock",
    "user_transfer": user_transfer.toString(),
    "data": data
  };

  final response = await apiRequest(url, map);
  return json.decode(response);
}
//********************************************* claim lock **************************************

//********************************************* get address **************************************
getaddressLib(phone_firebase) async {
  String url = 'https://new.likepoint.io/getAddressByphoneNumber';
  Map map = {"phoneNumber": phone_firebase};

  final response = await apiRequest(url, map);
  return json.decode(response);
}
//********************************************* get address **************************************

//********************************************* get first regist app **************************************
loadBuFirstLib(phone_firebase) async {
  String url =
      'https://1xpf4khsld.execute-api.ap-southeast-1.amazonaws.com/latest/loadAppBuFirst';
  Map map = {"phoneNationCode": phone_firebase.toString()};

  final response = await apiRequest(url, map);
  return json.decode(response);
}
//********************************************* get first regist app **************************************

Future<String> apiRequest(String url, Map jsonMap) async {
  HttpClient httpClient = new HttpClient();
  HttpClientRequest request = await httpClient.postUrl(Uri.parse(url));
  request.headers.set('content-type', 'application/json');
  request.headers.set('x-api-key', 'hjwsduripfksefghtrwdmngjtieowpsskfgtriuyt');
  request.add(utf8.encode(json.encode(jsonMap)));
  HttpClientResponse response = await request.close();
  // todo - you should check the response.statusCode
  String reply = await response.transform(utf8.decoder).join();
  httpClient.close();
  return reply;
}

mediaQuery1(context, String type, double value) {
//  BuildContext context;
  double _height = MediaQuery.of(context).size.height;
  double _width = MediaQuery.of(context).size.width;

  double widthScreen = 1080;
  double heightScreen = 2340;

  if (type == "height" || type == "h") {
    return (_height * (value / heightScreen));
  } else if (type == "width" || type == "w") {
    return (_width * (value / widthScreen));
  }
}

mediaQuery2(context, String type, double value) {
//  BuildContext context;
  double _height = MediaQuery.of(context).size.height;
  double _width = MediaQuery.of(context).size.width;

  double widthScreen = 828;
  double heightScreen = 1792;

  if (type == "height" || type == "h") {
    return (_height * (value / heightScreen));
  } else if (type == "width" || type == "w") {
    return (_width * (value / widthScreen));
  }
}

mediaQuery3(context, String type, double value) {
//  BuildContext context;
  double _height = MediaQuery.of(context).size.height;
  double _width = MediaQuery.of(context).size.width;

  double widthScreen = 414;
  double heightScreen = 736;

  if (type == "height" || type == "h") {
    return (_height * (value / heightScreen));
  } else if (type == "width" || type == "w") {
    return (_width * (value / widthScreen));
  }
}

convertDateTime(date, select) {
  var datetimeSplit = date.split("T");
  var datesplit = datetimeSplit[0].split("-");
  var timesplit = datetimeSplit[1].split(":");
  if (select == "Ddefalt") {
    return datetimeSplit[0];
  } else if (select == "DdefaltPlusOne") {
    return datesplit[2] +
        "-" +
        datesplit[1] +
        "-" +
        (int.parse(datesplit[0]) + 1).toString();
  } else if (select == "DBY") {
    return datesplit[0];
  } else if (select == "DBM") {
    return datesplit[1];
  } else if (select == "DBD") {
    return datesplit[2];
  } else if (select == "DY") {
    return datesplit[2];
  } else if (select == "DM") {
    return datesplit[1];
  } else if (select == "DD") {
    return datesplit[0];
  } else if (select == "D") {
    return datesplit[0] + "/" + datesplit[1] + "/" + datesplit[2];
  } else if (select == "DDB") {
    return datesplit[2] + "-" + datesplit[1] + "-" + datesplit[0];
  } else if (select == "dd-mm-yy") {
    return datesplit[0] +
        "-" +
        datesplit[1] +
        "-" +
        datesplit[2].substring(2, 4);
  } else if (select == "DTDB") {
    return datesplit[2] +
        "-" +
        datesplit[1] +
        "-" +
        datesplit[0] +
        " " +
        datetimeSplit[1];
  } else if (select == "T") {
    return datetimeSplit[1];
  } else if (select == "TfromDB") {
    var timesplitfromDB = datetimeSplit[1].split(".");
    return timesplitfromDB[0];
  } else if (select == "TH") {
    return timesplit[0];
  } else if (select == "TM") {
    return timesplit[1];
  } else if (select == "TS") {
    return timesplit[2];
  } else if (select == "THM") {
    return timesplit[0] + ":" + timesplit[1];
  } else if (select == "DTY") {
    if (datesplit[1].toString() == "01") {
      return datesplit[2] +
          " มกราคม " +
          (int.parse(datesplit[0]) + 543).toString();
    } else if (datesplit[1].toString() == "02") {
      return datesplit[2] +
          " กุมภาพันธ์ " +
          (int.parse(datesplit[0]) + 543).toString();
    } else if (datesplit[1].toString() == "03") {
      return datesplit[2] +
          " มีนาคม " +
          (int.parse(datesplit[0]) + 543).toString();
    } else if (datesplit[1].toString() == "04") {
      return datesplit[2] +
          " เมษายน " +
          (int.parse(datesplit[0]) + 543).toString();
    } else if (datesplit[1].toString() == "05") {
      return datesplit[2] +
          " พฤษภาคม " +
          (int.parse(datesplit[0]) + 543).toString();
    } else if (datesplit[1].toString() == "06") {
      return datesplit[2] +
          " มิถุนายน " +
          (int.parse(datesplit[0]) + 543).toString();
    } else if (datesplit[1].toString() == "07") {
      return datesplit[2] +
          " กรกฎาคม " +
          (int.parse(datesplit[0]) + 543).toString();
    } else if (datesplit[1].toString() == "08") {
      return datesplit[2] +
          " สิงหาคม " +
          (int.parse(datesplit[0]) + 543).toString();
    } else if (datesplit[1].toString() == "09") {
      return datesplit[2] +
          " กันยายน " +
          (int.parse(datesplit[0]) + 543).toString();
    } else if (datesplit[1].toString() == "10") {
      return datesplit[2] +
          " ตุลาคม " +
          (int.parse(datesplit[0]) + 543).toString();
    } else if (datesplit[1].toString() == "11") {
      return datesplit[2] +
          " พฤศจิกายน " +
          (int.parse(datesplit[0]) + 543).toString();
    } else if (datesplit[1].toString() == "12") {
      return datesplit[2] +
          " ธันวาคม " +
          (int.parse(datesplit[0]) + 543).toString();
    } else {
      return "error";
    }
  } else {
    return "select not fond";
  }
}

convertDateTimeFromDB(date, select) {
  var datetimeSplit = date.split("T");
  var datesplit = datetimeSplit[0].split("-");
  var timesplit = datetimeSplit[1].split(":");
  if (select == "Ddefalt") {
    return datetimeSplit[0];
  } else if (select == "D") {
    return datesplit[0] + "/" + datesplit[1] + "/" + datesplit[2];
  } else if (select == "DD") {
    return datesplit[2];
  } else if (select == "MM") {
    return datesplit[1];
  } else if (select == "YYYY") {
    return datesplit[0];
  } else if (select == "yyyy-MM-dd") {
    return datesplit[0] + "-" + datesplit[1] + "-" + datesplit[2];
  } else {
    return "select not fond";
  }
}

convertDateTime2(date, select) {
  var datetimeSplit = date.split(" ");
  var datesplit = datetimeSplit[0].split("-");
  var timesplit = datetimeSplit[1].split(":");
  if (select == "D") {
    return datesplit[0] + "/" + datesplit[1] + "/" + datesplit[2];
  } else if (select == "dd/MM/YYYY mm:ss") {
    return datesplit[2] +
        "/" +
        datesplit[1] +
        "/" +
        datesplit[0] +
        " " +
        timesplit[0] +
        ":" +
        timesplit[1];
  } else if (select == "dd/MM/YYYY") {
    return datesplit[2] + "/" + datesplit[1] + "/" + datesplit[0];
  } else if (select == "DDB") {
    return datesplit[2] + "-" + datesplit[1] + "-" + datesplit[0];
  } else if (select == "DTDB") {
    return datesplit[2] +
        "-" +
        datesplit[1] +
        "-" +
        datesplit[0] +
        " " +
        datetimeSplit[1];
  } else if (select == "T") {
    return datetimeSplit[1];
  } else if (select == "TH") {
    return timesplit[0];
  } else if (select == "TM") {
    return timesplit[1];
  } else if (select == "TS") {
    return timesplit[2];
  } else if (select == "THM") {
    return timesplit[0] + ":" + timesplit[1];
  } else {
    return "select not fond";
  }
}

cuttext(text, numText) {
  var numstring = text.length;
  if (numstring > numText) {
    return text.substring(0, numText) + "...";
  } else {
    return text;
  }
}

convertdateThai(date) {
  var datetimesplit = date.split("T");
  var datesplit = datetimesplit[0].split("-");
  var monththai = "";
  if (datesplit[1] == "01") {
    monththai = "มกราคม";
  } else if (datesplit[1] == "02") {
    monththai = "กุมภาพันธ์";
  } else if (datesplit[1] == "03") {
    monththai = "มีนาคม";
  } else if (datesplit[1] == "04") {
    monththai = "เมษายน";
  } else if (datesplit[1] == "05") {
    monththai = "พฤษภาคม";
  } else if (datesplit[1] == "06") {
    monththai = "มิถุนายน";
  } else if (datesplit[1] == "07") {
    monththai = "กรกฎาคม";
  } else if (datesplit[1] == "08") {
    monththai = "สิงหาคม";
  } else if (datesplit[1] == "09") {
    monththai = "กันยายน";
  } else if (datesplit[1] == "10") {
    monththai = "ตุลาคม";
  } else if (datesplit[1] == "11") {
    monththai = "พฤศจิกายน";
  } else if (datesplit[1] == "12") {
    monththai = "ธันวาคม";
  }

  var datenew = datesplit[2] +
      " " +
      monththai +
      " " +
      (int.parse(datesplit[0]) + 543).toString();
  return datenew;
}

convertdateENG(date) {
  var datetimesplit = date.split("T");
  var datesplit = datetimesplit[0].split("-");
  var montheng = "";
  if (datesplit[1] == "01") {
    montheng = "January";
  } else if (datesplit[1] == "02") {
    montheng = "February";
  } else if (datesplit[1] == "03") {
    montheng = "March";
  } else if (datesplit[1] == "04") {
    montheng = "April";
  } else if (datesplit[1] == "05") {
    montheng = "May";
  } else if (datesplit[1] == "06") {
    montheng = "June";
  } else if (datesplit[1] == "07") {
    montheng = "July";
  } else if (datesplit[1] == "08") {
    montheng = "August";
  } else if (datesplit[1] == "09") {
    montheng = "September";
  } else if (datesplit[1] == "10") {
    montheng = "October";
  } else if (datesplit[1] == "11") {
    montheng = "November";
  } else if (datesplit[1] == "12") {
    montheng = "December";
  }

  var datenew = datesplit[2] + " " + montheng + " " + datesplit[0];
  return datenew;
}

alerterror(context, textMessage) {
  showDialog<String>(
    context: context,
    builder: (BuildContext context) => AlertDialog(
      title: const Text('Error'),
      content: Text(textMessage.toString()),
      actions: <Widget>[
        // TextButton(
        //   onPressed: () => Navigator.pop(context, 'Cancel'),
        //   child: const Text('Cancel'),
        // ),
        TextButton(
          onPressed: () => Navigator.pop(context, 'OK'),
          child: const Text('OK'),
        ),
      ],
    ),
  );
}

info(context, textMessage) {
  showDialog<String>(
    context: context,
    builder: (BuildContext context) => AlertDialog(
      title: const Text('Info'),
      content: Text(textMessage.toString()),
      actions: <Widget>[
        // TextButton(
        //   onPressed: () => Navigator.pop(context, 'Cancel'),
        //   child: const Text('Cancel'),
        // ),
        TextButton(
          onPressed: () => Navigator.pop(context, 'OK'),
          child: const Text('OK'),
        ),
      ],
    ),
  );
}

success(context, textMessage) {
  showDialog<String>(
    context: context,
    builder: (BuildContext context) => AlertDialog(
      title: const Text('Success'),
      content: Text(textMessage.toString()),
      actions: <Widget>[
        // TextButton(
        //   onPressed: () => Navigator.pop(context, 'Cancel'),
        //   child: const Text('Cancel'),
        // ),
        TextButton(
          onPressed: () => Navigator.pop(context, 'OK'),
          child: const Text('OK'),
        ),
      ],
    ),
  );
}
