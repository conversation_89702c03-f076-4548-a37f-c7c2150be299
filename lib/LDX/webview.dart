import 'dart:async';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_analytics/observer.dart';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

class WebViewContainer extends StatefulWidget {
  final url;
  final textappbar;

  WebViewContainer(this.url, this.textappbar);
  @override
  createState() => _WebViewContainerState(this.url, this.textappbar);
}

class _WebViewContainerState extends State<WebViewContainer> {
  final Completer<WebViewController> _controller = Completer<WebViewController>();
  var _url;
  var textappbar;
  final _key = UniqueKey();
  _WebViewContainerState(this._url, this.textappbar);

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;
  static FirebaseAnalyticsObserver observer =
  FirebaseAnalyticsObserver(analytics: analytics);

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    analytics.setCurrentScreen(screenName: "WebViewLDX");
  }

  @override
  Widget build(BuildContext context) {
    final height = MediaQuery
        .of(context)
        .size
        .height;
    final width = MediaQuery
        .of(context)
        .size
        .width;
    return SafeArea(
      child: Scaffold(
//        appBar: new AppBar(
//          title: Text(textappbar),
//          backgroundColor: Colors.grey,
//        ),
          body: Stack(children: <Widget>[Column(
            children: [
              Expanded(
                  child: WebView(
                    key: _key,
                    javascriptMode: JavascriptMode.unrestricted,
                    initialUrl: _url,
                    onWebViewCreated: (WebViewController webViewController) {
                      _controller.complete(webViewController);
                    },
                  ))
            ],
          ),
            Container(alignment: Alignment.topLeft,height: 60,width: width*0.5,padding: EdgeInsets.fromLTRB(0, 20, 0, 0),child: IconButton(icon: Icon(Icons.backspace), onPressed: () {
              Navigator.pop(context);}),),
            Container(alignment: Alignment.topLeft,height: 60,width: width,margin: EdgeInsets.fromLTRB(40, 0, 0, 0),padding: EdgeInsets.fromLTRB(0, 20, 0, 0),child: FutureBuilder<WebViewController>(
                future: _controller.future,
                builder: (BuildContext context,
                    AsyncSnapshot<WebViewController> controller) {
                  if (controller.hasData) {
                    return IconButton(icon: Icon(Icons.refresh), onPressed: () {
                      controller.data!.reload();});
                  }
                  return Container();
                }),),
          ],)),
    );
  }
}
