import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dio/dio.dart';

import 'package:likewallet/LDX/library.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_analytics/observer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:likewallet/app_config.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:likewallet/libraryman/configuration_service.dart';
import 'package:likewallet/libraryman/ethcontractv2.dart';
import 'package:load/load.dart';
import 'package:shared_preferences/shared_preferences.dart';
// import 'package:sort/sort.dart';

class dashboardPoi extends StatefulWidget {
  @override
  _dashboardPoiState createState() => _dashboardPoiState();
}

class _dashboardPoiState extends State<dashboardPoi> {
  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;
  static FirebaseAnalyticsObserver observer =
      FirebaseAnalyticsObserver(analytics: analytics);

  int statusBTNSelectAll = 1;
  int statusBTNSelectLock = 0;
  int statusBTNSelectNoLock = 0;
  late BaseETHV2 eth2;
  late BaseAuth auth;
  late IConfigurationService configETH;
  Dio dio = new Dio();

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    analytics.setCurrentScreen(screenName: "dashboardPOI");
    auth = new Auth();
    eth2 = new EthContractV2();
    loadBalance();
    loadInfo();
    getTime();
  }

  var avatar =
      "https://s3-ap-southeast-1.amazonaws.com/storage-pkg/icon/icon1548914956default.png";
  var fname = "";
  var idldx = "";
  var level = "";
  double likebalance = 0.0;
  double likebalanceLock = 0.0;
  double likebalanceUnLock = 0.0;
  String phoneNumber = '';

  loadInfo() async {
    auth.getCurrentUser().then((user) {
      if (!mounted) return;
      FirebaseFirestore.instance
          .collection('users')
          .doc(user!.uid)
          .get()
          .then((DocumentSnapshot<Map<String, dynamic>> ds) {
        setState(() {
          fname = ds.data()!["firstName"] + " " + ds.data()!["lastName"];
          phoneNumber = user.phoneNumber.toString();
          avatar = ds.data()!["imageProfile"] ?? '';
        });
        FirebaseFirestore.instance
            .collection('users')
            .where('refCode', isEqualTo: ds.data()!["selfCode"])
            .snapshots()
            .listen((data) {
          setState(() {
            // refers = data.docs.length;
          });
        });
      });
    });
    setimgLevel();
  }

  Future<double> getLCMP({required String address}) async {
    Response response = await dio.post(
        "https://" + env.apiUrl + "/getAutocompound",
        data: {"address": address});
    if (response.data["statusCode"] == 200) {
      return double.parse(response.data["result"].toString());
    } else {
      return 0.0;
    }
  }

  Future<double> getLPCU({required String address}) async {
    Response resultLPCU = await dio
        .post("https://" + env.apiUrl + "/getLPCU", data: {"address": address});
    if (resultLPCU.data["statusCode"] == 200) {
      return double.parse(resultLPCU.data["result"].toString());
    } else {
      return 0.0;
    }
  }

  loadBalance() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    configETH = new ConfigurationService(pref);
    final address = await configETH.getAddress();
    final balance = await eth2.getBalance(address: address);
    final auto = await eth2.getBalanceLockAuto(address: address);
    final lock = await eth2.getBalanceLock(address: address);
    final lockAuto = (auto - lock);
    final lockLCMP = await getLCMP(address: address);
    final lockLPCU = await getLPCU(address: address);
    final lockAll = lockAuto + lock + lockLCMP + lockLPCU;
    print(lock);
    print(lockLCMP);
    print(lockLPCU);
    print(lockAuto);
    setState(() {
      likebalance = balance + lockAll;
      likebalanceLock = lockAll;
      likebalanceUnLock = balance.toDouble();
    });
    loadHistory();
  }

  var datetime = "00-00-0000T00:00:00";
  var numdate = "0";
  getTime() async {
    String url = 'http://devdev.prachakij.com/nuiAPI/LDX/gettime.php';
//    String url = 'https://a0df7e51.ngrok.io/agslearn/us-central1/ApiAppMS24';
    Map map = {"menu": "gettime"};

    final response = await apiRequest(url, map);
    print(response);
    var jsonResponse = json.decode(response);
    print(jsonResponse["date"]);
    if (jsonResponse["date"] != "") {
      setState(() {
        datetime = jsonResponse["date"];
        numdate = jsonResponse["numdate"];
      });
    } else {
      print("not get time");
    }
  }

  var imgLevel = "assets/images/dashboardpoi/basic.svg";
  setimgLevel() {
    if (level.toString() == "MASTER") {
      imgLevel = "assets/images/dashboardpoi/master.svg";
    } else if (level.toString() == "PRO") {
      imgLevel = "assets/images/dashboardpoi/pro.svg";
    } else if (level.toString() == "PRO") {
      imgLevel = "assets/images/dashboardpoi/standard.svg";
    } else {
      imgLevel = "assets/images/dashboardpoi/basic.svg";
    }
  }

  // List dataDashboard = [
  //   {
  //     "countDownload": 0,
  //     "persenDownloadDay": "0.00",
  //     "persenDownloadSeven": "0.00",
  //     "countDownloadLikeSeven": "0.00",
  //     "countRecom": 0,
  //     "persenRecomDay": "0.00",
  //     "persenRecomSeven": "0.00",
  //     "countRecomLikeSeven": "0.00",
  //     "countGame": 0,
  //     "persenGameDay": "0.00",
  //     "persenGameSeven": "0.00",
  //     "countGameLikeSeven": "0.00",
  //     "countLotto": 0,
  //     "persenLottoDay": "0.00",
  //     "persenLottoSeven": "0.00",
  //     "countLottoLikeSeven": "0.00",
  //     "countADS": 0,
  //     "persenADSDay": "0.00",
  //     "persenADSSeven": "0.00",
  //     "countADSLikeSeven": "0.00",
  //     "countMR": 0,
  //     "persenMRDay": "0.00",
  //     "persenMRSeven": "0.00",
  //     "countMRLikeSeven": "0.00",
  //     "countShareADS": 0,
  //     "persenShareADSDay": "0.00",
  //     "persenShareADSSeven": "0.00",
  //     "countShareADSLikeSeven": "0.00"
  //   }
  // ];

  List dataDashboard = [
    {
      "nameAct": "Download",
      "count": 0,
      "persenDay": "0.00",
      "persenSeven": "0.00",
      "countLikeSeven": "0.00"
    },
    {
      "nameAct": "Recom",
      "count": 0,
      "persenDay": "0.00",
      "persenSeven": "0.00",
      "countLikeSeven": "0.00"
    },
    {
      "nameAct": "Game",
      "count": 0,
      "persenDay": "0.00",
      "persenSeven": "0.00",
      "countLikeSeven": "0.00"
    },
    {
      "nameAct": "Lotto",
      "count": 0,
      "persenDay": "0.00",
      "persenSeven": "0.00",
      "countLikeSeven": "0.00"
    },
    {
      "nameAct": "ADS",
      "count": 0,
      "persenDay": "0.00",
      "persenSeven": "0.00",
      "countLikeSeven": "0.00"
    },
    {
      "nameAct": "MR",
      "count": 0,
      "persenDay": "0.00",
      "persenSeven": "0.00",
      "countLikeSeven": "0.00"
    },
    {
      "nameAct": "ShareADS",
      "count": 0,
      "persenDay": "0.00",
      "persenSeven": "0.00",
      "countLikeSeven": "0.00"
    }
  ];

  loadHistory() async {
    showLoadingDialog();
    // SharedPreferences prefs = await SharedPreferences.getInstance();
    // var phone_firebase = await prefs.getString('phone_firebase');
    // var likebalance = await prefs.getString('likebalance');

    String url =
        'https://1xpf4khsld.execute-api.ap-southeast-1.amazonaws.com/latest';
    Map map = {
      "menu": "getDashboardPOI",
      "phone_firebase": phoneNumber,
      "likepoint": likebalance.toString()
    };

    final response = await apiRequest(url, map);
    var jsonResponse = json.decode(response);
    if (jsonResponse["statusCode"].toString() == "200") {
      print(jsonResponse["result"]);
      setState(() {
        dataDashboard = jsonResponse["result"];
      });
      setPositionImg();
      hideLoadingDialog();
    } else {
      print(jsonResponse["msg"].toString());
      Fluttertoast.showToast(
          msg: jsonResponse["msg"].toString(),
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.TOP,
          // timeInSecForIos: 1,
          backgroundColor: Colors.red,
          textColor: Colors.black,
          fontSize: 16.0);
      hideLoadingDialog();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Center(
            child: Container(
              decoration: BoxDecoration(
                  gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [Color(0xff374452), Color(0xff000000)])),
            ),
          ),
          Container(
            margin: EdgeInsets.only(
              top: mediaQuery1(context, "h", 219),
              left: mediaQuery1(context, "w", 84),
            ),
            child: GestureDetector(
              onTap: () {
                Navigator.of(context).pop();
              },
              child: Container(
                alignment: Alignment.center,
                color: Colors.transparent,
                width: mediaQuery1(context, "h", 100),
                height: mediaQuery1(context, "h", 100),
                child: SvgPicture.string(
                  '<svg viewBox="0.0 0.0 20.0 32.0" ><path transform="translate(-3664.4, -626.0)" d="M 3684.397216796875 625.9999389648438 L 3664.397216796875 642 L 3684.397216796875 657.9998779296875" fill="none" stroke="#fffd00" stroke-width="5" stroke-linecap="round" stroke-linejoin="round" /></svg>',
                  allowDrawingOutsideViewBox: true,
                  fit: BoxFit.fill,
                  height: mediaQuery1(context, "h", 32),
                ),
              ),
            ),
          ),
          Container(
            margin: EdgeInsets.only(
              top: mediaQuery1(context, "h", 237),
              left: mediaQuery1(context, "w", 183.1),
            ),
            child: SvgPicture.string(
              '<svg viewBox="183.1 237.0 110.0 88.0" ><defs><filter id="shadow"><feDropShadow dx="0" dy="3" stdDeviation="10"/></filter><linearGradient id="gradient" x1="0.5" y1="0.0" x2="0.5" y2="1.0"><stop offset="0.0" stop-color="#ff28eae7"  /><stop offset="0.756587" stop-color="#4d28eae7" stop-opacity="0.3" /><stop offset="1.0" stop-color="#0028eae7" stop-opacity="0.0" /></linearGradient></defs><path transform="translate(183.13, 237.0)" d="M 90.75004577636719 77.00003814697266 L 19.25000953674316 77.00003814697266 C 17.73750877380371 77.00003814697266 16.50000953674316 78.23753356933594 16.50000953674316 79.75003814697266 L 16.50000953674316 85.25003814697266 C 16.50000953674316 86.76255035400391 17.73750877380371 88.00004577636719 19.25000953674316 88.00004577636719 L 90.75004577636719 88.00004577636719 C 92.26255035400391 88.00004577636719 93.50004577636719 86.76255035400391 93.50004577636719 85.25003814697266 L 93.50004577636719 79.75003814697266 C 93.50004577636719 78.23753356933594 92.26255035400391 77.00003814697266 90.75004577636719 77.00003814697266 Z M 101.7500534057617 22.0000114440918 C 97.19536590576172 22.0000114440918 93.50004577636719 25.69532585144043 93.50004577636719 30.2500171661377 C 93.50004577636719 31.47032928466797 93.77503967285156 32.60470581054688 94.25629425048828 33.65314102172852 L 81.81253814697266 41.11252212524414 C 79.16566467285156 42.69377517700195 75.74534606933594 41.80002212524414 74.21566772460938 39.1187744140625 L 60.20783615112305 14.60938167572021 C 62.04690551757812 13.09688091278076 63.25003433227539 10.82812976837158 63.25003433227539 8.250004768371582 C 63.25003433227539 3.695314168930054 59.55471420288086 0 55.00002670288086 0 C 50.44533920288086 0 46.75002288818359 3.695314168930054 46.75002288818359 8.250004768371582 C 46.75002288818359 10.82812976837158 47.95315170288086 13.09688091278076 49.79221725463867 14.60938167572021 L 35.78439331054688 39.11876678466797 C 34.25470352172852 41.80001449584961 30.81720352172852 42.69376754760742 28.1875171661377 41.11251831054688 L 15.76094532012939 33.65313720703125 C 16.22500801086426 32.62189102172852 16.5171947479248 31.4703254699707 16.5171947479248 30.25001335144043 C 16.5171947479248 25.69532203674316 12.82188129425049 22.00000953674316 8.267191886901855 22.00000953674316 C 3.712501049041748 22.00000953674316 0 25.69532585144043 0 30.2500171661377 C 0 34.80470657348633 3.695314168930054 38.50001907348633 8.250004768371582 38.50001907348633 C 8.696879386901855 38.50001907348633 9.143754959106445 38.43126678466797 9.573442459106445 38.36251831054688 L 22.0000114440918 71.50003051757812 L 88.00004577636719 71.50003051757812 L 100.4266128540039 38.36251831054688 C 100.8563003540039 38.43126678466797 101.3031845092773 38.50001907348633 101.7500534057617 38.50001907348633 C 106.3047409057617 38.50001907348633 110.0000534057617 34.80470657348633 110.0000534057617 30.2500171661377 C 110.0000534057617 25.69532585144043 106.3047409057617 22.0000114440918 101.7500534057617 22.0000114440918 Z" fill="url(#gradient)" stroke="#212830" stroke-width="5" stroke-linecap="round" stroke-linejoin="round" filter="url(#shadow)"/></svg>',
              allowDrawingOutsideViewBox: true,
              fit: BoxFit.fill,
              height: mediaQuery1(context, "h", 88),
            ),
          ),
          Container(
            alignment: Alignment.topRight,
            child: Container(
              margin: EdgeInsets.only(
                top: mediaQuery1(context, "h", 127),
                right: mediaQuery1(context, "w", 58),
              ),
              child: SvgPicture.asset(
                "assets/ldx/imgGraf.svg",
                fit: BoxFit.fill,
                height: mediaQuery1(context, "h", 249),
              ),
            ),
          ),
          Container(
            alignment: Alignment.topCenter,
            margin: EdgeInsets.only(top: mediaQuery1(context, "h", 159)),
            child: Text(
              AppLocalizations.of(context)!
                  .translate('ui_dpoiHead1')
                  .toString(),
              style: TextStyle(
                fontFamily: 'Prompt-Regular',
                fontSize: mediaQuery1(context, "h", 46),
                color: const Color(0xe5ffffff),
                letterSpacing: mediaQuery1(context, "h", 2.3000000000000003),
                height: 0.5869565217391305,
                shadows: [
                  Shadow(
                    color: const Color(0x33000000),
                    offset: Offset(0, 2),
                    blurRadius: 3,
                  )
                ],
              ),
              textHeightBehavior:
                  TextHeightBehavior(applyHeightToFirstAscent: false),
              textAlign: TextAlign.center,
            ),
          ),
          Container(
            alignment: Alignment.topCenter,
            margin: EdgeInsets.only(top: mediaQuery1(context, "h", 220)),
            child: Text(
              AppLocalizations.of(context)!
                  .translate('ui_dpoiHead2')
                  .toString(),
              style: TextStyle(
                fontFamily: 'Prompt-Regular',
                fontSize: mediaQuery1(context, "h", 40),
                color: const Color(0xffd0d0d0),
                letterSpacing: mediaQuery1(context, "h", 1.6),
                height: 1.3,
                shadows: [
                  Shadow(
                    color: const Color(0xff000000),
                    offset: Offset(0, 1),
                    blurRadius: 1,
                  )
                ],
              ),
              textHeightBehavior:
                  TextHeightBehavior(applyHeightToFirstAscent: false),
              textAlign: TextAlign.center,
            ),
          ),
          Container(
            alignment: Alignment.bottomCenter,
            child: Container(
              height: mediaQuery1(context, "h", 2010),
              decoration: BoxDecoration(
                color: Colors.black,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(mediaQuery1(context, "h", 60)),
                  topRight: Radius.circular(mediaQuery1(context, "h", 60)),
                ),
              ),
            ),
          ),
          Container(
            alignment: Alignment.bottomCenter,
            child: Container(
              height: mediaQuery1(context, "h", 2005),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(mediaQuery1(context, "h", 60)),
                  topRight: Radius.circular(mediaQuery1(context, "h", 60)),
                ),
                gradient: LinearGradient(
                  begin: Alignment(0.0, -1.0),
                  end: Alignment(0.0, 1.0),
                  colors: [const Color(0xff40525d), const Color(0xff000000)],
                  stops: [0.0, 1.0],
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0x34000000),
                    offset: Offset(0, -5),
                    blurRadius: 10,
                  ),
                ],
              ),
            ),
          ),
          Container(
            margin: EdgeInsets.only(
                top: mediaQuery1(context, "h", 390),
                left: mediaQuery1(context, "w", 49)),
            child: Container(
              width: mediaQuery1(context, "h", 150),
              height: mediaQuery1(context, "h", 150),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: Color(0xff2E2E2E),
                borderRadius:
                    BorderRadius.all(Radius.elliptical(9999.0, 9999.0)),
              ),
              child: Container(
                width: mediaQuery1(context, "h", 143),
                height: mediaQuery1(context, "h", 143),
                decoration: BoxDecoration(
                  borderRadius:
                      BorderRadius.all(Radius.elliptical(9999.0, 9999.0)),
                  image: DecorationImage(
                    image: NetworkImage(avatar),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
          ),
          Container(
            margin: EdgeInsets.only(
                top: mediaQuery1(context, "h", 387),
                left: mediaQuery1(context, "w", 150)),
            child: SvgPicture.asset(
              imgLevel,
              color: Color(0xff212830),
              fit: BoxFit.fitHeight,
              height: mediaQuery1(context, "h", 55.6),
            ),
          ),
          Container(
            margin: EdgeInsets.only(
                top: mediaQuery1(context, "h", 390),
                left: mediaQuery1(context, "w", 228)),
            child: Text(
              fname.toString(),
              style: TextStyle(
                fontFamily: 'Prompt-Medium',
                fontSize: mediaQuery1(context, "h", 35),
                color: const Color(0xffffffff),
                letterSpacing: mediaQuery1(context, "h", 1.75),
                fontWeight: FontWeight.w500,
                height: 1.4285714285714286,
              ),
              textHeightBehavior:
                  TextHeightBehavior(applyHeightToFirstAscent: false),
              textAlign: TextAlign.left,
            ),
          ),
          Container(
            margin: EdgeInsets.only(
                top: mediaQuery1(context, "h", 453),
                left: mediaQuery1(context, "w", 228)),
            child: Text(
              idldx.toString(),
              style: TextStyle(
                fontFamily: 'Prompt-Regular',
                fontSize: mediaQuery1(context, "h", 30),
                color: const Color(0xe7ffffff),
                letterSpacing: mediaQuery1(context, "h", 3),
                shadows: [
                  Shadow(
                    color: const Color(0x29000000),
                    offset: Offset(0, 2),
                    blurRadius: 2,
                  )
                ],
              ),
              textAlign: TextAlign.center,
            ),
          ),
          Container(
            margin: EdgeInsets.only(
                top: mediaQuery1(context, "h", 500),
                left: mediaQuery1(context, "w", 228)),
            child: Text(
              level.toString(),
              style: TextStyle(
                fontFamily: 'Prompt-Medium',
                fontSize: mediaQuery1(context, "h", 35),
                color: const Color(0xff28eae7),
                letterSpacing: mediaQuery1(context, "h", 7),
                fontWeight: FontWeight.w500,
                height: 1.4857142857142858,
                shadows: [
                  Shadow(
                    color: const Color(0xff000000),
                    offset: Offset(0, 1),
                    blurRadius: 1,
                  )
                ],
              ),
              textHeightBehavior:
                  TextHeightBehavior(applyHeightToFirstAscent: false),
              textAlign: TextAlign.left,
            ),
          ),
          Container(
            alignment: Alignment.topRight,
            margin: EdgeInsets.only(
                top: mediaQuery1(context, "h", 390),
                right: mediaQuery1(context, "w", 70)),
            child: Text(
              'คะแนนไลค์พอยท์',
              style: TextStyle(
                fontFamily: 'Prompt-Medium',
                fontSize: mediaQuery1(context, "h", 35),
                color: const Color(0xe4ffffff),
                letterSpacing: mediaQuery1(context, "h", 1.75),
                fontWeight: FontWeight.w500,
                height: 1.4285714285714286,
                shadows: [
                  Shadow(
                    color: const Color(0x80000000),
                    offset: Offset(0, 3),
                    blurRadius: 10,
                  )
                ],
              ),
              textHeightBehavior:
                  TextHeightBehavior(applyHeightToFirstAscent: false),
              textAlign: TextAlign.left,
            ),
          ),
          Container(
            alignment: Alignment.topRight,
            margin: EdgeInsets.only(
                top: mediaQuery1(context, "h", 464),
                right: mediaQuery1(context, "w", 161)),
            child: Text(
              statusBTNSelectAll == 1
                  ? likebalance.toStringAsFixed(2).replaceAllMapped(
                      new RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                      (Match m) => '${m[1]},')
                  : statusBTNSelectLock == 1
                      ? likebalanceLock.toStringAsFixed(2).replaceAllMapped(
                          new RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                          (Match m) => '${m[1]},')
                      : statusBTNSelectNoLock == 1
                          ? likebalanceUnLock
                              .toStringAsFixed(2)
                              .replaceAllMapped(
                                  new RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                                  (Match m) => '${m[1]},')
                          : likebalance.toStringAsFixed(2).replaceAllMapped(
                              new RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                              (Match m) => '${m[1]},'),
              style: TextStyle(
                fontFamily: 'Prompt-Regular',
                fontSize: mediaQuery1(context, "h", 65),
                color: const Color(0xffffffff),
                letterSpacing: mediaQuery1(context, "h", 2.6),
                height: 0.8769230769230769,
                shadows: [
                  Shadow(
                    color: const Color(0x80000000),
                    blurRadius: 10,
                  )
                ],
              ),
              textHeightBehavior:
                  TextHeightBehavior(applyHeightToFirstAscent: false),
              textAlign: TextAlign.right,
            ),
          ),
          Container(
            alignment: Alignment.topRight,
            margin: EdgeInsets.only(
                top: mediaQuery1(context, "h", 490),
                right: mediaQuery1(context, "w", 67)),
            child: Text(
              AppLocalizations.of(context)!.translate('ui_dpoiLike').toString(),
              style: TextStyle(
                fontFamily: 'Prompt-Medium',
                fontSize: mediaQuery1(context, "h", 31),
                color: const Color(0xff00eae7),
                letterSpacing: mediaQuery1(context, "h", 1.55),
                fontWeight: FontWeight.w500,
              ),
              textHeightBehavior:
                  TextHeightBehavior(applyHeightToFirstAscent: false),
              textAlign: TextAlign.left,
            ),
          ),
          Container(
            alignment: Alignment.topRight,
            margin: EdgeInsets.only(
                top: mediaQuery1(context, "h", 561),
                right: mediaQuery1(context, "w", 336)),
            child: statusBTNSelectAll == 1
                ? GestureDetector(
                    onTap: () {
                      setState(() {
                        statusBTNSelectAll = 1;
                        statusBTNSelectLock = 0;
                        statusBTNSelectNoLock = 0;
                      });
                    },
                    child: Text(
                      AppLocalizations.of(context)!
                          .translate('ui_dpoiAll')
                          .toString(),
                      style: TextStyle(
                        fontFamily: 'Prompt-Regular',
                        fontSize: mediaQuery1(context, "h", 33),
                        color: const Color(0xff28eae7),
                        letterSpacing:
                            mediaQuery1(context, "h", 1.6500000000000001),
                        height: 1.5151515151515151,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.right,
                    ),
                  )
                : GestureDetector(
                    onTap: () {
                      setState(() {
                        statusBTNSelectAll = 1;
                        statusBTNSelectLock = 0;
                        statusBTNSelectNoLock = 0;
                      });
                    },
                    child: Text(
                      AppLocalizations.of(context)!
                          .translate('ui_dpoiAll')
                          .toString(),
                      style: TextStyle(
                        fontFamily: 'Prompt-Light',
                        fontSize: mediaQuery1(context, "h", 33),
                        color: const Color(0xcdffffff),
                        letterSpacing:
                            mediaQuery1(context, "h", 1.6500000000000001),
                        fontWeight: FontWeight.w300,
                        height: 1.5151515151515151,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.left,
                    ),
                  ),
          ),
          Container(
            alignment: Alignment.topRight,
            margin: EdgeInsets.only(
                top: mediaQuery1(context, "h", 561),
                right: mediaQuery1(context, "w", 222)),
            child: statusBTNSelectLock == 1
                ? GestureDetector(
                    onTap: () {
                      setState(() {
                        statusBTNSelectAll = 0;
                        statusBTNSelectLock = 1;
                        statusBTNSelectNoLock = 0;
                      });
                    },
                    child: Text(
                      AppLocalizations.of(context)!
                          .translate('ui_dpoiLock')
                          .toString(),
                      style: TextStyle(
                        fontFamily: 'Prompt-Regular',
                        fontSize: mediaQuery1(context, "h", 33),
                        color: const Color(0xff28eae7),
                        letterSpacing:
                            mediaQuery1(context, "h", 1.6500000000000001),
                        height: 1.5151515151515151,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.right,
                    ),
                  )
                : GestureDetector(
                    onTap: () {
                      setState(() {
                        statusBTNSelectAll = 0;
                        statusBTNSelectLock = 1;
                        statusBTNSelectNoLock = 0;
                      });
                    },
                    child: Text(
                      AppLocalizations.of(context)!
                          .translate('ui_dpoiLock')
                          .toString(),
                      style: TextStyle(
                        fontFamily: 'Prompt-Light',
                        fontSize: mediaQuery1(context, "h", 33),
                        color: const Color(0xcdffffff),
                        letterSpacing:
                            mediaQuery1(context, "h", 1.6500000000000001),
                        fontWeight: FontWeight.w300,
                        height: 1.5151515151515151,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.left,
                    ),
                  ),
          ),
          Container(
            alignment: Alignment.topRight,
            margin: EdgeInsets.only(
                top: mediaQuery1(context, "h", 561),
                right: mediaQuery1(context, "w", 64)),
            child: statusBTNSelectNoLock == 1
                ? GestureDetector(
                    onTap: () {
                      setState(() {
                        statusBTNSelectAll = 0;
                        statusBTNSelectLock = 0;
                        statusBTNSelectNoLock = 1;
                      });
                    },
                    child: Text(
                      AppLocalizations.of(context)!
                          .translate('ui_dpoiNoLock')
                          .toString(),
                      style: TextStyle(
                        fontFamily: 'Prompt-Regular',
                        fontSize: mediaQuery1(context, "h", 33),
                        color: const Color(0xff28eae7),
                        letterSpacing:
                            mediaQuery1(context, "h", 1.6500000000000001),
                        height: 1.5151515151515151,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.right,
                    ),
                  )
                : GestureDetector(
                    onTap: () {
                      setState(() {
                        statusBTNSelectAll = 0;
                        statusBTNSelectLock = 0;
                        statusBTNSelectNoLock = 1;
                      });
                    },
                    child: Text(
                      'ไม่ล็อค',
                      style: TextStyle(
                        fontFamily: 'Prompt-Light',
                        fontSize: mediaQuery1(context, "h", 33),
                        color: const Color(0xcdffffff),
                        letterSpacing:
                            mediaQuery1(context, "h", 1.6500000000000001),
                        fontWeight: FontWeight.w300,
                        height: 1.5151515151515151,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.left,
                    ),
                  ),
          ),
          Container(
            alignment: Alignment.topCenter,
            margin: EdgeInsets.only(top: mediaQuery1(context, "h", 645)),
            child: Text(
              AppLocalizations.of(context)!
                  .translate('ui_dpoiStatusUpdate')
                  .toString(),
              style: TextStyle(
                fontFamily: 'Prompt-Regular',
                fontSize: mediaQuery1(context, "h", 40),
                color: const Color(0xffd0d0d0),
                letterSpacing: mediaQuery1(context, "h", 1.6),
                height: 1.25,
              ),
              textHeightBehavior:
                  TextHeightBehavior(applyHeightToFirstAscent: false),
              textAlign: TextAlign.center,
            ),
          ),
          Container(
            margin: EdgeInsets.only(
                top: mediaQuery1(context, "h", 726),
                left: mediaQuery1(context, "h", 367)),
            child: Container(
              width: mediaQuery1(context, "h", 15),
              height: mediaQuery1(context, "h", 15),
              decoration: BoxDecoration(
                borderRadius:
                    BorderRadius.all(Radius.elliptical(9999.0, 9999.0)),
                color: const Color(0xff28eae7),
              ),
            ),
          ),
          Container(
            alignment: Alignment.topRight,
            margin: EdgeInsets.only(
                top: mediaQuery1(context, "h", 711),
                right: mediaQuery1(context, "h", 372)),
            child: Text(
              AppLocalizations.of(context)!
                      .translate('ui_dpoiUpdate')
                      .toString() +
                  ' ' +
                  convertDateTime(datetime, "dd-mm-yy"),
              style: TextStyle(
                fontFamily: 'Prompt-Light',
                fontSize: mediaQuery1(context, "h", 30),
                color: const Color(0xcdffffff),
                letterSpacing: mediaQuery1(context, "h", 1.5),
                fontWeight: FontWeight.w300,
                height: 1.6666666666666667,
                shadows: [
                  Shadow(
                    color: const Color(0xff000000),
                    offset: Offset(0, 3),
                    blurRadius: 5,
                  )
                ],
              ),
              textHeightBehavior:
                  TextHeightBehavior(applyHeightToFirstAscent: false),
              textAlign: TextAlign.left,
            ),
          ),
          // **************************** box ***************************
          Container(
            alignment: Alignment.topCenter,
            margin: EdgeInsets.only(top: mediaQuery1(context, "h", 800)),
            child: Container(
              width: mediaQuery1(context, "w", 950),
              height: mediaQuery1(context, "h", 147),
              decoration: BoxDecoration(
                borderRadius:
                    BorderRadius.circular(mediaQuery1(context, "h", 40)),
                color: const Color(0xff000000),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0x4d000000),
                    offset: Offset(0, 3),
                    blurRadius: 12,
                  ),
                ],
              ),
              child: Stack(
                children: [
                  Container(
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 18),
                        left: mediaQuery1(context, "w", 161)),
                    child: Text(
                      setNameAct(dataDashboard[0]["nameAct"].toString()),
                      style: TextStyle(
                        fontFamily: 'Prompt-Medium',
                        fontSize: mediaQuery1(context, "h", 35),
                        color: const Color(0xffd0d0d0),
                        letterSpacing: mediaQuery1(context, "h", 1.75),
                        fontWeight: FontWeight.w500,
                        height: 1.4285714285714286,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.left,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topRight,
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 23),
                        right: mediaQuery1(context, "w", 281)),
                    child: Text.rich(
                      TextSpan(
                        style: TextStyle(
                          fontFamily: 'Prompt-Regular',
                          fontSize: mediaQuery1(context, "h", 30),
                          color: const Color(0xff28eae7),
                          letterSpacing: mediaQuery1(context, "h", 1.5),
                          height: 1.7857142857142858,
                        ),
                        children: [
                          TextSpan(
                            text: dataDashboard[0]["count"].toString(),
                          ),
                          TextSpan(
                            text: ' ',
                            style: TextStyle(
                              fontSize: mediaQuery1(context, "h", 26),
                              letterSpacing: mediaQuery1(context, "h", 1.3),
                            ),
                          ),
                          TextSpan(
                            text:
                                setUnit(dataDashboard[0]["nameAct"].toString()),
                            style: TextStyle(
                              fontSize: mediaQuery1(context, "h", 28),
                              color: const Color(0xff999999),
                              letterSpacing:
                                  mediaQuery1(context, "h", 1.4000000000000001),
                            ),
                          ),
                        ],
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.right,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topRight,
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 22),
                        right: mediaQuery1(context, "w", 44)),
                    child: Text(
                      AppLocalizations.of(context)!
                          .translate('ui_dpoiRecLikepoint')
                          .toString(),
                      style: TextStyle(
                        fontFamily: 'Prompt-Regular',
                        fontSize: mediaQuery1(context, "h", 28),
                        color: const Color(0xff999999),
                        letterSpacing:
                            mediaQuery1(context, "h", 1.4000000000000001),
                        height: 1.7857142857142858,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.right,
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 72),
                        left: mediaQuery1(context, "w", 161)),
                    child: Text(
                      dataDashboard[0]["persenSeven"].toString() + '%',
                      style: TextStyle(
                        fontFamily: 'Prompt-Regular',
                        fontSize: mediaQuery1(context, "h", 50),
                        color: const Color(0xffffe200),
                        letterSpacing: mediaQuery1(context, "h", 2),
                        height: 1.14,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.left,
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 88),
                        left: mediaQuery1(context, "w", 400)),
                    child: Text(
                      '+' + dataDashboard[0]["persenDay"].toString(),
                      style: TextStyle(
                        fontFamily: 'Prompt-Light',
                        fontSize: mediaQuery1(context, "h", 33),
                        color: const Color(0xff28eae7),
                        letterSpacing:
                            mediaQuery1(context, "h", 1.6500000000000001),
                        fontWeight: FontWeight.w300,
                        height: 1.5151515151515151,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.left,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topRight,
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 70),
                        right: mediaQuery1(context, "w", 119)),
                    child: Text(
                      dataDashboard[0]["countLikeSeven"].toString(),
                      style: TextStyle(
                        fontFamily: 'Prompt-Regular',
                        fontSize: mediaQuery1(context, "h", 50),
                        color: const Color(0xffd0d0d0),
                        letterSpacing: mediaQuery1(context, "h", 2),
                        height: 1.14,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.right,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topRight,
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 85),
                        right: mediaQuery1(context, "w", 42)),
                    child: Text(
                      AppLocalizations.of(context)!
                          .translate('ui_dpoiLike')
                          .toString(),
                      style: TextStyle(
                        fontFamily: 'Prompt-Medium',
                        fontSize: mediaQuery1(context, "h", 31),
                        color: const Color(0xff00eae7),
                        letterSpacing: mediaQuery1(context, "h", 1.55),
                        fontWeight: FontWeight.w500,
                        height: 1.1290322580645162,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.left,
                    ),
                  ),
                ],
              ),
            ),
          ),
          Container(
            alignment: Alignment.topCenter,
            margin: EdgeInsets.only(top: mediaQuery1(context, "h", 972)),
            child: Container(
              width: mediaQuery1(context, "w", 950),
              height: mediaQuery1(context, "h", 147),
              decoration: BoxDecoration(
                borderRadius:
                    BorderRadius.circular(mediaQuery1(context, "h", 40)),
                color: const Color(0xff000000),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0x4d000000),
                    offset: Offset(0, 3),
                    blurRadius: 12,
                  ),
                ],
              ),
              child: Stack(
                children: [
                  Container(
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 18),
                        left: mediaQuery1(context, "w", 161)),
                    child: Text(
                      setNameAct(dataDashboard[1]["nameAct"].toString()),
                      style: TextStyle(
                        fontFamily: 'Prompt-Medium',
                        fontSize: mediaQuery1(context, "h", 35),
                        color: const Color(0xffd0d0d0),
                        letterSpacing: mediaQuery1(context, "h", 1.75),
                        fontWeight: FontWeight.w500,
                        height: 1.4285714285714286,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.left,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topRight,
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 23),
                        right: mediaQuery1(context, "w", 281)),
                    child: Text.rich(
                      TextSpan(
                        style: TextStyle(
                          fontFamily: 'Prompt-Regular',
                          fontSize: mediaQuery1(context, "h", 30),
                          color: const Color(0xff28eae7),
                          letterSpacing: mediaQuery1(context, "h", 1.5),
                          height: 1.7857142857142858,
                        ),
                        children: [
                          TextSpan(
                            text: dataDashboard[1]["count"].toString(),
                          ),
                          TextSpan(
                            text: ' ',
                            style: TextStyle(
                              fontSize: mediaQuery1(context, "h", 26),
                              letterSpacing: mediaQuery1(context, "h", 1.3),
                            ),
                          ),
                          TextSpan(
                            text:
                                setUnit(dataDashboard[1]["nameAct"].toString()),
                            style: TextStyle(
                              fontSize: mediaQuery1(context, "h", 28),
                              color: const Color(0xff999999),
                              letterSpacing:
                                  mediaQuery1(context, "h", 1.4000000000000001),
                            ),
                          ),
                        ],
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.right,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topRight,
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 22),
                        right: mediaQuery1(context, "w", 44)),
                    child: Text(
                      AppLocalizations.of(context)!
                          .translate('ui_dpoiRecLikepoint')
                          .toString(),
                      style: TextStyle(
                        fontFamily: 'Prompt-Regular',
                        fontSize: mediaQuery1(context, "h", 28),
                        color: const Color(0xff999999),
                        letterSpacing:
                            mediaQuery1(context, "h", 1.4000000000000001),
                        height: 1.7857142857142858,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.right,
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 72),
                        left: mediaQuery1(context, "w", 161)),
                    child: Text(
                      dataDashboard[1]["persenSeven"].toString() + '%',
                      style: TextStyle(
                        fontFamily: 'Prompt-Regular',
                        fontSize: mediaQuery1(context, "h", 50),
                        color: const Color(0xff6bffd9),
                        letterSpacing: mediaQuery1(context, "h", 2),
                        height: 1.14,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.left,
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 88),
                        left: mediaQuery1(context, "w", 400)),
                    child: Text(
                      '+' + dataDashboard[1]["persenDay"].toString(),
                      style: TextStyle(
                        fontFamily: 'Prompt-Light',
                        fontSize: mediaQuery1(context, "h", 33),
                        color: const Color(0xff28eae7),
                        letterSpacing:
                            mediaQuery1(context, "h", 1.6500000000000001),
                        fontWeight: FontWeight.w300,
                        height: 1.5151515151515151,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.left,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topRight,
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 70),
                        right: mediaQuery1(context, "w", 119)),
                    child: Text(
                      dataDashboard[1]["countLikeSeven"].toString(),
                      style: TextStyle(
                        fontFamily: 'Prompt-Regular',
                        fontSize: mediaQuery1(context, "h", 50),
                        color: const Color(0xffd0d0d0),
                        letterSpacing: mediaQuery1(context, "h", 2),
                        height: 1.14,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.right,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topRight,
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 85),
                        right: mediaQuery1(context, "w", 42)),
                    child: Text(
                      AppLocalizations.of(context)!
                          .translate('ui_dpoiLike')
                          .toString(),
                      style: TextStyle(
                        fontFamily: 'Prompt-Medium',
                        fontSize: mediaQuery1(context, "h", 31),
                        color: const Color(0xff00eae7),
                        letterSpacing: mediaQuery1(context, "h", 1.55),
                        fontWeight: FontWeight.w500,
                        height: 1.1290322580645162,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.left,
                    ),
                  ),
                ],
              ),
            ),
          ),
          Container(
            alignment: Alignment.topCenter,
            margin: EdgeInsets.only(top: mediaQuery1(context, "h", 1144)),
            child: Container(
              width: mediaQuery1(context, "w", 950),
              height: mediaQuery1(context, "h", 147),
              decoration: BoxDecoration(
                borderRadius:
                    BorderRadius.circular(mediaQuery1(context, "h", 40)),
                color: const Color(0xff000000),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0x4d000000),
                    offset: Offset(0, 3),
                    blurRadius: 12,
                  ),
                ],
              ),
              child: Stack(
                children: [
                  Container(
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 18),
                        left: mediaQuery1(context, "w", 161)),
                    child: Text(
                      setNameAct(dataDashboard[2]["nameAct"].toString()),
                      style: TextStyle(
                        fontFamily: 'Prompt-Medium',
                        fontSize: mediaQuery1(context, "h", 35),
                        color: const Color(0xffd0d0d0),
                        letterSpacing: mediaQuery1(context, "h", 1.75),
                        fontWeight: FontWeight.w500,
                        height: 1.4285714285714286,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.left,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topRight,
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 23),
                        right: mediaQuery1(context, "w", 281)),
                    child: Text.rich(
                      TextSpan(
                        style: TextStyle(
                          fontFamily: 'Prompt-Regular',
                          fontSize: mediaQuery1(context, "h", 30),
                          color: const Color(0xff28eae7),
                          letterSpacing: mediaQuery1(context, "h", 1.5),
                          height: 1.7857142857142858,
                        ),
                        children: [
                          TextSpan(
                            text: dataDashboard[2]["count"].toString(),
                          ),
                          TextSpan(
                            text: ' ',
                            style: TextStyle(
                              fontSize: mediaQuery1(context, "h", 26),
                              letterSpacing: mediaQuery1(context, "h", 1.3),
                            ),
                          ),
                          TextSpan(
                            text:
                                setUnit(dataDashboard[2]["nameAct"].toString()),
                            style: TextStyle(
                              fontSize: mediaQuery1(context, "h", 28),
                              color: const Color(0xff999999),
                              letterSpacing:
                                  mediaQuery1(context, "h", 1.4000000000000001),
                            ),
                          ),
                        ],
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.right,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topRight,
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 22),
                        right: mediaQuery1(context, "w", 44)),
                    child: Text(
                      AppLocalizations.of(context)!
                          .translate('ui_dpoiRecLikepoint')
                          .toString(),
                      style: TextStyle(
                        fontFamily: 'Prompt-Regular',
                        fontSize: mediaQuery1(context, "h", 28),
                        color: const Color(0xff999999),
                        letterSpacing:
                            mediaQuery1(context, "h", 1.4000000000000001),
                        height: 1.7857142857142858,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.right,
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 72),
                        left: mediaQuery1(context, "w", 161)),
                    child: Text(
                      dataDashboard[2]["persenSeven"].toString() + '%',
                      style: TextStyle(
                        fontFamily: 'Prompt-Regular',
                        fontSize: mediaQuery1(context, "h", 50),
                        color: const Color(0xff6bffd9),
                        letterSpacing: mediaQuery1(context, "h", 2),
                        height: 1.14,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.left,
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 88),
                        left: mediaQuery1(context, "w", 400)),
                    child: Text(
                      '+' + dataDashboard[2]["persenDay"].toString(),
                      style: TextStyle(
                        fontFamily: 'Prompt-Light',
                        fontSize: mediaQuery1(context, "h", 33),
                        color: const Color(0xff28eae7),
                        letterSpacing:
                            mediaQuery1(context, "h", 1.6500000000000001),
                        fontWeight: FontWeight.w300,
                        height: 1.5151515151515151,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.left,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topRight,
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 70),
                        right: mediaQuery1(context, "w", 119)),
                    child: Text(
                      dataDashboard[2]["countLikeSeven"].toString(),
                      style: TextStyle(
                        fontFamily: 'Prompt-Regular',
                        fontSize: mediaQuery1(context, "h", 50),
                        color: const Color(0xffd0d0d0),
                        letterSpacing: mediaQuery1(context, "h", 2),
                        height: 1.14,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.right,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topRight,
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 85),
                        right: mediaQuery1(context, "w", 42)),
                    child: Text(
                      AppLocalizations.of(context)!
                          .translate('ui_dpoiLike')
                          .toString(),
                      style: TextStyle(
                        fontFamily: 'Prompt-Medium',
                        fontSize: mediaQuery1(context, "h", 31),
                        color: const Color(0xff00eae7),
                        letterSpacing: mediaQuery1(context, "h", 1.55),
                        fontWeight: FontWeight.w500,
                        height: 1.1290322580645162,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.left,
                    ),
                  ),
                ],
              ),
            ),
          ),
          Container(
            alignment: Alignment.topCenter,
            margin: EdgeInsets.only(top: mediaQuery1(context, "h", 1316)),
            child: Container(
              width: mediaQuery1(context, "w", 950),
              height: mediaQuery1(context, "h", 147),
              decoration: BoxDecoration(
                borderRadius:
                    BorderRadius.circular(mediaQuery1(context, "h", 40)),
                color: const Color(0xff000000),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0x4d000000),
                    offset: Offset(0, 3),
                    blurRadius: 12,
                  ),
                ],
              ),
              child: Stack(
                children: [
                  Container(
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 29),
                        left: mediaQuery1(context, "w", 161)),
                    child: Text(
                      setNameAct(dataDashboard[3]["nameAct"].toString()),
                      style: TextStyle(
                        fontFamily: 'Prompt-Medium',
                        fontSize: mediaQuery1(context, "h", 35),
                        color: const Color(0xffd0d0d0),
                        letterSpacing: mediaQuery1(context, "h", 1.75),
                        fontWeight: FontWeight.w500,
                        height: 1.4285714285714286,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.left,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topRight,
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 23),
                        right: mediaQuery1(context, "w", 281)),
                    child: Text.rich(
                      TextSpan(
                        style: TextStyle(
                          fontFamily: 'Prompt-Regular',
                          fontSize: mediaQuery1(context, "h", 30),
                          color: const Color(0xff28eae7),
                          letterSpacing: mediaQuery1(context, "h", 1.5),
                          height: 1.7857142857142858,
                        ),
                        children: [
                          TextSpan(
                            text: dataDashboard[3]["count"].toString(),
                          ),
                          TextSpan(
                            text: ' ',
                            style: TextStyle(
                              fontSize: mediaQuery1(context, "h", 26),
                              letterSpacing: mediaQuery1(context, "h", 1.3),
                            ),
                          ),
                          TextSpan(
                            text:
                                setUnit(dataDashboard[3]["nameAct"].toString()),
                            style: TextStyle(
                              fontSize: mediaQuery1(context, "h", 28),
                              color: const Color(0xff999999),
                              letterSpacing:
                                  mediaQuery1(context, "h", 1.4000000000000001),
                            ),
                          ),
                        ],
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.right,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topRight,
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 22),
                        right: mediaQuery1(context, "w", 44)),
                    child: Text(
                      AppLocalizations.of(context)!
                          .translate('ui_dpoiRecLikepoint')
                          .toString(),
                      style: TextStyle(
                        fontFamily: 'Prompt-Regular',
                        fontSize: mediaQuery1(context, "h", 28),
                        color: const Color(0xff999999),
                        letterSpacing:
                            mediaQuery1(context, "h", 1.4000000000000001),
                        height: 1.7857142857142858,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.right,
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 72),
                        left: mediaQuery1(context, "w", 161)),
                    child: Text(
                      dataDashboard[3]["persenSeven"].toString() + '%',
                      style: TextStyle(
                        fontFamily: 'Prompt-Regular',
                        fontSize: mediaQuery1(context, "h", 50),
                        color: const Color(0xff13a89d),
                        letterSpacing: mediaQuery1(context, "h", 2),
                        height: 1.14,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.left,
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 88),
                        left: mediaQuery1(context, "w", 400)),
                    child: Text(
                      '+' + dataDashboard[3]["persenDay"].toString(),
                      style: TextStyle(
                        fontFamily: 'Prompt-Light',
                        fontSize: mediaQuery1(context, "h", 33),
                        color: const Color(0xff28eae7),
                        letterSpacing:
                            mediaQuery1(context, "h", 1.6500000000000001),
                        fontWeight: FontWeight.w300,
                        height: 1.5151515151515151,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.left,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topRight,
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 70),
                        right: mediaQuery1(context, "w", 119)),
                    child: Text(
                      dataDashboard[3]["countLikeSeven"].toString(),
                      style: TextStyle(
                        fontFamily: 'Prompt-Regular',
                        fontSize: mediaQuery1(context, "h", 50),
                        color: const Color(0xffd0d0d0),
                        letterSpacing: mediaQuery1(context, "h", 2),
                        height: 1.14,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.right,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topRight,
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 85),
                        right: mediaQuery1(context, "w", 42)),
                    child: Text(
                      AppLocalizations.of(context)!
                          .translate('ui_dpoiLike')
                          .toString(),
                      style: TextStyle(
                        fontFamily: 'Prompt-Medium',
                        fontSize: mediaQuery1(context, "h", 31),
                        color: const Color(0xff00eae7),
                        letterSpacing: mediaQuery1(context, "h", 1.55),
                        fontWeight: FontWeight.w500,
                        height: 1.1290322580645162,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.left,
                    ),
                  ),
                ],
              ),
            ),
          ),
          Container(
            alignment: Alignment.topCenter,
            margin: EdgeInsets.only(top: mediaQuery1(context, "h", 1488)),
            child: Container(
              width: mediaQuery1(context, "w", 950),
              height: mediaQuery1(context, "h", 147),
              decoration: BoxDecoration(
                borderRadius:
                    BorderRadius.circular(mediaQuery1(context, "h", 40)),
                color: const Color(0xff000000),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0x4d000000),
                    offset: Offset(0, 3),
                    blurRadius: 12,
                  ),
                ],
              ),
              child: Stack(
                children: [
                  Container(
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 18),
                        left: mediaQuery1(context, "w", 161)),
                    child: Text(
                      setNameAct(dataDashboard[4]["nameAct"].toString()),
                      style: TextStyle(
                        fontFamily: 'Prompt-Medium',
                        fontSize: mediaQuery1(context, "h", 35),
                        color: const Color(0xffd0d0d0),
                        letterSpacing: mediaQuery1(context, "h", 1.75),
                        fontWeight: FontWeight.w500,
                        height: 1.4285714285714286,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.left,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topRight,
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 23),
                        right: mediaQuery1(context, "w", 281)),
                    child: Text.rich(
                      TextSpan(
                        style: TextStyle(
                          fontFamily: 'Prompt-Regular',
                          fontSize: mediaQuery1(context, "h", 30),
                          color: const Color(0xff28eae7),
                          letterSpacing: mediaQuery1(context, "h", 1.5),
                          height: 1.7857142857142858,
                        ),
                        children: [
                          TextSpan(
                            text: dataDashboard[4]["count"].toString(),
                          ),
                          TextSpan(
                            text: ' ',
                            style: TextStyle(
                              fontSize: mediaQuery1(context, "h", 26),
                              letterSpacing: mediaQuery1(context, "h", 1.3),
                            ),
                          ),
                          TextSpan(
                            text:
                                setUnit(dataDashboard[4]["nameAct"].toString()),
                            style: TextStyle(
                              fontSize: mediaQuery1(context, "h", 28),
                              color: const Color(0xff999999),
                              letterSpacing:
                                  mediaQuery1(context, "h", 1.4000000000000001),
                            ),
                          ),
                        ],
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.right,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topRight,
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 22),
                        right: mediaQuery1(context, "w", 44)),
                    child: Text(
                      AppLocalizations.of(context)!
                          .translate('ui_dpoiRecLikepoint')
                          .toString(),
                      style: TextStyle(
                        fontFamily: 'Prompt-Regular',
                        fontSize: mediaQuery1(context, "h", 28),
                        color: const Color(0xff999999),
                        letterSpacing:
                            mediaQuery1(context, "h", 1.4000000000000001),
                        height: 1.7857142857142858,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.right,
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 72),
                        left: mediaQuery1(context, "w", 161)),
                    child: Text(
                      dataDashboard[4]["persenSeven"].toString() + '%',
                      style: TextStyle(
                        fontFamily: 'Prompt-Regular',
                        fontSize: mediaQuery1(context, "h", 50),
                        color: const Color(0xff707070),
                        letterSpacing: mediaQuery1(context, "h", 2),
                        height: 1.14,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.left,
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 88),
                        left: mediaQuery1(context, "w", 400)),
                    child: Text(
                      '+' + dataDashboard[4]["persenDay"].toString(),
                      style: TextStyle(
                        fontFamily: 'Prompt-Light',
                        fontSize: mediaQuery1(context, "h", 33),
                        color: const Color(0xff28eae7),
                        letterSpacing:
                            mediaQuery1(context, "h", 1.6500000000000001),
                        fontWeight: FontWeight.w300,
                        height: 1.5151515151515151,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.left,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topRight,
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 70),
                        right: mediaQuery1(context, "w", 119)),
                    child: Text(
                      dataDashboard[4]["countLikeSeven"].toString(),
                      style: TextStyle(
                        fontFamily: 'Prompt-Regular',
                        fontSize: mediaQuery1(context, "h", 50),
                        color: const Color(0xffd0d0d0),
                        letterSpacing: mediaQuery1(context, "h", 2),
                        height: 1.14,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.right,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topRight,
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 85),
                        right: mediaQuery1(context, "w", 42)),
                    child: Text(
                      AppLocalizations.of(context)!
                          .translate('ui_dpoiLike')
                          .toString(),
                      style: TextStyle(
                        fontFamily: 'Prompt-Medium',
                        fontSize: mediaQuery1(context, "h", 31),
                        color: const Color(0xff00eae7),
                        letterSpacing: mediaQuery1(context, "h", 1.55),
                        fontWeight: FontWeight.w500,
                        height: 1.1290322580645162,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.left,
                    ),
                  ),
                ],
              ),
            ),
          ),
          Container(
            alignment: Alignment.topCenter,
            margin: EdgeInsets.only(top: mediaQuery1(context, "h", 1660)),
            child: Container(
              width: mediaQuery1(context, "w", 950),
              height: mediaQuery1(context, "h", 147),
              decoration: BoxDecoration(
                borderRadius:
                    BorderRadius.circular(mediaQuery1(context, "h", 40)),
                color: const Color(0xff000000),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0x4d000000),
                    offset: Offset(0, 3),
                    blurRadius: 12,
                  ),
                ],
              ),
              child: Stack(
                children: [
                  Container(
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 16),
                        left: mediaQuery1(context, "w", 161)),
                    child: Text(
                      setNameAct(dataDashboard[5]["nameAct"].toString()),
                      style: TextStyle(
                        fontFamily: 'Prompt-Medium',
                        fontSize: mediaQuery1(context, "h", 35),
                        color: const Color(0xffd0d0d0),
                        letterSpacing: mediaQuery1(context, "h", 1.75),
                        fontWeight: FontWeight.w500,
                        height: 1.4285714285714286,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.left,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topRight,
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 23),
                        right: mediaQuery1(context, "w", 281)),
                    child: Text.rich(
                      TextSpan(
                        style: TextStyle(
                          fontFamily: 'Prompt-Regular',
                          fontSize: mediaQuery1(context, "h", 30),
                          color: const Color(0xff28eae7),
                          letterSpacing: mediaQuery1(context, "h", 1.5),
                          height: 1.7857142857142858,
                        ),
                        children: [
                          TextSpan(
                            text: dataDashboard[5]["count"].toString(),
                          ),
                          TextSpan(
                            text: ' ',
                            style: TextStyle(
                              fontSize: mediaQuery1(context, "h", 26),
                              letterSpacing: mediaQuery1(context, "h", 1.3),
                            ),
                          ),
                          TextSpan(
                            text:
                                setUnit(dataDashboard[5]["nameAct"].toString()),
                            style: TextStyle(
                              fontSize: mediaQuery1(context, "h", 28),
                              color: const Color(0xff999999),
                              letterSpacing:
                                  mediaQuery1(context, "h", 1.4000000000000001),
                            ),
                          ),
                        ],
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.right,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topRight,
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 22),
                        right: mediaQuery1(context, "w", 44)),
                    child: Text(
                      AppLocalizations.of(context)!
                          .translate('ui_dpoiRecLikepoint')
                          .toString(),
                      style: TextStyle(
                        fontFamily: 'Prompt-Regular',
                        fontSize: mediaQuery1(context, "h", 28),
                        color: const Color(0xff999999),
                        letterSpacing:
                            mediaQuery1(context, "h", 1.4000000000000001),
                        height: 1.7857142857142858,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.right,
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 72),
                        left: mediaQuery1(context, "w", 161)),
                    child: Text(
                      dataDashboard[5]["persenSeven"].toString() + '%',
                      style: TextStyle(
                        fontFamily: 'Prompt-Regular',
                        fontSize: mediaQuery1(context, "h", 50),
                        color: const Color(0xff707070),
                        letterSpacing: mediaQuery1(context, "h", 2),
                        height: 1.14,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.left,
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 88),
                        left: mediaQuery1(context, "w", 400)),
                    child: Text(
                      '+' + dataDashboard[5]["persenDay"].toString(),
                      style: TextStyle(
                        fontFamily: 'Prompt-Light',
                        fontSize: mediaQuery1(context, "h", 33),
                        color: const Color(0xff28eae7),
                        letterSpacing:
                            mediaQuery1(context, "h", 1.6500000000000001),
                        fontWeight: FontWeight.w300,
                        height: 1.5151515151515151,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.left,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topRight,
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 70),
                        right: mediaQuery1(context, "w", 119)),
                    child: Text(
                      dataDashboard[5]["countLikeSeven"].toString(),
                      style: TextStyle(
                        fontFamily: 'Prompt-Regular',
                        fontSize: mediaQuery1(context, "h", 50),
                        color: const Color(0xffd0d0d0),
                        letterSpacing: mediaQuery1(context, "h", 2),
                        height: 1.14,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.right,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topRight,
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 85),
                        right: mediaQuery1(context, "w", 42)),
                    child: Text(
                      AppLocalizations.of(context)!
                          .translate('ui_dpoiLike')
                          .toString(),
                      style: TextStyle(
                        fontFamily: 'Prompt-Medium',
                        fontSize: mediaQuery1(context, "h", 31),
                        color: const Color(0xff00eae7),
                        letterSpacing: mediaQuery1(context, "h", 1.55),
                        fontWeight: FontWeight.w500,
                        height: 1.1290322580645162,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.left,
                    ),
                  ),
                ],
              ),
            ),
          ),
          Container(
            alignment: Alignment.topCenter,
            margin: EdgeInsets.only(top: mediaQuery1(context, "h", 1832)),
            child: Container(
              width: mediaQuery1(context, "w", 950),
              height: mediaQuery1(context, "h", 147),
              decoration: BoxDecoration(
                borderRadius:
                    BorderRadius.circular(mediaQuery1(context, "h", 40)),
                color: const Color(0xff000000),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0x4d000000),
                    offset: Offset(0, 3),
                    blurRadius: 12,
                  ),
                ],
              ),
              child: Stack(
                children: [
                  Container(
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 18),
                        left: mediaQuery1(context, "w", 161)),
                    child: Text(
                      setNameAct(dataDashboard[6]["nameAct"].toString()),
                      style: TextStyle(
                        fontFamily: 'Prompt-Medium',
                        fontSize: mediaQuery1(context, "h", 35),
                        color: const Color(0xffd0d0d0),
                        letterSpacing: mediaQuery1(context, "h", 1.75),
                        fontWeight: FontWeight.w500,
                        height: 1.4285714285714286,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.left,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topRight,
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 23),
                        right: mediaQuery1(context, "w", 281)),
                    child: Text.rich(
                      TextSpan(
                        style: TextStyle(
                          fontFamily: 'Prompt-Regular',
                          fontSize: mediaQuery1(context, "h", 30),
                          color: const Color(0xff28eae7),
                          letterSpacing: mediaQuery1(context, "h", 1.5),
                          height: 1.7857142857142858,
                        ),
                        children: [
                          TextSpan(
                            text: dataDashboard[6]["count"].toString(),
                          ),
                          TextSpan(
                            text: ' ',
                            style: TextStyle(
                              fontSize: mediaQuery1(context, "h", 26),
                              letterSpacing: mediaQuery1(context, "h", 1.3),
                            ),
                          ),
                          TextSpan(
                            text:
                                setUnit(dataDashboard[6]["nameAct"].toString()),
                            style: TextStyle(
                              fontSize: mediaQuery1(context, "h", 28),
                              color: const Color(0xff999999),
                              letterSpacing:
                                  mediaQuery1(context, "h", 1.4000000000000001),
                            ),
                          ),
                        ],
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.right,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topRight,
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 22),
                        right: mediaQuery1(context, "w", 44)),
                    child: Text(
                      AppLocalizations.of(context)!
                          .translate('ui_dpoiRecLikepoint')
                          .toString(),
                      style: TextStyle(
                        fontFamily: 'Prompt-Regular',
                        fontSize: mediaQuery1(context, "h", 28),
                        color: const Color(0xff999999),
                        letterSpacing:
                            mediaQuery1(context, "h", 1.4000000000000001),
                        height: 1.7857142857142858,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.right,
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 72),
                        left: mediaQuery1(context, "w", 161)),
                    child: Text(
                      dataDashboard[6]["persenSeven"].toString() + '%',
                      style: TextStyle(
                        fontFamily: 'Prompt-Regular',
                        fontSize: mediaQuery1(context, "h", 50),
                        color: const Color(0xff707070),
                        letterSpacing: mediaQuery1(context, "h", 2),
                        height: 1.14,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.left,
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 88),
                        left: mediaQuery1(context, "w", 400)),
                    child: Text(
                      '+' + dataDashboard[6]["persenDay"].toString(),
                      style: TextStyle(
                        fontFamily: 'Prompt-Light',
                        fontSize: mediaQuery1(context, "h", 33),
                        color: const Color(0xff28eae7),
                        letterSpacing:
                            mediaQuery1(context, "h", 1.6500000000000001),
                        fontWeight: FontWeight.w300,
                        height: 1.5151515151515151,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.left,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topRight,
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 70),
                        right: mediaQuery1(context, "w", 119)),
                    child: Text(
                      dataDashboard[6]["countLikeSeven"].toString(),
                      style: TextStyle(
                        fontFamily: 'Prompt-Regular',
                        fontSize: mediaQuery1(context, "h", 50),
                        color: const Color(0xffd0d0d0),
                        letterSpacing: mediaQuery1(context, "h", 2),
                        height: 1.14,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.right,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topRight,
                    margin: EdgeInsets.only(
                        top: mediaQuery1(context, "h", 85),
                        right: mediaQuery1(context, "w", 42)),
                    child: Text(
                      AppLocalizations.of(context)!
                          .translate('ui_dpoiLike')
                          .toString(),
                      style: TextStyle(
                        fontFamily: 'Prompt-Medium',
                        fontSize: mediaQuery1(context, "h", 31),
                        color: const Color(0xff00eae7),
                        letterSpacing: mediaQuery1(context, "h", 1.55),
                        fontWeight: FontWeight.w500,
                        height: 1.1290322580645162,
                      ),
                      textHeightBehavior:
                          TextHeightBehavior(applyHeightToFirstAscent: false),
                      textAlign: TextAlign.left,
                    ),
                  ),
                ],
              ),
            ),
          ),
          // **************************** box ***************************
          // **************************** img font box ***************************
          Container(
            margin: EdgeInsets.only(
                top: mediaQuery1(context, "h", positionImg1),
                left: mediaQuery1(context, "w", 60)),
            child: SvgPicture.asset(
              "assets/images/dashboardpoi/download.svg",
              fit: BoxFit.fitHeight,
              height: mediaQuery1(context, "h", 129.64),
            ),
          ),
          Container(
            margin: EdgeInsets.only(
                top: mediaQuery1(context, "h", positionImg2),
                left: mediaQuery1(context, "w", 53.5)),
            child: SvgPicture.asset(
              "assets/images/dashboardpoi/recomfriend.svg",
              fit: BoxFit.fitHeight,
              height: mediaQuery1(context, "h", 131.08),
            ),
          ),
          Container(
            margin: EdgeInsets.only(
                top: mediaQuery1(context, "h", positionImg3),
                left: mediaQuery1(context, "w", 59)),
            child: SvgPicture.asset(
              "assets/images/dashboardpoi/games.svg",
              fit: BoxFit.fitHeight,
              height: mediaQuery1(context, "h", 113.76),
            ),
          ),
          Container(
            margin: EdgeInsets.only(
                top: mediaQuery1(context, "h", positionImg4),
                left: mediaQuery1(context, "w", 35)),
            child: SvgPicture.asset(
              "assets/images/dashboardpoi/lotto.svg",
              fit: BoxFit.fitHeight,
              height: mediaQuery1(context, "h", 130),
            ),
          ),
          Container(
            margin: EdgeInsets.only(
                top: mediaQuery1(context, "h", positionImg5),
                left: mediaQuery1(context, "w", 53)),
            child: SvgPicture.asset(
              "assets/images/dashboardpoi/ads.svg",
              fit: BoxFit.fitHeight,
              height: mediaQuery1(context, "h", 94.7),
            ),
          ),
          Container(
            margin: EdgeInsets.only(
                top: mediaQuery1(context, "h", positionImg6),
                left: mediaQuery1(context, "w", 59)),
            child: SvgPicture.asset(
              "assets/images/dashboardpoi/MR.svg",
              fit: BoxFit.fitHeight,
              height: mediaQuery1(context, "h", 138.05),
            ),
          ),
          Container(
            margin: EdgeInsets.only(
                top: mediaQuery1(context, "h", positionImg7),
                left: mediaQuery1(context, "w", 53)),
            child: SvgPicture.asset(
              "assets/images/dashboardpoi/ads.svg",
              fit: BoxFit.fitHeight,
              height: mediaQuery1(context, "h", 94.7),
            ),
          ),
          // **************************** img font box ***************************
        ],
      ),
    );
  }

  setNameAct(nameAct) {
    if (nameAct.toString() == "Download") {
      return AppLocalizations.of(context)!
          .translate('ui_dpoiDownload')
          .toString();
    } else if (nameAct.toString() == "Recom") {
      return AppLocalizations.of(context)!
          .translate('ui_dpoiRecomfriend')
          .toString();
    } else if (nameAct.toString() == "Game") {
      return AppLocalizations.of(context)!.translate('ui_dpoiGames').toString();
    } else if (nameAct.toString() == "Lotto") {
      return AppLocalizations.of(context)!
          .translate('ui_dpoiLDXLotto')
          .toString();
    } else if (nameAct.toString() == "ADS") {
      return AppLocalizations.of(context)!
          .translate('ui_dpoiWatchADS')
          .toString();
    } else if (nameAct.toString() == "MR") {
      return AppLocalizations.of(context)!.translate('ui_dpoiActMR').toString();
    } else if (nameAct.toString() == "ShareADS") {
      return AppLocalizations.of(context)!
          .translate('ui_dpoiShareADS')
          .toString();
    }
  }

  setUnit(nameAct) {
    if (nameAct.toString() == "Download" ||
        nameAct.toString() == "Recom" ||
        nameAct.toString() == "MR") {
      return AppLocalizations.of(context)!
          .translate('ui_dpoiUnitRai')
          .toString();
    } else {
      return AppLocalizations.of(context)!.translate('ui_dpoiUnitTH');
    }
  }

  double positionImg1 = 817;
  double positionImg2 = 987.9;
  double positionImg3 = 1144;
  double positionImg4 = 1355;
  double positionImg5 = 1504;
  double positionImg6 = 1675;
  double positionImg7 = 1848;
  setPositionImg() {
    if (dataDashboard[0]["nameAct"].toString() == "Download") {
      setState(() {
        positionImg1 = 817;
      });
    } else if (dataDashboard[0]["nameAct"].toString() == "Recom") {
      setState(() {
        positionImg2 = 817;
      });
    } else if (dataDashboard[0]["nameAct"].toString() == "Game") {
      setState(() {
        positionImg3 = 817;
      });
    } else if (dataDashboard[0]["nameAct"].toString() == "Lotto") {
      setState(() {
        positionImg4 = 829;
      });
    } else if (dataDashboard[0]["nameAct"].toString() == "ADS") {
      setState(() {
        positionImg5 = 817;
      });
    } else if (dataDashboard[0]["nameAct"].toString() == "MR") {
      setState(() {
        positionImg6 = 817;
      });
    } else if (dataDashboard[0]["nameAct"].toString() == "ShareADS") {
      setState(() {
        positionImg7 = 817;
      });
    }

    if (dataDashboard[1]["nameAct"].toString() == "Download") {
      setState(() {
        positionImg1 = 987.9;
      });
    } else if (dataDashboard[1]["nameAct"].toString() == "Recom") {
      setState(() {
        positionImg2 = 987.9;
      });
    } else if (dataDashboard[1]["nameAct"].toString() == "Game") {
      setState(() {
        positionImg3 = 987.9;
      });
    } else if (dataDashboard[1]["nameAct"].toString() == "Lotto") {
      setState(() {
        positionImg4 = 999.9;
      });
    } else if (dataDashboard[1]["nameAct"].toString() == "ADS") {
      setState(() {
        positionImg5 = 987.9;
      });
    } else if (dataDashboard[1]["nameAct"].toString() == "MR") {
      setState(() {
        positionImg6 = 987.9;
      });
    } else if (dataDashboard[1]["nameAct"].toString() == "ShareADS") {
      setState(() {
        positionImg7 = 987.9;
      });
    }

    if (dataDashboard[2]["nameAct"].toString() == "Download") {
      setState(() {
        positionImg1 = 1144;
      });
    } else if (dataDashboard[2]["nameAct"].toString() == "Recom") {
      setState(() {
        positionImg2 = 1144;
      });
    } else if (dataDashboard[2]["nameAct"].toString() == "Game") {
      setState(() {
        positionImg3 = 1144;
      });
    } else if (dataDashboard[2]["nameAct"].toString() == "Lotto") {
      setState(() {
        positionImg4 = 1156;
      });
    } else if (dataDashboard[2]["nameAct"].toString() == "ADS") {
      setState(() {
        positionImg5 = 1144;
      });
    } else if (dataDashboard[2]["nameAct"].toString() == "MR") {
      setState(() {
        positionImg6 = 1144;
      });
    } else if (dataDashboard[2]["nameAct"].toString() == "ShareADS") {
      setState(() {
        positionImg7 = 1144;
      });
    }

    if (dataDashboard[3]["nameAct"].toString() == "Download") {
      setState(() {
        positionImg1 = 1343;
      });
    } else if (dataDashboard[3]["nameAct"].toString() == "Recom") {
      setState(() {
        positionImg2 = 1343;
      });
    } else if (dataDashboard[3]["nameAct"].toString() == "Game") {
      setState(() {
        positionImg3 = 1343;
      });
    } else if (dataDashboard[3]["nameAct"].toString() == "Lotto") {
      setState(() {
        positionImg4 = 1355;
      });
    } else if (dataDashboard[3]["nameAct"].toString() == "ADS") {
      setState(() {
        positionImg5 = 1343;
      });
    } else if (dataDashboard[3]["nameAct"].toString() == "MR") {
      setState(() {
        positionImg6 = 1343;
      });
    } else if (dataDashboard[3]["nameAct"].toString() == "ShareADS") {
      setState(() {
        positionImg7 = 1343;
      });
    }

    if (dataDashboard[4]["nameAct"].toString() == "Download") {
      setState(() {
        positionImg1 = 1504;
      });
    } else if (dataDashboard[4]["nameAct"].toString() == "Recom") {
      setState(() {
        positionImg2 = 1504;
      });
    } else if (dataDashboard[4]["nameAct"].toString() == "Game") {
      setState(() {
        positionImg3 = 1504;
      });
    } else if (dataDashboard[4]["nameAct"].toString() == "Lotto") {
      setState(() {
        positionImg4 = 1516;
      });
    } else if (dataDashboard[4]["nameAct"].toString() == "ADS") {
      setState(() {
        positionImg5 = 1504;
      });
    } else if (dataDashboard[4]["nameAct"].toString() == "MR") {
      setState(() {
        positionImg6 = 1504;
      });
    } else if (dataDashboard[4]["nameAct"].toString() == "ShareADS") {
      setState(() {
        positionImg7 = 1504;
      });
    }

    if (dataDashboard[5]["nameAct"].toString() == "Download") {
      setState(() {
        positionImg1 = 1675;
      });
    } else if (dataDashboard[5]["nameAct"].toString() == "Recom") {
      setState(() {
        positionImg2 = 1675;
      });
    } else if (dataDashboard[5]["nameAct"].toString() == "Game") {
      setState(() {
        positionImg3 = 1675;
      });
    } else if (dataDashboard[5]["nameAct"].toString() == "Lotto") {
      setState(() {
        positionImg4 = 1687;
      });
    } else if (dataDashboard[5]["nameAct"].toString() == "ADS") {
      setState(() {
        positionImg5 = 1675;
      });
    } else if (dataDashboard[5]["nameAct"].toString() == "MR") {
      setState(() {
        positionImg6 = 1675;
      });
    } else if (dataDashboard[5]["nameAct"].toString() == "ShareADS") {
      setState(() {
        positionImg7 = 1675;
      });
    }

    if (dataDashboard[6]["nameAct"].toString() == "Download") {
      setState(() {
        positionImg1 = 1848;
      });
    } else if (dataDashboard[6]["nameAct"].toString() == "Recom") {
      setState(() {
        positionImg2 = 1848;
      });
    } else if (dataDashboard[6]["nameAct"].toString() == "Game") {
      setState(() {
        positionImg3 = 1848;
      });
    } else if (dataDashboard[6]["nameAct"].toString() == "Lotto") {
      setState(() {
        positionImg4 = 1860;
      });
    } else if (dataDashboard[6]["nameAct"].toString() == "ADS") {
      setState(() {
        positionImg5 = 1848;
      });
    } else if (dataDashboard[6]["nameAct"].toString() == "MR") {
      setState(() {
        positionImg6 = 1848;
      });
    } else if (dataDashboard[6]["nameAct"].toString() == "ShareADS") {
      setState(() {
        positionImg7 = 1848;
      });
    }
  }
}
