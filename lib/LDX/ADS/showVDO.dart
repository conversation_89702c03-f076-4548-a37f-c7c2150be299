import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_analytics/observer.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:video_player/video_player.dart';

import '../library.dart';

class showVDO extends StatefulWidget {
  final running_ads;
  final imageads;
  final subjectads;
  final dataads;
  final nameActivities;
  final numads;
  final videoURL;

  showVDO(this.running_ads, this.imageads, this.subjectads, this.dataads,
      this.nameActivities, this.numads, this.videoURL);

  @override
  _showVDOState createState() => _showVDOState(
      this.running_ads,
      this.imageads,
      this.subjectads,
      this.dataads,
      this.nameActivities,
      this.numads,
      this.videoURL);
}

class _showVDOState extends State<showVDO> {
  final running_ads;
  final imageads;
  final subjectads;
  final dataads;
  final nameActivities;
  final numads;
  final videoURL;

  _showVDOState(this.running_ads, this.imageads, this.subjectads, this.dataads,
      this.nameActivities, this.numads, this.videoURL);

  int statusShowNumDown = 0;
  bool _saving = false;

  VideoPlayerController? _controller;

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;
  static FirebaseAnalyticsObserver observer =
      FirebaseAnalyticsObserver(analytics: analytics);

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    auth = new Auth();
    analytics.setCurrentScreen(screenName: "showvdoLDX");
    print("666 : " + videoURL.toString());
    _controller = VideoPlayerController.network(videoURL.toString())
      ..initialize().then((_) {
        // Ensure the first frame is shown after the video is initialized, even before the play button has been pressed.
        setState(() {});
      });
    _controller!.play();
    // setLifetime();
    allLoad();
  }

  allLoad() async {
    setState(() {
      _saving = true;
    });
    await setInit();
    await new Future.delayed(const Duration(seconds: 1));
    insertOpenVDO();
    getTime();
    startTimer2();
    setState(() {
      _saving = false;
    });
  }

  late BaseAuth auth;
  String firstName = "..loading";
  String lastName = "";
  String phoneNumber = "";
  String _uploadedFileURL = "noprofile";
  int refers = 0;
  setInit() {
    auth.getCurrentUser().then((user) {
      if (!mounted) return;

      FirebaseFirestore.instance
          .collection('users')
          .doc(user!.uid)
          .get()
          .then((DocumentSnapshot<Map<String, dynamic>> ds) {
        setState(() {
          firstName = ds.data()!["firstName"];
          lastName = ds.data()!["lastName"];
          phoneNumber = user.phoneNumber.toString();
          _uploadedFileURL = ds.data()!["imageProfile"] ?? '';
        });
        FirebaseFirestore.instance
            .collection('users')
            .where('refCode', isEqualTo: ds.data()!["selfCode"])
            .snapshots()
            .listen((data) {
          setState(() {
            refers = data.docs.length;
          });
        });
      });
    });
  }

  var datetime = "00-00-0000T00:00:00";
  var numdate = "0";
  getTime() async {
//      showLoadingDialog();
    String url = 'http://devdev.prachakij.com/nuiAPI/LDX/gettime.php';
//    String url = 'https://a0df7e51.ngrok.io/agslearn/us-central1/ApiAppMS24';
    Map map = {"menu": "gettime"};

    final response = await apiRequest(url, map);
    print(response);
    var jsonResponse = json.decode(response);
    print(jsonResponse["date"]);
    if (jsonResponse["date"] != "") {
      setState(() {
        datetime = jsonResponse["date"];
        numdate = jsonResponse["numdate"];
      });
      searchwatchVDO();
    } else {
      print("not get time");
    }
  }

  insertOpenVDO() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    var ID_LDX = await prefs.getString('ID_LDX');
    var email = await prefs.getString('email');

    String url =
        'https://1xpf4khsld.execute-api.ap-southeast-1.amazonaws.com/latest';
    Map map = {
      "menu": "insertwatchVDOadsldxInLikewallet",
      "running_ads": running_ads,
      "user": email,
      "phone_firebase": phoneNumber,
      "nameActivities": nameActivities,
      "numActivities": numads,
      "subjectADS": subjectads,
      "linkImmageADS": imageads,
      "linkVDO": videoURL.toString(),
      "status_recive_like": "N"
    };

    final response = await apiRequest(url, map);
    var jsonResponse = json.decode(response);
    if (jsonResponse["insertId"].toString() != "") {
      print('บันทึกการเปิด VDO แล้ว');
    } else {
      print('บันทึกการเปิด VDO ไม่สำเร็จ');
    }
  }

  searchwatchVDO() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    var ID_LDX = await prefs.getString('ID_LDX');

    String url =
        'https://1xpf4khsld.execute-api.ap-southeast-1.amazonaws.com/latest';
    Map map = {
      "menu": "searchwatchVDOldxInLikewallet",
      "running_ads": running_ads,
      "phone_firebase": phoneNumber,
      "datecheck": convertDateTime(datetime, "DDB")
    };

    final response = await apiRequest(url, map);
    List jsonResponse = json.decode(response);
    if (jsonResponse.length > 0) {
      print('วันนี้ได้รับ like จาก ADS นี้แล้ว');
      setState(() {
        statusShowNumDown = 1;
      });
    } else {
      print('ยังไม่ได้รับ like จาก VDO นี้');
      startTimer();
    }
  }

  saveReciveLike() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    var email = await prefs.getString('email');
    var ID_LDX = await prefs.getString('ID_LDX');

    String url =
        'https://1xpf4khsld.execute-api.ap-southeast-1.amazonaws.com/latest';
    Map map = {
      "menu": "updatewatchVDOldxInLikewallet",
      "running_ads": running_ads,
      "user": email,
      "phone_firebase": phoneNumber
    };

    final response = await apiRequest(url, map);
    var jsonResponse = json.decode(response);
    print(jsonResponse);
    if (jsonResponse["changedRows"].toString() != "0") {
      print("save saveReciveLike success");
//      hideLoadingDialog();
      Fluttertoast.showToast(
          msg: "บันทึกสำเร็จ",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.TOP,
          backgroundColor: Colors.green,
          textColor: Colors.black,
          fontSize: 16.0);
    } else {
      print('not save ACT !!!');
      Fluttertoast.showToast(
          msg: "บันทึก ReciveLike ไม่สำเร็จ",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.TOP,
          backgroundColor: Colors.red,
          textColor: Colors.black,
          fontSize: 16.0);
//      hideLoadingDialog();
    }
  }

  transferlikeWatchVDO() async {
    // SharedPreferences prefs = await SharedPreferences.getInstance();
    // var level = await prefs.getString('level');

    // if (level == "MASTER") {
    transferLikepointWatchVDO(334);
    //   // transferLikepointAdvisor(356);
    // } else if (level == "PRO") {
    //   transferLikepointWatchVDO(333);
    //   // transferLikepointAdvisor(355);
    // } else if (level == "STANDARD") {
    //   transferLikepointWatchVDO(332);
    //   // transferLikepointAdvisor(354);
    // } else {
    // transferLikepointWatchVDO(331);
    // transferLikepointAdvisor(353);
    // }
  }

  transferLikepointWatchVDO(id_activity) async {
    var jsonResponseloadBuFirstLib = await loadBuFirstLib(phoneNumber);
    if (jsonResponseloadBuFirstLib["statusCode"].toString() == "200") {
      var jsonResponsegetaddressLib = await getaddressLib(phoneNumber);
      if (jsonResponsegetaddressLib["status"].toString() == "200") {
        List jsonResponsesearchACTLib =
            await searchACTLib(id_activity.toString());
        if (jsonResponsesearchACTLib.length > 0) {
          print("search ACT success");
          // transferLifttime("watchVDO",jsonResponsesearchACTLib[0]["point"].toString());
          var jsonResponseinsertactivityNew = await insertactivityNew(
              "LDX",
              phoneNumber.toString(),
              "ภายนอก",
              firstName.toString() + " " + lastName.toString(),
              firstName.toString(),
              lastName.toString(),
              "ภายนอก",
              jsonResponsesearchACTLib[0]["point"].toString(),
              "รอดำเนินการ",
              jsonResponsesearchACTLib[0]["running"].toString(),
              jsonResponsesearchACTLib[0]["titile"].toString(),
              phoneNumber.toString(),
              phoneNumber.toString(),
              firstName.toString() + " " + lastName.toString(),
              firstName.toString(),
              lastName.toString(),
              jsonResponsegetaddressLib["result"]["data"].toString(),
              "NULL",
              "NULL",
              "NULL",
              jsonResponsesearchACTLib[0]["type_bu"].toString(),
              jsonResponsesearchACTLib[0]["type_bu_gr"].toString(),
              jsonResponsesearchACTLib[0]["use_bu"].toString(),
              jsonResponsesearchACTLib[0]["use_bu_gr"].toString(),
              jsonResponsesearchACTLib[0]["use_bag_name"].toString(),
              jsonResponsesearchACTLib[0]["status_check"].toString(),
              jsonResponsesearchACTLib[0]["isAutoClaim"].toString(),
              jsonResponsesearchACTLib[0]["isGojoy"].toString(),
              jsonResponsesearchACTLib[0]["pool_percent"].toString(),
              jsonResponsesearchACTLib[0]["pool"].toString(),
              jsonResponsesearchACTLib[0]["gojoy_id_activity"].toString(),
              jsonResponsesearchACTLib[0]["gojoy_name_activity"].toString(),
              "N",
              "LDX",
              "LDX Digital Ecosystem Co., Ltd",
              "โอน",
              "0xcd40c2f37177594e958f354718cbfa7aebd99864",
              "ส่วนกลาง",
              jsonResponseloadBuFirstLib['result'][0]["owner"].toString(),
              jsonResponsesearchACTLib[0]["tax_per"].toString(),
              jsonResponsesearchACTLib[0]["tax_point"].toString(),
              (jsonResponsesearchACTLib[0]["tax_point"] +
                      jsonResponsesearchACTLib[0]["point"])
                  .toString(),
              "NULL",
              "NULL",
              jsonResponsesearchACTLib[0]["pool_address"].toString());
          if (jsonResponseinsertactivityNew["statusCode"].toString() == "200") {
            print("save ACT success");
            if (_start < 35) {
              saveReciveLike();
            }
            var jsonResponseclaimlikeNew = await claimlikeNew(
                "LDX", jsonResponseinsertactivityNew["data"]);
            if (jsonResponseclaimlikeNew["statusCode"].toString() == "200") {
              Fluttertoast.showToast(
                  msg: "รับ likepoint สำเร็จ",
                  toastLength: Toast.LENGTH_SHORT,
                  gravity: ToastGravity.TOP,
                  backgroundColor: Colors.green,
                  textColor: Colors.black,
                  fontSize: 16.0);
            } else {
              print("not_transfer_like !");
              Fluttertoast.showToast(
                  msg: "ไม่สำเร็จ กรุณากดใหม่อีกครั้ง",
                  toastLength: Toast.LENGTH_SHORT,
                  gravity: ToastGravity.TOP,
                  backgroundColor: Colors.red,
                  textColor: Colors.black,
                  fontSize: 16.0);
            }
          } else {
            print('not save ACT !!!');
          }
        } else {
          print('not search ACT !!!');
        }
      } else {
        alerterror(context,
            "ไม่สามารถค้นหา address like wallet ได้ กรุณาติดต่อทีมงาน");
      }
    } else {
      print('not regis app bu!');
    }
  }

  transferLikepointAdvisor(id_activity) async {
    await getTime();
    SharedPreferences prefs = await SharedPreferences.getInstance();
    var dataAdvisorEmail = await prefs.getString('dataAdvisorEmail');
    var dataAdvisorIdldx = await prefs.getString('dataAdvisorIdldx');
    var dataAdvisorphonefirebase =
        await prefs.getString('dataAdvisorphonefirebase');
    var dataAdvisorFname = await prefs.getString('dataAdvisorFname');
    var dataAdvisorLname = await prefs.getString('dataAdvisorLname');
    var first_login = await prefs.getString('first_login');

    final dateRegis = DateTime(
        int.parse(convertDateTime(first_login, "DBY")),
        int.parse(convertDateTime(first_login, "DBM")),
        int.parse(convertDateTime(first_login, "DBD")));
    final datenow = DateTime(
        int.parse(convertDateTime(datetime, "DY")),
        int.parse(convertDateTime(datetime, "DM")),
        int.parse(convertDateTime(datetime, "DD")));
    final difference = datenow.difference(dateRegis).inDays;

    if (difference < 366) {
      List jsonResponsesearchACTLib =
          await searchACTLib(id_activity.toString());
      if (jsonResponsesearchACTLib.length > 0) {
        print("Advisor : search ACT success");
        var jsonResponsesaveACTLib = await saveACTLib(
            dataAdvisorEmail,
            dataAdvisorIdldx,
            dataAdvisorphonefirebase,
            dataAdvisorFname,
            dataAdvisorLname,
            jsonResponsesearchACTLib[0]["running"].toString(),
            jsonResponsesearchACTLib[0]["titile"].toString(),
            jsonResponsesearchACTLib[0]["point"].toString(),
            jsonResponsesearchACTLib[0]["type_bu"].toString(),
            jsonResponsesearchACTLib[0]["type_bu_gr"].toString(),
            jsonResponsesearchACTLib[0]["use_bu"].toString(),
            jsonResponsesearchACTLib[0]["use_bu_gr"].toString(),
            jsonResponsesearchACTLib[0]["use_bag_name"].toString(),
            jsonResponsesearchACTLib[0]["isAutoClaim"].toString(),
            jsonResponsesearchACTLib[0]["isGojoy"].toString(),
            jsonResponsesearchACTLib[0]["pool_percent"].toString(),
            jsonResponsesearchACTLib[0]["pool"].toString(),
            jsonResponsesearchACTLib[0]["gojoy_id_activity"].toString(),
            jsonResponsesearchACTLib[0]["gojoy_name_activity"].toString());
        if (jsonResponsesaveACTLib["insertId"].toString() != "") {
          print("Advisor : save ACT success");
        } else {
          print('Advisor : not save ACT !!!');
        }
      } else {
        print('Advisor : not search ACT !!!');
      }
    } else {
      print(
          "ผู้แนะนำไม่ได้รับ likepoint แล้เนื่องจากสมาชิกคนนี้สมัครเกิน 1 ปีแล้ว");
    }
  }

  Future<String> apiRequest(String url, Map jsonMap) async {
    HttpClient httpClient = new HttpClient();
    HttpClientRequest request = await httpClient.postUrl(Uri.parse(url));
    request.headers.set('content-type', 'application/json');
    request.headers
        .set('x-api-key', 'hjwsduripfksefghtrwdmngjtieowpsskfgtriuyt');
    request.add(utf8.encode(json.encode(jsonMap)));
    HttpClientResponse response = await request.close();
    // todo - you should check the response.statusCode
    String reply = await response.transform(utf8.decoder).join();
    httpClient.close();
    return reply;
  }

  Timer? _timer;
  int _start = 0;

  void startTimer() {
    const oneSec = const Duration(seconds: 1);
    _timer = new Timer.periodic(
      oneSec,
      (Timer timer) => setState(
        () {
          if (_start >= 120) {
            timer.cancel();
            print("transfer");
            transferlikeWatchVDO();
            // searchACTvdo();
            setState(() {
              statusShowNumDown = 1;
            });
          } else if (_controller!.value.isPlaying.toString() == "false") {
//            timer.cancel();
//            setState(() {
//              statusShowNumDown = 1;
//            });
            Navigator.of(context).pop();
          } else {
            if (_start % 20 == 0 && _start != 0) {
              print("transfer");
              transferlikeWatchVDO();
              // searchACTvdo();
            }
            _start = _start + 1;
          }
        },
      ),
    );
  }

  Timer? _timer2;
  int _start2 = 0;

  void startTimer2() {
    const oneSec = const Duration(seconds: 1);
    _timer2 = new Timer.periodic(
      oneSec,
      (Timer timer2) => setState(
        () {
          if (_controller!.value.isPlaying.toString() == "false") {
            Navigator.of(context).pop();
          } else {
            _start2 = _start2 + 1;
          }
        },
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
    _controller!.dispose();
    _timer!.cancel();
    _timer2!.cancel();
  }

  @override
  Widget build(BuildContext context) {
    final height = MediaQuery.of(context).size.height;
    final width = MediaQuery.of(context).size.width;
    return ModalProgressHUD(
      inAsyncCall: _saving,
      opacity: 0.1,
      progressIndicator: CustomLoading(),
      child: Scaffold(
        backgroundColor: Colors.black,
        body: Stack(
          children: <Widget>[
            new GestureDetector(
              onTap: () {
                Navigator.of(context).pop();
                print("Container clicked");
              },
              child: Container(
                margin: EdgeInsets.fromLTRB(0, 25, 0, 0),
                child: IconButton(
                  icon: Icon(
                    Icons.arrow_back_ios,
                    color: Color(0xffD0D0D0),
                    size: 16,
                  ),
                  onPressed: () {},
                ),
              ),
            ),
            Center(
              child: _controller!.value.isInitialized
                  ? AspectRatio(
                      aspectRatio: _controller!.value.aspectRatio,
                      child: VideoPlayer(_controller!),
                    )
                  : Container(),
            ),
            statusShowNumDown == 1
                ? Container()
                : Container(
                    alignment: Alignment.bottomRight,
                    margin: EdgeInsets.fromLTRB(0, 0, 50, 28),
                    child: Text(
                      "ท่านจะได้รับ LIKE ทุกๆ 20 วินาที แต่ไม่เกิน 120 วินาที ",
                      style: TextStyle(
                          color: Colors.red, fontFamily: 'Prompt-Regular'),
                    ),
                  ),
            statusShowNumDown == 1
                ? Container()
                : Container(
                    alignment: Alignment.bottomRight,
                    margin: EdgeInsets.fromLTRB(0, 0, 20, 20),
                    child: new Container(
                      alignment: Alignment.center,
                      width: 30.0,
                      height: 30.0,
                      decoration: new BoxDecoration(
                        color: Color(0xff00EAE7),
                        shape: BoxShape.circle,
                      ),
                      child: Text(
                        "${_start}",
                        style: TextStyle(fontFamily: 'Prompt-Regular'),
                      ),
                    ),
                  ),
          ],
        ),
      ),
    );
  }
}
