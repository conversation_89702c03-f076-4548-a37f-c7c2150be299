import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:ui';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_analytics/observer.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:likewallet/LDX/ADS/showVDO.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
// import 'package:social_share_plugin/social_share_plugin.dart';
import 'package:url_launcher/url_launcher.dart';

import '../library.dart';
import '../webview.dart';

class showads extends StatefulWidget {
  final running_ads;
  final imageads;
  final subjectads;
  final dataads;
  final nameActivities;
  final numads;
  final fullads;
  var timeout;
  var statusincom;
  final videoURL;
  final statusVDO;
  final link;
  final id_share;
  final statusLink;
  final sortADS;

  showads(
      this.running_ads,
      this.imageads,
      this.subjectads,
      this.dataads,
      this.nameActivities,
      this.numads,
      this.fullads,
      this.timeout,
      this.statusincom,
      this.videoURL,
      this.statusVDO,
      this.link,
      this.id_share,
      this.statusLink,
      this.sortADS);

  @override
  _showadsState createState() => _showadsState(
      this.running_ads,
      this.imageads,
      this.subjectads,
      this.dataads,
      this.nameActivities,
      this.numads,
      this.fullads,
      this.timeout,
      this.statusincom,
      this.videoURL,
      this.statusVDO,
      this.link,
      this.id_share,
      this.statusLink,
      this.sortADS);
}

class _showadsState extends State<showads> {
  final running_ads;
  final imageads;
  final subjectads;
  final dataads;
  final nameActivities;
  final numads;
  final fullads;
  var timeout;
  final statusincom;
  final videoURL;
  final statusVDO;
  final link;
  final id_share;
  final statusLink;
  final sortADS;

  int statusreward = 0;
  int statusshowenewreward = 0;

  int statusShowNumDown = 0;
  int statusShowNumDownVDO = 0;
  int statusProcessBack = 0;

  _showadsState(
      this.running_ads,
      this.imageads,
      this.subjectads,
      this.dataads,
      this.nameActivities,
      this.numads,
      this.fullads,
      this.timeout,
      this.statusincom,
      this.videoURL,
      this.statusVDO,
      this.link,
      this.id_share,
      this.statusLink,
      this.sortADS);

  String _platformVersion = 'Unknown';

  var statusSocail = "";
  bool _saving = false;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    auth = new Auth();

    allLoad();
    // setLifetime();
  }

  allLoad() async {
    setState(() {
      _saving = true;
    });
    await initPlatformState();
    await setInit();
    await new Future.delayed(const Duration(seconds: 1));
    await searchwatchADS();
    if (!mounted) return;
    setState(() {
      _saving = false;
    });
    await searchLikeADS();
    await searchCountLikeADS();
    if (statusincom != "true") {
      await insertOpenADS();
    }
  }

  late BaseAuth auth;
  String firstName = "..loading";
  String lastName = "";
  String phoneNumber = "";
  String _uploadedFileURL = "noprofile";
  int refers = 0;
  setInit() {
    auth.getCurrentUser().then((user) {
      if (!mounted) return;

      FirebaseFirestore.instance
          .collection('users')
          .doc(user!.uid)
          .get()
          .then((DocumentSnapshot<Map<String, dynamic>> ds) {
        setState(() {
          firstName = ds.data()!["firstName"];
          lastName = ds.data()!["lastName"];
          phoneNumber = user.phoneNumber.toString();
          _uploadedFileURL = ds.data()!["imageProfile"] ?? '';
        });
        FirebaseFirestore.instance
            .collection('users')
            .where('refCode', isEqualTo: ds.data()!["selfCode"])
            .snapshots()
            .listen((data) {
          setState(() {
            refers = data.docs.length;
          });
        });
      });
    });
  }

  // Platform messages are asynchronous, so we initialize in an async method.
  Future<void> initPlatformState() async {
    // String platformVersion;
    // // Platform messages may fail, so we use a try/catch PlatformException.
    // try {
    //   platformVersion = await SocialSharePlugin.platformVersion;
    // } on PlatformException {
    //   platformVersion = 'Failed to get platform version.';
    // }
    //
    // // If the widget was removed from the tree while the asynchronous platform
    // // message was in flight, we want to discard the reply rather than calling
    // // setState to update our non-existent appearance.
    // if (!mounted) return;
    //
    // setState(() {
    //   _platformVersion = platformVersion;
    // });
  }

  var datetime = "00-00-0000T00:00:00";
  var numdate = "0";
  getTime() async {
//      showLoadingDialog();
    String url = 'http://devdev.prachakij.com/nuiAPI/LDX/gettime.php';
//    String url = 'https://a0df7e51.ngrok.io/agslearn/us-central1/ApiAppMS24';
    Map map = {"menu": "gettime"};

    final response = await apiRequest(url, map);
    var jsonResponse = json.decode(response);
    print(jsonResponse["date"]);
    if (jsonResponse["date"] != "") {
      setState(() {
        datetime = jsonResponse["date"];
        numdate = jsonResponse["numdate"];
      });
    } else {
      print("not get time");
    }
  }

  insertOpenADS() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    var ID_LDX = await prefs.getString('ID_LDX');
    var email = await prefs.getString('email');

    String url =
        'https://1xpf4khsld.execute-api.ap-southeast-1.amazonaws.com/latest';
    Map map = {
      "menu": "insertwatchADSldxInLikewallet",
      "running_ads": running_ads,
      "user": email,
      "phone_firebase": phoneNumber,
      "nameActivities": nameActivities,
      "numActivities": numads,
      "subjectADS": subjectads,
      "linkImmageADS": imageads,
    };

    print("map : " + map.toString());

    final response = await apiRequest(url, map);
    var jsonResponse = json.decode(response);
    if (jsonResponse["insertId"].toString() != "") {
      print('บันทึกการเปิด ADS แล้ว');
    } else {
      print('บันทึกการเปิด ADS ไม่สำเร็จ');
    }
  }

  searchwatchADS() async {
    await getTime();
    SharedPreferences prefs = await SharedPreferences.getInstance();
    var ID_LDX = await prefs.getString('ID_LDX');

    String url =
        'https://1xpf4khsld.execute-api.ap-southeast-1.amazonaws.com/latest';
    Map map = {
      "menu": "searchwatchADSldxInLikewallet",
      "running_ads": running_ads,
      "phone_firebase": phoneNumber,
      "datecheck": convertDateTime(datetime, "DDB")
    };

    final response = await apiRequest(url, map);
    List jsonResponse = json.decode(response);
    if (jsonResponse.length > 0) {
      print('วันนี้ได้รับ like จาก ADS นี้แล้ว');
      setState(() {
        statusShowNumDown = 1;
      });
    } else {
      print('ยังไม่ได้รับ like จาก ADS นี้');
      if (timeout == null || timeout == "" || timeout == "null") {
        startTimer(10);
      } else {
        startTimer(timeout);
      }
    }
  }

  int countlike = 0;
  searchCountLikeADS() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    var ID_LDX = await prefs.getString('ID_LDX');

    String url =
        'https://1xpf4khsld.execute-api.ap-southeast-1.amazonaws.com/latest';
    Map map = {"menu": "searchCountLikeADS", "running_ads": running_ads};

    final response = await apiRequest(url, map);
    List jsonResponse = json.decode(response);
    if (jsonResponse.length > 0) {
      if (!mounted) return;
      setState(() {
        countlike = jsonResponse.length;
      });
    } else {
      print("ads นี้ยังไม่มีการกด like");
    }
  }

  int statuslike = 0;
  searchLikeADS() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    var ID_LDX = await prefs.getString('ID_LDX');

    String url =
        'https://1xpf4khsld.execute-api.ap-southeast-1.amazonaws.com/latest';
    Map map = {
      "menu": "searchLikeADS",
      "running_ads": running_ads,
      "id_ldx": ID_LDX,
    };

    final response = await apiRequest(url, map);
    List jsonResponse = json.decode(response);
    if (jsonResponse.length > 0) {
      print('ADS นี้กด like เรียบร้อยแล้ว');
      if (!mounted) return;
      setState(() {
        statuslike = 1;
      });
    } else {
      if (!mounted) return;
      setState(() {
        statuslike = 0;
      });
    }
  }

  saveLikeADS() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    var email = await prefs.getString('email');
    var ID_LDX = await prefs.getString('ID_LDX');

    String url =
        'https://1xpf4khsld.execute-api.ap-southeast-1.amazonaws.com/latest';
    Map map = {
      "menu": "updateLikeADS",
      "running_ads": running_ads,
      "user": email,
      "id_ldx": ID_LDX,
    };

    final response = await apiRequest(url, map);
    var jsonResponse = json.decode(response);
    print(jsonResponse);
    if (jsonResponse["changedRows"].toString() != "0") {
      print("save saveReciveLike success");
      setState(() {
        statuslike = 1;
      });
//      hideLoadingDialog();
      Fluttertoast.showToast(
          msg: "กด like สำเร็จ",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.TOP,
          backgroundColor: Colors.green,
          textColor: Colors.black,
          fontSize: 16.0);
    } else {
      print('not save ACT !!!');
      Fluttertoast.showToast(
          msg: "บันทึกกด like ไม่สำเร็จ",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.TOP,
          backgroundColor: Colors.red,
          textColor: Colors.black,
          fontSize: 16.0);
//      hideLoadingDialog();
    }
  }

//  transferlike() async {
//    showLoadingDialog();
//    await getaddress();
//    SharedPreferences prefs = await SharedPreferences.getInstance();
//    var level = await prefs.getString('level');
//    print("AddressLikepoint : " + AddressLikepoint);
//    print("level : " + level);
//    String url =
//        'https://1xpf4khsld.execute-api.ap-southeast-1.amazonaws.com/latest';
//    Map map = {
//      "menu": "tranferwatchADS",
//      "address": AddressLikepoint,
//      "level": level
//    };
//
//    final response = await apiRequest(url, map);
//    var jsonResponse = json.decode(response);
//    print(jsonResponse);
//    if (jsonResponse["res_msg"].toString() == "transfer complete") {
//      print("transfer like success");
//      saveACT(jsonResponse["tx"].toString());
//      setState(() {
//        statusShowNumDown = 1;
//      });
//      Fluttertoast.showToast(
//          msg: "รับ LIKE point เรียบร้อย",
//          toastLength: Toast.LENGTH_SHORT,
//          gravity: ToastGravity.TOP,
//          backgroundColor: Colors.green,
//          textColor: Colors.black,
//          fontSize: 16.0);
//    } else {
//      print('not transfer like !!!');
//      Fluttertoast.showToast(
//          msg: "โอน like ไม่สำเร็จ",
//          toastLength: Toast.LENGTH_SHORT,
//          gravity: ToastGravity.TOP,
//          backgroundColor: Colors.red,
//          textColor: Colors.black,
//          fontSize: 16.0);
//      hideLoadingDialog();
//    }
//  }

//  saveACT(tx) async {
//    SharedPreferences prefs = await SharedPreferences.getInstance();
//    var email = await prefs.getString('email');
//    var ID_LDX = await prefs.getString('ID_LDX');
//    var phone_firebase = await prefs.getString('phone_firebase');
//    var f_name = await prefs.getString('f_name');
//    var l_name = await prefs.getString('l_name');
//    var level = await prefs.getString('level');
//
//    String url =
//        'https://1xpf4khsld.execute-api.ap-southeast-1.amazonaws.com/latest';
//    Map map = {
//      "menu": "insertactivitywatchADS",
//      "update_user": email,
//      "create_user": email,
//      "id_ldx": ID_LDX,
//      "tel_ldx": phone_firebase,
//      "name_ldx": f_name,
//      "lname_ldx": l_name,
//      "tx": tx,
//      "updated_email_transfer": email,
//      "level": level
//    };
//
//    final response = await apiRequest(url, map);
//    var jsonResponse = json.decode(response);
//    print(jsonResponse);
//    if (jsonResponse["insertId"].toString() != "") {
//      saveBCT(tx, jsonResponse["insertId"].toString());
//      saveReciveLike();
//      print("save ACT success");
//    } else {
//      print('not save ACT !!!');
//      Fluttertoast.showToast(
//          msg: "บันทึก activity ไม่สำเร็จ",
//          toastLength: Toast.LENGTH_SHORT,
//          gravity: ToastGravity.TOP,
//          backgroundColor: Colors.red,
//          textColor: Colors.black,
//          fontSize: 16.0);
//      hideLoadingDialog();
//    }
//  }

//  ***************************************************** recive like share line ********************************************
  searchShareADSline() async {
    await getTime();
    SharedPreferences prefs = await SharedPreferences.getInstance();
    var ID_LDX = await prefs.getString('ID_LDX');

    String url =
        'https://1xpf4khsld.execute-api.ap-southeast-1.amazonaws.com/latest';
    Map map = {
      "menu": "searchShareADSlineldxInLikewallet",
      "running_ads": running_ads,
      "numActivities": numads,
      "phone_firebase": phoneNumber,
      "datecheck": convertDateTime(datetime, "DDB")
    };

    final response = await apiRequest(url, map);
    List jsonResponse = json.decode(response);
    if (jsonResponse.length > 0) {
      print('วันนี้ได้รับ like การ share line จาก ADS นี้แล้ว');
    } else {
      print('ยังไม่ได้รับ like การ share line จาก ADS นี้');
      // searchACTshare();
      transferlikeShareADS();
    }
  }

  saveReciveLikeShareLine() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    var email = await prefs.getString('email');
    var ID_LDX = await prefs.getString('ID_LDX');

    String url =
        'https://1xpf4khsld.execute-api.ap-southeast-1.amazonaws.com/latest';
    Map map = {
      "menu": "updateShareADSlineldxInLikewallet",
      "running_ads": running_ads,
      "user": email,
      "phone_firebase": phoneNumber,
    };

    final response = await apiRequest(url, map);
    var jsonResponse = json.decode(response);
    print(jsonResponse);
    if (jsonResponse["changedRows"].toString() != "0") {
      print("save saveReciveLike success");
//      hideLoadingDialog();
      Fluttertoast.showToast(
          msg: "บันทึกสำเร็จ",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.TOP,
          backgroundColor: Colors.green,
          textColor: Colors.black,
          fontSize: 16.0);
      setState(() {
        _saving = false;
      });
    } else {
      print('not save ACT !!!');
      Fluttertoast.showToast(
          msg: "บันทึก ReciveLike ไม่สำเร็จ",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.TOP,
          backgroundColor: Colors.red,
          textColor: Colors.black,
          fontSize: 16.0);
      setState(() {
        _saving = false;
      });
    }
  }

//  ***************************************************** recive like share line ********************************************
//  ***************************************************** recive like share facebook ********************************************

  searchShareADSfacebook() async {
    await getTime();
    SharedPreferences prefs = await SharedPreferences.getInstance();
    var ID_LDX = await prefs.getString('ID_LDX');

    String url =
        'https://1xpf4khsld.execute-api.ap-southeast-1.amazonaws.com/latest';
    Map map = {
      "menu": "searchShareADSfacebookldxInLikewallet",
      "running_ads": running_ads,
      "numActivities": numads,
      "phone_firebase": phoneNumber,
      "datecheck": convertDateTime(datetime, "DDB")
    };

    final response = await apiRequest(url, map);
    List jsonResponse = json.decode(response);
    if (jsonResponse.length > 0) {
      print('วันนี้ได้รับ like การ share facebook จาก ADS นี้แล้ว');
      Fluttertoast.showToast(
          msg: "วันนี้ได้รับ like การ share facebook จาก ADS นี้แล้ว",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.TOP,
          backgroundColor: Colors.red,
          textColor: Colors.black,
          fontSize: 16.0);
    } else {
      print('ยังไม่ได้รับ like การ share facebook จาก ADS นี้');
      // searchACTshare();
      transferlikeShareADS();
    }
  }

  saveReciveLikeShareFacebook() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    var email = await prefs.getString('email');
    var ID_LDX = await prefs.getString('ID_LDX');

    String url =
        'https://1xpf4khsld.execute-api.ap-southeast-1.amazonaws.com/latest';
    Map map = {
      "menu": "updateShareADSfacebookldxInLikewallet",
      "running_ads": running_ads,
      "user": email,
      "phone_firebase": phoneNumber,
    };

    final response = await apiRequest(url, map);
    var jsonResponse = json.decode(response);
    print(jsonResponse);
    if (jsonResponse["changedRows"].toString() != "0") {
      print("save saveReciveLike success");
//      hideLoadingDialog();
      Fluttertoast.showToast(
          msg: "บันทึกสำเร็จ",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.TOP,
          backgroundColor: Colors.green,
          textColor: Colors.black,
          fontSize: 16.0);
      setState(() {
        _saving = false;
      });
    } else {
      print('not save ACT !!!');
      Fluttertoast.showToast(
          msg: "บันทึก ReciveLike ไม่สำเร็จ",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.TOP,
          backgroundColor: Colors.red,
          textColor: Colors.black,
          fontSize: 16.0);
      setState(() {
        _saving = false;
      });
    }
  }

  saveReciveLike() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    var email = await prefs.getString('email');
    var ID_LDX = await prefs.getString('ID_LDX');

    String url =
        'https://1xpf4khsld.execute-api.ap-southeast-1.amazonaws.com/latest';
    Map map = {
      "menu": "updatewatchADSldxInLikewallet",
      "running_ads": running_ads,
      "user": email,
      "phone_firebase": phoneNumber,
    };

    final response = await apiRequest(url, map);
    var jsonResponse = json.decode(response);
    print("555 : " + jsonResponse.toString());
    if (jsonResponse["changedRows"].toString() != "0") {
      print("save saveReciveLike success");
      setState(() {
        _saving = false;
      });
//      hideLoadingDialog();
//       Fluttertoast.showToast(
//           msg: "บันทึกสำเร็จ",
//           toastLength: Toast.LENGTH_SHORT,
//           gravity: ToastGravity.TOP,
//           backgroundColor: Colors.green,
//           textColor: Colors.black,
//           fontSize: 16.0);
    } else {
      print('not save ACT !!!');
      Fluttertoast.showToast(
          msg: "บันทึก ReciveLike ไม่สำเร็จ",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.TOP,
          backgroundColor: Colors.red,
          textColor: Colors.black,
          fontSize: 16.0);
      setState(() {
        _saving = false;
      });
//      hideLoadingDialog();
    }
  }

  transferlikeWatchADS() async {
    setState(() {
      _saving = true;
    });
    // SharedPreferences prefs = await SharedPreferences.getInstance();
    // var level = await prefs.getString('level');

    // if (level == "MASTER") {
    transferLockLikepointWatchADS(326);
    //   // transferLikepointAdvisor(348);
    // } else if (level == "PRO") {
    //   transferLockLikepointWatchADS(325);
    //   // transferLikepointAdvisor(347);
    // } else if (level == "STANDARD") {
    //   transferLockLikepointWatchADS(324);
    //   // transferLikepointAdvisor(346);
    // } else {
    // transferLockLikepointWatchADS(323);
    // transferLikepointAdvisor(345);
    // }
  }

  transferLockLikepointWatchADS(id_activity) async {
    var jsonResponseloadBuFirstLib = await loadBuFirstLib(phoneNumber);
    if (jsonResponseloadBuFirstLib["statusCode"].toString() == "200") {
      var jsonResponsegetaddressLib = await getaddressLib(phoneNumber);
      if (jsonResponsegetaddressLib["status"].toString() == "200") {
        List jsonResponsesearchACTLib =
            await searchACTLib(id_activity.toString());
        if (jsonResponsesearchACTLib.length > 0) {
          print("search ACT success");
          // transferLifttime("watchADS",jsonResponsesearchACTLib[0]["point"].toString());
          var jsonResponseinsertactivitylock = await insertactivitylock(
              "App",
              phoneNumber.toString(),
              "ภายนอก",
              firstName.toString() + " " + lastName.toString(),
              firstName.toString(),
              lastName.toString(),
              "ภายนอก",
              jsonResponsesearchACTLib[0]["point"].toString(),
              // "1",
              "รอดำเนินการ",
              jsonResponsesearchACTLib[0]["running"].toString(),
              jsonResponsesearchACTLib[0]["titile"].toString(),
              phoneNumber.toString(),
              phoneNumber.toString(),
              firstName.toString() + " " + lastName.toString(),
              firstName.toString(),
              lastName.toString(),
              jsonResponsegetaddressLib["result"]["data"].toString(),
              "NULL",
              "NULL",
              "NULL",
              jsonResponsesearchACTLib[0]["type_bu"].toString(),
              jsonResponsesearchACTLib[0]["type_bu_gr"].toString(),
              jsonResponsesearchACTLib[0]["use_bu"].toString(),
              jsonResponsesearchACTLib[0]["use_bu_gr"].toString(),
              jsonResponsesearchACTLib[0]["use_bag_name"].toString(),
              jsonResponsesearchACTLib[0]["status_check"].toString(),
              jsonResponsesearchACTLib[0]["isAutoClaim"].toString(),
              jsonResponsesearchACTLib[0]["isGojoy"].toString(),
              jsonResponsesearchACTLib[0]["pool_percent"].toString(),
              jsonResponsesearchACTLib[0]["pool"].toString(),
              jsonResponsesearchACTLib[0]["gojoy_id_activity"].toString(),
              jsonResponsesearchACTLib[0]["gojoy_name_activity"].toString(),
              "N",
              "LDX",
              "LDX Digital Ecosystem Co., Ltd",
              // "TEST",
              "โอน",
              "0xcd40c2f37177594e958f354718cbfa7aebd99864",
              "ส่วนกลาง",
              jsonResponseloadBuFirstLib['result'][0]["owner"].toString(),
              jsonResponsesearchACTLib[0]["tax_per"].toString(),
              jsonResponsesearchACTLib[0]["tax_point"].toString(),
              (jsonResponsesearchACTLib[0]["tax_point"] +
                      jsonResponsesearchACTLib[0]["point"])
                  .toString(),
              "NULL",
              "NULL",
              jsonResponsesearchACTLib[0]["pool_address"].toString(),
              jsonResponsesearchACTLib[0]["date_lock"].toString());
          if (jsonResponseinsertactivitylock["statusCode"].toString() ==
              "200") {
            print("save ACT success");
            saveReciveLike();
            setState(() {
              statusShowNumDown = 1;
            });
            var jsonResponseclaimlikeLock = await claimlikeLock(
                "App", jsonResponseinsertactivitylock["data"]);
            if (jsonResponseclaimlikeLock["statusCode"].toString() == "200") {
              Fluttertoast.showToast(
                  msg: "รับ likepoint สำเร็จ",
                  toastLength: Toast.LENGTH_SHORT,
                  gravity: ToastGravity.TOP,
                  backgroundColor: Colors.green,
                  textColor: Colors.black,
                  fontSize: 16.0);
            } else {
              print("not_transfer_like !");
              Fluttertoast.showToast(
                  msg: "ไม่สำเร็จ กรุณากดใหม่อีกครั้ง",
                  toastLength: Toast.LENGTH_SHORT,
                  gravity: ToastGravity.TOP,
                  backgroundColor: Colors.red,
                  textColor: Colors.black,
                  fontSize: 16.0);
              setState(() {
                _saving = false;
              });
            }
          } else {
            print('not save ACT !!!');
          }
        } else {
          print('not search ACT !!!');
        }
      } else {
        alerterror(context,
            "ไม่สามารถค้นหา address like wallet ได้ กรุณาติดต่อทีมงาน");
      }
    } else {
      print('not regis app bu!');
    }
  }

  transferlikeShareADS() async {
    setState(() {
      _saving = true;
    });
    // SharedPreferences prefs = await SharedPreferences.getInstance();
    // var level = await prefs.getString('level');

    // if (level == "MASTER") {
    await transferLockLikepointShareADS(330);
    //   // await transferLikepointAdvisor(352);
    // } else if (level == "PRO") {
    //   await transferLockLikepointShareADS(329);
    //   // await transferLikepointAdvisor(351);
    // } else if (level == "STANDARD") {
    //   await transferLockLikepointShareADS(328);
    //   // await transferLikepointAdvisor(350);
    // } else {
    // await transferLockLikepointShareADS(327);
    // await transferLikepointAdvisor(349);
    // }
  }

  transferLockLikepointShareADS(id_activity) async {
    var jsonResponseloadBuFirstLib = await loadBuFirstLib(phoneNumber);
    if (jsonResponseloadBuFirstLib["statusCode"].toString() == "200") {
      var jsonResponsegetaddressLib = await getaddressLib(phoneNumber);
      if (jsonResponsegetaddressLib["status"].toString() == "200") {
        List jsonResponsesearchACTLib =
            await searchACTLib(id_activity.toString());
        if (jsonResponsesearchACTLib.length > 0) {
          print("search ACT success");
          // transferLifttime("shareADS",jsonResponsesearchACTLib[0]["point"].toString());
          var jsonResponseinsertactivitylock = await insertactivitylock(
              "App",
              phoneNumber.toString(),
              "ภายนอก",
              firstName.toString() + " " + lastName.toString(),
              firstName.toString(),
              lastName.toString(),
              "ภายนอก",
              jsonResponsesearchACTLib[0]["point"].toString(),
              "รอดำเนินการ",
              jsonResponsesearchACTLib[0]["running"].toString(),
              jsonResponsesearchACTLib[0]["titile"].toString(),
              phoneNumber.toString(),
              phoneNumber.toString(),
              firstName.toString() + " " + lastName.toString(),
              firstName.toString(),
              lastName.toString(),
              jsonResponsegetaddressLib["result"]["data"].toString(),
              "NULL",
              "NULL",
              "NULL",
              jsonResponsesearchACTLib[0]["type_bu"].toString(),
              jsonResponsesearchACTLib[0]["type_bu_gr"].toString(),
              jsonResponsesearchACTLib[0]["use_bu"].toString(),
              jsonResponsesearchACTLib[0]["use_bu_gr"].toString(),
              jsonResponsesearchACTLib[0]["use_bag_name"].toString(),
              jsonResponsesearchACTLib[0]["status_check"].toString(),
              jsonResponsesearchACTLib[0]["isAutoClaim"].toString(),
              jsonResponsesearchACTLib[0]["isGojoy"].toString(),
              jsonResponsesearchACTLib[0]["pool_percent"].toString(),
              jsonResponsesearchACTLib[0]["pool"].toString(),
              jsonResponsesearchACTLib[0]["gojoy_id_activity"].toString(),
              jsonResponsesearchACTLib[0]["gojoy_name_activity"].toString(),
              "N",
              "LDX",
              "LDX Digital Ecosystem Co., Ltd",
              "โอน",
              "0xcd40c2f37177594e958f354718cbfa7aebd99864",
              "ส่วนกลาง",
              jsonResponseloadBuFirstLib['result'][0]["owner"].toString(),
              jsonResponsesearchACTLib[0]["tax_per"].toString(),
              jsonResponsesearchACTLib[0]["tax_point"].toString(),
              (jsonResponsesearchACTLib[0]["tax_point"] +
                      jsonResponsesearchACTLib[0]["point"])
                  .toString(),
              "NULL",
              "NULL",
              jsonResponsesearchACTLib[0]["pool_address"].toString(),
              jsonResponsesearchACTLib[0]["date_lock"].toString());
          if (jsonResponseinsertactivitylock["statusCode"].toString() ==
              "200") {
            print("save ACT success");
            // saveReciveLikeShareFacebook();
            // saveReciveLike();
            if (statusSocail.toString() == "facebook") {
              saveReciveLikeShareFacebook();
            } else {
              saveReciveLikeShareLine();
            }
            var jsonResponseclaimlikeLock = await claimlikeLock(
                "App", jsonResponseinsertactivitylock["data"]);
            if (jsonResponseclaimlikeLock["statusCode"].toString() == "200") {
              Fluttertoast.showToast(
                  msg: "รับ likepoint สำเร็จ",
                  toastLength: Toast.LENGTH_SHORT,
                  gravity: ToastGravity.TOP,
                  backgroundColor: Colors.green,
                  textColor: Colors.black,
                  fontSize: 16.0);
            } else {
              print("not_transfer_like !");
              Fluttertoast.showToast(
                  msg: "ไม่สำเร็จ กรุณากดใหม่อีกครั้ง",
                  toastLength: Toast.LENGTH_SHORT,
                  gravity: ToastGravity.TOP,
                  backgroundColor: Colors.red,
                  textColor: Colors.black,
                  fontSize: 16.0);
              setState(() {
                _saving = false;
              });
            }
          } else {
            print('not save ACT !!!');
          }
        } else {
          print('not search ACT !!!');
        }
      } else {
        alerterror(context,
            "ไม่สามารถค้นหา address like wallet ได้ กรุณาติดต่อทีมงาน");
      }
    } else {
      print('not regis app bu!');
    }
  }

  transferLikepointAdvisor(id_activity) async {
    await getTime();
    SharedPreferences prefs = await SharedPreferences.getInstance();
    var dataAdvisorIdldx = await prefs.getString('dataAdvisorIdldx');
    var dataAdvisorphonefirebase =
        await prefs.getString('dataAdvisorphonefirebase');
    var dataAdvisorFname = await prefs.getString('dataAdvisorFname');
    var dataAdvisorLname = await prefs.getString('dataAdvisorLname');
    var first_login = await prefs.getString('first_login');
    var dataAdvisorfirstRegisApp =
        await prefs.getString('dataAdvisorfirstRegisApp');
    var dataAdvisorAddressLikepoint =
        await prefs.getString('dataAdvisorAddressLikepoint');

    final dateRegis = DateTime(
        int.parse(convertDateTime(first_login, "DBY")),
        int.parse(convertDateTime(first_login, "DBM")),
        int.parse(convertDateTime(first_login, "DBD")));
    final datenow = DateTime(
        int.parse(convertDateTime(datetime, "DY")),
        int.parse(convertDateTime(datetime, "DM")),
        int.parse(convertDateTime(datetime, "DD")));
    final difference = datenow.difference(dateRegis).inDays;

    if (difference < 366) {
      List jsonResponsesearchACTLib =
          await searchACTLib(id_activity.toString());
      if (jsonResponsesearchACTLib.length > 0) {
        print("Advisor : search ACT success");
        var jsonResponseinsertactivitylock = await insertactivitylock(
            "App",
            dataAdvisorphonefirebase.toString(),
            "ภายนอก",
            dataAdvisorFname.toString() + " " + dataAdvisorLname.toString(),
            dataAdvisorFname.toString(),
            dataAdvisorLname.toString(),
            "ภายนอก",
            jsonResponsesearchACTLib[0]["point"].toString(),
            // "1",
            "รอดำเนินการ",
            jsonResponsesearchACTLib[0]["running"].toString(),
            jsonResponsesearchACTLib[0]["titile"].toString(),
            dataAdvisorIdldx.toString(),
            dataAdvisorphonefirebase.toString(),
            dataAdvisorFname.toString() + " " + dataAdvisorLname.toString(),
            dataAdvisorFname.toString(),
            dataAdvisorLname.toString(),
            dataAdvisorAddressLikepoint.toString(),
            "NULL",
            "NULL",
            "NULL",
            jsonResponsesearchACTLib[0]["type_bu"].toString(),
            jsonResponsesearchACTLib[0]["type_bu_gr"].toString(),
            jsonResponsesearchACTLib[0]["use_bu"].toString(),
            jsonResponsesearchACTLib[0]["use_bu_gr"].toString(),
            jsonResponsesearchACTLib[0]["use_bag_name"].toString(),
            jsonResponsesearchACTLib[0]["status_check"].toString(),
            jsonResponsesearchACTLib[0]["isAutoClaim"].toString(),
            jsonResponsesearchACTLib[0]["isGojoy"].toString(),
            jsonResponsesearchACTLib[0]["pool_percent"].toString(),
            jsonResponsesearchACTLib[0]["pool"].toString(),
            jsonResponsesearchACTLib[0]["gojoy_id_activity"].toString(),
            jsonResponsesearchACTLib[0]["gojoy_name_activity"].toString(),
            "N",
            "LDX",
            "LDX Digital Ecosystem Co., Ltd",
            // "TEST",
            "โอน",
            "0xcd40c2f37177594e958f354718cbfa7aebd99864",
            "ส่วนกลาง",
            dataAdvisorfirstRegisApp.toString(),
            jsonResponsesearchACTLib[0]["tax_per"].toString(),
            jsonResponsesearchACTLib[0]["tax_point"].toString(),
            (jsonResponsesearchACTLib[0]["tax_point"] +
                    jsonResponsesearchACTLib[0]["point"])
                .toString(),
            "NULL",
            "NULL",
            jsonResponsesearchACTLib[0]["pool_address"].toString(),
            jsonResponsesearchACTLib[0]["date_lock"].toString());
        if (jsonResponseinsertactivitylock["statusCode"].toString() == "200") {
          print("save ACT success");
          var jsonResponseclaimlikeLock = await claimlikeLock(
              "App", jsonResponseinsertactivitylock["data"]);
          if (jsonResponseclaimlikeLock["statusCode"].toString() == "200") {
            Fluttertoast.showToast(
                msg: "รับ likepoint สำเร็จ",
                toastLength: Toast.LENGTH_SHORT,
                gravity: ToastGravity.TOP,
                backgroundColor: Colors.green,
                textColor: Colors.black,
                fontSize: 16.0);
            setState(() {
              _saving = false;
            });
          } else {
            print("not_transfer_like_Advisor !");
            Fluttertoast.showToast(
                msg: "ไม่สำเร็จ กรุณากดใหม่อีกครั้ง",
                toastLength: Toast.LENGTH_SHORT,
                gravity: ToastGravity.TOP,
                backgroundColor: Colors.red,
                textColor: Colors.black,
                fontSize: 16.0);
            setState(() {
              _saving = false;
            });
          }
        } else {
          print('Advisor : not save ACT !!!');
        }
      } else {
        print('Advisor : not search ACT !!!');
      }
    } else {
      print(
          "ผู้แนะนำไม่ได้รับ likepoint แล้เนื่องจากสมาชิกคนนี้สมัครเกิน 1 ปีแล้ว");
    }
  }

  @override
  void dispose() {
    _timer!.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final height = MediaQuery.of(context).size.height;
    final width = MediaQuery.of(context).size.width;

    return WillPopScope(
        onWillPop: () {
          setState(() {
            statusProcessBack = 1;
          });
          Navigator.of(context).pop();
          return Future.value(true);
        },
        child: ModalProgressHUD(
          inAsyncCall: _saving,
          opacity: 0.1,
          progressIndicator: CustomLoading(),
          child: Scaffold(
            body: Stack(
              children: <Widget>[
                Center(
                    child: Container(
                  decoration: BoxDecoration(
                      gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [Color(0xff495764), Color(0xff0F0F0F)])),
                )),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    GestureDetector(
                      onTap: () {
                        if (statusVDO == "Y") {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => showVDO(
                                      running_ads,
                                      imageads,
                                      subjectads,
                                      dataads,
                                      nameActivities,
                                      numads,
                                      videoURL)));
                        } else if (statusLink == "Y") {
                          _handleURLButtonPress(
                              context, link.toString(), "ADS");
                        }
                      },
                      child: Container(
                        width: mediaQuery2(context, "w", 828),
                        child: Image.network(imageads),
                      ),
                    ),
                    Container(
                      margin: EdgeInsets.only(
                          top: mediaQuery2(context, "h", 66),
                          left: mediaQuery2(context, "w", 81),
                          right: mediaQuery2(context, "w", 81)),
                      child: Text(
                        subjectads,
                        style: TextStyle(
                          fontFamily: 'Prompt-Regular',
                          fontSize: mediaQuery2(context, "h", 35),
                          color: const Color(0xffd0d0d0),
                          letterSpacing: mediaQuery2(context, "h", 1.05),
                          // height: 0.7142857142857143,
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    Container(
                      margin: EdgeInsets.only(
                          top: mediaQuery2(context, "h", 43),
                          left: mediaQuery2(context, "w", 81),
                          right: mediaQuery2(context, "w", 81)),
                      child: Container(
                        height: mediaQuery1(context, 'h', 49),
                        child: Row(
                          children: <Widget>[
                            Container(
                              alignment: Alignment.center,
                              width: mediaQuery2(context, "w", 144),
                              height: mediaQuery2(context, "h", 49),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(
                                    mediaQuery2(context, "h", 25)),
                                color: const Color(0xff495764),
                              ),
                              child: Text(
                                AppLocalizations.of(context)!
                                    .translate("ui_adsshare"),
                                style: TextStyle(
                                  fontFamily: 'Prompt-Regular',
                                  fontSize: mediaQuery2(context, "h", 24),
                                  color: const Color(0xffd0d0d0),
                                  letterSpacing:
                                      mediaQuery2(context, "h", 0.72),
                                  height: 1.0833333333333333,
                                ),
                                textAlign: TextAlign.left,
                              ),
                            ),
                            Container(
                              width: mediaQuery2(context, 'w', 30),
                            ),
                            GestureDetector(
                              onTap: () async {
                                if (statusLink == "Y") {
                                  final quote = subjectads.toString();
                                  // final result = await SocialSharePlugin
                                  //     .shareToFeedFacebookLink(
                                  //   quote: quote,
                                  //   url: link.toString(),
                                  //   onSuccess: (_) {
                                  //     print('FACEBOOK SUCCESS');
                                  //     searchShareADSfacebook();
                                  //     return;
                                  //   },
                                  //   onCancel: () {
                                  //     print('FACEBOOK CANCELLED');
                                  //     Fluttertoast.showToast(
                                  //         msg: "ท่านได้ทำการ ยกเลิก การแชร์",
                                  //         toastLength: Toast.LENGTH_SHORT,
                                  //         gravity: ToastGravity.TOP,
                                  //         backgroundColor: Colors.red,
                                  //         textColor: Colors.black,
                                  //         fontSize: 16.0);
                                  //     return;
                                  //   },
                                  //   onError: (error) {
                                  //     print('FACEBOOK ERROR $error');
                                  //     Fluttertoast.showToast(
                                  //         msg:
                                  //             "การแชร์ ผิดพลาด กรูณากดแชร์ใหม่อีกครั้ง",
                                  //         toastLength: Toast.LENGTH_SHORT,
                                  //         gravity: ToastGravity.TOP,
                                  //         backgroundColor: Colors.red,
                                  //         textColor: Colors.black,
                                  //         fontSize: 16.0);
                                  //     return;
                                  //   },
                                  // );

                                  // print("result : " + result.toString());
                                }

                                // String url = 'http://devdev.prachakij.com/nuiAPI/LDX/showOnWebNew.php?running_ads='+running_ads.toString();
                                // final quote = subjectads.toString();
                                // final result = await SocialSharePlugin.shareToFeedFacebookLink(
                                //   quote: quote,
                                //   url: url,
                                //   onSuccess: (_) {
                                //     print('FACEBOOK SUCCESS');
                                //     return;
                                //   },
                                //   onCancel: () {
                                //     print('FACEBOOK CANCELLED');
                                //     return;
                                //   },
                                //   onError: (error) {
                                //     print('FACEBOOK ERROR $error');
                                //     return;
                                //   },
                                // );
                                //
                                // print("result : "+result.toString());
                                setState(() {
                                  statusSocail = "facebook";
                                });
                                // searchShareADSfacebook();
//                                  ontabfacebook();
//                                      Navigator.pushReplacement(
//                                        context,
//                                        MaterialPageRoute(builder: (context) => socailShare()),
//                                      );
                                print("Container clicked");
                                //                  File file = await ImagePicker.pickImage(source: ImageSource.gallery);
                                //                  await SocialSharePlugin.shareToFeedFacebook('caption', file.path);
                              },
                              child: SvgPicture.asset(
                                "assets/image/LDX/ads/facebook-brands.svg",
                                width: mediaQuery2(context, 'h', 40.27),
                                height: mediaQuery2(context, 'h', 40.27),
                              ),
                            ),
                            Container(
                              width: mediaQuery2(context, 'w', 33.6),
                            ),
                            GestureDetector(
                              onTap: () {
                                setState(() {
                                  statusSocail = "line";
                                });
                                // searchShareADSline();
                                ontabline();
                                print("Container clicked");
                                //                  File file = await ImagePicker.pickImage(source: ImageSource.gallery);
                                //                  await SocialSharePlugin.shareToFeedFacebook('caption', file.path);
                              },
                              child: SvgPicture.asset(
                                "assets/image/LDX/ads/line-brands.svg",
                                width: mediaQuery2(context, 'h', 40.27),
                                height: mediaQuery2(context, 'h', 40.27),
                              ),
                            ),
//                              new GestureDetector(
//                                onTap: () {
//                                  ontabfacebook();
//                                  print("Container clicked");
//                                  //                  File file = await ImagePicker.pickImage(source: ImageSource.gallery);
//                                  //                  await SocialSharePlugin.shareToFeedFacebook('caption', file.path);
//                                },
//                                child: Container(child: Image.asset(
//                                  "assets/images/downloadapp/facebook-downloadapp.png",
//                                  color: Color(0xffD0D0D0),height: 22,
//                                ),),
//                              ),
                            Expanded(
                              child: Container(
                                margin: EdgeInsets.only(
                                    top: mediaQuery2(context, "h", 20),
                                    right: mediaQuery2(context, "w", 20)),
                                alignment: Alignment.centerRight,
                                child: Text(
                                  "${countlike.toString()} " +
                                      AppLocalizations.of(context)!
                                          .translate("ui_adslike"),
                                  style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 12,
                                      fontFamily: 'Prompt-Regular'),
                                ),
                              ),
                            ),
                            statuslike == 1
                                ? Container(
                                    alignment: Alignment.centerRight,
                                    child: SvgPicture.string(
                                      '<svg viewBox="708.0 1031.0 39.6 40.3" ><path transform="translate(703.83, 1026.83)" d="M 28.32578468322754 18.25948524475098 L 28.32578468322754 10.20644569396973 C 28.32578468322754 6.870767116546631 25.62168312072754 4.166665554046631 22.28600311279297 4.166665554046631 L 14.23296546936035 22.2860050201416 L 14.23296546936035 44.43186569213867 L 36.94253540039062 44.43186569213867 C 38.95033645629883 44.45456314086914 40.66799545288086 42.99455261230469 40.96905517578125 41.00932312011719 L 43.74735641479492 22.88998413085938 C 43.92463302612305 21.72200012207031 43.58004760742188 20.53509140014648 42.80486297607422 19.64362907409668 C 42.02968215942383 18.75217056274414 40.90211868286133 18.24610900878906 39.7208366394043 18.25948715209961 L 28.32578468322754 18.25948524475098 Z M 14.23296546936035 44.43186187744141 L 8.193184852600098 44.43186187744141 C 5.969400882720947 44.43186187744141 4.166666507720947 42.62912750244141 4.166666030883789 40.40534210205078 L 4.166666030883789 26.31252288818359 C 4.166666030883789 24.08873748779297 5.969401359558105 22.28600120544434 8.193185806274414 22.28600311279297 L 14.23296546936035 22.28600311279297" fill="none" stroke="#00eae7" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" /></svg>',
                                      allowDrawingOutsideViewBox: true,
                                      height: mediaQuery2(context, "h", 40.27),
                                    ),
                                  )
                                : Container(
                                    alignment: Alignment.centerRight,
                                    child: GestureDetector(
                                      onTap: () {
                                        saveLikeADS();
                                      },
                                      child: SvgPicture.string(
                                        '<svg viewBox="708.0 1031.0 39.6 40.3" ><path transform="translate(703.83, 1026.83)" d="M 28.32578468322754 18.25948524475098 L 28.32578468322754 10.20644569396973 C 28.32578468322754 6.870767116546631 25.62168312072754 4.166665554046631 22.28600311279297 4.166665554046631 L 14.23296546936035 22.2860050201416 L 14.23296546936035 44.43186569213867 L 36.94253540039062 44.43186569213867 C 38.95033645629883 44.45456314086914 40.66799545288086 42.99455261230469 40.96905517578125 41.00932312011719 L 43.74735641479492 22.88998413085938 C 43.92463302612305 21.72200012207031 43.58004760742188 20.53509140014648 42.80486297607422 19.64362907409668 C 42.02968215942383 18.75217056274414 40.90211868286133 18.24610900878906 39.7208366394043 18.25948715209961 L 28.32578468322754 18.25948524475098 Z M 14.23296546936035 44.43186187744141 L 8.193184852600098 44.43186187744141 C 5.969400882720947 44.43186187744141 4.166666507720947 42.62912750244141 4.166666030883789 40.40534210205078 L 4.166666030883789 26.31252288818359 C 4.166666030883789 24.08873748779297 5.969401359558105 22.28600120544434 8.193185806274414 22.28600311279297 L 14.23296546936035 22.28600311279297" fill="none" stroke="#00eae7" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" /></svg>',
                                        allowDrawingOutsideViewBox: true,
                                        height:
                                            mediaQuery2(context, "h", 40.27),
                                        color: Color(0xffD0D0D0),
                                      ),
                                    ),
                                  )
                          ],
                        ),
                      ),
                    ),
                    Expanded(
                      child: Container(
                        margin: EdgeInsets.only(
                            top: mediaQuery2(context, "h", 54),
                            bottom: mediaQuery2(context, "h", 124),
                            left: mediaQuery2(context, "w", 81),
                            right: mediaQuery2(context, "w", 81)),
                        child: ListView(
                          padding: EdgeInsets.only(top: 0),
                          children: <Widget>[
                            Text(
                              dataads,
                              style: TextStyle(
                                fontFamily: 'Prompt-Regular',
                                fontSize: mediaQuery2(context, "h", 25),
                                color: const Color(0xffd0d0d0),
                                letterSpacing: mediaQuery2(context, "h", 0.75),
                                height: 1.44,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                Container(
                  margin: EdgeInsets.only(
                      top: mediaQuery2(context, "h", 180),
                      left: mediaQuery2(context, "w", 82)),
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        statusProcessBack = 1;
                      });
                      Navigator.of(context).pop();
                      // if(statusSocail != ""){
                      //   print("pop true");
                      //   checkshareFacebook();
                      // }else{
                      //   print("pop false");
                      //   setState(() {
                      //     statusProcessBack = 1;
                      //   });
                      //   Navigator.of(context).pop();
                      // }
                      print("Container clicked");
                    },
                    child: Container(
                      alignment: Alignment.center,
                      color: Colors.transparent,
                      width: mediaQuery2(context, "h", 80),
                      height: mediaQuery2(context, "h", 80),
                      child: Stack(
                        children: <Widget>[
                          Container(
                            decoration: BoxDecoration(
                              boxShadow: [
                                new BoxShadow(
                                  color: Color(0xff000000),
                                  blurRadius: 1.0,
                                ),
                              ],
                              borderRadius: BorderRadius.circular(5.0),
                            ),
                            child: SvgPicture.string(
                              '<svg viewBox="0.0 0.0 16.0 25.6" ><path transform="translate(-3664.4, -626.0)" d="M 3680.397705078125 626 L 3664.397705078125 638.8001708984375 L 3680.397705078125 651.6002197265625" fill="none" stroke="#ffffff" stroke-width="5" stroke-linecap="round" stroke-linejoin="round" /></svg>',
                              allowDrawingOutsideViewBox: true,
                              height: mediaQuery2(context, "h", 25.6),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                recivecoin(width, height),
                statusShowNumDown == 1
                    ? Container()
                    : Container(
                        alignment: Alignment.bottomRight,
                        margin: EdgeInsets.only(
                            bottom: mediaQuery2(context, "h", 60),
                            right: mediaQuery2(context, "w", 113)),
                        child: Text(
                          'คุณจะได้รับ Likepoint ภายใน',
                          style: TextStyle(
                            fontFamily: 'Prompt-Regular',
                            fontSize: mediaQuery2(context, "h", 25),
                            color: const Color(0xffaafffe),
                            letterSpacing: mediaQuery2(context, "h", 0.75),
                            height: 1,
                          ),
                          textAlign: TextAlign.right,
                        ),
                      ),
                statusShowNumDown == 1
                    ? Container()
                    : Container(
                        alignment: Alignment.bottomRight,
                        margin: EdgeInsets.only(
                            bottom: mediaQuery2(context, "h", 53),
                            right: mediaQuery2(context, "w", 53)),
                        child: Container(
                          alignment: Alignment.center,
                          width: mediaQuery2(context, "h", 45),
                          height: mediaQuery2(context, "h", 45),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.all(
                                Radius.elliptical(9999.0, 9999.0)),
                            color: const Color(0xff27e4e1),
                          ),
                          child: Text(
                            _start.toString(),
                            style: TextStyle(
                              fontFamily: 'Prompt-Regular',
                              fontSize: mediaQuery2(context, "h", 25),
                              color: const Color(0xff191f26),
                              letterSpacing: mediaQuery2(context, "h", 0.75),
                              // height: 1,
                            ),
                            textAlign: TextAlign.left,
                          ),
                        ),
                      ),
                // Container(
                //         alignment: Alignment.bottomRight,
                //         margin: EdgeInsets.fromLTRB(0, 0, 20, 20),
                //         child: new Container(
                //           alignment: Alignment.center,
                //           width: 30.0,
                //           height: 30.0,
                //           decoration: new BoxDecoration(
                //             color: Color(0xff00EAE7),
                //             shape: BoxShape.circle,
                //           ),
                //           child: Text("${_start}",style: TextStyle(fontFamily: 'Prompt-Regular'),),
                //         ),
                //       ),
//      Container(margin: EdgeInsets.fromLTRB(20, 80, 20, 0),child: ListView(children: <Widget>[Stack(children: ListNews(),)],),),
              ],
            ),
          ),
        ));
  }

  Widget recivecoin(width, height) {
    if (statusreward == 1) {
      return new GestureDetector(
          onTap: () {
            setState(() {
              statusreward = 0;
            });
            print("Container clicked");
          },
          child: Container(
            color: Colors.transparent,
            height: height,
            width: width,
            child: Center(
                child: Image.asset(
              "assets/image/LDX/homeLDX/coin.gif",
              width: width,
            )),
          ));
    } else {
      return Container();
    }
  }

  ontabline() async {
    print("numads : ${numads}");
    // _launchURL("https://social-plugins.line.me/lineit/share?url=http%3A%2F%2Fdevdev.prachakij.com%2FnuiAPI%2FLDX%2FshowOnWeb.php%3FnumADS%3D${numads}");
    _launchURL(
        "https://social-plugins.line.me/lineit/share?url=http%3A%2F%2Fdevdev.prachakij.com%2FnuiAPI%2FLDX%2FshowOnWebNew.php%3Frunning_ads%3D${running_ads.toString()}");
  }

  List<Widget> Liststatusnews(double width) {
    List<Widget> list = [];
    for (var i = 0; i < fullads; i++) {
      if (i == sortADS - 1) {
        list.add(
          Expanded(
            child: Container(
              width: 6.0,
              height: 6.0,
              decoration: new BoxDecoration(
                color: Color(0xff00EAE7),
                shape: BoxShape.circle,
              ),
            ),
          ),
        );
      } else {
        list.add(
          Expanded(
            child: Container(
              width: 6.0,
              height: 6.0,
              decoration: new BoxDecoration(
                color: Color(0xffD0D0D0),
                shape: BoxShape.circle,
              ),
            ),
          ),
        );
      }
    }

    return list;
  }

  fetchPost(url) {
    return http.get(url);
  }

  Timer? _timer;
  int _start = 0;

  void startTimer(timeout) {
    _start = timeout;
    const oneSec = const Duration(seconds: 1);
    _timer = new Timer.periodic(
      oneSec,
      (Timer timer2) => setState(
        () {
          if (_start < 1) {
            timer2.cancel();
            if (statusProcessBack != 1) {
              transferlikeWatchADS();
            }
          } else {
            _start = _start - 1;
          }
        },
      ),
    );
  }

  _launchURL(url) async {
    print(url);
//    const url = 'https://flutter.dev';
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      throw 'Could not launch $url';
    }
  }

  void _handleURLButtonPress(
      BuildContext context, String url, String textappbar) {
    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) => WebViewContainer(url, textappbar)));
  }
}
