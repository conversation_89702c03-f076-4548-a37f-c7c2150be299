import 'dart:convert';
import 'dart:ui';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_analytics/observer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:global_configuration/global_configuration.dart';
import 'package:likewallet/LDX/ADS/showads.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:likewallet/screen_util.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';

import '../library.dart';

class ads extends StatefulWidget {
  @override
  _adsState createState() => _adsState();
}

class _adsState extends State<ads> {
  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;
  static FirebaseAnalyticsObserver observer =
      FirebaseAnalyticsObserver(analytics: analytics);

  bool _saving = false;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    allLoad();
  }

  allLoad() async {
    setState(() {
      _saving = true;
    });
    await doreloadads();
    setState(() {
      _saving = false;
    });
  }

  List dataads = [];
  doreloadads() async {
    dataads = [];
    String url =
        'https://1xpf4khsld.execute-api.ap-southeast-1.amazonaws.com/latest';
//    String url = 'https://086e808a.ngrok.io/agslearn/us-central1/ApiAppMS24';
    Map map = {"menu": "SearchADS"};

    final response = await apiRequest(url, map);
    List jsonResponse = json.decode(response);
    if (jsonResponse.length > 0) {
      setState(() {
        dataads = jsonResponse;
      });
    } else {
      print('news_notdata!!');
    }
  }

  @override
  Widget build(BuildContext context) {
    final height = MediaQuery.of(context).size.height;
    final width = MediaQuery.of(context).size.width;

    return ModalProgressHUD(
      inAsyncCall: _saving,
      opacity: 0.1,
      progressIndicator: CustomLoading(),
      child: Scaffold(
          body: Stack(
        children: <Widget>[
          Center(
              child: Container(
            decoration: BoxDecoration(
                gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [Color(0xff495764), Color(0xff0F0F0F)])),
          )),
          Container(
            margin: EdgeInsets.only(top: mediaQuery2(context, "h", 140)),
            child: ListView(
              padding: EdgeInsets.only(top: 0),
              children: <Widget>[
                Container(
                  alignment: Alignment.topCenter,
                  child: Image.asset("assets/image/LDX/ads/logoADS.png"),
                ),
                Container(
                  height: mediaQuery2(context, "h", 12.4),
                ),
                Container(
                  alignment: Alignment.topCenter,
                  child: Text(
                    AppLocalizations.of(context)!.translate("ui_ads"),
                    style: TextStyle(
                      fontFamily: 'Prompt-Medium',
                      fontSize: mediaQuery2(context, "h", 30),
                      color: const Color(0xe5ffffff),
                      letterSpacing: mediaQuery2(context, "h", 1.5),
                      fontWeight: FontWeight.w500,
                      height: 0.6666666666666666,
                      shadows: [
                        Shadow(
                          color: const Color(0x33000000),
                          offset: Offset(0, 1),
                          blurRadius: 2,
                        )
                      ],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Container(
                  height: mediaQuery2(context, "h", 47),
                ),
                new Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: Listviewads(),
                ),
              ],
            ),
          ),
          Positioned(
            top: MediaQuery.of(context).size.height *
                Screen_util("height", 139.33),
            left: MediaQuery.of(context).size.height * Screen_util("width", 5),
            child: GestureDetector(
              child: new Container(
                alignment: Alignment.centerLeft,
                width: MediaQuery.of(context).size.width,
                child: new IconButton(
                  icon: new Icon(
                    Icons.arrow_back_ios,
                    size: MediaQuery.of(context).size.height *
                        Screen_util("height", 44.47),
                  ),
                  color: Color(0xff707071),
                  onPressed: () => {Navigator.of(context).pop()},
                ),
              ),
            ),
          ),
          showHappyReward(),
        ],
      )),
    );
  }

  int statusshowHappyReward = 0;
  Widget showHappyReward() {
    if (statusshowHappyReward == 1) {
      return ClipRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 5.23, sigmaY: 5.23),
          child: Container(
            width: mediaQuery2(context, "w", 828),
            height: mediaQuery2(context, "h", 1792),
            alignment: Alignment.topCenter,
            decoration: BoxDecoration(
              color: const Color(0x00bebebe),
            ),
            child: Stack(
              children: <Widget>[
                Container(
                  alignment: Alignment.topCenter,
                  margin: EdgeInsets.only(top: mediaQuery2(context, "h", 336)),
                  child: Container(
                    width: mediaQuery2(context, "w", 580),
                    height: mediaQuery2(context, "h", 880),
                    decoration: BoxDecoration(
                      borderRadius:
                          BorderRadius.circular(mediaQuery2(context, "h", 60)),
                      gradient: LinearGradient(
                        begin: Alignment(0.04, 1.0),
                        end: Alignment(0.04, -1.0),
                        colors: [
                          const Color(0xffd0d0d0),
                          const Color(0xffffffff)
                        ],
                        stops: [0.0, 1.0],
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0x34000000),
                          offset: Offset(0, 5),
                          blurRadius: 10,
                        ),
                      ],
                    ),
                    child: Stack(
                      children: <Widget>[
                        Container(
                          alignment: Alignment.topCenter,
                          margin: EdgeInsets.only(
                              top: mediaQuery2(context, "h", 106)),
                          child: SvgPicture.asset(
                            "assets/image/LDX/homeLDX/Group-4070.svg",
                            height: mediaQuery2(context, "h", 88.82),
                          ),
                        ),
                        Container(
                          alignment: Alignment.topCenter,
                          margin: EdgeInsets.only(
                              top: mediaQuery2(context, "h", 229)),
                          child: Text.rich(
                            TextSpan(
                              style: TextStyle(
                                fontFamily: 'Prompt-Medium',
                                fontSize: mediaQuery2(context, "h", 30),
                                color: const Color(0xff2f7d8b),
                                letterSpacing: mediaQuery2(
                                    context, "h", 1.7999999999999998),
                                height: 1,
                                shadows: [
                                  Shadow(
                                    color: const Color(0x33000000),
                                    offset: Offset(0, 3),
                                    blurRadius: 3,
                                  )
                                ],
                              ),
                              children: [
                                TextSpan(
                                  text: 'LDX',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                TextSpan(
                                  text: ' ',
                                  style: TextStyle(
                                    color: const Color(0xff4b4b4b),
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                TextSpan(
                                  text: 'HAPPY',
                                  style: TextStyle(
                                    fontFamily: 'Prompt-SemiBold',
                                    color: const Color(0xff212830),
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                TextSpan(
                                  text: ' ',
                                  style: TextStyle(
                                    color: const Color(0xff4b4b4b),
                                  ),
                                ),
                                TextSpan(
                                  text: 'REWARD',
                                  style: TextStyle(
                                    color: const Color(0xfffffe37),
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        Container(
                          alignment: Alignment.topCenter,
                          margin: EdgeInsets.only(
                              top: mediaQuery2(context, "h", 392)),
                          child: Text.rich(
                            TextSpan(
                              style: TextStyle(
                                fontFamily: 'Prompt-Regular',
                                fontSize: mediaQuery2(context, "h", 26),
                                color: const Color(0xff4b4b4b),
                                letterSpacing: mediaQuery2(context, "h", 1.04),
                                height: 1.7857142857142858,
                                shadows: [
                                  Shadow(
                                    color: const Color(0x41000000),
                                    offset: Offset(0, 2),
                                    blurRadius: 3,
                                  )
                                ],
                              ),
                              children: [
                                TextSpan(
                                  text: AppLocalizations.of(context)!
                                          .translate("ui_adsAlert") +
                                      '\n',
                                ),
                                TextSpan(
                                  text: 'LDX Happy Reward\n',
                                  style: TextStyle(
                                    fontFamily: 'Prompt-Medium',
                                    fontSize: mediaQuery2(context, "h", 28),
                                    color: const Color(0xff2f3945),
                                    letterSpacing:
                                        mediaQuery2(context, "h", 1.12),
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                TextSpan(
                                  text: AppLocalizations.of(context)!
                                          .translate("ui_adsAlert2") +
                                      '\n' +
                                      AppLocalizations.of(context)!
                                          .translate("ui_adsAlert3") +
                                      ' ',
                                ),
                                TextSpan(
                                  text: 'Happy Pool',
                                  style: TextStyle(
                                    fontFamily: 'Prompt-Medium',
                                    fontSize: mediaQuery2(context, "h", 28),
                                    color: const Color(0xff006774),
                                    letterSpacing:
                                        mediaQuery2(context, "h", 1.12),
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        Container(
                          alignment: Alignment.topCenter,
                          margin: EdgeInsets.only(
                              top: mediaQuery2(context, "h", 739)),
                          child: GestureDetector(
                            onTap: () {
                              setState(() {
                                statusshowHappyReward = 0;
                              });
                            },
                            child: Container(
                              width: mediaQuery2(context, "w", 252),
                              height: mediaQuery2(context, "h", 70),
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(
                                    mediaQuery2(context, "h", 35)),
                                color: const Color(0xfe2f3945),
                                boxShadow: [
                                  BoxShadow(
                                    color: const Color(0x1a000000),
                                    offset: Offset(0, 1),
                                    blurRadius: 2,
                                  ),
                                ],
                              ),
                              child: Text(
                                AppLocalizations.of(context)!
                                    .translate("btn_adsAlert"),
                                style: TextStyle(
                                  fontFamily: 'Prompt-Medium',
                                  fontSize: mediaQuery2(context, "h", 30),
                                  color: const Color(0xfeffffff),
                                  letterSpacing: mediaQuery2(context, "h", 1.2),
                                  fontWeight: FontWeight.w500,
                                  // height: 1.0333333333333334,
                                  shadows: [
                                    Shadow(
                                      color: const Color(0x26000000),
                                      offset: Offset(0, 1),
                                      blurRadius: 1,
                                    )
                                  ],
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(top: mediaQuery2(context, "h", 190)),
                  alignment: Alignment.topCenter,
                  child: Image.asset(
                      "assets/image/LDX/ads/Congrat-get-likepoint.png",
                      height: mediaQuery2(context, "h", 679)),
                ),
                // Container(
                //     margin: EdgeInsets.only(top: mediaQuery2(context, "h", 190)),
                //     alignment: Alignment.topCenter,
                //     child: SvgPicture.asset("assets/images/ads/Congrat-get-likepoint.svg",height: mediaQuery2(context, "h", 679),)
                // ),
              ],
            ),
          ),
        ),
      );
    } else {
      return Container();
    }
  }

  List<Widget> Listviewads() {
    List<Widget> list = [];
    String Assessmenttimes = "";
    String date_seven = "";
    print("ทดสอบ ${dataads}");
    for (var i = 0; i < dataads.length.toInt(); i++) {
      list.add(
        Container(
          alignment: Alignment.topCenter,
          child: new GestureDetector(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (context) => showads(
                        dataads[i]["running"],
                        dataads[i]["linkImmageADS"],
                        dataads[i]["subjectADS"],
                        dataads[i]["textADS"],
                        dataads[i]["nameActivities"],
                        dataads[i]["numActivities"],
                        dataads.length.toInt(),
                        dataads[i]["timeout"],
                        "false",
                        dataads[i]["linkVDO"],
                        dataads[i]["statusVDO"],
                        dataads[i]["link"],
                        dataads[i]["id_share"],
                        dataads[i]["statusLink"],
                        dataads[i]["sortADS"])),
              );
              print("Container clicked");
            },
            child: Container(
              alignment: Alignment.bottomCenter,
              width: mediaQuery2(context, "w", 715),
              height: mediaQuery2(context, "h", 530),
              decoration: BoxDecoration(
                borderRadius:
                    BorderRadius.circular(mediaQuery2(context, "h", 40)),
                image: DecorationImage(
                  image:
                      new NetworkImage(dataads[i]["linkImmageADS"].toString()),
                  fit: BoxFit.cover,
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0x29000000),
                    offset: Offset(0, 3),
                    blurRadius: 6,
                  ),
                ],
              ),
              child: Container(
                width: mediaQuery2(context, "w", 715),
                height: mediaQuery2(context, "h", 125),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                    bottomRight: Radius.circular(mediaQuery2(context, "h", 40)),
                    bottomLeft: Radius.circular(mediaQuery2(context, "h", 40)),
                  ),
                  color: const Color(0xff191f26),
                  border:
                      Border.all(width: 2.0, color: const Color(0xff191f26)),
                ),
                child: Stack(
                  children: <Widget>[
                    Container(
                      margin: EdgeInsets.only(
                          top: mediaQuery2(context, "h", 26),
                          left: mediaQuery2(context, "w", 45),
                          right: mediaQuery2(context, "w", 80)),
                      child: Text(
                        dataads[i]["subjectADS"].toString(),
                        style: TextStyle(
                          fontFamily: 'Prompt-Medium',
                          fontSize: mediaQuery2(context, "h", 26),
                          color: const Color(0xffd0d0d0),
                          letterSpacing: mediaQuery2(context, "h", 1.3),
                          fontWeight: FontWeight.w500,
                          // height: 0.7692307692307693,
                          shadows: [
                            Shadow(
                              color: const Color(0x33000000),
                              offset: Offset(0, 1),
                              blurRadius: 2,
                            )
                          ],
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    dataads[i]["statusVDO"].toString() == "Y"
                        ? Container(
                            alignment: Alignment.topRight,
                            margin: EdgeInsets.only(
                                top: mediaQuery2(context, "h", 20),
                                right: mediaQuery2(context, "w", 40)),
                            child: SvgPicture.string(
                              '<svg viewBox="681.0 1315.0 50.0 50.0" ><path transform="translate(676.83, 1310.83)" d="M 24.16666603088379 40.41666412353516 L 39.16666793823242 29.16666793823242 L 24.16666793823242 17.91666412353516 L 24.16666793823242 40.41666412353516 Z M 29.16666793823242 4.166666507720947 C 15.36666679382324 4.166666507720947 4.166666507720947 15.36666679382324 4.166666507720947 29.16666793823242 C 4.166666507720947 42.96666717529297 15.36666679382324 54.16666793823242 29.16666793823242 54.16666793823242 C 42.96666717529297 54.16666793823242 54.16666793823242 42.9666748046875 54.16666793823242 29.16666793823242 C 54.16666793823242 15.36666679382324 42.96666717529297 4.166666507720947 29.16666793823242 4.166666507720947 Z M 29.16666793823242 49.16666793823242 C 18.14166641235352 49.16666793823242 9.166666984558105 40.19166946411133 9.166666984558105 29.16666793823242 C 9.166666984558105 18.14167022705078 18.14166641235352 9.166669845581055 29.16666793823242 9.166669845581055 C 40.19166946411133 9.166669845581055 49.16666412353516 18.14166831970215 49.16666412353516 29.16666793823242 C 49.16666412353516 40.19166946411133 40.1916618347168 49.16666793823242 29.16666412353516 49.16666793823242 Z" fill="#00eae7" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                              allowDrawingOutsideViewBox: true,
                              height: mediaQuery2(context, "h", 50),
                            ),
                          )
                        : Container(),
                  ],
                ),
              ),
            ),
//           child: Stack(
//             children: <Widget>[
//               Container(
//                 alignment: Alignment.bottomCenter,
//                 margin: EdgeInsets.fromLTRB(0, 20, 0, 0),
//                 width: width,
//                 height: 225,
//                 decoration: new BoxDecoration(
// //              color: Colors.red,
//                     image: new DecorationImage(
//                         fit: BoxFit.cover,
//                         image: new NetworkImage(dataads[i]["linkImmageADS"].toString())),
//                     //new Color.fromRGBO(255, 0, 0, 0.0),
//                     borderRadius: new BorderRadius.only(
//                         topLeft: const Radius.circular(30.0),
//                         topRight: const Radius.circular(30.0),
//                         bottomLeft: const Radius.circular(30.0),
//                         bottomRight: const Radius.circular(30.0))),
//                 child: Container(
//                     height: 65,
//                     width: width,
//                     decoration: new BoxDecoration(
//                         color: Color(0xff4E4B46).withOpacity(0.9),
//                         //new Color.fromRGBO(255, 0, 0, 0.0),
//                         borderRadius: new BorderRadius.only(
//                             bottomLeft: const Radius.circular(30.0),
//                             bottomRight: const Radius.circular(30.0))),
//                     child: Container(
//                       margin: EdgeInsets.fromLTRB(20, 10, 20, 0),
//                       child: Text(
//                         dataads[i]["subjectADS"].toString(),
//                         style: TextStyle(color: Color(0xffD0D0D0), fontSize: 14,fontFamily: 'Prompt-Medium'),
//                       ),
//                     )),
//               ),
// //              dataads[i]["statusVDO"].toString() == "Y" ? Container(
// //                margin: EdgeInsets.fromLTRB(0, 20, 0, 0),
// //                width: width,
// //                height: 225,
// //                alignment: Alignment.center,
// //                child: Image.asset("assets/images/ads/play.jpg",height: 50,),
// //              ):Container(),
//             ],
//           ),
          ),
        ),
      );
      list.add(Container(
        height: mediaQuery2(context, "h", 25),
      ));
    }

    return list;
  }
}
