import 'package:flutter/material.dart';
import 'package:likewallet/LDX/component/invite_to_%20download.dart';
import 'package:likewallet/LDX/library.dart';

class InviteToDownload extends StatefulWidget {
  @override
  _InviteToDownloadState createState() => _InviteToDownloadState();
}

class _InviteToDownloadState extends State<InviteToDownload> {
  bool openTab = false;
  int atBu = 0;
  List<dynamic> data = [
    {
      "image": "assets/ldx/logo-prachakij.png",
      "title": "Prachakij",
      "androidAppId": "com.prachakij.pms_app",
      "iOSAppId": "1507877291",
      "androidURl":
          "https://play.google.com/store/apps/details?id=com.prachakij.pms_app",
      "path": "pms://"
    },
    {
      "image": "assets/ldx/logo-aam.png",
      "title": "AAM",
      "androidAppId": "com.aamfinancegroup.aam",
      "iOSAppId": "1521965273",
      "androidURl":
          "https://play.google.com/store/apps/details?id=com.aamfinancegroup.aam",
      "path": "aam://"
    },
    {
      "image": "assets/ldx/logo-rplc.png",
      "title": "RPLC",
      "androidAppId": "com.ruampattanaleasing.rplc_app",
      "iOSAppId": "1541438944",
      "androidURl":
          "https://play.google.com/store/apps/details?id=com.ruampattanaleasing.rplc_app",
      "path": "rplcapp://"
    },
    {
      "image": "assets/ldx/logo-rafco.png",
      "title": "RAFCO",
      "androidAppId": "com.rptn.rafco",
      "iOSAppId": "1529562321",
      "androidURl":
          "https://play.google.com/store/apps/details?id=com.rptn.rafco",
      "path": "rafco://"
    }
  ];

  @override
  void initState() {
    super.initState();
  }

  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFF222C45),
      body: InkWell(
        onTap: () {
          setState(() => openTab = false);
        },
        child: Stack(
          children: [
            Column(
              children: [
                Row(
                  children: [
                    InviteComponent.buttonBackPage(context),
                  ],
                ),
                Expanded(
                  child: ListView.builder(
                    itemCount: data.length,
                    itemBuilder: (context, index) => AnimatedAlign(
                        alignment:
                            openTab ? Alignment.centerLeft : Alignment.center,
                        duration: Duration(milliseconds: 200),
                        child: Padding(
                          padding: EdgeInsets.only(
                            left: openTab ? 30 : 0,
                          ),
                          child: GestureDetector(
                            onTap: () {
                              setState(() {
                                atBu = index;
                                openTab = true;
                              });
                            },
                            child: InviteComponent.logoBU(
                                context: context,
                                image: data[index]['image'],
                                title: data[index]['title']),
                          ),
                        )),
                  ),
                ),
                openTab
                    ? Container()
                    : Column(
                        children: [
                          SizedBox(
                            height: 5,
                          ),
                          InviteComponent.textRichEarn(),
                          SizedBox(
                            height: 5,
                          ),
                          InviteComponent.textRichNote(),
                          SizedBox(
                            height: 20,
                          ),
                        ],
                      ),
                openTab
                    ? Container()
                    : InviteComponent.bgInviteDownload(context),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                AnimatedContainer(
                  duration: Duration(milliseconds: 250),
                  height: double.infinity,
                  alignment: Alignment.centerRight,
                  width: openTab ? MediaQuery.of(context).size.width / 1.5 : 0,
                  child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment(0.0, -1.0),
                          end: Alignment(0.0, 1.0),
                          colors: [
                            const Color(0xff00eae7),
                            const Color(0xff007895)
                          ],
                          stops: [0.0, 1.0],
                        ),
                      ),
                      child:
                          InviteComponent.textBu(index: atBu, close: openTab)),
                ),
              ],
            ),
            Positioned(
                bottom: 0, child: InviteComponent.inviteDownload(context)),
          ],
        ),
      ),
    );
  }
}
