import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as flutter_riverpod;
import 'package:flutter_svg/svg.dart';
import 'package:likewallet/LDX/dashborad/dashborad_poi.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/libraryman/auth.dart';
import 'package:likewallet/LDX/invite_to_ download.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';

import '../ImageTheme.dart';
import '../main.dart';
import '../screen_util.dart';
import 'ADS/ads.dart';
import 'library.dart';
//
// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:likewallet/LDX/component/home.dart';

class homeLDX extends StatefulWidget {
  @override
  _homeLDXState createState() => _homeLDXState();
}

class _homeLDXState extends State<homeLDX> {
//   @override
//   // TODO: implement context
//   Widget build(BuildContext context) {
//     // TODO: implement build
//     return ScreenUtilInit(
//       designSize: Size(428, 926),
//       builder: () => Scaffold(
//         body: Stack(
//           children: [
//             HomeComponent.bg(),
//             Column(
//               children: [
//                 HomeComponent.title(),
//                 HomeComponent.showLikePoint(),
//                 SizedBox(height: 21.h),
//                 HomeComponent.nameAndRanking(),
//                 HomeComponent.objectMenu(),
//               ],
//             ),
//           ],
//         ),
//       ),
//     );

  String _uploadedFileURL = "noprofile";
  late BaseAuth auth;
  bool _saving = false;

  String firstName = "..loading";
  String lastName = "";
  String phoneNumber = "";
  int refers = 0;

  @override
  void initState() {
    super.initState();
    auth = new Auth();
    allLoad();
  }

  allLoad() async {
    setState(() {
      _saving = true;
    });
    await setInit();
    // await setsessionlikebalance();
    setState(() {
      _saving = false;
    });
  }

  setInit() {
    auth.getCurrentUser().then((user) {
      if (!mounted) return;

      FirebaseFirestore.instance
          .collection('users')
          .doc(user!.uid)
          .get()
          .then((DocumentSnapshot<Map<String, dynamic>> ds) {
        setState(() {
          firstName = ds.data()!["firstName"];
          lastName = ds.data()!["lastName"];
          phoneNumber = user.phoneNumber.toString();
          _uploadedFileURL = ds.data()!["imageProfile"] ?? '';
        });
        FirebaseFirestore.instance
            .collection('users')
            .where('refCode', isEqualTo: ds.data()!["selfCode"])
            .snapshots()
            .listen((data) {
          setState(() {
            refers = data.docs.length;
          });
        });
      });
    });
  }

//   String likebalance = "";
//   String likebalanceShow = "";
//   setsessionlikebalance() async {
//     String url = 'https://new.likepoint.io/getBalanceByphoneNumber';
//     Map map = {"phoneNumber": phoneNumber.toString()};
// //    Map map = {"phoneNumber": "+66858802504"};
//
//     final response = await apiRequest(url, map);
//     var jsonResponse = json.decode(response);
//     if (jsonResponse["status"].toString() == "200") {
//       print("balance : " + jsonResponse.toString());
//       // likebalance = "16789654";
//       // likebalance = "500";
//       setState(() {
//         likebalance = jsonResponse['balance'].toDouble().toStringAsFixed(0).toString();
//         likebalanceShow = jsonResponse['balance'].toDouble().toStringAsFixed(2).toString();
//       });
//     } else {
//       print('No data!');
//     }
//   }

  @override
  Widget build(BuildContext context) {
    return ModalProgressHUD(
      inAsyncCall: _saving,
      opacity: 0.1,
      progressIndicator: CustomLoading(),
      child: Scaffold(
        body: Stack(
          children: [
            Container(
              color: Color(0xff08e8de),
            ),
            Positioned(
              top: MediaQuery.of(context).size.height *
                  Screen_util("height", 139.33),
              left:
                  MediaQuery.of(context).size.height * Screen_util("width", 5),
              child: GestureDetector(
                child: new Container(
                  alignment: Alignment.centerLeft,
                  width: MediaQuery.of(context).size.width,
                  child: new IconButton(
                    icon: new Icon(
                      Icons.arrow_back_ios,
                      size: MediaQuery.of(context).size.height *
                          Screen_util("height", 44.47),
                    ),
                    color: Color(0xff707071),
                    onPressed: () => {Navigator.of(context).pop()},
                  ),
                ),
              ),
            ),
            Container(
              alignment: Alignment.bottomCenter,
              child: Container(
                width: mediaQuery1(context, "w", 1080),
                height: mediaQuery1(context, "h", 2010),
                decoration: BoxDecoration(
                  color: Color(0xff0b0b0b),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(mediaQuery2(context, "h", 80)),
                    topRight: Radius.circular(mediaQuery2(context, "h", 80)),
                  ),
                ),
              ),
            ),
            Container(
              alignment: Alignment.bottomCenter,
              child: Container(
                width: mediaQuery1(context, "w", 1080),
                height: mediaQuery1(context, "h", 2005),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(mediaQuery2(context, "h", 80)),
                    topRight: Radius.circular(mediaQuery2(context, "h", 80)),
                  ),
                  gradient: LinearGradient(
                    begin: Alignment(0.0, -1.0),
                    end: Alignment(0.0, 1.0),
                    colors: [const Color(0xff212830), const Color(0xff0f0f0f)],
                    stops: [0.0, 1.0],
                  ),
                ),
              ),
            ),
            Container(
              alignment: Alignment.topCenter,
              margin: EdgeInsets.only(top: mediaQuery1(context, "h", 205)),
              child: Container(
                width: mediaQuery1(context, "h", 220),
                height: mediaQuery1(context, "h", 220),
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  color: Color(0xff2E2E2E),
                  borderRadius:
                      BorderRadius.all(Radius.elliptical(9999.0, 9999.0)),
                ),
                child: Container(
                  width: mediaQuery1(context, "h", 209),
                  height: mediaQuery1(context, "h", 209),
                  decoration: BoxDecoration(
                    borderRadius:
                        BorderRadius.all(Radius.elliptical(9999.0, 9999.0)),
                    image: DecorationImage(
                      image: _uploadedFileURL == 'noprofile'
                          ? AssetImage(
                              LikeWalletImage.defaultProfile,
                            ) as ImageProvider
                          : NetworkImage(_uploadedFileURL),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ),
            ),
            // Container(
            //   alignment: Alignment.topCenter,
            //   margin: EdgeInsets.only(top: mediaQuery1(context, "h", 425)),
            //   child: Text.rich(
            //     TextSpan(
            //       style: TextStyle(
            //         fontFamily: 'Prompt-Light',
            //         fontSize: mediaQuery1(context, "h", 40),
            //         color: const Color(0xffffffff),
            //         letterSpacing: mediaQuery1(context, "h", 1.2),
            //       ),
            //       children: [
            //         TextSpan(
            //           text:
            //               AppLocalizations.of(context)!.translate("ui_homeHey"),
            //           style: TextStyle(
            //             fontWeight: FontWeight.w300,
            //           ),
            //         ),
            //         TextSpan(
            //           text: ' ' + firstName.toString(),
            //           style: TextStyle(
            //             fontFamily: 'Prompt-Regular',
            //             fontSize: mediaQuery1(context, "h", 45),
            //             color: const Color(0xff28eae7),
            //             letterSpacing:
            //                 mediaQuery1(context, "h", 1.3499999999999999),
            //           ),
            //         ),
            //       ],
            //     ),
            //     textHeightBehavior:
            //         TextHeightBehavior(applyHeightToFirstAscent: false),
            //     textAlign: TextAlign.center,
            //   ),
            // ),
            GestureDetector(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => dashboardPoi()),
                );
              },
              child: Container(
                margin: EdgeInsets.only(
                  top: mediaQuery3(context, "h", 130),
                  left: mediaQuery3(context, "w", 30),
                ),
                //     left: mediaQuery3(context, "w", 111)),
                child: SvgPicture.asset(
                  "assets/ldx/btnDashborad.svg",
                  fit: BoxFit.fitHeight,
                  height: mediaQuery3(context, "h", 40),
                ),
              ),
            ),
            Container(
              alignment: Alignment.topCenter,
              margin: EdgeInsets.only(top: mediaQuery1(context, "h", 586)),
              child: Container(
                width: mediaQuery1(context, "w", 1010),
                height: mediaQuery1(context, "h", 147),
                decoration: BoxDecoration(
                  borderRadius:
                      BorderRadius.circular(mediaQuery1(context, "h", 40)),
                  color: const Color(0xff000000),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0x4d000000),
                      offset: Offset(0, 3),
                      blurRadius: 12,
                    ),
                  ],
                ),
                child: Stack(
                  children: <Widget>[
                    Container(
                      alignment: Alignment.centerLeft,
                      margin:
                          EdgeInsets.only(left: mediaQuery2(context, "w", 37)),
                      child: Text(
                        AppLocalizations.of(context)!
                            .translate("ui_homepointLikepoint"),
                        style: TextStyle(
                          fontFamily: 'Prompt-Medium',
                          fontSize: mediaQuery2(context, "h", 28),
                          color: const Color(0xffd0d0d0),
                          letterSpacing:
                              mediaQuery2(context, "h", 1.4000000000000001),
                          fontWeight: FontWeight.w500,
                          height: 1.3571428571428572,
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    Container(
                      alignment: Alignment.centerRight,
                      margin: EdgeInsets.only(
                          right: mediaQuery2(context, "w", 100)),
                      child: Text(
                        context
                            .read(totalAmount)
                            .state
                            .toStringAsFixed(2)
                            .toString()
                            .replaceAllMapped(
                                new RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                                (Match m) => '${m[1]},'),
                        style: TextStyle(
                          fontFamily: 'Prompt-Regular',
                          fontSize: mediaQuery2(context, "h", 35),
                          color: const Color(0xffd0d0d0),
                          letterSpacing: mediaQuery2(context, "h", 1.4),
                          // height: 1.2222222222222223,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Container(
                      alignment: Alignment.centerRight,
                      margin: EdgeInsets.only(
                          right: mediaQuery2(context, "w", 29.7)),
                      child: Text(
                        AppLocalizations.of(context)!.translate("ui_homelike"),
                        style: TextStyle(
                          fontFamily: 'Prompt-SemiBold',
                          fontSize: mediaQuery2(context, "h", 24),
                          color: const Color(0xff00eae7),
                          letterSpacing:
                              mediaQuery2(context, "h", 1.2000000000000002),
                          fontWeight: FontWeight.w600,
                          height: 1.4,
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Container(
              margin: EdgeInsets.only(
                  top: mediaQuery2(context, "h", 630),
                  left: mediaQuery2(context, "w", 70)),
              child: Text(
                AppLocalizations.of(context)!.translate("ui_homerecommend"),
                style: TextStyle(
                  fontFamily: 'Prompt-Medium',
                  fontSize: mediaQuery2(context, "h", 28),
                  color: const Color(0xff808080),
                  letterSpacing: mediaQuery2(context, "h", 1.4000000000000001),
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.left,
              ),
            ),
            Container(
              margin: EdgeInsets.only(
                  top: mediaQuery2(context, "h", 715),
                  left: mediaQuery2(context, "w", 40),
                  right: mediaQuery2(context, "w", 39)),
              child: Column(
                children: <Widget>[
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: <Widget>[
                      GestureDetector(
                        onTap: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => InviteToDownload(),
                              ));
                        },
                        child: Container(
                          width: mediaQuery2(context, "w", 320),
                          height: mediaQuery2(context, "h", 236),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(
                                mediaQuery2(context, "h", 20)),
                            color: const Color(0xff2e2e2e),
                            boxShadow: [
                              BoxShadow(
                                color: const Color(0x4d000000),
                                offset: Offset(0, 3),
                                blurRadius: 3,
                              ),
                            ],
                          ),
                          child: Stack(
                            children: <Widget>[
                              Container(
                                margin: EdgeInsets.only(
                                    top: mediaQuery2(context, "h", 28),
                                    left: mediaQuery2(context, "w", 35)),
                                child: Text(
                                  AppLocalizations.of(context)!
                                      .translate("ui_homeDownload"),
                                  style: TextStyle(
                                    fontFamily: 'Prompt-Medium',
                                    fontSize: mediaQuery2(context, "h", 26),
                                    color: const Color(0xffd0d0d0),
                                    letterSpacing:
                                        mediaQuery2(context, "h", 1.04),
                                    fontWeight: FontWeight.w500,
                                    shadows: [
                                      Shadow(
                                        color: const Color(0x99000000),
                                        offset: Offset(0, 1),
                                        blurRadius: 3,
                                      )
                                    ],
                                  ),
                                  textAlign: TextAlign.left,
                                ),
                              ),
                              Container(
                                  alignment: Alignment.bottomRight,
                                  margin: EdgeInsets.only(
                                      right: mediaQuery2(context, "w", 28)),
                                  child: SvgPicture.asset(
                                      "assets/ldx/phone_active.svg",
                                      height: mediaQuery2(context, "h", 165))
                                  // child: SvgPicture.asset("assets/images/home/<USER>",height: mediaQuery2(context, "h", 143.58),)
                                  ),
                              Container(
                                  alignment: Alignment.bottomLeft,
                                  margin: EdgeInsets.only(
                                      left: mediaQuery2(context, "w", 35),
                                      bottom: mediaQuery2(context, "w", 30)),
                                  child: SvgPicture.asset(
                                    "assets/ldx/015__circle_plus.svg",
                                    height: mediaQuery2(context, "h", 33),
                                  )),
                            ],
                          ),
                        ),
                      ),
                      GestureDetector(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(builder: (context) => ads()),
                          );
                        },
                        child: Container(
                          width: mediaQuery2(context, "w", 320),
                          height: mediaQuery2(context, "h", 236),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(
                                mediaQuery2(context, "h", 20)),
                            color: const Color(0xff2e2e2e),
                            boxShadow: [
                              BoxShadow(
                                color: const Color(0x4d000000),
                                offset: Offset(0, 3),
                                blurRadius: 3,
                              ),
                            ],
                          ),
                          child: Stack(
                            children: <Widget>[
                              Container(
                                alignment: Alignment.topRight,
                                margin: EdgeInsets.only(
                                    top: mediaQuery2(context, "h", 31),
                                    right: mediaQuery2(context, "w", 41)),
                                child: Text(
                                  AppLocalizations.of(context)!
                                      .translate("ui_homeShareADS"),
                                  style: TextStyle(
                                    fontFamily: 'Prompt-Medium',
                                    fontSize: mediaQuery2(context, "h", 26),
                                    color: const Color(0xffd0d0d0),
                                    letterSpacing:
                                        mediaQuery2(context, "h", 1.04),
                                    fontWeight: FontWeight.w500,
                                    shadows: [
                                      Shadow(
                                        color: const Color(0x99000000),
                                        offset: Offset(0, 1),
                                        blurRadius: 3,
                                      )
                                    ],
                                  ),
                                  textAlign: TextAlign.left,
                                ),
                              ),
                              Container(
                                  alignment: Alignment.bottomRight,
                                  margin: EdgeInsets.only(
                                      right: mediaQuery2(context, "w", 44),
                                      bottom: mediaQuery2(context, "h", 22.7)),
                                  child: SvgPicture.asset(
                                    "assets/image/LDX/homeLDX/online-marketing.svg",
                                    height: mediaQuery2(context, "h", 126.26),
                                  )),
                              Container(
                                  margin: EdgeInsets.only(
                                      left: mediaQuery2(context, "w", 29),
                                      top: mediaQuery2(context, "h", 29)),
                                  child: SvgPicture.asset(
                                    "assets/image/LDX/homeLDX/075__navigation.svg",
                                    height: mediaQuery2(context, "h", 32.79),
                                  )),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
