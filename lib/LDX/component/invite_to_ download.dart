import 'dart:math' as math;
import 'dart:ui' as ui;
import 'package:delayed_display/delayed_display.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:likewallet/LDX/library.dart';

class InviteComponent {
  InviteComponent._();

  static List<dynamic> data = [
    {
      "title": "Prachakij Mobile",
      "detail":
          "Service applications that accesses \nall of our services ISUZU Chanthaburi.",
      "androidAppId": "com.prachakij.pms_app",
      "iOSAppId": "1507877291",
      "androidURl":
          "https://play.google.com/store/apps/details?id=com.prachakij.pms_app",
      "path": "pms://"
    },
    {
      "title": "AAM Finance",
      "detail":
          "Be easy to experience all types of cash \nloan services and consulting.",
      "androidAppId": "com.aamfinancegroup.aam",
      "iOSAppId": "1521965273",
      "androidURl":
          "https://play.google.com/store/apps/details?id=com.aamfinancegroup.aam",
      "path": "aam://"
    },
    {
      "title": "RPLC Finance",
      "detail":
          "Be easy to experience all types of cash \nloan services and consulting.",
      "androidAppId": "com.ruampattanaleasing.rplc_app",
      "iOSAppId": "1541438944",
      "androidURl":
          "https://play.google.com/store/apps/details?id=com.ruampattanaleasing.rplc_app",
      "path": "rplcapp://"
    },
    {
      "title": "RAFCO Finance",
      "detail":
          "Be easy to experience all types of cash \nloan services and consulting.",
      "androidAppId": "com.rptn.rafco",
      "iOSAppId": "1529562321",
      "androidURl":
          "https://play.google.com/store/apps/details?id=com.rptn.rafco",
      "path": "rafco://"
    },
  ];

  static Widget buttonBackPage(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).pop();
      },
      child: Container(
        margin: EdgeInsets.only(
          top: mediaQuery1(context, "h", 120),
          left: mediaQuery1(context, "w", 84),
        ),
        child: Row(
          children: [
            Container(
              alignment: Alignment.center,
              color: Colors.transparent,
              width: mediaQuery1(context, "h", 100),
              height: mediaQuery1(context, "h", 100),
              child: SvgPicture.string(
                '<svg viewBox="0.0 0.0 20.0 32.0" ><path transform="translate(-3664.4, -626.0)" d="M 3684.397216796875 625.9999389648438 L 3664.397216796875 642 L 3684.397216796875 657.9998779296875" fill="none" stroke="#fffd00" stroke-width="5" stroke-linecap="round" stroke-linejoin="round" /></svg>',
                allowDrawingOutsideViewBox: true,
                fit: BoxFit.fill,
                height: mediaQuery1(context, "h", 32),
              ),
            ),
            Text(
              'Back',
              style:
                  TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
            ),
          ],
        ),
      ),
    );
  }

  static Widget logoBU(
      {required BuildContext context,
      required String image,
      required String title}) {
    return Container(
      child: Column(
        children: [
          Image.asset(
            image,
            height: mediaQuery1(context, "h", 200),
          ),
          Text(
            title,
            style: TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.normal),
          ),
          SizedBox(
            height: 10,
          ),
        ],
      ),
    );
  }

  static Widget textRichEarn() {
    return Text.rich(
      TextSpan(
        style: TextStyle(
          fontFamily: 'IBM Plex Sans Thai',
          fontSize: 13,
          color: const Color(0xffffffff),
          letterSpacing: 0.34,
          shadows: [
            Shadow(
              color: const Color(0x1a000000),
              offset: Offset(0, 2),
              blurRadius: 2,
            )
          ],
        ),
        children: [
          TextSpan(
            text: 'Earn ',
            style: TextStyle(
              fontWeight: FontWeight.w700,
            ),
          ),
          TextSpan(
            text: '1,000 LIKE ',
            style: TextStyle(
              color: const Color(0xff2cffff),
              fontWeight: FontWeight.w700,
            ),
          ),
          TextSpan(
            text: '/ 1 User',
            style: TextStyle(
              fontSize: 12,
              letterSpacing: 0.28,
              fontWeight: FontWeight.w300,
            ),
          ),
        ],
      ),
      textAlign: TextAlign.left,
    );
  }

  static Widget textRichNote() {
    return Text.rich(
      TextSpan(
        style: TextStyle(
          fontFamily: 'IBM Plex Sans Thai',
          fontSize: 11,
          color: const Color(0xffffffff),
          letterSpacing: 0.28,
          shadows: [
            Shadow(
              color: const Color(0x1a000000),
              offset: Offset(0, 1),
              blurRadius: 5,
            )
          ],
        ),
        children: [
          TextSpan(
            text: 'Note :',
            style: TextStyle(
              fontWeight: FontWeight.w500,
            ),
          ),
          TextSpan(
            text: ' Only users has been \ndownloaded and installed.',
            style: TextStyle(
              fontWeight: FontWeight.w300,
            ),
          ),
        ],
      ),
      textAlign: TextAlign.left,
    );
  }

  static Widget textRichINVITE() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text.rich(
          TextSpan(
            style: TextStyle(
              fontFamily: 'IBM Plex Sans Thai',
              fontSize: 20,
              color: const Color(0xffffffff),
              letterSpacing: 0.5,
              shadows: [
                Shadow(
                  color: const Color(0x1a000000),
                  offset: Offset(0, 1),
                  blurRadius: 5,
                )
              ],
            ),
            children: [
              TextSpan(
                text: 'INVITE ',
                style: TextStyle(
                  fontWeight: FontWeight.w700,
                ),
              ),
              TextSpan(
                text: 'TO',
                style: TextStyle(
                  fontSize: 12,
                  letterSpacing: 0.24,
                ),
              ),
            ],
          ),
          textAlign: TextAlign.left,
        ),
        Text(
          'Download',
          style: TextStyle(
            fontFamily: 'IBM Plex Sans Thai',
            fontSize: 20,
            color: const Color(0xe52cffff),
            letterSpacing: 0.5,
          ),
          textAlign: TextAlign.left,
        ),
        Text(
          'Get more my service partners\nApplications.',
          style: TextStyle(
            fontFamily: 'IBM Plex Sans Thai',
            fontSize: 11.5,
            color: const Color(0xe5ffffff),
            letterSpacing: 0.28,
            fontWeight: FontWeight.w300,
            height: 1.4285714285714286,
            shadows: [
              Shadow(
                color: const Color(0x1a000000),
                offset: Offset(0, 1),
                blurRadius: 5,
              )
            ],
          ),
          textAlign: TextAlign.left,
        ),
      ],
    );
  }

  static Widget bgInviteDownload(BuildContext context) {
    return Stack(
      children: [
        Container(
          height: 150,
          width: MediaQuery.of(context).size.width,
          child: SvgPicture.string(
            '<svg viewBox="0.0 740.0 428.0 194.3" ><defs><filter id="shadow"><feDropShadow dx="0" dy="-3" stdDeviation="10"/></filter><linearGradient id="gradient" x1="1.0" y1="0.089435" x2="0.33328" y2="1.0"><stop offset="0.0" stop-color="#ff00eae7"  /><stop offset="1.0" stop-color="#ff007895"  /></linearGradient></defs><path transform="translate(-1822.0, -980.82)" d="M 1822 1733 C 1822 1733 1868.1962890625 1758.564453125 1946.670776367188 1738.425048828125 C 2025.145141601562 1718.285766601562 2038.186889648438 1715.52197265625 2106.03955078125 1729.6171875 C 2173.892333984375 1743.71240234375 2187.44189453125 1729.745849609375 2213.727783203125 1729.6171875 C 2240.013671875 1729.488525390625 2250.000244140625 1741.475708007812 2250.000244140625 1741.475708007812 L 2250.000244140625 1915.134765625 L 1822 1915.134765625 L 1822 1733 Z" fill="url(#gradient)" stroke="#2cffff" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" filter="url(#shadow)"/></svg>',
            allowDrawingOutsideViewBox: true,
            // height: 150,
            // width: double.infinity,
            fit: BoxFit.fill,
          ),
        ),
        Positioned(
          left: 80.w,
          bottom: -100.h,
          child: Transform(
            alignment: Alignment.center,
            transform: Matrix4.rotationY(math.pi),
            child: SvgPicture.asset(
              'assets/ldx/avatar_icon.svg',
              height: 150,
            ),
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Container(
                margin: EdgeInsets.only(top: 30, right: 16),
                child: textRichINVITE())
          ],
        )
      ],
    );
  }

  static Widget inviteDownload(BuildContext context) {
    return Container(
      height: 150,
      width: MediaQuery.of(context).size.width,
      child: Stack(
        children: [
          Positioned(
            left: 80.w,
            bottom: -100.h,
            child: Transform(
              alignment: Alignment.center,
              transform: Matrix4.rotationY(math.pi),
              child: SvgPicture.asset(
                'assets/ldx/avatar_icon.svg',
                height: 150,
              ),
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Container(
                  margin: EdgeInsets.only(top: 30, right: 16),
                  child: textRichINVITE())
            ],
          )
        ],
      ),
    );
  }

  static Widget textBu({required int index, required bool close}) {
    return Padding(
      padding: const EdgeInsets.only(left: 20.0),
      child: close
          ? DelayedDisplay(
              fadingDuration: const Duration(milliseconds: 500),
              slidingBeginOffset: const Offset(1.0, 0.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    height: 100,
                  ),
                  Text(
                    data[index]['title'].toString(),
                    style: TextStyle(
                      fontFamily: 'IBM Plex Sans Thai',
                      fontSize: 15,
                      color: const Color(0xe5ffffff),
                      letterSpacing: 0.4,
                      fontWeight: FontWeight.w700,
                      shadows: [
                        Shadow(
                          color: const Color(0x1a000000),
                          offset: Offset(0, 1),
                          blurRadius: 5,
                        )
                      ],
                    ),
                    textAlign: TextAlign.left,
                  ),
                  Text(
                    data[index]['detail'].toString(),
                    style: TextStyle(
                      fontFamily: 'IBM Plex Sans Thai',
                      fontSize: 12,
                      color: const Color(0xffffffff),
                      letterSpacing: 0.28,
                      fontWeight: FontWeight.w300,
                      height: 1.4285714285714286,
                      shadows: [
                        Shadow(
                          color: const Color(0x1a000000),
                          offset: Offset(0, 1),
                          blurRadius: 2,
                        )
                      ],
                    ),
                    textAlign: TextAlign.left,
                  ),
                  SizedBox(
                    height: 20,
                  ),
                  InkWell(
                    onTap: () {
                      openApp(
                          androidAppId: data[index]['androidAppId'],
                          androidURl: data[index]['androidURl'],
                          iOSAppId: data[index]['iOSAppId'],
                          path: data[index]['path']);
                    },
                    child: Stack(
                      children: <Widget>[
                        Container(
                            height: 30,
                            width: 100,
                            decoration: BoxDecoration(
                              color: Color(0xff00DEE1),
                              borderRadius: BorderRadius.circular(32.0),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.grey.withOpacity(0.1),
                                  spreadRadius: 1,
                                  blurRadius: 10,
                                  offset: Offset(0, 0), // Shadow position
                                ),
                              ],
                              border: Border.all(
                                  width: 0.8, color: const Color(0xff2cffff)),
                            ),
                            child: Container(
                              alignment: Alignment.center,
                              child: Text(
                                "Invite friends",
                                style: TextStyle(
                                  fontFamily: 'IBM Plex Sans Thai',
                                  fontSize: 12,
                                  color: const Color(0xe5ffffff),
                                  letterSpacing: 0.32,
                                  shadows: [
                                    Shadow(
                                      color: const Color(0x1a000000),
                                      offset: Offset(0, 1),
                                      blurRadius: 2,
                                    )
                                  ],
                                ),
                              ),
                            ))
                      ],
                    ),
                  ),
                ],
              ),
            )
          : Container(),
    );
  }
}
