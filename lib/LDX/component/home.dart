import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class HomeComponent {
  HomeComponent._();

  static Widget bg() {
    return SvgPicture.asset(
      'assets/ldx/BG.svg',
      height: double.infinity,
      width: double.infinity,
      fit: BoxFit.fill,
    );
  }

  static Widget title() {
    return Container(
        width: double.infinity,
        height: 153.0.h,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment(0.0, -1.0),
            end: Alignment(0.0, 1.0),
            colors: [const Color(0xff00eae7), const Color(0xff00eae7)],
            stops: [0.0, 1.0],
          ),
        ),
        child: Container(
          alignment: Alignment.center,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text(
                '“ Earn more ”',
                style: TextStyle(
                  fontFamily: 'IBM Plex Sans Thai',
                  fontSize: 20.h,
                  color: const Color(0xe5ffffff),
                  letterSpacing: 0.4,
                  fontWeight: FontWeight.w700,
                  shadows: [
                    Shadow(
                      color: const Color(0x1a000000),
                      offset: Offset(0, 1),
                      blurRadius: 2,
                    )
                  ],
                ),
                textAlign: TextAlign.left,
              ),
              Text.rich(
                TextSpan(
                  style: TextStyle(
                    fontFamily: 'IBM Plex Sans Thai',
                    fontSize: 16.h,
                    color: const Color(0xff1f273e),
                    letterSpacing: 0.32,
                  ),
                  children: [
                    TextSpan(
                      text: 'with',
                    ),
                    TextSpan(
                      text: ' ',
                      style: TextStyle(
                        fontWeight: FontWeight.w300,
                      ),
                    ),
                    TextSpan(
                      text: 'LikeWallet',
                      style: TextStyle(
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    TextSpan(
                      text: ' ',
                      style: TextStyle(
                        fontWeight: FontWeight.w300,
                      ),
                    ),
                    TextSpan(
                      text: 'partner Campaigns',
                    ),
                  ],
                ),
                textAlign: TextAlign.left,
              ),
              SizedBox(height: 21.h),
            ],
          ),
        ));
  }

  static Widget showLikePoint() {
    return Container(
      width: double.infinity,
      height: 87.0.h,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment(0.0, -1.0),
          end: Alignment(0.0, 1.0),
          colors: [const Color(0xff222c45), const Color(0xff141322)],
          stops: [0.0, 1.0],
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'Total Partners Campaign Rewards',
            style: TextStyle(
              fontFamily: 'IBM Plex Sans Thai',
              fontSize: 14.h,
              color: const Color(0xe5ffffff),
              letterSpacing: 0.42,
              fontWeight: FontWeight.w300,
            ),
            textAlign: TextAlign.left,
          ),
          SizedBox(
            height: 13.h,
          ),
          Row(
            // crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Text(
                '23,462,034.53',
                style: TextStyle(
                  fontFamily: 'IBM Plex Sans Thai',
                  fontSize: 25.h,
                  color: const Color(0xff2cffff),
                  letterSpacing: 0.5,
                ),
                textAlign: TextAlign.left,
              ),
              SizedBox(
                width: 20.w,
              ),
              Text(
                'LIKE',
                style: TextStyle(
                  fontFamily: 'IBM Plex Sans Thai',
                  fontSize: 14.h,
                  color: const Color(0xfffffd00),
                  letterSpacing: 0.28,
                ),
                textAlign: TextAlign.left,
              ),
            ],
          ),
        ],
      ),
    );
  }

  static Widget nameAndRanking() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [name(), ranking()],
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            likeFrom(),
          ],
        )
      ],
    );
  }

  static Widget name() {
    return Text(
      'Hi..Krittanan',
      style: TextStyle(
        fontFamily: 'IBM Plex Sans Thai',
        fontSize: 15.h,
        color: const Color(0xffffffff),
        letterSpacing: 0.6,
        shadows: [
          Shadow(
            color: const Color(0x1a000000),
            offset: Offset(0, 1),
            blurRadius: 2,
          )
        ],
      ),
      textAlign: TextAlign.left,
    );
  }

  static Widget ranking() {
    return Row(
      children: [
        SvgPicture.asset(
          'assets/ldx/Icon_crown.svg',
          height: 11.2.h,
        ),
        SizedBox(width: 5.w),
        Text.rich(
          TextSpan(
            style: TextStyle(
              fontFamily: 'IBM Plex Sans Thai',
              fontSize: 13.h,
              color: const Color(0xff1f273e),
              letterSpacing: 0.26,
            ),
            children: [
              TextSpan(
                text: 'Your Global',
              ),
              TextSpan(
                text: ' Ranking : 213',
                style: TextStyle(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          textAlign: TextAlign.left,
        )
      ],
    );
  }

  static Widget likeFrom() {
    return Container(
      // height: 48.h,
      width: 134.w,
      child: Stack(
        // alignment: Alignment.centerRight,
        children: [
          Positioned(
              top: 0,
              right: 0,
              child: SvgPicture.asset('assets/ldx/like_from.svg')),
          Container(
            padding: EdgeInsets.only(top: 20.h),
            child: Text.rich(
              TextSpan(
                style: TextStyle(
                  fontFamily: 'IBM Plex Sans Thai',
                  fontSize: 15.h,
                  color: const Color(0xffffffff),
                  letterSpacing: 0.3,
                  height: 1.3,
                  shadows: [
                    Shadow(
                      color: const Color(0x27000000),
                      offset: Offset(0, 1),
                      blurRadius: 2,
                    )
                  ],
                ),
                children: [
                  TextSpan(
                    text: 'LIKE',
                  ),
                  TextSpan(
                    text: ' ',
                    style: TextStyle(
                      color: const Color(0xff2ee7ff),
                      fontWeight: FontWeight.w300,
                    ),
                  ),
                  TextSpan(
                    text: 'from \n',
                    style: TextStyle(
                      fontWeight: FontWeight.w300,
                    ),
                  ),
                  TextSpan(
                    text: 'Partners Campaign',
                    style: TextStyle(
                      color: const Color(0xff1f273e),
                    ),
                  ),
                ],
              ),
              textAlign: TextAlign.left,
            ),
          ),
        ],
      ),
    );
  }

  static Widget objectMenu() {
    return Stack(
      children: [
        Container(
          height: 286.5.h,
          width: double.infinity,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              SizedBox(height: 34.h),
              getMoreLikePoint(),
              SizedBox(height: 12.h),
              inviteToDownload(),
              SizedBox(height: 12.h),
              Container(
                child: Stack(
                  children: [
                    campaignsCenter(),
                    mrIcon(),
                  ],
                ),
              ),
            ],
          ),
        ),
        avatarIcons()
      ],
    );
  }

  static Widget getMoreLikePoint() {
    return Container(
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: const Color(0xff2cffff),
        ),
        width: 252.0.w,
        height: 50.0.h,
        child: Text.rich(
          TextSpan(
            style: TextStyle(
              fontFamily: 'IBM Plex Sans Thai',
              fontSize: 18.h,
              color: const Color(0xff3c3965),
              letterSpacing: 0.36,
              shadows: [
                Shadow(
                  color: const Color(0x26000000),
                  offset: Offset(0, 1),
                  blurRadius: 3,
                )
              ],
            ),
            children: [
              TextSpan(
                text: 'Get More ',
                style: TextStyle(
                  fontWeight: FontWeight.w700,
                ),
              ),
              TextSpan(
                text: 'Likepoint',
              ),
            ],
          ),
          textAlign: TextAlign.left,
        ));
  }

  static Widget inviteToDownload() {
    return ClipRect(
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 50.0, sigmaY: 50.0),
        child: Container(
          width: 277.0.w,
          height: 75.0.h,
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20.0),
              bottomLeft: Radius.circular(20.0),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              phoneActive(),
              SizedBox(
                width: 11.4.w,
              ),
              Text.rich(
                TextSpan(
                  style: TextStyle(
                    fontFamily: 'IBM Plex Sans Thai',
                    fontSize: 17.h,
                    color: const Color(0xffffffff),
                    letterSpacing: 0.34.w,
                    shadows: [
                      Shadow(
                        color: const Color(0x1a000000),
                        offset: Offset(0, 1),
                        blurRadius: 2,
                      )
                    ],
                  ),
                  children: [
                    TextSpan(
                      text: 'Invite to',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    TextSpan(
                      text: ' ',
                      style: TextStyle(
                        fontWeight: FontWeight.w300,
                      ),
                    ),
                    TextSpan(
                      text: 'Download',
                      style: TextStyle(
                        color: const Color(0xff2cffff),
                        fontWeight: FontWeight.w300,
                      ),
                    ),
                  ],
                ),
                textAlign: TextAlign.left,
              ),
              SizedBox(
                width: 25.w,
              ),
            ],
          ),
        ),
      ),
    );
  }

  static Widget campaignsCenter() {
    return ClipRect(
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 50.0, sigmaY: 50.0),
        child: Container(
          width: 277.0.w,
          height: 75.0.h,
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20.0),
              bottomLeft: Radius.circular(20.0),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text.rich(
                TextSpan(
                  style: TextStyle(
                    fontFamily: 'IBM Plex Sans Thai',
                    fontSize: 17,
                    color: const Color(0xffffffff),
                    letterSpacing: 0.34,
                    height: 1.2941176470588236,
                    shadows: [
                      Shadow(
                        color: const Color(0x1a000000),
                        offset: Offset(0, 1),
                        blurRadius: 2,
                      )
                    ],
                  ),
                  children: [
                    TextSpan(
                      text: 'Campaigns',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    TextSpan(
                      text: ' \n',
                      style: TextStyle(
                        fontWeight: FontWeight.w300,
                      ),
                    ),
                    TextSpan(
                      text: 'Center',
                      style: TextStyle(
                        color: const Color(0xff2cffff),
                        fontWeight: FontWeight.w300,
                      ),
                    ),
                  ],
                ),
                textAlign: TextAlign.left,
              ),
            ],
          ),
        ),
      ),
    );
  }

  static Widget avatarIcons() {
    return Positioned(
      left: -10.w,
      child: SvgPicture.asset(
        'assets/ldx/avatar_icon.svg',
        fit: BoxFit.fitHeight,
        // height: 263.5.h,
        height: 263.5.h,
      ),
    );
  }

  static Widget phoneActive() {
    return SvgPicture.asset(
      'assets/ldx/phone_active.svg',
      fit: BoxFit.fitHeight,
      // height: 263.5.h,
      height: 66.33.w,
    );
  }

  static Widget mrIcon() {
    return Positioned(
      child: SvgPicture.asset(
        'assets/ldx/friendship.svg',
        fit: BoxFit.fitHeight,
        // height: 263.5.h,
        height: 103.5.w,
      ),
    );
  }
}
