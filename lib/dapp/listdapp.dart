import 'package:flutter/material.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/screen/home.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'dart:async';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ListDapp extends StatefulWidget {
  ListDapp({
    required this.url,
  });
  final String url;
  _ListDapp createState() => new _ListDapp(url: url);
}

class _ListDapp extends State<ListDapp> {
  _ListDapp({
    required this.url,
  });
  final String url;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFFFFFFF),
      body: new Stack(
        alignment: Alignment.center,
        children: <Widget>[
          Positioned(
            child: new Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: <Widget>[
                new Padding(
                  padding: EdgeInsets.only(
                      top: MediaQuery.of(context).size.height / 7,
                      left: MediaQuery.of(context).size.width / 14),
                  child: new Row(
                    children: <Widget>[
                      new Text(
                        'Tester Zone',
                        style:
                            TextStyle(color: Color(0xff000000), fontSize: 36.0),
                      ),
                    ],
                  ),
                ),
                new Padding(
                  padding: EdgeInsets.only(
                      top: MediaQuery.of(context).size.height / 40,
                      left: MediaQuery.of(context).size.width / 14),
                  child: new Row(
                    children: <Widget>[
                      new Text(
                        'Welcome to testing new feature',
                        style:
                            TextStyle(color: Color(0xff000000), fontSize: 15.0),
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(
                      top: MediaQuery.of(context).size.width / 2.45),
                  child: new ButtonTheme(
                    minWidth: MediaQuery.of(context).size.width / 1.2,
                    height: 60,
                    child: new TextButton(
                      style: ButtonStyle(
                        shape: MaterialStateProperty.all(new RoundedRectangleBorder(borderRadius: new BorderRadius.circular(8.0),),),
                        foregroundColor: MaterialStateProperty.all<Color>(Color(0xff00F1E0)),
                        backgroundColor: MaterialStateProperty.all<Color>(Color(0xff00F1E0)),
                      ),
                        // shape: new RoundedRectangleBorder(borderRadius: new BorderRadius.circular(8.0),),
                        // disabledColor: Color(0xff00F1E0),
                        // color: Color(0xff00F1E0),
                        onPressed: () => {
//                          print(url);
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) => WebOpenURL(url: url)),
                              ),
                            },
                        child: new Text(
                          'Go to Lotto',
                          style: TextStyle(
                              color: Colors.black,
                              fontSize: 20,
                              fontWeight: FontWeight.bold),
                        )),
                  ),
                ),
                SizedBox(
                  height: 30.h,
                ),
                Container(
                  // padding: EdgeInsets.only(
                  //     top: MediaQuery.of(context).size.width / 2.45),
                  child: new ButtonTheme(
                    minWidth: MediaQuery.of(context).size.width / 1.2,
                    height: 60,
                    child:TextButton(
                      style: ButtonStyle(
                        shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                          RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                        ),
                        backgroundColor: MaterialStateProperty.resolveWith<Color>(
                              (Set<MaterialState> states) {
                            if (states.contains(MaterialState.disabled)) {
                              return Colors.black12; // Disabled color
                            }
                            return Colors.black12; // Regular color
                          },
                        ),
                      ),
                      onPressed: () => Navigator.of(context).pop(),
                      child: Text(
                        'Cancel',
                        style: TextStyle(
                          color: Colors.black,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class WebOpenURL extends StatefulWidget {
  WebOpenURL({required this.url});
  final String url;
  @override
  _WebOpenURL createState() => _WebOpenURL(url: url);
}

class _WebOpenURL extends State<WebOpenURL> {
  _WebOpenURL({required this.url});
  final String url;

  final Completer<WebViewController> _controller =
      Completer<WebViewController>();
  final webViewKey = GlobalKey<WebViewContainerState>();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Color(0xff141322),
        leading: new Container(),
        // This drop down menu demonstrates that Flutter widgets can be shown over the web view.
        actions: <Widget>[
          IconButton(
            icon: Icon(Icons.clear),
            onPressed: () {
              Navigator.pop(context);
            },
          )
        ],
      ),

      // We're using a Builder here so we have a context that is below the Scaffold
      // to allow calling Scaffold.of(context) so we can show a snackbar.
      body: Builder(builder: (BuildContext context) {
        return WebView(
          key: webViewKey,
          initialUrl: url,
          javascriptMode: JavascriptMode.unrestricted,
          onWebViewCreated: (WebViewController webViewController) {
            _controller.complete(webViewController);
          },
          // TODO(iskakaushik): Remove this when collection literals makes it to stable.
          // ignore: prefer_collection_literals
          // javascriptChannels: <JavascriptChannel>[
          //   _toasterJavascriptChannel(context),
          // ].toSet(),
          navigationDelegate: (NavigationRequest request) {
            if (request.url.startsWith('https://www.youtube.com/')) {
              print('blocking navigation to $request}');
              return NavigationDecision.prevent;
            }
            print('allowing navigation to $request');
            return NavigationDecision.navigate;
          },
          onPageStarted: (String url) {
            print('Page started loading: $url');
          },
          onPageFinished: (String url) {
            print('Page finished loading: $url');
          },
          gestureNavigationEnabled: true,
        );
      }),
      // floatingActionButton: favoriteButton(),
    );
  }

  // JavascriptChannel _toasterJavascriptChannel(BuildContext context) {
  //   return JavascriptChannel(
  //       name: 'Toaster',
  //       onMessageReceived: (JavascriptMessage message) {
  //         Scaffold.of(context).showSnackBar(
  //           SnackBar(content: Text(message.message)),
  //         );
  //       });
  // }

  Widget favoriteButton() {
    return FutureBuilder<WebViewController>(
        future: _controller.future,
        builder: (BuildContext context,
            AsyncSnapshot<WebViewController> controller) {
          if (controller.hasData) {
            return FloatingActionButton(
              onPressed: () async {
                Navigator.pop(context);
              },
              child: const Icon(Icons.arrow_back_ios),
            );
          }
          return Container();
        });
  }
}

class WebViewContainer extends StatefulWidget {
  WebViewContainer({required Key key}) : super(key: key);

  @override
  WebViewContainerState createState() => WebViewContainerState();
}

class WebViewContainerState extends State<WebViewContainer> {
  late WebViewController _webViewController;

  @override
  Widget build(BuildContext context) {
    return WebView(
      onWebViewCreated: (controller) {
        _webViewController = controller;
      },
      initialUrl: 'https://likewallet.io/lottery',
    );
  }

  void reloadWebView() {
    _webViewController.reload();
  }
}
