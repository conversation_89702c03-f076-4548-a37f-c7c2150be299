// import 'dart:convert';
// import 'dart:io';
//
// import 'package:device_apps/device_apps.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:fluttertoast/fluttertoast.dart';
// import 'package:image_picker/image_picker.dart';
//
// import 'package:likewallet/ImageTheme.dart';
// import 'package:likewallet/Theme.dart';
// import 'package:likewallet/animationPage.dart';
// import 'package:likewallet/change_phone/change_phone_take_photo.dart';
// import 'package:likewallet/change_phone/pending_change_phone.dart';
// import 'package:likewallet/change_phone/test.dart';
// import 'package:likewallet/device_utils.dart';
// import 'package:likewallet/libraryman/app_local.dart';
// import 'package:likewallet/routes.dart';
// import 'package:http/http.dart' as http;
// import 'package:likewallet/app_config.dart';
// import 'package:likewallet/animationPage.dart';
// import 'package:likewallet/libraryman/app_local.dart';
//
// import 'package:likewallet/screen_util.dart';
//
// import 'package:likewallet/screen/takephoto.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:camera/camera.dart';
// import 'package:likewallet/libraryman/auth.dart';
// import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
// import 'package:likewallet/libraryman/custom_loading.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
//
// class ChangePhone extends StatefulWidget {
//   ChangePhone({required this.oldPhone});
//   final String oldPhone;
//   _ChangePhone createState() => new _ChangePhone(oldPhone: oldPhone);
// }
//
// class _ChangePhone extends State<ChangePhone> {
//   _ChangePhone({required this.oldPhone});
//
//   final String oldPhone;
//   String dropdownValue = '+66';
//   TextEditingController newPhone = new TextEditingController();
//   TextEditingController changePhoneText = new TextEditingController();
//   late String Name;
//   late String LastName;
//   late String IDCard;
//   late String Front;
//   late String Back;
//   late String Selfie;
//   bool selfie_check = false;
//   String uploadChangePhone = 'none';
//   String kycStatus = 'nokyc';
//   bool _saving = false;
//   bool checkAutoFocus = false;
//   bool slider = false;
//   late BaseAuth FirebaseAuth;
//   late CameraDescription firstCamera;
//   String urlPhoto = "";
//
//   @override
//   void initState() {
//     super.initState();
//     FirebaseAuth = Auth();
//     checkPending();
//     print(oldPhone);
//   }
//
//   void setCamera() async {
//     // Obtain a list of the available cameras on the device.
//     final cameras = await availableCameras();
//     // Get a specific camera from the list of available cameras.
//     firstCamera = cameras.first;
//   }
//
//   late SharedPreferences sharedPreferences;
//
//   checkPending() async {
//     try {
//       await FirebaseFirestore.instance
//           .collection('changePhone')
//           .where("oldPhone", isEqualTo: oldPhone)
//           .where("status", isEqualTo: 'pending')
//           .limit(1)
//           .get()
//           .then((value) {
//         value.docs.forEach((data) {
//           print(data.data()['status']);
//           if (data.data()['status'] == 'pending') {
//             Navigator.push(
//               context,
//               MaterialPageRoute(builder: (context) => PendingChangePhone()),
//             );
//           }
//         });
//       });
//     } catch (e) {
//       print(e);
//     }
//
//     // setState(() {
//     //   _saving = true;
//     // });
//
//     // sharedPreferences = await SharedPreferences.getInstance();
//     // setState(() {
//     //   selfie_check = sharedPreferences.getBool("selfie_check");
//     // });
//     // if (selfie_check == true) {
//     //   setState(() {
//     //     uploadChangePhone = sharedPreferences.getString('uploadChangePhone');
//     //     changePhoneText.text =
//     //         AppLocalizations.of(context)!.translate('change_phone_input2_1');
//     //   });
//     //   print(uploadChangePhone);
//     // }
//   }
//
//   setPhoto(value) {
//     if (value != null) {
//       setState(() {
//         changePhoneText.text = 'ถ่ายเเล้ว';
//       });
//     }
//   }
//
//   Widget build(BuildContext context) {
//     return new WillPopScope(
//       onWillPop: () {
//         return Future.value();
//       },
//       child: ModalProgressHUD(
//         opacity: 0.1,
//         inAsyncCall: _saving,
//         progressIndicator: CustomLoading(),
//         child: Scaffold(
//           backgroundColor: LikeWalletAppTheme.bule2,
//           body: GestureDetector(
//             onTap: () => DeviceUtils.hideKeyboard(context),
//             child: SingleChildScrollView(
//               child: form(),
//             ),
//           ),
//         ),
//       ),
//     );
//   }
//
//   Widget form() {
//     return Container(
//       height: MediaQuery.of(context).size.height,
//       child: new Column(
//         children: <Widget>[
//           //ปุ่ม back
//           Container(
//               padding: EdgeInsets.only(
//                 top: mediaQuery(context, 'height', 100.33),
//                 left: 0,
//               ),
//               child: _backButton()),
//           //หัวเรื่อง KYC
//           SizedBox(
//             height: 50.h,
//           ),
//           Container(child: _titel()),
//           SizedBox(height: mediaQuery(context, 'height', 105)),
//           // Container(
//           //     padding: EdgeInsets.symmetric(
//           //         vertical: mediaQuery(context, 'height', 0)),
//           //     child: textFieldKYC(
//           //         context, checkAutoFocus, newPhone, 'change_phone_input1')),
//           _inputPhone(),
//           SizedBox(height: mediaQuery(context, 'height', 55)),
//           Container(
//               child: textFieldKYC2(context, changePhoneText,
//                   'change_phone_input2', selfie_check)),
//           SizedBox(height: mediaQuery(context, 'height', 100)),
//           Container(
//               // height: 500,
//               width: MediaQuery.of(context).size.width,
//               child: Stack(
//                 alignment: Alignment.center,
//                 children: [
//                   AnimatedContainer(
//                       duration: Duration(milliseconds: 200),
//                       alignment:
//                           slider ? Alignment.centerLeft : Alignment.center,
//                       child: slider ? Container() : _sendButton()),
//                   AnimatedContainer(
//                     duration: Duration(milliseconds: 200),
//                     alignment:
//                         slider ? Alignment.center : Alignment.centerRight,
//                     // width: slider ? mediaQuery(context, 'width', 200) : 0,
//                     child: slider
//                         ? Text(
//                             AppLocalizations.of(context)!
//                                 .translate('change_phone_complete'),
//                             style: TextStyle(color: LikeWalletAppTheme.bule1),
//                           )
//                         : Container(),
//                   )
//                 ],
//               )),
//           Expanded(child: Container()),
//           Container(
//               width: MediaQuery.of(context).size.width,
//               height: mediaQuery(context, "height", 544.51),
//               child: Image.asset(LikeWalletImage.logo_change_photo)),
//         ],
//       ),
//     );
//   }
//
//   Future<bool> sendChangePhone(newPhone) async {
//     await FirebaseAuth.getCurrentUser().then((decodedToken) async {
//       print(decodedToken!.uid);
//       print(oldPhone);
//       print(newPhone);
//       print('No');
//       print(uploadChangePhone);
//       final date = (DateTime.now().millisecondsSinceEpoch);
//       await FirebaseFirestore.instance
//           .collection('changePhone')
//           .doc(date.toString())
//           .set({
//         'oldPhone': oldPhone,
//         'newPhone': newPhone,
//         'uid': decodedToken.uid,
//         'url': urlPhoto,
//         'status': 'pending',
//         'date': date,
//       }).then((data) {
//         setState(() {
//           _saving = false;
//         });
//       });
//     });
//     return Future.value();
//   }
//
//   Widget _backButton() {
//     return backButton(context, LikeWalletAppTheme.gray);
//   }
//
//   Widget _titel() {
//     return Container(
//         alignment: Alignment.center,
//         width: mediaQuery(context, 'width', 927),
//         child: Text(
//           AppLocalizations.of(context)!.translate('change_phone_title'),
//           textAlign: TextAlign.center,
//           style: TextStyle(
//               color: LikeWalletAppTheme.gray7.withOpacity(0.8),
//               fontSize: mediaQuery(context, 'height', 42),
//               fontWeight: FontWeight.w100,
//               letterSpacing: 0.55,
//               fontFamily: AppLocalizations.of(context)!.translate('font1')),
//         ));
//   }
//
//   Widget _sendButton() {
//     return Container(
//       width: slider ? 0 : 439.14.w,
//       height: 132.h,
//       child: new TextButton(
//           onPressed: () {
//             DeviceUtils.hideKeyboard(context);
//             if (newPhone.text.length == 10 && changePhoneText.text != "") {
//               setState(() {
//                 _saving = true;
//                 slider = true;
//               });
//               checkExistUser().then((newPhone) {
//                 print(newPhone);
//                 sendChangePhone(newPhone).then((value) {
//                   checkPending();
//                 });
//               });
//             } else {
//               showColoredToast('กรุณากรอกข้อมูลให้ครบถ้วน');
//             }
//           },
//           style: ButtonStyle(
//             backgroundColor: MaterialStateProperty.all<Color>(
//               Color(0xff00F1E0),
//             ),
//             shape: MaterialStateProperty.all<RoundedRectangleBorder>(
//                 RoundedRectangleBorder(
//               borderRadius: BorderRadius.circular(10.0),
//             )),
//           ),
//           child: new Text(
//             AppLocalizations.of(context)!.translate('change_phone_button'),
//             style: TextStyle(
//                 color: LikeWalletAppTheme.black,
//                 fontSize: MediaQuery.of(context).size.height *
//                     Screen_util("height", 51),
//                 fontFamily: AppLocalizations.of(context)!.translate('font1'),
//                 fontWeight: FontWeight.normal),
//           )),
//     );
//   }
//
//   textFieldKYC(context, checkAutoFocus, controller, label) {
//     return new Container(
//       decoration: BoxDecoration(
//         color: LikeWalletAppTheme.bule2,
//         border: Border.all(
//           color: LikeWalletAppTheme.bule1,
//           width: mediaQuery(context, 'height', 0.3),
//         ),
//         borderRadius: BorderRadius.all(Radius.circular(5.0)),
//       ),
//       alignment: Alignment.center,
//       height: mediaQuery(context, 'height', 156),
//       width: mediaQuery(context, 'width', 930),
//       child: TextField(
//         autofocus: checkAutoFocus == true ? true : false,
//         controller: controller,
//         style: TextStyle(
//           fontSize: mediaQuery(context, 'height', 45),
//           color: LikeWalletAppTheme.white.withOpacity(0.9),
//           fontFamily: AppLocalizations.of(context)!.translate('font1'),
//           fontWeight: FontWeight.w100,
//         ),
//         decoration: InputDecoration(
//           isDense: true,
//           contentPadding: EdgeInsets.only(left: 10),
//           hintText: AppLocalizations.of(context)!.translate(label),
//           hintStyle: TextStyle(
//             fontSize: mediaQuery(context, 'height', 45),
//             color: LikeWalletAppTheme.white.withOpacity(0.3),
//             fontFamily: AppLocalizations.of(context)!.translate('font1'),
//             fontWeight: FontWeight.w100,
//           ),
//           border: InputBorder.none,
//         ),
//         keyboardType: TextInputType.number,
//       ),
//     );
//   }
//
//   Widget _inputPhone() {
//     return Container(
//       alignment: Alignment.center,
//       height: mediaQuery(context, 'height', 156),
//       width: mediaQuery(context, 'width', 930),
//       decoration: BoxDecoration(
//         color: Color(0xff141322),
//         border: Border.all(
//             color: Color(0xff0FE8D8), width: mediaQuery(context, 'width', 0.4)),
//         borderRadius: BorderRadius.all(Radius.circular(5.0)),
//       ),
//       child: Row(
//         children: <Widget>[
//           new Container(
//             alignment: Alignment.center,
//             height: MediaQuery.of(context).size.height * 0.07,
//             width: MediaQuery.of(context).size.width * 0.2,
//             child: DropdownButtonHideUnderline(
//               child: DropdownButton<String>(
//                 value: dropdownValue,
//                 elevation: 15,
//                 dropdownColor: LikeWalletAppTheme.bule2_4,
//                 focusColor: LikeWalletAppTheme.white.withOpacity(0.9),
//                 style:
//                     TextStyle(color: LikeWalletAppTheme.white.withOpacity(0.3)),
//                 iconEnabledColor: LikeWalletAppTheme.white.withOpacity(0.3),
//                 iconDisabledColor: LikeWalletAppTheme.white.withOpacity(0.3),
//                 onChanged: (String? newValue) {
//                   setState(() {
//                     dropdownValue = newValue.toString();
//                   });
//                 },
//                 items: <String>['+66', '+856', '+855']
//                     .map<DropdownMenuItem<String>>((String value) {
//                   return DropdownMenuItem<String>(
//                     value: value,
//                     child: Text(value),
//                   );
//                 }).toList(),
//               ),
//             ),
//             decoration: BoxDecoration(
//                 border: Border(
//               right: BorderSide(
//                   //                   <--- left side
//                   color: Color(0xff0FE8D8),
//                   width: mediaQuery(context, 'width', 0.55)),
//             )),
//           ),
//           new Container(
//             alignment: Alignment.center,
//             height: MediaQuery.of(context).size.height * 0.07,
//             width: MediaQuery.of(context).size.width * 0.65,
// //                color: Colors.blue,
//             child: TextFormField(
//               inputFormatters: [
//                 LengthLimitingTextInputFormatter(10),
//               ],
//               controller: newPhone,
//               style: TextStyle(
//                 fontSize: MediaQuery.of(context).size.height *
//                     Screen_util("height", 45),
//                 fontFamily: AppLocalizations.of(context)!.translate('font1'),
//                 color: LikeWalletAppTheme.white.withOpacity(0.9),
//                 fontWeight: FontWeight.w100,
//               ),
//               decoration: InputDecoration(
//                 isDense: true,
//                 contentPadding: EdgeInsets.only(left: 20),
//                 hintText:
//                     AppLocalizations.of(context)!.translate('forget_mobile'),
//                 hintStyle: TextStyle(
//                   fontSize: MediaQuery.of(context).size.height *
//                       Screen_util("height", 45),
//                   fontFamily: AppLocalizations.of(context)!.translate('font1'),
//                   color: LikeWalletAppTheme.white.withOpacity(0.3),
//                   fontWeight: FontWeight.w100,
//                 ),
//                 border: InputBorder.none,
//               ),
//               keyboardType: TextInputType.number,
//             ),
//           ),
//         ],
//       ),
//     );
//   }
//
//   textFieldKYC2(context, controller, text, check) {
//     return new Container(
//       alignment: Alignment.centerLeft,
//       width: mediaQuery(context, 'width', 930),
//       child: Container(
//         alignment: Alignment.center,
//         height: mediaQuery(context, 'height', 156),
//         width: mediaQuery(context, 'width', 930),
//         decoration: BoxDecoration(
//           color: Color(0xff141322),
//           border: Border.all(
//             color: Color(0xff0FE8D8),
//             width: mediaQuery(context, 'height', 0.3),
//           ),
//           borderRadius: BorderRadius.all(Radius.circular(5.0)),
//         ),
//         child: Row(
//           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//           children: <Widget>[
//             new Container(
//               alignment: Alignment.center,
//               height: MediaQuery.of(context).size.height * 0.05,
//               width: MediaQuery.of(context).size.width * 0.65,
//               child: TextField(
//                 enabled: false,
//                 controller: controller,
//                 style: TextStyle(
//                   fontSize: mediaQuery(context, 'height', 45),
//                   color: LikeWalletAppTheme.white.withOpacity(0.9),
//                   fontFamily: AppLocalizations.of(context)!.translate('font1'),
//                   fontWeight: FontWeight.w100,
//                 ),
//                 decoration: InputDecoration(
//                   isDense: true,
//                   contentPadding: EdgeInsets.only(left: 10),
//                   hintText: AppLocalizations.of(context)!.translate(text),
//                   hintStyle: TextStyle(
//                     fontSize: mediaQuery(context, 'height', 45),
//                     color: LikeWalletAppTheme.white.withOpacity(0.3),
//                     fontFamily:
//                         AppLocalizations.of(context)!.translate('font1'),
//                     fontWeight: FontWeight.w100,
//                   ),
//                   border: InputBorder.none,
//                 ),
//                 keyboardType: TextInputType.text,
//               ),
//             ),
//             GestureDetector(
//               onTap: () async {
//                 if (!check) {
//                   _navigateAndDisplaySelection(context);
//                 }
//               },
//               child: Container(
//                 alignment: Alignment.center,
//                 // height: MediaQuery.of(context).size.height * 0.05,
//                 // width: MediaQuery.of(context).size.width * 0.2,
//                 padding: EdgeInsets.only(
//                   right: mediaQuery(context, 'width', 39),
//                 ),
//                 child: check
//                     ? Container(
//                         padding: EdgeInsets.only(
//                           right: mediaQuery(context, 'width', 39),
//                         ),
//                         alignment: Alignment.centerRight,
//                         height: mediaQuery(context, 'height', 57.57),
//                         child: Image.asset(
//                             LikeWalletImage.icon_check_photo_success,
//                             color: LikeWalletAppTheme.bule1),
//                       )
//                     : Container(
//                         alignment: Alignment.centerRight,
//                         height: mediaQuery(context, 'height', 85),
//                         width: mediaQuery(context, 'height', 85),
//                         decoration: BoxDecoration(
//                           shape: BoxShape.circle,
//                           image: DecorationImage(
//                               fit: BoxFit.cover,
//                               image:
//                                   AssetImage(LikeWalletImage.icon_take_photo)),
//                           border: Border.all(
//                               // color: Colors.white,
//                               ),
//                         )),
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
//
//   _navigateAndDisplaySelection(BuildContext context) async {
//     // Navigator.push returns a Future that completes after calling
//     // Navigator.pop on the Selection Screen.
//     // Obtain a list of the available cameras on the device.
//     // final cameras = await availableCameras();
//     //
//     // // Get a specific camera from the list of available cameras.
//     // final firstCamera = cameras.first;
//     // final result = await Navigator.push(
//     //   context,
//     //   MaterialPageRoute(
//     //       builder: (context) => TakePictureScreen(
//     //             camera: firstCamera,
//     //           )),
//     // );
//     setState(() => _saving = true);
//     final ImagePicker _picker = ImagePicker();
//     final XFile? result = await _picker.pickImage(
//       source: ImageSource.camera,
//       imageQuality: 80,
//       maxHeight: 600,
//       maxWidth: 600,
//     );
//     if (result == null) {
//       setState(() {
//         _saving = false;
//       });
//     } else {
//       upload(File(result.path)).then((data) async {
//         setState(() {
//           print(data);
//           _saving = false;
//           changePhoneText.text =
//               AppLocalizations.of(context)!.translate('change_phone_input2_1');
//           urlPhoto = data[0].toString();
//           selfie_check = true;
//         });
//       });
//     }
//
//     // After the Selection Screen returns a result, hide any previous snackbars
//     // and show the new result.
//   }
//
//   Future<List<String>> upload(File file) async {
//     if (file == null) return ['false'];
//     String base64Image = base64Encode(file.readAsBytesSync());
//     String fileName = file.path.split("/").last;
//     try {
//       var url = Uri.https(env.apiCheck, '/uploadBackFromBase');
//       final response = await http.post(url, body: {
//         "image": base64Image,
//         "name": fileName,
//       });
//       print(response.statusCode);
//       final body = json.decode(response.body);
//       print(body["result"]);
//       print(body["result"]["id"]);
//       print(body["result"]["url"]);
//       return [body["result"]["url"]];
//     } catch (e) {
//       print(e);
//       return ['false'];
//     }
//     // return [];
//   }
//
//   Future<String?> checkExistUser() async {
//     if (newPhone.text.substring(0, 1) == '0') {
//       newPhone.text = dropdownValue +
//           newPhone.text.substring(1, newPhone.text.length).toString();
//       return newPhone.text;
//     }
//   }
// }
