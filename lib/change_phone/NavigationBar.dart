import 'dart:io';
import 'package:likewallet/Theme.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:likewallet/bank/banking.dart';
import 'package:likewallet/screen/home.dart';
import 'package:likewallet/screen_util.dart';
import 'package:likewallet/ImageTheme.dart';
import 'package:likewallet/screen/navigationbar/contact_us.dart';
import 'package:likewallet/routes.dart';

class NavigationBar extends StatefulWidget {
  _NavigationBar createState() => new _NavigationBar();
}

class _NavigationBar extends State<NavigationBar> {
  int _selectPage = 0;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  int curIndex = 0;
  Widget build(BuildContext context) {
    return SizedBox(
      height: mediaQuery(context, 'height', 260),
      child: BottomNavigationBar(
        type: BottomNavigationBarType
            .fixed, // this will be set when a new tab is tapped
        fixedColor: Colors.transparent,
        backgroundColor: Colors.transparent,
        currentIndex: curIndex,
        elevation: 0,
        onTap: (index) {
          setState(() {
            print(index);
            if (index == 0 ||
                index == 1 ||
                index == 2 ||
                index == 3 ||
                index == 4) {
              if (index == 0) {
//                      if(!(curIndex == index)){
//                        setState(() {
//                          curIndex = 0;
//                        });

                AppRoutes.makeFirst(context, HomeLikewallet(selectPage: 0));
                Navigator.of(context).pushNamedAndRemoveUntil(
                    '/home', (Route<dynamic> route) => true);
//                      }
              }
              if (index == 1) {
//                      if(!(curIndex == index)) {
//                        setState(() {
//                          curIndex = 1;
//                        });
//              AppRoutes.makeFirst(context, HomeLikewallet(selectPage: 1));
                Navigator.pushAndRemoveUntil(
                    context,
                    MaterialPageRoute(
                      builder: (context) => HomeLikewallet(
                        selectPage: 1,
                      ),
                    ),
                    (Route<dynamic> route) => false);
              }

              if (index == 2) {
//                      if(!(curIndex == index)) {
//                          setState(() {
//                            curIndex = 2;
//                          });
                Navigator.pushAndRemoveUntil(
                    context,
                    MaterialPageRoute(
                      builder: (context) => HomeLikewallet(
                        selectPage: 2,
                      ),
                    ),
                    (Route<dynamic> route) => false);
//                      }
              }
              if (index == 3) {
//                      if(!(curIndex == index)) {
//                        setState(() {
//                          curIndex = 3;
//                        });
                Navigator.pushAndRemoveUntil(
                    context,
                    MaterialPageRoute(
                      builder: (context) => HomeLikewallet(
                        selectPage: 3,
                      ),
                    ),
                    (Route<dynamic> route) => false);
//                      }
              }
              if (index == 4) {
//                    if(!(curIndex == index)) {
//                      setState(() {
//                        curIndex = 4;
//                      });

                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => ContactUS()),
                );
//              Navigator.pushAndRemoveUntil(
//                  context,
//                  MaterialPageRoute(
//                    builder: (context) => HomeLikewallet(
//                      selectPage: 4,
//                    ),
//                  ),
//                  (Route<dynamic> route) => false);
//                    }
              }
            }
          });
        },
        items: [
          BottomNavigationBarItem(
            icon: Container(
              height: mediaQuery(context, "height", 170),
              width: mediaQuery(context, "width", 170),
              alignment: Alignment.bottomCenter,
              padding:
                  EdgeInsets.only(bottom: mediaQuery(context, 'height', 40)),
              child: Image.asset(
                LikeWalletImage.iconHome,
                height: mediaQuery(context, "height", 60),
                color: LikeWalletAppTheme.white.withOpacity(0.5),
              ),
            ),
          ),
          BottomNavigationBarItem(
            icon: Container(
              height: mediaQuery(context, "height", 170),
              width: mediaQuery(context, "width", 170),
              alignment: Alignment.bottomCenter,
              padding:
                  EdgeInsets.only(bottom: mediaQuery(context, 'height', 40)),
              child: Image.asset(
                LikeWalletImage.iconBuzzer,
                height: mediaQuery(context, "height", 60),
                color: LikeWalletAppTheme.white.withOpacity(0.5),
              ),
            ),
          ),
          BottomNavigationBarItem(
            icon: Container(
              height: mediaQuery(context, "height", 170),
              width: mediaQuery(context, "width", 170),
              padding:
                  EdgeInsets.only(bottom: mediaQuery(context, 'height', 40)),
              alignment: Alignment.bottomCenter,
              child: Image.asset(
                LikeWalletImage.iconRefer,
                height: mediaQuery(context, "height", 100),
                color: LikeWalletAppTheme.white.withOpacity(0.5),
              ),
            ),
          ),
          BottomNavigationBarItem(
            icon: Container(
              height: mediaQuery(context, "height", 170),
              width: mediaQuery(context, "width", 170),
              padding:
                  EdgeInsets.only(bottom: mediaQuery(context, 'height', 40)),
              alignment: Alignment.bottomCenter,
              child: Image.asset(
                LikeWalletImage.iconHistory,
                height: mediaQuery(context, "height", 60),
                color: LikeWalletAppTheme.white.withOpacity(0.5),
              ),
            ),
          ),
          BottomNavigationBarItem(
            icon: GestureDetector(
              onTap: () {
                print('firstclick');
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => ContactUS()),
                );
              },
              child: Container(
                height: mediaQuery(context, "height", 180),
                width: mediaQuery(context, "width", 170),
                padding:
                    EdgeInsets.only(bottom: mediaQuery(context, 'height', 35)),
                alignment: Alignment.bottomCenter,
                child: Image.asset(
                  LikeWalletImage.iconFeedback,
                  height: mediaQuery(context, 'height', 90),
                  color: LikeWalletAppTheme.white.withOpacity(0.5),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}
