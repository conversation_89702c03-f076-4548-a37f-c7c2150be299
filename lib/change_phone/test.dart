// import 'dart:async';
// import 'dart:io';
//
// import 'package:adobe_xd/adobe_xd.dart';
// import 'package:camera/camera.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_svg/svg.dart';
//
// Future<void> main() async {
//   // Ensure that plugin services are initialized so that `availableCameras()`
//   // can be called before `runApp()`
//   WidgetsFlutterBinding.ensureInitialized();
//
//   // Obtain a list of the available cameras on the device.
//   final cameras = await availableCameras();
//
//   // Get a specific camera from the list of available cameras.
//   final firstCamera = cameras.first;
//
//   runApp(
//     MaterialApp(
//       theme: ThemeData.dark(),
//       home: TakePictureScreen(
//           // Pass the appropriate camera to the TakePictureScreen widget.
//           ),
//     ),
//   );
// }
//
// // A screen that allows users to take a picture using a given camera.
// class TakePictureScreen extends StatefulWidget {
//   const TakePictureScreen({
//     Key? key,
//   }) : super(key: key);
//
//   @override
//   TakePictureScreenState createState() => TakePictureScreenState();
// }
//
// class TakePictureScreenState extends State<TakePictureScreen> {
//   @override
//   void initState() {
//     super.initState();
//   }
//
//   @override
//   void dispose() {
//     super.dispose();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(title: const Text('Take a picture')),
//       // You must wait until the controller is initialized before displaying the
//       // camera preview. Use a FutureBuilder to display a loading spinner until the
//       // controller has finished initializing.
//       body: Column(
//         children: [
//           // Container(
//           //   height: 50,
//           //   width: 50,
//           //   color: Colors.red,
//           // ),
//           SizedBox(
//             height: 19.0,
//             width: 50.0,
//             child: Stack(
//               children: <Widget>[
//                 Pinned.fromPins(
//                   Pin(size: 50.0, start: 0.0),
//                   Pin(size: 19.0, start: 2.0),
//                   child: Stack(
//                     children: <Widget>[
//                       Text(
//                         'Round',
//                         style: TextStyle(
//                           fontFamily: 'Proxima Nova',
//                           fontSize: 14,
//                           color: const Color(0xff424c5c),
//                           letterSpacing: 0.7000000000000001,
//                         ),
//                         textAlign: TextAlign.left,
//                       ),
//                     ],
//                   ),
//                 ),
//               ],
//             ),
//           ),
//           SizedBox(
//               height: 41.0,
//               width: 210.0,
//               child: Stack(
//                 children: <Widget>[
//                   SizedBox(
//                     width: 210.0,
//                     height: 41.0,
//                     child: SvgPicture.string(
//                       '<svg viewBox="-9.0 500.0 210.0 41.0" ><path transform="translate(-9.0, 500.0)" d="M 19.7730712890625 0 L 190.2269287109375 0 C 201.1472930908203 0 210 9.178160667419434 210 20.5 C 210 31.82183456420898 201.1472930908203 41 190.2269287109375 41 L 19.7730712890625 41 C 8.852705001831055 41 0 31.82183456420898 0 20.5 C 0 9.178160667419434 8.852705001831055 0 19.7730712890625 0 Z" fill="#171a26" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
//                       allowDrawingOutsideViewBox: true,
//                     ),
//                   ),
//                   Pinned.fromPins(
//                     Pin(size: 144.0, middle: 0.5),
//                     Pin(size: 16.0, middle: 0.44),
//                     child: Stack(
//                       children: [
//                         Transform.translate(
//                           offset: Offset(0.0, 0.0),
//                           child: SizedBox(
//                             width: 3150.0,
//                             child: Text(
//                               'View your lotto numbers',
//                               style: TextStyle(
//                                 fontFamily: 'Proxima Nova',
//                                 fontSize: 12,
//                                 color: const Color(0xffebedf1),
//                                 letterSpacing: 0.6000000000000001,
//                                 height: 1,
//                               ),
//                               textHeightBehavior: TextHeightBehavior(
//                                   applyHeightToFirstAscent: false),
//                               textAlign: TextAlign.center,
//                             ),
//                           ),
//                         ),
//                       ],
//                     ),
//                   ),
//                   Pinned.fromPins(
//                     Pin(size: 144.0, middle: 0.5),
//                     Pin(size: 16.0, middle: 0.44),
//                     child: Stack(
//                       children: [
//                         Transform.translate(
//                           offset: Offset(0.0, 0.0),
//                           child: SizedBox(
//                             width: 3150.0,
//                             child: Text(
//                               'View your lotto numbers',
//                               style: TextStyle(
//                                 fontFamily: 'Proxima Nova',
//                                 fontSize: 12,
//                                 color: const Color(0xffebedf1),
//                                 letterSpacing: 0.6000000000000001,
//                                 height: 1,
//                               ),
//                               textHeightBehavior: TextHeightBehavior(
//                                   applyHeightToFirstAscent: false),
//                               textAlign: TextAlign.center,
//                             ),
//                           ),
//                         ),
//                       ],
//                     ),
//                   ),
//                 ],
//               )),
//           SizedBox(
//             height: 210.0,
//             width: 441.0,
//             child: Stack(
//               alignment: Alignment.center,
//               children: <Widget>[
//                 SizedBox(
//                   width: 210.0,
//                   height: 40.0,
//                   child: SvgPicture.string(
//                     '<svg viewBox="24.0 441.0 210.0 40.0" ><path transform="translate(24.0, 441.0)" d="M 19.7730712890625 0 L 190.2269287109375 0 C 201.1472930908203 0 210 8.954303741455078 210 20 C 210 31.04569244384766 201.1472930908203 40 190.2269287109375 40 L 19.7730712890625 40 C 8.852705001831055 40 0 31.04569244384766 0 20 C 0 8.954303741455078 8.852705001831055 0 19.7730712890625 0 Z" fill="#171a26" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
//                     allowDrawingOutsideViewBox: true,
//                   ),
//                 ),
//                 Transform.translate(
//                   offset: Offset(0.0, 0.0),
//                   child: SizedBox(
//                     // width: 441.0,
//                     child: Text(
//                       'View your lotto numbers',
//                       style: TextStyle(
//                         fontFamily: 'Proxima Nova',
//                         fontSize: 12,
//                         color: const Color(0xffebedf1),
//                         letterSpacing: 0.6000000000000001,
//                         height: 1,
//                       ),
//                       textHeightBehavior:
//                           TextHeightBehavior(applyHeightToFirstAscent: false),
//                       textAlign: TextAlign.center,
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//           ),
//         ],
//       ),
//
//       floatingActionButton: FloatingActionButton(
//         // Provide an onPressed callback.
//         onPressed: () async {},
//         child: const Icon(Icons.camera_alt),
//       ),
//     );
//   }
// }
//
// // A widget that displays the picture taken by the user.
// class DisplayPictureScreen extends StatelessWidget {
//   final String imagePath;
//
//   const DisplayPictureScreen({Key? key, required this.imagePath})
//       : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(title: const Text('Display the Picture')),
//       // The image is stored as a file on the device. Use the `Image.file`
//       // constructor with the given path to display the image.
//       body: Image.file(File(imagePath)),
//     );
//   }
// }
