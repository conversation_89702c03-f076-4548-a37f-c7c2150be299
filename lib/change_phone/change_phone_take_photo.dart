import 'dart:io';

import 'package:flutter/material.dart';
import 'package:likewallet/change_phone/change_phone.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:likewallet/screen_util.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flare_flutter/flare_actor.dart';
import 'package:camera/camera.dart';
import 'package:path/path.dart' show join;
import 'package:path_provider/path_provider.dart';
import 'package:likewallet/kyc/crop_image.dart';
import 'package:likewallet/kyc/kyc.dart';

import 'package:path/path.dart' show basename;
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:likewallet/app_config.dart';

import 'package:dio/dio.dart';
import 'package:likewallet/Theme.dart';
import 'package:likewallet/ImageTheme.dart';

import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:likewallet/libraryman/custom_loading.dart';
import 'package:flutter/foundation.dart';
import 'package:fluttertoast/fluttertoast.dart';

enum stateKYCPhoto { FRONT, BACK, SELFIE }

class ChangePhoneTakePhoto extends StatefulWidget {
  const ChangePhoneTakePhoto({
    Key? key,
    required this.camera,
  }) : super(key: key);

  final CameraDescription camera;
  _ChangePhoneTakePhoto createState() => new _ChangePhoneTakePhoto();
}

class _ChangePhoneTakePhoto extends State<ChangePhoneTakePhoto> {
  late List cameras;
  late int selectedCameraIdx;
  late CameraDescription selectedCamera;
  late CameraLensDirection lensDirection;

  bool checkFace = false;

  /// ///

  Key containerCamera = UniqueKey();
  late CameraController _controller;
  late Future<void> _initializeControllerFuture;
  String curImage = 'none';
  int number = 0;
  int count = 0;

  bool _saving = false;
  changeContent(value) {
    if (value == 0) {
      setState(() {
        curImage = 'none';
      });
    }
    setState(() {
      number = value;
    });
  }

  bool front_check = false;
  bool end_check = false;
  bool selfie_check = false;
  late SharedPreferences sharedPreferences;

  Future<List<String>> upload(File file) async {
    if (file == null) return ['false'];
    String base64Image = base64Encode(file.readAsBytesSync());
    String fileName = file.path.split("/").last;
    var url = Uri.https(env.apiCheck, '/uploadBackFromBase');
    final response = await http.post(url, body: {
      "image": base64Image,
      "name": fileName,
    });
    print(response.statusCode);
    final body = json.decode(response.body);

    print(body["result"]);
    print(body["result"]["id"]);
    print(body["result"]["url"]);
    return [body["result"]["url"]];
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    // To display the current output from the Camera,
    // create a CameraController.
    _controller = CameraController(
      // Get a specific camera from the list of available cameras.
      widget.camera,
      // Define the resolution to use.
      ResolutionPreset.medium,
    );

    // Next, initialize the controller. This returns a Future.
    _initializeControllerFuture = _controller.initialize();
    availableCameras().then((availableCameras) {
      cameras = availableCameras;
      if (cameras.length > 0) {
        setState(() {
          // 2
          selectedCameraIdx = 0;
        });
        _initializeControllerFuture =
            _initCameraController(cameras[selectedCameraIdx]).then((void v) {});
      } else {
        print("No camera available");
      }
    }).catchError((err) {
      // 3
      print('Error: $err.code\nError Message: $err.message');
    });

//    _controller = CameraController(
//      // Get a specific camera from the list of available cameras.
//      firstCamera,
//      // Define the resolution to use.
//      ResolutionPreset.max,
//    );

    // Next, initialize the controller. This returns a Future.
//    _initializeControllerFuture = _controller.initialize();
  }

  void showColoredToast(msg, colors) {
    Fluttertoast.showToast(
        msg: msg,
        toastLength: Toast.LENGTH_LONG,
        backgroundColor: colors,
        textColor: Colors.white);
  }
//
//  Future<void> takePicSelfie() async {
//      try {
//        // Ensure that the camera is initialized.
//
//        // Construct the path where the image should be saved using the path
//        // package.
//        final path = join(
//          // Store the picture in the temp directory.
//          // Find the temp directory using the `path_provider` plugin.
//          (await getTemporaryDirectory()).path,
//          '${DateTime.now()}.png',
//        );
//        final path2 = join(
//          // Store the picture in the temp directory.
//          // Find the temp directory using the `path_provider` plugin.
//          (await getTemporaryDirectory()).path,
//          '${DateTime.now()}2.png',
//        );
//
//        await _scanKey.currentState.stop();
//        // Attempt to take a picture and log where it's been saved.
//        await _scanKey.currentState.takePicture(path);
//
//        await _scanKey.currentState.start();
//        print(path2);
//        ImageProcessor.cropSquare(path, path2, false).then((data){
//          if(!mounted) return;
//          setState(() {
//            curImage = path2;
//          });
//
//        });
//      } catch (e) {
//        // If an error occurs, log the error to the console.
//        print(e);
//      }
//  }

  Future<void> takePic() async {
    // Take the Picture in a try / catch block. If anything goes wrong,
    // catch the error.
    try {
      // Ensure that the camera is initialized.
      await _initializeControllerFuture;

      final image = await _controller.takePicture();

      // Construct the path where the image should be saved using the
      // pattern package.
      // final path = join(
      //   // Store the picture in the temp directory.
      //   // Find the temp directory using the `path_provider` plugin.
      //   (await getTemporaryDirectory()).path,
      //   '${DateTime.now()}.png',
      // );
      // final path2 = join(
      //   // Store the picture in the temp directory.
      //   // Find the temp directory using the `path_provider` plugin.
      //   (await getTemporaryDirectory()).path,
      //   '${DateTime.now()}2.png',
      // );
      // // Attempt to take a picture and log where it's been saved.
      // await _controller.takePicture();
      // ImageProcessor.cropSquare(path, path2, false).then((data) {
      //   if (!mounted) return;
      setState(() {
        curImage = image.path;
      });
      // });
      // If the picture was taken, display it on a new screen.

    } catch (e) {
      // If an error occurs, log the error to the console.
      print(e);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xff141322),
      body: ModalProgressHUD(
        opacity: 0.1,
        child: Selfie(),
        inAsyncCall: _saving,
        progressIndicator: CustomLoading(),
      ),
    );
  }

  Widget Selfie() {
    return Stack(
      alignment: Alignment.center,
      children: <Widget>[
        //ปุ่ม back
        Positioned(
            top: mediaQuery(context, 'height', 100.33),
            left: mediaQuery(context, 'width', 20),
            child: backButton(context, LikeWalletAppTheme.gray)),
        Positioned(
          top: MediaQuery.of(context).size.height * Screen_util("height", 268),
          child: new Container(
              alignment: Alignment.center,
              width: MediaQuery.of(context).size.width * 0.9,
              child: Text(
                AppLocalizations.of(context)!.translate('change_phone_title1'),
                style: TextStyle(
                    letterSpacing: 0.5,
                    color: LikeWalletAppTheme.bule1,
                    fontSize: MediaQuery.of(context).size.height *
                        Screen_util("height", 45),
                    fontWeight: FontWeight.w200,
                    fontFamily:
                        AppLocalizations.of(context)!.translate('font1')),
              )),
        ),

        Positioned(
          top: MediaQuery.of(context).size.height * Screen_util("height", 390),
          child: Container(
            color: Colors.grey.withOpacity(0.3),
            height:
                MediaQuery.of(context).size.height * Screen_util("height", 794),
            width:
                MediaQuery.of(context).size.width * Screen_util("width", 1080),
            child: FutureBuilder<void>(
              future: _initializeControllerFuture,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.done) {
                  var size = MediaQuery.of(context).size.width;
                  // If the Future is complete, display the preview.
                  return Transform.scale(
                    scale: 0.85,
                    child: Container(
                      width: size,
                      height: size,
                      child: ClipRRect(
                        child: OverflowBox(
                          alignment: Alignment.center,
                          child: FittedBox(
                            fit: BoxFit.fitWidth,
                            child: Container(
                              width: size,
                              height: size / _controller.value.aspectRatio,
                              child: CameraPreview(
                                _controller,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                } else {
                  // Otherwise, display a loading indicator.
                  return Center(child: CircularProgressIndicator());
                }
              },
            ),
          ),
        ),
        if (number == 1)
          Positioned(
            top:
                MediaQuery.of(context).size.height * Screen_util("height", 390),
            child: Container(
              color: Colors.black,
              height: MediaQuery.of(context).size.height *
                  Screen_util("height", 800),
              width: MediaQuery.of(context).size.width *
                  Screen_util("width", 1080),
            ),
          ),
        if (number == 1)
          Positioned(
            top:
                MediaQuery.of(context).size.height * Screen_util("height", 390),
            child: Container(
              color: Colors.grey.withOpacity(0.3),
              height: MediaQuery.of(context).size.height *
                  Screen_util("height", 800),
              width: MediaQuery.of(context).size.width *
                  Screen_util("width", 1080),
              child: curImage == 'none'
                  ? Center(child: CircularProgressIndicator())
                  : Image.file(File(curImage)),
            ),
          ),
        if (number == 0)
          Positioned(
              top: MediaQuery.of(context).size.height *
                  Screen_util("height", 1072),
              left:
                  MediaQuery.of(context).size.width * Screen_util("width", 450),
              child: Container(
                height: MediaQuery.of(context).size.height *
                    Screen_util("height", 200),
                width: MediaQuery.of(context).size.width *
                    Screen_util("width", 200),
                decoration: BoxDecoration(
                  color: Color(0xff141322),
                  shape: BoxShape.circle,
                ),
              )),
        BorderCamera(),
        BG(),
        if (number == 0)
          Positioned(
            top: MediaQuery.of(context).size.height *
                Screen_util("height", 1678),
            child: new Container(
                alignment: Alignment.center,
                width: MediaQuery.of(context).size.width * 0.9,
                child: Column(
                  children: <Widget>[
                    Text(
                      AppLocalizations.of(context)!
                          .translate('change_phone_title2'),
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Color(0xff707071),
                        fontSize: MediaQuery.of(context).size.height *
                            Screen_util("height", 45),
                        fontFamily:
                            AppLocalizations.of(context)!.translate('font1'),
                      ),
                    ),
//                    Row(
//                      children: <Widget>[
//                        Text(
//                          AppLocalizations.of(context)
//                              .translate('selfie_details2'),
//                          style: TextStyle(
//                            color: Color(0xff707071),
//                            fontSize: MediaQuery.of(context).size.height *
//                                Screen_util("height", 45),
//                            fontFamily:
//                                AppLocalizations.of(context)!.translate('font1'),
//                          ),
//                        ),
//                        Text(
//                          AppLocalizations.of(context)
//                              .translate('selfie_details3'),
//                          style: TextStyle(
//                            color: Color(0xffFFFFFF),
//                            fontSize: MediaQuery.of(context).size.height *
//                                Screen_util("height", 45),
//                            fontFamily:
//                                AppLocalizations.of(context)!.translate('font1'),
//                          ),
//                        ),
//                      ],
//                    )
                  ],
                )),
          ),
        if (number == 0)
          Positioned(
            top: MediaQuery.of(context).size.height *
                Screen_util("height", 1080),
            child: GestureDetector(
                onTap: () {
                  takePic();
                  changeContent(1);
                },
                child: Image.asset(
                  'assets/image/take_photo/button_take_photo.png',
                  height: MediaQuery.of(context).size.height *
                      Screen_util("height", 200),
                  width: MediaQuery.of(context).size.width *
                      Screen_util("width", 200),
                )),
          ),
        if (number == 0)
          Positioned(
            bottom:
                MediaQuery.of(context).size.height * Screen_util("height", 0),
            child: Container(
                width: MediaQuery.of(context).size.width,
                height: mediaQuery(context, "height", 544.51),
                child: Image.asset(LikeWalletImage.logo_change_photo)),
          ),

        if (number == 1)
          Positioned(
            top: MediaQuery.of(context).size.height *
                Screen_util("height", 1284),
            child: GestureDetector(
              onTap: () {
                changeContent(0);
              },
              child: Container(
                alignment: Alignment.center,
                height: MediaQuery.of(context).size.height *
                    Screen_util("height", 257),
                width: MediaQuery.of(context).size.width *
                    Screen_util("width", 257),
                decoration: BoxDecoration(
                  color: Color(0xff282534),
                  shape: BoxShape.circle,
                  boxShadow: [
                    new BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        offset: new Offset(0, 3),
                        blurRadius: 12.0,
                        spreadRadius: 0.0),
                  ],
                ),
                child: Text(
                  AppLocalizations.of(context)!.translate('button_again'),
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    letterSpacing: 0.8,
                    color: LikeWalletAppTheme.gray7,
                    fontWeight: FontWeight.w100,
                    fontSize: MediaQuery.of(context).size.height *
                        Screen_util("height", 30),
                    fontFamily:
                        AppLocalizations.of(context)!.translate('font1'),
                  ),
                ),
              ),
            ),
          ),
        if (number == 1)
          Positioned(
            top: MediaQuery.of(context).size.height *
                Screen_util("height", 1592),
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _saving = true;
                });
                selfie_check = true;
                SetBool_selfie(selfie_check);
                upload(File(curImage)).then((data) async {
                  setState(() {
                    _saving = false;
                  });
                  Navigator.pop(context, data[0]);
                  //
                  // sharedPreferences = await SharedPreferences.getInstance();
                  // sharedPreferences.setString('uploadChangePhone', data[0]);
                  // print(data[0]);
                  // Navigator.push(
                  //   context,
                  //   MaterialPageRoute(
                  //     builder: (context) => ChangePhone(
                  //       photoChangePhone: data[0],
                  //     ),
                  //   ),
                  // );
                });
              },
              child: Container(
                  alignment: Alignment.center,
                  height: MediaQuery.of(context).size.height *
                      Screen_util("height", 257),
                  width: MediaQuery.of(context).size.width *
                      Screen_util("width", 257),
                  decoration: BoxDecoration(
                    color: Color(0xff282534),
                    shape: BoxShape.circle,
                    boxShadow: [
                      new BoxShadow(
                          color: Colors.black.withOpacity(0.2),
                          offset: new Offset(0, 3),
                          blurRadius: 12.0,
                          spreadRadius: 0.0),
                    ],
                  ),
                  child: Text(
                    AppLocalizations.of(context)!.translate('button_yes'),
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      letterSpacing: 0.8,
                      color: LikeWalletAppTheme.bule1,
                      fontSize: MediaQuery.of(context).size.height *
                          Screen_util("height", 30),
                      fontWeight: FontWeight.w100,
                      fontFamily:
                          AppLocalizations.of(context)!.translate('font1'),
                    ),
                  )),
            ),
          ),
        Positioned(
          top: MediaQuery.of(context).size.height * Screen_util("height", 125),
          right: mediaQuery(context, 'height', 75),
          child: GestureDetector(
            onTap: () async {
              print('switch camera');
              _onSwitchCamera();
            },
            child: Container(
                alignment: Alignment.center,
                height: MediaQuery.of(context).size.height *
                    Screen_util("height", 80),
                width: MediaQuery.of(context).size.width *
                    Screen_util("width", 80),
                decoration: BoxDecoration(
                  color: Color(0xff0FE8D8),
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Color(0xff000000),
                    width: MediaQuery.of(context).size.width *
                        Screen_util("height", 3),
                  ),
                ),
                child: new Icon(
                  Icons.switch_camera,
                )),
          ),
        )
      ],
    );
  }

  void _onSwitchCamera() {
    selectedCameraIdx =
        selectedCameraIdx < cameras.length - 1 ? selectedCameraIdx + 1 : 0;
    CameraDescription selectedCamera = cameras[selectedCameraIdx];
    _initCameraController(selectedCamera);
  }

  Future _initCameraController(CameraDescription cameraDescription) async {
    if (_controller != null) {
//      await _controller.dispose();
    }

    // 3
    _controller = CameraController(cameraDescription, ResolutionPreset.high);

    // If the controller is updated then update the UI.
    // 4
    _controller.addListener(() {
      // 5
      if (mounted) {
        setState(() {});
      }

      if (_controller.value.hasError) {
        print('Camera error ${_controller.value.errorDescription}');
      }
    });

    // 6
    try {
      await _controller.initialize();
    } on CameraException catch (e) {
//      _showCameraException(e);
      print(e);
    }

    if (mounted) {
      setState(() {});
    }
  }

  Widget BG() {
    return Positioned(
        bottom: 0,
        child: Container(
          child: Image.asset(
            number == 1
                ? LikeWalletImage.bg_take_photo2
                : LikeWalletImage.bg_take_photo,
            fit: BoxFit.fill,
            height: number == 1
                ? mediaQuery(context, 'height', 1110.87)
                : mediaQuery(context, 'height', 1325.87),
            width: mediaQuery(context, 'width', 1080),
          ),
        ));
  }

  SetBool_front(bool value) async {
    sharedPreferences = await SharedPreferences.getInstance();
    setState(() {
      front_check = value;
      sharedPreferences.setBool("front_check", front_check);
    });
  }

  SetBool_end(bool value) async {
    sharedPreferences = await SharedPreferences.getInstance();
    setState(() {
      end_check = value;
      sharedPreferences.setBool("end_check", end_check);
    });
  }

  SetBool_selfie(bool value) async {
    sharedPreferences = await SharedPreferences.getInstance();
    setState(() {
      selfie_check = value;
      sharedPreferences.setBool("selfie_check", selfie_check);
    });
  }

  Widget BorderCamera() {
    return Positioned(
      top: MediaQuery.of(context).size.height * Screen_util("height", 470),
      child: Image.asset('assets/image/take_photo/border_camera.png'),
      height: mediaQuery(context, 'height', 608.3),
      width: mediaQuery(context, 'width', 924.53),
    );
  }
}
