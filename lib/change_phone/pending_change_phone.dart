// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:likewallet/ImageTheme.dart';
// import 'package:likewallet/Theme.dart';
// import 'package:likewallet/libraryman/app_local.dart';
// import 'package:likewallet/change_phone/NavigationBar.dart';
// import 'package:likewallet/screen_util.dart';
//
// class PendingChangePhone extends StatefulWidget {
//   PendingChangePhone();
//
//   _PendingChangePhone createState() => new _PendingChangePhone();
// }
//
// class _PendingChangePhone extends State<PendingChangePhone> {
//   Widget build(BuildContext context) {
//     return new Scaffold(
//         body: Stack(
//       alignment: Alignment.center,
//       children: [
//         _bg(),
//         Positioned(
//           top: mediaQuery(context, 'height', 375),
//           left: mediaQuery(context, 'width', 337),
//           child: Container(
//             child: Image.asset(
//               LikeWalletImage.logo_change_photo2,
//               height: mediaQuery(context, 'height', 243.88),
//             ),
//           ),
//         ),
//         Positioned(
//             top: mediaQuery(context, 'height', 469),
//             right: mediaQuery(context, 'width', 337.6),
//             child: Image.asset(
//               LikeWalletImage.icon_setting,
//               height: mediaQuery(context, 'height', 233),
//             )),
//         Positioned(
//           top: mediaQuery(context, 'height', 859),
//           width: mediaQuery(context, 'width', 825),
//           child: Text(
//             AppLocalizations.of(context)!.translate('change_phone_pending_text'),
//             textAlign: TextAlign.center,
//             style: TextStyle(
//                 color: LikeWalletAppTheme.bule1,
//                 fontSize: mediaQuery(context, 'height', 42),
//                 fontFamily: AppLocalizations.of(context)!.translate('font1'),
//                 letterSpacing: 0.3,
//                 fontWeight: FontWeight.w100),
//           ),
//         ),
//         Container(
//           alignment: Alignment.bottomCenter,
//           child: NavigationBar(),
//         )
//       ],
//     ));
//   }
//
//   Widget _bg() {
//     return Column(
//       children: [
//         Container(
//           alignment: Alignment.bottomLeft,
//           height: mediaQuery(context, 'height', 619),
//           decoration: BoxDecoration(
//             gradient: LinearGradient(
//               begin: Alignment(1.0, -0.95),
//               end: Alignment(-1.0, 0.9),
//               colors: [const Color(0xff30f3ca), const Color(0xff24c6e4)],
//               stops: [0.0, 1.0],
//             ),
//           ),
//         ),
//         Container(
//           height: MediaQuery.of(context).size.height -
//               mediaQuery(context, 'height', 619),
//           decoration: BoxDecoration(
//             gradient: LinearGradient(
//               begin: Alignment(0.0, -1.0),
//               end: Alignment(0.0, 1.0),
//               colors: [const Color(0xff25263c), const Color(0xff141322)],
//               stops: [0.0, 1.0],
//             ),
//             boxShadow: [
//               BoxShadow(
//                 color: const Color(0xcc00887f),
//                 offset: Offset(0, -mediaQuery(context, 'height', 12)),
//                 blurRadius: mediaQuery(context, 'height', 35),
//               ),
//             ],
//           ),
//         ), // Container(
//         //   height: MediaQuery.of(context).size.height -
//         //       mediaQuery(context, 'height', 619),
//         //   color: LikeWalletAppTheme.bule2,
//         // ),
//       ],
//     );
//   }
// }
