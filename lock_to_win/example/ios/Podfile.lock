PODS:
  - abseil/algorithm (0.20200225.0):
    - abseil/algorithm/algorithm (= 0.20200225.0)
    - abseil/algorithm/container (= 0.20200225.0)
  - abseil/algorithm/algorithm (0.20200225.0):
    - abseil/base/config
  - abseil/algorithm/container (0.20200225.0):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/meta/type_traits
  - abseil/base (0.20200225.0):
    - abseil/base/atomic_hook (= 0.20200225.0)
    - abseil/base/base (= 0.20200225.0)
    - abseil/base/base_internal (= 0.20200225.0)
    - abseil/base/bits (= 0.20200225.0)
    - abseil/base/config (= 0.20200225.0)
    - abseil/base/core_headers (= 0.20200225.0)
    - abseil/base/dynamic_annotations (= 0.20200225.0)
    - abseil/base/endian (= 0.20200225.0)
    - abseil/base/errno_saver (= 0.20200225.0)
    - abseil/base/exponential_biased (= 0.20200225.0)
    - abseil/base/log_severity (= 0.20200225.0)
    - abseil/base/malloc_internal (= 0.20200225.0)
    - abseil/base/periodic_sampler (= 0.20200225.0)
    - abseil/base/pretty_function (= 0.20200225.0)
    - abseil/base/raw_logging_internal (= 0.20200225.0)
    - abseil/base/spinlock_wait (= 0.20200225.0)
    - abseil/base/throw_delegate (= 0.20200225.0)
  - abseil/base/atomic_hook (0.20200225.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/base/base (0.20200225.0):
    - abseil/base/atomic_hook
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/log_severity
    - abseil/base/raw_logging_internal
    - abseil/base/spinlock_wait
    - abseil/meta/type_traits
  - abseil/base/base_internal (0.20200225.0):
    - abseil/base/config
    - abseil/meta/type_traits
  - abseil/base/bits (0.20200225.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/base/config (0.20200225.0)
  - abseil/base/core_headers (0.20200225.0):
    - abseil/base/config
  - abseil/base/dynamic_annotations (0.20200225.0)
  - abseil/base/endian (0.20200225.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/base/errno_saver (0.20200225.0):
    - abseil/base/config
  - abseil/base/exponential_biased (0.20200225.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/base/log_severity (0.20200225.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/base/malloc_internal (0.20200225.0):
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
  - abseil/base/periodic_sampler (0.20200225.0):
    - abseil/base/core_headers
    - abseil/base/exponential_biased
  - abseil/base/pretty_function (0.20200225.0)
  - abseil/base/raw_logging_internal (0.20200225.0):
    - abseil/base/atomic_hook
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
  - abseil/base/spinlock_wait (0.20200225.0):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/base/errno_saver
  - abseil/base/throw_delegate (0.20200225.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
  - abseil/container/common (0.20200225.0):
    - abseil/meta/type_traits
    - abseil/types/optional
  - abseil/container/compressed_tuple (0.20200225.0):
    - abseil/utility/utility
  - abseil/container/container_memory (0.20200225.0):
    - abseil/memory/memory
    - abseil/utility/utility
  - abseil/container/fixed_array (0.20200225.0):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/throw_delegate
    - abseil/container/compressed_tuple
    - abseil/memory/memory
  - abseil/container/flat_hash_map (0.20200225.0):
    - abseil/algorithm/container
    - abseil/container/container_memory
    - abseil/container/hash_function_defaults
    - abseil/container/raw_hash_map
    - abseil/memory/memory
  - abseil/container/hash_function_defaults (0.20200225.0):
    - abseil/base/config
    - abseil/hash/hash
    - abseil/strings/strings
  - abseil/container/hash_policy_traits (0.20200225.0):
    - abseil/meta/type_traits
  - abseil/container/hashtable_debug_hooks (0.20200225.0):
    - abseil/base/config
  - abseil/container/hashtablez_sampler (0.20200225.0):
    - abseil/base/base
    - abseil/base/core_headers
    - abseil/base/exponential_biased
    - abseil/container/have_sse
    - abseil/debugging/stacktrace
    - abseil/memory/memory
    - abseil/synchronization/synchronization
    - abseil/utility/utility
  - abseil/container/have_sse (0.20200225.0)
  - abseil/container/inlined_vector (0.20200225.0):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/throw_delegate
    - abseil/container/inlined_vector_internal
    - abseil/memory/memory
  - abseil/container/inlined_vector_internal (0.20200225.0):
    - abseil/base/core_headers
    - abseil/container/compressed_tuple
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/types/span
  - abseil/container/layout (0.20200225.0):
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/types/span
    - abseil/utility/utility
  - abseil/container/raw_hash_map (0.20200225.0):
    - abseil/base/throw_delegate
    - abseil/container/container_memory
    - abseil/container/raw_hash_set
  - abseil/container/raw_hash_set (0.20200225.0):
    - abseil/base/bits
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/container/common
    - abseil/container/compressed_tuple
    - abseil/container/container_memory
    - abseil/container/hash_policy_traits
    - abseil/container/hashtable_debug_hooks
    - abseil/container/hashtablez_sampler
    - abseil/container/have_sse
    - abseil/container/layout
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/utility/utility
  - abseil/debugging/debugging_internal (0.20200225.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/errno_saver
    - abseil/base/raw_logging_internal
  - abseil/debugging/demangle_internal (0.20200225.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/debugging/stacktrace (0.20200225.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/debugging/debugging_internal
  - abseil/debugging/symbolize (0.20200225.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/debugging/debugging_internal
    - abseil/debugging/demangle_internal
  - abseil/hash/city (0.20200225.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
  - abseil/hash/hash (0.20200225.0):
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/container/fixed_array
    - abseil/hash/city
    - abseil/meta/type_traits
    - abseil/numeric/int128
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/variant
    - abseil/utility/utility
  - abseil/memory (0.20200225.0):
    - abseil/memory/memory (= 0.20200225.0)
  - abseil/memory/memory (0.20200225.0):
    - abseil/base/core_headers
    - abseil/meta/type_traits
  - abseil/meta (0.20200225.0):
    - abseil/meta/type_traits (= 0.20200225.0)
  - abseil/meta/type_traits (0.20200225.0):
    - abseil/base/config
  - abseil/numeric/int128 (0.20200225.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/strings/internal (0.20200225.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/meta/type_traits
  - abseil/strings/str_format (0.20200225.0):
    - abseil/strings/str_format_internal
  - abseil/strings/str_format_internal (0.20200225.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/numeric/int128
    - abseil/strings/strings
    - abseil/types/span
  - abseil/strings/strings (0.20200225.0):
    - abseil/base/base
    - abseil/base/bits
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/base/throw_delegate
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/numeric/int128
    - abseil/strings/internal
  - abseil/synchronization/graphcycles_internal (0.20200225.0):
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
  - abseil/synchronization/kernel_timeout_internal (0.20200225.0):
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/time/time
  - abseil/synchronization/synchronization (0.20200225.0):
    - abseil/base/atomic_hook
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/synchronization/graphcycles_internal
    - abseil/synchronization/kernel_timeout_internal
    - abseil/time/time
  - abseil/time (0.20200225.0):
    - abseil/time/internal (= 0.20200225.0)
    - abseil/time/time (= 0.20200225.0)
  - abseil/time/internal (0.20200225.0):
    - abseil/time/internal/cctz (= 0.20200225.0)
  - abseil/time/internal/cctz (0.20200225.0):
    - abseil/time/internal/cctz/civil_time (= 0.20200225.0)
    - abseil/time/internal/cctz/time_zone (= 0.20200225.0)
  - abseil/time/internal/cctz/civil_time (0.20200225.0):
    - abseil/base/config
  - abseil/time/internal/cctz/time_zone (0.20200225.0):
    - abseil/base/config
    - abseil/time/internal/cctz/civil_time
  - abseil/time/time (0.20200225.0):
    - abseil/base/base
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/numeric/int128
    - abseil/strings/strings
    - abseil/time/internal/cctz/civil_time
    - abseil/time/internal/cctz/time_zone
  - abseil/types (0.20200225.0):
    - abseil/types/any (= 0.20200225.0)
    - abseil/types/bad_any_cast (= 0.20200225.0)
    - abseil/types/bad_any_cast_impl (= 0.20200225.0)
    - abseil/types/bad_optional_access (= 0.20200225.0)
    - abseil/types/bad_variant_access (= 0.20200225.0)
    - abseil/types/compare (= 0.20200225.0)
    - abseil/types/optional (= 0.20200225.0)
    - abseil/types/span (= 0.20200225.0)
    - abseil/types/variant (= 0.20200225.0)
  - abseil/types/any (0.20200225.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/types/bad_any_cast
    - abseil/utility/utility
  - abseil/types/bad_any_cast (0.20200225.0):
    - abseil/base/config
    - abseil/types/bad_any_cast_impl
  - abseil/types/bad_any_cast_impl (0.20200225.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
  - abseil/types/bad_optional_access (0.20200225.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
  - abseil/types/bad_variant_access (0.20200225.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
  - abseil/types/compare (0.20200225.0):
    - abseil/base/core_headers
    - abseil/meta/type_traits
  - abseil/types/optional (0.20200225.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/types/bad_optional_access
    - abseil/utility/utility
  - abseil/types/span (0.20200225.0):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/throw_delegate
    - abseil/meta/type_traits
  - abseil/types/variant (0.20200225.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/types/bad_variant_access
    - abseil/utility/utility
  - abseil/utility/utility (0.20200225.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/meta/type_traits
  - BoringSSL-GRPC (0.0.7):
    - BoringSSL-GRPC/Implementation (= 0.0.7)
    - BoringSSL-GRPC/Interface (= 0.0.7)
  - BoringSSL-GRPC/Implementation (0.0.7):
    - BoringSSL-GRPC/Interface (= 0.0.7)
  - BoringSSL-GRPC/Interface (0.0.7)
  - cloud_firestore (2.5.4):
    - Firebase/Firestore (= 8.11.0)
    - firebase_core
    - Flutter
  - Firebase/Auth (8.11.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 8.11.0)
  - Firebase/CoreOnly (8.11.0):
    - FirebaseCore (= 8.11.0)
  - Firebase/Firestore (8.11.0):
    - Firebase/CoreOnly
    - FirebaseFirestore (~> 8.11.0)
  - firebase_auth (3.3.8):
    - Firebase/Auth (= 8.11.0)
    - firebase_core
    - Flutter
  - firebase_core (1.12.0):
    - Firebase/CoreOnly (= 8.11.0)
    - Flutter
  - FirebaseAuth (8.11.0):
    - FirebaseCore (~> 8.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/Environment (~> 7.7)
    - GTMSessionFetcher/Core (~> 1.5)
  - FirebaseCore (8.11.0):
    - FirebaseCoreDiagnostics (~> 8.0)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/Logger (~> 7.7)
  - FirebaseCoreDiagnostics (8.14.0):
    - GoogleDataTransport (~> 9.1)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/Logger (~> 7.7)
    - nanopb (~> 2.30908.0)
  - FirebaseFirestore (8.11.0):
    - abseil/algorithm (= 0.20200225.0)
    - abseil/base (= 0.20200225.0)
    - abseil/container/flat_hash_map (= 0.20200225.0)
    - abseil/memory (= 0.20200225.0)
    - abseil/meta (= 0.20200225.0)
    - abseil/strings/strings (= 0.20200225.0)
    - abseil/time (= 0.20200225.0)
    - abseil/types (= 0.20200225.0)
    - FirebaseCore (~> 8.0)
    - "gRPC-C++ (~> 1.28.0)"
    - leveldb-library (~> 1.22)
    - nanopb (~> 2.30908.0)
  - Flutter (1.0.0)
  - flutter_secure_storage (3.3.1):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
    - Toast
  - GoogleDataTransport (9.1.2):
    - GoogleUtilities/Environment (~> 7.2)
    - nanopb (~> 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/AppDelegateSwizzler (7.7.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
  - GoogleUtilities/Environment (7.7.0):
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.7.0):
    - GoogleUtilities/Environment
  - GoogleUtilities/Network (7.7.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.7.0)"
  - GoogleUtilities/Reachability (7.7.0):
    - GoogleUtilities/Logger
  - "gRPC-C++ (1.28.2)":
    - "gRPC-C++/Implementation (= 1.28.2)"
    - "gRPC-C++/Interface (= 1.28.2)"
  - "gRPC-C++/Implementation (1.28.2)":
    - abseil/container/inlined_vector (= 0.20200225.0)
    - abseil/memory/memory (= 0.20200225.0)
    - abseil/strings/str_format (= 0.20200225.0)
    - abseil/strings/strings (= 0.20200225.0)
    - abseil/types/optional (= 0.20200225.0)
    - "gRPC-C++/Interface (= 1.28.2)"
    - gRPC-Core (= 1.28.2)
  - "gRPC-C++/Interface (1.28.2)"
  - gRPC-Core (1.28.2):
    - gRPC-Core/Implementation (= 1.28.2)
    - gRPC-Core/Interface (= 1.28.2)
  - gRPC-Core/Implementation (1.28.2):
    - abseil/container/inlined_vector (= 0.20200225.0)
    - abseil/memory/memory (= 0.20200225.0)
    - abseil/strings/str_format (= 0.20200225.0)
    - abseil/strings/strings (= 0.20200225.0)
    - abseil/types/optional (= 0.20200225.0)
    - BoringSSL-GRPC (= 0.0.7)
    - gRPC-Core/Interface (= 1.28.2)
  - gRPC-Core/Interface (1.28.2)
  - GTMSessionFetcher/Core (1.7.1)
  - leveldb-library (1.22.1)
  - lock_to_win (0.0.1):
    - Flutter
  - nanopb (2.30908.0):
    - nanopb/decode (= 2.30908.0)
    - nanopb/encode (= 2.30908.0)
  - nanopb/decode (2.30908.0)
  - nanopb/encode (2.30908.0)
  - path_provider_ios (0.0.1):
    - Flutter
  - PromisesObjC (2.1.0)
  - shared_preferences_ios (0.0.1):
    - Flutter
  - Toast (4.0.0)

DEPENDENCIES:
  - cloud_firestore (from `.symlinks/plugins/cloud_firestore/ios`)
  - firebase_auth (from `.symlinks/plugins/firebase_auth/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - Flutter (from `Flutter`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - lock_to_win (from `.symlinks/plugins/lock_to_win/ios`)
  - path_provider_ios (from `.symlinks/plugins/path_provider_ios/ios`)
  - shared_preferences_ios (from `.symlinks/plugins/shared_preferences_ios/ios`)

SPEC REPOS:
  trunk:
    - abseil
    - BoringSSL-GRPC
    - Firebase
    - FirebaseAuth
    - FirebaseCore
    - FirebaseCoreDiagnostics
    - FirebaseFirestore
    - GoogleDataTransport
    - GoogleUtilities
    - "gRPC-C++"
    - gRPC-Core
    - GTMSessionFetcher
    - leveldb-library
    - nanopb
    - PromisesObjC
    - Toast

EXTERNAL SOURCES:
  cloud_firestore:
    :path: ".symlinks/plugins/cloud_firestore/ios"
  firebase_auth:
    :path: ".symlinks/plugins/firebase_auth/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  Flutter:
    :path: Flutter
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  lock_to_win:
    :path: ".symlinks/plugins/lock_to_win/ios"
  path_provider_ios:
    :path: ".symlinks/plugins/path_provider_ios/ios"
  shared_preferences_ios:
    :path: ".symlinks/plugins/shared_preferences_ios/ios"

SPEC CHECKSUMS:
  abseil: 6c8eb7892aefa08d929b39f9bb108e5367e3228f
  BoringSSL-GRPC: 8edf627ee524575e2f8d19d56f068b448eea3879
  cloud_firestore: b6f2881c2ab05197836bf773780c1b9ba4411156
  Firebase: 44dd9724c84df18b486639e874f31436eaa9a20c
  firebase_auth: 386760cb543983c9f00ed148cd83e2c8443c94a2
  firebase_core: 443bccfd6aa6b42f07be365b500773dc69db2d87
  FirebaseAuth: d96d73aba85d192d7a7aa0b86dd6d7f8ec170b4b
  FirebaseCore: 2f4f85b453cc8fea4bb2b37e370007d2bcafe3f0
  FirebaseCoreDiagnostics: fd0c8490f34287229c1d6c103d3a55f81ec85712
  FirebaseFirestore: 3b2e4b532c68809b3df5f8d39d28b3398ffc196b
  Flutter: 50d75fe2f02b26cc09d224853bb45737f8b3214a
  flutter_secure_storage: 7953c38a04c3fdbb00571bcd87d8e3b5ceb9daec
  fluttertoast: 16fbe6039d06a763f3533670197d01fc73459037
  GoogleDataTransport: 629c20a4d363167143f30ea78320d5a7eb8bd940
  GoogleUtilities: e0913149f6b0625b553d70dae12b49fc62914fd1
  "gRPC-C++": 13d8ccef97d5c3c441b7e3c529ef28ebee86fad2
  gRPC-Core: 4afa11bfbedf7cdecd04de535a9e046893404ed5
  GTMSessionFetcher: 4577a4cc914a5a07c40a8a0ad0acc22080418c2d
  leveldb-library: 50c7b45cbd7bf543c81a468fe557a16ae3db8729
  lock_to_win: d1158323d6b9450852185328c29ed09af4ddddf1
  nanopb: a0ba3315591a9ae0a16a309ee504766e90db0c96
  path_provider_ios: 14f3d2fd28c4fdb42f44e0f751d12861c43cee02
  PromisesObjC: 99b6f43f9e1044bd87a95a60beff28c2c44ddb72
  shared_preferences_ios: 548a61f8053b9b8a49ac19c1ffbc8b92c50d68ad
  Toast: 91b396c56ee72a5790816f40d3a94dd357abc196

PODFILE CHECKSUM: 4e8f8b2be68aeea4c0d5beb6ff1e79fface1d048

COCOAPODS: 1.11.2
