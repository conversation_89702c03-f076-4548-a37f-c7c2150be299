import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'dart:async';

import 'package:flutter/services.dart';
import 'package:lock_to_win/lock_to_win.dart';
import 'package:lock_to_win_example/test.dart';

void main() {
  runApp(MaterialApp(home: MyApp()));
}

class MyApp extends StatefulWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  String _platformVersion = 'Unknown';

  @override
  void initState() {
    super.initState();
    initPlatformState();
  }

  // Platform messages are asynchronous, so we initialize in an async method.
  Future<void> initPlatformState() async {
    String platformVersion;
    // Platform messages may fail, so we use a try/catch PlatformException.
    // We also handle the message potentially returning null.
    try {
      platformVersion =
          await LockToWin.platformVersion ?? 'Unknown platform version';
    } on PlatformException {
      platformVersion = 'Failed to get platform version.';
    }

    // If the widget was removed from the tree while the asynchronous platform
    // message was in flight, we want to discard the reply rather than calling
    // setState to update our non-existent appearance.
    if (!mounted) return;

    setState(() {
      _platformVersion = platformVersion;
    });
  }

  var data = {
    "mnemonic":
        'repeat expect gate gate company hire trend truth penalty walk crucial envelope',
    "address": "******************************************",
    "token":
        "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.X2kJkAkjnAjzjcNyPIyd9oA97Hs0_GqduoTNUkcsXyED2DhBPK7JGWQHTDaO2pKTbp2qUE5PfhAfkIAqoLRV6KFRizgM9DLXSz0XGxulKh7OEnlw7xAbgUghbzGoPrU-hVDjnHTat7NoD1wt4zOlWsiCoIp96i97jRLEbf4wkPb01qwzBMmoOPC5pg_D-ysUX5jKvvcDSYHoAZ7SZaocnMvSe6399-8NWZC9oNJXx0uUz2iR4bd-PfxiST-WYp2wZqXvN8VWtVcgPNqD2-LOJ6JRLI0btkMkpKUpGDtb9KAa-aM0w76XpGHTb0fPxaezYJro6n5oNFnLUebIJ2PqvA",
  };
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Plugin example app'),
      ),
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Center(
            child: CupertinoButton(
                child: Text(
                  "Click Me",
                ),
                color: CupertinoColors.activeBlue,
                onPressed: () {
                  LockToWin.startLockToWin(context, data: data);
                  // Navigator.push(
                  //     context, MaterialPageRoute(builder: (context) => Test()));
                }),
          ),
        ],
      ),
    );
  }
}
