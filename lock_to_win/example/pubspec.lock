# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  _fe_analyzer_shared:
    dependency: transitive
    description:
      name: _fe_analyzer_shared
      sha256: e6dd5609f0945ef6bc72bdcd28af6eeabefef99a6d62b7790320133789217759
      url: "https://pub.dev"
    source: hosted
    version: "38.0.0"
  analyzer:
    dependency: transitive
    description:
      name: analyzer
      sha256: c71e50e4e1674c9ffeaf053bb8d38e4a03b94d08af6a27fb352f3ff569becc44
      url: "https://pub.dev"
    source: hosted
    version: "3.4.1"
  animated:
    dependency: transitive
    description:
      name: animated
      sha256: de84f1daecec3d81c5c72fb2958f03389aee3aeaa0323adf365bacf1ecfaaf0f
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  animated_text_kit:
    dependency: transitive
    description:
      name: animated_text_kit
      sha256: "5e6bf696c1db94fae17ef0527a17cef82f3100daebb53a3bdeb1c34f6b2ccdcc"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.1"
  animations:
    dependency: transitive
    description:
      name: animations
      sha256: b70b084d3a3895aab7764bd505b89e72c199e5edcd1e4e3af0c76b52c2c7c5b6
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  archive:
    dependency: transitive
    description:
      name: archive
      sha256: eb33140ede1b4039f4ad631f7bf3cfa58e24514e8bf87184bc32f17541af87fc
      url: "https://pub.dev"
    source: hosted
    version: "3.3.0"
  args:
    dependency: transitive
    description:
      name: args
      sha256: "0bd9a99b6eb96f07af141f0eb53eace8983e8e5aa5de59777aca31684680ef22"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  asn1lib:
    dependency: transitive
    description:
      name: asn1lib
      sha256: c273725e171cea7e69c8953181202a2850297bcc7617916d83b396cd791a2dcd
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  async:
    dependency: transitive
    description:
      name: async
      sha256: bfe67ef28df125b7dddcea62755991f807aa39a2492a23e1550161692950bbe0
      url: "https://pub.dev"
    source: hosted
    version: "2.10.0"
  bip32:
    dependency: transitive
    description:
      name: bip32
      sha256: "54787cd7a111e9d37394aabbf53d1fc5e2e0e0af2cd01c459147a97c0e3f8a97"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  bip39:
    dependency: transitive
    description:
      name: bip39
      sha256: de1ee27ebe7d96b84bb3a04a4132a0a3007dcdd5ad27dd14aa87a29d97c45edc
      url: "https://pub.dev"
    source: hosted
    version: "1.0.6"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      sha256: "6cfb5af12253eaf2b368f07bacc5a80d1301a071c73360d746b7f2e32d762c66"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  bs58check:
    dependency: transitive
    description:
      name: bs58check
      sha256: c4a164d42b25c2f6bc88a8beccb9fc7d01440f3c60ba23663a20a70faf484ea9
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  build:
    dependency: transitive
    description:
      name: build
      sha256: c9b6c412967d7887e88efe1ffbfe0f31bfaf6a5a4b98eb8d59964977a90f2f9e
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  build_config:
    dependency: transitive
    description:
      name: build_config
      sha256: ad77deb6e9c143a3f550fbb4c5c1e0c6aadabe24274898d06b9526c61b9cf4fb
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  built_collection:
    dependency: transitive
    description:
      name: built_collection
      sha256: "376e3dd27b51ea877c28d525560790aee2e6fbb5f20e2f85d5081027d94e2100"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.1"
  built_value:
    dependency: transitive
    description:
      name: built_value
      sha256: b6c9911b2d670376918d5b8779bc27e0e612a94ec3ff0343689e991d8d0a3b8a
      url: "https://pub.dev"
    source: hosted
    version: "8.1.4"
  characters:
    dependency: transitive
    description:
      name: characters
      sha256: e6a326c8af69605aec75ed6c187d06b349707a27fbff8222ca9cc2cff167975c
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  charcode:
    dependency: transitive
    description:
      name: charcode
      sha256: fb98c0f6d12c920a02ee2d998da788bca066ca5f148492b7085ee23372b12306
      url: "https://pub.dev"
    source: hosted
    version: "1.3.1"
  checked_yaml:
    dependency: transitive
    description:
      name: checked_yaml
      sha256: dd007e4fb8270916820a0d66e24f619266b60773cddd082c6439341645af2659
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  clock:
    dependency: transitive
    description:
      name: clock
      sha256: cb6d7f03e1de671e34607e909a7213e31d7752be4fb66a86d29fe1eb14bfb5cf
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  cloud_firestore:
    dependency: transitive
    description:
      name: cloud_firestore
      sha256: "7313ccae834ee8dd0b93ea367ba228b86212f5ca39d48c1e142dbbd377af5ed4"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.4"
  cloud_firestore_platform_interface:
    dependency: transitive
    description:
      name: cloud_firestore_platform_interface
      sha256: "1d34efcd9b9f24e9100aef7d39e58639133e5deb8f8a83b6cc68eeebc597c46a"
      url: "https://pub.dev"
    source: hosted
    version: "5.5.0"
  cloud_firestore_web:
    dependency: transitive
    description:
      name: cloud_firestore_web
      sha256: "2e71a9c3ecda12561375fe5d4105654e47d73163bb0d5194e6b1aab61cef931d"
      url: "https://pub.dev"
    source: hosted
    version: "2.6.9"
  code_builder:
    dependency: transitive
    description:
      name: code_builder
      sha256: bdb1ab29be158c4784d7f9b7b693745a0719c5899e31c01112782bb1cb871e80
      url: "https://pub.dev"
    source: hosted
    version: "4.1.0"
  collection:
    dependency: transitive
    description:
      name: collection
      sha256: cfc915e6923fe5ce6e153b0723c753045de46de1b4d63771530504004a45fae0
      url: "https://pub.dev"
    source: hosted
    version: "1.17.0"
  convert:
    dependency: transitive
    description:
      name: convert
      sha256: f08428ad63615f96a27e34221c65e1a451439b5f26030f78d790f461c686d65d
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  crypto:
    dependency: transitive
    description:
      name: crypto
      sha256: cf75650c66c0316274e21d7c43d3dea246273af5955bd94e8184837cd577575c
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  cupertino_icons:
    dependency: "direct main"
    description:
      name: cupertino_icons
      sha256: "1989d917fbe8e6b39806207df5a3fdd3d816cbd090fac2ce26fb45e9a71476e5"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  dart_style:
    dependency: transitive
    description:
      name: dart_style
      sha256: "959d3de27b5e0713f954643ee38619551da06e61e08088968fe7e1eb0cd720b1"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.2"
  ed25519_hd_key:
    dependency: transitive
    description:
      name: ed25519_hd_key
      sha256: "326608234e986ea826a5db4cf4cd6826058d860875a3fff7926c0725fe1a604d"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  encrypt:
    dependency: transitive
    description:
      name: encrypt
      sha256: "4fd4e4fdc21b9d7d4141823e1e6515cd94e7b8d84749504c232999fba25d9bbb"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.1"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      sha256: "511392330127add0b769b75a987850d136345d9227c6b94c96a04cf4a391bf78"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.1"
  ffi:
    dependency: transitive
    description:
      name: ffi
      sha256: "35d0f481d939de0d640b3db9a7aa36a52cd22054a798a73b4f50bdad5ce12678"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  file:
    dependency: transitive
    description:
      name: file
      sha256: b69516f2c26a5bcac4eee2e32512e1a5205ab312b3536c1c1227b2b942b5f9ad
      url: "https://pub.dev"
    source: hosted
    version: "6.1.2"
  firebase_auth:
    dependency: transitive
    description:
      name: firebase_auth
      sha256: "22407a8c78b0f879d4c6b0d244f56952f26352a574a8c42262293275835533fd"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  firebase_auth_platform_interface:
    dependency: transitive
    description:
      name: firebase_auth_platform_interface
      sha256: "707612dbe6e6f7d96dd2dc75d61f0f31b5536e8c5cbdc45639f3f5ca79d1b781"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  firebase_auth_web:
    dependency: transitive
    description:
      name: firebase_auth_web
      sha256: f3ce926b2f862f0a3de5607e45520ab8a3a967bba6a2f8b7de73192aee92c618
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  firebase_core:
    dependency: transitive
    description:
      name: firebase_core
      sha256: "0232f60f41be892ec52f100b0c4a035d2ab523fa7f8adc8d183e01a75e42db1a"
      url: "https://pub.dev"
    source: hosted
    version: "1.12.0"
  firebase_core_platform_interface:
    dependency: transitive
    description:
      name: firebase_core_platform_interface
      sha256: f9af376a733d91ab8ff1f2942479cb81bbb8a8bc67c2b6c51a9cae5a10f028a0
      url: "https://pub.dev"
    source: hosted
    version: "4.2.4"
  firebase_core_web:
    dependency: transitive
    description:
      name: firebase_core_web
      sha256: "91abd7fdec09db644d8bdf4022fac43e639bf1a3b87d5810f8b9a14edc221f2d"
      url: "https://pub.dev"
    source: hosted
    version: "1.6.0"
  fixnum:
    dependency: transitive
    description:
      name: fixnum
      sha256: "6a2ef17156f4dc49684f9d99aaf4a93aba8ac49f5eac861755f5730ddf6e2e4e"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_countdown_timer:
    dependency: transitive
    description:
      name: flutter_countdown_timer
      sha256: dfcbd7d6f76a5589f78f3f3ba2f9ea2e199368eccc1adce4153ce985b9587bc5
      url: "https://pub.dev"
    source: hosted
    version: "4.1.0"
  flutter_custom_clippers:
    dependency: transitive
    description:
      name: flutter_custom_clippers
      sha256: "9452abe174befb0eee2b4de11ef7f38daced5054908791a45d89aa49e80c0a94"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  flutter_easyloading:
    dependency: transitive
    description:
      name: flutter_easyloading
      sha256: "9b43cf38ef9fddcd0fd1b7821ea9d80b494484e3522383b76e7bf6da67ebf8b9"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.3"
  flutter_lints:
    dependency: "direct dev"
    description:
      name: flutter_lints
      sha256: b543301ad291598523947dc534aaddc5aaad597b709d2426d3a0e0d44c5cb493
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  flutter_localizations:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_secure_storage:
    dependency: transitive
    description:
      name: flutter_secure_storage
      sha256: "9f3dd2ac3b6875b0fde5b04734789c3ef35ba3965c18e99dd564a7a2f8056df6"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.1"
  flutter_spinkit:
    dependency: transitive
    description:
      name: flutter_spinkit
      sha256: "77a2117c0517ff909221f3160b8eb20052ab5216107581168af574ac1f05dff8"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.0"
  flutter_svg:
    dependency: transitive
    description:
      name: flutter_svg
      sha256: "9ac1967e2f72a08af11b05b39167920f90d043cf67163d13a544a358c8f31afa"
      url: "https://pub.dev"
    source: hosted
    version: "0.22.0"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  fluttertoast:
    dependency: transitive
    description:
      name: fluttertoast
      sha256: "720d7ccbc908b2da776cfecd1d335daad99e7aab23c30e1d80df2a4461ba41c0"
      url: "https://pub.dev"
    source: hosted
    version: "8.0.7"
  get:
    dependency: "direct main"
    description:
      name: get
      sha256: "34cd0cfbbb76e3aa3f9d582b467a5d326235dd7a57276b6d8b5ead51cf51a256"
      url: "https://pub.dev"
    source: hosted
    version: "4.6.1"
  glob:
    dependency: transitive
    description:
      name: glob
      sha256: "8321dd2c0ab0683a91a51307fa844c6db4aa8e3981219b78961672aaab434658"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  global_configuration:
    dependency: transitive
    description:
      name: global_configuration
      sha256: "63fdee759d2bdde498804061bd44860b2025b27b5661c60a5fb39663df88261f"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0-nullsafety.1"
  google_fonts:
    dependency: transitive
    description:
      name: google_fonts
      sha256: "8729bcb3d4226758859855dd7b9124a49d6acb4d197f58a18a34597ca10056e8"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  hex:
    dependency: transitive
    description:
      name: hex
      sha256: "4e7cd54e4b59ba026432a6be2dd9d96e4c5205725194997193bf871703b82c4a"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0"
  http:
    dependency: transitive
    description:
      name: http
      sha256: "2ed163531e071c2c6b7c659635112f24cb64ecbebf6af46b550d536c0b1aa112"
      url: "https://pub.dev"
    source: hosted
    version: "0.13.4"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      sha256: e362d639ba3bc07d5a71faebb98cde68c05bfbcfbbb444b60b6f60bb67719185
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  intl:
    dependency: transitive
    description:
      name: intl
      sha256: "910f85bce16fb5c6f614e117efa303e85a1731bb0081edf3604a2ae6e9a3cc91"
      url: "https://pub.dev"
    source: hosted
    version: "0.17.0"
  js:
    dependency: transitive
    description:
      name: js
      sha256: "5528c2f391ededb7775ec1daa69e65a2d61276f7552de2b5f7b8d34ee9fd4ab7"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.5"
  json_annotation:
    dependency: transitive
    description:
      name: json_annotation
      sha256: "2639efc0237c7b71c6584696c0847ea4e4733ddaf571ae9c79d5295e8ae17272"
      url: "https://pub.dev"
    source: hosted
    version: "4.4.0"
  json_rpc_2:
    dependency: transitive
    description:
      name: json_rpc_2
      sha256: f03482ca7adb5f3a5f0854d90005e2523f93aa6f510741aabe2f026fb5a65ae8
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  lints:
    dependency: transitive
    description:
      name: lints
      sha256: a2c3d198cb5ea2e179926622d433331d8b58374ab8f29cdda6e863bd62fd369c
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  lock_to_win:
    dependency: "direct main"
    description:
      path: ".."
      relative: true
    source: path
    version: "0.0.1"
  logging:
    dependency: transitive
    description:
      name: logging
      sha256: "293ae2d49fd79d4c04944c3a26dfd313382d5f52e821ec57119230ae16031ad4"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  lottie:
    dependency: transitive
    description:
      name: lottie
      sha256: c24e6c9ec37e07810b6242e143d06f219a5c5d1fb8491c242a0d421812541704
      url: "https://pub.dev"
    source: hosted
    version: "1.2.2"
  matcher:
    dependency: transitive
    description:
      name: matcher
      sha256: "16db949ceee371e9b99d22f88fa3a73c4e59fd0afed0bd25fc336eb76c198b72"
      url: "https://pub.dev"
    source: hosted
    version: "0.12.13"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      sha256: d92141dc6fe1dad30722f9aa826c7fbc896d021d792f80678280601aff8cf724
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0"
  meta:
    dependency: transitive
    description:
      name: meta
      sha256: "6c268b42ed578a53088d834796959e4a1814b5e9e164f147f580a386e5decf42"
      url: "https://pub.dev"
    source: hosted
    version: "1.8.0"
  nested:
    dependency: transitive
    description:
      name: nested
      sha256: "03bac4c528c64c95c722ec99280375a6f2fc708eec17c7b3f07253b626cd2a20"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  package_config:
    dependency: transitive
    description:
      name: package_config
      sha256: a4d5ede5ca9c3d88a2fef1147a078570c861714c806485c596b109819135bc12
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  path:
    dependency: transitive
    description:
      name: path
      sha256: db9d4f58c908a4ba5953fcee2ae317c94889433e5024c27ce74a37f94267945b
      url: "https://pub.dev"
    source: hosted
    version: "1.8.2"
  path_drawing:
    dependency: transitive
    description:
      name: path_drawing
      sha256: "3bdd251dae9ffaef944450b73f168610db7e968e7b20daf0c3907f8b4aafc8a2"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.1+1"
  path_parsing:
    dependency: transitive
    description:
      name: path_parsing
      sha256: ee5c47c1058ad66b4a41746ec3996af9593d0858872807bcd64ac118f0700337
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1"
  path_provider:
    dependency: transitive
    description:
      name: path_provider
      sha256: e92dee4d38a9044605cb3fb253e9b46eb9375dfcad4515d0379b44ac90797568
      url: "https://pub.dev"
    source: hosted
    version: "2.0.9"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      sha256: "8b759fb6c74955931e87f550cc9e890b0cccb7ef8e710943973efeaa9695c54d"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.12"
  path_provider_ios:
    dependency: transitive
    description:
      name: path_provider_ios
      sha256: "943b76e54056386432cdc2731cb303e2f580346b61a1fc73819721767be72309"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.8"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      sha256: "1e109f4df28bd95eab71e323008b53d19c4d633bc1ab05b577518773474e9621"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.5"
  path_provider_macos:
    dependency: transitive
    description:
      name: path_provider_macos
      sha256: "0adeb313e1f2c3fc52baeeee59b0fe9c2d1f7da56fd96a9234e1702ec653a453"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.5"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      sha256: "3dc0d51b07f85fec3746d9f4e8d31c73bb173cafa2e763f03f8df2e8d1878882"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      sha256: "366ad4e3541ea707f859e7148d4d5aba67d589d7936cee04a05c464a277eeb27"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.5"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      sha256: "1a914995d4ef10c94ff183528c120d35ed43b5eaa8713fc6766a9be4570782e2"
      url: "https://pub.dev"
    source: hosted
    version: "4.4.0"
  pinenacl:
    dependency: transitive
    description:
      name: pinenacl
      sha256: ef7e8d8eaebdd3e44c13074ca394d730e9a20e453c3c67d01630efec19b67c47
      url: "https://pub.dev"
    source: hosted
    version: "0.3.3"
  platform:
    dependency: transitive
    description:
      name: platform
      sha256: "4a451831508d7d6ca779f7ac6e212b4023dd5a7d08a27a63da33756410e32b76"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      sha256: "075f927ebbab4262ace8d0b283929ac5410c0ac4e7fc123c76429564facfb757"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  pointycastle:
    dependency: transitive
    description:
      name: pointycastle
      sha256: d3031d8b58b62352176b13a734905ebc53d25498c7a5e161e7f3da581f2eb03f
      url: "https://pub.dev"
    source: hosted
    version: "3.5.2"
  process:
    dependency: transitive
    description:
      name: process
      sha256: "53fd8db9cec1d37b0574e12f07520d582019cb6c44abf5479a01505099a34a09"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.4"
  provider:
    dependency: transitive
    description:
      name: provider
      sha256: "7896193cf752c40ba7f7732a95264319a787871e5d628225357f5c909182bc06"
      url: "https://pub.dev"
    source: hosted
    version: "6.0.2"
  pub_semver:
    dependency: transitive
    description:
      name: pub_semver
      sha256: "816c1a640e952d213ddd223b3e7aafae08cd9f8e1f6864eed304cc13b0272b07"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  pubspec_parse:
    dependency: transitive
    description:
      name: pubspec_parse
      sha256: "3686efe4a4613a4449b1a4ae08670aadbd3376f2e78d93e3f8f0919db02a7256"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  shared_preferences:
    dependency: transitive
    description:
      name: shared_preferences
      sha256: "1cd0c3c0be0826eb52362ab018a81eed13b616ad9a52548c6ceb1bb349e6b6eb"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.13"
  shared_preferences_android:
    dependency: transitive
    description:
      name: shared_preferences_android
      sha256: bc236594233d10b7668dd90414fe0e09d906115aaa1dfe269e478e5f2af532a6
      url: "https://pub.dev"
    source: hosted
    version: "2.0.11"
  shared_preferences_ios:
    dependency: transitive
    description:
      name: shared_preferences_ios
      sha256: "69d593a80fee48b97c66787eb930cdd42941c1537e80a1ff88a8c12a926c47d4"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  shared_preferences_linux:
    dependency: transitive
    description:
      name: shared_preferences_linux
      sha256: ac361c65c4cf342dfc0a8b9e45eab66b9b3ad6eaff9785850d4ec0cf6b474422
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  shared_preferences_macos:
    dependency: transitive
    description:
      name: shared_preferences_macos
      sha256: f063907c3f678de8daa033d234b7c9e420df5fe3d499a97bfb82cc30cf171496
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  shared_preferences_platform_interface:
    dependency: transitive
    description:
      name: shared_preferences_platform_interface
      sha256: "992f0fdc46d0a3c0ac2e5859f2de0e577bbe51f78a77ee8f357cbe626a2ad32d"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  shared_preferences_web:
    dependency: transitive
    description:
      name: shared_preferences_web
      sha256: "09da0185028a227d51721cade7a3cbd5cc5f163a19593266f2acba87f729bf9c"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  shared_preferences_windows:
    dependency: transitive
    description:
      name: shared_preferences_windows
      sha256: ae68cf0df0910e38c95522dbd8a6082ce9715053c369750c5709d17de81d032e
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  simple_tooltip:
    dependency: transitive
    description:
      name: simple_tooltip
      sha256: "1817e07e073a7957067323d2fb2b9cc77c7c624e18fc494e19a7f3022baab6d3"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.99"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: dd904f795d4b4f3b870833847c461801f6750a9fa8e61ea5ac53f9422b31f250
      url: "https://pub.dev"
    source: hosted
    version: "1.9.1"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: c3c7d8edb15bee7f0f74debd4b9c5f3c2ea86766fe4178eb2a18eb30a0bdaed5
      url: "https://pub.dev"
    source: hosted
    version: "1.11.0"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: "83615bee9045c1d322bbbd1ba209b7a749c2cbcdcb3fdd1df8eb488b3279c1c8"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  stream_transform:
    dependency: transitive
    description:
      name: stream_transform
      sha256: ed464977cb26a1f41537e177e190c67223dbd9f4f683489b6ab2e5d211ec564e
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "556692adab6cfa87322a115640c11f13cb77b3f076ddcc5d6ae3c20242bedcde"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: a29248a84fbb7c79282b40b8c72a1209db169a2e0542bce341da992fe1bc7e84
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  test_api:
    dependency: transitive
    description:
      name: test_api
      sha256: ad540f65f92caa91bf21dfc8ffb8c589d6e4dc0c2267818b4cc2792857706206
      url: "https://pub.dev"
    source: hosted
    version: "0.4.16"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: "53bdf7e979cfbf3e28987552fd72f637e63f3c8724c9e56d9246942dc2fa36ee"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  uuid:
    dependency: transitive
    description:
      name: uuid
      sha256: "2469694ad079893e3b434a627970c33f2fa5adc46dfe03c9617546969a9a8afc"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.6"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      sha256: "80b3257d1492ce4d091729e3a67a60407d227c27241d6927be0130c98e741803"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  watcher:
    dependency: transitive
    description:
      name: watcher
      sha256: e42dfcc48f67618344da967b10f62de57e04bae01d9d3af4c2596f3712a88c99
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  web3dart:
    dependency: transitive
    description:
      name: web3dart
      sha256: "48b89a5fac0029770a18d1a8bd05ce8431722bacf76184e4301dae05781565e5"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.5"
  web_socket_channel:
    dependency: transitive
    description:
      name: web_socket_channel
      sha256: "0c2ada1b1aeb2ad031ca81872add6be049b8cb479262c6ad3c4b0f9c24eaab2f"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  win32:
    dependency: transitive
    description:
      name: win32
      sha256: "1709e736470cadbeefb717fb7936d014132d8a818de40f4be2f549a92ba50e82"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.1"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      sha256: "060b6e1c891d956f72b5ac9463466c37cce3fa962a921532fc001e86fe93438e"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0+1"
  xml:
    dependency: transitive
    description:
      name: xml
      sha256: baa23bcba1ba4ce4b22c0c7a1d9c861e7015cb5169512676da0b85138e72840c
      url: "https://pub.dev"
    source: hosted
    version: "5.3.1"
  yaml:
    dependency: transitive
    description:
      name: yaml
      sha256: "3cee79b1715110341012d27756d9bae38e650588acd38d3f3c610822e1337ace"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
sdks:
  dart: ">=2.18.0 <3.0.0"
  flutter: ">=2.8.0"
