name: lock_to_win
description: A new Flutter project.
version: 0.0.1
homepage:

environment:
  sdk: ">=3.4.3 <4.0.0"
  flutter: ">=2.5.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter
  animated: ^2.0.0
  animated_text_kit: ^4.2.1
  flutter_custom_clippers: ^2.0.0
  flutter_svg: ^1.1.6
  get: ^4.6.5
  intl: ^0.19.0
#  provider:
  lottie: ^1.2.1
#  google_fonts: 2.2.0
  animations: ^2.0.2
  simple_tooltip: ^1.0.0
  cloud_firestore:  ^5.6.3
  firebase_auth: ^5.4.2
  firebase_core: ^3.1.1
  web3dart: ^2.3.5
  path: ^1.8.0
  web_socket_channel: ^2.1.0
  global_configuration: ^2.0.0-nullsafety
  flutter_easyloading: ^3.0.3
  shared_preferences: ^2.0.13
  flutter_secure_storage: ^4.2.0
  bip39: ^1.0.6
  ed25519_hd_key: ^2.2.0
  hex: ^0.2.0
  bip32: ^2.0.0
  encrypt: ^5.0.1
  flutter_countdown_timer:
  flutter_spinkit: ^5.1.0
  fluttertoast: ^8.2.4
  likewallet:
    path: ../

dependency_overrides:
  intl: ^0.19.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^1.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  # This section identifies this Flutter project as a plugin project.
  # The 'pluginClass' and Android 'package' identifiers should not ordinarily
  # be modified. They are used by the tooling to maintain consistency when
  # adding or updating assets for this project.
  plugin:
    platforms:
      android:
        package: com.example.lock_to_win
        pluginClass: LockToWinPlugin
      ios:
        pluginClass: LockToWinPlugin
      web:
        pluginClass: LockToWinWeb
        fileName: lock_to_win_web.dart

  assets:
    - assets/svgs/
    - assets/images/
    - assets/animatedIcons/

  uses-material-design: true

  fonts:
    - family: Xlite
      fonts:
        - asset: assets/fonts/Xlite.ttf
    - family: ProximaNova
      fonts:
        - asset: assets/fonts/PROXIMANOVA-BLACK-WEBFONT.TTF
          weight: 900
        - asset: assets/fonts/PROXIMANOVA-BOLD-WEBFONT.TTF
          weight: 700
        - asset: assets/fonts/PROXIMANOVA-LIGHT-WEBFONT.TTF
          weight: 300
        - asset: assets/fonts/PROXIMANOVA-REG-WEBFONT.TTF
          weight: 400
        - asset: assets/fonts/PROXIMANOVA-SBOLD-WEBFONT.TTF
          weight: 600
        - asset: assets/fonts/PROXIMANOVA-THIN-WEBFONT.TTF
          weight: 100
        - asset: assets/fonts/PROXIMANOVA-XBOLD-WEBFONT.TTF
          weight: 800
    - family: IBMPlexSansThai-Regular
      fonts:
        - asset: assets/fonts/IBMPlexSansThai-Regular.ttf
    - family: IBMPlexSansThai-Medium
      fonts:
        - asset: assets/fonts/IBMPlexSansThai-Medium.ttf
    - family: DINPro-Medium
      fonts:
        - asset: assets/fonts/DINPro-Medium.ttf
    - family: DINPro-Bold
      fonts:
        - asset: assets/fonts/DINPro-Bold.ttf

