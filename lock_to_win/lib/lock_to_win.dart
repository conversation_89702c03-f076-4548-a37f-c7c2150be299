import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:lock_to_win/main.dart';

class LockToWin {
  static const MethodChannel _channel = MethodChannel('lock_to_win');

  static Future<String?> get platformVersion async {
    final String? version = await _channel.invokeMethod('getPlatformVersion');
    return version;
  }

  static void startLockToWin(BuildContext context,
      {required Map<String, Object> data}) async {
    await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => LockToWinMain(data)),
    );
  }
}
