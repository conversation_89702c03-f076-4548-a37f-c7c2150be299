/// Flutter icons MyFlutterApp
/// Copyright (C) 2019 by original authors @ fluttericon.com, fontello.com
/// This font was generated by FlutterIcon.com, which is derived from Fontello.
///
/// To use this font, place it in your fonts/ directory and include the
/// following in your pubspec.yaml
///
/// flutter:
///   fonts:
///    - family:  MyFlutterApp
///      fonts:
///       - asset: fonts/MyFlutterApp.ttf
///
///
/// * Font Awesome, Copyright (C) 2016 by <PERSON>
///         Author:    <PERSON>
///         License:   SIL ()
///         Homepage:  http://fortawesome.github.com/Font-Awesome/
///
import 'package:flutter/widgets.dart';
import 'package:global_configuration/global_configuration.dart';

class env {
  env._();

//  static const apiUrl = "http://192.168.86.248:6555";

//  static const OldAPI = "https://api.likepoint.io";
//  static const apiUrl = "http://192.168.86.70:6555";
  static String chainId = GlobalConfiguration().getValue("chainId");
  static String apiFee = GlobalConfiguration().getValue("apiFee");
  static String apiCheck = GlobalConfiguration().getValue("apiCheck");
  static String gasClaim = GlobalConfiguration().getValue("gasClaim");
  static String apiSumSub = "https://kyc.likewalletapp.com/user/";
//  static const apiCheck = "http://192.168.86.28:7001";

  //kovan
//  static const String rpcUrl = 'https://kovan.infura.io/v3/********************************';
//  static const String wsUrl = 'wss://kovan.infura.io/ws/v3/********************************';
//  static const String contractLock = '******************************************';
//  static const String contractLike = '******************************************';
//  static const String contractAirdrop = '******************************************';

//tomochain mainnet (test)
//  static const apiUrl = "https://newtest.likepoint.io";

//    static const apiUrl = "http://192.168.86.45:6555";
//
////
//
//

//  static const OldAPI = "https://api.likepoint.io";
//  static const String rpcUrl = 'https://rpc.tomochain.com';
//  static const String wsUrl = 'wss://ws.tomochain.com';
//  static const String contractLock = '******************************************';
//  static const String contractLike = '0x6360D4D723bFb1588880692A584F8C10fb4A076f';
//  static const String contractAirdrop = '0x4BD455e34697Ad4A1255b7E044eAc0E6FE0f4Ca7';
//  static const String contractSlotMachine = '0xE6221F0886C8Aa37b5DC6B3E5099DE29fd9ec6cf';
//
//  static const String contractMessage = '0xA56C2e00Ca1D05625E00Bdd4914598205fEB285f';

//  tomochain mainnet
  static String apiUrl = GlobalConfiguration().getValue("apiUrl");
  static String OldAPI = GlobalConfiguration().getValue("OldAPI");
  static String apiLikepointBCT =
      'https://jwt5vh9tqb.execute-api.ap-southeast-1.amazonaws.com';
  static String rpcUrl = GlobalConfiguration().getValue("rpcUrl");
  static String PortfolioUrl = GlobalConfiguration().getValue("PortfolioUrl");
  static String wsUrl = GlobalConfiguration().getValue("wsUrl");
  static String contractLock = GlobalConfiguration().getValue("contractLock");
  static String contractLike = GlobalConfiguration().getValue("contractLike");
  //new
  static String contractLotteryLock =
      GlobalConfiguration().getValue("contractLotteryLock");
  static String contractNFT = GlobalConfiguration().getValue("contractNFT");
  static String lottery = GlobalConfiguration().getValue("lottery");
  static String abi_contractLotteryLock =
      GlobalConfiguration().getValue("abi_contractLotteryLock");
  static String abi_contractNFT =
      GlobalConfiguration().getValue("abi_contractNFT");
  static String abi_lottery = GlobalConfiguration().getValue("abi_lottery");

  static String contractAirdrop =
      GlobalConfiguration().getValue("contractAirdrop");
  static String contractSlotMachine =
      GlobalConfiguration().getValue("contractSlotMachine");
  static String contractMessage =
      GlobalConfiguration().getValue("contractMessage");
  static String contractLoan = GlobalConfiguration().getValue("contractLoan");

  static String abiContractLike =
      GlobalConfiguration().getValue("abiContractLike");
  static String abiContractLock =
      GlobalConfiguration().getValue("abiContractLock");
  static String abiContractAirdrop =
      GlobalConfiguration().getValue("abiContractAirdrop");
  static String abiContractSlot =
      GlobalConfiguration().getValue("abiContractSlot");
  static String abiContractLoan =
      GlobalConfiguration().getValue("abiContractLoan");
  static String abiContractMessage =
      GlobalConfiguration().getValue("abiContractMessage");
  static String APIKEY = GlobalConfiguration().getValue("APIKEY");
  static String SECRETKEY = GlobalConfiguration().getValue("SECRETKEY");

  ///test
  ///
  ///
//  static const apiUrl = "https://newtest.likepoint.io";
//  static const OldAPI = "https://apitest.likepoint.io";
//  static const String rpcUrl = 'https://rpc.testnet.tomochain.com';
//  static const String PortfolioUrl = 'https://portfoliotest.likepoint.io';
//  static const String wsUrl = 'wss://ws.testnet.tomochain.com';
//  static const String contractLock =
//      '0x1aA01eE86e1337664c00F27A43Ab80722fDcbFF4';
//  static const String contractLike =
//      '0xa8F4c297413a4Cc98AA03e1795d3d4ef122b5373';
//  static const String contractAirdrop =
//      '0xFf9A1f68f1304Eee02A7dc7e4d1c78E10eDAe378';
//  static const String contractSlotMachine =
//      '0xE6221F0886C8Aa37b5DC6B3E5099DE29fd9ec6cf';
//
//  static const String contractLoan =
//      '0xd2560E5bEB8d2a7D9175f549bc7Ab12c9DAF13b9';
//  static const String contractMessage =
//      '0xc3B95E0558a48E7afD596f8e389Ed89C43664435';
//
//  static const abiContractLike =
//      '[ { "constant": false, "inputs": [ { "name": "_amount", "type": "uint256" } ], "name": "addMint", "outputs": [ { "name": "", "type": "bool" } ], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": false, "inputs": [ { "name": "_spender", "type": "address" }, { "name": "_value", "type": "uint256" } ], "name": "approve", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": false, "inputs": [ { "name": "_amount", "type": "uint256" } ], "name": "burnToken", "outputs": [ { "name": "", "type": "bool" } ], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": false, "inputs": [], "name": "finishMinting", "outputs": [ { "name": "", "type": "bool" } ], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": false, "inputs": [ { "name": "_to", "type": "address" }, { "name": "_amount", "type": "uint256" } ], "name": "mint", "outputs": [ { "name": "", "type": "bool" } ], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": false, "inputs": [ { "name": "_to", "type": "address" }, { "name": "_amount", "type": "uint256" }, { "name": "_releaseTime", "type": "uint256" } ], "name": "mintTimelocked", "outputs": [ { "name": "", "type": "address" } ], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": false, "inputs": [], "name": "pause", "outputs": [ { "name": "", "type": "bool" } ], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": false, "inputs": [ { "name": "_to", "type": "address" }, { "name": "_value", "type": "uint256" } ], "name": "transfer", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": false, "inputs": [ { "name": "_from", "type": "address" }, { "name": "_to", "type": "address" }, { "name": "_value", "type": "uint256" } ], "name": "transferFrom", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": false, "inputs": [ { "name": "newOwner", "type": "address" } ], "name": "transferOwnership", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": false, "inputs": [], "name": "unpause", "outputs": [ { "name": "", "type": "bool" } ], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "anonymous": false, "inputs": [ { "indexed": true, "name": "to", "type": "address" }, { "indexed": false, "name": "value", "type": "uint256" } ], "name": "Mint", "type": "event" }, { "anonymous": false, "inputs": [ { "indexed": true, "name": "to", "type": "address" }, { "indexed": false, "name": "value", "type": "uint256" } ], "name": "MintMore", "type": "event" }, { "anonymous": false, "inputs": [ { "indexed": true, "name": "to", "type": "address" }, { "indexed": false, "name": "value", "type": "uint256" } ], "name": "BurnToken", "type": "event" }, { "anonymous": false, "inputs": [], "name": "MintFinished", "type": "event" }, { "anonymous": false, "inputs": [], "name": "Pause", "type": "event" }, { "anonymous": false, "inputs": [], "name": "Unpause", "type": "event" }, { "anonymous": false, "inputs": [ { "indexed": true, "name": "owner", "type": "address" }, { "indexed": true, "name": "spender", "type": "address" }, { "indexed": false, "name": "value", "type": "uint256" } ], "name": "Approval", "type": "event" }, { "anonymous": false, "inputs": [ { "indexed": true, "name": "from", "type": "address" }, { "indexed": true, "name": "to", "type": "address" }, { "indexed": false, "name": "value", "type": "uint256" } ], "name": "Transfer", "type": "event" }, { "constant": true, "inputs": [ { "name": "_owner", "type": "address" }, { "name": "_spender", "type": "address" } ], "name": "allowance", "outputs": [ { "name": "remaining", "type": "uint256" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [ { "name": "_owner", "type": "address" } ], "name": "balanceOf", "outputs": [ { "name": "balance", "type": "uint256" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [], "name": "decimals", "outputs": [ { "name": "", "type": "uint256" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [], "name": "mintingFinished", "outputs": [ { "name": "", "type": "bool" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [], "name": "name", "outputs": [ { "name": "", "type": "string" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [], "name": "owner", "outputs": [ { "name": "", "type": "address" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [], "name": "paused", "outputs": [ { "name": "", "type": "bool" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [], "name": "symbol", "outputs": [ { "name": "", "type": "string" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [], "name": "totalSupply", "outputs": [ { "name": "", "type": "uint256" } ], "payable": false, "stateMutability": "view", "type": "function" } ]';
//  static const abiContractLock =
//      '[ { "constant": true, "inputs": [ { "name": "", "type": "address" } ], "name": "balanceToken", "outputs": [ { "name": "", "type": "uint256" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [ { "name": "likeAddr", "type": "address" }, { "name": "_sender", "type": "address" } ], "name": "getAmountAll", "outputs": [ { "name": "", "type": "uint256" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": false, "inputs": [ { "name": "likeAddr", "type": "address" }, { "name": "borrower", "type": "address" }, { "name": "amount", "type": "uint256" }, { "name": "canTransfer", "type": "uint256" } ], "name": "transferDepositFund", "outputs": [ { "name": "", "type": "bool" } ], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": false, "inputs": [ { "name": "likeAddr", "type": "address" }, { "name": "borrower", "type": "address" } ], "name": "adminClearBlacklist", "outputs": [ { "name": "", "type": "bool" } ], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": false, "inputs": [ { "name": "likeAddr", "type": "address" }, { "name": "recev", "type": "address" }, { "name": "amount", "type": "uint256" }, { "name": "expire", "type": "uint256" } ], "name": "depositTokenByAdmin", "outputs": [ { "name": "", "type": "bool" } ], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": false, "inputs": [ { "name": "newOwner", "type": "address" }, { "name": "number", "type": "uint256" } ], "name": "updateLenderContract", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": false, "inputs": [ { "name": "expire", "type": "uint256" } ], "name": "setExpire", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": false, "inputs": [ { "name": "likeAddr", "type": "address" }, { "name": "amount", "type": "uint256" } ], "name": "requestWithdraw", "outputs": [ { "name": "", "type": "bool" } ], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": true, "inputs": [ { "name": "likeAddr", "type": "address" }, { "name": "_sender", "type": "address" } ], "name": "getLock", "outputs": [ { "name": "", "type": "uint256" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": false, "inputs": [ { "name": "likeAddr", "type": "address" }, { "name": "holder", "type": "address" }, { "name": "amount", "type": "uint256" }, { "name": "lockto", "type": "uint256" } ], "name": "depositTimeLock", "outputs": [ { "name": "", "type": "bool" } ], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": true, "inputs": [], "name": "waitNewOwner", "outputs": [ { "name": "", "type": "address" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [ { "name": "", "type": "address" }, { "name": "", "type": "address" } ], "name": "tokens", "outputs": [ { "name": "token", "type": "address" }, { "name": "expire", "type": "uint256" }, { "name": "block", "type": "uint256" }, { "name": "start", "type": "uint256" }, { "name": "amount", "type": "uint256" }, { "name": "amountUnlock", "type": "uint256" }, { "name": "isTransfer", "type": "uint8" }, { "name": "isWithdraw", "type": "uint8" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": false, "inputs": [ { "name": "lenderAddress", "type": "address" } ], "name": "addLenderContract", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": true, "inputs": [ { "name": "likeAddr", "type": "address" }, { "name": "_sender", "type": "address" } ], "name": "getDepositTime", "outputs": [ { "name": "", "type": "uint256" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [], "name": "totalLenderContract", "outputs": [ { "name": "", "type": "uint256" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [ { "name": "likeAddr", "type": "address" }, { "name": "_sender", "type": "address" } ], "name": "getAmount", "outputs": [ { "name": "", "type": "uint256" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": false, "inputs": [], "name": "acceptOwnership", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": true, "inputs": [ { "name": "likeAddr", "type": "address" }, { "name": "_sender", "type": "address" } ], "name": "getTransfer", "outputs": [ { "name": "", "type": "uint8" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": false, "inputs": [ { "name": "likeAddr", "type": "address" }, { "name": "borrower", "type": "address" }, { "name": "amount", "type": "uint256" } ], "name": "transferWithdrawFund", "outputs": [ { "name": "", "type": "bool" } ], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": false, "inputs": [ { "name": "likeAddr", "type": "address" } ], "name": "withdrawToken", "outputs": [ { "name": "", "type": "bool" } ], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": false, "inputs": [ { "name": "likeAddr", "type": "address" }, { "name": "amount", "type": "uint256" }, { "name": "message", "type": "string" } ], "name": "adminWithdrawToken", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": true, "inputs": [], "name": "owner", "outputs": [ { "name": "", "type": "address" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": false, "inputs": [ { "name": "checker", "type": "address" } ], "name": "checkAdmin", "outputs": [ { "name": "", "type": "bool" } ], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": true, "inputs": [ { "name": "", "type": "address" } ], "name": "balanceLending", "outputs": [ { "name": "", "type": "uint256" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [ { "name": "likeAddr", "type": "address" }, { "name": "_sender", "type": "address" } ], "name": "getWithdraw", "outputs": [ { "name": "", "type": "uint8" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": false, "inputs": [ { "name": "likeAddr", "type": "address" }, { "name": "amount", "type": "uint256" }, { "name": "expire", "type": "uint256" } ], "name": "depositToken", "outputs": [ { "name": "", "type": "bool" } ], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": true, "inputs": [ { "name": "", "type": "address" }, { "name": "", "type": "address" } ], "name": "timelockhold", "outputs": [ { "name": "token", "type": "address" }, { "name": "expire", "type": "uint256" }, { "name": "block", "type": "uint256" }, { "name": "start", "type": "uint256" }, { "name": "amount", "type": "uint256" }, { "name": "isWithdraw", "type": "uint8" }, { "name": "round", "type": "uint256" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [ { "name": "", "type": "uint256" } ], "name": "ownerLender", "outputs": [ { "name": "", "type": "address" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [], "name": "listLenderContract", "outputs": [ { "name": "", "type": "address[]" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [ { "name": "", "type": "uint256" } ], "name": "reserveLender", "outputs": [ { "name": "", "type": "address" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [ { "name": "likeAddr", "type": "address" } ], "name": "getActiveLock", "outputs": [ { "name": "", "type": "uint256" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [ { "name": "", "type": "address" } ], "name": "activeLock", "outputs": [ { "name": "", "type": "uint256" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": false, "inputs": [ { "name": "_address", "type": "address" } ], "name": "removeLenderContract", "outputs": [ { "name": "", "type": "address[]" } ], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": true, "inputs": [], "name": "isExpire", "outputs": [ { "name": "", "type": "uint256" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": false, "inputs": [ { "name": "likeAddr", "type": "address" }, { "name": "borrower", "type": "address" }, { "name": "collateral", "type": "uint256" }, { "name": "liquidate", "type": "uint256" }, { "name": "liquidator", "type": "address" }, { "name": "dept", "type": "uint256" } ], "name": "liquidateCollateral", "outputs": [ { "name": "", "type": "bool" } ], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": false, "inputs": [ { "name": "likeAddr", "type": "address" }, { "name": "amount", "type": "uint256" } ], "name": "withdrawTimeLock", "outputs": [ { "name": "", "type": "bool" } ], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": false, "inputs": [ { "name": "newOwner", "type": "address" } ], "name": "transferOwnership", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "anonymous": false, "inputs": [ { "indexed": false, "name": "contractAddress", "type": "address" }, { "indexed": false, "name": "sender", "type": "address" }, { "indexed": false, "name": "amount", "type": "uint256" } ], "name": "TransferWithdrawFund", "type": "event" }, { "anonymous": false, "inputs": [ { "indexed": false, "name": "contractAddress", "type": "address" }, { "indexed": false, "name": "sender", "type": "address" }, { "indexed": false, "name": "amount", "type": "uint256" } ], "name": "TransferDepositFund", "type": "event" }, { "anonymous": false, "inputs": [ { "indexed": false, "name": "likeAddr", "type": "address" }, { "indexed": false, "name": "borrower", "type": "address" }, { "indexed": false, "name": "fullcollateral", "type": "uint256" }, { "indexed": false, "name": "collateral", "type": "uint256" }, { "indexed": false, "name": "liquidator", "type": "address" }, { "indexed": false, "name": "dept", "type": "uint256" } ], "name": "LiquidateCollateral", "type": "event" }, { "anonymous": false, "inputs": [ { "indexed": false, "name": "contractAddress", "type": "address" }, { "indexed": false, "name": "sender", "type": "address" }, { "indexed": false, "name": "amount", "type": "uint256" }, { "indexed": false, "name": "expire", "type": "uint256" } ], "name": "DepositToken", "type": "event" }, { "anonymous": false, "inputs": [ { "indexed": false, "name": "contractAddress", "type": "address" }, { "indexed": false, "name": "sender", "type": "address" }, { "indexed": false, "name": "amount", "type": "uint256" } ], "name": "WithdrawTokenTimeLock", "type": "event" }, { "anonymous": false, "inputs": [ { "indexed": false, "name": "contractAddress", "type": "address" }, { "indexed": false, "name": "sender", "type": "address" }, { "indexed": false, "name": "amount", "type": "uint256" }, { "indexed": false, "name": "amountexists", "type": "uint256" } ], "name": "WithdrawToken", "type": "event" }, { "anonymous": false, "inputs": [ { "indexed": false, "name": "contractAddress", "type": "address" }, { "indexed": false, "name": "sender", "type": "address" }, { "indexed": false, "name": "amount", "type": "uint256" }, { "indexed": false, "name": "message", "type": "string" } ], "name": "AdminWithdrawToken", "type": "event" }, { "anonymous": false, "inputs": [ { "indexed": false, "name": "contractAddress", "type": "address" }, { "indexed": false, "name": "sender", "type": "address" } ], "name": "RequestWithdraw", "type": "event" }, { "anonymous": false, "inputs": [ { "indexed": false, "name": "newLenderOwner", "type": "address" } ], "name": "transferLenderOwner", "type": "event" }, { "anonymous": false, "inputs": [ { "indexed": false, "name": "newOwner", "type": "address" } ], "name": "transferOwner", "type": "event" } ]';
//  static const abiContractAirdrop =
//      '[ { "constant": true, "inputs": [ { "name": "", "type": "address" } ], "name": "balanceToken", "outputs": [ { "name": "", "type": "uint256" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [ { "name": "", "type": "address" }, { "name": "", "type": "address" } ], "name": "Claim", "outputs": [ { "name": "lastTime", "type": "uint256" }, { "name": "round", "type": "uint256" }, { "name": "nextTime", "type": "uint256" }, { "name": "amount", "type": "uint256" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [ { "name": "", "type": "address" } ], "name": "Round", "outputs": [ { "name": "", "type": "uint256" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [ { "name": "_addr", "type": "address" }, { "name": "_lock", "type": "address" }, { "name": "_checker", "type": "address" } ], "name": "checkRewards", "outputs": [ { "name": "", "type": "uint256" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [ { "name": "", "type": "address" }, { "name": "", "type": "uint256" } ], "name": "balanceOfRound", "outputs": [ { "name": "", "type": "uint256" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": false, "inputs": [ { "name": "_addr", "type": "address" }, { "name": "_round", "type": "uint256" } ], "name": "adminWithdrawByRound", "outputs": [ { "name": "", "type": "bool" } ], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": true, "inputs": [ { "name": "", "type": "address" }, { "name": "", "type": "uint256" } ], "name": "TotalRewards", "outputs": [ { "name": "", "type": "uint256" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [], "name": "waitNewOwner", "outputs": [ { "name": "", "type": "address" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [ { "name": "", "type": "address" }, { "name": "", "type": "uint256" } ], "name": "TotalLock", "outputs": [ { "name": "", "type": "uint256" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": false, "inputs": [ { "name": "_addr", "type": "address" }, { "name": "_lock", "type": "address" } ], "name": "getRewards", "outputs": [ { "name": "", "type": "bool" } ], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": false, "inputs": [], "name": "acceptOwnership", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": true, "inputs": [], "name": "owner", "outputs": [ { "name": "", "type": "address" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": false, "inputs": [ { "name": "newOracle", "type": "address" } ], "name": "transferOracleAddress", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": false, "inputs": [ { "name": "_addr", "type": "address" }, { "name": "lock", "type": "address" }, { "name": "rewards", "type": "uint256" }, { "name": "_expire", "type": "uint256" } ], "name": "updateRewards", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": false, "inputs": [ { "name": "_addr", "type": "address" } ], "name": "adminWithdraw", "outputs": [ { "name": "", "type": "bool" } ], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": true, "inputs": [], "name": "oracleAddress", "outputs": [ { "name": "", "type": "address" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": false, "inputs": [ { "name": "newOwner", "type": "address" } ], "name": "transferOwnership", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": true, "inputs": [ { "name": "", "type": "address" }, { "name": "", "type": "uint256" } ], "name": "CheckExpire", "outputs": [ { "name": "status", "type": "uint8" }, { "name": "expire", "type": "uint256" }, { "name": "start", "type": "uint256" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "inputs": [ { "name": "oracle", "type": "address" } ], "payable": false, "stateMutability": "nonpayable", "type": "constructor" }, { "anonymous": false, "inputs": [ { "indexed": false, "name": "_addr", "type": "address" }, { "indexed": false, "name": "_lock", "type": "address" }, { "indexed": false, "name": "round", "type": "uint256" }, { "indexed": false, "name": "sender", "type": "address" }, { "indexed": false, "name": "reward", "type": "uint256" } ], "name": "GetRewards", "type": "event" }, { "anonymous": false, "inputs": [ { "indexed": false, "name": "_addr", "type": "address" }, { "indexed": false, "name": "lock", "type": "address" }, { "indexed": false, "name": "rewards", "type": "uint256" }, { "indexed": false, "name": "round", "type": "uint256" }, { "indexed": false, "name": "expire", "type": "uint256" } ], "name": "UpdateRewards", "type": "event" }, { "anonymous": false, "inputs": [ { "indexed": false, "name": "_addr", "type": "address" }, { "indexed": false, "name": "_round", "type": "uint256" }, { "indexed": false, "name": "balance", "type": "uint256" } ], "name": "AdminWithdrawByRound", "type": "event" }, { "anonymous": false, "inputs": [ { "indexed": false, "name": "_addr", "type": "address" }, { "indexed": false, "name": "balance", "type": "uint256" } ], "name": "AdminWithdraw", "type": "event" }, { "anonymous": false, "inputs": [ { "indexed": false, "name": "newOwner", "type": "address" } ], "name": "transferOwner", "type": "event" } ]';
//  static const abiContractSlot =
//      '[ { "constant": false, "inputs": [], "name": "acceptOwnership", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": false, "inputs": [ { "name": "likeAddr", "type": "address" }, { "name": "amount", "type": "uint256" }, { "name": "message", "type": "string" } ], "name": "adminDepositToken", "outputs": [ { "name": "", "type": "bool" } ], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": false, "inputs": [ { "name": "likeAddr", "type": "address" }, { "name": "amount", "type": "uint256" }, { "name": "message", "type": "string" } ], "name": "adminWithdrawToken", "outputs": [ { "name": "", "type": "bool" } ], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": false, "inputs": [ { "name": "likeAddr", "type": "address" }, { "name": "amount", "type": "uint256" }, { "name": "message", "type": "string" } ], "name": "buyTicket", "outputs": [ { "name": "", "type": "bool" } ], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": false, "inputs": [ { "name": "likeAddr", "type": "address" }, { "name": "newPrice", "type": "uint256" } ], "name": "changePrice", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": false, "inputs": [ { "name": "likeAddr", "type": "address" }, { "name": "_prize", "type": "uint256" } ], "name": "changePrize", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": false, "inputs": [ { "name": "likeAddr", "type": "address" }, { "name": "_lucker", "type": "address" }, { "name": "message", "type": "string" } ], "name": "claimReward", "outputs": [ { "name": "", "type": "bool" } ], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": false, "inputs": [ { "name": "likeAddr", "type": "address" }, { "name": "_ticket", "type": "uint256" }, { "name": "message", "type": "string" } ], "name": "subTicket", "outputs": [ { "name": "", "type": "bool" } ], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": false, "inputs": [ { "name": "newOracle", "type": "address" } ], "name": "transferOracleAddress", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": false, "inputs": [ { "name": "newOwner", "type": "address" } ], "name": "transferOwnership", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": false, "inputs": [ { "name": "likeAddr", "type": "address" }, { "name": "_address", "type": "address" }, { "name": "_ticket", "type": "uint256" } ], "name": "transferTicket", "outputs": [ { "name": "", "type": "bool" } ], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "inputs": [ { "name": "likeAddr", "type": "address" }, { "name": "oracle", "type": "address" }, { "name": "price", "type": "uint256" }, { "name": "_prize", "type": "uint256" } ], "payable": false, "stateMutability": "nonpayable", "type": "constructor" }, { "anonymous": false, "inputs": [ { "indexed": false, "name": "contractAddress", "type": "address" }, { "indexed": false, "name": "sender", "type": "address" }, { "indexed": false, "name": "amount", "type": "uint256" }, { "indexed": false, "name": "message", "type": "string" } ], "name": "AdminDepositToken", "type": "event" }, { "anonymous": false, "inputs": [ { "indexed": false, "name": "contractAddress", "type": "address" }, { "indexed": false, "name": "sender", "type": "address" }, { "indexed": false, "name": "amount", "type": "uint256" }, { "indexed": false, "name": "message", "type": "string" } ], "name": "AdminWithdrawToken", "type": "event" }, { "anonymous": false, "inputs": [ { "indexed": false, "name": "contractAddress", "type": "address" }, { "indexed": false, "name": "_buyer", "type": "address" }, { "indexed": false, "name": "amount", "type": "uint256" }, { "indexed": false, "name": "message", "type": "string" } ], "name": "BuyTicket", "type": "event" }, { "anonymous": false, "inputs": [ { "indexed": false, "name": "contractAddress", "type": "address" }, { "indexed": false, "name": "_buyer", "type": "address" }, { "indexed": false, "name": "ticket", "type": "uint256" }, { "indexed": false, "name": "message", "type": "string" } ], "name": "SubTicket", "type": "event" }, { "anonymous": false, "inputs": [ { "indexed": false, "name": "contractAddress", "type": "address" }, { "indexed": false, "name": "_owner", "type": "address" }, { "indexed": false, "name": "newPrice", "type": "uint256" } ], "name": "ChangePrice", "type": "event" }, { "anonymous": false, "inputs": [ { "indexed": false, "name": "contractAddress", "type": "address" }, { "indexed": false, "name": "_owner", "type": "address" }, { "indexed": false, "name": "_prize", "type": "uint256" } ], "name": "ChangePrize", "type": "event" }, { "anonymous": false, "inputs": [ { "indexed": false, "name": "contractAddress", "type": "address" }, { "indexed": false, "name": "_sender", "type": "address" }, { "indexed": false, "name": "_receiver", "type": "address" }, { "indexed": false, "name": "ticket", "type": "uint256" } ], "name": "TransferTicket", "type": "event" }, { "anonymous": false, "inputs": [ { "indexed": false, "name": "contractAddress", "type": "address" }, { "indexed": false, "name": "_lucker", "type": "address" }, { "indexed": false, "name": "amount", "type": "uint256" }, { "indexed": false, "name": "message", "type": "string" } ], "name": "ClaimReward", "type": "event" }, { "anonymous": false, "inputs": [ { "indexed": false, "name": "newOwner", "type": "address" } ], "name": "transferOwner", "type": "event" }, { "constant": true, "inputs": [ { "name": "", "type": "address" } ], "name": "balanceToken", "outputs": [ { "name": "", "type": "uint256" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [], "name": "oracleAddress", "outputs": [ { "name": "", "type": "address" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [], "name": "owner", "outputs": [ { "name": "", "type": "address" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [ { "name": "", "type": "address" } ], "name": "pricePerticket", "outputs": [ { "name": "", "type": "uint256" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [ { "name": "", "type": "address" } ], "name": "prize", "outputs": [ { "name": "", "type": "uint256" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [ { "name": "", "type": "address" }, { "name": "", "type": "address" } ], "name": "ticket", "outputs": [ { "name": "", "type": "uint256" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [], "name": "waitNewOwner", "outputs": [ { "name": "", "type": "address" } ], "payable": false, "stateMutability": "view", "type": "function" } ]';
//
//  static const abiContractMessage =
//      '[ { "constant": false, "inputs": [ { "name": "trc20", "type": "address" }, { "name": "_to", "type": "address" }, { "name": "_value", "type": "uint256" }, { "name": "message", "type": "string" } ], "name": "transferMessage", "outputs": [ { "name": "", "type": "bool" } ], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "anonymous": false, "inputs": [ { "indexed": false, "name": "trc20", "type": "address" }, { "indexed": false, "name": "_from", "type": "address" }, { "indexed": false, "name": "_to", "type": "address" }, { "indexed": false, "name": "_value", "type": "uint256" }, { "indexed": false, "name": "message", "type": "string" } ], "name": "TransferMessage", "type": "event" } ]';
//
//  static const abiContractLoan =
//      '[ { "constant": false, "inputs": [ { "name": "_borrower", "type": "address" } ], "name": "checkLiquidate", "outputs": [ { "name": "", "type": "bool" } ], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": true, "inputs": [], "name": "roundInterest", "outputs": [ { "name": "", "type": "uint256" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": false, "inputs": [ { "name": "_like", "type": "address" }, { "name": "_lock", "type": "address" } ], "name": "rePayAll", "outputs": [ { "name": "", "type": "bool" } ], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": false, "inputs": [ { "name": "_date", "type": "uint256" } ], "name": "setEndDate", "outputs": [ { "name": "", "type": "bool" } ], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": false, "inputs": [ { "name": "_like", "type": "address" }, { "name": "_lock", "type": "address" }, { "name": "amount", "type": "uint256" } ], "name": "rePay", "outputs": [ { "name": "", "type": "bool" } ], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": true, "inputs": [ { "name": "", "type": "address" } ], "name": "contractLoans", "outputs": [ { "name": "borrower", "type": "address" }, { "name": "startTime", "type": "uint256" }, { "name": "interest", "type": "uint256" }, { "name": "endTime", "type": "uint256" }, { "name": "dept", "type": "uint256" }, { "name": "amountInterest", "type": "uint256" }, { "name": "principal", "type": "uint256" }, { "name": "collateral", "type": "uint256" }, { "name": "liquidateCollateral", "type": "uint256" }, { "name": "repayPrincipal", "type": "uint256" }, { "name": "repayInterest", "type": "uint256" }, { "name": "isActive", "type": "uint8" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": false, "inputs": [ { "name": "_like", "type": "address" }, { "name": "spender", "type": "address" } ], "name": "removeApproval", "outputs": [ { "name": "", "type": "bool" } ], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": true, "inputs": [], "name": "waitNewOwner", "outputs": [ { "name": "", "type": "address" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [], "name": "totalIncome", "outputs": [ { "name": "", "type": "uint256" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [ { "name": "", "type": "uint256" } ], "name": "user", "outputs": [ { "name": "", "type": "address" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [], "name": "limitLoan", "outputs": [ { "name": "", "type": "uint256" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": false, "inputs": [ { "name": "_interest", "type": "uint256" } ], "name": "changeInterest", "outputs": [ { "name": "", "type": "bool" } ], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": true, "inputs": [ { "name": "", "type": "uint256" } ], "name": "userReserve", "outputs": [ { "name": "", "type": "address" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [], "name": "InterestorKeep", "outputs": [ { "name": "", "type": "address" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": false, "inputs": [], "name": "acceptOwnership", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": false, "inputs": [ { "name": "_like", "type": "address" }, { "name": "_lock", "type": "address" }, { "name": "_borrower", "type": "address" } ], "name": "liquidateLoan", "outputs": [ { "name": "", "type": "bool" } ], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": true, "inputs": [], "name": "owner", "outputs": [ { "name": "", "type": "address" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": false, "inputs": [ { "name": "inter", "type": "address" } ], "name": "setInterestor", "outputs": [ { "name": "", "type": "bool" } ], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": false, "inputs": [ { "name": "_like", "type": "address" }, { "name": "spender", "type": "address" }, { "name": "value", "type": "uint256" } ], "name": "approvalContract", "outputs": [ { "name": "", "type": "bool" } ], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": true, "inputs": [ { "name": "", "type": "uint256" } ], "name": "interestHistory", "outputs": [ { "name": "interest", "type": "uint256" }, { "name": "round", "type": "uint256" }, { "name": "timestamp", "type": "uint256" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [], "name": "getTotalUser", "outputs": [ { "name": "", "type": "uint256" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [ { "name": "_like", "type": "address" }, { "name": "_lock", "type": "address" } ], "name": "maximumBorrow", "outputs": [ { "name": "", "type": "uint256" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [], "name": "balance", "outputs": [ { "name": "", "type": "uint256" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [ { "name": "_borrower", "type": "address" } ], "name": "getCurrentDept", "outputs": [ { "components": [ { "name": "borrower", "type": "address" }, { "name": "startTime", "type": "uint256" }, { "name": "interest", "type": "uint256" }, { "name": "endTime", "type": "uint256" }, { "name": "dept", "type": "uint256" }, { "name": "amountInterest", "type": "uint256" }, { "name": "principal", "type": "uint256" }, { "name": "collateral", "type": "uint256" }, { "name": "liquidateCollateral", "type": "uint256" }, { "name": "repayPrincipal", "type": "uint256" }, { "name": "repayInterest", "type": "uint256" }, { "name": "isActive", "type": "uint8" } ], "name": "", "type": "tuple" }, { "name": "", "type": "uint256" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [], "name": "endDate", "outputs": [ { "name": "", "type": "uint256" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [], "name": "interest", "outputs": [ { "name": "", "type": "uint256" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": false, "inputs": [ { "name": "_like", "type": "address" }, { "name": "_lock", "type": "address" }, { "name": "borrowAmount", "type": "uint256" } ], "name": "initLoan", "outputs": [ { "name": "", "type": "bool" } ], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": true, "inputs": [], "name": "getAPY", "outputs": [ { "name": "", "type": "uint256" } ], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": false, "inputs": [ { "name": "newOwner", "type": "address" } ], "name": "transferOwnership", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "inputs": [], "payable": false, "stateMutability": "nonpayable", "type": "constructor" }, { "anonymous": false, "inputs": [ { "indexed": false, "name": "borrower", "type": "address" }, { "indexed": false, "name": "_like", "type": "address" }, { "indexed": false, "name": "_lock", "type": "address" }, { "indexed": false, "name": "amount", "type": "uint256" }, { "indexed": false, "name": "dept", "type": "uint256" } ], "name": "RePay", "type": "event" }, { "anonymous": false, "inputs": [ { "indexed": false, "name": "interestor", "type": "address" } ], "name": "changeInterestor", "type": "event" }, { "anonymous": false, "inputs": [ { "indexed": false, "name": "rate", "type": "uint256" } ], "name": "changeInterestRate", "type": "event" }, { "anonymous": false, "inputs": [ { "indexed": false, "name": "borrower", "type": "address" }, { "indexed": false, "name": "_like", "type": "address" }, { "indexed": false, "name": "_lock", "type": "address" }, { "indexed": false, "name": "amount", "type": "uint256" } ], "name": "InitLoan", "type": "event" }, { "anonymous": false, "inputs": [ { "indexed": false, "name": "likeAddr", "type": "address" }, { "indexed": false, "name": "borrower", "type": "address" }, { "indexed": false, "name": "collateral", "type": "uint256" }, { "indexed": false, "name": "liquidator", "type": "address" }, { "indexed": false, "name": "dept", "type": "uint256" } ], "name": "LiquidateCollateral", "type": "event" }, { "anonymous": false, "inputs": [ { "indexed": false, "name": "endDate", "type": "uint256" } ], "name": "changeEndDate", "type": "event" }, { "anonymous": false, "inputs": [ { "indexed": false, "name": "newOwner", "type": "address" } ], "name": "transferOwner", "type": "event" } ]';
//  static const APIKEY = '61da7951-8ffb-494d-98b2-37994652bfbc';
//  static const SECRETKEY = 'b6a45dc2-4467-45be-b1e1-0611395e63cf';
}

//  static const apiUrl = "http://**************:6555";
