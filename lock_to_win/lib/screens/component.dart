import 'dart:ffi';
import 'dart:ui';

import 'package:animated/animated.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_countdown_timer/countdown_timer_controller.dart';
import 'package:flutter_countdown_timer/current_remaining_time.dart';
import 'package:flutter_countdown_timer/flutter_countdown_timer.dart';
import 'package:flutter_custom_clippers/flutter_custom_clippers.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:lock_to_win/config/color_system.dart';
import 'package:lock_to_win/config/dark_mode_colors.dart';
import 'package:lock_to_win/config/light_mode_colors.dart';
import 'package:lock_to_win/controller/lottery.dart';
import 'package:lock_to_win/controller/test.dart';
import 'package:lock_to_win/main.dart';
import 'package:lock_to_win/models/ballon2.dart';
import 'package:lock_to_win/models/lotto/lotto_now_round.dart';
import 'package:lock_to_win/models/lotto/lotto_round.dart';
import 'package:lock_to_win/widgets/ballon.dart';
import 'package:likewallet/libraryman/app_local.dart';
import '../models/lotto/your_lotto_list_pre_round.dart';

class Component {
  static Widget preRound(_future, LotteryController controller) {
    return FutureBuilder(
      future: _future,
      builder: (
        BuildContext context,
        AsyncSnapshot<List> snapshot,
      ) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Column(
            children: [
              Row(
                children: [
                  Text(
                    AppLocalizations.of(context)!.translate('ltw_round'),
                    style: TextStyle(
                      fontSize: 14,
                      fontFamily: "IBMPlexSansThai-Regular",
                      letterSpacing: 0.3,
                      color: isDarkMode ? textGray400Dark : textGray400Light,
                    ),
                  ),
                  Container(
                    height: 50,
                    child: SpinKitThreeBounce(
                      color: Colors.grey,
                      size: 14,
                    ),
                  ),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    AppLocalizations.of(context)!.translate('ltw_total_prize'),
                    style: TextStyle(
                      fontSize: 14,
                      fontFamily: "IBMPlexSansThai-Regular",
                      letterSpacing: 0.3,
                      color: isDarkMode ? textGray400Dark : textGray400Light,
                    ),
                  ),
                  Container(
                    height: 50,
                    child: SpinKitThreeBounce(
                      color: Colors.grey,
                      size: 14,
                    ),
                  ),
                ],
              ),
              SizedBox(
                height: 5,
              ),
              Divider(
                color: Color(0xffEA84FD).withOpacity(0.5),
              ),
              SizedBox(
                height: 5,
              ),
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Text(
                              AppLocalizations.of(context)!.translate('ltw_number_matched'),
                              style: TextStyle(
                                fontSize: 12,
                                fontFamily: "DINPro-Medium",
                                letterSpacing: 0.3,
                                color: Color(0xffEA84FD),
                              ),
                            ),
                          ],
                        ),
                        SizedBox(
                          height: 15,
                        ),
                        RichText(
                          text: TextSpan(
                            text: '4',
                            style: TextStyle(
                                fontSize: 14,
                                fontFamily: "IBMPlexSansThai-Regular",
                                letterSpacing: 0.3,
                                color: isDarkMode
                                    ? Color(0xffE9ECF5)
                                    : Colors.black),
                            children: <TextSpan>[
                              TextSpan(
                                text: " "+AppLocalizations.of(context)!.translate('ltw_digit'),
                                style: TextStyle(
                                  fontSize: 14,
                                  fontFamily: "IBMPlexSansThai-Regular",
                                  letterSpacing: 0.3,
                                  color: textGray300,
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(
                          height: 3,
                        ),
                        RichText(
                          text: TextSpan(
                            text: '3',
                            style: TextStyle(
                                fontSize: 14,
                                fontFamily: "IBMPlexSansThai-Regular",
                                letterSpacing: 0.3,
                                color: isDarkMode
                                    ? Color(0xffE9ECF5)
                                    : Colors.black),
                            children: <TextSpan>[
                              TextSpan(
                                text: " "+AppLocalizations.of(context)!.translate('ltw_digit'),
                                style: TextStyle(
                                  fontSize: 14,
                                  fontFamily: "IBMPlexSansThai-Regular",
                                  letterSpacing: 0.3,
                                  color: textGray300,
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(
                          height: 3,
                        ),
                        RichText(
                          text: TextSpan(
                            text: '2',
                            style: TextStyle(
                                fontSize: 14,
                                fontFamily: "IBMPlexSansThai-Regular",
                                color: isDarkMode
                                    ? Color(0xffE9ECF5)
                                    : Colors.black),
                            children: <TextSpan>[
                              TextSpan(
                                text: " "+AppLocalizations.of(context)!.translate('ltw_digit'),
                                style: TextStyle(
                                  fontFamily: "IBMPlexSansThai-Regular",
                                  fontSize: 14,
                                  letterSpacing: 0.3,
                                  color: textGray300,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    child: Column(
                      children: [
                        Text(
                          AppLocalizations.of(context)!.translate('ltw_winners'),
                          style: TextStyle(
                            fontFamily: "DINPro-Medium",
                            fontSize: 12,
                            letterSpacing: 0.3,
                            color: Color(0xffEA84FD),
                          ),
                        ),
                        SizedBox(
                          height: 15,
                        ),
                        Text(
                          '0',
                          style: TextStyle(
                            fontSize: 14,
                            fontFamily: "IBMPlexSansThai-Regular",
                            letterSpacing: 0.3,
                            color:
                                isDarkMode ? textGray300Dark : textGray300Light,
                          ),
                        ),
                        SizedBox(
                          height: 3,
                        ),
                        Text(
                          '0',
                          style: TextStyle(
                            fontSize: 14,
                            fontFamily: "IBMPlexSansThai-Regular",
                            letterSpacing: 0.3,
                            color: isDarkMode ? textGray300Dark : textGray300Light,
                          ),
                        ),
                        SizedBox(
                          height: 3,
                        ),
                        Text(
                          '0',
                          style: TextStyle(
                            fontSize: 14,
                            fontFamily: "IBMPlexSansThai-Regular",
                            letterSpacing: 0.3,
                            color:
                                isDarkMode ? textGray300Dark : textGray300Light,
                          ),
                        )
                      ],
                    ),
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            InkWell(
                              onTap: () {
                                // isStackItemShow = true;
                                controller.setTolTipVisible(true);
                                // print(tolTipVisible);
                              },
                              child: Image.asset(
                                'assets/images/Group 40633.png',
                                height: 11,
                                package: 'lock_to_win',
                              ),
                            ),
                            SizedBox(
                              width: 8,
                            ),
                            Text(
                              AppLocalizations.of(context)!.translate('ltw_prize_like'),
                              style: TextStyle(
                                fontSize: 12,
                                fontFamily: "DINPro-Medium",
                                letterSpacing: 0.3,
                                color: Color(0xffEA84FD),
                              ),
                            ),
                          ],
                        ),
                        SizedBox(
                          height: 15,
                        ),
                        Text(
                          '0',
                          style: TextStyle(
                            fontSize: 14,
                            fontFamily: "IBMPlexSansThai-Regular",
                            fontWeight: FontWeight.w600,
                            letterSpacing: 0.3,
                            color: isDarkMode
                                ? Color(0xffffffff)
                                : Color(0xff000000),
                          ),
                        ),
                        SizedBox(
                          height: 3,
                        ),
                        Text(
                          '0',
                          style: TextStyle(
                            fontSize: 14,
                            fontFamily: "IBMPlexSansThai-Regular",
                            fontWeight: FontWeight.w600,
                            letterSpacing: 0.3,
                            color: isDarkMode
                                ? Color(0xffffffff)
                                : Color(0xff000000),
                          ),
                        ),
                        SizedBox(
                          height: 3,
                        ),
                        Text(
                          '0',
                          style: TextStyle(
                            fontSize: 14,
                            letterSpacing: 0.3,
                            fontFamily: "IBMPlexSansThai-Regular",
                            fontWeight: FontWeight.w600,
                            color: isDarkMode
                                ? Color(0xffffffff)
                                : Color(0xff000000),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              SizedBox(
                height: 5,
              ),
              Divider(
                color: Color(0xffEA84FD).withOpacity(0.5),
              ),
              SizedBox(
                height: 5,
              ),
            ],
          );
        } else if (snapshot.connectionState == ConnectionState.done) {
          if (snapshot.hasError) {
            return Text(AppLocalizations.of(context)!.translate('ltw_error'));
          } else if (snapshot.hasData) {
            return Column(
              children: [
                Row(
                  children: [
                    Text(
                      AppLocalizations.of(context)!.translate('ltw_round'),
                      style: TextStyle(
                        fontSize: 14,
                        fontFamily: "IBMPlexSansThai-Regular",
                        letterSpacing: 0.3,
                        color: isDarkMode ? textGray400Dark : textGray400Light,
                      ),
                    ),
                    Text(
                      '#${snapshot.data![0]!.toInt()}',
                      style: TextStyle(
                        fontFamily: "IBMPlexSansThai-Regular",
                        color: Color(0xffEA84FD),
                        fontSize: 14,
                        letterSpacing: 0.3,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      AppLocalizations.of(context)!.translate('ltw_total_prize'),
                      style: TextStyle(
                        fontSize: 14,
                        fontFamily: "IBMPlexSansThai-Regular",
                        letterSpacing: 0.3,
                        color: isDarkMode ? textGray400Dark : textGray400Light,
                      ),
                    ),
                    Text(
                      '${snapshot.data![1].totalPrev} LIKE',
                      style: TextStyle(
                        fontFamily: "IBMPlexSansThai-Regular",
                        fontWeight: FontWeight.w600,
                        letterSpacing: 0.3,
                        fontSize: 14,
                        color: isDarkMode ? Color(0xffCAD2DE) : Colors.black,
                      ),
                    )
                  ],
                ),
                SizedBox(
                  height: 5,
                ),
                Divider(
                  color: Color(0xffEA84FD).withOpacity(0.5),
                ),
                SizedBox(
                  height: 5,
                ),
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Text(
                                AppLocalizations.of(context)!.translate('ltw_number_matched'),
                                style: TextStyle(
                                  fontSize: 12,
                                  fontFamily: "DINPro-Medium",
                                  letterSpacing: 0.3,
                                  color: Color(0xffEA84FD),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(
                            height: 15,
                          ),
                          RichText(
                            text: TextSpan(
                              text: '4',
                              style: TextStyle(
                                  fontSize: 14,
                                  fontFamily: "IBMPlexSansThai-Regular",
                                  letterSpacing: 0.3,
                                  color: isDarkMode
                                      ? Color(0xffE9ECF5)
                                      : Colors.black),
                              children: <TextSpan>[
                                TextSpan(
                                  text: " "+AppLocalizations.of(context)!.translate('ltw_digit'),
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontFamily: "IBMPlexSansThai-Regular",
                                    letterSpacing: 0.3,
                                    color: textGray300,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(
                            height: 3,
                          ),
                          RichText(
                            text: TextSpan(
                              text: '3',
                              style: TextStyle(
                                  fontSize: 14,
                                  fontFamily: "IBMPlexSansThai-Regular",
                                  letterSpacing: 0.3,
                                  color: isDarkMode
                                      ? Color(0xffE9ECF5)
                                      : Colors.black),
                              children: <TextSpan>[
                                TextSpan(
                                  text: " "+AppLocalizations.of(context)!.translate('ltw_digit'),
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontFamily: "IBMPlexSansThai-Regular",
                                    letterSpacing: 0.3,
                                    color: textGray300,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(
                            height: 3,
                          ),
                          RichText(
                            text: TextSpan(
                              text: '2',
                              style: TextStyle(
                                  fontSize: 14,
                                  fontFamily: "IBMPlexSansThai-Regular",
                                  color: isDarkMode
                                      ? Color(0xffE9ECF5)
                                      : Colors.black),
                              children: <TextSpan>[
                                TextSpan(
                                  text: " "+AppLocalizations.of(context)!.translate('ltw_digit'),
                                  style: TextStyle(
                                    fontFamily: "IBMPlexSansThai-Regular",
                                    fontSize: 14,
                                    letterSpacing: 0.3,
                                    color: textGray300,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(
                      child: Column(
                        children: [
                          Text(
                            AppLocalizations.of(context)!.translate('ltw_winners'),
                            style: TextStyle(
                              fontFamily: "DINPro-Medium",
                              fontSize: 12,
                              letterSpacing: 0.3,
                              color: Color(0xffEA84FD),
                            ),
                          ),
                          SizedBox(
                            height: 15,
                          ),
                          Text(
                            '${snapshot.data![1].matchFour}',
                            style: TextStyle(
                              fontSize: 14,
                              fontFamily: "IBMPlexSansThai-Regular",
                              letterSpacing: 0.3,
                              fontWeight: int.parse(snapshot.data![1].matchFour) > 0 ? FontWeight.bold : FontWeight.normal,
                              color: isDarkMode
                                  ? int.parse(snapshot.data![1].matchFour) > 0 ? Color(0xffFFFFFF) : textGray300Dark
                                  : int.parse(snapshot.data![1].matchFour) > 0 ? Color(0xff000000) : textGray300Light,
                            ),
                          ),
                          SizedBox(
                            height: 3,
                          ),
                          Text(
                            '${snapshot.data![1].matchThree}',
                            style: TextStyle(
                              fontSize: 14,
                              fontFamily: "IBMPlexSansThai-Regular",
                              letterSpacing: 0.3,
                              fontWeight: int.parse(snapshot.data![1].matchThree) > 0 ? FontWeight.bold : FontWeight.normal,
                              color: isDarkMode
                                  ? int.parse(snapshot.data![1].matchThree) > 0 ? Color(0xffFFFFFF) : textGray300Dark
                                  : int.parse(snapshot.data![1].matchThree) > 0 ? Color(0xff000000) : textGray300Light,
                            ),
                          ),
                          SizedBox(
                            height: 3,
                          ),
                          Text(
                            '${snapshot.data![1].matchTwo}',
                            style: TextStyle(
                              fontSize: 14,
                              fontFamily: "IBMPlexSansThai-Regular",
                              letterSpacing: 0.3,
                              fontWeight: int.parse(snapshot.data![1].matchTwo) > 0 ? FontWeight.bold : FontWeight.normal,
                              color: isDarkMode
                                  ? int.parse(snapshot.data![1].matchTwo) > 0 ? Color(0xffFFFFFF) : textGray300Dark
                                  : int.parse(snapshot.data![1].matchTwo) > 0 ? Color(0xff000000) : textGray300Light,
                            ),
                          )
                        ],
                      ),
                    ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              InkWell(
                                onTap: () {
                                  // isStackItemShow = true;
                                  controller.setTolTipVisible(true);
                                  // print(tolTipVisible);
                                },
                                child: Image.asset(
                                  'assets/images/Group 40633.png',
                                  height: 11,
                                  package: 'lock_to_win',
                                ),
                              ),
                              SizedBox(
                                width: 8,
                              ),
                              Text(
                                AppLocalizations.of(context)!.translate('ltw_prize_like'),
                                style: TextStyle(
                                  fontSize: 12,
                                  fontFamily: "DINPro-Medium",
                                  letterSpacing: 0.3,
                                  color: Color(0xffEA84FD),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(
                            height: 15,
                          ),
                          Text(
                            '${snapshot.data![1].totalReward4}',
                            style: TextStyle(
                              fontSize: 14,
                              fontFamily: "IBMPlexSansThai-Regular",
                              fontWeight: FontWeight.w600,
                              letterSpacing: 0.3,
                              color: isDarkMode
                                  ? Color(0xffffffff)
                                  : Color(0xff000000),
                            ),
                          ),
                          SizedBox(
                            height: 3,
                          ),
                          Text(
                            '${snapshot.data![1].totalReward3}',
                            style: TextStyle(
                              fontSize: 14,
                              fontFamily: "IBMPlexSansThai-Regular",
                              fontWeight: FontWeight.w600,
                              letterSpacing: 0.3,
                              color: isDarkMode
                                  ? Color(0xffffffff)
                                  : Color(0xff000000),
                            ),
                          ),
                          SizedBox(
                            height: 3,
                          ),
                          Text(
                            '${snapshot.data![1].totalReward2}',
                            style: TextStyle(
                              fontSize: 14,
                              letterSpacing: 0.3,
                              fontFamily: "IBMPlexSansThai-Regular",
                              fontWeight: FontWeight.w600,
                              color: isDarkMode
                                  ? Color(0xffffffff)
                                  : Color(0xff000000),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  height: 5,
                ),
                Divider(
                  color: Color(0xffEA84FD).withOpacity(0.5),
                ),
                SizedBox(
                  height: 5,
                ),
              ],
            );
          } else {
            return Text(AppLocalizations.of(context)!.translate('ltw_empty_data'));
          }
        } else {
          return Text('');
        }
      },
    );
  }

  static Widget perRoundWinner(_future) {
    return FutureBuilder(
      future: _future,
      builder: (
        BuildContext context,
        AsyncSnapshot<List<num>?> snapshot,
      ) {
        print(snapshot.connectionState);
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              margin: EdgeInsets.only(
                top: Get.height * 0.22,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Ballon(
                    color: 0xff21C3FF,
                    number: '0',
                  ),
                  Ballon(
                    color: 0xff0058FF,
                    number: '0',
                  ),
                  Ballon(
                    color: 0xffFFAA12,
                    number: '0',
                  ),
                  Ballon(
                    color: 0xffC764FC,
                    number: '0',
                  ),
                ],
              ),
            ),
          );
        } else if (snapshot.connectionState == ConnectionState.done) {
          if (snapshot.hasError) {
            return Text(AppLocalizations.of(context)!.translate('ltw_error'));
          } else if (snapshot.hasData) {
            return Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                margin: EdgeInsets.only(
                  top: Get.height * 0.22,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Ballon(
                      color: 0xff21C3FF,
                      number: snapshot.data![0].toStringAsFixed(0),
                    ),
                    Ballon(
                      color: 0xff0058FF,
                      number: snapshot.data![1].toStringAsFixed(0),
                    ),
                    Ballon(
                      color: 0xffFFAA12,
                      number: snapshot.data![2].toStringAsFixed(0),
                    ),
                    Ballon(
                      color: 0xffC764FC,
                      number: snapshot.data![3].toStringAsFixed(0),
                    ),
                  ],
                ),
              ),
            );
          } else {
            return Text(AppLocalizations.of(context)!.translate('ltw_empty_data'));
          }
        } else {
          return Text(AppLocalizations.of(context)!.translate('ltw_state')+': ${snapshot.connectionState}');
        }
      },
    );
  }

  static Widget countDownTime(CountdownTimerController countdown) {
    return CountdownTimer(
      controller: countdown,
      widgetBuilder: (_, CurrentRemainingTime? time) {
        if (time == null) {
          return Text('');
        }
        return Row(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              '${time.days == null ? 0 : time.days}',
              style: TextStyle(
                color: isDarkMode ? Color(0xffE9ECF5) : Colors.black,
                fontSize: 20,
                fontFamily: "IBMPlexSansThai-Regular",
                letterSpacing: 0.3,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(
              width: 2,
            ),
            Container(
              padding: EdgeInsets.only(bottom: 3),
              child: Text(
                AppLocalizations.of(_)!.translate('ltw_days'),
                style: TextStyle(
                  color: isDarkMode ? Color(0xffE9ECF5) : Colors.black,
                  fontSize: 12,
                  fontFamily: "IBMPlexSansThai-Regular",
                  letterSpacing: 0.3,
                ),
              ),
            ),
            SizedBox(
              width: 2,
            ),
            Text(
              ' ${time.hours == null ? 0 : time.hours}:${time.min == null ? 0 : time.min}:${time.sec == null ? 0 : time.sec}',
              style: TextStyle(
                color: isDarkMode ? Color(0xffE9ECF5) : Colors.black,
                fontSize: 20,
                fontFamily: "IBMPlexSansThai-Regular",
                letterSpacing: 0.3,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        );
      },
    );
  }

  static Widget dateTime(DateTime date) {
    return RichText(
      text: TextSpan(
        text: '${DateFormat('MMM dd, yyyy').format(
          date,
        )}\t',
        style: TextStyle(
          fontFamily: "IBMPlexSansThai-Regular",
          fontSize: 14,
          letterSpacing: 0.3,
          fontWeight: FontWeight.w600,
          color: isDarkMode ? Color(0xffE9ECF5) : Color(0xff262F3E),
        ),
        children: <TextSpan>[
          TextSpan(
            text: '${DateFormat('hh:mm a').format(
              date,
            )}\t',
            style: TextStyle(
                fontFamily: "IBMPlexSansThai-Regular",
                fontSize: 14,
                letterSpacing: 0.3,
                fontWeight: FontWeight.w600,
                color: isDarkMode ? Color(0xffE9ECF5) : Color(0xff262F3E)),
          ),
        ],
      ),
    );
  }

  static Widget preDateTime(DateTime date) {
    return Text(
      DateFormat('dd/MM/yyyy')
          .format(date),
      style: GoogleFonts
          .ibmPlexSansThai(
          textStyle:
          TextStyle(
            fontSize: 12,
            letterSpacing: 0.3,
            fontWeight:
            FontWeight.bold,
            color: isDarkMode
                ? Colors.white
                : Colors.black,
          )),
    );
  }

  static Widget roundNow(double round) {
    return Text(
      '#${round.toStringAsFixed(0)}',
      style: TextStyle(
        fontFamily: "IBMPlexSansThai-Regular",
        fontSize: 14,
        letterSpacing: 0.3,
        fontWeight: FontWeight.w600,
        color: Color(0xff0078FF),
      ),
    );
  }

  static Widget nextRound(
      {BuildContext? context,
      double? round,
      String? total,
      LotteryController? controller}) {
    final f = new NumberFormat("###,###.##");
    return Column(
      children: [
        InkWell(
          onTap: () {
            showButtonSheet(context!, round: round!, data: controller!);
          },
          child: Container(
            width: Get.width,
            margin: EdgeInsets.only(right: 5),
            alignment: Alignment.topRight,
            child: Container(
              padding: EdgeInsets.only(top: 13, left: 15, right: 15),
              child: Image.asset(
                'assets/images/Group 36519.png',
                width: 18,
                package: 'lock_to_win',
                height: 5,
                color: Color(0xff738097),
              ),
            ),
          ),
        ),
        SizedBox(
          height: 6,
        ),
        RichText(
          text: TextSpan(
            text: AppLocalizations.of(context!)!.translate('ltw_prize_next') +' ',
            style: TextStyle(
              fontSize: 14,
              fontFamily: "IBMPlexSansThai-Regular",
              color: isDarkMode ? Color(0xff8a96ab) : textGray300Light,
            ),
            children: <TextSpan>[
              TextSpan(
                text: '(#${round!.toInt()})',
                style: TextStyle(
                  fontFamily: "IBMPlexSansThai-Regular",
                  color: Color(0xff0078FF),
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
        SizedBox(
          height: 5,
        ),
        Text(
          '${f.format(double.parse(total!))} LIKE',
          style: TextStyle(
              color: isDarkMode ? Color(0xffE9ECF5) : Color(0xff262F3E),
              fontSize: 20,
              fontWeight: FontWeight.w600,
              fontFamily: 'Regular'),
        )
      ],
    );
  }

  static Future<void> showButtonSheet(
    BuildContext context, {
    double? round,
    LotteryController? data,
  }) {
    final f = new NumberFormat("###,###.##");
    return showModalBottomSheet(
      isScrollControlled: true,
      constraints: BoxConstraints(maxHeight: 550),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (BuildContext context, setState) {
            return Container(
              decoration: BoxDecoration(
                color: isDarkMode ? Color(0xff1D2532) : Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              height: Get.height,
              width: Get.width,
              padding: EdgeInsets.only(
                top: 10,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  InkWell(
                    onTap: () {
                      Navigator.pop(context);
                      setState() {}
                    },
                    child: Container(
                      height: 15,
                      width: 30,
                      margin: EdgeInsets.only(right: 15),
                      // width: Get.width,
                      alignment: Alignment.topRight,
                      child: Container(
                        margin: EdgeInsets.only(top: 5),
                        child: Image.asset(
                          'assets/images/Group 36519.png',
                          package: 'lock_to_win',
                          width: 18,
                          height: 5,
                        ),
                      ),
                    ),
                  ),
                  Center(
                    child: Container(
                      margin: EdgeInsets.only(left: 10),
                      child: RichText(
                        text: TextSpan(
                          text: AppLocalizations.of(context)!.translate('ltw_prize_next')+' ',
                          style: TextStyle(
                            fontSize: 14,
                            fontFamily: "IBMPlexSansThai-Regular",
                            color: isDarkMode
                                ? Color(0xff8a96ab)
                                : textGray300Light,
                          ),
                          children: <TextSpan>[
                            TextSpan(
                              text: '(#${round!.toInt()})',
                              style: TextStyle(
                                fontFamily: "IBMPlexSansThai-Regular",
                                color: Color(0xff0078FF),
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 5,
                  ),
                  Center(
                    child: Container(
                      margin: EdgeInsets.only(left: 10),
                      child: Text(
                        '${f.format(double.parse(data!.lottoNowRound!.total))} LIKE',
                        style: TextStyle(
                            fontWeight: FontWeight.w600,
                            letterSpacing: 0.3,
                            color: isDarkMode
                                ? Color(0xffE9ECF5)
                                : Color(0xff262F3E),
                            fontSize: 20),
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 15,
                  ),
                  Expanded(
                    child: Container(
                      width: double.infinity,
                      // margin: EdgeInsets.only(top: 12),
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 90,
                        ),
                        decoration: BoxDecoration(
                          image: DecorationImage(
                            fit: BoxFit.fill,
                            image: AssetImage(
                                isDarkMode
                                    ? 'assets/images/Prize_Next_Round_Dark.jpg'
                                    : 'assets/images/Prize_Next_Round_Light.jpg',
                                package: 'lock_to_win'),
                          ),
                        ),
                        child: Column(
                          children: [
                            RichText(
                              text: TextSpan(
                                text: AppLocalizations.of(context)!.translate('ltw_first_prize')+' ',
                                style: TextStyle(
                                  fontSize: 16,
                                  letterSpacing: 0.3,
                                  fontWeight: FontWeight.bold,
                                  fontFamily: "IBMPlexSansThai-Medium",
                                  color: isDarkMode
                                      ? Color(0xffFFB440)
                                      : Color(0xffFFB440),
                                ),
                                children: <TextSpan>[
                                  TextSpan(
                                    text: AppLocalizations.of(context)!.translate('ltw_4_matched'),
                                    style: TextStyle(
                                      fontWeight: FontWeight.w500,
                                      letterSpacing: 0.3,
                                      fontFamily: "IBMPlexSansThai-Regular",
                                      color: Color(0xffFFFFFF),
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(
                              height: 10,
                            ),
                            Text(
                              '${f.format(double.parse(data.lottoNowRound!.totalNowReward4))} LIKE',
                              style: TextStyle(
                                color: isDarkMode
                                    ? Color(0xffFFB440)
                                    : Color(0xffFFB440),
                                fontSize: 20,
                                letterSpacing: 0.3,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            SizedBox(
                              height: 30,
                            ),
                            RichText(
                              text: TextSpan(
                                text: AppLocalizations.of(context)!.translate('ltw_second_prize')+' ',
                                style: TextStyle(
                                  fontSize: 16,
                                  letterSpacing: 0.3,
                                  fontWeight: FontWeight.bold,
                                  fontFamily: "IBMPlexSansThai-Medium",
                                  color: isDarkMode
                                      ? Color(0xffEA84FD)
                                      : Color(0xffC515E5),
                                ),
                                children: <TextSpan>[
                                  TextSpan(
                                    text: AppLocalizations.of(context)!.translate('ltw_3_matched'),
                                    style: TextStyle(
                                      fontWeight: FontWeight.w500,
                                      letterSpacing: 0.3,
                                      fontFamily: "IBMPlexSansThai-Regular",
                                      color: Color(0xffFFFFFF),
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(
                              height: 10,
                            ),
                            Text(
                              '${f.format(double.parse(data.lottoNowRound!.totalNowReward3))} LIKE',
                              style: TextStyle(
                                color: isDarkMode
                                    ? Color(0xffEA84FD)
                                    : Color(0xffC515E5),
                                fontSize: 20,
                                letterSpacing: 0.3,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            SizedBox(
                              height: 30,
                            ),
                            RichText(
                              text: TextSpan(
                                text: AppLocalizations.of(context)!.translate('ltw_third_prize')+' ',
                                style: TextStyle(
                                  fontSize: 16,
                                  letterSpacing: 0.3,
                                  fontWeight: FontWeight.bold,
                                  fontFamily: "IBMPlexSansThai-Medium",
                                  color: isDarkMode
                                      ? Color(0xff15C3FF)
                                      : Color(0xff0031FF),
                                ),
                                children: <TextSpan>[
                                  TextSpan(
                                    text: AppLocalizations.of(context)!.translate('ltw_2_matched'),
                                    style: TextStyle(
                                      fontWeight: FontWeight.w500,
                                      letterSpacing: 0.3,
                                      fontFamily: "IBMPlexSansThai-Regular",
                                      color: Color(0xffFFFFFF),
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(
                              height: 10,
                            ),
                            Text(
                              '${f.format(double.parse(data.lottoNowRound!.totalNowReward2))} LIKE',
                              style: TextStyle(
                                color: isDarkMode
                                    ? Color(0xff15C3FF)
                                    : Color(0xff0031FF),
                                fontSize: 20,
                                letterSpacing: 0.3,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  )
                ],
              ),
            );
          },
        );
      },
    );
  }
  // static Widget preRoundReward() {
  //   return FutureBuilder(
  //     future: getRoundLast(),
  //     builder: (
  //         BuildContext context,
  //         AsyncSnapshot<LottoRound> snapshot,
  //         ) {
  //       print(snapshot.connectionState);
  //       if (snapshot.connectionState == ConnectionState.waiting) {
  //         return Center(
  //           child: SpinKitThreeBounce(
  //             color: Colors.grey,
  //             size: 100,
  //           ),
  //         );
  //       } else if (snapshot.connectionState == ConnectionState.done) {
  //         if (snapshot.hasError) {
  //           return const Text('Error');
  //         } else if (snapshot.hasData) {
  //           return Column(
  //             children: [
  //               Row(
  //                 children: [
  //                   Text(
  //                     'Round',
  //                     style: TextStyle(
  //                       fontSize: 14,
  //                       fontFamily: "IBMPlexSansThai-Regular",
  //                       letterSpacing: 0.3,
  //                       color: isDarkMode ? textGray400Dark : textGray400Light,
  //                     ),
  //                   ),
  //                   Text(
  //                     '#020',
  //                     style: TextStyle(
  //                       fontFamily: "IBMPlexSansThai-Regular",
  //                       color: Color(0xffEA84FD),
  //                       fontSize: 14,
  //                       letterSpacing: 0.3,
  //                       fontWeight: FontWeight.w600,
  //                     ),
  //                   ),
  //                 ],
  //               ),
  //               Row(
  //                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //                 children: [
  //                   Text(
  //                     'Total Prize',
  //                     style: TextStyle(
  //                       fontSize: 14,
  //                       fontFamily: "IBMPlexSansThai-Regular",
  //                       letterSpacing: 0.3,
  //                       color: isDarkMode ? textGray400Dark : textGray400Light,
  //                     ),
  //                   ),
  //                   Text(
  //                     '5,123,2432.18 LIKE',
  //                     style: TextStyle(
  //                       fontFamily: "IBMPlexSansThai-Regular",
  //                       fontWeight: FontWeight.w600,
  //                       letterSpacing: 0.3,
  //                       fontSize: 14,
  //                       color: isDarkMode ? Color(0xffCAD2DE) : Colors.black,
  //                     ),
  //                   )
  //                 ],
  //               ),
  //               SizedBox(
  //                 height: 5,
  //               ),
  //               Divider(
  //                 color: Color(0xffEA84FD).withOpacity(0.5),
  //               ),
  //               SizedBox(
  //                 height: 5,
  //               ),
  //               Row(
  //                 children: [
  //                   Expanded(
  //                     child: Column(
  //                       crossAxisAlignment: CrossAxisAlignment.start,
  //                       children: [
  //                         Row(
  //                           mainAxisAlignment: MainAxisAlignment.start,
  //                           children: [
  //                             Text(
  //                               'No. Matched',
  //                               style: TextStyle(
  //                                 fontSize: 12,
  //                                 fontFamily: "DINPro-Medium",
  //                                 letterSpacing: 0.3,
  //                                 color: Color(0xffEA84FD),
  //                               ),
  //                             ),
  //                           ],
  //                         ),
  //                         SizedBox(
  //                           height: 15,
  //                         ),
  //                         RichText(
  //                           text: TextSpan(
  //                             text: '4',
  //                             style: TextStyle(
  //                                 fontSize: 14,
  //                                 fontFamily: "IBMPlexSansThai-Regular",
  //                                 letterSpacing: 0.3,
  //                                 color: isDarkMode
  //                                     ? Color(0xffE9ECF5)
  //                                     : Colors.black),
  //                             children: <TextSpan>[
  //                               TextSpan(
  //                                 text: " digits",
  //                                 style: TextStyle(
  //                                   fontSize: 14,
  //                                   fontFamily: "IBMPlexSansThai-Regular",
  //                                   letterSpacing: 0.3,
  //                                   color: textGray300,
  //                                 ),
  //                               ),
  //                             ],
  //                           ),
  //                         ),
  //                         SizedBox(
  //                           height: 3,
  //                         ),
  //                         RichText(
  //                           text: TextSpan(
  //                             text: '3',
  //                             style: TextStyle(
  //                                 fontSize: 14,
  //                                 fontFamily: "IBMPlexSansThai-Regular",
  //                                 letterSpacing: 0.3,
  //                                 color: isDarkMode
  //                                     ? Color(0xffE9ECF5)
  //                                     : Colors.black),
  //                             children: <TextSpan>[
  //                               TextSpan(
  //                                 text: " digits",
  //                                 style: TextStyle(
  //                                   fontSize: 14,
  //                                   fontFamily: "IBMPlexSansThai-Regular",
  //                                   letterSpacing: 0.3,
  //                                   color: textGray300,
  //                                 ),
  //                               ),
  //                             ],
  //                           ),
  //                         ),
  //                         SizedBox(
  //                           height: 3,
  //                         ),
  //                         RichText(
  //                           text: TextSpan(
  //                             text: '2',
  //                             style: TextStyle(
  //                                 fontSize: 14,
  //                                 fontFamily: "IBMPlexSansThai-Regular",
  //                                 color: isDarkMode
  //                                     ? Color(0xffE9ECF5)
  //                                     : Colors.black),
  //                             children: <TextSpan>[
  //                               TextSpan(
  //                                 text: " digits",
  //                                 style: TextStyle(
  //                                   fontFamily: "IBMPlexSansThai-Regular",
  //                                   fontSize: 14,
  //                                   letterSpacing: 0.3,
  //                                   color: textGray300,
  //                                 ),
  //                               ),
  //                             ],
  //                           ),
  //                         ),
  //                       ],
  //                     ),
  //                   ),
  //                   SizedBox(
  //                     child: Column(
  //                       children: [
  //                         Text(
  //                           'Winners',
  //                           style: TextStyle(
  //                             fontFamily: "DINPro-Medium",
  //                             fontSize: 12,
  //                             letterSpacing: 0.3,
  //                             color: Color(0xffEA84FD),
  //                           ),
  //                         ),
  //                         SizedBox(
  //                           height: 15,
  //                         ),
  //                         Text(
  //                           "0",
  //                           style: TextStyle(
  //                             fontSize: 14,
  //                             fontFamily: "IBMPlexSansThai-Regular",
  //                             letterSpacing: 0.3,
  //                             color: isDarkMode
  //                                 ? textGray300Dark
  //                                 : textGray300Light,
  //                           ),
  //                         ),
  //                         SizedBox(
  //                           height: 3,
  //                         ),
  //                         Text(
  //                           "2",
  //                           style: TextStyle(
  //                             fontSize: 14,
  //                             fontFamily: "IBMPlexSansThai-Regular",
  //                             letterSpacing: 0.3,
  //                             fontWeight: FontWeight.bold,
  //                             color: isDarkMode
  //                                 ? Color(0xffFFFFFF)
  //                                 : Color(0xff000000),
  //                           ),
  //                         ),
  //                         SizedBox(
  //                           height: 3,
  //                         ),
  //                         Text(
  //                           "0",
  //                           style: TextStyle(
  //                             fontSize: 14,
  //                             fontFamily: "IBMPlexSansThai-Regular",
  //                             color: isDarkMode
  //                                 ? textGray300Dark
  //                                 : textGray300Light,
  //                           ),
  //                         )
  //                       ],
  //                     ),
  //                   ),
  //                   Expanded(
  //                     child: Column(
  //                       crossAxisAlignment: CrossAxisAlignment.end,
  //                       children: [
  //                         Row(
  //                           mainAxisAlignment: MainAxisAlignment.end,
  //                           children: [
  //                             InkWell(
  //                               onTap: () {
  //                                 // isStackItemShow = true;
  //                                 // tolTipVisible = true;
  //                                 // print(tolTipVisible);
  //                               },
  //                               child: Image.asset(
  //                                 'assets/images/Group 40633.png',
  //                                 height: 11,
  //                                 package: 'lock_to_win',
  //                               ),
  //                             ),
  //                             SizedBox(
  //                               width: 8,
  //                             ),
  //                             Text(
  //                               'Prize (LIKE)',
  //                               style: TextStyle(
  //                                 fontSize: 12,
  //                                 fontFamily: "DINPro-Medium",
  //                                 letterSpacing: 0.3,
  //                                 color: Color(0xffEA84FD),
  //                               ),
  //                             ),
  //                           ],
  //                         ),
  //                         SizedBox(
  //                           height: 15,
  //                         ),
  //                         Text(
  //                           "425.7467",
  //                           style: TextStyle(
  //                             fontSize: 14,
  //                             fontFamily: "IBMPlexSansThai-Regular",
  //                             fontWeight: FontWeight.w600,
  //                             letterSpacing: 0.3,
  //                             color: isDarkMode
  //                                 ? Color(0xffffffff)
  //                                 : Color(0xff000000),
  //                           ),
  //                         ),
  //                         SizedBox(
  //                           height: 3,
  //                         ),
  //                         Text(
  //                           "5425.4254",
  //                           style: TextStyle(
  //                             fontSize: 14,
  //                             fontFamily: "IBMPlexSansThai-Regular",
  //                             fontWeight: FontWeight.w600,
  //                             letterSpacing: 0.3,
  //                             color: isDarkMode
  //                                 ? Color(0xffffffff)
  //                                 : Color(0xff000000),
  //                           ),
  //                         ),
  //                         SizedBox(
  //                           height: 3,
  //                         ),
  //                         Text(
  //                           "5425,5245",
  //                           style: TextStyle(
  //                             fontSize: 14,
  //                             letterSpacing: 0.3,
  //                             fontFamily: "IBMPlexSansThai-Regular",
  //                             fontWeight: FontWeight.w600,
  //                             color: isDarkMode
  //                                 ? Color(0xffffffff)
  //                                 : Color(0xff000000),
  //                           ),
  //                         ),
  //                       ],
  //                     ),
  //                   ),
  //                 ],
  //               ),
  //               SizedBox(
  //                 height: 5,
  //               ),
  //               Divider(
  //                 color: Color(0xffEA84FD).withOpacity(0.5),
  //               ),
  //               SizedBox(
  //                 height: 5,
  //               ),
  //             ],
  //           );
  //         } else {
  //           return const Text('Empty data');
  //         }
  //       } else {
  //         return Text('State: ${snapshot.connectionState}');
  //       }
  //     },
  //   );
  // }

  static Widget preRoundCheckYourNumber(BuildContext context,
      YourLottoListPreRound data, LotteryController controller) {
    bool isYes = false;
    if (data.data!.length > 0) {
      isYes = true;
    }
    final Controller c = Get.put(Controller());
    return Stack(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AppLocalizations.of(context)!.translate('ltw_play_last_round'),
                  style: GoogleFonts.ibmPlexSansThai(
                    textStyle: TextStyle(
                      color: isDarkMode ? textGray300Dark : textGray300Light,
                      fontSize: 12,
                    ),
                  ),
                ),
                SizedBox(
                  height: 5,
                ),
                Row(
                  children: [
                    Text(AppLocalizations.of(context)!.translate('ltw_yes'),
                        style: TextStyle(
                          letterSpacing: 0.3,
                          fontFamily: "IBMPlexSansThai-Regular",
                          fontWeight: isYes ? FontWeight.w600 : FontWeight.w500,
                          color: isDarkMode && isYes
                              ? Color(0xffE9ECF5)
                              : Color(0xff3E4B5E),
                        )),
                    SizedBox(
                      width: 2,
                    ),
                    Text(
                      '/',
                      style: TextStyle(
                          letterSpacing: 0.3,
                          fontFamily: "IBMPlexSansThai-Regular",
                          color: Color(0xff3E4B5E)),
                    ),
                    SizedBox(
                      width: 2,
                    ),
                    Text(AppLocalizations.of(context)!.translate('ltw_no'),
                        style: TextStyle(
                          letterSpacing: 0.3,
                          fontFamily: "IBMPlexSansThai-Regular",
                          fontWeight: isYes == false
                              ? FontWeight.w600
                              : FontWeight.w500,
                          color: isDarkMode && isYes == false
                              ? Color(0xffE9ECF5)
                              : Color(0xff3E4B5E),
                        )),
                  ],
                ),
              ],
            ),
            InkWell(
              onTap: () {
                // controller.setIsTooltipNavigate(true);
                controller.setIsStackItemShow(true);
                // tolTipVisible = true;
                // print(tolTipVisible);
                // setState(() {
                controller.height = 260;
                controller.width = Get.width;
                controller.size = 40;
                // });`
              },
              child: AnimatedOpacity(
                opacity: 1.0,
                duration: const Duration(seconds: 2),
                child: Container(
                  alignment: Alignment.center,
                  height: 36,
                  width: 160,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                        colors: isDarkMode
                            ? isYes
                                ? [Color(0xff7937FE), Color(0xff6210FD)]
                                : [
                                    Color(0xff7937FE).withOpacity(0.2),
                                    Color(0xff6210FD).withOpacity(0.2)
                                  ]
                            : isYes
                                ? [
                                    Color(0xff935EFE),
                                    Color(0xff7C37FD),
                                  ]
                                : [
                                    Color(0xff935EFE).withOpacity(0.3),
                                    Color(0xff7C37FD).withOpacity(0.3),
                                  ]),
                    boxShadow: isDarkMode
                        ? <BoxShadow>[
                            BoxShadow(
                              color: Color(0xff945DFF).withOpacity(0.15),
                              blurRadius: 1,
                              offset: Offset(0.0, 0),
                            )
                          ]
                        : [
                            BoxShadow(
                              color: Color(0xff945DFF).withOpacity(0.30),
                              blurRadius: 5.0,
                              offset: Offset(0.0, 4),
                            )
                          ],
                    borderRadius: BorderRadius.circular(50),
                  ),
                  child: Text(
                    AppLocalizations.of(context)!.translate('ltw_view_your_number'),
                    style: TextStyle(
                      fontSize: 12,
                      fontFamily: "IBMPlexSansThai-Regular",
                      letterSpacing: 0.3,
                      color: isDarkMode
                          ? isYes
                              ? Color(0xffEFE6FF)
                              : Color(0xffEFE6FF).withOpacity(0.2)
                          : isYes
                              ? Color(0xffEBEDFD)
                              : Color(0xffEBEDFD).withOpacity(0.5),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
        // tolTipVisible == false
        //     ? InkWell(
        //         onTap: () {
        //           // tolTipVisible = true;
        //           // isTooltipNavigate = false;
        //           // isSecondToptipVisible = false;
        //           // setState(() {});
        //         },
        //         child: Column(
        //           crossAxisAlignment: CrossAxisAlignment.start,
        //           children: [
        //             SizedBox(
        //               height: 37,
        //             ),
        //             // Container(
        //             //   margin: EdgeInsets.only(
        //             //       left: 30),
        //             //   child: Image.asset(
        //             //     'assets/images/triangle_PNG16.png',
        //             //     height: 15,
        //             //     color:
        //             //         Color(0xff2B374B),
        //             //   ),
        //             // ),
        //             Stack(
        //               children: [
        //                 Container(
        //                   padding: EdgeInsets.only(
        //                     top: 10,
        //                   ),
        //                   width: 235,
        //                   height: 100,
        //                   child: Container(
        //                     padding: EdgeInsets.only(
        //                       top: 30,
        //                       left: 25,
        //                       right: 25,
        //                     ),
        //                     width: 227,
        //                     height: 100,
        //                     decoration: BoxDecoration(
        //                       color: Color(0xff2B374B),
        //                       borderRadius: BorderRadius.circular(14),
        //                     ),
        //                     child: Text(
        //                       'Also over here “No” is active instead of “Yes”.',
        //                       style: TextStyle(
        //                         color: Color(0xffD3AA00),
        //                       ),
        //                       textAlign: TextAlign.center,
        //                     ),
        //                   ),
        //                 ),
        //                 Positioned(
        //                   top: 0,
        //                   left: 30,
        //                   child: ClipPath(
        //                     clipper: ParallelogramClipper(),
        //                     child: Container(
        //                       height: 25,
        //                       width: 25,
        //                       color: Color(0xff2B374B),
        //                       child: Center(child: Text("")),
        //                     ),
        //                   ),
        //                 ),
        //               ],
        //             ),
        //           ],
        //         ),
        //       )
        //     : Container(),
      ],
    );
  }

  static Widget listNumber({required LotteryController controller}) {
    return Align(
      child: Animated(
        value: controller.isStackItemShow ? 1 : 0.5,
        curve: Curves.easeInOut,
        duration: Duration(milliseconds: 300),
        builder: (context, child, animation) => Transform.scale(
          scale: animation.value,
          child: child,
        ),
        child: Container(
          margin: EdgeInsets.only(top: 48),
          height: controller.height*1.06,
          width: controller.width,
          child: ClipRRect(
            // <-- clips to the 200x200 [Container] below
            borderRadius: BorderRadius.circular(20),
            child: BackdropFilter(
                filter: ImageFilter.blur(
                  sigmaX: 12.0,
                  sigmaY: 12.0,
                ),
                child: Column(
                  children: [
                    InkWell(
                      onTap: () {
                        controller.setIsStackItemShow(false);
                        controller.height = 0;
                        controller.width = 0;
                        controller.size = 0;
                      },
                      child: Container(
                        alignment: Alignment.topRight,
                        margin: EdgeInsets.only(right: 20, top: 10),
                        child: Image.asset(
                          'assets/images/Group 36634.png',
                          height: 20,
                          package: 'lock_to_win',
                        ),
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.only(left: 30, right: 30),
                      alignment: Alignment.topCenter,
                      decoration: BoxDecoration(
                        // color: isDarkMode ? Color(0xff020202) : Colors.white,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: SingleChildScrollView(
                        child: Column(
                          children: [
                            GridView.builder(
                              shrinkWrap: true,
                              physics: NeverScrollableScrollPhysics(),
                              gridDelegate:
                                  SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: 5,
                              ),
                              itemCount: controller.ballon.length,
                              itemBuilder: (context, i) {
                                return Container(
                                  alignment: Alignment.center,
                                  margin: EdgeInsets.only(
                                      bottom: 12, left: 6, right: 6),
                                  height: controller.size,
                                  width: controller.size,
                                  decoration: BoxDecoration(
                                    gradient: controller.ballon[i].gradient!,
                                    borderRadius: BorderRadius.circular(100),
                                  ),
                                  child: Text(
                                    controller.ballon[i].number!,
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontFamily: "IBMPlexSansThai-Regular",
                                      letterSpacing: 0.3,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black,
                                    ),
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                )),
          ),
        ),
      ),
    );
  }
}
