import 'package:flutter/material.dart';
import 'package:lock_to_win/main.dart';
import 'package:likewallet/libraryman/app_local.dart';

class GameRulesScreen extends StatelessWidget {
  const GameRulesScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: isDarkMode ? Color(0xff11161E) : Color(0xffEFF1F4),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Container(
            color: isDarkMode ? Color(0xff11161E) : Color(0xffEFF1F4),
            padding: EdgeInsets.only(top: 20, left: 20, right: 20),
            width: double.infinity,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  margin: EdgeInsets.only(bottom: 25),
                  alignment: Alignment.topRight,
                  child: InkWell(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    child: Image.asset(
                      'assets/images/Group 36634_blue.png',
                      height: 22,
                      package: 'lock_to_win',
                    ),
                  ),
                ),

                Text(
                  AppLocalizations.of(context)?.locale.toString() == 'en_US' ? "LikeWallet invites you to join our “LOCK TO WIN“ activity." : "เชิญร่วมสนุกกับกิจกรรม “ล็อคทูวิน“ ลุ้นรับรางวัลสุดพิเศษจากไลค์วอลเลท",
                  style: TextStyle(
                    fontFamily: "IBMPlexSansThai-Medium",
                    fontWeight: FontWeight.bold,
                    letterSpacing: 1,
                    fontSize: 14,
                    color: isDarkMode ? Color(0xffE9ECF5) : Color(0xff262F3E),
                  ),
                ),
                SizedBox(
                  height: 20,
                ),
                Text(
                  AppLocalizations.of(context)?.locale.toString() == 'en_US'
                      ? AppLocalizations.of(context)!.translate('ltw_title_rule') +' '+ AppLocalizations.of(context)!.translate('ltw_game_rule')
                      : AppLocalizations.of(context)!.translate('ltw_game_rule') +' '+ AppLocalizations.of(context)!.translate('ltw_title_rule'),
                  style: TextStyle(
                    fontFamily: "IBMPlexSansThai-Medium",
                    fontWeight: FontWeight.bold,
                    letterSpacing: 1,
                    fontSize: 14,
                    color: isDarkMode ? Color(0xffE9ECF5) : Color(0xff262F3E),
                  ),
                ),
                SizedBox(
                  height: 20,
                ),
                RichText(
                  text: TextSpan(
                      text: AppLocalizations.of(context)!.translate('ltw_hot_to_get')+' ',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        fontFamily: "IBMPlexSansThai-Regular",
                        color: Color(0xff0078FF),
                      ),
                      children: <TextSpan>[
                        TextSpan(
                          text: AppLocalizations.of(context)!.translate('ltw_title_rule')+' ',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            letterSpacing: 1,
                            fontFamily: "IBMPlexSansThai-Medium",
                            color: isDarkMode
                                ? Color(0xffEBEDFD)
                                : Color(0xff0078FF),
                            fontSize: 14,
                          ),
                        ),
                        TextSpan(
                            text: AppLocalizations.of(context)?.locale.toString() == 'en_US' ?
                            ' '+AppLocalizations.of(context)!.translate('ltw_tickets') : '',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              fontFamily: "IBMPlexSansThai-Regular",
                              color: Color(0xff0078FF),
                            ))
                      ]),
                ),
                SizedBox(
                  height: 5,
                ),
                Text(
                  AppLocalizations.of(context)!.translate('ltw_ticket_rule_detail'),
                  style: TextStyle(
                    fontSize: 14,
                    height: 1.3,
                    fontFamily: "IBMPlexSansThai-Regular",
                    color: Color(0xff738097),
                  ),
                ),
                SizedBox(
                  height: 20,
                ),
                Row(
                  children: [
                    Text(
                      AppLocalizations.of(context)!.translate('ltw_user_limit')+': ',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                        fontFamily: "IBMPlexSansThai-Regular",
                        color: Color(0xff0078FF),
                      ),
                    ),
                    Text(
                      AppLocalizations.of(context)!.translate('ltw_maximum_ticket')+' ',
                      style: TextStyle(
                        color: Color(0xff738097),
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  height: 20,
                ),
                // Text(AppLocalizations.of(context)!.translate('ltw_how_to_win')+':',
                //     style: TextStyle(
                //       fontWeight: FontWeight.bold,
                //       fontSize: 14,
                //       fontFamily: "IBMPlexSansThai-Regular",
                //       color: Color(0xff0078FF),
                //     )),
                // SizedBox(
                //   height: 5,
                // ),
                // Text(
                //   AppLocalizations.of(context)!.translate('ltw_win_rule_detail1'),
                //   style: TextStyle(
                //     color: Color(0xff738097),
                //     fontSize: 14,
                //     height: 1.3,
                //     fontFamily: "IBMPlexSansThai-Regular",
                //   ),
                // ),
                // SizedBox(
                //   height: 15,
                // ),
                // Text(
                //   AppLocalizations.of(context)!.translate('ltw_win_rule_detail2'),
                //   style: TextStyle(
                //     color: Color(0xff738097),
                //     fontSize: 14,
                //     height: 1.3,
                //     fontFamily: "IBMPlexSansThai-Regular",
                //   ),
                // ),
                // SizedBox(
                //   height: 30,
                // ),
                Text(
                  AppLocalizations.of(context)!.translate('ltw_how_to_win')+':',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                    fontFamily: "IBMPlexSansThai-Regular",
                    color: Color(0xff0078FF),
                  ),
                ),
                SizedBox(
                  height: 5,
                ),
                Text(
                  AppLocalizations.of(context)!.translate('ltw_wining_ratio_detail'),
                  style: TextStyle(
                    color: Color(0xff738097),
                    fontSize: 14,
                    height: 1.3,
                    fontFamily: "IBMPlexSansThai-Regular",
                  ),
                ),
                SizedBox(
                  height: 20,
                ),
                RichText(
                  text: TextSpan(
                    text: AppLocalizations.of(context)!.translate('ltw_note')+' ',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                      fontFamily: "IBMPlexSansThai-Regular",
                      color: Color(0xff0078FF),
                    ),
                    children: <TextSpan>[
                      TextSpan(
                          text:
                          AppLocalizations.of(context)!.translate('ltw_note_detail'),
                          style: TextStyle(
                            color: Color(0xff00CEFF),
                            fontSize: 14,
                            fontWeight: FontWeight.normal,
                            height: 1.3,
                            fontFamily: "IBMPlexSansThai-Regular",
                          )),
                    ],
                  ),
                ),
                SizedBox(
                  height: 40,
                ),
                Text(
                  AppLocalizations.of(context)?.locale.toString() == 'en_US'
                      ? AppLocalizations.of(context)!.translate('ltw_title_rule')+' '
                      +AppLocalizations.of(context)!.translate('ltw_game_phase') +':'
                      :AppLocalizations.of(context)!.translate('ltw_game_phase')+' '
                      +AppLocalizations.of(context)!.translate('ltw_title_rule') +':',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                    fontFamily: "IBMPlexSansThai-Regular",
                    color: Color(0xff0078FF),
                  ),
                ),
                SizedBox(
                  height: 5,
                ),
                Text(
                  AppLocalizations.of(context)!.translate('ltw_game_phase_detail'),
                  style: TextStyle(
                    color: Color(0xff738097),
                    fontSize: 14,
                    height: 1.3,
                    fontFamily: "IBMPlexSansThai-Regular",
                  ),
                ),
                SizedBox(
                  height: 10,
                ),
                Text(
                  AppLocalizations.of(context)!.translate('ltw_how_draw'),
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                    fontFamily: "IBMPlexSansThai-Regular",
                    color: Color(0xff0078FF),
                  ),
                ),
                SizedBox(
                  height: 10,
                ),
                Text(
                  AppLocalizations.of(context)!.translate('ltw_how_draw_detail'),
                  style: TextStyle(
                    color: Color(0xff738097),
                    fontSize: 14,
                    height: 1.3,
                    fontFamily: "IBMPlexSansThai-Regular",
                  ),
                ),
                SizedBox(
                  height: 40,
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}