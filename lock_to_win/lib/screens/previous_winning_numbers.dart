import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:lock_to_win/config/color_system.dart';
import 'package:lock_to_win/config/dark_mode_colors.dart';
import 'package:lock_to_win/config/light_mode_colors.dart';
import 'package:lock_to_win/main.dart';
import 'package:lock_to_win/models/previous_winning.dart';
import 'package:lock_to_win/widgets/search_text_field.dart';

class PreviousWinningNumbers extends StatelessWidget {
  TextEditingController _searchTextController = TextEditingController();

  PreviousWinningNumbers({
    Key? key,
    this.image,
    this.title,
  }) : super(key: key);
  String? image;
  String? title;

  List<PreviousWinningModle> previ = [
    PreviousWinningModle(
        dateTime: DateTime.now(),
        digit1: '4',
        digit2: '3',
        digit3: '2',
        likes: '123,3243,434,18',
        prizeLike1: '4324,4234',
        prizeLike2: '534,345,7',
        prizeLike3: '454,534,6',
        roundNo: '#01',
        winnig1: '0',
        winnig2: '2',
        winnig3: '0',
        yourNumbers: '10',
        winningNumbers: '3342'),
    PreviousWinningModle(
        dateTime: DateTime.now(),
        digit1: '4',
        digit2: '3',
        digit3: '2',
        likes: '123,3243,434,18',
        prizeLike1: '4324,4234',
        prizeLike2: '534,345,7',
        prizeLike3: '454,534,6',
        roundNo: '#02',
        winnig1: '0',
        winnig2: '2',
        winnig3: '0',
        yourNumbers: '10',
        winningNumbers: '9345')
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: isDarkMode ? Color(0xff080B0F) : Color(0xffE3E7EC),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Container(
            width: double.infinity,
            color: isDarkMode ? Color(0xff080B0F) : Color(0xffE3E7EC),
            child: Column(
              children: [
                Container(
                  height: 90,
                  color: isDarkMode ? Color(0xff1D2532) : Color(0xffFeFeFe),
                  child: Row(
                    children: [
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            Navigator.pop(context);
                          },
                          child: Icon(
                            Icons.arrow_back_ios,
                            color:
                                isDarkMode ? Color(0xff738097) : Colors.black,
                            size: 16,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 5,
                        child: Column(
                          children: [
                            SizedBox(
                              height: 28,
                            ),
                            Image.asset(
                              image!,
                              package: 'lock_to_win',
                              height: 14,
                            ),
                            SizedBox(
                              height: 8,
                            ),
                            Text(
                              title!,
                              style: TextStyle(
                                fontSize: 14,
                                letterSpacing: 0.3,
                                color: isDarkMode
                                    ? Color(0xffAAB4C4)
                                    : Colors.black,
                              ),
                            ),
                            SizedBox(
                              height: 8,
                            ),
                          ],
                        ),
                      ),
                      Expanded(
                        child: Container(),
                      ),
                    ],
                  ),
                ),
                Container(
                    padding: EdgeInsets.all(8),
                    decoration:
                        BoxDecoration(borderRadius: BorderRadius.circular(6.0)),
                    child: Container(
                      padding: EdgeInsets.only(left: 8, right: 8, top: 8),
                      margin: EdgeInsets.only(bottom: 20),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(6.0),
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: isDarkMode
                                ? [
                                    Color(0xff161C27),
                                    Color(0xff161C27).withOpacity(0),
                                  ]
                                : [
                                    Color(0xffEFF1F4),
                                    Color(0xffF6F7F8).withOpacity(0),
                                  ],
                          )),
                      child: Column(
                        children: [
                          SearchTextField(
                            searchTextController: _searchTextController,
                            text: 'Search Round No.',
                            autofocus: false,
                            onChanged: (String keyword) {},
                          ),

                          // TextFormField(
                          //   decoration: InputDecoration(
                          //     border: OutlineInputBorder(
                          //         borderSide: BorderSide.none,
                          //         borderRadius: BorderRadius.circular(12)),
                          //     filled: true,
                          //     fillColor: isDarkMode
                          //         ? Color(0xff3E4B5E)
                          //         : Color(0xffCAD2DE),
                          //     prefixIcon: Icon(
                          //       Icons.search,
                          //       color:
                          //           isDarkMode ? Color(0xff738097) : Colors.black,
                          //     ),
                          //     hintText: 'Search Round No.',
                          //     hintStyle: TextStyle(
                          //       color:
                          //           isDarkMode ? Color(0xff738097) : Colors.black,
                          //     ),
                          //   ),
                          // ),
                          SizedBox(
                            height: 8,
                          ),
                          ListView.builder(
                            shrinkWrap: true,
                            physics: NeverScrollableScrollPhysics(),
                            itemCount: previ.length,
                            itemBuilder: (context, i) {
                              final data = previ[i];
                              return Container(
                                margin: EdgeInsets.only(bottom: 8),
                                decoration: BoxDecoration(
                                  color: isDarkMode
                                      ? Color(0xff1D2532)
                                      : Color(0xffFFFFFF),
                                  borderRadius: BorderRadius.circular(10),
                                  border: Border.all(
                                      color: isDarkMode
                                          ? Color(0xff1D2532)
                                          : Color(0xff1D2532)
                                              .withOpacity(0.03)),
                                ),
                                child: Column(
                                  children: [
                                    Container(
                                      padding:
                                          EdgeInsets.symmetric(horizontal: 20),
                                      margin: EdgeInsets.only(top: 15),
                                      child: Row(
                                        children: [
                                          Text(
                                            'Round',
                                            style: TextStyle(
                                              fontSize: 14,
                                              letterSpacing: 0.3,
                                              fontFamily:
                                                  "IBMPlexSansThai-Regular",
                                              color: isDarkMode
                                                  ? Color(0xff738097)
                                                  : Color(0xff1D2532),
                                            ),
                                          ),
                                          SizedBox(
                                            width: 5,
                                          ),
                                          Text(
                                            data.roundNo!,
                                            style: TextStyle(
                                              fontFamily:
                                                  "IBMPlexSansThai-Regular",
                                              color: Color(0xffEA84FD),
                                              letterSpacing: 0.3,
                                              fontSize: 14,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Container(
                                      padding:
                                          EdgeInsets.symmetric(horizontal: 20),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            'Winning Numbers',
                                            style: TextStyle(
                                              fontSize: 14,
                                              letterSpacing: 0.3,
                                              fontFamily:
                                                  "IBMPlexSansThai-Regular",
                                              color: isDarkMode
                                                  ? Color(0xff738097)
                                                  : Color(0xff1D2532),
                                            ),
                                          ),
                                          Text(
                                            data.winningNumbers!,
                                            style: TextStyle(
                                              fontFamily:
                                                  "IBMPlexSansThai-Regular",
                                              fontWeight: FontWeight.bold,
                                              letterSpacing: 0.3,
                                              color: Color(0xffEA84FD),
                                              fontSize: 14,
                                              // fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Container(
                                      padding:
                                          EdgeInsets.symmetric(horizontal: 20),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            'Total Prize',
                                            style: TextStyle(
                                              fontSize: 14,
                                              letterSpacing: 0.3,
                                              fontFamily:
                                                  "IBMPlexSansThai-Regular",
                                              color: isDarkMode
                                                  ? Color(0xff738097)
                                                  : Color(0xff1D2532),
                                            ),
                                          ),
                                          SizedBox(
                                            width: 5,
                                          ),
                                          Text(
                                            '${data.likes} LIKE',
                                            style: TextStyle(
                                              fontWeight: FontWeight.w600,
                                              fontSize: 14,
                                              letterSpacing: 0.3,
                                              fontFamily:
                                                  "IBMPlexSansThai-Medium",
                                              color: isDarkMode
                                                  ? Color(0xffE9ECF5)
                                                  : Color(0xff262F3E),
                                              // fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Container(
                                      padding:
                                          EdgeInsets.symmetric(horizontal: 20),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            'Draw Time',
                                            style: TextStyle(
                                              fontSize: 14,
                                              letterSpacing: 0.3,
                                              fontFamily:
                                                  "IBMPlexSansThai-Regular",
                                              color: isDarkMode
                                                  ? Color(0xff738097)
                                                  : Color(0xff1D2532),
                                            ),
                                          ),
                                          SizedBox(
                                            width: 5,
                                          ),
                                          RichText(
                                            text: TextSpan(
                                              text:
                                                  '${DateFormat('dd/MM/yyyy').format(
                                                DateTime.now(),
                                              )}\t',
                                              style: TextStyle(
                                                fontSize: 12,
                                                fontWeight: FontWeight.bold,
                                                letterSpacing: 0.3,
                                                fontFamily:
                                                    "IBMPlexSansThai-Medium",
                                                color: isDarkMode
                                                    ? Color(0xffE9ECF5)
                                                    : Color(0xff262F3E),
                                              ),
                                              children: <TextSpan>[
                                                TextSpan(
                                                  text: DateFormat("hh:mm:ss")
                                                      .format(DateTime.now()),
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                    fontFamily:
                                                        "IBMPlexSansThai-Regular",
                                                    color: isDarkMode
                                                        ? Color(0xff8A96AB)
                                                        : Color(0xff4E5D75),
                                                    fontSize: 12,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    SizedBox(
                                      height: 10,
                                    ),
                                    Divider(
                                      height: 1,
                                      thickness: 0.5,
                                      color: Color(0xffEA84FD),
                                    ),
                                    Container(
                                      padding:
                                          EdgeInsets.symmetric(horizontal: 20),
                                      width: double.infinity,
                                      decoration: BoxDecoration(
                                          gradient: LinearGradient(
                                        begin: Alignment.topCenter,
                                        end: Alignment.bottomCenter,
                                        colors: [
                                          Color(0xffEA84FD).withOpacity(0.10),
                                          Color(0xffEA84FD).withOpacity(0),
                                        ],
                                        stops: [0.0, 0.5],
                                      )),
                                      child: Container(
                                        margin: EdgeInsets.only(top: 15),
                                        child: Row(
                                          children: [
                                            Expanded(
                                              child: Column(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.start,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    'No. Matched',
                                                    style: TextStyle(
                                                        fontFamily:
                                                            "DINPro-Medium",
                                                        fontSize: 12.5,
                                                        letterSpacing: 0.3,
                                                        color:
                                                            Color(0xff9760FE)),
                                                  ),
                                                  SizedBox(
                                                    height: 15,
                                                  ),
                                                  Row(
                                                    children: [
                                                      Text(
                                                        data.digit1!,
                                                        style: TextStyle(
                                                            fontSize: 14,
                                                            letterSpacing: 0.3,
                                                            fontFamily:
                                                                "IBMPlexSansThai-Regular",
                                                            color: isDarkMode
                                                                ? Color(
                                                                    0xffE9ECF5)
                                                                : Colors.black),
                                                      ),
                                                      Container(
                                                        margin: EdgeInsets.only(
                                                            left: 5),
                                                        child: Text(
                                                          'digits',
                                                          style: TextStyle(
                                                              fontSize: 14,
                                                              letterSpacing:
                                                                  0.3,
                                                              fontFamily:
                                                                  "IBMPlexSansThai-Regular",
                                                              color: isDarkMode
                                                                  ? Color(
                                                                      0xff8A96AB)
                                                                  : Color(
                                                                      0xff1D2532)),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  SizedBox(
                                                    height: 3,
                                                  ),
                                                  Row(
                                                    children: [
                                                      Text(
                                                        data.digit2!,
                                                        style: TextStyle(
                                                            fontSize: 14,
                                                            letterSpacing: 0.3,
                                                            fontFamily:
                                                                "IBMPlexSansThai-Regular",
                                                            color: isDarkMode
                                                                ? Color(
                                                                    0xffE9ECF5)
                                                                : Colors.black),
                                                      ),
                                                      Container(
                                                        margin: EdgeInsets.only(
                                                            left: 5),
                                                        child: Text(
                                                          'digits',
                                                          style: TextStyle(
                                                              fontSize: 14,
                                                              letterSpacing:
                                                                  0.3,
                                                              fontFamily:
                                                                  "IBMPlexSansThai-Regular",
                                                              color: isDarkMode
                                                                  ? Color(
                                                                      0xff8A96AB)
                                                                  : Color(
                                                                      0xff1D2532)),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  SizedBox(
                                                    height: 3,
                                                  ),
                                                  Row(
                                                    children: [
                                                      Text(
                                                        data.digit3!,
                                                        style: TextStyle(
                                                            fontSize: 14,
                                                            letterSpacing: 0.3,
                                                            fontFamily:
                                                                "IBMPlexSansThai-Regular",
                                                            color: isDarkMode
                                                                ? Color(
                                                                    0xffE9ECF5)
                                                                : Colors.black),
                                                      ),
                                                      Container(
                                                        margin: EdgeInsets.only(
                                                            left: 5),
                                                        child: Text(
                                                          'digits',
                                                          style: TextStyle(
                                                              fontSize: 14,
                                                              letterSpacing:
                                                                  0.3,
                                                              fontFamily:
                                                                  "IBMPlexSansThai-Regular",
                                                              color: isDarkMode
                                                                  ? Color(
                                                                      0xff8A96AB)
                                                                  : Color(
                                                                      0xff1D2532)),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ],
                                              ),
                                            ),
                                            SizedBox(
                                              child: Column(
                                                children: [
                                                  Text(
                                                    'Winners',
                                                    style: TextStyle(
                                                        fontFamily:
                                                            "DINPro-Medium",
                                                        fontSize: 12.5,
                                                        letterSpacing: 0.3,
                                                        color:
                                                            Color(0xff9760FE)),
                                                  ),
                                                  SizedBox(
                                                    height: 15,
                                                  ),
                                                  data.winnig1! == "0"
                                                      ? Text(
                                                          data.winnig1!,
                                                          style: TextStyle(
                                                              fontFamily:
                                                                  "IBMPlexSansThai-Regular",
                                                              fontSize: 14,
                                                              letterSpacing:
                                                                  0.3,
                                                              color: Color(
                                                                  0xff8A96AB)),
                                                        )
                                                      : Text(
                                                          data.winnig1!,
                                                          style: TextStyle(
                                                              fontFamily:
                                                                  "IBMPlexSansThai-Regular",
                                                              fontSize: 14,
                                                              letterSpacing:
                                                                  0.3,
                                                              color: isDarkMode
                                                                  ? Color(
                                                                      0xffE9ECF5)
                                                                  : Color(
                                                                      0xff262F3E)),
                                                        ),
                                                  SizedBox(
                                                    height: 3,
                                                  ),
                                                  data.winnig2! == "0"
                                                      ? Text(
                                                          data.winnig2!,
                                                          style: TextStyle(
                                                              fontFamily:
                                                                  "IBMPlexSansThai-Regular",
                                                              fontSize: 14,
                                                              letterSpacing:
                                                                  0.3,
                                                              color: Color(
                                                                  0xff8A96AB)),
                                                        )
                                                      : Text(
                                                          data.winnig2!,
                                                          style: TextStyle(
                                                              fontFamily:
                                                                  "IBMPlexSansThai-Regular",
                                                              fontSize: 14,
                                                              letterSpacing:
                                                                  0.3,
                                                              color: isDarkMode
                                                                  ? Color(
                                                                      0xffE9ECF5)
                                                                  : Color(
                                                                      0xff262F3E)),
                                                        ),
                                                  SizedBox(
                                                    height: 3,
                                                  ),
                                                  data.winnig3! == "0"
                                                      ? Text(
                                                          data.winnig3!,
                                                          style: TextStyle(
                                                              fontFamily:
                                                                  "IBMPlexSansThai-Regular",
                                                              fontSize: 14,
                                                              letterSpacing:
                                                                  0.3,
                                                              color: Color(
                                                                  0xff8A96AB)),
                                                        )
                                                      : Text(
                                                          data.winnig3!,
                                                          style: TextStyle(
                                                              fontFamily:
                                                                  "IBMPlexSansThai-Regular",
                                                              fontSize: 14,
                                                              letterSpacing:
                                                                  0.3,
                                                              color: isDarkMode
                                                                  ? Color(
                                                                      0xffE9ECF5)
                                                                  : Color(
                                                                      0xff262F3E)),
                                                        ),
                                                ],
                                              ),
                                            ),
                                            Expanded(
                                                child: Column(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.end,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.end,
                                              children: [
                                                Text(
                                                  'Prize (LIKE)',
                                                  style: TextStyle(
                                                      fontFamily:
                                                          "DINPro-Medium",
                                                      fontSize: 12.5,
                                                      letterSpacing: 0.3,
                                                      color: Color(0xff9760FE)),
                                                ),
                                                SizedBox(
                                                  height: 15,
                                                ),
                                                Text(data.prizeLike1!,
                                                    style: TextStyle(
                                                        fontSize: 14,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                        letterSpacing: 0.3,
                                                        fontFamily:
                                                            "IBMPlexSansThai-Regular",
                                                        color: isDarkMode
                                                            ? Color(0xffE9ECF5)
                                                            : Color(
                                                                0xff1D2532))),
                                                SizedBox(
                                                  height: 3,
                                                ),
                                                Text(data.prizeLike2!,
                                                    style: TextStyle(
                                                        fontSize: 14,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                        letterSpacing: 0.3,
                                                        fontFamily:
                                                            "IBMPlexSansThai-Regular",
                                                        color: isDarkMode
                                                            ? Color(0xffE9ECF5)
                                                            : Color(
                                                                0xff1D2532))),
                                                SizedBox(
                                                  height: 3,
                                                ),
                                                Text(data.prizeLike3!,
                                                    style: TextStyle(
                                                        fontSize: 14,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                        letterSpacing: 0.3,
                                                        fontFamily:
                                                            "IBMPlexSansThai-Regular",
                                                        color: isDarkMode
                                                            ? Color(0xffE9ECF5)
                                                            : Color(
                                                                0xff1D2532))),
                                              ],
                                            )),
                                          ],
                                        ),
                                      ),
                                    ),
                                    SizedBox(
                                      height: 10,
                                    ),
                                    Container(
                                      padding:
                                          EdgeInsets.symmetric(horizontal: 20),
                                      child: Divider(
                                        thickness: 0.5,
                                        color: isDarkMode
                                            ? Color(0xff3E4B5E)
                                            : Color(0xffAAB4C4),
                                      ),
                                    ),
                                    SizedBox(
                                      height: 10,
                                    ),
                                    Container(
                                        padding: EdgeInsets.symmetric(
                                            horizontal: 20),
                                        child: Row(
                                          children: [
                                            Expanded(
                                              flex: 2,
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Row(
                                                    children: [
                                                      Text(
                                                        '3244',
                                                        style: TextStyle(
                                                          fontSize: 12,
                                                          fontFamily:
                                                              "IBMPlexSansThai-Medium",
                                                          letterSpacing: 0.3,
                                                          color:
                                                              Color(0xffEA84FD),
                                                        ),
                                                      ),
                                                      SizedBox(
                                                        width: 5,
                                                      ),
                                                      Text(
                                                        '1234',
                                                        style: TextStyle(
                                                          fontSize: 12,
                                                          fontFamily:
                                                              "IBMPlexSansThai-Medium",
                                                          letterSpacing: 0.3,
                                                          color: Color(
                                                                  0xffEA84FD)
                                                              .withOpacity(0.9),
                                                        ),
                                                      ),
                                                      SizedBox(
                                                        width: 5,
                                                      ),
                                                      Text(
                                                        '8736',
                                                        style: TextStyle(
                                                          fontSize: 12,
                                                          fontFamily:
                                                              "IBMPlexSansThai-Medium",
                                                          letterSpacing: 0.3,
                                                          color: Color(
                                                                  0xffEA84FD)
                                                              .withOpacity(0.8),
                                                        ),
                                                      ),
                                                      SizedBox(
                                                        width: 5,
                                                      ),
                                                      Text(
                                                        '6381',
                                                        style: TextStyle(
                                                          fontSize: 12,
                                                          fontFamily:
                                                              "IBMPlexSansThai-Medium",
                                                          letterSpacing: 0.3,
                                                          color: Color(
                                                                  0xffEA84FD)
                                                              .withOpacity(0.7),
                                                        ),
                                                      ),
                                                      SizedBox(
                                                        width: 5,
                                                      ),
                                                      Text(
                                                        '4830',
                                                        style: TextStyle(
                                                          fontSize: 12,
                                                          fontFamily:
                                                              "IBMPlexSansThai-Medium",
                                                          letterSpacing: 0.3,
                                                          color: Color(
                                                                  0xffEA84FD)
                                                              .withOpacity(0.6),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  SizedBox(
                                                    height: 4,
                                                  ),
                                                  Row(
                                                    children: [
                                                      Text(
                                                        '8921',
                                                        style: TextStyle(
                                                          fontSize: 12,
                                                          fontFamily:
                                                              "IBMPlexSansThai-Medium",
                                                          letterSpacing: 0.3,
                                                          color:
                                                              Color(0xff9760FE),
                                                        ),
                                                      ),
                                                      SizedBox(
                                                        width: 5,
                                                      ),
                                                      Text(
                                                        '3244',
                                                        style: TextStyle(
                                                          fontSize: 12,
                                                          fontFamily:
                                                              "IBMPlexSansThai-Medium",
                                                          letterSpacing: 0.3,
                                                          color: Color(
                                                                  0xff9760FE)
                                                              .withOpacity(0.9),
                                                        ),
                                                      ),
                                                      SizedBox(
                                                        width: 5,
                                                      ),
                                                      Text(
                                                        '1720',
                                                        style: TextStyle(
                                                          fontSize: 12,
                                                          fontFamily:
                                                              "IBMPlexSansThai-Medium",
                                                          letterSpacing: 0.3,
                                                          color: Color(
                                                                  0xff9760FE)
                                                              .withOpacity(0.8),
                                                        ),
                                                      ),
                                                      SizedBox(
                                                        width: 5,
                                                      ),
                                                      Text(
                                                        '5590',
                                                        style: TextStyle(
                                                          fontSize: 12,
                                                          fontFamily:
                                                              "IBMPlexSansThai-Medium",
                                                          letterSpacing: 0.3,
                                                          color: Color(
                                                                  0xff9760FE)
                                                              .withOpacity(0.7),
                                                        ),
                                                      ),
                                                      SizedBox(
                                                        width: 5,
                                                      ),
                                                      Text(
                                                        '0239',
                                                        style: TextStyle(
                                                          fontSize: 12,
                                                          fontFamily:
                                                              "IBMPlexSansThai-Medium",
                                                          letterSpacing: 0.3,
                                                          color: Color(
                                                                  0xff9760FE)
                                                              .withOpacity(0.6),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ],
                                              ),
                                            ),
                                            Expanded(
                                              child: Column(
                                                children: [
                                                  Container(
                                                    alignment:
                                                        Alignment.centerRight,
                                                    margin: EdgeInsets.only(
                                                        right: 0.5),
                                                    child: Text(
                                                      'Your Numbers',
                                                      style: TextStyle(
                                                          fontSize: 12,
                                                          fontFamily:
                                                              "IBMPlexSansThai-Medium",
                                                          letterSpacing: 0.3,
                                                          color: isDarkMode
                                                              ? Color(
                                                                  0xff8A96AB)
                                                              : Color(
                                                                  0xff4E5D75)),
                                                    ),
                                                  ),
                                                  SizedBox(height: 4),
                                                  Container(
                                                    alignment:
                                                        Alignment.centerRight,
                                                    child: Text(
                                                      '10',
                                                      style: TextStyle(
                                                        fontSize: 12,
                                                        fontFamily:
                                                            "IBMPlexSansThai-Medium",
                                                        fontWeight:
                                                            FontWeight.w500,
                                                        letterSpacing: 0.3,
                                                        color: isDarkMode
                                                            ? Color(0xffCAD2DE)
                                                            : Color(0xff262F3E),
                                                      ),
                                                    ),
                                                  )
                                                ],
                                              ),
                                            ),
                                          ],
                                        )),
                                    SizedBox(
                                      height: 15,
                                    ),
                                  ],
                                ),
                              );
                            },
                          )
                        ],
                      ),
                    ))
              ],
            ),
          ),
        ),
      ),
    );
  }
}
