import 'dart:async';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_countdown_timer/current_remaining_time.dart';
import 'package:flutter_countdown_timer/index.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:lock_to_win/controller/lottery.dart';
import 'package:lock_to_win/main.dart';
import 'package:lock_to_win/models/lotto/lotto_now_round.dart';
import 'package:lock_to_win/models/lotto/your_lotto_list.dart';
import 'package:lock_to_win/models/lotto/your_lotto_list_pre_round.dart';
import 'package:lock_to_win/screens/component.dart';
import 'package:lock_to_win/widgets/color.dart';
import 'package:lock_to_win/widgets/loading.dart';
import 'package:lottie/lottie.dart';
import 'package:lock_to_win/config/dark_mode_colors.dart';
import 'package:lock_to_win/config/light_mode_colors.dart';
// import 'package:xlite/main.dart';
import 'package:lock_to_win/models/ballon2.dart';
import 'package:lock_to_win/screens/game-rules_screen.dart';
import 'package:lock_to_win/screens/lock.dart';
import 'package:lock_to_win/widgets/ballon.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:lock_to_win/widgets/glass_morphism.dart';

class LockToWinChoosenScreen extends StatefulWidget {
  LockToWinChoosenScreen({Key? key, this.image}) : super(key: key);
  String? image;
  @override
  State<LockToWinChoosenScreen> createState() => _LockToWinChoosenScreenState();
}

class _LockToWinChoosenScreenState extends State<LockToWinChoosenScreen>
    with SingleTickerProviderStateMixin {
  final storeController = Get.find<LotteryController>();
  Future<double>? _futureBalanceTicket;
  Future<List>? _futureRoundNow;
  Future<YourLottoList>? _futureYourLottoRound;
  @override
  void initState() {
    super.initState();
    setDataRandom();
    _futureRoundNow = roundNow();
  }

  Future<List>? roundNow() async {
    final data1 = await storeController.getRoundNow();
    final data2 = await storeController.getNowRoundReward();
    final data3 = await storeController.getAnnoncesTime();
    return [data1, data2, data3];
  }

  Map<int, String> monthsInYear = {
    1: "Jan",
    2: "February",
    3: "March",
    4: "April",
    5: "May",
    6: "June",
    12: "December"
  };
  double _currentSliderValue = 0;

  late final AnimationController _controller = AnimationController(
    duration: const Duration(seconds: 2),
    vsync: this,
  );
  late final Animation<Offset> _offsetAnimationRight = Tween<Offset>(
    begin: const Offset(0, 0.0),
    end: const Offset(1, 0.0),
  ).animate(CurvedAnimation(
    parent: _controller,
    curve: Curves.fastLinearToSlowEaseIn,
  ));

  late final Animation<Offset> _offsetAnimationLeft = Tween<Offset>(
    begin: const Offset(0, 0.0),
    end: const Offset(-1, 0.0),
  ).animate(CurvedAnimation(
    parent: _controller,
    curve: Curves.fastLinearToSlowEaseIn,
  ));

  late final Animation<Offset> _offsetAnimationLeftIn = Tween<Offset>(
    begin: const Offset(-5, 0),
    end: const Offset(0, 0.0),
  ).animate(CurvedAnimation(
    parent: _controller,
    curve: Curves.fastLinearToSlowEaseIn,
  ));

  late final Animation<Offset> _offsetAnimationRightIn = Tween<Offset>(
    begin: const Offset(1, 0),
    end: const Offset(0, 0.0),
  ).animate(CurvedAnimation(
    parent: _controller,
    curve: Curves.fastLinearToSlowEaseIn,
  ));

  List<Ballon2> _selectedViewDog = [];
  List<Ballon2> _unselected = [];

  List<Ballon2> _unselected1 = [];
  List numberSelect = [];
  final _unselectedListKey = GlobalKey<AnimatedListState>();
  final _selectedListKey = GlobalKey<AnimatedListState>();

  final _unselectedListKey1 = GlobalKey<AnimatedListState>();
  final _selectedListKey1 = GlobalKey<AnimatedListState>();
  final _selectedListKey2 = GlobalKey<AnimatedListState>();
  final _selectedListKey3 = GlobalKey<AnimatedListState>();

  List<Ballon2> ballon2data = [];
  bool isShowView = false;
  bool isButtonChange = false;
  bool isRange = false;
  bool isGrigeList = false;
  bool isHideButtonAndNavigate = false;
  DateTime selectedDate = DateTime.now();
  bool isGenAllMyNumbers = true;
  bool isPageChange = false;
  bool isDogView = false;

  setDataRandom() async {
    _futureBalanceTicket = storeController.getBalanceTicket();
    if (storeController.yourLottoList.length > 0) {
      for (var i = 0; i < storeController.yourLottoList.length; i++) {
        // print(storeController.yourLottoList[i].number);
        _selectedViewDog.add(Ballon2(
          number: '${storeController.yourLottoList[i]}',
          gradient: ColorTheme.linearGradientRandom20[i],
          textColor: ColorTheme.textColorRandom20[i],
        ));
      }
      _controller.forward();
      Future.delayed(Duration(milliseconds: 500), () {
        _controller.forward();
        isDogView = true;
        isGrigeList = true;
        isGenAllMyNumbers = false;
      });
      setState(() {});
    } else {
      final random20 = await storeController.randomNumber20();
      await storeController.addRandomRow(random20);
      storeController.setRandomNumberSuccess(true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<LotteryController>(
      init: LotteryController(),
      builder: (controller) => SafeArea(
        bottom: false,
        top: false,
        child: Scaffold(
          body: SafeArea(
            bottom: false,
            top: false,
            child: Container(
              color: isDarkMode ? Color(0xff11161E) : Color(0xffEFF1F4),
              width: Get.width,
              height: Get.height,
              child: Column(
                children: [
                  Container(
                    padding: EdgeInsets.only(
                      left: 12,
                      top: Get.height * 0.05,
                      right: 20,
                      bottom: 7,
                    ),
                    decoration: BoxDecoration(
                      color: isDarkMode ? Color(0xff1D2532) : Color(0xffFEFEFE),
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(20),
                        bottomRight: Radius.circular(20),
                      ),
                    ),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        SvgPicture.asset(
                          widget.image!,
                          height: 114,
                          package: 'lock_to_win',
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Container(
                              padding: EdgeInsets.only(
                                right: 2,
                              ),
                              child: Text(
                                AppLocalizations.of(context)!.translate('ltw_your_ticket'),
                                style: TextStyle(
                                  fontSize: 14,
                                  letterSpacing: 0.3,
                                  fontFamily: "IBMPlexSansThai-Regular",
                                  color: isDarkMode
                                      ? textGray300Dark
                                      : textGray300Light,
                                ),
                              ),
                            ),
                            FutureBuilder(
                              future: _futureBalanceTicket,
                              builder: (
                                BuildContext context,
                                AsyncSnapshot<double> snapshot,
                              ) {
                                print(snapshot.connectionState);
                                if (snapshot.connectionState ==
                                    ConnectionState.waiting) {
                                  return Center(
                                    child: SpinKitThreeBounce(
                                      color: Colors.grey,
                                      size: 20,
                                    ),
                                  );
                                } else if (snapshot.connectionState ==
                                    ConnectionState.done) {
                                  if (snapshot.hasError) {
                                    return Text(AppLocalizations.of(context)!.translate('ltw_error'));
                                  } else if (snapshot.hasData) {
                                    return Container(
                                      padding: EdgeInsets.only(
                                        right: 2,
                                      ),
                                      child: Text(
                                        '${snapshot.data!.toStringAsFixed(0)}',
                                        style: TextStyle(
                                            fontSize: 24,
                                            fontFamily: 'SFPro',
                                            letterSpacing: 0.3,
                                            fontWeight: FontWeight.w600,
                                            color: isDarkMode
                                                ? Color(0xffE9ECF5)
                                                : Colors.black),
                                      ),
                                    );
                                  } else {
                                    return Text(AppLocalizations.of(context)!.translate('ltw_empty_data'));
                                  }
                                } else {
                                  return Text(
                                      AppLocalizations.of(context)!.translate('ltw_state')+': ${snapshot.connectionState}');
                                }
                              },
                            ),
                            SizedBox(
                              height: 10,
                            ),
                            Container(
                              padding: EdgeInsets.only(
                                right: 2,
                              ),
                              child: Text(
                                AppLocalizations.of(context)!.translate('ltw_more_ticket'),
                                style: TextStyle(
                                  fontSize: 14,
                                  letterSpacing: 0.3,
                                  fontFamily: "IBMPlexSansThai-Regular",
                                  color: isDarkMode
                                      ? textGray300Dark
                                      : textGray300Light,
                                ),
                              ),
                            ),
                            SizedBox(
                              height: 3,
                            ),
                            Row(
                              children: [
                                // Image.asset(
                                //   'assets/images/Group 36526.png',
                                //   height: 17,
                                // ),
                                Container(
                                    padding: EdgeInsets.only(
                                      right: 4,
                                    ),
                                    height: 38,
                                    child: isDarkMode
                                        ? Lottie.asset(
                                            'assets/animatedIcons/Animated Lock.json',
                                            package: 'lock_to_win',
                                          )
                                        : Lottie.asset(
                                            'assets/animatedIcons/Animated Lock_LM.json',
                                            package: 'lock_to_win',
                                          )),
                                // SizedBox(
                                //   width: 5,
                                // ),
                                Container(
                                  padding: EdgeInsets.only(
                                    right: 2,
                                  ),
                                  child: InkWell(
                                    onTap: () {
                                      Loading.typeLoadingError(AppLocalizations.of(context)!.translate('ltw_coming_soon'));
                                    },
                                    // onTap: () {
                                    //   showModalBottomSheet(
                                    //     backgroundColor: isDarkMode
                                    //         ? Color(0xff1D2532)
                                    //         : Color(0xffEFF1F4),
                                    //     isScrollControlled: true,
                                    //     constraints:
                                    //         BoxConstraints(maxHeight: 550),
                                    //     shape: RoundedRectangleBorder(
                                    //       borderRadius: BorderRadius.only(
                                    //         topLeft: Radius.circular(20),
                                    //         topRight: Radius.circular(20),
                                    //       ),
                                    //     ),
                                    //     context: context,
                                    //     builder: (context) {
                                    //       return StatefulBuilder(
                                    //         builder: (BuildContext context,
                                    //             StateSetter setState) {
                                    //           return Stack(
                                    //             children: [
                                    //               Container(
                                    //                 decoration: BoxDecoration(
                                    //                     color: isDarkMode
                                    //                         ? Color(0xff161C26)
                                    //                         : Colors.white,
                                    //                     borderRadius:
                                    //                         BorderRadius.only(
                                    //                             topRight: Radius
                                    //                                 .circular(
                                    //                                     20.0),
                                    //                             topLeft: Radius
                                    //                                 .circular(
                                    //                                     20.0))),
                                    //                 padding: EdgeInsets.only(
                                    //                   top: 10,
                                    //                 ),
                                    //                 width: Get.width,
                                    //                 child: Column(
                                    //                   children: [
                                    //                     Stack(
                                    //                       children: [
                                    //                         Container(
                                    //                             alignment:
                                    //                                 Alignment
                                    //                                     .center,
                                    //                             margin: EdgeInsets
                                    //                                 .only(
                                    //                                     top:
                                    //                                         20),
                                    //                             child: RichText(
                                    //                               text:
                                    //                                   TextSpan(
                                    //                                 text:
                                    //                                     'You will get ',
                                    //                                 style:
                                    //                                     TextStyle(
                                    //                                   fontSize:
                                    //                                       12,
                                    //                                   fontFamily:
                                    //                                       "IBMPlexSansThai-Regular",
                                    //                                   letterSpacing:
                                    //                                       0.3,
                                    //                                   color: isDarkMode
                                    //                                       ? Color(
                                    //                                           0xff8A96AB)
                                    //                                       : Color(
                                    //                                           0xff4E5D75),
                                    //                                 ),
                                    //                                 children: <
                                    //                                     TextSpan>[
                                    //                                   TextSpan(
                                    //                                       text:
                                    //                                           '1 ticket/100,000',
                                    //                                       style:
                                    //                                           TextStyle(
                                    //                                         fontSize:
                                    //                                             12,
                                    //                                         fontFamily:
                                    //                                             "IBMPlexSansThai-Regular",
                                    //                                         letterSpacing:
                                    //                                             0.3,
                                    //                                         color: isDarkMode
                                    //                                             ? Color(0xffE9ECF5)
                                    //                                             : Color(0xff262F3E),
                                    //                                       )),
                                    //                                   TextSpan(
                                    //                                       text:
                                    //                                           ' Locked Likepoint.',
                                    //                                       style:
                                    //                                           TextStyle(
                                    //                                         fontSize:
                                    //                                             12,
                                    //                                         fontFamily:
                                    //                                             "IBMPlexSansThai-Regular",
                                    //                                         color: isDarkMode
                                    //                                             ? Color(0xff8A96AB)
                                    //                                             : Color(0xff4E5D75),
                                    //                                       )),
                                    //                                 ],
                                    //                               ),
                                    //                             )),
                                    //                         Container(
                                    //                           margin: EdgeInsets
                                    //                               .only(
                                    //                                   right:
                                    //                                       15),
                                    //                           child: InkWell(
                                    //                             onTap: () {
                                    //                               Navigator.pop(
                                    //                                   context);
                                    //                             },
                                    //                             child:
                                    //                                 Container(
                                    //                               margin:
                                    //                                   EdgeInsets
                                    //                                       .all(
                                    //                                           10),
                                    //                               alignment:
                                    //                                   Alignment
                                    //                                       .centerRight,
                                    //                               child: Image
                                    //                                   .asset(
                                    //                                 'assets/images/Group 36610.png',
                                    //                                 package:
                                    //                                     'lock_to_win',
                                    //                                 height: 20,
                                    //                               ),
                                    //                             ),
                                    //                           ),
                                    //                         ),
                                    //                       ],
                                    //                     ),
                                    //                     SizedBox(
                                    //                       height: 5,
                                    //                     ),
                                    //                     Container(
                                    //                       child: InkWell(
                                    //                         onTap: () {
                                    //                           Get.to(
                                    //                             GameRulesScreen(),
                                    //                             transition:
                                    //                                 Transition
                                    //                                     .rightToLeft,
                                    //                           );
                                    //                         },
                                    //                         child: Container(
                                    //                           child: RichText(
                                    //                             text: TextSpan(
                                    //                               text:
                                    //                                   'How many tickets will you get? ',
                                    //                               style:
                                    //                                   TextStyle(
                                    //                                 fontSize:
                                    //                                     12,
                                    //                                 fontFamily:
                                    //                                     "IBMPlexSansThai-Regular",
                                    //                                 letterSpacing:
                                    //                                     0.3,
                                    //                                 color: isDarkMode
                                    //                                     ? Color(
                                    //                                         0xff8A96AB)
                                    //                                     : Color(
                                    //                                         0xff4E5D75),
                                    //                               ),
                                    //                               children: <
                                    //                                   TextSpan>[
                                    //                                 TextSpan(
                                    //                                     text:
                                    //                                         'Slide to view…',
                                    //                                     style:
                                    //                                         TextStyle(
                                    //                                       fontSize:
                                    //                                           12,
                                    //                                       fontFamily:
                                    //                                           "IBMPlexSansThai-Regular",
                                    //                                       letterSpacing:
                                    //                                           0.3,
                                    //                                       fontWeight:
                                    //                                           FontWeight.bold,
                                    //                                       color:
                                    //                                           Color(0xff0078FF),
                                    //                                     )),
                                    //                               ],
                                    //                             ),
                                    //                           ),
                                    //                         ),
                                    //                       ),
                                    //                     ),
                                    //                     SizedBox(
                                    //                       height: 5.0,
                                    //                     ),
                                    //                   ],
                                    //                 ),
                                    //               ),
                                    //               Container(
                                    //                 height: Get.height * 0.11,
                                    //                 width: Get.width,
                                    //                 margin: EdgeInsets.only(
                                    //                     top: 100),
                                    //                 decoration: BoxDecoration(
                                    //                   borderRadius:
                                    //                       BorderRadius.circular(
                                    //                           20.0),
                                    //                   gradient: LinearGradient(
                                    //                     colors: isDarkMode
                                    //                         ? [
                                    //                             Color(0xff009AFF)
                                    //                                 .withOpacity(
                                    //                                     0.6),
                                    //                             Color(0xff1564EC)
                                    //                                 .withOpacity(
                                    //                                     0.6),
                                    //                           ]
                                    //                         : [
                                    //                             Color(
                                    //                                 0xff009AFF),
                                    //                             Color(
                                    //                                 0xff1564EC),
                                    //                           ],
                                    //                   ),
                                    //                 ),
                                    //                 child: Container(
                                    //                   margin: EdgeInsets.only(
                                    //                       top: 11),
                                    //                   alignment:
                                    //                       Alignment.topCenter,
                                    //                   child: Text(
                                    //                     'You will get',
                                    //                     style: TextStyle(
                                    //                       fontSize: 16,
                                    //                       fontFamily:
                                    //                           "IBMPlexSansThai-Regular",
                                    //                       letterSpacing: 0.3,
                                    //                       color: isDarkMode
                                    //                           ? Color(
                                    //                               0xff00BCFC)
                                    //                           : Color(
                                    //                               0xff7ED9FD),
                                    //                     ),
                                    //                   ),
                                    //                 ),
                                    //               ),
                                    //               Container(
                                    //                 margin: EdgeInsets.only(
                                    //                     top: 145),
                                    //                 padding: EdgeInsets.only(
                                    //                     bottom: 5.0),
                                    //                 decoration: BoxDecoration(
                                    //                   boxShadow: [
                                    //                     BoxShadow(
                                    //                         color: Color(
                                    //                                 0xff000000)
                                    //                             .withOpacity(
                                    //                                 0.20),
                                    //                         // spreadRadius: 10,
                                    //                         blurRadius: 16,
                                    //                         offset:
                                    //                             Offset(0, -10)),
                                    //                   ],
                                    //                   borderRadius:
                                    //                       BorderRadius.only(
                                    //                           topLeft: Radius
                                    //                               .circular(20),
                                    //                           topRight: Radius
                                    //                               .circular(
                                    //                                   20)),
                                    //                   gradient: LinearGradient(
                                    //                     colors: isDarkMode
                                    //                         ? [
                                    //                             Color(
                                    //                                 0xff030406),
                                    //                             Color(
                                    //                                 0xff161C26),
                                    //                           ]
                                    //                         : [
                                    //                             Color(
                                    //                                 0xffEFF1F4),
                                    //                             Color(
                                    //                                 0xffFEFEFE),
                                    //                           ],
                                    //                     begin:
                                    //                         Alignment.topCenter,
                                    //                     end: Alignment
                                    //                         .bottomCenter,
                                    //                   ),
                                    //                 ),
                                    //                 child:
                                    //                     SingleChildScrollView(
                                    //                   child: Column(
                                    //                     children: [
                                    //                       SizedBox(
                                    //                           height: 30.0),
                                    //                       Container(
                                    //                         // width: Get.width,
                                    //                         child: Stack(
                                    //                           children: [
                                    //                             Container(
                                    //                               // width: Get.width *
                                    //                               //     0.5,
                                    //                               child: Stack(
                                    //                                 children: [
                                    //                                   Container(
                                    //                                     height: Get.height *
                                    //                                         0.09,
                                    //                                     width: Get.width *
                                    //                                         0.2,
                                    //                                     decoration:
                                    //                                         BoxDecoration(
                                    //                                       shape:
                                    //                                           BoxShape.circle,
                                    //                                       gradient: LinearGradient(
                                    //                                           begin: Alignment.centerRight,
                                    //                                           end: Alignment.centerLeft,
                                    //                                           colors: [
                                    //                                             Color(0xff0078FF).withOpacity(0.1),
                                    //                                             Color(0xff0078FF).withOpacity(0.0),
                                    //                                           ]),
                                    //                                     ),
                                    //                                   ),
                                    //                                   Container(
                                    //                                     margin: EdgeInsets.only(
                                    //                                         left:
                                    //                                             50.0),
                                    //                                     height: Get.height *
                                    //                                         0.09,
                                    //                                     width: Get.width *
                                    //                                         0.2,
                                    //                                     decoration:
                                    //                                         BoxDecoration(
                                    //                                       shape:
                                    //                                           BoxShape.circle,
                                    //                                       gradient: LinearGradient(
                                    //                                           begin: Alignment.centerRight,
                                    //                                           end: Alignment.centerLeft,
                                    //                                           colors: [
                                    //                                             Color(0xff0078FF).withOpacity(0.3),
                                    //                                             Color(0xff0078FF).withOpacity(0.0),
                                    //                                           ]),
                                    //                                     ),
                                    //                                   ),
                                    //                                   Container(
                                    //                                     margin: EdgeInsets.only(
                                    //                                         left:
                                    //                                             125.0),
                                    //                                     height: Get.height *
                                    //                                         0.09,
                                    //                                     width: Get.width *
                                    //                                         0.2,
                                    //                                     decoration:
                                    //                                         BoxDecoration(
                                    //                                       shape:
                                    //                                           BoxShape.circle,
                                    //                                       gradient: LinearGradient(
                                    //                                           begin: Alignment.centerLeft,
                                    //                                           end: Alignment.centerRight,
                                    //                                           colors: [
                                    //                                             Color(0xff0078FF).withOpacity(0.3),
                                    //                                             Color(0xff0078FF).withOpacity(0),
                                    //                                           ]),
                                    //                                     ),
                                    //                                   ),
                                    //                                   Container(
                                    //                                     margin: EdgeInsets.only(
                                    //                                         left:
                                    //                                             180.0),
                                    //                                     height: Get.height *
                                    //                                         0.09,
                                    //                                     width: Get.width *
                                    //                                         0.2,
                                    //                                     decoration:
                                    //                                         BoxDecoration(
                                    //                                       shape:
                                    //                                           BoxShape.circle,
                                    //                                       gradient: LinearGradient(
                                    //                                           begin: Alignment.centerLeft,
                                    //                                           end: Alignment.centerRight,
                                    //                                           colors: [
                                    //                                             Color(0xff0078FF).withOpacity(0.1),
                                    //                                             Color(0xff0078FF).withOpacity(0.0),
                                    //                                           ]),
                                    //                                     ),
                                    //                                   ),
                                    //                                   Container(
                                    //                                     margin:
                                    //                                         EdgeInsets.only(
                                    //                                       left:
                                    //                                           90.0,
                                    //                                       // right:
                                    //                                       //     80.0
                                    //                                     ),
                                    //                                     height: Get.height *
                                    //                                         0.09,
                                    //                                     width: Get.width *
                                    //                                         0.2,
                                    //                                     decoration:
                                    //                                         BoxDecoration(
                                    //                                       shape:
                                    //                                           BoxShape.circle,
                                    //                                       gradient: LinearGradient(
                                    //                                           begin: Alignment.center,
                                    //                                           end: Alignment.center,
                                    //                                           colors: [
                                    //                                             Color(0xff0078FF).withOpacity(0.1),
                                    //                                             Color(0xff0078FF).withOpacity(0.1),
                                    //                                           ]),
                                    //                                     ),
                                    //                                     child:
                                    //                                         Center(
                                    //                                       child:
                                    //                                           Text(
                                    //                                         _currentSliderValue.toInt().toString(),
                                    //                                         style:
                                    //                                             TextStyle(
                                    //                                           fontSize: 38,
                                    //                                           color: isDarkMode ? Color(0xffE9ECF5) : Color(0xff262F3E),
                                    //                                         ),
                                    //                                       ),
                                    //                                     ),
                                    //                                   ),
                                    //                                 ],
                                    //                               ),
                                    //                             ),
                                    //                           ],
                                    //                         ),
                                    //                       ),
                                    //                       SizedBox(height: 10),
                                    //                       RichText(
                                    //                         text: TextSpan(
                                    //                           text:
                                    //                               '“Lock to Win” ',
                                    //                           style: TextStyle(
                                    //                             fontSize: 14,
                                    //                             fontFamily:
                                    //                                 "IBMPlexSansThai-Regular",
                                    //                             letterSpacing:
                                    //                                 0.3,
                                    //                             fontWeight:
                                    //                                 FontWeight
                                    //                                     .bold,
                                    //                             color: Color(
                                    //                                 0xff0078FF),
                                    //                           ),
                                    //                           children: <
                                    //                               TextSpan>[
                                    //                             TextSpan(
                                    //                               text:
                                    //                                   'Tickets',
                                    //                               style:
                                    //                                   TextStyle(
                                    //                                 fontFamily:
                                    //                                     "IBMPlexSansThai-Regular",
                                    //                                 letterSpacing:
                                    //                                     0.3,
                                    //                                 color: Color(
                                    //                                     0xff0078FF),
                                    //                                 fontSize:
                                    //                                     14,
                                    //                               ),
                                    //                             ),
                                    //                           ],
                                    //                         ),
                                    //                       ),
                                    //                       SizedBox(
                                    //                         height: 15.0,
                                    //                       ),
                                    //                       Container(
                                    //                         margin:
                                    //                             EdgeInsets.only(
                                    //                                 top: 10,
                                    //                                 left: 36,
                                    //                                 right: 36),
                                    //                         width: Get.width,
                                    //                         child: Row(
                                    //                           mainAxisAlignment:
                                    //                               MainAxisAlignment
                                    //                                   .spaceBetween,
                                    //                           children: [
                                    //                             Text(
                                    //                               'Locked Amount (LIKE)',
                                    //                               style: TextStyle(
                                    //                                   fontFamily:
                                    //                                       "IBMPlexSansThai-Regular",
                                    //                                   letterSpacing:
                                    //                                       0.3,
                                    //                                   color: isDarkMode
                                    //                                       ? Color(
                                    //                                           0xff738097)
                                    //                                       : Color(
                                    //                                           0xff5B6B85),
                                    //                                   fontSize:
                                    //                                       12),
                                    //                             ),
                                    //                             SizedBox(
                                    //                               height: 50,
                                    //                             ),
                                    //                             Text(
                                    //                               '100,000,000',
                                    //                               style:
                                    //                                   TextStyle(
                                    //                                 fontSize:
                                    //                                     14,
                                    //                                 fontFamily:
                                    //                                     "IBMPlexSansThai-Regular",
                                    //                                 fontWeight:
                                    //                                     FontWeight
                                    //                                         .bold,
                                    //                                 letterSpacing:
                                    //                                     0.3,
                                    //                                 color: isDarkMode
                                    //                                     ? Color(
                                    //                                         0xffE9ECF5)
                                    //                                     : Color(
                                    //                                         0xff262F3E),
                                    //                               ),
                                    //                             ),
                                    //                           ],
                                    //                         ),
                                    //                       ),
                                    //                       Container(
                                    //                         padding:
                                    //                             EdgeInsets.only(
                                    //                                 left: 15,
                                    //                                 right: 15),
                                    //                         child: SliderTheme(
                                    //                           data: SliderThemeData(
                                    //                               thumbColor: Color(
                                    //                                   0xff009AFF),
                                    //                               inactiveTrackColor: isDarkMode
                                    //                                   ? Color(
                                    //                                       0xff1D2532)
                                    //                                   : Color(0xff1D2532)
                                    //                                       .withOpacity(
                                    //                                           0.2),
                                    //                               activeTrackColor: Color(
                                    //                                       0xff0078FF)
                                    //                                   .withOpacity(
                                    //                                       0.2),
                                    //                               overlayColor: Color(
                                    //                                       0xff1AAEFC)
                                    //                                   .withOpacity(
                                    //                                       0.15),
                                    //                               thumbShape: RoundSliderThumbShape(
                                    //                                   enabledThumbRadius:
                                    //                                       10.0),
                                    //                               overlayShape:
                                    //                                   RoundSliderOverlayShape(
                                    //                                       overlayRadius:
                                    //                                           20.0)),
                                    //                           child: Slider(
                                    //                             value:
                                    //                                 _currentSliderValue,
                                    //                             max: 10,
                                    //                             onChanged:
                                    //                                 (double
                                    //                                     value) {
                                    //                               _currentSliderValue =
                                    //                                   value;
                                    //                               setState(
                                    //                                   () {});
                                    //                             },
                                    //                           ),
                                    //                         ),
                                    //                       ),
                                    //                       SizedBox(height: 30),
                                    //                       InkWell(
                                    //                         onTap: () {
                                    //                           // Get.to(() =>
                                    //                           //     LockScrenn());
                                    //
                                    //                           showDialog(
                                    //                             context:
                                    //                                 context,
                                    //                             builder:
                                    //                                 (BuildContext
                                    //                                     context) {
                                    //                               return AlertDialog(
                                    //                                   contentPadding:
                                    //                                       EdgeInsets.only(
                                    //                                           top:
                                    //                                               10.0),
                                    //                                   // alignment: Alignment.center,
                                    //                                   content:
                                    //                                       Container(
                                    //                                     height:
                                    //                                         50,
                                    //                                     child: Text(
                                    //                                         'กำลังปรับปรุง',
                                    //                                         style:
                                    //                                             TextStyle(fontSize: 18)),
                                    //                                     alignment:
                                    //                                         Alignment.center,
                                    //                                   ));
                                    //                             },
                                    //                           );
                                    //                         },
                                    //                         child: Container(
                                    //                           alignment:
                                    //                               Alignment
                                    //                                   .center,
                                    //                           margin: EdgeInsets.only(
                                    //                               top: 8,
                                    //                               bottom: 20,
                                    //                               right:
                                    //                                   Get.width *
                                    //                                       0.1,
                                    //                               left:
                                    //                                   Get.width *
                                    //                                       0.1),
                                    //                           height: 40,
                                    //                           decoration:
                                    //                               BoxDecoration(
                                    //                             gradient: isDarkMode
                                    //                                 ? LinearGradient(
                                    //                                     begin: Alignment
                                    //                                         .centerLeft,
                                    //                                     end: Alignment
                                    //                                         .centerRight,
                                    //                                     colors: [
                                    //                                       Color(
                                    //                                           0xff1AAEFC),
                                    //                                       Color(
                                    //                                           0xff3838E8),
                                    //                                     ],
                                    //                                   )
                                    //                                 : LinearGradient(
                                    //                                     begin: Alignment
                                    //                                         .centerLeft,
                                    //                                     end: Alignment
                                    //                                         .centerRight,
                                    //                                     colors: [
                                    //                                       Color(
                                    //                                           0xff384AE8),
                                    //                                       Color(
                                    //                                           0xff384AE8),
                                    //                                     ],
                                    //                                   ),
                                    //                             boxShadow:
                                    //                                 isDarkMode
                                    //                                     ? []
                                    //                                     : [
                                    //                                         BoxShadow(
                                    //                                           color: Color(0xff384AE8).withOpacity(0.25),
                                    //                                           blurRadius: 9.0,
                                    //                                           offset: Offset(0.1, 15),
                                    //                                         ),
                                    //                                       ],
                                    //                             borderRadius:
                                    //                                 BorderRadius
                                    //                                     .circular(
                                    //                                         60),
                                    //                           ),
                                    //                           child: RichText(
                                    //                             text: TextSpan(
                                    //                                 text:
                                    //                                     'Go to ',
                                    //                                 style:
                                    //                                     TextStyle(
                                    //                                   fontSize:
                                    //                                       14,
                                    //                                   letterSpacing:
                                    //                                       0.30,
                                    //                                   fontFamily:
                                    //                                       "IBMPlexSansThai-Regular",
                                    //                                   color: Color(
                                    //                                       0xffEBEDFD),
                                    //                                 ),
                                    //                                 children: <
                                    //                                     TextSpan>[
                                    //                                   TextSpan(
                                    //                                     text:
                                    //                                         'Lock&Earn',
                                    //                                     style:
                                    //                                         TextStyle(
                                    //                                       fontWeight:
                                    //                                           FontWeight.bold,
                                    //                                       fontFamily:
                                    //                                           "IBMPlexSansThai-Regular",
                                    //                                       color:
                                    //                                           Color(0xffEBEDFD),
                                    //                                       fontSize:
                                    //                                           14,
                                    //                                     ),
                                    //                                   ),
                                    //                                   TextSpan(
                                    //                                       text:
                                    //                                           ' to lock more',
                                    //                                       style:
                                    //                                           TextStyle(
                                    //                                         fontSize:
                                    //                                             14,
                                    //                                         fontFamily:
                                    //                                             "IBMPlexSansThai-Regular",
                                    //                                         color:
                                    //                                             Color(0xffEBEDFD),
                                    //                                       ))
                                    //                                 ]),
                                    //                           ),
                                    //                         ),
                                    //                       ),
                                    //                       SizedBox(height: 55),
                                    //                     ],
                                    //                   ),
                                    //                 ),
                                    //               ),
                                    //             ],
                                    //           );
                                    //         },
                                    //       );
                                    //     },
                                    //   );
                                    // },
                                    child: Container(
                                      alignment: Alignment.center,
                                      height: 25,
                                      width: 105,
                                      decoration: BoxDecoration(
                                        color: isDarkMode
                                            ? Color(0xff020202)
                                            : Color(0xffE9ECF5),
                                        borderRadius: BorderRadius.circular(50),
                                        boxShadow: isDarkMode
                                            ? <BoxShadow>[
                                                BoxShadow(
                                                  color: Colors.black
                                                      .withOpacity(0.12),
                                                  blurRadius: 3,
                                                  offset: Offset(0.0, 2),
                                                )
                                              ]
                                            : [],

                                        // boxShadow:  <BoxShadow>[
                                        //   BoxShadow(
                                        //     color: Colors.black.withOpacity(0.2),
                                        //     blurRadius: 5.0,
                                        //     offset: Offset(0.0, 5),
                                        //   )
                                        //   ],
                                      ),
                                      child: Text(
                                        AppLocalizations.of(context)!.translate('ltw_lock_more'),
                                        style: GoogleFonts.ibmPlexSansThai(
                                          fontSize: 12,
                                          fontWeight: FontWeight.w600,
                                          letterSpacing: 1.5,
                                          color: Color(0xff0078FD),
                                        ),
                                      ),
                                    ),
                                  ),
                                )
                              ],
                            )
                          ],
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Container(
                      child: Column(
                        children: [
                          //   InkWell(
                          //   onTap: ()  => setState(() => _selectedViewDog.add(Ballon2(
                          //       number: '0924',
                          //       textColor: Color(0xff000000),
                          //       style: TextStyle(
                          //         shadows: <Shadow>[
                          //           Shadow(
                          //             offset: Offset(10.0, 10.0),
                          //             blurRadius: 3.0,
                          //             color: Color.fromARGB(255, 0, 0, 0),
                          //           ),
                          //           Shadow(
                          //             offset: Offset(10.0, 10.0),
                          //             blurRadius: 8.0,
                          //             color: Colors.black54,
                          //           ),
                          //         ],
                          //       ),
                          //       gradient: LinearGradient(
                          //         colors: [
                          //           Color(0xffFDC441),
                          //           Color(0xffFF9300),
                          //         ],
                          //         begin: Alignment.topCenter,
                          //         end: Alignment.bottomCenter,
                          //       )))),
                          //   child: Icon(Icons.add),
                          // ),
                          ///TODO : step 1 TOP ELEMENT
                          // isDogView
                          //     ? SizedBox(height: 28)
                          //     : SizedBox(height: 10),
                          isGrigeList
                              ? Container(
                                  height: 170,
                                  padding: EdgeInsets.symmetric(horizontal: 20),
                                  width: double.infinity,
                                  child: Center(
                                    child: GridView.builder(
                                        padding: EdgeInsets.only(top:20 ,bottom: 0),
                                        gridDelegate:
                                            const SliverGridDelegateWithFixedCrossAxisCount(
                                          crossAxisCount: 5, // แสดง 5 คอลัมน์
                                        ),
                                        itemCount: _selectedViewDog.length,
                                        itemBuilder:
                                            (BuildContext context, int index) {
                                          return Item(
                                              function: (function) {},
                                              text: _selectedViewDog[index]
                                                  .number!,
                                              gradient: _selectedViewDog[index]
                                                  .gradient!,
                                              textColor: _selectedViewDog[index]
                                                  .textColor!);
                                        }),
                                  ),
                                )
                              : Container(
                                  height: 170,
                                  padding: EdgeInsets.symmetric(horizontal: 20),
                                  child: GridView.builder(
                                      padding: EdgeInsets.only(top:20 ,bottom: 0),
                                      gridDelegate:
                                          const SliverGridDelegateWithFixedCrossAxisCount(
                                        crossAxisCount: 5, // แสดง 5 คอลัมน์
                                      ),
                                      itemCount: _unselected.length,
                                      itemBuilder:
                                          (BuildContext context, int index) {
                                        return InkWell(
                                          onTap: () {
                                            _moveItem(
                                              fromIndex: index,
                                              fromList: _unselected,
                                              fromKey: _unselectedListKey,
                                              toList: controller.selected,
                                              toKey: _selectedListKey,
                                            );
                                            isHideButtonAndNavigate = true;
                                            setState(() {});
                                          },
                                          child: Item(
                                            function: () {},
                                            text: _unselected[index].number!,
                                            gradient:
                                                _unselected[index].gradient!,
                                            textColor:
                                                _unselected[index].textColor!,
                                          ),
                                        );
                                      }),
                                ),

                          SizedBox(height: 5),
                          ////GrideView
                          ///
                          // isGrigeList
                          //     ? Container(
                          //         height: 60,
                          //         width: double.infinity,
                          //         // color: Colors.orange,
                          //         child: Center(
                          //           child: ListView.builder(
                          //               shrinkWrap: true,
                          //               scrollDirection: Axis.horizontal,
                          //               itemCount: _selectedViewDog2.length,
                          //               itemBuilder: (c, i) {
                          //                 return Item(
                          //                     function: (function) {},
                          //                     text:
                          //                         _selectedViewDog2[i].number!,
                          //                     gradient: _selectedViewDog2[i]
                          //                         .gradient!,
                          //                     textColor: _selectedViewDog2[i]
                          //                         .textColor!);
                          //               }),
                          //         ),
                          //       )
                          //     : Container(
                          //         width: 500,
                          //         height: 60,
                          //         // color: Colors.green,
                          //         child: Center(
                          //           child: AnimatedList(
                          //             key: _unselectedListKey1,
                          //             shrinkWrap: true,
                          //             initialItemCount: _unselected1.length,
                          //             scrollDirection: Axis.horizontal,
                          //             itemBuilder: (context, index, animation) {
                          //               return InkWell(
                          //                 onTap: () {
                          //                   if (controller.selected.length !=
                          //                       5) {
                          //                     _moveItem(
                          //                       fromIndex: index,
                          //                       fromList: _unselected1,
                          //                       fromKey: _unselectedListKey1,
                          //                       toList: controller.selected,
                          //                       toKey: _selectedListKey,
                          //                     );
                          //                     setState(() {});
                          //                   } else if (controller
                          //                           .selected1.length !=
                          //                       5) {
                          //                     _moveItem(
                          //                       fromIndex: index,
                          //                       fromList: _unselected1,
                          //                       fromKey: _unselectedListKey1,
                          //                       toList: controller.selected1,
                          //                       toKey: _selectedListKey1,
                          //                     );
                          //                     setState(() {});
                          //                   } else if (controller
                          //                           .selected2.length !=
                          //                       5) {
                          //                     _moveItem(
                          //                       fromIndex: index,
                          //                       fromList: _unselected1,
                          //                       fromKey: _unselectedListKey1,
                          //                       toList: controller.selected2,
                          //                       toKey: _selectedListKey2,
                          //                     );
                          //                     setState(() {});
                          //                   } else if (controller
                          //                           .selected3.length !=
                          //                       5) {
                          //                     _moveItem(
                          //                       fromIndex: index,
                          //                       fromList: _unselected1,
                          //                       fromKey: _unselectedListKey1,
                          //                       toList: controller.selected3,
                          //                       toKey: _selectedListKey3,
                          //                     );
                          //                     setState(() {});
                          //                   }
                          //                   isHideButtonAndNavigate = true;
                          //                   setState(() {});
                          //                 },
                          //                 child: Item(
                          //                   function: () {},
                          //                   text: _unselected1[index].number!,
                          //                   gradient:
                          //                       _unselected1[index].gradient!,
                          //                   textColor:
                          //                       _unselected1[index].textColor!,
                          //                 ),
                          //               );
                          //             },
                          //           ),
                          //         ),
                          //       ),
                          SizedBox(height: 10),
                          Expanded(
                            child: Container(
                              width: Get.width,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: isGrigeList == false
                                        ? SlideTransition(
                                            position: _offsetAnimationRight,
                                            child: ListView(
                                              padding: EdgeInsets.all(0),
                                              shrinkWrap: true,
                                              children: [
                                                Stack(
                                                  children: [
                                                    InkWell(
                                                      onTap: () {
                                                        Navigator.pop(context);
                                                        controller
                                                            .setRandomNumberSuccess(
                                                                false);
                                                        controller
                                                            .clearNumber();
                                                      },
                                                      child: Container(
                                                        padding:
                                                            EdgeInsets.only(
                                                                left: 24,
                                                                top: 5),
                                                        child: Image.asset(
                                                          'assets/images/back_to_previous_icon.png',
                                                          height: 15,
                                                          package:
                                                              'lock_to_win',
                                                        ),
                                                      ),
                                                    ),
                                                    Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .center,
                                                      children: [
                                                        Text(
                                                          AppLocalizations.of(context)!.translate('ltw_choose_number'),
                                                          style: TextStyle(
                                                            fontSize: 14,
                                                            fontFamily:
                                                                "IBMPlexSansThai-Regular",
                                                            color: Color(
                                                                0xff738097),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ],
                                                ),

                                                SizedBox(
                                                  height: 15,
                                                ),
                                                controller.isRandomNumberSuccess
                                                    ? Column(
                                                        children: [
                                                          //////////////////FIRST LIST GOES HERE ///////////////////
                                                          // Container(
                                                          //   width: 500,
                                                          //   height: 60,
                                                          //   child: Center(
                                                          //     child:
                                                          //         AnimatedList(
                                                          //             key:
                                                          //                 _selectedListKey,
                                                          //             scrollDirection:
                                                          //                 Axis
                                                          //                     .horizontal,
                                                          //             initialItemCount: controller
                                                          //                 .selected
                                                          //                 .length,
                                                          //             shrinkWrap:
                                                          //                 true,
                                                          //             itemBuilder: (context,
                                                          //                 index,
                                                          //                 animation) {
                                                          //               return Column(
                                                          //                 children: [
                                                          //                 if(controller
                                                          //                     .selected)
                                                          //                     InkWell(
                                                          //                       onTap: () {
                                                          //                         setState(() {});
                                                          //                         _moveItem(
                                                          //                           fromIndex: index,
                                                          //                           fromList: controller.selected,
                                                          //                           fromKey: _selectedListKey,
                                                          //                           toList: _unselected,
                                                          //                           toKey: _unselectedListKey,
                                                          //                         );
                                                          //                         print("_unselected.length : ${_unselected.length}");
                                                          //                         // _addItem(
                                                          //                         //     controller.selected);
                                                          //                       },
                                                          //                       child: Item(
                                                          //                         function: () {
                                                          //                           setState(() {});
                                                          //                         },
                                                          //                         text: controller.selected[index].number!,
                                                          //                         gradient: controller.selected[index].gradient!,
                                                          //                         textColor: controller.selected[index].textColor!,
                                                          //                       ),
                                                          //                     )
                                                          //                 ],
                                                          //               );
                                                          //             }),
                                                          //   ),
                                                          // ),
                                                          SizedBox(height: 5),
                                                          ///// SECOND LIST ///////////
                                                          Container(
                                                            height: Get.height * 0.34,
                                                            padding: EdgeInsets
                                                                .symmetric(
                                                                    horizontal:
                                                                        20),
                                                            child: GridView
                                                                .builder(
                                                              padding: EdgeInsets.only(top:0),
                                                                key: _selectedListKey,
                                                                    gridDelegate:
                                                                        const SliverGridDelegateWithFixedCrossAxisCount(
                                                                      crossAxisCount:
                                                                          5, // แสดง 3 คอลัมน์
                                                                    ),
                                                                    itemCount: controller
                                                                        .selected
                                                                        .length,
                                                                    itemBuilder:
                                                                        (BuildContext
                                                                                context,
                                                                            int index) {
                                                                      return InkWell(
                                                                        onTap:
                                                                            () {
                                                                          if (_unselected.length !=
                                                                              controller.balanceTicket.toInt()) {
                                                                            setState(() {});
                                                                            isGenAllMyNumbers =
                                                                                false;
                                                                            _moveItem(
                                                                              fromIndex: index,
                                                                              fromList: controller.selected,
                                                                              fromKey: _selectedListKey,
                                                                              toList: _unselected,
                                                                              toKey: _unselectedListKey,
                                                                            );
                                                                          } else {}
                                                                        },
                                                                        child:
                                                                            Item(
                                                                          function:
                                                                              () {
                                                                            setState(() {});
                                                                          },
                                                                          text: controller
                                                                              .selected[index]
                                                                              .number!,
                                                                          gradient: controller
                                                                              .selected[index]
                                                                              .gradient!,
                                                                          textColor: controller
                                                                              .selected[index]
                                                                              .textColor!,
                                                                        ),
                                                                      );
                                                                    }),
                                                          ),

                                                          // Container(
                                                          //   width: 500,
                                                          //   height: 60,
                                                          //   child: Center(
                                                          //     child:
                                                          //         AnimatedList(
                                                          //       key:
                                                          //           _selectedListKey1,
                                                          //       scrollDirection:
                                                          //           Axis.horizontal,
                                                          //       shrinkWrap:
                                                          //           true,
                                                          //       initialItemCount:
                                                          //           controller
                                                          //               .selected1
                                                          //               .length,
                                                          //       itemBuilder:
                                                          //           (context,
                                                          //               index,
                                                          //               animation) {
                                                          //         return InkWell(
                                                          //           onTap: () {
                                                          //             //    _moveItem(
                                                          //             //   fromIndex: index,
                                                          //             //   fromList: _selected,
                                                          //             //   fromKey: _selectedListKey,
                                                          //             //   toList: _unselected,
                                                          //             //   toKey: _unselectedListKey,
                                                          //             // );
                                                          //             setState(
                                                          //                 () {});
                                                          //             print(_unselected
                                                          //                 .length);
                                                          //             print(_unselected1
                                                          //                 .length);
                                                          //             print(controller
                                                          //                 .selected1
                                                          //                 .length);
                                                          //
                                                          //             isGenAllMyNumbers =
                                                          //                 false;
                                                          //             setState(
                                                          //                 () {});
                                                          //
                                                          //             if (_unselected
                                                          //                     .length >=
                                                          //                 5) {
                                                          //               print(
                                                          //                   " First List is called ${_unselected.length}");
                                                          //               _moveItem(
                                                          //                 fromIndex:
                                                          //                     index,
                                                          //                 fromList:
                                                          //                     controller.selected1,
                                                          //                 fromKey:
                                                          //                     _selectedListKey1,
                                                          //                 toList:
                                                          //                     _unselected1,
                                                          //                 toKey:
                                                          //                     _unselectedListKey1,
                                                          //               );
                                                          //               setState(
                                                          //                   () {});
                                                          //             } else {
                                                          //               if (_unselected1.length <
                                                          //                   3) {
                                                          //                 print(
                                                          //                     "Second List is called ${_unselected1.length}");
                                                          //
                                                          //                 _moveItem(
                                                          //                   fromIndex:
                                                          //                       index,
                                                          //                   fromList:
                                                          //                       controller.selected1,
                                                          //                   fromKey:
                                                          //                       _selectedListKey1,
                                                          //                   toList:
                                                          //                       _unselected,
                                                          //                   toKey:
                                                          //                       _unselectedListKey,
                                                          //                 );
                                                          //                 setState(
                                                          //                     () {});
                                                          //               } else {
                                                          //                 print(
                                                          //                     "Do Nothing");
                                                          //               }
                                                          //               setState(
                                                          //                   () {});
                                                          //             }
                                                          //           },
                                                          //           child: Item(
                                                          //             function:
                                                          //                 () {
                                                          //               setState(
                                                          //                   () {});
                                                          //             },
                                                          //             text: controller
                                                          //                 .selected1[
                                                          //                     index]
                                                          //                 .number!,
                                                          //             gradient: controller
                                                          //                 .selected1[
                                                          //                     index]
                                                          //                 .gradient!,
                                                          //             textColor: controller
                                                          //                 .selected1[
                                                          //                     index]
                                                          //                 .textColor!,
                                                          //           ),
                                                          //         );
                                                          //       },
                                                          //     ),
                                                          //   ),
                                                          // ),
                                                          // SizedBox(height: 5),
                                                          // ///// THIRD LIST ///////////
                                                          // ///
                                                          // Container(
                                                          //   width: 500,
                                                          //   height: 60,
                                                          //   child: Center(
                                                          //     child:
                                                          //         AnimatedList(
                                                          //       key:
                                                          //           _selectedListKey2,
                                                          //       scrollDirection:
                                                          //           Axis.horizontal,
                                                          //       shrinkWrap:
                                                          //           true,
                                                          //       initialItemCount:
                                                          //           controller
                                                          //               .selected2
                                                          //               .length,
                                                          //       itemBuilder:
                                                          //           (context,
                                                          //               index,
                                                          //               animation) {
                                                          //         return InkWell(
                                                          //           onTap: () {
                                                          //             //    _moveItem(
                                                          //             //   fromIndex: index,
                                                          //             //   fromList: _selected,
                                                          //             //   fromKey: _selectedListKey,
                                                          //             //   toList: _unselected,
                                                          //             //   toKey: _unselectedListKey,
                                                          //             // );
                                                          //             setState(
                                                          //                 () {});
                                                          //             print(_unselected
                                                          //                 .length);
                                                          //             print(_unselected1
                                                          //                 .length);
                                                          //             print(controller
                                                          //                 .selected1
                                                          //                 .length);
                                                          //
                                                          //             isGenAllMyNumbers =
                                                          //                 false;
                                                          //             setState(
                                                          //                 () {});
                                                          //
                                                          //             if (_unselected
                                                          //                     .length >=
                                                          //                 5) {
                                                          //               print(
                                                          //                   " First List is called ${_unselected.length}");
                                                          //               _moveItem(
                                                          //                 fromIndex:
                                                          //                     index,
                                                          //                 fromList:
                                                          //                     controller.selected2,
                                                          //                 fromKey:
                                                          //                     _selectedListKey2,
                                                          //                 toList:
                                                          //                     _unselected1,
                                                          //                 toKey:
                                                          //                     _unselectedListKey1,
                                                          //               );
                                                          //               setState(
                                                          //                   () {});
                                                          //             } else {
                                                          //               if (_unselected1.length <
                                                          //                   3) {
                                                          //                 print(
                                                          //                     "Second List is called ${_unselected1.length}");
                                                          //
                                                          //                 _moveItem(
                                                          //                   fromIndex:
                                                          //                       index,
                                                          //                   fromList:
                                                          //                       controller.selected2,
                                                          //                   fromKey:
                                                          //                       _selectedListKey2,
                                                          //                   toList:
                                                          //                       _unselected,
                                                          //                   toKey:
                                                          //                       _unselectedListKey,
                                                          //                 );
                                                          //                 setState(
                                                          //                     () {});
                                                          //               } else {
                                                          //                 print(
                                                          //                     "Do Nothing");
                                                          //                 setState(
                                                          //                     () {});
                                                          //               }
                                                          //               setState(
                                                          //                   () {});
                                                          //             }
                                                          //             // isHideButtonAndNavigate =
                                                          //             //     false;
                                                          //             setState(
                                                          //                 () {});
                                                          //           },
                                                          //           child: Item(
                                                          //             function:
                                                          //                 () {},
                                                          //             text: controller
                                                          //                 .selected2[
                                                          //                     index]
                                                          //                 .number!,
                                                          //             gradient: controller
                                                          //                 .selected2[
                                                          //                     index]
                                                          //                 .gradient!,
                                                          //             textColor: controller
                                                          //                 .selected2[
                                                          //                     index]
                                                          //                 .textColor!,
                                                          //           ),
                                                          //         );
                                                          //       },
                                                          //     ),
                                                          //   ),
                                                          // ),
                                                          // SizedBox(height: 5),
                                                          // ///// Fourth LIST ///////////
                                                          // ///
                                                          // Container(
                                                          //   width: 500,
                                                          //   height: 60,
                                                          //   child: Center(
                                                          //     child:
                                                          //         AnimatedList(
                                                          //       key:
                                                          //           _selectedListKey3,
                                                          //       scrollDirection:
                                                          //           Axis.horizontal,
                                                          //       shrinkWrap:
                                                          //           true,
                                                          //       initialItemCount:
                                                          //           controller
                                                          //               .selected3
                                                          //               .length,
                                                          //       itemBuilder:
                                                          //           (context,
                                                          //               index,
                                                          //               animation) {
                                                          //         return InkWell(
                                                          //           onTap: () {
                                                          //             setState(
                                                          //                 () {});
                                                          //             print(_unselected
                                                          //                 .length);
                                                          //             print(_unselected1
                                                          //                 .length);
                                                          //             print(controller
                                                          //                 .selected1
                                                          //                 .length);
                                                          //
                                                          //             isGenAllMyNumbers =
                                                          //                 false;
                                                          //             setState(
                                                          //                 () {});
                                                          //
                                                          //             if (_unselected
                                                          //                     .length >=
                                                          //                 5) {
                                                          //               print(
                                                          //                   " First List is called ${_unselected.length}");
                                                          //               _moveItem(
                                                          //                 fromIndex:
                                                          //                     index,
                                                          //                 fromList:
                                                          //                     controller.selected3,
                                                          //                 fromKey:
                                                          //                     _selectedListKey3,
                                                          //                 toList:
                                                          //                     _unselected1,
                                                          //                 toKey:
                                                          //                     _unselectedListKey1,
                                                          //               );
                                                          //               setState(
                                                          //                   () {});
                                                          //             } else {
                                                          //               if (_unselected1.length <
                                                          //                   3) {
                                                          //                 print(
                                                          //                     "Second List is called ${_unselected1.length}");
                                                          //
                                                          //                 _moveItem(
                                                          //                   fromIndex:
                                                          //                       index,
                                                          //                   fromList:
                                                          //                       controller.selected3,
                                                          //                   fromKey:
                                                          //                       _selectedListKey3,
                                                          //                   toList:
                                                          //                       _unselected,
                                                          //                   toKey:
                                                          //                       _unselectedListKey,
                                                          //                 );
                                                          //                 setState(
                                                          //                     () {});
                                                          //               } else {
                                                          //                 print(
                                                          //                     "Do Nothing");
                                                          //                 setState(
                                                          //                     () {});
                                                          //               }
                                                          //               setState(
                                                          //                   () {});
                                                          //             }
                                                          //             isHideButtonAndNavigate =
                                                          //                 false;
                                                          //             setState(
                                                          //                 () {});
                                                          //           },
                                                          //           child: Item(
                                                          //             function:
                                                          //                 () {},
                                                          //             text: controller
                                                          //                 .selected3[
                                                          //                     index]
                                                          //                 .number!,
                                                          //             gradient: controller
                                                          //                 .selected3[
                                                          //                     index]
                                                          //                 .gradient!,
                                                          //             textColor: controller
                                                          //                 .selected3[
                                                          //                     index]
                                                          //                 .textColor!,
                                                          //           ),
                                                          //         );
                                                          //       },
                                                          //     ),
                                                          //   ),
                                                          // ),
                                                        ],
                                                      )
                                                    : Container()

                                                ///TODO : step 2

                                                // Container(
                                                //   width: 500,
                                                //   height: 70,
                                                //   color: Colors.white,
                                                //   child: AnimatedList(
                                                //     key: _selectedListKey1,
                                                //     scrollDirection: Axis.horizontal,
                                                //     initialItemCount: _selected1.length,
                                                //     itemBuilder: (context, index, animation) {
                                                //       return InkWell(
                                                //         onTap: () => _moveItem(
                                                //           fromIndex: index,
                                                //           fromList: _selected1,
                                                //           fromKey: _selectedListKey,
                                                //           toList: _unselected1,
                                                //           toKey: _unselectedListKey1,
                                                //         ),
                                                //         child: Item(text: _selected[index]),
                                                //       );
                                                //     },
                                                //   ),
                                                // ),

                                                // Container(
                                                //   margin: EdgeInsets.symmetric(
                                                //       horizontal: 12),
                                                //   width: Get.width,
                                                //   child: GridView.builder(
                                                //     shrinkWrap: true,
                                                //     physics:
                                                //         NeverScrollableScrollPhysics(),
                                                //     itemCount: ballon.length,
                                                //     gridDelegate:
                                                //         SliverGridDelegateWithFixedCrossAxisCount(
                                                //       crossAxisCount: 5,
                                                //     ),
                                                //     itemBuilder: (context, index) {
                                                //       final data = ballon[index];
                                                //       return InkWell(
                                                //         onTap: () {
                                                //           if (ballon2data.length <
                                                //               10) {
                                                //             ballon2data
                                                //                 .add(ballon[index]);
                                                //             ballon.removeAt(index);
                                                //             if (ballon2data.length >
                                                //                 9) {
                                                //               isButtonChange = true;
                                                //               setState(() {});
                                                //             }
                                                //             setState(() {});
                                                //           } else {
                                                //             return;
                                                //           }
                                                //         },
                                                //         child: ballon22(
                                                //           bGColor: data.color,
                                                //           number: data.number,
                                                //         ),
                                                //       );
                                                //     },
                                                //   ),
                                                // ),
                                              ],
                                            ),
                                          )
                                        :
                                        /////////// WAITING DOG CONGITY FILE ////////////////

                                        isDogView
                                            ? Container(
                                                margin: EdgeInsets.only(
                                                    top: 30, right: 25),
                                                width: Get.width,
                                                // color: Colors.red,
                                                child: Stack(
                                                  children: [
                                                    SlideTransition(
                                                      position:
                                                          _offsetAnimationLeftIn,
                                                      child: Container(
                                                          // margin: EdgeInsets.only(bottom: 30),
                                                          height: 300,
                                                          width: 200,
                                                          child: Lottie.asset(
                                                              'assets/animatedIcons/Waiting Dog.json',
                                                              package:
                                                                  'lock_to_win')),
                                                    ),
                                                    SlideTransition(
                                                      position:
                                                          _offsetAnimationRightIn,
                                                      child: Row(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .end,
                                                        children: [
                                                          Column(
                                                            crossAxisAlignment:
                                                                CrossAxisAlignment
                                                                    .end,
                                                            children: [
                                                              Container(
                                                                child: Text(
                                                                  AppLocalizations.of(context)!.translate('ltw_got_number'),
                                                                  style:
                                                                      TextStyle(
                                                                    fontSize:
                                                                        12,
                                                                    letterSpacing:
                                                                        0.3,
                                                                    fontFamily:
                                                                        "IBMPlexSansThai-Regular",
                                                                    color: isDarkMode
                                                                        ? Color(
                                                                            0xff738097)
                                                                        : Color(
                                                                            0xff5B6B85),
                                                                  ),
                                                                ),
                                                              ),
                                                              Container(
                                                                child: Text(
                                                                  AppLocalizations.of(context)!.translate('ltw_stay_tune'),
                                                                  style:
                                                                      TextStyle(
                                                                    fontSize:
                                                                        12,
                                                                    letterSpacing:
                                                                        0.3,
                                                                    fontFamily:
                                                                        "IBMPlexSansThai-Regular",
                                                                    color: isDarkMode
                                                                        ? Color(
                                                                            0xffE9ECF5)
                                                                        : Color(
                                                                            0xff262F3E),
                                                                  ),
                                                                ),
                                                              ),
                                                              SizedBox(
                                                                height: 15,
                                                              ),
                                                              Container(
                                                                child: Text(
                                                                  AppLocalizations.of(context)!.translate('ltw_draw_time')+':',
                                                                  style:
                                                                      TextStyle(
                                                                    fontSize:
                                                                        12,
                                                                    fontFamily:
                                                                        "IBMPlexSansThai-Regular",
                                                                    color: isDarkMode
                                                                        ? Color(
                                                                            0xff738097)
                                                                        : Color(
                                                                            0xff5B6B85),
                                                                  ),
                                                                ),
                                                              ),
                                                              Container(
                                                                child: Text(
                                                                  '${DateFormat('MMM dd, yyyy hh:mm').format(
                                                                    controller
                                                                        .annoncesTime!
                                                                        .date,
                                                                  )}\t',
                                                                  style:
                                                                      TextStyle(
                                                                    fontSize:
                                                                        12,
                                                                    letterSpacing:
                                                                        0.3,
                                                                    fontFamily:
                                                                        "IBMPlexSansThai-Regular",
                                                                    color: isDarkMode
                                                                        ? Color(
                                                                            0xffE9ECF5)
                                                                        : Color(
                                                                            0xff262F3E),
                                                                  ),
                                                                ),
                                                              ),
                                                              SizedBox(
                                                                height: 15,
                                                              ),
                                                              Container(
                                                                child: Text(
                                                                  AppLocalizations.of(context)!.translate('ltw_time_left')+':',
                                                                  style:
                                                                      TextStyle(
                                                                    fontSize:
                                                                        12,
                                                                    fontFamily:
                                                                        "IBMPlexSansThai-Regular",
                                                                    color: isDarkMode
                                                                        ? Color(
                                                                            0xff738097)
                                                                        : Color(
                                                                            0xff5B6B85),
                                                                  ),
                                                                ),
                                                              ),
                                                              CountdownTimer(
                                                                  controller:
                                                                      controller
                                                                          .controller,
                                                                  widgetBuilder: (_,
                                                                      CurrentRemainingTime?
                                                                          time) {
                                                                    if (time ==
                                                                        null) {
                                                                      return Text(
                                                                          '');
                                                                    }
                                                                    return Container(
                                                                      child:
                                                                          Row(
                                                                        crossAxisAlignment:
                                                                            CrossAxisAlignment.end,
                                                                        children: [
                                                                          Text(
                                                                            '${time.days == null ? 0 : time.days}',
                                                                            style:
                                                                                TextStyle(
                                                                              color: isDarkMode ? Color(0xffE9ECF5) : Color(0xff262F3E),
                                                                              letterSpacing: 0.3,
                                                                              fontFamily: "IBMPlexSansThai-Regular",
                                                                              fontSize: 20,
                                                                            ),
                                                                          ),
                                                                          SizedBox(
                                                                            width:
                                                                                2,
                                                                          ),
                                                                          Container(
                                                                            margin:
                                                                                EdgeInsets.only(bottom: 4),
                                                                            child:
                                                                                Text(
                                                                                  AppLocalizations.of(context)!.translate('ltw_days'),
                                                                              style: TextStyle(letterSpacing: 0.3, fontFamily: "IBMPlexSansThai-Regular", fontSize: 12, color: Color(0xff3E4B5E)),
                                                                            ),
                                                                          ),
                                                                          SizedBox(
                                                                            width:
                                                                                2,
                                                                          ),
                                                                          Text(
                                                                            ' ${time.hours == null ? 0 : time.hours}:${time.min == null ? 0 : time.min}:${time.sec == null ? 0 : time.sec}',
                                                                            style:
                                                                                TextStyle(
                                                                              color: isDarkMode ? Color(0xffE9ECF5) : Color(0xff262F3E),
                                                                              letterSpacing: 0.3,
                                                                              fontFamily: "IBMPlexSansThai-Regular",
                                                                              fontSize: 20,
                                                                            ),
                                                                          ),
                                                                        ],
                                                                      ),
                                                                    );
                                                                  }),
                                                              SizedBox(
                                                                  height: 15),
                                                              InkWell(
                                                                onTap: () {
                                                                  Navigator.pop(
                                                                      context);
                                                                },
                                                                child:
                                                                    Container(
                                                                  child: Row(
                                                                    children: [
                                                                      Text(
                                                                        AppLocalizations.of(context)!.translate('ltw_close'),
                                                                        style: TextStyle(
                                                                            letterSpacing:
                                                                                0.3,
                                                                            fontFamily:
                                                                                "IBMPlexSansThai-Regular",
                                                                            fontSize:
                                                                                14,
                                                                            color:
                                                                                Color(0xff0078FF)),
                                                                      ),
                                                                      SizedBox(
                                                                        width:
                                                                            5,
                                                                      ),
                                                                      Image
                                                                          .asset(
                                                                        'assets/images/Group 36634_blue.png',
                                                                        package:
                                                                            'lock_to_win',
                                                                        height:
                                                                            20,
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ),
                                                              )
                                                            ],
                                                          )
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              )
                                            : Container(),
                                  ),
                                  // isButtonChange == false

                                  // (_unselected.length == 0)
                                  _unselected.length == 0
                                      ? //Row Of (Gen All My Numbers)
                                      InkWell(
                                          onTap: () async {
                                            // for (int i = 0;
                                            //     i < controller.selected.length;
                                            //     i++) {
                                            //   _unselected
                                            //       .add(controller.selected[i]);
                                            //   controller.selected.remove(i);
                                            //   await Future.delayed(
                                            //     Duration(microseconds: 10000),
                                            //   );
                                            //   setState(() {
                                            //     isShowView = false;
                                            //   });
                                            //   // isButtonChange = true;
                                            //   setState(() {});
                                            // }
                                            // isGrigeList = true;
                                            // isHideButtonAndNavigate = true;
                                            // setState(() {});
                                          },
                                          child: SlideTransition(
                                            position: _offsetAnimationLeft,
                                            child: Container(
                                              alignment: Alignment.topCenter,
                                              padding: EdgeInsets.only(right:20,left:20,top:7,bottom:7),
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  Container(
                                                    padding: EdgeInsets.only(
                                                        right: 10),
                                                    child: Text(
                                                      AppLocalizations.of(context)!.translate('ltw_dont_want_choose'),
                                                      style: TextStyle(
                                                        fontSize: 11,
                                                        fontFamily:
                                                            "IBMPlexSansThai-Regular",
                                                        color:
                                                            Color(0xff738097),
                                                      ),
                                                    ),
                                                  ),
                                                  InkWell(
                                                    onTap: () async {
                                                      final data = await controller
                                                          .randomNumberFree(
                                                              controller
                                                                  .balanceTicket
                                                                  .toInt());
                                                      for (var i = 0;
                                                          i < data.length;
                                                          i++) {
                                                        _selectedViewDog.add(Ballon2(
                                                            number: data[i]
                                                                .join(''),
                                                            gradient: ColorTheme
                                                                    .linearGradientRandom20[
                                                                i],
                                                            textColor: ColorTheme
                                                                    .textColorRandom20[
                                                                i]));
                                                      }
                                                      print(numberSelect);
                                                      final res = await controller
                                                          .chooseBuyTicket(
                                                              token: controller
                                                                  .idToken!,
                                                              tricketNumber:
                                                                  data);
                                                      if (res) {
                                                        _controller.forward();
                                                        setState(() {});
                                                        Future.delayed(
                                                            Duration(
                                                                milliseconds:
                                                                    500), () {
                                                          _controller.forward();
                                                          isDogView = true;
                                                          isGrigeList = true;
                                                          isGenAllMyNumbers =
                                                              false;
                                                          isButtonChange ==
                                                              false;
                                                          _unselected = [];
                                                          setState(() {});
                                                        });
                                                      }

                                                      // _controller.forward();
                                                      // Future.delayed(
                                                      //     Duration(
                                                      //         milliseconds:
                                                      //             500), () {
                                                      //   _controller.forward();
                                                      //   isDogView = true;
                                                      //   isGrigeList = true;
                                                      //   isGenAllMyNumbers =
                                                      //       false;
                                                      //   for (int i = 0;
                                                      //       i <
                                                      //           controller
                                                      //               .balanceTicket
                                                      //               .toInt();
                                                      //       i++) {
                                                      //     Future.delayed(
                                                      //         Duration(
                                                      //             milliseconds:
                                                      //                 300 * i),
                                                      //         () {
                                                      //       setState(() =>
                                                      //           _selectedViewDog
                                                      //               .add(controller
                                                      //                       .selected[
                                                      //                   i]));
                                                      //     });
                                                      //   }
                                                      // });
                                                    },
                                                    child: Stack(
                                                      children: [
                                                        if (isDarkMode == false)
                                                          Container(
                                                            margin:
                                                                EdgeInsets.only(
                                                                    top: 15,
                                                                    left: 10),
                                                            height: 30,
                                                            width: 160,
                                                            decoration:
                                                                BoxDecoration(
                                                              // color: Color(0xff384AE8).withOpacity(0.4),
                                                              boxShadow: [
                                                                BoxShadow(
                                                                  color: Color(
                                                                          0xff384AE8)
                                                                      .withOpacity(
                                                                          0.2),
                                                                  blurRadius: 5,
                                                                  offset: Offset(
                                                                      0,
                                                                      0), // changes position of shadow
                                                                ),
                                                              ],
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          50),
                                                            ),
                                                            child: ClipRRect(
                                                              // <-- clips to the 200x200 [Container] below
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          50),
                                                              child:
                                                                  BackdropFilter(
                                                                filter:
                                                                    ImageFilter
                                                                        .blur(
                                                                  sigmaX: 1.0,
                                                                  sigmaY: 1.0,
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                        Container(
                                                          alignment:
                                                              Alignment.center,
                                                          height: 36,
                                                          width: 182,
                                                          decoration:
                                                              BoxDecoration(
                                                            color: isDarkMode
                                                                ? Color(0xff0078FF)
                                                                    .withOpacity(
                                                                        0.10)
                                                                : Color(
                                                                    0xff384AE8),
                                                            border: Border.all(
                                                                color: Color(
                                                                    0xff0078FF)),
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        50),
                                                          ),
                                                          child: Text(
                                                            AppLocalizations.of(context)!.translate('ltw_gen_all'),
                                                            style: TextStyle(
                                                              fontSize: 14,
                                                              fontFamily:
                                                                  "IBMPlexSansThai-Regular",
                                                              color: isDarkMode
                                                                  ? Color(
                                                                      0xff0078FF)
                                                                  : Color(
                                                                      0xffEBEDFD),
                                                            ),
                                                          ),
                                                        )
                                                      ],
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ))
                                      : //Button confirm my numbers
                                      // isHideButtonAndNavigate == false
                                      ((_unselected.length +
                                                  _unselected1.length) >=
                                              controller.balanceTicket.toInt())
                                          ? InkWell(
                                              onTap: () async {
                                                List numberSelect = [];
                                                _selectedViewDog = _unselected;
                                                if (_selectedViewDog
                                                    .isNotEmpty) {
                                                  for (var number
                                                      in _selectedViewDog) {
                                                    numberSelect.add(number
                                                        .number!
                                                        .split(''));
                                                  }
                                                  print(numberSelect);
                                                  final res = await controller
                                                      .chooseBuyTicket(
                                                          token: controller
                                                              .idToken!,
                                                          tricketNumber:
                                                              numberSelect);
                                                  if (res) {
                                                    _controller.forward();
                                                    setState(() {});
                                                    Future.delayed(
                                                        Duration(
                                                            milliseconds: 500),
                                                        () {
                                                      _controller.forward();
                                                      isDogView = true;
                                                      isGrigeList = true;
                                                      isGenAllMyNumbers = false;
                                                      isButtonChange == false;
                                                      _unselected = [];
                                                      setState(() {});
                                                    });
                                                  }
                                                }
                                              },
                                              child: Container(
                                                alignment: Alignment.center,
                                                margin: EdgeInsets.only(
                                                  top: 8,
                                                  bottom: 8,
                                                  right: Get.width * 0.1,
                                                  left: Get.width * 0.1,
                                                ),
                                                height: 39,
                                                decoration: BoxDecoration(
                                                  color: Color(0xff4D4DFF),
                                                  borderRadius:
                                                      BorderRadius.circular(60),
                                                ),
                                                child: Text(
                                                  AppLocalizations.of(context)!.translate('ltw_confirm_number'),
                                                  style: TextStyle(
                                                      color: Colors.white),
                                                ),
                                              ),
                                            )
                                          : Container(
                                              margin: EdgeInsets.only(
                                                top: 8,
                                                bottom: 8,
                                                right: Get.width * 0.1,
                                                left: Get.width * 0.1,
                                              ),
                                              height: 39,
                                            ),
                                  SizedBox(
                                    height: 10,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                      // child: FutureBuilder(
                      //   future: _futureBalanceTicket,
                      //   builder: (
                      //     BuildContext context,
                      //     AsyncSnapshot<double> snapshot,
                      //   ) {
                      //     print(snapshot.connectionState);
                      //     if (snapshot.connectionState ==
                      //         ConnectionState.waiting) {
                      //       return Center(
                      //         child: SpinKitThreeBounce(
                      //           color: Colors.grey,
                      //           size: 20,
                      //         ),
                      //       );
                      //     } else if (snapshot.connectionState ==
                      //         ConnectionState.done) {
                      //       if (snapshot.hasError) {
                      //         return const Text('Error');
                      //       } else if (snapshot.hasData) {
                      //         return Container(
                      //           padding: EdgeInsets.only(
                      //             right: 2,
                      //           ),
                      //           child: Text(
                      //             '${snapshot.data!.toStringAsFixed(0)}',
                      //             style: TextStyle(
                      //                 fontSize: 24,
                      //                 fontFamily: 'SFPro',
                      //                 letterSpacing: 0.3,
                      //                 fontWeight: FontWeight.w600,
                      //                 color: isDarkMode
                      //                     ? Color(0xffE9ECF5)
                      //                     : Colors.black),
                      //           ),
                      //         );
                      //       } else {
                      //         return const Text('Empty data');
                      //       }
                      //     } else {
                      //       return Text('State: ${snapshot.connectionState}');
                      //     }
                      //   },
                      // ),
                    ),
                  ),
                  Container(
                    // padding: EdgeInsets.only(top: 12, right: 15, left: 15),
                    height: 105,
                    decoration: BoxDecoration(
                        color:
                            isDarkMode ? Color(0xff1D2532) : Color(0xffFEFEFE),
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(20.0),
                          topRight: Radius.circular(20.0),
                        ),
                        boxShadow: <BoxShadow>[
                          BoxShadow(
                            color: Color(0xff000000).withOpacity(0.1),
                            blurRadius: 15.0,
                            spreadRadius: 10,
                            offset: Offset(0.0, 0.25),
                          )
                        ]),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        FutureBuilder(
                          future: _futureRoundNow,
                          builder: (
                            BuildContext context,
                            AsyncSnapshot<List?> snapshot,
                          ) {
                            print(snapshot.connectionState);
                            if (snapshot.connectionState ==
                                ConnectionState.waiting) {
                              return Center(
                                child: SpinKitThreeBounce(
                                  color: Colors.grey,
                                  size: 50,
                                ),
                              );
                            } else if (snapshot.connectionState ==
                                ConnectionState.done) {
                              if (snapshot.hasError) {
                                return Text(AppLocalizations.of(context)!.translate('ltw_error'));
                              } else if (snapshot.hasData) {
                                return Component.nextRound(
                                    context: context,
                                    round: snapshot.data![0],
                                    total: snapshot.data![1],
                                    controller: controller);
                              } else {
                                return Text(AppLocalizations.of(context)!.translate('ltw_empty_data'));
                              }
                            } else {
                              return Text(AppLocalizations.of(context)!.translate('ltw_state')+': ${snapshot.connectionState}');
                            }
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget note() {
    return Container(
      height: Get.height,
      width: Get.width,
      child: Text('data'),
    );
  }

  Widget ballon22({String? number, Gradient? bGColor}) {
    return Container(
      alignment: Alignment.center,
      margin: EdgeInsets.all(6),
      height: 50,
      width: 50,
      decoration: BoxDecoration(
        gradient: bGColor,
        borderRadius: BorderRadius.circular(100),
      ),
      child: Text(
        number!,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: Colors.black,
        ),
      ),
    );
  }

  ///Animation
  ///
  ///

  int _flyingCount = 0;

  _addItem(number) {
    isDogView = true;
    isGrigeList = false;
    _unselected.add(number);
    setState(() {});
  }

  _moveItem({
    required int fromIndex,
    required List<Ballon2> fromList,
    required GlobalKey<AnimatedListState> fromKey,
    required List<Ballon2> toList,
    required GlobalKey<AnimatedListState> toKey,
    Duration duration = const Duration(milliseconds: 300),
  }) {
    final globalKey = GlobalKey();
    final item = fromList.removeAt(fromIndex);
    print(fromKey);
    // fromKey.currentState!.removeItem(
    //   fromIndex,
    //   (context, animation) {
    //     return SizeTransition(
    //       sizeFactor: animation,
    //       child: Opacity(
    //         key: globalKey,
    //         opacity: 0.0,
    //         child: Item(
    //           function: () {},
    //           text: item.number!,
    //           gradient: item.gradient!,
    //           textColor: item.textColor!,
    //         ),
    //       ),
    //     );
    //   },
    //   duration: duration,
    // );
    // _flyingCount++;
    //
    // WidgetsBinding.instance!.addPostFrameCallback((timeStamp) async {
    //   // Find the starting position of the moving item, which is exactly the
    //   // gap its leaving behind, in the original list.
    //   final box1 = globalKey.currentContext!.findRenderObject() as RenderBox;
    //   final pos1 = box1.localToGlobal(Offset.zero);
    //   // Find the destination position of the moving item, which is at the
    //   // end of the destination list.
    //   final box2 = toKey.currentContext!.findRenderObject() as RenderBox;
    //   final box2height = box1.size.height * 0;
    //   final pos2 = box2.localToGlobal(Offset(0, box2height));
    //   // Insert an overlay to "fly over" the item between two lists.
    //   final entry = OverlayEntry(builder: (BuildContext context) {
    //     return TweenAnimationBuilder(
    //       tween: Tween<Offset>(begin: pos1, end: pos2),
    //       duration: duration,
    //       builder: (_, Offset value, child) {
    //         return Positioned(
    //           left: value.dx,
    //           top: value.dy,
    //           child: Item(
    //             function: () {},
    //             text: item.number!,
    //             gradient: item.gradient!,
    //             textColor: item.textColor!,
    //           ),
    //         );
    //       },
    //     );
    //   });
    //
    //   Overlay.of(context)!.insert(entry);
    //   await Future.delayed(duration);
    //   entry.remove();
    toList.insert(0, item);
    // toKey.currentState!.insertItem(toList.length - 1);
    // _flyingCount--;
    // });
  }
}

class Item extends StatelessWidget {
  final String text;
  final Function function;
  final Gradient gradient;
  final Color textColor;

  const Item(
      {Key? key,
      required this.function,
      required this.text,
      required this.gradient,
      required this.textColor})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(0.0),
      child: Container(
        alignment: Alignment.center,
        margin: EdgeInsets.only(left: 7, right: 7),
        height: 51,
        width: 51,
        decoration: BoxDecoration(
          gradient: gradient,
          // borderRadius: BorderRadius.circular(100),
          shape: BoxShape.circle,
          boxShadow: isDarkMode
              ? <BoxShadow>[]
              : [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: 18,
                    offset: Offset(3, 9),
                  )
                ],
        ),
        child: Text(
          text,
          style: TextStyle(
            fontFamily: "DINPro-Bold",
            fontSize: 15,
            fontWeight: FontWeight.bold,
            color: textColor,
          ),
        ),
      ),
    );
  }

  Widget ballon({String? text}) {
    return Container(
        width: 50, height: 50, color: Colors.red, child: Text(text!));
  }

  Widget ballon2({String? text}) {
    return Container(
        width: 50, height: 50, color: Colors.red, child: Text(text!));
  }
}
