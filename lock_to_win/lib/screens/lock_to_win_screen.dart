import 'dart:async';
import 'dart:ui';

import 'package:animated/animated.dart';
import 'package:animated_text_kit/animated_text_kit.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:likewallet/libraryman/app_local.dart';
import 'package:flutter_countdown_timer/current_remaining_time.dart';
import 'package:flutter_countdown_timer/flutter_countdown_timer.dart';
import 'package:flutter_custom_clippers/flutter_custom_clippers.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:global_configuration/global_configuration.dart';
import 'package:intl/intl.dart';
import 'package:lock_to_win/controller/lottery.dart';
import 'package:lock_to_win/models/lotto/annonces_time.dart';
import 'package:lock_to_win/models/lotto/your_lotto_list_pre_round.dart';
import 'package:lock_to_win/screens/component.dart';
import 'package:lock_to_win/services/lotto_service.dart';
import 'package:lock_to_win/widgets/loading.dart';
import 'package:lottie/lottie.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lock_to_win/config/color_system.dart';
import 'package:lock_to_win/config/dark_mode_colors.dart';
import 'package:flutter_svg/avd.dart';
import 'package:lock_to_win/config/light_mode_colors.dart';
import 'package:lock_to_win/main.dart';
import 'package:lock_to_win/models/ballon2.dart';
import 'package:animations/animations.dart';
import 'package:lock_to_win/screens/game-rules_screen.dart';
import 'package:lock_to_win/screens/lock_to_win_choosen_screen.dart';
import 'package:lock_to_win/screens/previous_winning_numbers.dart';
import 'package:simple_tooltip/simple_tooltip.dart';
import 'package:lock_to_win/widgets/ballon.dart';
import 'package:lock_to_win/widgets/glass_morphism.dart';

class LockToWinScreen extends StatefulWidget {
  const LockToWinScreen({Key? key, this.data}) : super(key: key);
  final data;
  @override
  State<LockToWinScreen> createState() => _LockToWinScreenState();
}

class _LockToWinScreenState extends State<LockToWinScreen>
    with TickerProviderStateMixin {
  final storeController = Get.find<LotteryController>();
  Future<List>? _future;
  Future<List<num>?>? _futureWinner;
  Future<List>? _futureRoundNow;
  Future<List>? _futurePreTime;
  Future<YourLottoListPreRound?>? _futurePreNumber;
  Future<bool>? _futurePlayed;

  LotteryService? lotteryService;

  Future<List> getStart() async {
    lotteryService = LotteryService();
    final data1 = await storeController.getPreviousRoundNumber();
    final data2 = await storeController.getRoundLast();
    return [data1, data2];
  }

  Future<List<num>?> getWinner() async {
    final data = await storeController.preRoundWinner();
    return data;
  }

  Future<List>? roundNow() async {
    final data1 = await storeController.getRoundNow();
    final data2 = await storeController.getAnnoncesTime();
    return [data1, data2];
  }

  Future<List>? getPreTime() async {
    final data = await storeController.getPreviousTime();
    return [data];
  }

  @override
  void initState() {
    super.initState();
    init();
    lotteryService = LotteryService();
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
    storeController.clearController();
  }

  Future<void> init() async {
    Loading.typeLoadingAwait('Please wait..');
    await Future.delayed(Duration(seconds: 2));
    storeController.setDataUser(widget.data);

    await storeController.verifyToken(storeController.dataUser!.token);
    storeController.init(data: widget.data).then((ready) async {
      final result = await storeController.checkSyncDataFromFirebase(
          address: widget.data['address']);
      if (result) {
        if (ready) {
          _future = getStart();
          _futureWinner = getWinner();
          _futureRoundNow = roundNow();
          _futurePreTime = getPreTime();
          _futurePreNumber =
              storeController.getYourLottoPreRound(dataUser: widget.data);
          _futurePlayed = storeController.checkLottoLastRound();
          setState(() {});
        }
      }
    });
  }

  String image = 'assets/images/Group 36513.png';
  String lockToWinImage = 'assets/svgs/lockToWin.svg';
  String lockToWinLightImage = 'assets/svgs/IconLock-to-Win-Light.svg';
  bool yesNo = false;
  bool isStackItemShow = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: isDarkMode ? Colors.black : Colors.white,
      body: storeController.readyState
          ? GetBuilder<LotteryController>(
              init: LotteryController(),
              builder: (controller) => SafeArea(
                    bottom: false,
                    top: false,
                    child: Container(
                      width: Get.width,
                      height: Get.height,
                      child: Stack(
                        children: [
                          Container(
                            // height: Get.height,
                            color: isDarkMode
                                ? Color(0xff11161E)
                                : Color(0xffEFF1F4),
                            width: double.infinity,
                            child: Column(
                              children: [
                                Container(
                                  color:
                                      isDarkMode ? layerBG3Dark : layerBG3Light,
                                  padding:
                                      EdgeInsets.only(top: Get.height * 0.05),
                                  height:
                                      MediaQuery.of(context).size.height * 0.35,
                                  width: Get.width,
                                  child: Stack(
                                    children: [
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.end,
                                        children: [
                                          InkWell(
                                            onTap: () {
                                              Navigator.of(context,
                                                      rootNavigator: true)
                                                  .pop(context);
                                            },
                                            child: Padding(
                                              padding: const EdgeInsets.only(
                                                right: 14.0,
                                                bottom: 5.0,
                                              ),
                                              child: Icon(
                                                Icons.arrow_back_ios,
                                                size: 17.0,
                                                color: Color(0xff738097),
                                              ),
                                            ),
                                          ),
                                          Container(
                                            padding: EdgeInsets.only(right: 20),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.end,
                                              children: [
                                                Text(
                                                AppLocalizations.of(context)!
                                                .translate('ltw_win_number'),
                                                  style: GoogleFonts
                                                      .ibmPlexSansThai(
                                                          textStyle: TextStyle(
                                                    fontSize: 12,
                                                    letterSpacing: 0.3,
                                                    color: isDarkMode
                                                        ? textGray400Dark
                                                        : textGray400Light,
                                                  )),
                                                ),
                                                Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.end,
                                                  children: [
                                                    Text(
                                                      AppLocalizations.of(context)!
                                                          .translate('ltw_last_round')+':\t',
                                                      style: GoogleFonts
                                                          .ibmPlexSansThai(
                                                              textStyle:
                                                                  TextStyle(
                                                        letterSpacing: 0.3,
                                                        color: isDarkMode
                                                            ? textGray400Dark
                                                            : textGray400Light,
                                                        fontSize: 12,
                                                      )),
                                                    ),
                                                    FutureBuilder(
                                                      future: _futurePreTime,
                                                      builder: (
                                                          BuildContext context,
                                                          AsyncSnapshot<List> snapshot,
                                                          ) {
                                                        print(snapshot.connectionState);
                                                        if (snapshot.connectionState ==
                                                            ConnectionState.waiting) {
                                                          return Center(
                                                            child: SpinKitThreeBounce(
                                                              color: Colors.grey,
                                                              size: 15,
                                                            ),
                                                          );
                                                        } else if (snapshot
                                                            .connectionState ==
                                                            ConnectionState.done) {
                                                          if (snapshot.hasError) {
                                                            return Text(AppLocalizations.of(context)!
                                                                .translate('ltw_error'));
                                                          } else if (snapshot.hasData) {
                                                            return
                                                                Component.preDateTime(
                                                                    snapshot
                                                                        .data![0].date);
                                                          } else {
                                                            return Text(
                                                                AppLocalizations.of(context)!
                                                                    .translate('ltw_empty_data'));
                                                          }
                                                        } else {
                                                          return Text(
                                                              AppLocalizations.of(context)!
                                                                  .translate('ltw_state')+': ${snapshot.connectionState}');
                                                        }
                                                      },
                                                    ),
                                                  ],
                                                ),
                                                SizedBox(
                                                  height: 10,
                                                ),
                                              ],
                                            ),
                                          ),
                                          Container(
                                            padding: EdgeInsets.only(
                                                bottom: Get.height * 0.01),
                                            child: Align(
                                                alignment:
                                                    Alignment.bottomCenter,
                                                child: Lottie.asset(
                                                  'assets/animatedIcons/Numbers_Animation_Blue.json',
                                                  package: 'lock_to_win',
                                                  height: Get.height * 0.2,
                                                )),
                                          ),
                                        ],
                                      ),
                                      Align(
                                        alignment: Alignment.topLeft,
                                        child: Column(
                                          children: [
                                            Container(
                                              padding:
                                                  EdgeInsets.only(left: 12),
                                              child: SvgPicture.asset(
                                                isDarkMode
                                                    ? lockToWinImage
                                                    : lockToWinLightImage,
                                                height: 125,
                                                package: 'lock_to_win',
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Align(
                                        alignment: Alignment.topRight,
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.end,
                                          children: [],
                                        ),
                                      ),
                                      Align(
                                        alignment: Alignment.topCenter,
                                        child: Container(
                                          decoration: BoxDecoration(
                                              gradient: LinearGradient(
                                            begin: Alignment.topCenter,
                                            end: Alignment.bottomCenter,
                                            colors: [
                                              Color(0xff0074FC)
                                                  .withOpacity(0.2),
                                              Color(0xff0074FC).withOpacity(0),
                                            ],
                                          )),
                                          // height: 400,
                                          width: Get.width,
                                          margin: EdgeInsets.only(
                                            top: 218,
                                          ),
                                        ),
                                      ),
                                      Component.perRoundWinner(_futureWinner),
                                    ],
                                  ),
                                ),

                                Expanded(
                                  child: InkWell(
                                    onTap: () {
                                      controller.setTolTipVisible(false);
                                    },
                                    child: ListView(
                                      padding: EdgeInsets.only(
                                        top: 0,
                                      ),
                                      children: [
                                        Stack(
                                          children: [
                                            Container(
                                              width: double.infinity,
                                              padding: EdgeInsets.symmetric(
                                                  horizontal: 20, vertical: 5),
                                              decoration: BoxDecoration(
                                                color: isDarkMode
                                                    ? Color(0xff1D2532)
                                                    : Color(0xffFEFEFE),
                                                borderRadius: BorderRadius.only(
                                                  bottomLeft:
                                                      Radius.circular(20),
                                                  bottomRight:
                                                      Radius.circular(20),
                                                ),
                                              ),
                                              child: Column(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.start,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Component.preRound(
                                                      _future, controller),
                                                  SizedBox(
                                                    height: 10,
                                                  ),
                                                  FutureBuilder(
                                                    future: _futurePreNumber,
                                                    builder: (
                                                      BuildContext context,
                                                      AsyncSnapshot<
                                                              YourLottoListPreRound?>
                                                          snapshot,
                                                    ) {
                                                      print(snapshot
                                                          .connectionState);
                                                      if (snapshot
                                                              .connectionState ==
                                                          ConnectionState
                                                              .waiting) {
                                                        return Center(
                                                            child: Column(
                                                          children: [
                                                            Text(
                                                              AppLocalizations.of(context)!
                                                                  .translate('ltw_ticket_waiting'),
                                                              style: TextStyle(
                                                                  color: Colors
                                                                      .white),
                                                            ),
                                                            SpinKitThreeInOut(
                                                              color:
                                                                  Colors.grey,
                                                              size: 20.0,
                                                            ),
                                                          ],
                                                        ));
                                                      } else if (snapshot
                                                              .connectionState ==
                                                          ConnectionState
                                                              .done) {
                                                        if (snapshot.hasError) {
                                                          return Text(
                                                            AppLocalizations.of(context)!
                                                                .translate('ltw_error'));
                                                        } else if (snapshot
                                                            .hasData) {
                                                          print('have');
                                                          print(snapshot
                                                              .data!.data);
                                                          return Component
                                                              .preRoundCheckYourNumber(context,
                                                                  snapshot
                                                                      .data!,
                                                                  controller);
                                                        } else {
                                                          // print("ZZZZZZ");
                                                          // print(snapshot.data);
                                                          return Text(
                                                              AppLocalizations.of(context)!
                                                                  .translate('ltw_empty_data'));
                                                        }
                                                      } else {
                                                        return Text(
                                                            AppLocalizations.of(context)!
                                                                .translate('ltw_state')+': ${snapshot.connectionState}');
                                                      }
                                                    },
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.end,
                                          children: [
                                            InkWell(
                                              onTap: () {
                                                Loading.typeLoadingError(
                                                    AppLocalizations.of(context)!
                                                        .translate('ltw_coming_soon'));
                                                // Get.to(
                                                //   PreviousWinningNumbers(
                                                //     image: image,
                                                //     title: title,
                                                //   ),
                                                //   transition:
                                                //       Transition.rightToLeft,
                                                // );
                                              },
                                              child: Column(
                                                children: [
                                                  Container(
                                                    margin: EdgeInsets.only(
                                                        right: 5),
                                                    padding: EdgeInsets.only(
                                                        right: 14, top: 20),
                                                    width: double.infinity,
                                                    child: Image.asset(
                                                      image,
                                                      height: 14,
                                                      package: 'lock_to_win',
                                                      alignment:
                                                          Alignment.topRight,
                                                    ),
                                                  ),
                                                  Container(
                                                    margin: EdgeInsets.only(
                                                        right: 2),
                                                    padding: EdgeInsets.only(
                                                        top: 10, right: 16),
                                                    child: Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment.end,
                                                      children: [
                                                        Text(
                                                          AppLocalizations.of(context)!
                                                              .translate('ltw_previous_win'),
                                                          style: TextStyle(
                                                            fontSize: 12,
                                                            fontFamily:
                                                                "IBMPlexSansThai-Regular",
                                                            letterSpacing: 0.3,
                                                            color: Color(
                                                                0xff738097),
                                                          ),
                                                        ),
                                                        SizedBox(
                                                          width: 5,
                                                        ),
                                                        Icon(
                                                          Icons
                                                              .arrow_forward_ios,
                                                          color:
                                                              Color(0xff738097),
                                                          size: 12,
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            SizedBox(
                                              height: 30+Get.height*0.015,
                                            ),
                                            InkWell(
                                              onTap: () {
                                                Get.to(
                                                  GameRulesScreen(),
                                                  transition:
                                                      Transition.rightToLeft,
                                                );
                                              },
                                              child: Center(
                                                child: Container(
                                                  width: Get.width * 0.5,
                                                  alignment: Alignment.center,
                                                  decoration: BoxDecoration(
                                                    gradient: isDarkMode
                                                        ? LinearGradient(
                                                            begin: Alignment(
                                                                -1.0, 0.0),
                                                            end: Alignment(
                                                                1.0, 0.0),
                                                            colors: [
                                                              const Color(
                                                                  0x001d2532),
                                                              const Color(
                                                                  0xff1d2532),
                                                              const Color(
                                                                  0x001d2532)
                                                            ],
                                                            stops: [
                                                              0.0,
                                                              0.532,
                                                              1.0
                                                            ],
                                                          )
                                                        : LinearGradient(
                                                            begin: Alignment(
                                                                -1.0, 0.0),
                                                            end: Alignment(
                                                                1.0, 0.0),
                                                            colors: [
                                                              const Color(
                                                                  0x00fefefe),
                                                              const Color(
                                                                  0xfffefefe),
                                                              const Color(
                                                                  0x00fefefe)
                                                            ],
                                                            stops: [
                                                              0.0,
                                                              0.532,
                                                              1.0
                                                            ],
                                                          ),
                                                  ),
                                                  height: 24,
                                                  child: Text(
                                                    AppLocalizations.of(context)!
                                                        .translate('ltw_game_rule'),
                                                    style: TextStyle(
                                                      color: Color(0xff738097),
                                                      letterSpacing: 0.3,
                                                      fontSize: 11,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                            SizedBox(height: 10),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ),

                                // Complete last Item
                                controller.isTooltipNavigate == false
                                    ? Container(
                                        // margin: EdgeInsets.only(top: 2),
                                        padding: EdgeInsets.only(
                                            top: 19,
                                            right: 20,
                                            left: 20,
                                            bottom: 30),
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(20),
                                          color: isDarkMode
                                              ? Color(0xff1D2532)
                                              : Color(0xffFEFEFE),
                                          boxShadow: <BoxShadow>[
                                            BoxShadow(
                                              color: Color(0xff000000)
                                                  .withOpacity(0.1),
                                              blurRadius: 15.0,
                                              spreadRadius: 10,
                                              offset: Offset(0.0, 0.25),
                                            )
                                          ],
                                        ),
                                        child: Column(
                                          children: [
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                Text(
                                                  AppLocalizations.of(context)!
                                                      .translate('ltw_next_round'),
                                                  style: GoogleFonts
                                                      .ibmPlexSansThai(
                                                    fontSize: 12,
                                                    letterSpacing: 0.3,
                                                    color: Color(0xff8a96ab),
                                                  ),
                                                ),
                                                Text(
                                                  AppLocalizations.of(context)!
                                                      .translate('ltw_draw_time')+':',
                                                  style: GoogleFonts
                                                      .ibmPlexSansThai(
                                                    fontSize: 12,
                                                    letterSpacing: 0.3,
                                                    color: Color(0xff8a96ab),
                                                  ),
                                                )
                                              ],
                                            ),
                                            SizedBox(
                                              height: 5,
                                            ),
                                            FutureBuilder(
                                              future: _futureRoundNow,
                                              builder: (
                                                BuildContext context,
                                                AsyncSnapshot<List> snapshot,
                                              ) {
                                                print(snapshot.connectionState);
                                                if (snapshot.connectionState ==
                                                    ConnectionState.waiting) {
                                                  return Center(
                                                    child: SpinKitThreeBounce(
                                                      color: Colors.grey,
                                                      size: 50,
                                                    ),
                                                  );
                                                } else if (snapshot
                                                        .connectionState ==
                                                    ConnectionState.done) {
                                                  if (snapshot.hasError) {
                                                    return Text(AppLocalizations.of(context)!
                                                        .translate('ltw_error'));
                                                  } else if (snapshot.hasData) {
                                                    return Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .spaceBetween,
                                                      children: [
                                                        Component.roundNow(
                                                            snapshot.data![0]),
                                                        Component.dateTime(
                                                            snapshot
                                                                .data![1].date)
                                                      ],
                                                    );
                                                  } else {
                                                    return Text(
                                                        AppLocalizations.of(context)!
                                                            .translate('ltw_empty_data'));
                                                  }
                                                } else {
                                                  return Text(
                                                      AppLocalizations.of(context)!
                                                          .translate('ltw_state')+': ${snapshot.connectionState}');
                                                }
                                              },
                                            ),
                                            SizedBox(
                                              height: 10,
                                            ),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Text(
                                                      AppLocalizations.of(context)!
                                                          .translate('ltw_time_left')+':',
                                                      style: GoogleFonts
                                                          .ibmPlexSansThai(
                                                        color:
                                                            Color(0xff8a96ab),
                                                        letterSpacing: 0.3,
                                                        fontSize: 12,
                                                      ),
                                                    ),
                                                    storeController
                                                            .controller.isBlank!
                                                        ? Container()
                                                        : Component
                                                            .countDownTime(
                                                                storeController
                                                                    .controller),
                                                  ],
                                                ),
                                                controller.checkClaim
                                                    ? InkWell(
                                                        onTap: () async {
                                                          await storeController
                                                              .getYourLottoRound(
                                                                  storeController
                                                                      .getPK!);

                                                          Get.to(
                                                            () =>
                                                                LockToWinChoosenScreen(
                                                              image: isDarkMode
                                                                  ? lockToWinImage
                                                                  : lockToWinLightImage,
                                                            ),
                                                            transition:
                                                                Transition
                                                                    .rightToLeft,
                                                          );
                                                        },
                                                        child: Container(
                                                          alignment:
                                                              Alignment.center,
                                                          height: 36,
                                                          margin:
                                                              EdgeInsets.only(
                                                                  top: 5.0),
                                                          width: 168,
                                                          decoration:
                                                              BoxDecoration(
                                                            gradient: isDarkMode
                                                                ? LinearGradient(
                                                                    begin: Alignment
                                                                        .centerLeft,
                                                                    end: Alignment
                                                                        .centerRight,
                                                                    colors: [
                                                                      Color(
                                                                          0xFF1AAEFC),
                                                                      Color(
                                                                          0xFF384AE8),
                                                                    ],
                                                                  )
                                                                : LinearGradient(
                                                                    begin: Alignment
                                                                        .centerLeft,
                                                                    end: Alignment
                                                                        .centerRight,
                                                                    colors: [
                                                                      Color(
                                                                          0xFF384AE8),
                                                                      Color(
                                                                          0xFF384AE8),
                                                                    ],
                                                                  ),
                                                            boxShadow: isDarkMode
                                                                ? <BoxShadow>[]
                                                                : [
                                                                    BoxShadow(
                                                                      color: Color(
                                                                              0xff5B6AEC)
                                                                          .withOpacity(
                                                                              0.25),
                                                                      blurRadius:
                                                                          5.0,
                                                                      offset:
                                                                          Offset(
                                                                              0.0,
                                                                              8),
                                                                    )
                                                                  ],
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        50),
                                                          ),
                                                          child: FutureBuilder(
                                                            future: _futurePlayed,
                                                            builder: (
                                                                BuildContext context,
                                                                AsyncSnapshot<bool> snapshot,
                                                                ) {
                                                              print(snapshot.connectionState);
                                                              if (snapshot.connectionState ==
                                                                  ConnectionState.waiting) {
                                                                return Center(
                                                                  child: SpinKitThreeBounce(
                                                                    color: Colors.grey,
                                                                    size: 15,
                                                                  ),
                                                                );
                                                              } else if (snapshot
                                                                  .connectionState ==
                                                                  ConnectionState.done) {
                                                                if (snapshot.hasError) {
                                                                  return Text(AppLocalizations.of(context)!
                                                                      .translate('ltw_error'));
                                                                } else if (snapshot.hasData) {
                                                                  return
                                                                    Text(
                                                                      snapshot.data! ? AppLocalizations.of(context)!.translate('ltw_played')
                                                                          : AppLocalizations.of(context)!.translate('ltw_play_next_round'),
                                                                      style: GoogleFonts
                                                                          .ibmPlexSansThai(
                                                                        letterSpacing:
                                                                            0.5,
                                                                        fontSize: 14,
                                                                        color: isDarkMode
                                                                            ? Color(
                                                                                0xffEBEDFD)
                                                                            : Color(
                                                                                0xffFFFFFF),
                                                                      ),
                                                                    );
                                                                } else {
                                                                  return Text(
                                                                      AppLocalizations.of(context)!
                                                                          .translate('ltw_empty_data'));
                                                                }
                                                              } else {
                                                                return Text(
                                                                    AppLocalizations.of(context)!
                                                                        .translate('ltw_state')+': ${snapshot.connectionState}');
                                                              }
                                                            },
                                                          ),
                                                        ),
                                                      )
                                                    : Container(
                                                        alignment:
                                                            Alignment.center,
                                                        height: 36,
                                                        margin: EdgeInsets.only(
                                                            top: 5.0),
                                                        width: 168,
                                                        decoration:
                                                            BoxDecoration(
                                                          gradient: isDarkMode
                                                              ? LinearGradient(
                                                                  begin: Alignment
                                                                      .centerLeft,
                                                                  end: Alignment
                                                                      .centerRight,
                                                                  colors: [
                                                                    Colors
                                                                        .white70,
                                                                    Colors.grey,
                                                                  ],
                                                                )
                                                              : LinearGradient(
                                                                  begin: Alignment
                                                                      .centerLeft,
                                                                  end: Alignment
                                                                      .centerRight,
                                                                  colors: [
                                                                    Color(
                                                                        0xFF384AE8),
                                                                    Color(
                                                                        0xFF384AE8),
                                                                  ],
                                                                ),
                                                          boxShadow: isDarkMode
                                                              ? <BoxShadow>[]
                                                              : [
                                                                  BoxShadow(
                                                                    color: Color(
                                                                            0xff5B6AEC)
                                                                        .withOpacity(
                                                                            0.25),
                                                                    blurRadius:
                                                                        5.0,
                                                                    offset:
                                                                        Offset(
                                                                            0.0,
                                                                            8),
                                                                  )
                                                                ],
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(50),
                                                        ),
                                                        child: FutureBuilder(
                                                          future: _futurePlayed,
                                                          builder: (
                                                              BuildContext context,
                                                              AsyncSnapshot<bool> snapshot,
                                                              ) {
                                                            print(snapshot.connectionState);
                                                            if (snapshot.connectionState ==
                                                                ConnectionState.waiting) {
                                                              return Center(
                                                                child: SpinKitThreeBounce(
                                                                  color: Colors.grey,
                                                                  size: 15,
                                                                ),
                                                              );
                                                            } else if (snapshot
                                                                .connectionState ==
                                                                ConnectionState.done) {
                                                              if (snapshot.hasError) {
                                                                return Text(AppLocalizations.of(context)!
                                                                    .translate('ltw_error'));
                                                              } else if (snapshot.hasData) {
                                                                return
                                                                  Text(
                                                                    snapshot.data! ? AppLocalizations.of(context)!.translate('ltw_played')
                                                                        : AppLocalizations.of(context)!.translate('ltw_play_next_round'),
                                                                    style: GoogleFonts
                                                                        .ibmPlexSansThai(
                                                                      letterSpacing: 0.5,
                                                                      fontSize: 14,
                                                                      color: isDarkMode
                                                                          ? Color(
                                                                          0xffEBEDFD)
                                                                          : Color(
                                                                          0xffFFFFFF),
                                                                    ),
                                                                  );
                                                              } else {
                                                                return Text(
                                                                    AppLocalizations.of(context)!
                                                                        .translate('ltw_empty_data'));
                                                              }
                                                            } else {
                                                              return Text(
                                                                  AppLocalizations.of(context)!
                                                                      .translate('ltw_state')+': ${snapshot.connectionState}');
                                                            }
                                                          },
                                                        ),
                                                      ),
                                              ],
                                            ),
                                            SizedBox(
                                              height: 5,
                                            ),
                                          ],
                                        ),
                                      )
                                    : //Complete last Item with different items
                                    Container(
                                        padding: EdgeInsets.only(
                                            left: 10, right: 10),
                                        color: Colors.transparent,
                                        child: Container(
                                          margin: EdgeInsets.only(top: 5),
                                          child: Container(
                                            width: double.infinity,
                                            height: 270,
                                            decoration: BoxDecoration(
                                                borderRadius: BorderRadius.only(
                                                    topRight:
                                                        Radius.circular(20.0),
                                                    topLeft:
                                                        Radius.circular(20.0)),
                                                boxShadow: [
                                                  BoxShadow(
                                                    color: Color(0xff000000)
                                                        .withOpacity(0.16),
                                                    spreadRadius: 3,
                                                    blurRadius: 5,
                                                    offset: Offset(0,
                                                        -1), // changes position of shadow
                                                  ),
                                                ],
                                                gradient: LinearGradient(
                                                  begin: Alignment.topCenter,
                                                  end: Alignment.bottomCenter,
                                                  colors: [
                                                    Color(0xff1BA2E6),
                                                    Color(0xff172069),
                                                  ],
                                                )),
                                            child: Stack(
                                              children: [
                                                Container(
                                                  margin:
                                                      EdgeInsets.only(top: 20),
                                                  alignment:
                                                      Alignment.topCenter,
                                                  child: Text(
                                                      AppLocalizations.of(context)!
                                                          .translate('ltw_congration'),
                                                      style: TextStyle(
                                                          fontSize: 14,
                                                          letterSpacing: 0.3,
                                                          fontFamily:
                                                              "IBMPlexSansThai-Regular",
                                                          color: Color(
                                                              0xffE9ECF5))),
                                                ),
                                                Container(
                                                    margin: EdgeInsets.only(
                                                        top: 20),
                                                    alignment:
                                                        Alignment.topCenter,
                                                    child: Lottie.asset(
                                                      'assets/animatedIcons/You_Win.json',
                                                      package: 'lock_to_win',
                                                      width: 500,
                                                    )),
                                                Container(
                                                  height: 44,
                                                  margin: EdgeInsets.only(
                                                    top: 190,
                                                    left: 15,
                                                    right: 15,
                                                  ),
                                                  width: Get.width,
                                                  child: ElevatedButton(
                                                      style: ElevatedButton
                                                          .styleFrom(
                                                        primary:
                                                            Colors.transparent,
                                                        shadowColor:
                                                            Colors.transparent,
                                                        onPrimary:
                                                            Colors.transparent,
                                                        onSurface:
                                                            Colors.transparent,
                                                      ),
                                                      onPressed: () async {
                                                        final res = await storeController
                                                            .lotteryService
                                                            .multipleClaimButton(
                                                                tokenList:
                                                                    storeController
                                                                        .tokenIdWin,
                                                                address:
                                                                    storeController
                                                                        .dataUser!
                                                                        .address,
                                                                preRound:
                                                                    storeController
                                                                        .previousRound);

                                                        if (res) {
                                                          await FirebaseFirestore
                                                              .instance
                                                              .collection(
                                                                  'lockToWin')
                                                              .doc(widget.data[
                                                                  'address'])
                                                              .collection(
                                                                  'round')
                                                              .doc(
                                                                  '${storeController.previousRound.toInt()}')
                                                              .update({
                                                            "statusClaim":
                                                                "CLAIMED"
                                                          });
                                                          controller
                                                                  .checkClaim =
                                                              true;
                                                          controller
                                                              .setIsTooltipNavigate(
                                                                  false);
                                                        }

                                                        // Loading
                                                        //     .typeLoadingDismiss();
                                                        // Get.snackbar(
                                                        //   'title',
                                                        //   'message',
                                                        //   borderRadius: 12,
                                                        //   duration: Duration(
                                                        //       seconds: 2),
                                                        //   animationDuration:
                                                        //       Duration(
                                                        //           seconds: 1),
                                                        //   maxWidth: Get.width,
                                                        //   barBlur: 10,
                                                        //   margin:
                                                        //       EdgeInsets.all(0),
                                                        //   backgroundColor:
                                                        //       Color(0xffFFFFFF)
                                                        //           .withOpacity(
                                                        //               0.2),
                                                        //   messageText:
                                                        //       Container(
                                                        //     margin:
                                                        //         EdgeInsets.only(
                                                        //             bottom: 20),
                                                        //     alignment: Alignment
                                                        //         .center,
                                                        //     child: Text(
                                                        //       '2,065.667.09',
                                                        //       style: TextStyle(
                                                        //         color: isDarkMode
                                                        //             ? Colors
                                                        //                 .white
                                                        //             : Colors
                                                        //                 .black,
                                                        //         letterSpacing:
                                                        //             0.3,
                                                        //         fontSize: 24,
                                                        //       ),
                                                        //     ),
                                                        //   ),
                                                        //   titleText: Container(
                                                        //     margin:
                                                        //         EdgeInsets.only(
                                                        //             top: 20),
                                                        //     alignment: Alignment
                                                        //         .center,
                                                        //     child: Text(
                                                        //       'You earn (LIKE)',
                                                        //       style: TextStyle(
                                                        //         color: isDarkMode
                                                        //             ? Colors
                                                        //                 .white
                                                        //             : Colors
                                                        //                 .black,
                                                        //         letterSpacing:
                                                        //             0.3,
                                                        //         fontSize: 14,
                                                        //       ),
                                                        //     ),
                                                        //   ),
                                                        // );
                                                      },
                                                      child: Stack(
                                                        children: [
                                                          Container(
                                                            child: SvgPicture
                                                                .string(
                                                              '<svg viewBox="0.0 52.0 336.9 50.0" ><defs><linearGradient id="gradient" x1="0.0" y1="0.5" x2="1.0" y2="0.5"><stop offset="0.0" stop-color="#ffef9cff"  /><stop offset="1.0" stop-color="#ff6845fd"  /></linearGradient></defs><path transform="translate(0.03, 52.0)" d="M 25 5 L 311.93701171875 5 C 322.9819946289062 5 331.93701171875 13.95400047302246 331.93701171875 25 C 331.93701171875 36.04600143432617 322.9819946289062 45 311.93701171875 45 L 25 45 C 13.95400047302246 45 5 36.04600143432617 5 25 C 5 13.95400047302246 13.95400047302246 5 25 5 M 336.85400390625 26.94799995422363 C 336.9030151367188 26.30400085449219 336.93701171875 25.6560001373291 336.93701171875 25 C 336.93701171875 24.35700035095215 336.9049987792969 23.72200012207031 336.8569946289062 23.09099960327148 C 335.8770141601562 10.19499969482422 325.0790100097656 0 311.93701171875 0 L 25 0 C 11.21500015258789 0 0 11.21500015258789 0 25 C 0 38.58300018310547 10.89099979400635 49.66299819946289 24.39800071716309 49.98500061035156 C 24.59900093078613 49.9900016784668 24.79800033569336 50 25 50 L 311.93701171875 50 C 312.2109985351562 50 312.4819946289062 49.98799896240234 312.7550048828125 49.97900009155273 C 325.510986328125 49.56600189208984 335.8770141601562 39.55599975585938 336.85400390625 26.94799995422363 M 311.93701171875 49 L 25 49 C 11.76700019836426 49 1 38.23300170898438 1 25 C 1 11.76700019836426 11.76700019836426 1 25 1 L 311.93701171875 1 C 325.1700134277344 1 335.93701171875 11.76700019836426 335.93701171875 25 C 335.93701171875 38.23300170898438 325.1700134277344 49 311.93701171875 49" fill="url(#gradient)" fill-opacity="0.9" stroke="none" stroke-width="1" stroke-opacity="0.9" stroke-miterlimit="10" stroke-linecap="butt" /></svg>',
                                                              allowDrawingOutsideViewBox:
                                                                  true,
                                                              fit: BoxFit.fill,
                                                            ),
                                                          ),
                                                          Container(
                                                              child:
                                                                  AnimatedTextKit(
                                                            repeatForever: true,
                                                            animatedTexts: [
                                                              RotateAnimatedText(
                                                                AppLocalizations.of(context)!
                                                                    .translate('ltw_claim_prize'),
                                                                duration: Duration(
                                                                    milliseconds:
                                                                        1000),
                                                                textStyle:
                                                                    TextStyle(
                                                                  fontSize:
                                                                      16.0,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w600,
                                                                  color: Color(
                                                                      0xffEBEDFD),
                                                                  shadows: [
                                                                    Shadow(
                                                                      blurRadius:
                                                                          10.0,
                                                                      color: Color(
                                                                          0xffF5BFFF),
                                                                    ),
                                                                  ],
                                                                ),
                                                              ),
                                                            ],
                                                          ))
                                                        ],
                                                      )

                                                      // AnimatedTextKit(
                                                      //   repeatForever: true,
                                                      //   animatedTexts: [
                                                      //     RotateAnimatedText(
                                                      //       'CLAIM YOUR PRIZE',
                                                      //       textStyle: TextStyle(
                                                      //         fontSize: 16.0,
                                                      //         fontWeight: FontWeight.w600,
                                                      //         color: Color(0xffEBEDFD),
                                                      //       ),
                                                      //     ),
                                                      //   ],
                                                      // ),
                                                      ),
                                                )
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                              ],
                            ),
                          ),
                          // isStackItemShow == true
                          //     ?
                          Component.listNumber(controller: controller),
                          //   Align(
                          //   alignment: Alignment.bottomRight,
                          //   child: AnimatedContainer(
                          //     duration: Duration(milliseconds: 500),
                          //     height: height,
                          //     width: width,
                          //     color: Colors.red,
                          //     margin: EdgeInsets.only(
                          //         bottom: MediaQuery.of(context).size.height * 0.31),
                          //     child: ClipRRect(
                          //       child: GlassMorphism(
                          //         start: 0,
                          //         end: 0,
                          //         child: AnimatedContainer(
                          //           duration: Duration(milliseconds: 500),
                          //           padding: EdgeInsets.all(16),
                          //           alignment: Alignment.center,
                          //           width: width,
                          //           height: height,
                          //           decoration: BoxDecoration(
                          //             // color: isDarkMode ? Color(0xff020202) : Colors.white,
                          //             borderRadius: BorderRadius.circular(12),
                          //           ),
                          //           child: SingleChildScrollView(
                          //             child: Column(
                          //               children: [
                          //                 InkWell(
                          //                   onTap: () {
                          //                     isStackItemShow = false;
                          //                     setState(() {
                          //                       height = 0;
                          //                       width = 0;
                          //                       size = 0;
                          //                     });
                          //                   }, child: Container(
                          //                   alignment: Alignment.topRight,
                          //                   child: Image.asset(
                          //                     'assets/images/Group 36634.png',
                          //                     height: 20,
                          //                   ),
                          //                 ),
                          //
                          //                 ),
                          //                 SizedBox(
                          //                   height: 16,
                          //                 ),
                          //                 GridView.builder(
                          //                   shrinkWrap: true,
                          //                   physics: NeverScrollableScrollPhysics(),
                          //                   gridDelegate:
                          //                       SliverGridDelegateWithFixedCrossAxisCount(
                          //                     crossAxisCount: 5,
                          //                   ),
                          //                   itemCount: ballon.length,
                          //                   itemBuilder: (context, i) {
                          //                     return Container(
                          //                       alignment: Alignment.center,
                          //                       margin: EdgeInsets.only(
                          //                           bottom: 12, left: 6, right: 6),
                          //                       height: size,
                          //                       width: size,
                          //                       decoration: BoxDecoration(
                          //                         gradient: ballon[i].gradient!,
                          //                         borderRadius: BorderRadius.circular(100),
                          //                       ),
                          //                       child: Text(
                          //                         ballon[i].number!,
                          //                         style: TextStyle(
                          //                           fontSize: 14,
                          //                           fontWeight: FontWeight.bold,
                          //                           color: Colors.black,
                          //                         ),
                          //                       ),
                          //                     );
                          //                   },
                          //                 ),
                          //               ],
                          //             ),
                          //           ),
                          //         ),
                          //       ),
                          //     ),
                          //   ),
                          // ),

                          controller.tolTipVisible == true
                              ? Positioned(
                                  child: Container(
                                    margin: EdgeInsets.only(left: 80, top: 145),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.end,
                                      children: [
                                        SizedBox(
                                          height: MediaQuery.of(context)
                                                  .size
                                                  .height *
                                              0.077,
                                        ),
                                        Stack(
                                          children: [
                                            Container(
                                              padding: EdgeInsets.only(
                                                bottom: 14,
                                              ),
                                              width: 235,
                                              height: 140,
                                              child: Container(
                                                margin:
                                                    EdgeInsets.only(top: 10),
                                                decoration: BoxDecoration(
                                                  color: Colors.transparent,
                                                  borderRadius:
                                                      BorderRadius.circular(14),
                                                ),
                                                width: 230,
                                                height: 120,
                                                child: ClipRect(
                                                  child: BackdropFilter(
                                                    filter: ImageFilter.blur(
                                                        sigmaX: 2.0,
                                                        sigmaY: 2.0),
                                                    child: Container(
                                                      decoration: BoxDecoration(
                                                        color: Color(0xff3A4A64)
                                                            .withOpacity(0.6),
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(14),
                                                      ),
                                                      child: Container(
                                                        padding:
                                                            EdgeInsets.only(
                                                          top: 15,
                                                          left: 12,
                                                          right: 12,
                                                        ),
                                                        child: Text(
                                                          AppLocalizations.of(context)!
                                                              .translate('ltw_prize_detail'),
                                                          style: TextStyle(
                                                            fontSize: 14.0,
                                                            color: isDarkMode
                                                                ? Color(
                                                                    0xffEF9CFF)
                                                                : Color(
                                                                    0xffd912ff),
                                                          ),
                                                          textAlign:
                                                              TextAlign.left,
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                            Positioned(
                                              bottom: 4.0,
                                              right: 30,
                                              child: ClipPath(
                                                clipper: TriangleClipper(),
                                                child: Container(
                                                  color: Color(0xff3A4A64)
                                                      .withOpacity(0.6),
                                                  height: 10,
                                                  width: 20,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                )
                              : Container(),
                          isSecondToptipVisible == false
                              ? Positioned(
                                  right: 20,
                                  bottom: 160,
                                  child: InkWell(
                                    onTap: () {
                                      isSecondToptipVisible = true;
                                      setState(() {});
                                    },
                                    child: Container(
                                      alignment: Alignment.bottomCenter,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.end,
                                        children: [
                                          SizedBox(
                                            height: 40,
                                          ),
                                          Container(
                                            padding: EdgeInsets.only(
                                                top: 30, left: 25, right: 25),
                                            width: 227,
                                            height: 160,
                                            decoration: BoxDecoration(
                                              color: Color(0xff2B374B),
                                              borderRadius:
                                                  BorderRadius.circular(14),
                                            ),
                                            child: Text(
                                              AppLocalizations.of(context)!
                                                  .translate('ltw_win_detail'),
                                              style: TextStyle(
                                                color: Color(0xffD3AA00),
                                              ),
                                              textAlign: TextAlign.center,
                                            ),
                                          ),
                                          Container(
                                            margin: EdgeInsets.only(right: 60),
                                            child: Image.asset(
                                              'assets/images/downward triangle.png',
                                              height: 20,
                                              package: 'lock_to_win',
                                              color: Color(0xff2B374B),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                )
                              : Container(),
                        ],
                      ),
                    ),
                  ))
          : Container(
              color: Colors.grey,
            ),
    );
  }

  bool isSecondToptipVisible = true;
  bool isButtonShow = true;
  // void changeBool(bool change) {
  //   change = isTooltipNavigate;
  // }

  Widget ballon2({String? number, int? bGColor}) {
    return Container(
      alignment: Alignment.center,
      margin: EdgeInsets.only(bottom: 10, left: 5, right: 5),
      height: 50,
      width: 50,
      decoration: BoxDecoration(
        color: Color(0xffD0F74E),
        borderRadius: BorderRadius.circular(100),
      ),
      child: Text(
        '3553',
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: Colors.black,
        ),
      ),
    );
  }
}
