import 'dart:convert';
import 'dart:math';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:lock_to_win/app_config.dart';
import 'package:lock_to_win/library/address_service.dart';
import 'package:lock_to_win/library/configuration_service.dart';
import 'package:lock_to_win/models/ballon2.dart';
import 'package:lock_to_win/models/lotto/annonces_time.dart';
import 'package:lock_to_win/models/lotto/lotto_now_round.dart';
import 'package:lock_to_win/models/lotto/lotto_round.dart';
import 'package:lock_to_win/models/lotto/your_lotto_list.dart';
import 'package:lock_to_win/models/lotto/your_lotto_list_pre_round.dart';
import 'package:lock_to_win/models/lotto/data_user.dart';
import 'package:lock_to_win/services/lotto_service.dart';
import 'package:lock_to_win/widgets/color.dart';
import 'package:lock_to_win/widgets/loading.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_countdown_timer/index.dart';
import 'package:http/http.dart' as http;

class LotteryController extends GetxController {
  /// You do not need that. I recommend using it just for ease of syntax.
  /// with static method: Controller.to.increment();
  /// with no static method: Get.find<Controller>().increment();
  /// There is no difference in performance, nor any side effect of using either syntax. Only one does not need the type, and the other the IDE will autocomplete it.
  static LotteryController get to => Get.find(); // add this line

  late IConfigurationService configETH;
  late LotteryServiceFunction lotteryService;
  late IAddressService addressService;
  late CountdownTimerController controller;

  bool readyState = false;
  int counter = 0;
  String? getPK;
  String? address;
  double? lastRound;
  double? preRound;
  bool isYes = false;
  bool checkMultiClaim = false;
  bool isTooltipNavigate = false;
  bool isStackItemShow = false;
  bool isRandomNumberSuccess = false;
  double height = 0;
  double width = 0;
  double size = 0;
  double balanceTicket = 0.0;
  List<Ballon2> ballon = [];

  List<double> tokenIdWinList = [];
  List tokenIdWin = [];
  bool tolTipVisible = false;
  List<List<int>> dataList = [];
  List<Ballon2> selected = [];
  List<Ballon2> selected1 = [];
  List<Ballon2> selected2 = [];
  List<Ballon2> selected3 = [];
  List yourNumber = [];
  DataUser? dataUser;
  bool checkClaim = false;

  void setPlayLast(bool value) {
    isYes = value;
    update();
  }

  void setIsTooltipNavigate(bool value) {
    isTooltipNavigate = value;

    update();
  }

  void setIsStackItemShow(bool value) {
    isStackItemShow = value;
    update();
  }

  Random? rnd;
  Future<int> getRandomIntInclusive() async {
    int min = 1;
    int max = 10;
    rnd = new Random();
    int r = min + rnd!.nextInt(max - min);
    return r;
  }

  Future<List<List<int>>> randomNumber20() async {
    dataList.clear();
    for (var i = 0; i < 20; i++) {
      var one = await getRandomIntInclusive();
      var two = await getRandomIntInclusive();
      var three = await getRandomIntInclusive();
      var four = await getRandomIntInclusive();
      final data = [one, two, three, four];
      dataList.add(data);
    }
    return dataList;
  }

  Future<List<List<int>>> randomNumberFree(int amount) async {
    List<List<int>> dataList = [];
    for (var i = 0; i < amount; i++) {
      var one = await getRandomIntInclusive();
      var two = await getRandomIntInclusive();
      var three = await getRandomIntInclusive();
      var four = await getRandomIntInclusive();
      final data = [one, two, three, four];
      dataList.add(data);
    }
    return dataList;
  }

  void setRandomNumberSuccess(bool value) {
    isRandomNumberSuccess = value;
    update();
  }

  void clearNumber() {
    selected = [];
    selected1 = [];
    selected2 = [];
    selected3 = [];
    update();
  }

  void setTolTipVisible(bool value) {
    tolTipVisible = value;
    update();
  }

  Future<bool> init({required Map<String, Object> data}) async {
    selected = [];
    lotteryService = LotteryService();
    SharedPreferences pref = await SharedPreferences.getInstance();
    configETH = new ConfigurationService(pref);
    addressService = new AddressService(configETH);
    getPK = addressService.getPrivateKey('${data['mnemonic']}');
    address = '${data['address']}';
    update();
    if (getPK!.isNotEmpty && address!.isNotEmpty) {
      Loading.typeLoadingSuccess('Ready to use');
      AnnoncesTime? annoncesTime = await lotteryService.announcesCountDown();
      controller = CountdownTimerController(
          endTime: annoncesTime.date.millisecondsSinceEpoch, onEnd: onEnd);
      readyState = true;
      update();
      return readyState;
    } else {
      readyState = false;
      update();
      return readyState;
    }
  }

  Future<bool> checkSyncDataFromFirebase({required String address}) async {
    try {
      lotteryService = LotteryService();
      preRound = await lotteryService.lastRound() - 1;
      print("preRound" + preRound.toString());
      final data = await FirebaseFirestore.instance
          .collection('lockToWin')
          .doc(address)
          .collection('round')
          .doc('${preRound!.toInt()}')
          .get();
      if (data.exists) {
        print('exists');
        if (data.data()!['yourLottoList'] != null) {
          await syncDataFirebase(address: address);
        }
        return true;
      } else {
        print('syncDataFirebase');
        await syncDataFirebase(address: address);
        return true;
      }
    } catch (e) {
      print(e);
      return false;
    }
  }

  addRandomRow(random20) {
    selected = [];
    print(random20);
    for (var i = 0; i < random20.length; i++) {
      selected.add(Ballon2(
        number: random20[i].join().toString(),
        gradient: ColorTheme.linearGradientRandom20[i],
        textColor: ColorTheme.textColorRandom20[i],
        style: TextStyle(),
      ));
    }
  }

  Future<List> checkClaimReward(dataUser, YourLottoListPreRound data) async {
    Loading.typeLoadingAwaitTap('กำลังตรวจรางวัล');
    preRound = await lotteryService.lastRound() - 1;

    List dataList = [];
    data.data!.forEach((element) {
      dataList.add({
        '"number"': element.number,
        '"tokendId"': element.tokendId,
        '"reward"': element.reward
      });
    });

    print('${dataUser['token']}');
    final idToken = await verifyToken('${dataUser['token']}');
    if (idToken!.isNotEmpty) {
      final isTooltipNavigate = await lotteryService.checkMultiClaim(
          tokenNumber: dataList,
          idToken: idToken,
          address: dataUser['address'],
          preRound: preRound!);
      setIsTooltipNavigate(isTooltipNavigate);
    }
    await FirebaseFirestore.instance
        .collection('lockToWin')
        .doc(dataUser['address'])
        .collection('round')
        .doc('${preRound!.toInt()}')
        .update({"checkStatus": true});

    getYourLottoPreRound(dataUser: dataUser);

    return yourNumber;
  }

  // Future<bool> checkClaim(YourLottoListPreRound yourLottoListPreRound) async {
  //   final dataYourLotto = [];
  //   for (var i = 0; i < yourLottoListPreRound.data!.length; i++) {
  //     var num = 0;
  //     if (winner![0].toInt() ==
  //         int.parse(yourLottoListPreRound.data![i].number.split('')[0])) {
  //       num += 2;
  //     }
  //     if (winner![1].toInt() ==
  //         int.parse(yourLottoListPreRound.data![i].number.split('')[1])) {
  //       num += 1;
  //     }
  //     if (winner![2].toInt() ==
  //         int.parse(yourLottoListPreRound.data![i].number.split('')[2])) {
  //       num += 1;
  //     }
  //     if (winner![3].toInt() ==
  //         int.parse(yourLottoListPreRound.data![i].number.split('')[3])) {
  //       num += 1;
  //     }
  //     dataYourLotto.add({
  //       'number': yourLottoListPreRound.data![i].number.toString(),
  //       "amountWin": num
  //     });
  //   }
  //   bool status = false;
  //   for (var i = 0; i < dataYourLotto.length; i++) {
  //     print(dataYourLotto[i]['amountWin']);
  //     if (dataYourLotto[i]['amountWin'] >= 2) {
  //       status = true;
  //       break;
  //     } else {
  //       status = false;
  //     }
  //   }
  //   return status;
  // }

  // Future<void> checkClaimPreRound(dataUser) async {
  //   final data = await FirebaseFirestore.instance
  //       .collection('lockToWin')
  //       .doc(dataUser['address'])
  //       .collection('round')
  //       .doc('${preRound!.toInt()}')
  //       .get();
  //
  //
  // }

  YourLottoListPreRound? yourLottoListPreRound;
  Future<YourLottoListPreRound?> getYourLottoPreRound(
      {required dataUser}) async {
    try {
      print('${preRound!.toInt()}');
      print(dataUser['address']);
      ballon.clear();
      if (getPK != null) {
        final data = await FirebaseFirestore.instance
            .collection('lockToWin')
            .doc(dataUser['address'])
            .collection('round')
            .doc('${preRound!.toInt()}')
            .get();
        tokenIdWin = data.data()!['tokenIdWinList'];
        print(tokenIdWin);
        yourLottoListPreRound =
            YourLottoListPreRound.fromJson(data.data()!['yourData']);
        print(yourLottoListPreRound!.data!);
        //TODO sync Data push Firestore
        for (var i = 0; i < yourLottoListPreRound!.data!.length; i++) {
          ballon.add(Ballon2(
              number: yourLottoListPreRound!.data![i].number,
              gradient: ColorTheme.colorNumber[i]));
        }
        // //TODO ตรวจรางวัลสถานะ = false
        if (data.data()!['checkStatus'] == false) {
          checkClaimReward(dataUser, yourLottoListPreRound!);
        }
        //TODO ตรวจแล้วสถานะ = true
        else {
          //TODO ตรวจแล้วสถานะ 1.ไม่ถูกรางวัล false,SUCCESS ,2.ถูกรางวัลกดรับเเล้ว
          if ((data.data()!['statusWinner'] == false &&
                  data.data()!['statusClaim'] == 'SUCCESS') ||
              (data.data()!['statusWinner'] == true &&
                  data.data()!['statusClaim'] == 'CLAIMED')) {
            checkClaim = true;
            update();
          } else if (data.data()!['statusWinner'] == true &&
              data.data()!['statusClaim'] == 'PENDING') {
            setIsTooltipNavigate(true);
            checkClaim = false;
            update();
          } else {}
        }
        update();
      }
      // print(yourLottoListPreRound!);
      return yourLottoListPreRound!;
    } catch (e) {
      print(e);
      return null;
    }
  }

  Future<void> syncDataFirebase({required String address}) async {
    try {
      List setData = [];
      Loading.typeLoadingAwait('Please wait syncing data');
      if (getPK != null) {
        lotteryService = LotteryService();
        preRound = await lotteryService.lastRound() - 1;
        final data = await lotteryService.lottoPreNumber(
            pketh: getPK!, round: preRound!.toInt());
        //setData push Firestore
        print("data[0] ${data[0]}");
        print("data[0] ${data[0].length}");
        for (var i = 0; i < data[0].length; i++) {
          setData.add({
            "number": data[0][i]['number'].join(),
            "tokendId": data[0][i]['tokendId'],
            "reward": data[0][i]['reward'],
          });
          if (data[0][i]['reward'] > 0) {
            tokenIdWinList.add(double.parse(data[0][i]['tokendId']));
          }
          print(setData);
        }

        //Add to database
        await FirebaseFirestore.instance
            .collection('lockToWin')
            .doc('${address}')
            .collection('round')
            .doc('${preRound!.toInt()}')
            .set({
          "checkStatus": false,
          "statusWinner": false,
          "statusClaim": "PENDING",
          "tokenIdWinList": FieldValue.arrayUnion(tokenIdWinList),
          "yourData": FieldValue.arrayUnion(setData)
        });
      }
      Loading.typeLoadingDismiss();
    } catch (e) {
      print(e);
      Loading.typeLoadingDismiss();
    }
  }

  String? idToken;
  Future<String?> verifyToken(String token) async {
    try {
      var url = Uri.parse(
          "https://identitytoolkit.googleapis.com/v1/accounts:signInWithCustomToken?key=AIzaSyC-p2YK544BFMY2AwJVAdBI_iKpTphbuPc");
      final response = await http
          .post(url, body: {"token": token, "returnSecureToken": 'true'});
      print(response.statusCode);
      if (response.statusCode == 200) {
        var body = json.decode(response.body);
        idToken = body['idToken'];
        update();
        return body['idToken'];
      } else {
        return null;
      }
    } catch (e) {
      print(e);
      return null;
    }
  }

  // late YourLottoList yourLottoList;
  List<dynamic> yourLottoList = [];
  Future<List<dynamic>> getYourLottoRound(String pk) async {
    Loading.typeLoadingAwaitTap('กำลังตรวจสอบการทาย');

    if (pk.isNotEmpty) {
      var round = await lotteryService.lastRound();
      print("round" + round.toString());
      await FirebaseFirestore.instance
          .collection('lockToWin')
          .doc(address)
          .collection('round')
          .doc('${lastRound!.toInt()}')
          .get()
          .then((value) {
        if (value.exists) {
          print("data " + value.data()!['yourLottoList'].toString());
          yourLottoList = value.data()!['yourLottoList'];
          update();
        } else {
          yourLottoList = [];
          update();
        }
      });
    }
    EasyLoading.dismiss();
    return yourLottoList;
  }

  Future<bool> checkLottoLastRound() async {
    var round = await lotteryService.lastRound();
    return await FirebaseFirestore.instance
        .collection('lockToWin')
        .doc(address)
        .collection('round')
        .doc('${round.toInt()}')
        .get()
        .then((value) {
      if (value.exists) {
        return true;
      } else {
        return false;
      }
    });
  }

  // late YourLottoList yourLottoList;
  // Future<YourLottoList> getYourLottoRound(String pk) async {
  //   Loading.typeLoadingAwaitTap('กำลังตรวจสอบการทาย');
  //
  //   if (pk.isNotEmpty) {
  //     var round = await lotteryService.lastRound();
  //     print("round" + round.toString());
  //     yourLottoList =
  //     await lotteryService.lottoNumber(pketh: pk, round: round.toInt());
  //     print("data " + yourLottoList.data.toString());
  //     update();
  //   }
  //   EasyLoading.dismiss();
  //   return yourLottoList;
  // }
  AnnoncesTime? annoncesTime;
  Future<AnnoncesTime?> getAnnoncesTime() async {
    annoncesTime = await lotteryService.announcesCountDown();
    update();
    return annoncesTime;
  }

  AnnoncesTime? preTime;
  Future<AnnoncesTime?> getPreviousTime() async {
    preTime = await lotteryService.previousTime();
    update();
    return preTime;
  }

  Future<LottoRound> getRoundLast() async {
    lastRound = await lotteryService.lastRound();
    double previousRound = lastRound! - 1;

    LottoRound lottoRoundLast =
        await lotteryService.lottoRound(round: previousRound.toInt());
    return lottoRoundLast;
  }

  Future<double> getRoundNow() async {
    lastRound = await lotteryService.lastRound();
    return lastRound!;
  }

  List<num>? winner = [];
  double previousRound = 0.0;
  Future<List<num>?> preRoundWinner() async {
    lastRound = await lotteryService.lastRound();
    previousRound = lastRound! - 1;
    update();
    winner = await lotteryService.preRoundWinner(round: previousRound.toInt());
    update();
    return winner;
  }

  Future<double> getPreviousRoundNumber() async {
    lotteryService = LotteryService();
    final pre = await lotteryService.lastRound() - 1;
    return pre;
  }

  void onEnd() {
    print('onEnd');
  }

  Future<double> getBalanceTicket() async {
    balanceTicket = await lotteryService.balanceTicket(address: address!);
    update();
    return balanceTicket;
  }

  LottoNowRound? lottoNowRound;
  Future<String> getNowRoundReward() async {
    lottoNowRound = await lotteryService.lottoNowRound();
    update();
    return lottoNowRound!.total;
  }

  void clearController() async {
    setTolTipVisible(false);
    setIsStackItemShow(false);
    setRandomNumberSuccess(false);
    setIsTooltipNavigate(false);
    update();
  }

  Future<bool> chooseBuyTicket({
    required String token,
    required List tricketNumber,
  }) async {
    await EasyLoading.show(
      status: 'กำลังดำเนินการ',
      maskType: EasyLoadingMaskType.black,
      dismissOnTap: true,
    );
    // var url = Uri.parse('http://new.likepoint.io/buyticket');
    var url = Uri.https(env.apiUrl, "/chooseBuyTicket");
    bool status = false;
    print({
      "apiKey": env.APIKEY,
      "secretKey": env.SECRETKEY,
      "token": token,
      'tokenList': tricketNumber.toString()
    });
    lastRound = await lotteryService.lastRound();

    List data = [];
    for (var i in tricketNumber) {
      data.add(i.join(''));
    }
    try {
      final response = await http.post(url, body: {
        "apiKey": env.APIKEY,
        "secretKey": env.SECRETKEY,
        "token": token,
        'tokenList': tricketNumber.toString()
      });

      if (response.statusCode == 200) {
        var body = json.decode(response.body);

        print(body);
        if (body['result'] == 'SUCCESS') {
          await FirebaseFirestore.instance
              .collection('lockToWin')
              .doc(address)
              .collection('round')
              .doc('${lastRound!.toInt()}')
              .set({
            "yourLottoList": FieldValue.arrayUnion(data),
          });
          await EasyLoading.dismiss();
          return status = true;
        } else if (body['result'] == 'NEED_LIKEPOINT_IN_WALLET') {
          // LikeWalletAppTheme.configLoadingError();
          EasyLoading.showError('คุณต้องมี likepoint ติดกระเป๋า');
          return status = false;
        } else if (body['result'] == 'USED_TICKET') {
          // LikeWalletAppTheme.configLoadingError();
          EasyLoading.showError('คุณทายไปเเล้ว');
          return status = false;
        } else if (body['result'] == 'NO_TICKET') {
          // LikeWalletAppTheme.configLoadingError();
          EasyLoading.showError('คุณไม่มีตั๋ว โปรดล็อคไลท์เพิ่ม');
          return status = false;
        }
        await EasyLoading.dismiss();
      }
    } catch (e) {
      print(e);
      // LikeWalletAppTheme.configLoadingError();
      EasyLoading.showError(e.toString());
      return status = false;
    }
    return status;
  }

  void setDataUser(dynamic data) {
    dataUser = DataUser.fromJson(data);
    update();
  }
}
