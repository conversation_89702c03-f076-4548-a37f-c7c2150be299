import 'dart:typed_data';
import 'configuration_service.dart';
import 'package:bip39/bip39.dart' as bip39;
import "package:hex/hex.dart";
import 'package:web3dart/credentials.dart';
import 'package:bip32/bip32.dart' as bip32;

abstract class IAddressService {
  String generateMnemonic();
  String getPrivateKey(String mnemonic);
  String getPrivateKeyTest();
  Future<EthereumAddress> getPublicAddress(String privateKey);
  Future<bool> setupFromMnemonic(String mnemonic);
  Future<bool> setupFromPrivateKey(String privateKey);
  String entropyToMnemonic(String entropyMnemonic);
  String getPrivateKey44(String mnemonic);
}

class AddressService implements IAddressService {
  IConfigurationService _configService;
  AddressService(this._configService);

  @override
  String getPrivateKey44(String mnemonic) {
    String seed = bip39.mnemonicToSeedHex(mnemonic);
//    KeyData master = ED25519_HD_KEY.derivePath("m/0'/2147483647'/0'/2147483646'/0'", seed);
//    final privateKey = HEX.encode(master.key);
    final root = bip32.BIP32.fromSeed(HEX.decode(seed) as Uint8List);
    final child1 = root.derivePath("m/44'/60'/0'/0/0");
    final privateKey = HEX.encode(child1.privateKey as List<int>);
    return privateKey;
  }

  @override
  String generateMnemonic() {
    return bip39.generateMnemonic();
  }

  String entropyToMnemonic(String entropyMnemonic) {
    return bip39.entropyToMnemonic(entropyMnemonic);
  }

  @override
  String getPrivateKey(String mnemonic) {
    String seed = bip39.mnemonicToSeedHex(mnemonic);
//    KeyData master = ED25519_HD_KEY.derivePath("m/0'/2147483647'/0'/2147483646'/0'", seed);
//    final privateKey = HEX.encode(master.key);
//    print('mnemonic getPK : '+ mnemonic);
    final root = bip32.BIP32.fromSeed(HEX.decode(seed) as Uint8List);
    final string = root.toBase58();
    final restored = bip32.BIP32.fromBase58(string);
    final child1 = restored.derivePath("m/44'/60'/0'/0/0");
    final privateKey = HEX.encode(child1.privateKey as List<int>);

    return privateKey;
  }

  String getPrivateKeyTest() {
    String seed = bip39.mnemonicToSeedHex(
        'path one ceiling head menu robot video already dry quantum analyst disagree');

//    KeyData master = ED25519_HD_KEY.derivePath("m/0'/2147483647'/0'/2147483646'/0'", seed);
//    final privateKey = HEX.encode(master.key);
//    print('mnemonic getPK : '+ mnemonic);
    final root = bip32.BIP32.fromSeed(HEX.decode(seed) as Uint8List);
//    print('defaut pk : '+ HEX.encode(root.privateKey));
//    0x160f89D3AEacCB0955E14B3D0BC30C10509A56DE
//    print("root default : "+ HEX.encode(root.derivePath("m/44'/60'/0'/0/0").privateKey));
//    print("root: "+ root.derivePath("m/44'/60'/0'/0").privateKey.toString());
    final string = root.toBase58();
    final restored = bip32.BIP32.fromBase58(string);
//    final child1 = root.derivePath("m/44'/60'/0'/0/0");
    final child1 = restored.derivePath("m/44'/60'/0'/0/0");
    final privateKey = HEX.encode(child1.privateKey as List<int>);

    // print(privateKey);
    return privateKey;
  }

  @override
  Future<EthereumAddress> getPublicAddress(String privateKey) async {
    final private = EthPrivateKey.fromHex(privateKey);
    final address = await private.extractAddress();
    print("address: $address");
    return address;
  }

  @override
  Future<bool> setupFromMnemonic(String mnemonic) async {
    final cryptMnemonic = bip39.mnemonicToEntropy(mnemonic);
    await _configService.setPrivateKey(null.toString());
    // await _configService.setMnemonic(cryptMnemonic);
    await _configService.setupDone(true);
    return true;
  }

  @override
  Future<bool> setupFromPrivateKey(String privateKey) async {
    // await _configService.setMnemonic(null.toString());
    await _configService.setPrivateKey(privateKey);
    await _configService.setupDone(true);
    return true;
  }
}
