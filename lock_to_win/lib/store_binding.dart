import 'package:firebase_core/firebase_core.dart';
import 'package:get/get.dart';
import 'package:global_configuration/global_configuration.dart';
import 'package:lock_to_win/controller/lottery.dart';

class StoreBinding implements Bindings {
  @override
  void dependencies() async {
    Get.lazyPut(() => LotteryController());
    await GlobalConfiguration()
        .loadFromUrl("https://new.likepoint.io/configAPInew");
    await Firebase.initializeApp(
      options: const FirebaseOptions(
        apiKey: "AIzaSyC-p2YK544BFMY2AwJVAdBI_iKpTphbuPc",
        appId: "1:126028778629:android:be11f9d589d464cceaedec",
        messagingSenderId: "126028778629",
        projectId: "newlikewallet",
      ),
    );
    print('connected');
  }
}
