import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get/get.dart';
import 'package:global_configuration/global_configuration.dart';
import 'package:lock_to_win/library/address_service.dart';
import 'package:lock_to_win/library/configuration_service.dart';
import 'package:lock_to_win/screens/lock_to_win_screen.dart';
import 'package:lock_to_win/services/lotto_service.dart';
import 'package:lock_to_win/store_binding.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:likewallet/libraryman/app_local.dart';
// import 'package:lock_to_win//screens/bottom_bar.dart';
ThemeMode currentThemeMode = ThemeMode.dark;
bool isDarkMode = true;

// void main() async {
//   WidgetsFlutterBinding.ensureInitialized();
//
//   runApp(LockToWinMain());
// }

class LockToWinMain extends StatefulWidget {
  LockToWinMain(this.data);
  final data;
  @override
  State<LockToWinMain> createState() => _LockToWinMainState();
}

class _LockToWinMainState extends State<LockToWinMain> {
  refresh() {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      debugShowCheckedModeBanner: false,
      initialBinding: StoreBinding(),
      title: 'XLITE',
      builder: EasyLoading.init(),
      initialRoute: '/lock-to-win',
      locale:
      widget.data['lang'] == null ? Locale('en') : Locale(widget.data['lang']),
      supportedLocales: [
        Locale('en', 'US'),
        Locale('th', '')
      ],
      localizationsDelegates: [
        AppLocalizations.delegate, // Add this line
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate
      ],
      routes: {
        // When navigating to the "/" route, build the FirstScreen widget.
        '/lock-to-win': (context) => LockToWinScreen(data: widget.data),
        // When navigating to the "/second" route, build the SecondScreen widget.
      },
    );
  }
}
