import 'package:flutter/material.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

class ColorTheme {
  ColorTheme._();
  static const Color blue = Color(0xff0FE8D8);
  static const Color purple = Color(0xffA989FF);
  static const Color darkBlue = Color(0xff00a9c8);
  static void configLoadingError() {
    EasyLoading.instance
      ..indicatorType = EasyLoadingIndicatorType.fadingCircle
      ..loadingStyle = EasyLoadingStyle.custom
      ..indicatorSize = 45.0
      ..progressColor = Colors.white
      ..backgroundColor = Colors.red.withOpacity(0.8)
      ..indicatorColor = Colors.white
      ..textColor = Colors.white
      ..maskColor = Colors.grey.withOpacity(0.5)
      ..userInteractions = false
      ..dismissOnTap = false
      ..fontSize = 13;
  }

  static void configLoadingAwait() {
    EasyLoading.instance
      ..indicatorType = EasyLoadingIndicatorType.fadingCircle
      ..loadingStyle = EasyLoadingStyle.custom
      ..indicatorSize = 45.0
      ..progressColor = Colors.black38
      ..backgroundColor = Colors.black38.withOpacity(0.9)
      ..indicatorColor = Colors.white
      ..textColor = Colors.white
      ..maskColor = Colors.black.withOpacity(0.5)
      ..userInteractions = false
      ..dismissOnTap = true
      ..fontSize = 13;
  }

  static void configLoadingSuccess() {
    EasyLoading.instance
      ..indicatorType = EasyLoadingIndicatorType.fadingCircle
      ..loadingStyle = EasyLoadingStyle.custom
      ..indicatorSize = 45.0
      ..progressColor = Colors.white
      ..backgroundColor = Color(0xff08eef3)
      ..indicatorColor = Colors.white
      ..textColor = Colors.white
      ..maskColor = Colors.grey.withOpacity(0.5)
      ..userInteractions = false
      ..dismissOnTap = false
      ..fontSize = 13;
  }

  static List<LinearGradient> colorNumber = [
    LinearGradient(
      colors: [
        Color(0xffD0F74E),
        Color(0xffFFDC64),
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ),
    LinearGradient(
      colors: [
        Color(0xff3DFFFF),
        Color(0xff00AADD),
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ),
    LinearGradient(
      colors: [
        Color(0xff7979FF),
        Color(0xff4D4DFF),
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ),
    LinearGradient(
      colors: [
        Color(0xffFDC441),
        Color(0xffFF9300),
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ),
    LinearGradient(
      colors: [
        Color(0xff94F990),
        Color(0xff00CBD4),
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ),
    LinearGradient(
      colors: [
        Color(0xffFDC441),
        Color(0xffFF9300),
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ),
    LinearGradient(
      colors: [
        Color(0xff7979FF),
        Color(0xff4D4DFF),
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ),
    LinearGradient(
      colors: [
        Color(0xffF9B1FF),
        Color(0xffF9B1FF),
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ),
    LinearGradient(
      colors: [
        Color(0xff53DEFF),
        Color(0xff00CEFF),
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ),
    LinearGradient(
      colors: [
        Color(0xffD7A8FF),
        Color(0xff7846FF),
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    )
  ];
  static List<Color> textColorRandom20 = [
    Color(0xff000000),
    Color(0xffEA84FD),
    Color(0xff000000),
    Color(0xff000000),
    Color(0xff00F5FF),
    Color(0xffFFFFFF),
    Color(0xff000000),
    Color(0xffEA84FD),
    Color(0xff000000),
    Color(0xff000000),
    Color(0xff000000),
    Color(0xff00F5FF),
    Color(0xff000000),
    Color(0xff000000),
    Color(0xffFFFFFF),
    Color(0xffFFFFFF),
    Color(0xff000000),
    Color(0xff00CEFF),
    Color(0xff000000),
    Color(0xff384AE8),
  ];

  static List<LinearGradient> linearGradientRandom20 = [
    LinearGradient(
      colors: [
        Color(0xffFDC441),
        Color(0xffFF9300),
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ),
    LinearGradient(
      colors: [
        Color(0xff956DFF),
        Color(0xff651FFF),
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ),
    LinearGradient(
      colors: [
        Color(0xff3DFFFF),
        Color(0xff00AADD),
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ),
    LinearGradient(
      colors: [
        Color(0xff94F990),
        Color(0xff00CBD4),
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ),
    LinearGradient(
      colors: [
        Color(0xffA69BFF),
        Color(0xff6D4AFF),
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ),
    LinearGradient(
      colors: [
        Color(0xffD7A8FF),
        Color(0xff7846FF),
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ),
    LinearGradient(
      colors: [
        Color(0xff7453FF),
        Color(0xff4D4DFF),
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ),
    LinearGradient(
      colors: [
        Color(0xff405DFF),
        Color(0xff4726FF),
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ),
    LinearGradient(
      colors: [
        Color(0xff4D4DFF),
        Color(0xff4D4DFC),
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ),
    LinearGradient(
      colors: [
        Color(0xffF9B1FF),
        Color(0xffBA5EFD),
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ),
    LinearGradient(
      colors: [
        Color(0xff3DFFFF),
        Color(0xff00AADD),
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ),
    LinearGradient(
      colors: [
        Color(0xff7453FF),
        Color(0xff4D4DFF),
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ),
    LinearGradient(
      colors: [
        Color(0xff94F990),
        Color(0xff00CBD4),
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ),
    LinearGradient(
      colors: [
        Color(0xffFDC441),
        Color(0xffFF9300),
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ),
    LinearGradient(
      colors: [
        Color(0xff956DFF),
        Color(0xff651FFF),
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ),
    LinearGradient(
      colors: [
        Color(0xffD7A8FF),
        Color(0xff7846FF),
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ),
    LinearGradient(
      colors: [
        Color(0xffD0F74E),
        Color(0xffFFDC64),
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ),
    LinearGradient(
      colors: [
        Color(0xff956DFF),
        Color(0xff651FFF),
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ),
    LinearGradient(
      colors: [
        Color(0xff3DFFFF),
        Color(0xff00AADD),
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ),
    LinearGradient(
      colors: [
        Color(0xff94F990),
        Color(0xff00CBD4),
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    )
  ];
}
