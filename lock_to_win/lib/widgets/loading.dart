import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:lock_to_win/config/color_system.dart';
import 'package:lock_to_win/widgets/color.dart';
import 'package:lottie/lottie.dart';

class Loading {
  Loading._();

  static Widget showLoading({required BuildContext context}) {
    return CircularProgressIndicator(
      valueColor: AlwaysStoppedAnimation<Color>(Color(0xff08EEF3)),
    );
  }

  static Widget showSendLoading(
      {required BuildContext context,
      required String loadingTitle,
      required String loadingDetail}) {
    final mediaQueryData = MediaQuery.of(context);
    return MediaQuery(
      data: mediaQueryData.copyWith(textScaleFactor: 1.0),
      child: AlertDialog(
        contentPadding: EdgeInsets.zero,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(
            Radius.circular(5.0),
          ),
        ),
        backgroundColor: processingPopupBgColor,
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Lottie.asset('assets/animatedIcons/processing.json',
                height: 132, width: 132, package: 'minilikewallet'),
            Text(
              loadingTitle,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: whiteColor,
                fontSize: 14,
                height: 1.6,
              ),
            ),
            SizedBox(height: 12),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Text(
                loadingDetail,
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: textGray200,
                  fontSize: 14,
                  height: 1.6,
                  letterSpacing: 0.75,
                ),
              ),
            ),
            SizedBox(height: 38),
          ],
        ),
      ),
    );
  }

  static void typeLoadingAwait(String? text) {
    ColorTheme.configLoadingAwait();

    EasyLoading.show(
        status: text!,
        maskType: EasyLoadingMaskType.black,
        dismissOnTap: false);
  }

  static void typeLoadingAwaitTap(String? text) {
    ColorTheme.configLoadingAwait();

    EasyLoading.show(
        status: text!, maskType: EasyLoadingMaskType.black, dismissOnTap: true);
  }

  static void typeLoadingError(String? text) {
    ColorTheme.configLoadingError();
    EasyLoading.showError(text!);
  }

  static void typeLoadingSuccess(String? text) {
    ColorTheme.configLoadingSuccess();
    EasyLoading.showSuccess(text!);
  }

  static void typeLoadingDismiss() {
    EasyLoading.dismiss();
  }
}
