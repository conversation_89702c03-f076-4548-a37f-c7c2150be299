import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lock_to_win/main.dart';

class Ballon extends StatelessWidget {
  Ballon({Key? key, this.color, this.number}) : super(key: key);
  int? color;
  String? number;
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          margin: EdgeInsets.only(top: Get.height * 0.01, left: 20),
          decoration: BoxDecoration(
            color: isDarkMode
                ? Colors.black.withOpacity(0.1)
                : Colors.grey.withOpacity(0.01),
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(10),
                topRight: Radius.circular(10),
                bottomLeft: Radius.circular(10),
                bottomRight: Radius.circular(10)),
            boxShadow: [
              BoxShadow(
                color: isDarkMode
                    ? Colors.black.withOpacity(0.3)
                    : Colors.grey.withOpacity(0.3),
                spreadRadius: 2.5,
                blurRadius: 9,
                offset: Offset(0, 4), // changes position of shadow
              ),
            ],
          ),
          child: ClipOval(
            child: Container(
              height: 34,
              width: 24,
            ),
            // child:  Container(
            //   height: 44,
            //   width: 34,
            //   decoration: new BoxDecoration(
            //       gradient: LinearGradient(
            //         begin: Alignment.center,
            //         end: Alignment.center,
            //         colors: [
            //           Color(0xff000000).withOpacity(0.6),
            //           Color(0xff000000).withOpacity(0.5),
            //         ],
            //         stops: [0.1,5]
            //       )
            //   ),
            // ),
          ),
        ),
        Container(
          alignment: Alignment.center,
          margin: EdgeInsets.only(bottom: Get.height * 0.005, left: 7, right: 7),
          height: 50,
          width: 50,
          decoration: BoxDecoration(
            gradient: isDarkMode
                ? LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Color(0xff020202).withOpacity(0.6),
                      Color(0xff020202),
                    ],
                  )
                : LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Color(0xffFFFFFF).withOpacity(0.8),
                      Color(0xffFFFFFF),
                    ],
                  ),
            // color: isDarkMode ? Color(0xff020202) : Color(0xffE9ECF5),
            borderRadius: BorderRadius.circular(100),
          ),
          child: Text(
            number!,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Color(color!),
            ),
          ),
        )
      ],
    );
  }
}
