import 'package:flutter/material.dart';
import 'package:lock_to_win/config/light_mode_colors.dart';
import 'package:lock_to_win/models/g_color.dart';

import '../main.dart';
import 'dark_mode_colors.dart';

bool isThemeDark = false;

// Following colors change according to whether the theme is Dark or Light
Color layerBG3 = isThemeDark ? layerBG3Dark : layerBG3Light;
Color layerBG2 = isThemeDark ? layerBG2Dark : layerBG2Light;
Color layerBG1 = isThemeDark ? layerBG1Dark : layerBG1Light;
Color screenBG = isThemeDark ? screenBGDark : screenBGLight;

Color textMainColor = isThemeDark ? textMainColorDark : textMainColorLight;
Color textGray100 = isThemeDark ? textGray100Dark : textGray100Light;
Color textGray200 = isThemeDark ? textGray200Dark : textGray200Light;
Color textGray300 = isThemeDark ? textGray300Dark : textGray300Light;
Color textGray400 = isThemeDark ? textGray400Dark : textGray400Light;
Color textGray700 = isThemeDark ? textGray700Dark : textGray700Light;

Color block4 = isThemeDark ? block4Dark : block4Light;
Color block3 = isThemeDark ? block3Dark : block3Light;
Color block2 = isThemeDark ? block2Dark : block2Light;
Color block1 = isThemeDark ? block1Dark : block1Light;
Color appBarIconColor =
    isThemeDark ? hamburgerMenuColorDark : hamburgerMenuColorLight;

// Following are common colors in both Dark and Light mode
const Color blackBlock = Color(0xFF07090D);

const Color deepBlue = Color(0xFF384AE8);
Color blueColor = isThemeDark ? blueDark : blueLight;
const Color turquoise = Color(0xFF00F5FF);

const Color blueGradient1Start = Color(0xFF1080FE);
const Color blueGradient1End = Color(0xFF4065FF);
const Color blueGradient2Start = Color(0xFF009AFF);
const Color blueGradient2End = Color(0xFF1564EC);

// const Color turquoiseGradientStart = Color(0xFF0EFFD4);
const Color turquoiseGradientStart = Color(0xFF53DEFF);
// const Color turquoiseGradientEnd = Color(0xFF00CEFF);
const Color turquoiseGradientEnd = Color(0xFF00CEFF);
const Color blue2 = Color(0xFF00CEFF);

const Color purpleGradient2Start = Color(0xFF8E80FF);
const Color purpleGradient2End = Color(0xFF6D4AFF);
const Color purpleGradient3Start = Color(0xFF956DFF);
const Color purpleGradient3End = Color(0xFF651FFF);

const Color purple1 = Color(0xFF936BFF);
const Color purple2 = Color(0xFF7453FF);

const Color pink = Color(0xFFEA84FD);
const Color pinkPurple = Color(0xFFC184FD);

const Color gray350 = Color(0xFF7F8BA1);

// Following are some colors which I have added which were not mentioned in the XD
// file separately. These are here so that I can use and change a single color
// from here which is used at multiple places

const Color bottomAppBarIconColor = Color(0xFF8A99AB);

// const Color homeTransparentOverlayBGStart = Color(0x50323A47);
// const Color homeTransparentOverlayBGEnd = Color(0x00323A47);

const Color fabGradientStart = Color(0xFF4065FF);
const Color fabGradientEnd = Color(0xFF384AE8);
const Color whiteColor = Color(0xFFFFFFFF);
const Color blackColor = Color(0xFF000000);

const Color cardTextWhite = Color(0xFFECF2FF);
const Color cardRearText = Color(0xFFAEB8CF);

const Color likepointCardStart = Color(0xFF348AFF);
const Color likepointCardEnd = deepBlue;
const Color likepointBarGradientStart = Color(0xFF00E8FB);
const Color likepointBarGradientEnd = Color(0xFF1080FE);

const Color dividerColor = Color(0xFF444C5B);
const Color textFieldBorderColor = Color(0xFF8F97A6);

const Color loadingWidgetGradientStart = Color(0xFF7B5CFF);
const Color loadingWidgetGradientEnd = purpleGradient2End;

const Color textGreenColor = Color(0xFF26BF73);
Color textBlueColor = blueColor;
const Color textRedColor = Color(0xFFF43130);

const Color buttonText = Color(0xFFEBEDFD);

const Color removeIconColor = Color(0xFFC2C6CD);

const Color processingPopupBgColor = Color(0xFF2B3C4B);

// G series colors
Color g1start = isThemeDark ? G1startDark : G1Light;
Color g1end = isThemeDark ? G1endDark : G1Light;

Color g2start = isThemeDark ? G2startDark : G2Light;
Color g2end = isThemeDark ? G2endDark : G2Light;

Color g3start = isThemeDark ? G3startDark : G3Light;
Color g3end = isThemeDark ? G3endDark : G3Light;

Color g4start = isThemeDark ? G4startDark : G4Light;
Color g4end = isThemeDark ? G4endDark : G4Light;

Color g5start = isThemeDark ? G5startDark : G5Light;
Color g5end = isThemeDark ? G5endDark : G5Light;

Color g6start = isThemeDark ? G6startDark : G6Light;
Color g6end = isThemeDark ? G6endDark : G6Light;

Color g7start = isThemeDark ? G7startDark : G7Light;
Color g7end = isThemeDark ? G7endDark : G7Light;

Color g8start = isThemeDark ? G8startDark : G8Light;
Color g8end = isThemeDark ? G8endDark : G8Light;

Color g9start = isThemeDark ? G9startDark : G9Light;
Color g9end = isThemeDark ? G9endDark : G9Light;

Color g10start = isThemeDark ? G10startDark : G10Light;
Color g10end = isThemeDark ? G10endDark : G10Light;

Color g11start = isThemeDark ? G11startDark : G11Light;
Color g11end = isThemeDark ? G11endDark : G11Light;

GColor g1 = GColor(start: g1start, end: g1end);
GColor g2 = GColor(start: g2start, end: g2end);
GColor g3 = GColor(start: g3start, end: g3end);
GColor g4 = GColor(start: g4start, end: g4end);
GColor g5 = GColor(start: g5start, end: g5end);
GColor g6 = GColor(start: g6start, end: g6end);
GColor g7 = GColor(start: g7start, end: g7end);
GColor g8 = GColor(start: g8start, end: g8end);
GColor g9 = GColor(start: g9start, end: g9end);
GColor g10 = GColor(start: g10start, end: g10end);
GColor g11 = GColor(start: g11start, end: g11end);

List<GColor> gColors = [
  g1,
  g2,
  g3,
  g4,
  g5,
  g6,
  g7,
  g8,
  g9,
  g10,
  g11,
];
