import 'package:flutter/material.dart';
import 'package:lock_to_win/config/color_system.dart';

ThemeData darkTheme = ThemeData(
  canvasColor: layerBG2,
  brightness: Brightness.dark,
  colorScheme: ColorScheme.dark(),
  fontFamily: 'SFPro',
  textTheme: TextTheme(
    bodyText2: TextStyle(
      letterSpacing: 0.75,
      color: Color(0xFFEBEDF1),
    ),
  ),
);

ThemeData lightTheme = ThemeData(
  canvasColor: layerBG2,
  brightness: Brightness.light,
  colorScheme: ColorScheme.light(),
  fontFamily: 'SFPro',
  textTheme: TextTheme(
    bodyText2: TextStyle(
      letterSpacing: 0.75,
      color: Color(0xFF1C1C1C),
    ),
  ),
);
