# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  _fe_analyzer_shared:
    dependency: transitive
    description:
      name: _fe_analyzer_shared
      sha256: ae92f5d747aee634b87f89d9946000c2de774be1d6ac3e58268224348cd0101a
      url: "https://pub.dev"
    source: hosted
    version: "61.0.0"
  _flutterfire_internals:
    dependency: transitive
    description:
      name: _flutterfire_internals
      sha256: "2f428053492f92303e42c9afa8e3a78ad1886760e7b594e2b5a6b6ee47376360"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  adaptive_number:
    dependency: transitive
    description:
      name: adaptive_number
      sha256: "3a567544e9b5c9c803006f51140ad544aedc79604fd4f3f2c1380003f97c1d77"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  ags_authrest2:
    dependency: "direct main"
    description:
      name: ags_authrest2
      sha256: d61a26f437b49811f071b76e7b6363e047f44d98611d647d8e4b576eee870662
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3"
  analyzer:
    dependency: transitive
    description:
      name: analyzer
      sha256: ea3d8652bda62982addfd92fdc2d0214e5f82e43325104990d4f4c4a2a313562
      url: "https://pub.dev"
    source: hosted
    version: "5.13.0"
  animated:
    dependency: transitive
    description:
      name: animated
      sha256: de84f1daecec3d81c5c72fb2958f03389aee3aeaa0323adf365bacf1ecfaaf0f
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  animated_text_kit:
    dependency: transitive
    description:
      name: animated_text_kit
      sha256: "37392a5376c9a1a503b02463c38bc0342ef814ddbb8f9977bc90f2a84b22fa92"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.2"
  animations:
    dependency: transitive
    description:
      name: animations
      sha256: ef57563eed3620bd5d75ad96189846aca1e033c0c45fc9a7d26e80ab02b88a70
      url: "https://pub.dev"
    source: hosted
    version: "2.0.8"
  archive:
    dependency: transitive
    description:
      name: archive
      sha256: "22600aa1e926be775fa5fe7e6894e7fb3df9efda8891c73f70fb3262399a432d"
      url: "https://pub.dev"
    source: hosted
    version: "3.4.10"
  args:
    dependency: transitive
    description:
      name: args
      sha256: eef6c46b622e0494a36c5a12d10d77fb4e855501a91c1b9ef9339326e58f0596
      url: "https://pub.dev"
    source: hosted
    version: "2.4.2"
  asn1lib:
    dependency: transitive
    description:
      name: asn1lib
      sha256: b74e3842a52c61f8819a1ec8444b4de5419b41a7465e69d4aa681445377398b0
      url: "https://pub.dev"
    source: hosted
    version: "1.4.1"
  async:
    dependency: transitive
    description:
      name: async
      sha256: bfe67ef28df125b7dddcea62755991f807aa39a2492a23e1550161692950bbe0
      url: "https://pub.dev"
    source: hosted
    version: "2.10.0"
  base32:
    dependency: transitive
    description:
      name: base32
      sha256: ddad4ebfedf93d4500818ed8e61443b734ffe7cf8a45c668c9b34ef6adde02e2
      url: "https://pub.dev"
    source: hosted
    version: "2.1.3"
  bip32:
    dependency: "direct main"
    description:
      name: bip32
      sha256: "54787cd7a111e9d37394aabbf53d1fc5e2e0e0af2cd01c459147a97c0e3f8a97"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  bip39:
    dependency: "direct main"
    description:
      name: bip39
      sha256: de1ee27ebe7d96b84bb3a04a4132a0a3007dcdd5ad27dd14aa87a29d97c45edc
      url: "https://pub.dev"
    source: hosted
    version: "1.0.6"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      sha256: "6cfb5af12253eaf2b368f07bacc5a80d1301a071c73360d746b7f2e32d762c66"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  bs58check:
    dependency: transitive
    description:
      name: bs58check
      sha256: c4a164d42b25c2f6bc88a8beccb9fc7d01440f3c60ba23663a20a70faf484ea9
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  build_config:
    dependency: transitive
    description:
      name: build_config
      sha256: bf80fcfb46a29945b423bd9aad884590fb1dc69b330a4d4700cac476af1708d1
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  cached_network_image:
    dependency: "direct main"
    description:
      name: cached_network_image
      sha256: fd3d0dc1d451f9a252b32d95d3f0c3c487bc41a75eba2e6097cb0b9c71491b15
      url: "https://pub.dev"
    source: hosted
    version: "3.2.3"
  cached_network_image_platform_interface:
    dependency: transitive
    description:
      name: cached_network_image_platform_interface
      sha256: bb2b8403b4ccdc60ef5f25c70dead1f3d32d24b9d6117cfc087f496b178594a7
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  cached_network_image_web:
    dependency: transitive
    description:
      name: cached_network_image_web
      sha256: b8eb814ebfcb4dea049680f8c1ffb2df399e4d03bf7a352c775e26fa06e02fa0
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  camera:
    dependency: "direct main"
    description:
      name: camera
      sha256: "1f9010f0689774380fbcd7d6b7820a5157e8e97685fa66d619e1d1f58b3fdf93"
      url: "https://pub.dev"
    source: hosted
    version: "0.10.5+5"
  camera_android:
    dependency: transitive
    description:
      name: camera_android
      sha256: "58463140f1b39591b8e2155861b436abad4ceb48160058be8374164ff0309ef3"
      url: "https://pub.dev"
    source: hosted
    version: "0.10.8+13"
  camera_avfoundation:
    dependency: transitive
    description:
      name: camera_avfoundation
      sha256: "3b6d9f550cfd658c71f34a99509528501e5e5d4fa79f11e3a4d6ef380d8e0254"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.13+7"
  camera_platform_interface:
    dependency: transitive
    description:
      name: camera_platform_interface
      sha256: "86fd4fc597c6e455265ddb5884feb352d0171ad14b9cdf3aba30da59b25738c4"
      url: "https://pub.dev"
    source: hosted
    version: "2.6.0"
  camera_web:
    dependency: transitive
    description:
      name: camera_web
      sha256: "012c82efeb6b69626f1cf7a37c5277c1032428bcae98c7ab6401ff5e91eca628"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.2+2"
  carousel_slider:
    dependency: "direct main"
    description:
      name: carousel_slider
      sha256: "9c695cc963bf1d04a47bd6021f68befce8970bcd61d24938e1fb0918cf5d9c42"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.1"
  characters:
    dependency: transitive
    description:
      name: characters
      sha256: e6a326c8af69605aec75ed6c187d06b349707a27fbff8222ca9cc2cff167975c
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  checked_yaml:
    dependency: transitive
    description:
      name: checked_yaml
      sha256: feb6bed21949061731a7a75fc5d2aa727cf160b91af9a3e464c5e3a32e28b5ff
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  clippy_flutter:
    dependency: "direct main"
    description:
      name: clippy_flutter
      sha256: f63f487afe1233f7189f83154aa9f4903dddda61687b5b81ce4955250c133a07
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0-nullsafety.1"
  clock:
    dependency: transitive
    description:
      name: clock
      sha256: cb6d7f03e1de671e34607e909a7213e31d7752be4fb66a86d29fe1eb14bfb5cf
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  cloud_firestore:
    dependency: "direct main"
    description:
      name: cloud_firestore
      sha256: "7313ccae834ee8dd0b93ea367ba228b86212f5ca39d48c1e142dbbd377af5ed4"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.4"
  cloud_firestore_platform_interface:
    dependency: transitive
    description:
      name: cloud_firestore_platform_interface
      sha256: d023142c18c28b2610c23c196e829c96976569cc2aa2f8e45328ae8a64c428d1
      url: "https://pub.dev"
    source: hosted
    version: "5.7.7"
  cloud_firestore_web:
    dependency: transitive
    description:
      name: cloud_firestore_web
      sha256: "3d7d4fa8c1dc5a1f7cb33985ae0ab9924d33d76d4959fe26aed84b7d282887e3"
      url: "https://pub.dev"
    source: hosted
    version: "2.8.10"
  collection:
    dependency: transitive
    description:
      name: collection
      sha256: cfc915e6923fe5ce6e153b0723c753045de46de1b4d63771530504004a45fae0
      url: "https://pub.dev"
    source: hosted
    version: "1.17.0"
  connectivity_plus:
    dependency: "direct main"
    description:
      name: connectivity_plus
      sha256: "77a180d6938f78ca7d2382d2240eb626c0f6a735d0bfdce227d8ffb80f95c48b"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.2"
  connectivity_plus_platform_interface:
    dependency: transitive
    description:
      name: connectivity_plus_platform_interface
      sha256: cf1d1c28f4416f8c654d7dc3cd638ec586076255d407cef3ddbdaf178272a71a
      url: "https://pub.dev"
    source: hosted
    version: "1.2.4"
  convert:
    dependency: transitive
    description:
      name: convert
      sha256: "0f08b14755d163f6e2134cb58222dd25ea2a2ee8a195e53983d57c075324d592"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.1"
  cross_file:
    dependency: transitive
    description:
      name: cross_file
      sha256: "445db18de832dba8d851e287aff8ccf169bed30d2e94243cb54c7d2f1ed2142c"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.3+6"
  crypto:
    dependency: "direct main"
    description:
      name: crypto
      sha256: ff625774173754681d66daaf4a448684fb04b78f902da9cb3d308c19cc5e8bab
      url: "https://pub.dev"
    source: hosted
    version: "3.0.3"
  csslib:
    dependency: transitive
    description:
      name: csslib
      sha256: "706b5707578e0c1b4b7550f64078f0a0f19dec3f50a178ffae7006b0a9ca58fb"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  currency_text_input_formatter:
    dependency: "direct main"
    description:
      name: currency_text_input_formatter
      sha256: c7ceefbfda7d279c489fcc00b4515795ae1e5657968bfd32b4129a6488558e95
      url: "https://pub.dev"
    source: hosted
    version: "2.1.8"
  dart_jsonwebtoken:
    dependency: "direct main"
    description:
      name: dart_jsonwebtoken
      sha256: f565a76800b01d3216296a049bc8f189695ab2433afa36cad0e086e46b48d70d
      url: "https://pub.dev"
    source: hosted
    version: "2.8.0"
  dash_chat:
    dependency: "direct main"
    description:
      path: dash_chat
      relative: true
    source: path
    version: "1.1.5"
  date_format:
    dependency: "direct main"
    description:
      name: date_format
      sha256: "8e5154ca363411847220c8cbc43afcf69c08e8debe40ba09d57710c25711760c"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.7"
  dbus:
    dependency: transitive
    description:
      name: dbus
      sha256: "4f814fc7e73057f78f307a6c4714fe2ffb4bdb994ab1970540a068ec4d5a45be"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.3"
  delayed_display:
    dependency: "direct main"
    description:
      name: delayed_display
      sha256: "8d722bb730071b872cef2bc0b07ef20549ebd269087a16ed1c5696896246c296"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  dependency_validator:
    dependency: "direct main"
    description:
      name: dependency_validator
      sha256: "16906f2137a312d0ffe754aee156436563a63d6ab7faaded83d401b5385d5d35"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.0"
  dio:
    dependency: "direct main"
    description:
      name: dio
      sha256: "7d328c4d898a61efc3cd93655a0955858e29a0aa647f0f9e02d59b3bb275e2e8"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.6"
  ed25519_edwards:
    dependency: transitive
    description:
      name: ed25519_edwards
      sha256: "6ce0112d131327ec6d42beede1e5dfd526069b18ad45dcf654f15074ad9276cd"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.1"
  ed25519_hd_key:
    dependency: "direct main"
    description:
      name: ed25519_hd_key
      sha256: c5c9f11a03f5789bf9dcd9ae88d641571c802640851f1cacdb13123f171b3a26
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  eip1559:
    dependency: transitive
    description:
      name: eip1559
      sha256: de454d6321bd753eb79116e9ec4f8df20895c2e97f9a3839a032f3a728985516
      url: "https://pub.dev"
    source: hosted
    version: "0.6.1"
  eip55:
    dependency: transitive
    description:
      name: eip55
      sha256: "213a9b86add87a5216328e8494b0ab836e401210c4d55eb5e521bd39e39169e1"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  encrypt:
    dependency: "direct main"
    description:
      name: encrypt
      sha256: "4fd4e4fdc21b9d7d4141823e1e6515cd94e7b8d84749504c232999fba25d9bbb"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.1"
  event_bus:
    dependency: transitive
    description:
      name: event_bus
      sha256: "44baa799834f4c803921873e7446a2add0f3efa45e101a054b1f0ab9b95f8edc"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      sha256: "511392330127add0b769b75a987850d136345d9227c6b94c96a04cf4a391bf78"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.1"
  ffi:
    dependency: transitive
    description:
      name: ffi
      sha256: "13a6ccf6a459a125b3fcdb6ec73bd5ff90822e071207c663bfd1f70062d51d18"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  file:
    dependency: transitive
    description:
      name: file
      sha256: "1b92bec4fc2a72f59a8e15af5f52cd441e4a7860b49499d69dfa817af20e925d"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.4"
  file_selector_linux:
    dependency: transitive
    description:
      name: file_selector_linux
      sha256: "045d372bf19b02aeb69cacf8b4009555fb5f6f0b7ad8016e5f46dd1387ddd492"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.2+1"
  file_selector_macos:
    dependency: transitive
    description:
      name: file_selector_macos
      sha256: b15c3da8bd4908b9918111fa486903f5808e388b8d1c559949f584725a6594d6
      url: "https://pub.dev"
    source: hosted
    version: "0.9.3+3"
  file_selector_platform_interface:
    dependency: transitive
    description:
      name: file_selector_platform_interface
      sha256: "0aa47a725c346825a2bd396343ce63ac00bda6eff2fbc43eabe99737dede8262"
      url: "https://pub.dev"
    source: hosted
    version: "2.6.1"
  file_selector_windows:
    dependency: transitive
    description:
      name: file_selector_windows
      sha256: d3547240c20cabf205c7c7f01a50ecdbc413755814d6677f3cb366f04abcead0
      url: "https://pub.dev"
    source: hosted
    version: "0.9.3+1"
  firebase_analytics:
    dependency: "direct main"
    description:
      name: firebase_analytics
      sha256: "5e277bfb0ffa57a7f0935f6e6f529fcdaf27d65f195d50e5b2bc4a38a2fc00ae"
      url: "https://pub.dev"
    source: hosted
    version: "9.3.8"
  firebase_analytics_platform_interface:
    dependency: transitive
    description:
      name: firebase_analytics_platform_interface
      sha256: "52c9bd117e2468059381aa20269f3df7362b26ee52fb24c8671cda30ea85ac53"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.7"
  firebase_analytics_web:
    dependency: transitive
    description:
      name: firebase_analytics_web
      sha256: ff1aee3a05f34f5c8f8c21a0470441c66958a509f96ccca17fdedaced141768e
      url: "https://pub.dev"
    source: hosted
    version: "0.4.2+7"
  firebase_auth:
    dependency: "direct main"
    description:
      name: firebase_auth
      sha256: "22407a8c78b0f879d4c6b0d244f56952f26352a574a8c42262293275835533fd"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  firebase_auth_platform_interface:
    dependency: transitive
    description:
      name: firebase_auth_platform_interface
      sha256: "707612dbe6e6f7d96dd2dc75d61f0f31b5536e8c5cbdc45639f3f5ca79d1b781"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  firebase_auth_web:
    dependency: transitive
    description:
      name: firebase_auth_web
      sha256: f3ce926b2f862f0a3de5607e45520ab8a3a967bba6a2f8b7de73192aee92c618
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  firebase_core:
    dependency: "direct main"
    description:
      name: firebase_core
      sha256: "4f1d7c13a909e82ff026679c9b8493cdeb35a9c76bc46c42bf9e2240c9e57e80"
      url: "https://pub.dev"
    source: hosted
    version: "1.24.0"
  firebase_core_platform_interface:
    dependency: "direct main"
    description:
      name: firebase_core_platform_interface
      sha256: b51257a8b4388565cd66062d727d3e60067b5f5cc3390eb0ecd20b8f97741bdb
      url: "https://pub.dev"
    source: hosted
    version: "4.5.1"
  firebase_core_web:
    dependency: transitive
    description:
      name: firebase_core_web
      sha256: "839f1b48032a61962792cea1225fae030d4f27163867f181d6d2072dd40acbee"
      url: "https://pub.dev"
    source: hosted
    version: "1.7.3"
  firebase_messaging:
    dependency: "direct main"
    description:
      name: firebase_messaging
      sha256: "6126f8f6f3bba6e3ffff05300711615c388eb31e0a7431cebe5514f6572cbdff"
      url: "https://pub.dev"
    source: hosted
    version: "13.1.0"
  firebase_messaging_platform_interface:
    dependency: transitive
    description:
      name: firebase_messaging_platform_interface
      sha256: "71f120e8b405a172cb0088a2cd62fd8b77be0f0c942a5bda411b80268c1702f8"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.0"
  firebase_messaging_web:
    dependency: transitive
    description:
      name: firebase_messaging_web
      sha256: b7c5e019c4ecf3a512c4a0f00a4a9c1f260eb11872380a91f35983387488ce94
      url: "https://pub.dev"
    source: hosted
    version: "3.2.0"
  firebase_storage:
    dependency: "direct main"
    description:
      name: firebase_storage
      sha256: "1fe46462c9632db0f62db5b9f1abcb8d4232a6d3e7098d745f5790b71dedc822"
      url: "https://pub.dev"
    source: hosted
    version: "10.3.11"
  firebase_storage_platform_interface:
    dependency: transitive
    description:
      name: firebase_storage_platform_interface
      sha256: a4a1bed8764baf9b6d377d9870ebb2c65e13a38bc813cfaaf0ac2a37d422bcdd
      url: "https://pub.dev"
    source: hosted
    version: "4.1.19"
  firebase_storage_web:
    dependency: transitive
    description:
      name: firebase_storage_web
      sha256: "791516c9170ab6643282807913cf89ebb950d66206eb92ed154a847438047587"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.9"
  fixnum:
    dependency: transitive
    description:
      name: fixnum
      sha256: "25517a4deb0c03aa0f32fd12db525856438902d9c16536311e76cdc57b31d7d1"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  flare_flutter:
    dependency: "direct main"
    description:
      name: flare_flutter
      sha256: "99d63c60f00fac81249ce6410ee015d7b125c63d8278a30da81edf3317a1f6a0"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.2"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_blurhash:
    dependency: transitive
    description:
      name: flutter_blurhash
      sha256: "05001537bd3fac7644fa6558b09ec8c0a3f2eba78c0765f88912882b1331a5c6"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.0"
  flutter_cache_manager:
    dependency: transitive
    description:
      name: flutter_cache_manager
      sha256: "8207f27539deb83732fdda03e259349046a39a4c767269285f449ade355d54ba"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.1"
  flutter_contacts:
    dependency: "direct main"
    description:
      name: flutter_contacts
      sha256: "388d32cd33f16640ee169570128c933b45f3259bddbfae7a100bb49e5ffea9ae"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.9+2"
  flutter_countdown_timer:
    dependency: "direct main"
    description:
      name: flutter_countdown_timer
      sha256: dfcbd7d6f76a5589f78f3f3ba2f9ea2e199368eccc1adce4153ce985b9587bc5
      url: "https://pub.dev"
    source: hosted
    version: "4.1.0"
  flutter_custom_clippers:
    dependency: transitive
    description:
      name: flutter_custom_clippers
      sha256: "473e3daf61c2a6cee0ad137393259a25223239d519a131c7ec1cac04d06e5407"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  flutter_easyloading:
    dependency: "direct main"
    description:
      name: flutter_easyloading
      sha256: ba21a3c883544e582f9cc455a4a0907556714e1e9cf0eababfcb600da191d17c
      url: "https://pub.dev"
    source: hosted
    version: "3.0.5"
  flutter_idensic_mobile_sdk_plugin:
    dependency: "direct main"
    description:
      name: flutter_idensic_mobile_sdk_plugin
      sha256: "0461691b61eb809319c4b5c466fa89d531bce8b21a77e7028367e61910f9505a"
      url: "https://pub.dev"
    source: hosted
    version: "1.32.1"
  flutter_jailbreak_detection:
    dependency: "direct main"
    description:
      name: flutter_jailbreak_detection
      sha256: "67ff11ea41965152d24db7104da1f9b343f94ada64c2a9e309ec4d753a12d281"
      url: "https://pub.dev"
    source: hosted
    version: "1.10.0"
  flutter_keyboard_visibility:
    dependency: "direct main"
    description:
      name: flutter_keyboard_visibility
      sha256: "4983655c26ab5b959252ee204c2fffa4afeb4413cd030455194ec0caa3b8e7cb"
      url: "https://pub.dev"
    source: hosted
    version: "5.4.1"
  flutter_keyboard_visibility_linux:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_linux
      sha256: "6fba7cd9bb033b6ddd8c2beb4c99ad02d728f1e6e6d9b9446667398b2ac39f08"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  flutter_keyboard_visibility_macos:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_macos
      sha256: c5c49b16fff453dfdafdc16f26bdd8fb8d55812a1d50b0ce25fc8d9f2e53d086
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  flutter_keyboard_visibility_platform_interface:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_platform_interface
      sha256: e43a89845873f7be10cb3884345ceb9aebf00a659f479d1c8f4293fcb37022a4
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  flutter_keyboard_visibility_web:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_web
      sha256: d3771a2e752880c79203f8d80658401d0c998e4183edca05a149f5098ce6e3d1
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  flutter_keyboard_visibility_windows:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_windows
      sha256: fc4b0f0b6be9b93ae527f3d527fb56ee2d918cd88bbca438c478af7bcfd0ef73
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  flutter_linkify:
    dependency: "direct main"
    description:
      name: flutter_linkify
      sha256: "74669e06a8f358fee4512b4320c0b80e51cffc496607931de68d28f099254073"
      url: "https://pub.dev"
    source: hosted
    version: "6.0.0"
  flutter_local_notifications:
    dependency: "direct main"
    description:
      name: flutter_local_notifications
      sha256: f222919a34545931e47b06000836b5101baeffb0e6eb5a4691d2d42851740dd9
      url: "https://pub.dev"
    source: hosted
    version: "12.0.4"
  flutter_local_notifications_linux:
    dependency: transitive
    description:
      name: flutter_local_notifications_linux
      sha256: "6af440e3962eeab8459602c309d7d4ab9e62f05d5cfe58195a28f846a0b5d523"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  flutter_local_notifications_platform_interface:
    dependency: transitive
    description:
      name: flutter_local_notifications_platform_interface
      sha256: "5ec1feac5f7f7d9266759488bc5f76416152baba9aa1b26fe572246caa00d1ab"
      url: "https://pub.dev"
    source: hosted
    version: "6.0.0"
  flutter_localization:
    dependency: "direct main"
    description:
      name: flutter_localization
      sha256: a65ae241b865f2780f2ef624a4cdd019ca894d0b7df0a0b77190746ef0b9c8fa
      url: "https://pub.dev"
    source: hosted
    version: "0.1.14"
  flutter_localizations:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_markdown:
    dependency: "direct main"
    description:
      name: flutter_markdown
      sha256: dc6d5258653f6857135b32896ccda7f7af0c54dcec832495ad6835154c6c77c0
      url: "https://pub.dev"
    source: hosted
    version: "0.6.15"
  flutter_native_timezone:
    dependency: "direct main"
    description:
      name: flutter_native_timezone
      sha256: ed7bfb982f036243de1c068e269182a877100c994f05143c8b26a325e28c1b02
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  flutter_parsed_text:
    dependency: transitive
    description:
      name: flutter_parsed_text
      sha256: "0afae4a017341ddcb5276740550df6d21ad81ad6ec0faff4f2f01967e1a43064"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.5"
  flutter_plugin_android_lifecycle:
    dependency: transitive
    description:
      name: flutter_plugin_android_lifecycle
      sha256: b068ffc46f82a55844acfa4fdbb61fad72fa2aef0905548419d97f0f95c456da
      url: "https://pub.dev"
    source: hosted
    version: "2.0.17"
  flutter_riverpod:
    dependency: "direct main"
    description:
      name: flutter_riverpod
      sha256: "2885685ad4ff8c62e84b6295cc13c24c733164006edd2b4ac1204beec0e35e54"
      url: "https://pub.dev"
    source: hosted
    version: "0.14.0+3"
  flutter_rounded_date_picker:
    dependency: "direct main"
    description:
      name: flutter_rounded_date_picker
      sha256: e6aa2dc5d3b44e8bbe85ef901be69eac59ba4136427f11f4c8b2a303e1e774e7
      url: "https://pub.dev"
    source: hosted
    version: "3.0.4"
  flutter_screenutil:
    dependency: "direct main"
    description:
      name: flutter_screenutil
      sha256: "0a122936b450324cbdfd51be0819cc6fcebb093eb65585e9cd92263f7a1a8a39"
      url: "https://pub.dev"
    source: hosted
    version: "5.7.0"
  flutter_secure_storage:
    dependency: "direct main"
    description:
      name: flutter_secure_storage
      sha256: "9f3dd2ac3b6875b0fde5b04734789c3ef35ba3965c18e99dd564a7a2f8056df6"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.1"
  flutter_spinkit:
    dependency: "direct main"
    description:
      name: flutter_spinkit
      sha256: d2696eed13732831414595b98863260e33e8882fc069ee80ec35d4ac9ddb0472
      url: "https://pub.dev"
    source: hosted
    version: "5.2.1"
  flutter_svg:
    dependency: "direct main"
    description:
      name: flutter_svg
      sha256: "9ac1967e2f72a08af11b05b39167920f90d043cf67163d13a544a358c8f31afa"
      url: "https://pub.dev"
    source: hosted
    version: "0.22.0"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  fluttertoast:
    dependency: "direct main"
    description:
      name: fluttertoast
      sha256: dd87f28cd7525d1885ad59a9122573c2cf0dc1fc97f6e3609380da871eb11d9f
      url: "https://pub.dev"
    source: hosted
    version: "8.2.0"
  freezed_annotation:
    dependency: transitive
    description:
      name: freezed_annotation
      sha256: "70776c4541e5cacfe45bcaf00fe79137b8c61aa34fb5765a05ce6c57fd72c6e9"
      url: "https://pub.dev"
    source: hosted
    version: "0.14.3"
  geolocator:
    dependency: "direct main"
    description:
      name: geolocator
      sha256: "6cb9fb6e5928b58b9a84bdf85012d757fd07aab8215c5205337021c4999bad27"
      url: "https://pub.dev"
    source: hosted
    version: "11.1.0"
  geolocator_android:
    dependency: transitive
    description:
      name: geolocator_android
      sha256: "93906636752ea4d4e778afa981fdfe7409f545b3147046300df194330044d349"
      url: "https://pub.dev"
    source: hosted
    version: "4.3.1"
  geolocator_apple:
    dependency: transitive
    description:
      name: geolocator_apple
      sha256: bc2aca02423ad429cb0556121f56e60360a2b7d694c8570301d06ea0c00732fd
      url: "https://pub.dev"
    source: hosted
    version: "2.3.7"
  geolocator_platform_interface:
    dependency: transitive
    description:
      name: geolocator_platform_interface
      sha256: b8cc1d3be0ca039a3f2174b0b026feab8af3610e220b8532e42cff8ec6658535
      url: "https://pub.dev"
    source: hosted
    version: "4.1.0"
  geolocator_web:
    dependency: transitive
    description:
      name: geolocator_web
      sha256: "49d8f846ebeb5e2b6641fe477a7e97e5dd73f03cbfef3fd5c42177b7300fb0ed"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  geolocator_windows:
    dependency: transitive
    description:
      name: geolocator_windows
      sha256: "53da08937d07c24b0d9952eb57a3b474e29aae2abf9dd717f7e1230995f13f0e"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.3"
  get:
    dependency: transitive
    description:
      name: get
      sha256: "2ba20a47c8f1f233bed775ba2dd0d3ac97b4cf32fc17731b3dfc672b06b0e92a"
      url: "https://pub.dev"
    source: hosted
    version: "4.6.5"
  get_it:
    dependency: "direct main"
    description:
      name: get_it
      sha256: "529de303c739fca98cd7ece5fca500d8ff89649f1bb4b4e94fb20954abcd7468"
      url: "https://pub.dev"
    source: hosted
    version: "7.6.0"
  glob:
    dependency: transitive
    description:
      name: glob
      sha256: "0e7014b3b7d4dac1ca4d6114f82bf1782ee86745b9b42a92c9289c23d8a0ab63"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  global_configuration:
    dependency: "direct main"
    description:
      name: global_configuration
      sha256: "63fdee759d2bdde498804061bd44860b2025b27b5661c60a5fb39663df88261f"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0-nullsafety.1"
  google_fonts:
    dependency: transitive
    description:
      name: google_fonts
      sha256: "8729bcb3d4226758859855dd7b9124a49d6acb4d197f58a18a34597ca10056e8"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  hex:
    dependency: "direct main"
    description:
      name: hex
      sha256: "4e7cd54e4b59ba026432a6be2dd9d96e4c5205725194997193bf871703b82c4a"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0"
  html:
    dependency: transitive
    description:
      name: html
      sha256: "3a7812d5bcd2894edf53dfaf8cd640876cf6cef50a8f238745c8b8120ea74d3a"
      url: "https://pub.dev"
    source: hosted
    version: "0.15.4"
  http:
    dependency: "direct main"
    description:
      name: http
      sha256: "5895291c13fa8a3bd82e76d5627f69e0d85ca6a30dcac95c4ea19a5d555879c2"
      url: "https://pub.dev"
    source: hosted
    version: "0.13.6"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      sha256: "2aa08ce0341cc9b354a498388e30986515406668dbcc4f7c950c3e715496693b"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.2"
  image:
    dependency: "direct main"
    description:
      name: image
      sha256: "02bafd3b4f399bfeb10034deba9753d93b55ce41cd0c4d3d8b355626f80e5b32"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.3"
  image_crop:
    dependency: "direct main"
    description:
      name: image_crop
      sha256: a580010f9e90b20581ecf5e8f4e9aa838cb935ea1497f9d1b196baa382b84a6d
      url: "https://pub.dev"
    source: hosted
    version: "0.4.1"
  image_gallery_saver:
    dependency: "direct main"
    description:
      name: image_gallery_saver
      sha256: be812580c7a320d3bf583af89cac6b376f170d48000aca75215a73285a3223a0
      url: "https://pub.dev"
    source: hosted
    version: "1.7.1"
  image_picker:
    dependency: "direct main"
    description:
      name: image_picker
      sha256: b6951e25b795d053a6ba03af5f710069c99349de9341af95155d52665cb4607c
      url: "https://pub.dev"
    source: hosted
    version: "0.8.9"
  image_picker_android:
    dependency: transitive
    description:
      name: image_picker_android
      sha256: d6a6e78821086b0b737009b09363018309bbc6de3fd88cc5c26bc2bb44a4957f
      url: "https://pub.dev"
    source: hosted
    version: "0.8.8+2"
  image_picker_for_web:
    dependency: transitive
    description:
      name: image_picker_for_web
      sha256: "869fe8a64771b7afbc99fc433a5f7be2fea4d1cb3d7c11a48b6b579eb9c797f0"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  image_picker_ios:
    dependency: transitive
    description:
      name: image_picker_ios
      sha256: "76ec722aeea419d03aa915c2c96bf5b47214b053899088c9abb4086ceecf97a7"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.8+4"
  image_picker_linux:
    dependency: transitive
    description:
      name: image_picker_linux
      sha256: "4ed1d9bb36f7cd60aa6e6cd479779cc56a4cb4e4de8f49d487b1aaad831300fa"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+1"
  image_picker_macos:
    dependency: transitive
    description:
      name: image_picker_macos
      sha256: "3f5ad1e8112a9a6111c46d0b57a7be2286a9a07fc6e1976fdf5be2bd31d4ff62"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+1"
  image_picker_platform_interface:
    dependency: transitive
    description:
      name: image_picker_platform_interface
      sha256: ed9b00e63977c93b0d2d2b343685bed9c324534ba5abafbb3dfbd6a780b1b514
      url: "https://pub.dev"
    source: hosted
    version: "2.9.1"
  image_picker_windows:
    dependency: transitive
    description:
      name: image_picker_windows
      sha256: "6ad07afc4eb1bc25f3a01084d28520496c4a3bb0cb13685435838167c9dcedeb"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+1"
  intl:
    dependency: "direct main"
    description:
      name: intl
      sha256: "910f85bce16fb5c6f614e117efa303e85a1731bb0081edf3604a2ae6e9a3cc91"
      url: "https://pub.dev"
    source: hosted
    version: "0.17.0"
  io:
    dependency: transitive
    description:
      name: io
      sha256: "2ec25704aba361659e10e3e5f5d672068d332fc8ac516421d483a11e5cbd061e"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  js:
    dependency: transitive
    description:
      name: js
      sha256: "5528c2f391ededb7775ec1daa69e65a2d61276f7552de2b5f7b8d34ee9fd4ab7"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.5"
  json_annotation:
    dependency: transitive
    description:
      name: json_annotation
      sha256: b10a7b2ff83d83c777edba3c6a0f97045ddadd56c944e1a23a3fdf43a1bf4467
      url: "https://pub.dev"
    source: hosted
    version: "4.8.1"
  json_rpc_2:
    dependency: transitive
    description:
      name: json_rpc_2
      sha256: "5e469bffa23899edacb7b22787780068d650b106a21c76db3c49218ab7ca447e"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.2"
  launch_review:
    dependency: "direct main"
    description:
      name: launch_review
      sha256: "04cdaf752033cefd53bc0fa9c22105801ef53791a93d8b6cdd00fcb3c1c1604b"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  linkify:
    dependency: transitive
    description:
      name: linkify
      sha256: "4139ea77f4651ab9c315b577da2dd108d9aa0bd84b5d03d33323f1970c645832"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  load:
    dependency: "direct main"
    description:
      name: load
      sha256: "79e76a239d63a4965ee90e04885fb6790bd51f57a63fbe5dc101bbcc1928b2cd"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  loading_indicator:
    dependency: "direct main"
    description:
      name: loading_indicator
      sha256: a101ffb2aa3e646137d7810bfa90b50525dd3f72c01235b6df7491cf6af6f284
      url: "https://pub.dev"
    source: hosted
    version: "3.1.1"
  local_auth:
    dependency: "direct main"
    description:
      name: local_auth
      sha256: d3fece0749101725b03206f84a7dab7aaafb702dbbd09131ff8d8173259a9b19
      url: "https://pub.dev"
    source: hosted
    version: "1.1.11"
  lock_to_win:
    dependency: "direct main"
    description:
      path: lock_to_win
      relative: true
    source: path
    version: "0.0.1"
  logging:
    dependency: transitive
    description:
      name: logging
      sha256: "623a88c9594aa774443aa3eb2d41807a48486b5613e67599fb4c41c0ad47c340"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  lottie:
    dependency: "direct main"
    description:
      name: lottie
      sha256: "893da7a0022ec2fcaa616f34529a081f617e86cc501105b856e5a3184c58c7c2"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.3"
  markdown:
    dependency: transitive
    description:
      name: markdown
      sha256: acf35edccc0463a9d7384e437c015a3535772e09714cf60e07eeef3a15870dcd
      url: "https://pub.dev"
    source: hosted
    version: "7.1.1"
  matcher:
    dependency: transitive
    description:
      name: matcher
      sha256: "16db949ceee371e9b99d22f88fa3a73c4e59fd0afed0bd25fc336eb76c198b72"
      url: "https://pub.dev"
    source: hosted
    version: "0.12.13"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      sha256: d92141dc6fe1dad30722f9aa826c7fbc896d021d792f80678280601aff8cf724
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0"
  meta:
    dependency: transitive
    description:
      name: meta
      sha256: "6c268b42ed578a53088d834796959e4a1814b5e9e164f147f580a386e5decf42"
      url: "https://pub.dev"
    source: hosted
    version: "1.8.0"
  mime:
    dependency: transitive
    description:
      name: mime
      sha256: e4ff8e8564c03f255408decd16e7899da1733852a9110a58fe6d1b817684a63e
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  modal_bottom_sheet:
    dependency: "direct main"
    description:
      name: modal_bottom_sheet
      sha256: "3bba63c62d35c931bce7f8ae23a47f9a05836d8cb3c11122ada64e0b2f3d718f"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0-pre"
  modal_progress_hud_nsn:
    dependency: "direct main"
    description:
      name: modal_progress_hud_nsn
      sha256: "7d1b2eb50da63c994f8ec2da5738183dbc8235a528e840ecbf67439adb7a6cc2"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.1"
  mqtt_client:
    dependency: "direct main"
    description:
      name: mqtt_client
      sha256: ba10ec490ded55dc4e77bbc992529d823fb15d0d5ec68c2895f960312060c541
      url: "https://pub.dev"
    source: hosted
    version: "9.8.1"
  nested:
    dependency: transitive
    description:
      name: nested
      sha256: "03bac4c528c64c95c722ec99280375a6f2fc708eec17c7b3f07253b626cd2a20"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  nm:
    dependency: transitive
    description:
      name: nm
      sha256: "2c9aae4127bdc8993206464fcc063611e0e36e72018696cd9631023a31b24254"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.0"
  octo_image:
    dependency: transitive
    description:
      name: octo_image
      sha256: "107f3ed1330006a3bea63615e81cf637433f5135a52466c7caa0e7152bca9143"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  open_store:
    dependency: "direct main"
    description:
      name: open_store
      sha256: "3d9f6a364a817235b779a23ba1bfcca220ab770c89e3b1f4974429e7a32b6cad"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.0"
  otp:
    dependency: "direct main"
    description:
      name: otp
      sha256: fcb7f21e30c4cd80a0a982c27a9b75151cc1fe3d8f7ee680673c090171b1ad55
      url: "https://pub.dev"
    source: hosted
    version: "3.1.4"
  package_config:
    dependency: transitive
    description:
      name: package_config
      sha256: "1c5b77ccc91e4823a5af61ee74e6b972db1ef98c2ff5a18d3161c982a55448bd"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  package_info:
    dependency: "direct main"
    description:
      name: package_info
      sha256: "6c07d9d82c69e16afeeeeb6866fe43985a20b3b50df243091bfc4a4ad2b03b75"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  path:
    dependency: transitive
    description:
      name: path
      sha256: db9d4f58c908a4ba5953fcee2ae317c94889433e5024c27ce74a37f94267945b
      url: "https://pub.dev"
    source: hosted
    version: "1.8.2"
  path_drawing:
    dependency: transitive
    description:
      name: path_drawing
      sha256: "3bdd251dae9ffaef944450b73f168610db7e968e7b20daf0c3907f8b4aafc8a2"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.1+1"
  path_parsing:
    dependency: transitive
    description:
      name: path_parsing
      sha256: ee5c47c1058ad66b4a41746ec3996af9593d0858872807bcd64ac118f0700337
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1"
  path_provider:
    dependency: "direct main"
    description:
      name: path_provider
      sha256: "3087813781ab814e4157b172f1a11c46be20179fcc9bea043e0fba36bc0acaa2"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.15"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      sha256: e595b98692943b4881b219f0a9e3945118d3c16bd7e2813f98ec6e532d905f72
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  path_provider_foundation:
    dependency: transitive
    description:
      name: path_provider_foundation
      sha256: "19314d595120f82aca0ba62787d58dde2cc6b5df7d2f0daf72489e38d1b57f2d"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.1"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      sha256: f7a1fe3a634fe7734c8d3f2766ad746ae2a2884abe22e241a8b301bf5cac3279
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      sha256: "94b1e0dd80970c1ce43d5d4e050a9918fce4f4a775e6142424c30a29a363265c"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      sha256: a34ecd7fb548f8e57321fd8e50d865d266941b54e6c3b7758cf8f37c24116905
      url: "https://pub.dev"
    source: hosted
    version: "2.0.7"
  pattern_formatter:
    dependency: "direct main"
    description:
      name: pattern_formatter
      sha256: c488f1d1e3bbb0b90ec21abd50a891feb89857c4755cdcd81a75525e89c78121
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  permission_handler:
    dependency: "direct main"
    description:
      name: permission_handler
      sha256: "284a66179cabdf942f838543e10413246f06424d960c92ba95c84439154fcac8"
      url: "https://pub.dev"
    source: hosted
    version: "11.0.1"
  permission_handler_android:
    dependency: transitive
    description:
      name: permission_handler_android
      sha256: f9fddd3b46109bd69ff3f9efa5006d2d309b7aec0f3c1c5637a60a2d5659e76e
      url: "https://pub.dev"
    source: hosted
    version: "11.1.0"
  permission_handler_apple:
    dependency: transitive
    description:
      name: permission_handler_apple
      sha256: "99e220bce3f8877c78e4ace901082fb29fa1b4ebde529ad0932d8d664b34f3f5"
      url: "https://pub.dev"
    source: hosted
    version: "9.1.4"
  permission_handler_platform_interface:
    dependency: transitive
    description:
      name: permission_handler_platform_interface
      sha256: "6760eb5ef34589224771010805bea6054ad28453906936f843a8cc4d3a55c4a4"
      url: "https://pub.dev"
    source: hosted
    version: "3.12.0"
  permission_handler_windows:
    dependency: transitive
    description:
      name: permission_handler_windows
      sha256: cc074aace208760f1eee6aa4fae766b45d947df85bc831cde77009cdb4720098
      url: "https://pub.dev"
    source: hosted
    version: "0.1.3"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      sha256: "49392a45ced973e8d94a85fdb21293fbb40ba805fc49f2965101ae748a3683b4"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.0"
  photo_manager:
    dependency: "direct overridden"
    description:
      name: photo_manager
      sha256: "2f98fed8fede27eaf55021a1ce382609a715b52096a94a315f99ae33b6d2eaab"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.2"
  pin_input_text_field:
    dependency: transitive
    description:
      name: pin_input_text_field
      sha256: "8d6fc670aa673a4df5976086f0e8039972a5b2bcb783c8db8dd3b9b4b072ca90"
      url: "https://pub.dev"
    source: hosted
    version: "4.5.1"
  pinenacl:
    dependency: transitive
    description:
      name: pinenacl
      sha256: "3a5503637587d635647c93ea9a8fecf48a420cc7deebe6f1fc85c2a5637ab327"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.1"
  platform:
    dependency: "direct main"
    description:
      name: platform
      sha256: "0a279f0707af40c890e80b1e9df8bb761694c074ba7e1d4ab1bc4b728e200b59"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.3"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      sha256: da3fdfeccc4d4ff2da8f8c556704c08f912542c5fb3cf2233ed75372384a034d
      url: "https://pub.dev"
    source: hosted
    version: "2.1.6"
  pointycastle:
    dependency: transitive
    description:
      name: pointycastle
      sha256: "7c1e5f0d23c9016c5bbd8b1473d0d3fb3fc851b876046039509e18e0c7485f2c"
      url: "https://pub.dev"
    source: hosted
    version: "3.7.3"
  process:
    dependency: transitive
    description:
      name: process
      sha256: "53fd8db9cec1d37b0574e12f07520d582019cb6c44abf5479a01505099a34a09"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.4"
  provider:
    dependency: "direct main"
    description:
      name: provider
      sha256: "59471e0a4595e264625d3496af567ac85bdae1148ec985aff1e0555786f53ecf"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  pub_semver:
    dependency: transitive
    description:
      name: pub_semver
      sha256: "40d3ab1bbd474c4c2328c91e3a7df8c6dd629b79ece4c4bd04bee496a224fb0c"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  pubspec_parse:
    dependency: transitive
    description:
      name: pubspec_parse
      sha256: c63b2876e58e194e4b0828fcb080ad0e06d051cb607a6be51a9e084f47cb9367
      url: "https://pub.dev"
    source: hosted
    version: "1.2.3"
  pull_to_refresh:
    dependency: "direct main"
    description:
      name: pull_to_refresh
      sha256: bbadd5a931837b57739cf08736bea63167e284e71fb23b218c8c9a6e042aad12
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  qr:
    dependency: transitive
    description:
      name: qr
      sha256: "64957a3930367bf97cc211a5af99551d630f2f4625e38af10edd6b19131b64b3"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  qr_code_scanner:
    dependency: "direct main"
    description:
      name: qr_code_scanner
      sha256: f23b68d893505a424f0bd2e324ebea71ed88465d572d26bb8d2e78a4749591fd
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  qr_flutter:
    dependency: "direct main"
    description:
      name: qr_flutter
      sha256: "5095f0fc6e3f71d08adef8feccc8cea4f12eec18a2e31c2e8d82cb6019f4b097"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.0"
  qrcode_flutter:
    dependency: "direct main"
    description:
      name: qrcode_flutter
      sha256: "015762f099442e3c23488fb2c9c5186d61540ef619a319dc30d05aa3f49f8bbe"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  qrscan:
    dependency: "direct main"
    description:
      name: qrscan
      sha256: "0ee72eca0dcbc35ab74894010e3589c3675ddb7c5a551d5f29ab0d3bb1bfb135"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.3"
  quiver:
    dependency: transitive
    description:
      name: quiver
      sha256: b1c1ac5ce6688d77f65f3375a9abb9319b3cb32486bdc7a1e0fdf004d7ba4e47
      url: "https://pub.dev"
    source: hosted
    version: "3.2.1"
  riverpod:
    dependency: transitive
    description:
      name: riverpod
      sha256: "13cbe0e17b659f38027986df967b3eaf7f42c519786352167fc3db1be44eae07"
      url: "https://pub.dev"
    source: hosted
    version: "0.14.0+3"
  rounded_loading_button:
    dependency: "direct main"
    description:
      name: rounded_loading_button
      sha256: ec4af194330ba688527749059b7c1a3d1ddda6c18794ec4b0696f0cb3b9784ff
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  rxdart:
    dependency: "direct main"
    description:
      name: rxdart
      sha256: "0c7c0cedd93788d996e33041ffecda924cc54389199cde4e6a34b440f50044cb"
      url: "https://pub.dev"
    source: hosted
    version: "0.27.7"
  scan:
    dependency: "direct main"
    description:
      name: scan
      sha256: b343ec36f863a88d41eb4c174b810c055c6bd1f1822b2188ab31aab684fb7cdb
      url: "https://pub.dev"
    source: hosted
    version: "1.6.0"
  sec:
    dependency: transitive
    description:
      name: sec
      sha256: "8bbd56df884502192a441b5f5d667265498f2f8728a282beccd9db79e215f379"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  share:
    dependency: "direct main"
    description:
      name: share
      sha256: "97e6403f564ed1051a01534c2fc919cb6e40ea55e60a18ec23cee6e0ce19f4be"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.4"
  shared_preferences:
    dependency: "direct main"
    description:
      name: shared_preferences
      sha256: "81429e4481e1ccfb51ede496e916348668fd0921627779233bd24cc3ff6abd02"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.2"
  shared_preferences_android:
    dependency: transitive
    description:
      name: shared_preferences_android
      sha256: "8568a389334b6e83415b6aae55378e158fbc2314e074983362d20c562780fb06"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  shared_preferences_foundation:
    dependency: transitive
    description:
      name: shared_preferences_foundation
      sha256: "7bf53a9f2d007329ee6f3df7268fd498f8373602f943c975598bbb34649b62a7"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.4"
  shared_preferences_linux:
    dependency: transitive
    description:
      name: shared_preferences_linux
      sha256: "9f2cbcf46d4270ea8be39fa156d86379077c8a5228d9dfdb1164ae0bb93f1faa"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  shared_preferences_platform_interface:
    dependency: transitive
    description:
      name: shared_preferences_platform_interface
      sha256: d4ec5fc9ebb2f2e056c617112aa75dcf92fc2e4faaf2ae999caa297473f75d8a
      url: "https://pub.dev"
    source: hosted
    version: "2.3.1"
  shared_preferences_web:
    dependency: transitive
    description:
      name: shared_preferences_web
      sha256: d762709c2bbe80626ecc819143013cc820fa49ca5e363620ee20a8b15a3e3daf
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  shared_preferences_windows:
    dependency: transitive
    description:
      name: shared_preferences_windows
      sha256: "841ad54f3c8381c480d0c9b508b89a34036f512482c407e6df7a9c4aa2ef8f59"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  simple_tooltip:
    dependency: "direct main"
    description:
      name: simple_tooltip
      sha256: c0a1076c0f173fbd7ba08098ca8523888308f311c87447882866c220681a4e86
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.99"
  sms_autofill:
    dependency: "direct main"
    description:
      name: sms_autofill
      sha256: a851630775fa11ca985d85d8c4e51573c26d9647ab35de151748eb733d4893e8
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  sms_receiver:
    dependency: "direct main"
    description:
      name: sms_receiver
      sha256: afe0f324c131e5cec2ca5f52991e0a328bbe315faee40cf2526ca335c808c6b8
      url: "https://pub.dev"
    source: hosted
    version: "0.4.2"
  sort:
    dependency: "direct main"
    description:
      name: sort
      sha256: a513f9f9f5cd387fb7aaff691540c18f03b47b27d230100dc99a004078d376d2
      url: "https://pub.dev"
    source: hosted
    version: "0.0.2"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: dd904f795d4b4f3b870833847c461801f6750a9fa8e61ea5ac53f9422b31f250
      url: "https://pub.dev"
    source: hosted
    version: "1.9.1"
  sqflite:
    dependency: transitive
    description:
      name: sqflite
      sha256: b4d6710e1200e96845747e37338ea8a819a12b51689a3bcf31eff0003b37a0b9
      url: "https://pub.dev"
    source: hosted
    version: "2.2.8+4"
  sqflite_common:
    dependency: transitive
    description:
      name: sqflite_common
      sha256: "8f7603f3f8f126740bc55c4ca2d1027aab4b74a1267a3e31ce51fe40e3b65b8f"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.5+1"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: c3c7d8edb15bee7f0f74debd4b9c5f3c2ea86766fe4178eb2a18eb30a0bdaed5
      url: "https://pub.dev"
    source: hosted
    version: "1.11.0"
  state_notifier:
    dependency: transitive
    description:
      name: state_notifier
      sha256: "8fe42610f179b843b12371e40db58c9444f8757f8b69d181c97e50787caed289"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.2+1"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: "83615bee9045c1d322bbbd1ba209b7a749c2cbcdcb3fdd1df8eb488b3279c1c8"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  stream_transform:
    dependency: transitive
    description:
      name: stream_transform
      sha256: "14a00e794c7c11aa145a170587321aedce29769c08d7f58b1d141da75e3b1c6f"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "556692adab6cfa87322a115640c11f13cb77b3f076ddcc5d6ae3c20242bedcde"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  synchronized:
    dependency: transitive
    description:
      name: synchronized
      sha256: "5fcbd27688af6082f5abd611af56ee575342c30e87541d0245f7ff99faa02c60"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: a29248a84fbb7c79282b40b8c72a1209db169a2e0542bce341da992fe1bc7e84
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  test_api:
    dependency: transitive
    description:
      name: test_api
      sha256: ad540f65f92caa91bf21dfc8ffb8c589d6e4dc0c2267818b4cc2792857706206
      url: "https://pub.dev"
    source: hosted
    version: "0.4.16"
  timezone:
    dependency: transitive
    description:
      name: timezone
      sha256: "2236ec079a174ce07434e89fcd3fcda430025eb7692244139a9cf54fdcf1fc7d"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.4"
  toast:
    dependency: "direct main"
    description:
      name: toast
      sha256: "12433091e3e5a25b3a25f670126e42547c9ade135de30ad9ace45d1ddccd57c9"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.0"
  transparent_image:
    dependency: transitive
    description:
      name: transparent_image
      sha256: "0a9a2f3a400afa7e322d27808be2b784c147651211a16164865e60cc535224d7"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: facc8d6582f16042dd49f2463ff1bd6e2c9ef9f3d5da3d9b087e244a7b564b3c
      url: "https://pub.dev"
    source: hosted
    version: "1.3.2"
  uni_links:
    dependency: "direct main"
    description:
      name: uni_links
      sha256: "051098acfc9e26a9fde03b487bef5d3d228ca8f67693480c6f33fd4fbb8e2b6e"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.1"
  uni_links_platform_interface:
    dependency: transitive
    description:
      name: uni_links_platform_interface
      sha256: "929cf1a71b59e3b7c2d8a2605a9cf7e0b125b13bc858e55083d88c62722d4507"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  uni_links_web:
    dependency: transitive
    description:
      name: uni_links_web
      sha256: "7539db908e25f67de2438e33cc1020b30ab94e66720b5677ba6763b25f6394df"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.0"
  url_launcher:
    dependency: "direct main"
    description:
      name: url_launcher
      sha256: eb1e00ab44303d50dd487aab67ebc575456c146c6af44422f9c13889984c00f3
      url: "https://pub.dev"
    source: hosted
    version: "6.1.11"
  url_launcher_android:
    dependency: transitive
    description:
      name: url_launcher_android
      sha256: "31222ffb0063171b526d3e569079cf1f8b294075ba323443fdc690842bfd4def"
      url: "https://pub.dev"
    source: hosted
    version: "6.2.0"
  url_launcher_ios:
    dependency: transitive
    description:
      name: url_launcher_ios
      sha256: "4ac97281cf60e2e8c5cc703b2b28528f9b50c8f7cebc71df6bdf0845f647268a"
      url: "https://pub.dev"
    source: hosted
    version: "6.2.0"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      sha256: "9f2d390e096fdbe1e6e6256f97851e51afc2d9c423d3432f1d6a02a8a9a8b9fd"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      sha256: b7244901ea3cf489c5335bdacda07264a6e960b1c1b1a9f91e4bc371d9e68234
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      sha256: "980e8d9af422f477be6948bdfb68df8433be71f5743a188968b0c1b887807e50"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      sha256: ba140138558fcc3eead51a1c42e92a9fb074a1b1149ed3c73e66035b2ccd94f2
      url: "https://pub.dev"
    source: hosted
    version: "2.0.19"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      sha256: "7754a1ad30ee896b265f8d14078b0513a4dba28d358eabb9d5f339886f4a1adc"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  uuid:
    dependency: "direct main"
    description:
      name: uuid
      sha256: "648e103079f7c64a36dc7d39369cabb358d377078a051d6ae2ad3aa539519313"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.7"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      sha256: "80b3257d1492ce4d091729e3a67a60407d227c27241d6927be0130c98e741803"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  video_player:
    dependency: "direct main"
    description:
      name: video_player
      sha256: "74b86e63529cf5885130c639d74cd2f9232e7c8a66cbecbddd1dcb9dbd060d1e"
      url: "https://pub.dev"
    source: hosted
    version: "2.7.2"
  video_player_android:
    dependency: transitive
    description:
      name: video_player_android
      sha256: "3fe89ab07fdbce786e7eb25b58532d6eaf189ceddc091cb66cba712f8d9e8e55"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.10"
  video_player_avfoundation:
    dependency: transitive
    description:
      name: video_player_avfoundation
      sha256: bf1a1322bf68bccd349982ba1f5a41314a3880861fb9a93d25d6d0a2345845f0
      url: "https://pub.dev"
    source: hosted
    version: "2.4.11"
  video_player_platform_interface:
    dependency: transitive
    description:
      name: video_player_platform_interface
      sha256: be72301bf2c0150ab35a8c34d66e5a99de525f6de1e8d27c0672b836fe48f73a
      url: "https://pub.dev"
    source: hosted
    version: "6.2.1"
  video_player_web:
    dependency: transitive
    description:
      name: video_player_web
      sha256: "9c34a243785feca23148bfcd772dbb803d63c9304488177ec4f3f4463802fcb7"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.17"
  wallet:
    dependency: transitive
    description:
      name: wallet
      sha256: "687fd89a16557649b26189e597792962f405797fc64113e8758eabc2c2605c32"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.13"
  watcher:
    dependency: transitive
    description:
      name: watcher
      sha256: "6a7f46926b01ce81bfc339da6a7f20afbe7733eff9846f6d6a5466aa4c6667c0"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  web3dart:
    dependency: "direct main"
    description:
      name: web3dart
      sha256: "0b96223a6b284e3146e65dc842ded139eca68a85c4ab79c5ba1a73284927d3cd"
      url: "https://pub.dev"
    source: hosted
    version: "2.6.1"
  web_socket_channel:
    dependency: "direct main"
    description:
      name: web_socket_channel
      sha256: d88238e5eac9a42bb43ca4e721edba3c08c6354d4a53063afaa568516217621b
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  webview_flutter:
    dependency: "direct main"
    description:
      name: webview_flutter
      sha256: "6886b3ceef1541109df5001054aade5ee3c36b5780302e41701c78357233721c"
      url: "https://pub.dev"
    source: hosted
    version: "2.8.0"
  webview_flutter_android:
    dependency: transitive
    description:
      name: webview_flutter_android
      sha256: "8b3b2450e98876c70bfcead876d9390573b34b9418c19e28168b74f6cb252dbd"
      url: "https://pub.dev"
    source: hosted
    version: "2.10.4"
  webview_flutter_platform_interface:
    dependency: transitive
    description:
      name: webview_flutter_platform_interface
      sha256: "812165e4e34ca677bdfbfa58c01e33b27fd03ab5fa75b70832d4b7d4ca1fa8cf"
      url: "https://pub.dev"
    source: hosted
    version: "1.9.5"
  webview_flutter_wkwebview:
    dependency: transitive
    description:
      name: webview_flutter_wkwebview
      sha256: a5364369c758892aa487cbf59ea41d9edd10f9d9baf06a94e80f1bd1b4c7bbc0
      url: "https://pub.dev"
    source: hosted
    version: "2.9.5"
  win32:
    dependency: transitive
    description:
      name: win32
      sha256: c0e3a4f7be7dae51d8f152230b86627e3397c1ba8c3fa58e63d44a9f3edc9cef
      url: "https://pub.dev"
    source: hosted
    version: "2.6.1"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      sha256: bd512f03919aac5f1313eb8249f223bacf4927031bf60b02601f81f687689e86
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0+3"
  xml:
    dependency: transitive
    description:
      name: xml
      sha256: "80d494c09849dc3f899d227a78c30c5b949b985ededf884cb3f3bcd39f4b447a"
      url: "https://pub.dev"
    source: hosted
    version: "5.4.1"
  yaml:
    dependency: transitive
    description:
      name: yaml
      sha256: "75769501ea3489fca56601ff33454fe45507ea3bfb014161abc3b43ae25989d5"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
sdks:
  dart: ">=2.19.6 <3.0.0"
  flutter: ">=3.7.0"
