name: likewallet
description: A new Flutter application.

version: 2.2.4

environment:
  sdk: ">=2.12.0 <3.0.0"

dependencies:
  platform: ^3.1.0
  path_provider: ^2.0.2
  flutter:
    sdk: flutter
  flutter_native_timezone: ^2.0.0
  share: ^2.0.4
  fluttertoast: 8.2.0
  shared_preferences: ^2.0.6
  flutter_screenutil: ^5.5.3+2
  qr_flutter: ^4.0.0
  web3dart: ^2.2.0
  web_socket_channel: ^2.1.0
  bip39: ^1.0.6
  toast: ^0.3.0
  crypto: ^3.0.1
  flutter_spinkit: ^5.0.0
#  connectivity: ^3.0.6
  connectivity_plus: ^4.0.2
  cached_network_image: ^3.0.0
  image: ^3.0.2
  uuid: ^3.0.1
  currency_text_input_formatter: ^2.0.1
  flutter_easyloading: ^3.0.3
  loading_indicator: ^3.1.1
  image_gallery_saver: ^1.6.9
  open_store: ^0.5.0
  package_info: ^2.0.2
  global_configuration: ^2.0.0-nullsafety.1
#  modal_progress_hud: ^0.1.2
  modal_progress_hud_nsn: ^0.5.0
  http: ^0.13.3
  pattern_formatter: ^2.0.0
  lottie: ^1.1.0
  ed25519_hd_key: ^2.1.0
#  dart_ssss: ^0.0.5
  local_auth: ^1.1.6
  get_it: ^7.1.3
#  passcode_screen:
#    path: ./passcode_screen-1.0.2
  firebase_messaging: ^13.1.0
  firebase_storage: ^10.3.11
  cloud_firestore: ^2.3.0
  firebase_core: ^1.10.0
  firebase_core_platform_interface: 4.5.1
  firebase_auth: ^2.0.0
  sms_receiver: ^0.4.1
  sms_autofill: ^2.2.0
  qr_code_scanner: ^1.0.0-alpha.1
  permission_handler: ^11.0.1
  firebase_analytics: ^9.3.8
  pull_to_refresh: ^2.0.0
  hex: ^0.2.0
  bip32: ^2.0.0
  provider: ^5.0.0
  encrypt: ^5.0.0
  flutter_secure_storage: ^4.2.0
  dependency_validator: ^4.1.0
  image_picker: ^0.8.3+2
  dio: ^4.0.0
  qrscan: ^0.3.2
  scan: ^1.6.0
  ags_authrest2: ^1.0.3
  qrcode_flutter: ^3.0.0-nullsafety.0
  rxdart: ^0.27.1
  flutter_keyboard_visibility: ^5.3.0
  mqtt_client: ^9.3.2
  url_launcher: ^6.0.9
  rounded_loading_button: ^2.1.0
  dash_chat:
    path: ./dash_chat
  camera: ^0.10.3+2
  simple_tooltip: ^1.0.0
  video_player: ^2.6.1
  flutter_countdown_timer: ^4.1.0
  delayed_display: ^2.0.0
  flutter_idensic_mobile_sdk_plugin: ^1.19.7
  launch_review: ^3.0.1
#  firebase_messaging: ^10.0.3
  flutter_local_notifications: ^12.0.4
  flare_flutter: ^3.0.1
  date_format: ^2.0.2
  webview_flutter: ^2.0.9
  otp: ^3.0.0
  image_crop: ^0.4.0
  clippy_flutter: ^2.0.0-nullsafety.1
  flutter_riverpod: ^0.14.0+3
#  app_settings: ^4.1.1
#  contacts_service: ^0.6.3
  flutter_contacts: ^1.1.9+2
  flutter_jailbreak_detection: ^1.8.0
  uni_links: ^0.5.1
  flutter_linkify: ^6.0.0
#  pie_chart:
#    path: ./pie_chart
  lock_to_win:
    path: ./lock_to_win
#  snaplist: ^0.1.8
  flutter_svg: ^0.22.0
  carousel_slider: ^4.0.0
  intl: ^0.17.0
#  stream_chat_flutter: ^3.1.1
  geolocator: ^11.1.0
  load: ^1.0.0
  dart_jsonwebtoken: ^2.4.0
  sort: ^0.0.2
  flutter_localization: ^0.1.13
  flutter_markdown: ^0.6.10
#  CHANGE NEW
  modal_bottom_sheet: ^3.0.0-pre
  flutter_rounded_date_picker: ^3.0.4

dev_dependencies:
  flutter_test:
    sdk: flutter

dependency_overrides:
  flutter_test:
    sdk: flutter
  photo_manager: ^3.2.2
#  flutter_contacts: ^1.1.9
#  sms_autofill: ^2.2.0
#  provider: ^5.0.0
#  flutter_riverpod: ^0.14.0+3

flutter_icons:
  image_path: assets/icon/icon.png
  android: "launcher_icon"
  ios: true

flutter:
  uses-material-design: true

  assets:
  - assets/bip39.txt
  - assets/image/
  - assets/i18n/en.json
  - assets/i18n/th.json
  - assets/i18n/vi.json
  - assets/i18n/km.json
  - assets/i18n/lo.json
  - assets/image/locklike/
  - assets/svgs/
  - assets/animation/
  - assets/animation/kyc/
  - assets/image/ads/
  - assets/image/refer/
  - assets/image/history/
  - assets/image/choice_user/
  - assets/image/index/
  - assets/image/contact_us/
  - assets/image/login/
  - assets/image/banking/buylike/
  - assets/image/banking/cash/
  - assets/image/receive/
  - assets/image/banking/cash/
  - assets/image/take_photo/
  - assets/image/closed_system/
  - assets/image/banking/
  - assets/image/notification/
  - assets/image/banking/send/
  - assets/image/spendlike/
  - assets/image/banking/send/
  - assets/image/store/
  - assets/image/change_phone/
  - assets/image/feedback/
  - assets/image/pin_code/
  - assets/image/home/
  - assets/image/info/
  - assets/image/network/
  - assets/image/reward/
  - assets/image/alert_update/
  - assets/image/lendex/
  - assets/image/kyc/
  - assets/animatedIcons/
  - assets/
    #LDX
  - assets/image/LDX/homeLDX/
  - assets/image/LDX/ads/
  - assets/ldx/
  - assets/markdown/

  fonts:
    - family: Proxima Nova
      fonts:
        - asset: assets/fonts/ProximaNova/ProximaNova-Black.ttf
        - asset: assets/fonts/ProximaNova/ProximaNova-Bold.ttf
        - asset: assets/fonts/ProximaNova/ProximaNova-Extrabld.ttf
        - asset: assets/fonts/ProximaNova/ProximaNova-Light.ttf
        - asset: assets/fonts/ProximaNova/ProximaNova-Regular.ttf
        - asset: assets/fonts/ProximaNova/ProximaNova-Semibold.ttf
    - family: Proxima Nova Light
      fonts:
        - asset: assets/fonts/ProximaNova/ProximaNova-Light.ttf
    - family: Proxima Nova Thin
      fonts:
        - asset: assets/fonts/ProximaNova/ProximaNovaT-Thin.ttf
    - family: Prompt
      fonts:
        - asset: assets/fonts/Prompt/Prompt-Regular.ttf
    - family: Prompt Light
      fonts:
        - asset: assets/fonts/Prompt/Prompt-Light.ttf
    - family: IconHome
      fonts:
        - asset: assets/fonts/IconHome.ttf
    - family: IBMPlexSansThai
      fonts:
        - asset: assets/fonts/IBMPlexSansThai-Bold.ttf
          weight: 700
        - asset: assets/fonts/IBMPlexSansThai-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/IBMPlexSansThai-Medium.ttf
          weight: 500
        - asset: assets/fonts/IBMPlexSansThai-Regular.ttf
        - asset: assets/fonts/IBMPlexSansThai-Light.ttf
          weight: 300
        - asset: assets/fonts/IBMPlexSansThai-ExtraLight.ttf
          weight: 200
        - asset: assets/fonts/IBMPlexSansThai-Thin.ttf
          weight: 100
