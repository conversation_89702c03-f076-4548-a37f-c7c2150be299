{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "camera_avfoundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.13+7/", "native_build": true, "dependencies": []}, {"name": "cloud_firestore", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-2.5.4/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-4.0.2/", "native_build": true, "dependencies": []}, {"name": "firebase_analytics", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-9.3.8/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-2.0.0/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-1.24.0/", "native_build": true, "dependencies": []}, {"name": "firebase_messaging", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-13.1.0/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_storage", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_storage-10.3.11/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "flutter_contacts", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_contacts-1.1.9+2/", "native_build": true, "dependencies": []}, {"name": "flutter_idensic_mobile_sdk_plugin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_idensic_mobile_sdk_plugin-1.32.1/", "native_build": true, "dependencies": []}, {"name": "flutter_jailbreak_detection", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_jailbreak_detection-1.10.0/", "native_build": true, "dependencies": []}, {"name": "flutter_keyboard_visibility", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility-5.4.1/", "native_build": true, "dependencies": []}, {"name": "flutter_local_notifications", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-12.0.4/", "native_build": true, "dependencies": []}, {"name": "flutter_localization", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_localization-0.1.14/", "native_build": true, "dependencies": []}, {"name": "flutter_native_timezone", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_native_timezone-2.0.0/", "native_build": true, "dependencies": []}, {"name": "flutter_secure_storage", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-4.2.1/", "native_build": true, "dependencies": []}, {"name": "fluttertoast", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.0/", "native_build": true, "dependencies": []}, {"name": "geolocator_apple", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.7/", "native_build": true, "dependencies": []}, {"name": "image_crop", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_crop-0.4.1/", "native_build": true, "dependencies": []}, {"name": "image_gallery_saver", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_gallery_saver-1.7.1/", "native_build": true, "dependencies": []}, {"name": "image_picker_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.8+4/", "native_build": true, "dependencies": []}, {"name": "launch_review", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/launch_review-3.0.1/", "native_build": true, "dependencies": []}, {"name": "local_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth-1.1.11/", "native_build": true, "dependencies": []}, {"name": "lock_to_win", "path": "/Users/<USER>/Documents/LikeWalletPharse3/lock_to_win/", "native_build": true, "dependencies": ["cloud_firestore", "firebase_auth", "firebase_core", "flutter_secure_storage", "fluttertoast"]}, {"name": "package_info", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info-2.0.2/", "native_build": true, "dependencies": []}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.3.1/", "native_build": true, "dependencies": []}, {"name": "permission_handler_apple", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/", "native_build": true, "dependencies": []}, {"name": "photo_manager", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.2.2/", "native_build": true, "dependencies": []}, {"name": "qr_code_scanner", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/", "native_build": true, "dependencies": []}, {"name": "qrcode_flutter", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/qrcode_flutter-3.0.0/", "native_build": true, "dependencies": []}, {"name": "scan", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scan-1.6.0/", "native_build": true, "dependencies": []}, {"name": "share", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share-2.0.4/", "native_build": true, "dependencies": []}, {"name": "shared_preferences_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.3.4/", "native_build": true, "dependencies": []}, {"name": "sms_autofill", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sms_autofill-2.4.0/", "native_build": true, "dependencies": []}, {"name": "sms_receiver", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sms_receiver-0.4.2/", "native_build": true, "dependencies": []}, {"name": "sqflite", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.2.8+4/", "native_build": true, "dependencies": []}, {"name": "uni_links", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uni_links-0.5.1/", "native_build": true, "dependencies": []}, {"name": "url_launcher_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.2.0/", "native_build": true, "dependencies": []}, {"name": "video_player_avfoundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.4.11/", "native_build": true, "dependencies": []}, {"name": "webview_flutter_wkwebview", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/", "native_build": true, "dependencies": []}], "android": [{"name": "camera_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_android-0.10.8+13/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "cloud_firestore", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-2.5.4/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-4.0.2/", "native_build": true, "dependencies": []}, {"name": "firebase_analytics", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-9.3.8/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-2.0.0/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-1.24.0/", "native_build": true, "dependencies": []}, {"name": "firebase_messaging", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-13.1.0/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_storage", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_storage-10.3.11/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "flutter_contacts", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_contacts-1.1.9+2/", "native_build": true, "dependencies": []}, {"name": "flutter_idensic_mobile_sdk_plugin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_idensic_mobile_sdk_plugin-1.32.1/", "native_build": true, "dependencies": []}, {"name": "flutter_jailbreak_detection", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_jailbreak_detection-1.10.0/", "native_build": true, "dependencies": []}, {"name": "flutter_keyboard_visibility", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility-5.4.1/", "native_build": true, "dependencies": []}, {"name": "flutter_local_notifications", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-12.0.4/", "native_build": true, "dependencies": []}, {"name": "flutter_localization", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_localization-0.1.14/", "native_build": true, "dependencies": []}, {"name": "flutter_native_timezone", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_native_timezone-2.0.0/", "native_build": true, "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.17/", "native_build": true, "dependencies": []}, {"name": "flutter_secure_storage", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-4.2.1/", "native_build": true, "dependencies": []}, {"name": "fluttertoast", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.0/", "native_build": true, "dependencies": []}, {"name": "geolocator_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-4.3.1/", "native_build": true, "dependencies": []}, {"name": "image_crop", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_crop-0.4.1/", "native_build": true, "dependencies": []}, {"name": "image_gallery_saver", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_gallery_saver-1.7.1/", "native_build": true, "dependencies": []}, {"name": "image_picker_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.8+2/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "launch_review", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/launch_review-3.0.1/", "native_build": true, "dependencies": []}, {"name": "local_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth-1.1.11/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "lock_to_win", "path": "/Users/<USER>/Documents/LikeWalletPharse3/lock_to_win/", "native_build": true, "dependencies": ["cloud_firestore", "firebase_auth", "firebase_core", "flutter_secure_storage", "fluttertoast"]}, {"name": "package_info", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info-2.0.2/", "native_build": true, "dependencies": []}, {"name": "path_provider_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.1/", "native_build": true, "dependencies": []}, {"name": "permission_handler_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-11.1.0/", "native_build": true, "dependencies": []}, {"name": "photo_manager", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.2.2/", "native_build": true, "dependencies": []}, {"name": "qr_code_scanner", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/", "native_build": true, "dependencies": []}, {"name": "qrcode_flutter", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/qrcode_flutter-3.0.0/", "native_build": true, "dependencies": []}, {"name": "qrscan", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/qrscan-0.3.3/", "native_build": true, "dependencies": []}, {"name": "scan", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scan-1.6.0/", "native_build": true, "dependencies": []}, {"name": "share", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share-2.0.4/", "native_build": true, "dependencies": []}, {"name": "shared_preferences_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.2.1/", "native_build": true, "dependencies": []}, {"name": "sms_autofill", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sms_autofill-2.4.0/", "native_build": true, "dependencies": []}, {"name": "sms_receiver", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sms_receiver-0.4.2/", "native_build": true, "dependencies": []}, {"name": "sqflite", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.2.8+4/", "native_build": true, "dependencies": []}, {"name": "uni_links", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uni_links-0.5.1/", "native_build": true, "dependencies": []}, {"name": "url_launcher_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.2.0/", "native_build": true, "dependencies": []}, {"name": "video_player_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_android-2.4.10/", "native_build": true, "dependencies": []}, {"name": "webview_flutter_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_android-2.10.4/", "native_build": true, "dependencies": []}], "macos": [{"name": "cloud_firestore", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-2.5.4/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-4.0.2/", "native_build": true, "dependencies": []}, {"name": "file_selector_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.3+3/", "native_build": true, "dependencies": []}, {"name": "firebase_analytics", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-9.3.8/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-2.0.0/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-1.24.0/", "native_build": true, "dependencies": []}, {"name": "firebase_messaging", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-13.1.0/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_storage", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_storage-10.3.11/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "flutter_keyboard_visibility_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_macos-1.0.0/", "native_build": false, "dependencies": []}, {"name": "flutter_local_notifications", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-12.0.4/", "native_build": true, "dependencies": []}, {"name": "flutter_localization", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_localization-0.1.14/", "native_build": true, "dependencies": []}, {"name": "flutter_native_timezone", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_native_timezone-2.0.0/", "native_build": true, "dependencies": []}, {"name": "geolocator_apple", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.7/", "native_build": true, "dependencies": []}, {"name": "image_picker_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+1/", "native_build": false, "dependencies": ["file_selector_macos"]}, {"name": "package_info", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info-2.0.2/", "native_build": true, "dependencies": []}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.3.1/", "native_build": true, "dependencies": []}, {"name": "photo_manager", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.2.2/", "native_build": true, "dependencies": []}, {"name": "shared_preferences_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.3.4/", "native_build": true, "dependencies": []}, {"name": "sqflite", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.2.8+4/", "native_build": true, "dependencies": []}, {"name": "url_launcher_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.1.0/", "native_build": true, "dependencies": []}], "linux": [{"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-4.0.2/", "native_build": false, "dependencies": []}, {"name": "file_selector_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.2+1/", "native_build": true, "dependencies": []}, {"name": "flutter_keyboard_visibility_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_linux-1.0.0/", "native_build": false, "dependencies": []}, {"name": "flutter_localization", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_localization-0.1.14/", "native_build": true, "dependencies": []}, {"name": "flutter_secure_storage", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-4.2.1/", "native_build": true, "dependencies": []}, {"name": "image_picker_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+1/", "native_build": false, "dependencies": ["file_selector_linux"]}, {"name": "path_provider_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/", "native_build": false, "dependencies": []}, {"name": "shared_preferences_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.3.2/", "native_build": false, "dependencies": ["path_provider_linux"]}, {"name": "url_launcher_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.1.0/", "native_build": true, "dependencies": []}], "windows": [{"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-4.0.2/", "native_build": true, "dependencies": []}, {"name": "file_selector_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+1/", "native_build": true, "dependencies": []}, {"name": "flutter_keyboard_visibility_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_windows-1.0.0/", "native_build": false, "dependencies": []}, {"name": "flutter_localization", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_localization-0.1.14/", "native_build": true, "dependencies": []}, {"name": "geolocator_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_windows-0.2.3/", "native_build": true, "dependencies": []}, {"name": "image_picker_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/", "native_build": false, "dependencies": ["file_selector_windows"]}, {"name": "path_provider_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.0.7/", "native_build": false, "dependencies": []}, {"name": "permission_handler_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.1.3/", "native_build": true, "dependencies": []}, {"name": "shared_preferences_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.3.2/", "native_build": false, "dependencies": ["path_provider_windows"]}, {"name": "url_launcher_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.0/", "native_build": true, "dependencies": []}], "web": [{"name": "camera_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_web-0.3.2+2/", "dependencies": []}, {"name": "cloud_firestore_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_web-2.8.10/", "dependencies": ["firebase_core_web"]}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-4.0.2/", "dependencies": []}, {"name": "firebase_analytics_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_web-0.4.2+7/", "dependencies": ["firebase_core_web"]}, {"name": "firebase_auth_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_web-2.0.0/", "dependencies": ["firebase_core_web"]}, {"name": "firebase_core_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-1.7.3/", "dependencies": []}, {"name": "firebase_messaging_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.2.0/", "dependencies": ["firebase_core_web"]}, {"name": "firebase_storage_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_storage_web-3.3.9/", "dependencies": ["firebase_core_web"]}, {"name": "flutter_keyboard_visibility_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_web-2.0.0/", "dependencies": []}, {"name": "flutter_localization", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_localization-0.1.14/", "dependencies": []}, {"name": "flutter_native_timezone", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_native_timezone-2.0.0/", "dependencies": []}, {"name": "fluttertoast", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.0/", "dependencies": []}, {"name": "geolocator_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_web-3.0.0/", "dependencies": []}, {"name": "image_picker_for_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-2.2.0/", "dependencies": []}, {"name": "lock_to_win", "path": "/Users/<USER>/Documents/LikeWalletPharse3/lock_to_win/", "dependencies": ["fluttertoast"]}, {"name": "qrcode_flutter", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/qrcode_flutter-3.0.0/", "dependencies": []}, {"name": "shared_preferences_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.2.1/", "dependencies": []}, {"name": "toast", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/toast-0.3.0/", "dependencies": []}, {"name": "uni_links_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uni_links_web-0.1.0/", "dependencies": []}, {"name": "url_launcher_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.0.19/", "dependencies": []}, {"name": "video_player_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_web-2.0.17/", "dependencies": []}]}, "dependencyGraph": [{"name": "camera", "dependencies": ["camera_android", "camera_avfoundation", "camera_web", "flutter_plugin_android_lifecycle"]}, {"name": "camera_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "camera_avfoundation", "dependencies": []}, {"name": "camera_web", "dependencies": []}, {"name": "cloud_firestore", "dependencies": ["cloud_firestore_web", "firebase_core"]}, {"name": "cloud_firestore_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "connectivity_plus", "dependencies": []}, {"name": "file_selector_linux", "dependencies": []}, {"name": "file_selector_macos", "dependencies": []}, {"name": "file_selector_windows", "dependencies": []}, {"name": "firebase_analytics", "dependencies": ["firebase_analytics_web", "firebase_core"]}, {"name": "firebase_analytics_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_auth", "dependencies": ["firebase_auth_web", "firebase_core"]}, {"name": "firebase_auth_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_core", "dependencies": ["firebase_core_web"]}, {"name": "firebase_core_web", "dependencies": []}, {"name": "firebase_messaging", "dependencies": ["firebase_core", "firebase_messaging_web"]}, {"name": "firebase_messaging_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_storage", "dependencies": ["firebase_core", "firebase_storage_web"]}, {"name": "firebase_storage_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "flutter_contacts", "dependencies": []}, {"name": "flutter_idensic_mobile_sdk_plugin", "dependencies": []}, {"name": "flutter_jailbreak_detection", "dependencies": []}, {"name": "flutter_keyboard_visibility", "dependencies": ["flutter_keyboard_visibility_linux", "flutter_keyboard_visibility_macos", "flutter_keyboard_visibility_web", "flutter_keyboard_visibility_windows"]}, {"name": "flutter_keyboard_visibility_linux", "dependencies": []}, {"name": "flutter_keyboard_visibility_macos", "dependencies": []}, {"name": "flutter_keyboard_visibility_web", "dependencies": []}, {"name": "flutter_keyboard_visibility_windows", "dependencies": []}, {"name": "flutter_local_notifications", "dependencies": []}, {"name": "flutter_localization", "dependencies": ["shared_preferences"]}, {"name": "flutter_native_timezone", "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "dependencies": []}, {"name": "flutter_secure_storage", "dependencies": []}, {"name": "fluttertoast", "dependencies": []}, {"name": "geolocator", "dependencies": ["geolocator_android", "geolocator_apple", "geolocator_web", "geolocator_windows"]}, {"name": "geolocator_android", "dependencies": []}, {"name": "geolocator_apple", "dependencies": []}, {"name": "geolocator_web", "dependencies": []}, {"name": "geolocator_windows", "dependencies": []}, {"name": "image_crop", "dependencies": []}, {"name": "image_gallery_saver", "dependencies": []}, {"name": "image_picker", "dependencies": ["image_picker_android", "image_picker_for_web", "image_picker_ios", "image_picker_linux", "image_picker_macos", "image_picker_windows"]}, {"name": "image_picker_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "image_picker_for_web", "dependencies": []}, {"name": "image_picker_ios", "dependencies": []}, {"name": "image_picker_linux", "dependencies": ["file_selector_linux"]}, {"name": "image_picker_macos", "dependencies": ["file_selector_macos"]}, {"name": "image_picker_windows", "dependencies": ["file_selector_windows"]}, {"name": "launch_review", "dependencies": []}, {"name": "local_auth", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "lock_to_win", "dependencies": ["cloud_firestore", "firebase_auth", "firebase_core", "shared_preferences", "flutter_secure_storage", "fluttertoast"]}, {"name": "package_info", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "permission_handler", "dependencies": ["permission_handler_android", "permission_handler_apple", "permission_handler_windows"]}, {"name": "permission_handler_android", "dependencies": []}, {"name": "permission_handler_apple", "dependencies": []}, {"name": "permission_handler_windows", "dependencies": []}, {"name": "photo_manager", "dependencies": []}, {"name": "qr_code_scanner", "dependencies": []}, {"name": "qrcode_flutter", "dependencies": []}, {"name": "qrscan", "dependencies": []}, {"name": "scan", "dependencies": []}, {"name": "share", "dependencies": []}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}, {"name": "sms_autofill", "dependencies": []}, {"name": "sms_receiver", "dependencies": []}, {"name": "sqflite", "dependencies": []}, {"name": "toast", "dependencies": []}, {"name": "uni_links", "dependencies": ["uni_links_web"]}, {"name": "uni_links_web", "dependencies": []}, {"name": "url_launcher", "dependencies": ["url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_web", "url_launcher_windows"]}, {"name": "url_launcher_android", "dependencies": []}, {"name": "url_launcher_ios", "dependencies": []}, {"name": "url_launcher_linux", "dependencies": []}, {"name": "url_launcher_macos", "dependencies": []}, {"name": "url_launcher_web", "dependencies": []}, {"name": "url_launcher_windows", "dependencies": []}, {"name": "video_player", "dependencies": ["video_player_android", "video_player_avfoundation", "video_player_web"]}, {"name": "video_player_android", "dependencies": []}, {"name": "video_player_avfoundation", "dependencies": []}, {"name": "video_player_web", "dependencies": []}, {"name": "webview_flutter", "dependencies": ["webview_flutter_android", "webview_flutter_wkwebview"]}, {"name": "webview_flutter_android", "dependencies": []}, {"name": "webview_flutter_wkwebview", "dependencies": []}], "date_created": "2025-07-09 10:25:20.058257", "version": "3.7.12"}